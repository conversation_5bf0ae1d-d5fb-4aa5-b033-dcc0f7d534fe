image: maven:3-eclipse-temurin-21

variables:
  # Defines the location of the analysis task cache
  SONAR_USER_HOME: "${CI_PROJECT_DIR}/.sonar"
  # 在sonarqube中创建项目时生成的TOKEN, 也可以在gitlab中配置 Settings > CI/CD > Variables
  SONAR_TOKEN: "sqa_e3d331c5349eb8be4014c3156054b6dcfc3efaaa"
  SONAR_HOST_URL: "http://************:9000"
  # Tells git to fetch all the branches of the project, required by the analysis task
  GIT_DEPTH: 0
  MAVEN_REPO: /.m2 #maven仓库，注意这里是容器里面的仓库地址，后面会把这个目录挂载到宿主机里面去，方式每次构建拉取jar影响构建性能。
  MAVEN_OPTS: "-Djava.awt.headless=true -Dmaven.repo.local=/.m2/repository"
  MAVEN_CLI_OPTS: "--batch-mode --errors --fail-at-end --show-version -P test,jacoco"

cache:
  paths:
    - /.m2/repository
    - /.m2/settings.xml  # 缓存 settings.xml 文件
  # keep cache across branch
  key: "$CI_BUILD_REF_NAME"

stages:
  - sonarqube-check
#  - sonarqube-vulnerability-report

before_script:
  - cp /.m2/settings.xml ~/.m2/settings.xml

sonarqube-check:
  stage: sonarqube-check

  tags:
    - saas

  script:
    # 跳过saas-inquiry-user-server（打出来的jar太大，不上传到服务器）
    - mvn clean deploy  -pl !'saas-inquiry-user/saas-inquiry-user-server' -Dmaven.test.skip=true $MAVEN_CLI_OPTS
    # 加上-Ddubbo.registry.register=false，不注册dubbo，只订阅
    - mvn verify org.sonarsource.scanner.maven:sonar-maven-plugin:sonar -Dmaven.test.skip=false -Ddubbo.registry.register=false $MAVEN_CLI_OPTS
  allow_failure: true
  # 仅检测 合并请求和test分支代码提交
  only:
    - merge_requests
    - test
  # rules 是在 GitLab CI/CD 版本 12.3 中引入的，我们用的11
  # rules:
  #   - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
  #   - if: $CI_COMMIT_BRANCH == 'master'
  #   - if: $CI_COMMIT_BRANCH == 'main'
  #   - if: $CI_COMMIT_BRANCH == 'develop'

#只有 GitLab Ultimate 版本可以使用 GitLab 缺陷报告。如果你没有订阅专项服务，可以安全的删除 sonarqube-vulnerability-report 步骤。
#sonarqube-vulnerability-report:
#  stage: sonarqube-vulnerability-report
#  script:
#    - 'curl -u "${SONAR_TOKEN}:" "${SONAR_HOST_URL}/api/issues/gitlab_sast_export?projectKey=saas_saas-inquiry-kernel_d2475805-865e-4993-9306-243ca07e27cf&branch=${CI_COMMIT_BRANCH}&pullRequest=${CI_MERGE_REQUEST_IID}" -o gl-sast-sonar-report.json'
#  allow_failure: true
#  rules:
#    - if: $CI_PIPELINE_SOURCE == 'merge_request_event'
#    - if: $CI_COMMIT_BRANCH == 'master'
#    - if: $CI_COMMIT_BRANCH == 'main'
#    - if: $CI_COMMIT_BRANCH == 'develop'
#  artifacts:
#    expire_in: 1 day
#    reports:
#      sast: gl-sast-sonar-report.json