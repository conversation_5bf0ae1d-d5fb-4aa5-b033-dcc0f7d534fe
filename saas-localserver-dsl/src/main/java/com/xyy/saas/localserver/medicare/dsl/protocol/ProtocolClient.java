package com.xyy.saas.localserver.medicare.dsl.protocol;

import com.xyy.saas.localserver.medicare.dsl.executor.value.InputObject;
import com.xyy.saas.localserver.medicare.dsl.protocol.request.BodyStrategy;
import com.xyy.saas.localserver.medicare.dsl.protocol.response.ProtocolResponse;
import lombok.Getter;
import lombok.experimental.SuperBuilder;

/**
 * @Desc 协议客户端
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/07/25 15:54
 */
@SuperBuilder
public abstract class ProtocolClient {

    @Getter
    protected InputObject inputObject;

    @Getter
    protected BodyStrategy bodyStrategy;

    public ProtocolClient(InputObject inputObject, BodyStrategy bodyStrategy) {
        this.inputObject = inputObject;
        this.bodyStrategy = bodyStrategy;
    }

    /**
     * 编码,生成请求body
     *
     * @return
     */
    public abstract ProtocolClient toGenerateBody();

    /**
     * 调用请求第三方
     *
     * @return
     */
    public abstract ProtocolResponse toInvoker();

    public abstract void parseResponseBody();

    public void printLog() {
        //TODO 打印日志
    }
}
