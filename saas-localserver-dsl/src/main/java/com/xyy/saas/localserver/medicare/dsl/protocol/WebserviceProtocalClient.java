package com.xyy.saas.localserver.medicare.dsl.protocol;

import cn.hutool.core.map.MapUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.xyy.saas.localserver.medicare.dsl.config.base.ContractConfig;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContext;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContextHolder;
import com.xyy.saas.localserver.medicare.dsl.executor.value.InputObject;
import com.xyy.saas.localserver.medicare.dsl.parse.JsonParameterParser;
import com.xyy.saas.localserver.medicare.dsl.parse.ObjectValueParser;
import com.xyy.saas.localserver.medicare.dsl.protocol.request.RequestBodyBuilderStrategy;
import com.xyy.saas.localserver.medicare.dsl.protocol.response.ProtocolResponse;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * @Desc WebService协议客户端
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/07/25 15:56
 */
@Slf4j
@SuperBuilder
public class WebserviceProtocalClient extends ProtocolClient {

    private static final ObjectValueParser valueParser = new JsonParameterParser();

    /**
     * 编码,生成请求body
     *
     * @return
     */
    @Override
    public ProtocolClient toGenerateBody() {
        try {
            // 生成SOAP XML请求体
            RequestBodyBuilderStrategy requestBodyBuilderStrategy = bodyStrategy.getClazz().getDeclaredConstructor().newInstance();
            String body = requestBodyBuilderStrategy.generateBody(inputObject);
            inputObject.setBody(body);
            
            // 添加公共Header
            Map<String, String> commonHeaders = DSLContextHolder.getContext().getCommonHeader();
            if (MapUtil.isNotEmpty(commonHeaders)) {
                inputObject.addHeader(commonHeaders);
            }
            
            log.debug("生成SOAP请求体: {}", body);
        } catch (Exception e) {
            log.error("生成SOAP body数据失败,input:{}", inputObject, e);
            throw new RuntimeException("生成SOAP body数据失败,请联系开发人员");
        }
        return this;
    }

    /**
     * 调用请求第三方
     *
     * @return
     */
    @Override
    public ProtocolResponse toInvoker() {
        ContractConfig.FunctionConfig functionConfig = this.inputObject.getFunctionConfig();
        String url = valueParser.parseValue(functionConfig.getDomain(), DSLContextHolder.getContext()) + 
                     valueParser.parseValue(functionConfig.getPath(), DSLContextHolder.getContext());
        
        int timeout = functionConfig.getTimeout() * 1000;
        Map<String, String> header = this.inputObject.getHeader();
        
        log.info("调用WebService接口: {}", url);
        log.debug("请求头: {}", header);
        log.debug("请求体: {}", inputObject.getBody());
        
        try {
            // 创建HTTP POST请求
            HttpRequest httpRequest = HttpUtil.createPost(url)
                    .addHeaders(header)
                    .body(inputObject.getBody())
                    .timeout(timeout);
            
            // 执行请求
            HttpResponse httpResponse = httpRequest.execute();
            
            String responseBody = httpResponse.body();
            log.info("WebService响应状态: {}", httpResponse.getStatus());
            log.debug("WebService响应内容: {}", responseBody);
            
            // 构建响应对象
            ProtocolResponse response = ProtocolResponse.builder()
                    .code(httpResponse.isOk() ? 0 : -1)
                    .requestCode(httpResponse.getStatus())
                    .body(responseBody)
                    .build();
            
            // 解析SOAP响应
            parseSoapResponse(response, responseBody);
            
            return response;
            
        } catch (Exception e) {
            log.error("调用WebService接口失败: {}", url, e);
            throw new RuntimeException("调用WebService接口失败: " + e.getMessage());
        }
    }

    /**
     * 解析SOAP响应
     *
     * @param response 协议响应对象
     * @param responseBody 响应体
     */
    private void parseSoapResponse(ProtocolResponse response, String responseBody) {
        try {
            Map<String, Object> resultMap = new HashMap<>();
            
            if (StringUtils.hasText(responseBody)) {
                // 简单的XML解析，提取SOAP Body内容
                String bodyContent = extractSoapBody(responseBody);
                
                if (StringUtils.hasText(bodyContent)) {
                    // 尝试解析为JSON格式（如果响应是JSON格式）
                    if (bodyContent.trim().startsWith("{") || bodyContent.trim().startsWith("[")) {
                        try {
                            JSONObject jsonResponse = JSONUtil.parseObj(bodyContent);
                            resultMap.putAll(jsonResponse);
                        } catch (Exception e) {
                            log.debug("响应不是JSON格式，作为普通文本处理");
                            resultMap.put("response", bodyContent);
                        }
                    } else {
                        // 解析XML格式的响应，构建原始XML解析结果
                        buildXmlParseResult(resultMap, bodyContent);
                    }
                } else {
                    // 如果没有找到SOAP Body，直接解析整个响应
                    resultMap.put("response", responseBody);
                }
            }
            
            response.setResultMap(resultMap);
            
        } catch (Exception e) {
            log.error("解析SOAP响应失败", e);
            Map<String, Object> errorMap = new HashMap<>();
            errorMap.put("error", "解析响应失败: " + e.getMessage());
            errorMap.put("rawResponse", responseBody);
            response.setResultMap(errorMap);
        }
    }

    /**
     * 构建XML解析结果，支持DSL配置的转义解析
     *
     * @param resultMap 结果Map
     * @param xmlContent XML内容
     */
    private void buildXmlParseResult(Map<String, Object> resultMap, String xmlContent) {
        try {
            // 构建完整的XML解析结果，包括所有可能的字段和嵌套结构
            Map<String, Object> xmlStructure = parseXmlToMap(xmlContent);
            resultMap.putAll(xmlStructure);
            
            log.debug("XML解析结果: {}", xmlStructure);
            
        } catch (Exception e) {
            log.error("构建XML解析结果失败", e);
            resultMap.put("response", xmlContent);
        }
    }

    /**
     * 将XML内容解析为Map结构，支持嵌套和转义内容
     *
     * @param xmlContent XML内容
     * @return 解析后的Map结构
     */
    private Map<String, Object> parseXmlToMap(String xmlContent) {
        Map<String, Object> result = new HashMap<>();
        
        // 使用正则表达式解析XML标签
        Pattern tagPattern = Pattern.compile("<([^/>\\s]+)[^>]*>(.*?)</\\1>", Pattern.DOTALL);
        Matcher matcher = tagPattern.matcher(xmlContent);
        
        while (matcher.find()) {
            String fullTagName = matcher.group(1);
            String tagContent = matcher.group(2).trim();
            
            // 去除命名空间前缀，简化标签名
            String tagName = removeNamespacePrefix(fullTagName);
            
            if (StringUtils.hasText(tagContent)) {
                // 检查内容是否包含XML标签（嵌套结构）
                if (tagContent.contains("<") && tagContent.contains(">")) {
                    // 检查是否是转义的XML内容
                    if (isEscapedXml(tagContent)) {
                        // 转义的XML内容，先反转义再解析
                        String unescapedContent = unescapeXml(tagContent);
                        Map<String, Object> nestedMap = parseXmlToMap(unescapedContent);
                        result.put(tagName, nestedMap);
                    } else {
                        // 普通嵌套XML，递归解析
                        Map<String, Object> nestedMap = parseXmlToMap(tagContent);
                        result.put(tagName, nestedMap);
                    }
                } else {
                    // 简单文本内容
                    result.put(tagName, tagContent);
                }
            } else {
                // 空标签
                result.put(tagName, "");
            }
        }
        
        return result;
    }

    /**
     * 去除XML标签的命名空间前缀
     *
     * @param tagName 完整的标签名
     * @return 去除命名空间前缀后的标签名
     */
    private String removeNamespacePrefix(String tagName) {
        if (tagName.contains(":")) {
            return tagName.substring(tagName.indexOf(":") + 1);
        }
        return tagName;
    }

    /**
     * 检查内容是否是转义的XML
     *
     * @param content 内容
     * @return 是否是转义的XML
     */
    private boolean isEscapedXml(String content) {
        return content.contains("&lt;") && content.contains("&gt;");
    }

    /**
     * 反转义XML内容
     *
     * @param escapedXml 转义的XML内容
     * @return 反转义后的XML内容
     */
    private String unescapeXml(String escapedXml) {
        return escapedXml
                .replace("&lt;", "<")
                .replace("&gt;", ">")
                .replace("&amp;", "&")
                .replace("&quot;", "\"")
                .replace("&apos;", "'");
    }

    /**
     * 提取SOAP Body内容
     *
     * @param soapResponse SOAP响应
     * @return Body内容
     */
    private String extractSoapBody(String soapResponse) {
        try {
            // 使用正则表达式提取SOAP Body内容
            Pattern pattern = Pattern.compile("<soap:Body[^>]*>(.*?)</soap:Body>", Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
            Matcher matcher = pattern.matcher(soapResponse);
            
            if (matcher.find()) {
                return matcher.group(1).trim();
            }
            
            // 如果没有找到soap:Body，尝试查找Body
            pattern = Pattern.compile("<Body[^>]*>(.*?)</Body>", Pattern.DOTALL | Pattern.CASE_INSENSITIVE);
            matcher = pattern.matcher(soapResponse);
            
            if (matcher.find()) {
                return matcher.group(1).trim();
            }
            
            return soapResponse;
        } catch (Exception e) {
            log.error("提取SOAP Body失败", e);
            return soapResponse;
        }
    }

    /**
     * 解析响应体
     */
    @Override
    public void parseResponseBody() {
        // WebService响应解析已在toInvoker方法中完成
        log.debug("WebService响应解析完成");
    }
}
