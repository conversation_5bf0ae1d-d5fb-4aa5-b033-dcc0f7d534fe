package com.xyy.saas.localserver.medicare.dsl.parse;

import cn.hutool.json.JSONUtil;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContext;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContextHolder;
import org.springframework.expression.ParseException;
import org.springframework.expression.spel.SpelEvaluationException;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;

/**
 * @Desc Json参数解析器
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/09/04 13:42
 */
public class JsonParameterParser extends SpELParameterValueParser implements ObjectValueParser {


    private static final String INDEX = "_index_";
    private static final String _INDEX_ = "[" + INDEX + "]";
    private static final String _INDEX_SIZE_ = "[_index_size_]";

    /**
     * @param <T>
     * @param config
     * @param context
     * @param function
     * @return
     */
    @Override
    public <T extends Object> T parseObject(T config, Object context, Function function) {
        T result = null;
        if (config instanceof String) {
            result = (T) this.parseValue(config.toString(), (DSLContext) context);
        } else if (config instanceof Number) {
            result = (T) this.parseValue(config.toString(), (DSLContext) context);
        } else if (config instanceof Boolean) {
            // Boolean 类型直接返回，不作为表达式解析
            result = config;
        } else if (config instanceof Map configMap) {
            result = (T) parseMap(configMap, (DSLContext) context);
        } else if (config instanceof List configList) {
            result = (T) parseList(configList, (DSLContext) context);
        } else if (config == null) {
            result = null;
        }
        function.apply(result);
        return result;
    }


    @Override
    public <T extends Object> T parseObject(T config, Object context, Function function, String key, Map<String, Object> unresolvedMap) {
        try {
            return parseObject(config, context, function);
        } catch (ParseException | SpelEvaluationException e) {
            unresolvedMap.put(key, context);
            return null;
        }
    }

    /**
     * @param expressionMap
     * @param context
     * @return
     */
    @Override
    public Map<String, Object> parseMap(Map<String, Object> expressionMap, DSLContext context) {
        Map<String, Object> result = new LinkedHashMap<>();
        expressionMap.forEach((key, value) -> {
            if (value instanceof String childExpression) {
                result.put(key, parseValue(childExpression, context));
            } else if (value instanceof Map childExpression) {
                result.put(key, parseMap(childExpression, context));
            } else if (value instanceof List childExpression) {
                result.put(key, parseList(childExpression, context));
            } else if (value instanceof Boolean || value instanceof Number) {
                // 支持基本类型：Boolean、Number等，直接放入结果
                result.put(key, value);
            } else if (value == null) {
                // 支持null值
                result.put(key, null);
            } else {
                throw new UnsupportedOperationException("parseMap 不支持解析[" + value.getClass() + "]参数类型");
            }
        });
        return result;
    }

    /**
     * @param expressionList
     * @param context
     * @return
     */
    @Override
    public List<?> parseList(List<?> expressionList, DSLContext context) {
        if (CollectionUtils.isEmpty(expressionList)) {
            throw new UnsupportedOperationException("参数配置错误,数组对象内至少需要包含一个对象,目前对象个数: 0");
        }
        //取第一个对象
        Object childConfig = expressionList.get(0);
        if (childConfig instanceof List) {
            throw new UnsupportedOperationException("暂不支持List嵌套List");
        }
        List<Map<String, Object>> result = new ArrayList<>();
        if (childConfig instanceof Map) { //当List下面嵌套的是Map
            Map<String, Object> childConfigMap = (Map<String, Object>) childConfig;
            int size = getListDateSize(childConfigMap);
            //取到size后,开始循环Size的次数拼装对象
            for (int i = 0; i < size; i++) {
                Map<String, Object> item = new LinkedHashMap<>();
                Integer index = i;
                childConfigMap.forEach((k, v) -> {
                    if (v instanceof Map) {
                        //支持list里嵌套map，部分地区结算接口detail商品明细里会有扩展字段，里面是个map的json
                        String expression = JSONUtil.toJsonStr(v).replace(INDEX, String.valueOf(index));
                        item.put(k, parseMap(JSONUtil.parseObj(expression), context));
                    } else if (v instanceof String vv) {
                        //把_index_字符串换成该对象的游标,就可以从Context中取到值
                        String expression = vv.replace(INDEX, String.valueOf(index));
                        item.put((String) k, parseValue(expression, context));
                    } else if (v instanceof List configList) {
                        item.put(k, parseList(configList, context));
//                        throw new UnsupportedOperationException("暂不支持List嵌套Map再嵌套非Map和String");
                    }
                });
                result.add(item);
            }
        } else {
            // List 元素为普通对象
            return expressionList.stream().map(item -> {
                if (item instanceof String strItem) {
                    return this.parseValue(strItem, context);
                }
                return item;
            }).toList();
        }
        return result;
    }

    /**
     * 当解析的对象是数组时,找到第一个标记了[INDEX]的EL表达式,前半部分对象取.Size()方法并且从context中解析到数组长度
     * 当取不到时，可以指定[_index_size_]的长度,直接从context解析值
     *
     * @param childConfigMap
     * @return
     */
    private int getListDateSize(Map<String, Object> childConfigMap) {
        StringBuilder exp = new StringBuilder();
        for (Map.Entry<String, Object> entry : childConfigMap.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (value instanceof Map) {
//                exp.append("map.size()");
                throw new RuntimeException("key:" + key + ",暂不支持List嵌套Map再嵌套Map");
            }
            if (value instanceof String expression) {
                //找到包含[_index_]的key，并且不包含Java函数T()的value,从中解出数组的Size
                if (expression.contains(_INDEX_) && !expression.contains("T(")) {
                    exp.append(expression.substring(0, expression.indexOf(_INDEX_))).append(".size()");
                    break;
                }
                //如果所有的key的表达式都没有[_index_],或者都使用Java函数T()做了处理，则可以指定Map中的一个key=_index_size_的作为数组的长度
                if (key.equalsIgnoreCase(_INDEX_SIZE_)) {
                    exp.append(expression);
                    break;
                }
            }
        }
        if (!StringUtils.hasLength(exp.toString())) {
            //当没配置index参数时,Size就是1,只构造Map结构的对象
            return 1;
        }
        return Integer.valueOf(parseValue(exp.toString(), DSLContextHolder.getContext()));
    }

}
