package com.xyy.saas.localserver.medicare.dsl.protocol.request;

import cn.hutool.core.map.MapUtil;
import com.xyy.saas.localserver.medicare.dsl.config.base.ContractConfig;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContextHolder;
import com.xyy.saas.localserver.medicare.dsl.executor.value.InputObject;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * @Desc SOAP WebService协议的Body构建策略
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/08 14:40
 */
public class SoapBodyBuilderStrategy extends RequestBodyBuilderStrategy {

    /**
     * 生成SOAP格式的请求体
     *
     * @param inputObject 输入对象
     * @return SOAP XML格式字符串
     */
    @Override
    public String generateBody(InputObject inputObject) {
        ContractConfig contractConfig = inputObject.getContractConfig();
        ContractConfig.FunctionConfig functionConfig = inputObject.getFunctionConfig();
        Map<String, Object> requestBody = new LinkedHashMap<>();
        
        if (contractConfig.isCommon()) {
            // 如果存在公共参数，就把公共参数都放到requestBody
            Map<String, Object> commonInput = DSLContextHolder.getContext().getCommonInput();
            requestBody.putAll(commonInput);
        }
        
        Map<String, Object> bodys = inputObject.getBodys();
        if (MapUtil.isNotEmpty(bodys)) {
            mergeMaps(requestBody, bodys);
        }
        
        return buildSoapEnvelope(requestBody);
    }

    /**
     * 构建SOAP信封
     *
     * @param requestBody 请求体数据
     * @return SOAP XML字符串
     */
    private String buildSoapEnvelope(Map<String, Object> requestBody) {
        StringBuilder soapBuilder = new StringBuilder();
        
        // 添加XML声明
        soapBuilder.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n");
        
        // 开始SOAP信封
        soapBuilder.append("<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n");
        soapBuilder.append("  <soap:Body>\n");
        
        // 处理methodName和methodData
        String methodName = (String) requestBody.get("methodName");
        if (methodName != null) {
            soapBuilder.append("    <").append(methodName).append(">\n");
            
            // 添加CDATA开始
            soapBuilder.append("      <![CDATA[\n");
            
            // 处理methodData
            Object methodData = requestBody.get("methodData");
            if (methodData instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = (Map<String, Object>) methodData;
                appendMapToXml(soapBuilder, dataMap, "        ");
            }
            
            // 添加CDATA结束
            soapBuilder.append("      ]]>\n");
            
            soapBuilder.append("    </").append(methodName).append(">\n");
        } else {
            // 如果没有methodName，直接转换整个requestBody
            appendMapToXml(soapBuilder, requestBody, "    ");
        }
        
        // 结束SOAP信封
        soapBuilder.append("  </soap:Body>\n");
        soapBuilder.append("</soap:Envelope>");
        
        return soapBuilder.toString();
    }

    /**
     * 将Map转换为XML格式
     *
     * @param builder XML构建器
     * @param map     要转换的Map
     * @param indent  缩进字符串
     */
    private void appendMapToXml(StringBuilder builder, Map<String, Object> map, String indent) {
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            if (value == null) {
                builder.append(indent).append("<").append(key).append("/>\n");
            } else if (value instanceof Map) {
                builder.append(indent).append("<").append(key).append(">\n");
                @SuppressWarnings("unchecked")
                Map<String, Object> nestedMap = (Map<String, Object>) value;
                appendMapToXml(builder, nestedMap, indent + "  ");
                builder.append(indent).append("</").append(key).append(">\n");
            } else {
                builder.append(indent).append("<").append(key).append(">")
                       .append(escapeXml(String.valueOf(value)))
                       .append("</").append(key).append(">\n");
            }
        }
    }

    /**
     * XML字符转义
     *
     * @param text 要转义的文本
     * @return 转义后的文本
     */
    private String escapeXml(String text) {
        if (text == null) {
            return "";
        }
        return text.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace("\"", "&quot;")
                   .replace("'", "&apos;");
    }

    /**
     * 合并Map，递归处理嵌套的Map
     *
     * @param target 目标Map
     * @param source 源Map
     */
    public static void mergeMaps(Map<String, Object> target, Map<String, Object> source) {
        for (Map.Entry<String, Object> entry : source.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (target.containsKey(key)) {
                Object targetValue = target.get(key);
                if (targetValue instanceof Map && value instanceof Map) {
                    // 递归合并子Map
                    @SuppressWarnings("unchecked")
                    Map<String, Object> targetMap = (Map<String, Object>) targetValue;
                    @SuppressWarnings("unchecked")
                    Map<String, Object> sourceMap = (Map<String, Object>) value;
                    mergeMaps(targetMap, sourceMap);
                }
            } else {
                // 如果目标Map中没有这个键，则直接放入
                target.put(key, deepCopy(value));
            }
        }
    }

    /**
     * 深拷贝对象
     *
     * @param value 要拷贝的对象
     * @return 拷贝后的对象
     */
    @SuppressWarnings("unchecked")
    private static Object deepCopy(Object value) {
        if (value instanceof Map) {
            Map<String, Object> original = (Map<String, Object>) value;
            Map<String, Object> copy = new LinkedHashMap<>();
            for (Map.Entry<String, Object> entry : original.entrySet()) {
                copy.put(entry.getKey(), deepCopy(entry.getValue()));
            }
            return copy;
        } else {
            return value;
        }
    }
} 