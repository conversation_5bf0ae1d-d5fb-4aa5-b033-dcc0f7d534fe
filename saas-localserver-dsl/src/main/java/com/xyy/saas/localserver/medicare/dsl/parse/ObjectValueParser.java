package com.xyy.saas.localserver.medicare.dsl.parse;

import com.xyy.saas.localserver.medicare.dsl.executor.DSLContext;

import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * @Desc 参数解析器
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/09/04 10:55
 */
public interface ObjectValueParser extends ParameterValueParser {


    <T extends Object> T parseObject(T config, Object context, Function function);

    <T extends Object> T parseObject(T config, Object context, Function function, String key, Map<String, Object> unresolvedMap);

    Map<String, ?> parseMap(Map<String, Object> expressionMap, DSLContext context);

    List<?> parseList(List<?> expressionList, DSLContext context);


}
