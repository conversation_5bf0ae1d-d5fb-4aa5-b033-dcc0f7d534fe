package com.xyy.saas.localserver.medicare.dsl.reader;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.JsonNode;
import com.xyy.saas.localserver.medicare.dsl.config.DSLKey;
import com.xyy.saas.localserver.medicare.dsl.config.base.ContractConfig;
import com.xyy.saas.localserver.medicare.dsl.config.base.DSLConfig;
import com.xyy.saas.localserver.medicare.dsl.config.base.LogicalConfig;
import com.xyy.saas.localserver.medicare.dsl.config.base.ViewUIConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.Assert;

import java.io.IOException;

/**
 * 字符串配置读取器
 * 用于解析YAML格式的字符串配置
 */
@Slf4j
public class StringConfigReader extends ConfigReader {

    /**
     * 读取配置
     *
     * @param yamlSource       YAML格式的配置字符串
     * @param commonYamlSource 公共配置字符串
     * @return DSL配置对象
     */
    @Override
    public DSLConfig readerConfig(DSLKey key, String yamlSource, String commonYamlSource) {
        Assert.notNull(yamlSource, "配置内容不能为null");
        try {
            JsonNode rootNode = objectMapper.readTree(yamlSource);
            JsonNode jsonNode = rootNode.get("dslType");
            if (jsonNode == null) {
                ContractConfig contractConfig = objectMapper.readValue(yamlSource, ContractConfig.class);
                return readerCommonConfig(contractConfig, commonYamlSource);
            }
            String dslType = jsonNode.asText();
            switch (dslType) {
                case "contract":
                    ContractConfig contractConfig = objectMapper.readValue(yamlSource, ContractConfig.class);
                    return readerCommonConfig(contractConfig, commonYamlSource);
                case "logical":
                    return objectMapper.readValue(yamlSource, LogicalConfig.class);
                case "viewui":
                    return objectMapper.readValue(yamlSource, ViewUIConfig.class);
                default:
                    log.error("未知的DSLType类型: {}", dslType);
                    throw new IllegalArgumentException("未知的DSLType类型:" + dslType);
            }
        } catch (IOException e) {
            log.error("解析配置失败", e);
            throw new RuntimeException("解析配置失败", e);
        }
    }


    /**
     * 读取公共配置
     *
     * @param contractConfig   合约配置
     * @param commonYamlSource 公共配置源
     * @return 合约配置
     */
    @Override
    public ContractConfig readerCommonConfig(ContractConfig contractConfig, String commonYamlSource) throws JsonProcessingException {
        if (!contractConfig.isCommon()) {
            // 没有公共参数的
            return contractConfig;
        }
        // 有公共参数的,反序列化公共参数,不用所有协议都配一遍公共参数
        try {
            ContractConfig.CommonConfig commonConfig = objectMapper.readValue(commonYamlSource, ContractConfig.CommonConfig.class);
            contractConfig.setCommonConfig(commonConfig);
        } catch (IOException e) {
            log.error("解析公共配置失败", e);
            throw new RuntimeException("解析公共配置失败", e);
        }
        return contractConfig;
    }
} 