package com.xyy.saas.localserver.medicare.dsl.config;

import com.xyy.saas.localserver.medicare.dsl.config.internet.InternetConfig;

/**
 * @Desc 医保节点key，命名统一只能小写字母，因为在端侧windows系统对大小写敏感
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/02 15:25
 */
public enum DSLKey {

    signin("签到"),

    readcard("读卡", InternetConfig.InternetType.readcard),

    preBalance("预结算"),

    settlement("结算"),

    personInfo("人员信息"),
    ;

    private String key;

    private InternetConfig.InternetType internetType;

    DSLKey(String key) {
        this.key = key;
    }

    DS<PERSON><PERSON><PERSON>(String key, InternetConfig.InternetType internetType) {
        this.key = key;
        this.internetType = internetType;
    }
}
