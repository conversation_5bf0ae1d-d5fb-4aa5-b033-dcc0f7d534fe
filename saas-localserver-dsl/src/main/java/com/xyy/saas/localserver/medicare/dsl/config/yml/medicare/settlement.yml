settlement:

  enable: true
  name: '结算'
  protocol: http
  format: json
  domain: 127.0.0.1

  path: /fsi/api/signInSignOutService/signIn

  header:

  function:
    - path: /fsi/api/signInSignOutService/signIn
      protocol: http
      perdsl:
        "a": context.set(a)
      input:
        header:
          "token": ""Utils.xxx(orgin,a,b)"
          "infno": "9001",
          "mdtrtarea_admvs": "data.merchantInfo.merchantAreaCode",
          "insuplc_admdvs": "data.merchantInfo.merchantAreaCode",
          "opter": "data.operatorInfo.operatorId",
          "opter_name": "data.operatorInfo.operatorName",
          "fixmedins_code": "data.merchantInfo.medicareInstitutionCode",
          "fixmedins_name": "data.merchantInfo.medicareInstitutionName",
          "sign_no": "utils.xxx(data.operatorInfo.periodNo)",
        body:
          "opterNo": "data.operatorInfo.operatorCode"
          "mac": "data.merchantInfo.macAddress"
          "list":
            - "item": [
                - "aa": "",
                "b": [ ],
                - "aa": "",
                "b": [ ],
            ]
          "ip": "data.merchantInfo.privateNetworkIp"
          "sign": utils.xxx(data.operatorInfo.operatorCode, data.merchantInfo.macAddress)
      output:
        "code": "extendMap['out_1']['infcode']",
        "msg": "extendMap['out_1']['err_msg']",
        "result": "extendMap['out_1']['output']['signinoutb']['sign_no']"
      result:
        #        success: context.code == 0
        success: "context.code == 0"
        tips: "error.msg"


    - path: /fsi/api/signInSignOutService/signIn
      input:
        header:
          "infno": "9001",
          "mdtrtarea_admvs": "data.merchantInfo.merchantAreaCode",
          "insuplc_admdvs": "data.merchantInfo.merchantAreaCode",
          "opter": "data.operatorInfo.operatorId",
          "opter_name": "data.operatorInfo.operatorName",
          "fixmedins_code": "data.merchantInfo.medicareInstitutionCode",
          "fixmedins_name": "data.merchantInfo.medicareInstitutionName",
          "sign_no": "data.operatorInfo.periodNo",
        body:
          "opterNo": "data.operatorInfo.operatorCode"
          "mac": "data.merchantInfo.macAddress"
          "ip": "data.merchantInfo.privateNetworkIp"
      output:
        "code": "extendMap['out_1']['infcode']",
        "msg": "extendMap['out_1']['err_msg']",
        "result": "extendMap['out_1']['output']['signinoutb']['sign_no']"
