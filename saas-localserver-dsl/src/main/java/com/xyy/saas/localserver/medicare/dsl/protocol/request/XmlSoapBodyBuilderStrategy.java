package com.xyy.saas.localserver.medicare.dsl.protocol.request;

import cn.hutool.core.map.MapUtil;
import com.xyy.saas.localserver.medicare.dsl.config.base.ContractConfig;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContextHolder;
import com.xyy.saas.localserver.medicare.dsl.executor.value.InputObject;

import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;

/**
 * @Desc XML SOAP WebService协议的Body构建策略 - 支持JSON到XML自动转换
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/08 14:40
 */
public class XmlSoapBodyBuilderStrategy extends RequestBodyBuilderStrategy {

    /**
     * 生成SOAP格式的请求体，支持JSON到XML的自动转换
     *
     * @param inputObject 输入对象
     * @return SOAP XML格式字符串
     */
    @Override
    public String generateBody(InputObject inputObject) {
        ContractConfig contractConfig = inputObject.getContractConfig();
        ContractConfig.FunctionConfig functionConfig = inputObject.getFunctionConfig();
        Map<String, Object> requestBody = new LinkedHashMap<>();
        
        if (contractConfig.isCommon()) {
            // 如果存在公共参数，就把公共参数都放到requestBody
            Map<String, Object> commonInput = DSLContextHolder.getContext().getCommonInput();
            requestBody.putAll(commonInput);
        }
        
        Map<String, Object> bodys = inputObject.getBodys();
        if (MapUtil.isNotEmpty(bodys)) {
            mergeMaps(requestBody, bodys);
        }
        
        return buildSoapEnvelope(requestBody);
    }

    /**
     * 构建SOAP信封，支持自动XML转换
     *
     * @param requestBody 请求体数据
     * @return SOAP XML字符串
     */
    private String buildSoapEnvelope(Map<String, Object> requestBody) {
        StringBuilder soapBuilder = new StringBuilder();
        
        // 添加XML声明
        soapBuilder.append("<?xml version=\"1.0\" encoding=\"utf-8\"?>\n");
        
        // 开始SOAP信封
        soapBuilder.append("<soap:Envelope xmlns:soap=\"http://schemas.xmlsoap.org/soap/envelope/\">\n");
        soapBuilder.append("  <soap:Body>\n");
        
        // 处理methodName和methodData
        String methodName = (String) requestBody.get("methodName");
        if (methodName != null) {
            // 获取命名空间配置，如果没有则使用默认值
            String namespace = getNamespace(requestBody);
            
            // 添加命名空间声明，解决WebService命名空间问题
            if (namespace != null && !namespace.isEmpty()) {
                soapBuilder.append("    <").append(methodName)
                          .append(" xmlns=\"").append(namespace).append("\"").append(">\n");
            } else {
                soapBuilder.append("    <").append(methodName).append(">\n");
            }
            
            // 处理methodData中的每个参数 - 按照DSL配置中的顺序
            Object methodData = requestBody.get("methodData");
            if (methodData instanceof Map) {
                @SuppressWarnings("unchecked")
                Map<String, Object> dataMap = (Map<String, Object>) methodData;
                
                // 按照DSL配置中的自然顺序处理参数
                for (Map.Entry<String, Object> entry : dataMap.entrySet()) {
                    String key = entry.getKey();
                    Object value = entry.getValue();
                    
                    // 跳过namespace字段，不作为参数发送
                    if ("namespace".equals(key)) {
                        continue;
                    }
                    
                    // 检查是否需要XML转义处理
                    boolean needXmlEscape = false;
                    Object actualValue = value;
                    
                    if (value instanceof Map) {
                        @SuppressWarnings("unchecked")
                        Map<String, Object> valueMap = (Map<String, Object>) value;
                        
                        // 方式1：检查是否配置了xmlEscape=true且有value字段
                        if (Boolean.TRUE.equals(valueMap.get("xmlEscape")) && valueMap.containsKey("value")) {
                            needXmlEscape = true;
                            actualValue = valueMap.get("value");
                        }
                        // 方式2：如果没有特殊配置，直接使用Map作为需要转换的XML内容
                        else if (!valueMap.containsKey("xmlEscape") && !valueMap.containsKey("value")) {
                            // 如果Map不包含配置字段，可能是直接的XML内容，暂时按普通方式处理
                            // 用户可以通过添加xmlEscape: true来明确指定需要转义
                        }
                    }
                    
                    if (needXmlEscape && actualValue instanceof Map) {
                        // XML转义处理：将JSON转换为转义的XML字符串
                        String xmlContent = convertMapToSimpleXml((Map<String, Object>) actualValue);
                        String escapedXmlContent = escapeXml(xmlContent);
                        soapBuilder.append("      <").append(key).append(" xmlns=\"\">")
                                   .append(escapedXmlContent)
                                   .append("</").append(key).append(">\n");
                    } else {
                        // 普通参数直接处理，明确使用无命名空间覆盖父元素命名空间继承
                        soapBuilder.append("      <").append(key).append(" xmlns=\"\">")
                                   .append(String.valueOf(actualValue))
                                   .append("</").append(key).append(">\n");
                    }
                }
            }
            
            soapBuilder.append("    </").append(methodName).append(">\n");
        } else {
            // 如果没有methodName，直接转换整个requestBody
            appendMapToXml(soapBuilder, requestBody, "    ");
        }
        
        // 结束SOAP信封
        soapBuilder.append("  </soap:Body>\n");
        soapBuilder.append("</soap:Envelope>");
        
        return soapBuilder.toString();
    }

    /**
     * 将JSON Map转换为XML字符串
     *
     * @param jsonMap JSON格式的Map
     * @return XML字符串
     */
    private String convertJsonToXml(Map<String, Object> jsonMap) {
        StringBuilder xmlBuilder = new StringBuilder();
        
        // 添加XML声明
        xmlBuilder.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
        
        // 转换JSON Map为XML
        appendMapToXmlContent(xmlBuilder, jsonMap, "");
        
        return xmlBuilder.toString();
    }

    /**
     * 将JSON Map转换为XML字符串（不包含XML声明）
     *
     * @param jsonMap JSON格式的Map
     * @return XML字符串（无XML声明）
     */
    private String convertJsonToXmlWithoutDeclaration(Map<String, Object> jsonMap) {
        StringBuilder xmlBuilder = new StringBuilder();
        
        // 转换JSON Map为XML，不添加XML声明
        appendMapToXmlContent(xmlBuilder, jsonMap, "");
        
        return xmlBuilder.toString();
    }

    /**
     * 将Map转换为简洁的XML字符串（无XML声明，无换行）
     *
     * @param map 要转换的Map
     * @return 简洁的XML字符串
     */
    private String convertMapToSimpleXml(Map<String, Object> map) {
        StringBuilder xmlBuilder = new StringBuilder();
        appendMapToSimpleXml(xmlBuilder, map);
        return xmlBuilder.toString();
    }

    /**
     * 递归将Map转换为简洁的XML内容（无换行，无缩进）
     *
     * @param builder XML构建器
     * @param map     要转换的Map
     */
    private void appendMapToSimpleXml(StringBuilder builder, Map<String, Object> map) {
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            if (value == null) {
                builder.append("<").append(key).append("/>");
            } else if (value instanceof Map) {
                builder.append("<").append(key).append(">");
                @SuppressWarnings("unchecked")
                Map<String, Object> nestedMap = (Map<String, Object>) value;
                appendMapToSimpleXml(builder, nestedMap);
                builder.append("</").append(key).append(">");
            } else if (value instanceof List) {
                // 处理List类型：展开为多个相同标签的XML元素
                @SuppressWarnings("unchecked")
                List<Object> listValue = (List<Object>) value;
                for (Object item : listValue) {
                    if (item instanceof Map) {
                        builder.append("<").append(key).append(">");
                        @SuppressWarnings("unchecked")
                        Map<String, Object> itemMap = (Map<String, Object>) item;
                        appendMapToSimpleXml(builder, itemMap);
                        builder.append("</").append(key).append(">");
                    } else {
                        builder.append("<").append(key).append(">")
                               .append(String.valueOf(item))
                               .append("</").append(key).append(">");
                    }
                }
            } else {
                builder.append("<").append(key).append(">")
                       .append(String.valueOf(value))
                       .append("</").append(key).append(">");
            }
        }
    }

    /**
     * 递归将Map转换为XML内容
     *
     * @param builder XML构建器
     * @param map     要转换的Map
     * @param indent  缩进字符串
     */
    private void appendMapToXmlContent(StringBuilder builder, Map<String, Object> map, String indent) {
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            if (value == null) {
                builder.append(indent).append("<").append(key).append("/>");
            } else if (value instanceof Map) {
                builder.append(indent).append("<").append(key).append(">");
                @SuppressWarnings("unchecked")
                Map<String, Object> nestedMap = (Map<String, Object>) value;
                appendMapToXmlContent(builder, nestedMap, indent + "  ");
                builder.append("</").append(key).append(">");
            } else if (value instanceof List) {
                // 处理List类型：展开为多个相同标签的XML元素
                @SuppressWarnings("unchecked")
                List<Object> listValue = (List<Object>) value;
                for (Object item : listValue) {
                    if (item instanceof Map) {
                        builder.append(indent).append("<").append(key).append(">");
                        @SuppressWarnings("unchecked")
                        Map<String, Object> itemMap = (Map<String, Object>) item;
                        appendMapToXmlContent(builder, itemMap, indent + "  ");
                        builder.append("</").append(key).append(">");
                    } else {
                        builder.append(indent).append("<").append(key).append(">")
                               .append(escapeXml(String.valueOf(item)))
                               .append("</").append(key).append(">");
                    }
                }
            } else {
                builder.append(indent).append("<").append(key).append(">")
                       .append(escapeXml(String.valueOf(value)))
                       .append("</").append(key).append(">");
            }
        }
    }

    /**
     * 将Map转换为XML格式（原有方法保持不变）
     *
     * @param builder XML构建器
     * @param map     要转换的Map
     * @param indent  缩进字符串
     */
    private void appendMapToXml(StringBuilder builder, Map<String, Object> map, String indent) {
        for (Map.Entry<String, Object> entry : map.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            
            if (value == null) {
                builder.append(indent).append("<").append(key).append("/>\n");
            } else if (value instanceof Map) {
                builder.append(indent).append("<").append(key).append(">\n");
                @SuppressWarnings("unchecked")
                Map<String, Object> nestedMap = (Map<String, Object>) value;
                appendMapToXml(builder, nestedMap, indent + "  ");
                builder.append(indent).append("</").append(key).append(">\n");
            } else if (value instanceof List) {
                // 处理List类型：展开为多个相同标签的XML元素
                @SuppressWarnings("unchecked")
                List<Object> listValue = (List<Object>) value;
                for (Object item : listValue) {
                    if (item instanceof Map) {
                        builder.append(indent).append("<").append(key).append(">\n");
                        @SuppressWarnings("unchecked")
                        Map<String, Object> itemMap = (Map<String, Object>) item;
                        appendMapToXml(builder, itemMap, indent + "  ");
                        builder.append(indent).append("</").append(key).append(">\n");
                    } else {
                        builder.append(indent).append("<").append(key).append(">")
                               .append(escapeXml(String.valueOf(item)))
                               .append("</").append(key).append(">\n");
                    }
                }
            } else {
                builder.append(indent).append("<").append(key).append(">")
                       .append(escapeXml(String.valueOf(value)))
                       .append("</").append(key).append(">\n");
            }
        }
    }

    /**
     * 获取WebService命名空间
     * 
     * @param requestBody 请求体数据
     * @return 命名空间字符串，如果没有配置则返回null
     */
    private String getNamespace(Map<String, Object> requestBody) {
        // 检查是否在methodData中配置了namespace
        Object methodData = requestBody.get("methodData");
        if (methodData instanceof Map) {
            @SuppressWarnings("unchecked")
            Map<String, Object> dataMap = (Map<String, Object>) methodData;
            Object namespaceObj = dataMap.get("namespace");
            if (namespaceObj != null) {
                return String.valueOf(namespaceObj);
            }
        }
        
        // 检查是否在顶层配置了namespace
        Object namespaceObj = requestBody.get("namespace");
        if (namespaceObj != null) {
            return String.valueOf(namespaceObj);
        }
        
        // 如果没有配置，返回null（不使用命名空间）
        return null;
    }

    /**
     * XML字符转义
     *
     * @param text 要转义的文本
     * @return 转义后的文本
     */
    private String escapeXml(String text) {
        if (text == null) {
            return "";
        }
        return text.replace("&", "&amp;")
                   .replace("<", "&lt;")
                   .replace(">", "&gt;")
                   .replace("\"", "&quot;")
                   .replace("'", "&apos;");
    }

    /**
     * 合并Map，递归处理嵌套的Map
     *
     * @param target 目标Map
     * @param source 源Map
     */
    public static void mergeMaps(Map<String, Object> target, Map<String, Object> source) {
        for (Map.Entry<String, Object> entry : source.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();

            if (target.containsKey(key)) {
                Object targetValue = target.get(key);
                if (targetValue instanceof Map && value instanceof Map) {
                    // 递归合并子Map
                    @SuppressWarnings("unchecked")
                    Map<String, Object> targetMap = (Map<String, Object>) targetValue;
                    @SuppressWarnings("unchecked")
                    Map<String, Object> sourceMap = (Map<String, Object>) value;
                    mergeMaps(targetMap, sourceMap);
                }
            } else {
                // 如果目标Map中没有这个键，则直接放入
                target.put(key, deepCopy(value));
            }
        }
    }

    /**
     * 深拷贝对象
     *
     * @param value 要拷贝的对象
     * @return 拷贝后的对象
     */
    @SuppressWarnings("unchecked")
    private static Object deepCopy(Object value) {
        if (value instanceof Map) {
            Map<String, Object> original = (Map<String, Object>) value;
            Map<String, Object> copy = new LinkedHashMap<>();
            for (Map.Entry<String, Object> entry : original.entrySet()) {
                copy.put(entry.getKey(), deepCopy(entry.getValue()));
            }
            return copy;
        } else {
            return value;
        }
    }
} 