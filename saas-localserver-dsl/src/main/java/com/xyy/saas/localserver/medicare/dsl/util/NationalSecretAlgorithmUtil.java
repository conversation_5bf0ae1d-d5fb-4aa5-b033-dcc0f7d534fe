package com.xyy.saas.localserver.medicare.dsl.util;

import cn.hsaf.common.utils.EasyGmUtils;
import cn.hsaf.common.utils.SignUtil;
import cn.hsaf.common.utils.StringUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.CharsetUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.PropertyFilter;
import lombok.extern.slf4j.Slf4j;
import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.bouncycastle.util.encoders.Hex;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.security.Security;
import java.util.Base64;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

/**
 * 1.电子处方列表查询  /fixmedins/rxAuthQuery
 * {"appId":"43AF047BBA47FC8A1AE8EFB232BDBBCB","data":{"fixmedinsCode":"43AF047BBA47FC8A1AE8EFB232BDBBCB","psnName":"o8z4C5avQXqC0aWFPf1Mzu6D7 WCQ_bd","psnCertType":"350181199011193519","certno":"01","ecToken":"13763873033","ipInfo":"测试","opterName":"测试","optinsNo":"测试","outFlag":"11111","outPoolareaNo":"1111","insuPlcNo":"11111"},"encType":"SM4","signType":"SM2","timestamp":"","version":"1.0.0"}
 * <p>
 * <p>
 * 2.电子处方下载   /fixmedins/rxInfoDld
 * {"appId":"43AF047BBA47FC8A1AE8EFB232BDBBCB","data":{"epcToken":"43AF047BBA47FC8A1AE8EFB232BDBBCB","authRxno":"o8z4C5avQXqC0aWFPf1Mzu6D7 WCQ_bd","fixmedinsCode":"350181199011193519","devInfo":"01","ecToken":"13763873033","ipInfo":"测试","opter":"测试","opterName":"测试","optinsNo":"11111"},"encType":"SM4","signType":"SM2","timestamp":"","version":"1.0.0"}
 * <p>
 * 3.电子处方信息核验   /fixmedins/rxInfoVerify
 * {"appId":"43AF047BBA47FC8A1AE8EFB232BDBBCB","data":{"hiRxno":"43AF047BBA47FC8A1AE8EFB232BDBBCB","fixmedinsCode":"o8z4C5avQXqC0aWFPf1Mzu6D7 WCQ_bd","rxTraceCode":"350181199011193519","rxFile":"01","epcToken":"13763873033"},"encType":"SM4","signType":"SM2","timestamp":"","version":"1.0.0"}
 * <p>
 * <p>
 * 4.电子处方审核信息上传  /fixmedins/rxChkUpld
 * {"appId":"43AF047BBA47FC8A1AE8EFB232BDBBCB","data":{"hiRxno":"43AF047BBA47FC8A1AE8EFB232BDBBCB","epcToken":"o8z4C5avQXqC0aWFPf1Mzu6D7 WCQ_bd","ecToken":"350181199011193519","fixmedinsCode":"01","fixmedinsName":"13763873033","pharCertType":"13763873033","pharCertno":"13763873033","pharName":"13763873033","pharCode":"13763873033","rxChkOpnn":"13763873033","rxChkStasCodg":"13763873033","rxChkTime":"13763873033","rxSignVerifySn":"13763873033"},"encType":"SM4","signType":"SM2","timestamp":"","version":"1.0.0"}
 * <p>
 * 5.电子处方的药品销售出库明细上传  /fixmedins/rxSelDrugUpld
 * {"appId":"43AF047BBA47FC8A1AE8EFB232BDBBCB","data":{"hiRxno":"43AF047BBA47FC8A1AE8EFB232BDBBCB","fixmedinsCode":"01","fixmedinsName":"''","rchkPharCertType":"''","rchkPharCertno":"''","rchkPharName":"''","dspeerCertType":"''","dspeerCertno":"''","dspeerName":"''","rxChkBizSn":"''","hiFeesetlFlag":"''","psnCashPay":"''","setlId":"''","mdtrtId":"''","mdtrtareaNo":"''","rxSaleDrugDetl":[{"rtalDocno":"''","stooutNo":"''","stooutTime":"''","medListCodg":"''","medinsListCodg":"''","drugProdname":"''","drugGenname":"''","drugSpec":"''","drugDosform":"''","prdrName":"''","aprvno":"''","manuLotnum":"''","bchno":"''","drugstdcode":"''","drugTraceCodg":"''","drugProdBarc":"''","shelfLoc":"''","drugTotlcnt":"''","drugTotlcntEmp":"''","drugSumamt":"''"}]},"encType":"SM4","signType":"SM2","timestamp":"","version":"1.0.0"}
 * 医保国密算法工具类
 *
 * @Author:chenxiaoyi
 * @Date:2023/01/18 16:52
 */
@Slf4j
public class NationalSecretAlgorithmUtil {


    static {
        Security.addProvider(new BouncyCastleProvider());
    }


    public static void main(String[] args) {

        /**
         * 渠道id
         */
        String chnlId = "5F5CDB8F5F4441769767405B5D9ED4F1";
        /**
         * 渠道私钥
         */
        String prvkey = "AOtBHi46Y0CG3X33FfQeTbi02DUEPUwsSx1n/0O8wNHU";
        /**
         * 渠道密钥
         */
        String sm4key = "2413ED66F9574E86B6E8F5EB5515C0FE";
        /**
         * 平台公钥
         */
        String pubKey = "BKGGgMrBOnODAB6OZyYTDtagrSxMZsCZaHTYowqS5xhHxfuQ+S7+gp47b2khbMfOQeT+ah1WeGVH32vA1ShsCOs=";
        /**
         * 报文体
         */
        String input = """
                {
                    "fixmedinsCode": "H61010400856",
                    "pageNum": 1,
                    "pageSize": 100
                }
                """;

        JSONObject jsonObject1 = encryptMsg(chnlId, sm4key, prvkey, JSONObject.parseObject(input));


        System.out.println(jsonObject1);

        String msg = FileUtil.readString("D://1.txt", CharsetUtil.CHARSET_UTF_8);

        JSONObject jsonObject = JSON.parseObject(msg);

        decryptMsg(chnlId, sm4key, pubKey, jsonObject);
    }


    /**
     * sm2签名
     *
     * @param message 未加密报文
     * @param sm4key  渠道sm4密钥
     * @param prvKey  渠道私钥
     * @return 签名串 String
     * @throws Exception
     */
    public static String signature(String message, String sm4key, String prvKey) {
        byte[] messageByte;
        try {
            JSONObject jsonObject = JSON.parseObject(message);
            removeEmpty(jsonObject);
            String signText = SignUtil.getSignText(jsonObject, sm4key);

            messageByte = signText.getBytes(StandardCharsets.UTF_8);
        } catch (Exception e) {
            messageByte = message.getBytes();
        }
        byte[] chnlSecretByte = sm4key.getBytes();
        byte[] prvkey = Base64.getDecoder().decode(prvKey);
        return Base64.getEncoder().encodeToString(EasyGmUtils.signSm3WithSm2(messageByte, chnlSecretByte, prvkey));
    }

    /**
     * sm2验签
     *
     * @param msg      sm4解密后报文
     * @param source   原始响应报文
     * @param signData 签名串
     * @param sm4key   渠道密钥
     * @param pubKey   平台公钥
     * @return 验证是否通过 boolean
     * @throws Exception
     */
    public static boolean verify(String msg, String source, String signData, String sm4key, String pubKey) {
        byte[] msgByte;
        try {
            JSONObject jsonObject = JSON.parseObject(msg);
            JSONObject jsonObjects = JSON.parseObject(source);
            jsonObjects.remove("signData");
            jsonObjects.remove("encData");
            jsonObjects.remove("business"); // ? 处理不存在的字段

            jsonObjects.put("data", jsonObject);
            removeEmpty(jsonObject);
            String str = SignUtil.getSignText(jsonObjects, sm4key);
            msgByte = str.getBytes(StandardCharsets.UTF_8);
        } catch (Exception e) {
            msgByte = msg.getBytes();
        }
        byte[] signatureByte = Base64.getDecoder().decode(signData),
                chnlSecretByte = sm4key.getBytes(),
                pubKeyByte = Base64.getDecoder().decode(pubKey);
        return EasyGmUtils.verifySm3WithSm2(msgByte, chnlSecretByte, signatureByte, pubKeyByte);
    }


    /**
     * sm4加密
     *
     * @param chnlId  渠道id
     * @param sm4key  渠道sm4密钥
     * @param message 待加密报文
     * @return 加密后的报文内容 String
     */
    public static String sm4Encrypt(String chnlId, String sm4key, String message) {
        //用appId加密appSecret获取新秘钥
        byte[] appSecretEncData = EasyGmUtils.sm4Encrypt(chnlId.substring(0, 16).getBytes(StandardCharsets.UTF_8), sm4key.getBytes(StandardCharsets.UTF_8));
        //新秘钥串
        byte[] secKey = Hex.toHexString(appSecretEncData).toUpperCase().substring(0, 16).getBytes(StandardCharsets.UTF_8);
        //加密数据
        return Hex.toHexString(EasyGmUtils.sm4Encrypt(secKey, message.getBytes(StandardCharsets.UTF_8))).toUpperCase();
    }

    /**
     * sm4解密
     *
     * @param chnlId  渠道id
     * @param sm4key  渠道sm4密钥
     * @param message 待解密报文
     * @return 解密后的报文 String
     */
    public static String sm4Decrypt(String chnlId, String sm4key, String message) {
        //生产解密key
        byte[] appSecretEncDataDecode = EasyGmUtils.sm4Encrypt(chnlId.substring(0, 16).getBytes(StandardCharsets.UTF_8), sm4key.getBytes(StandardCharsets.UTF_8));
        byte[] secKeyDecode = Hex.toHexString(appSecretEncDataDecode).toUpperCase().substring(0, 16).getBytes(StandardCharsets.UTF_8);
        return new String(EasyGmUtils.sm4Decrypt(secKeyDecode, Hex.decode(message)), StandardCharsets.UTF_8);
    }

    private final static String version = "1.0.0";
    private final static String encType = "SM4";
    private final static String signType = "SM2";

    /**
     * 创建请求报文
     *
     * @param chnlId    渠道id
     * @param encData   加密的报文
     * @param signData  签名的报文
     * @param transType 请求接口名
     * @return
     */
    public static JSONObject buildMsg(String chnlId, String encData, String signData, String transType) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("appId", chnlId);
        jsonObject.put("encData", encData);
        jsonObject.put("encType", encType);
        jsonObject.put("signData", signData);
        jsonObject.put("signType", signType);
        jsonObject.put("timestamp", System.currentTimeMillis());
        jsonObject.put("transType", transType);
        jsonObject.put("version", version);
        return jsonObject;
    }


    public static PropertyFilter propertyFilter = (object, name, value) ->
            !List.of("originalRxFile", "rxFile", "encData").contains(name);

    /**
     * 创建和加密请求报文
     *
     * @param chnlId    渠道id
     * @param sm4key    渠道sm4密钥
     * @param prvkey    渠道私钥
     * @param transType 请求接口名
     * @param body      原始未加密的请求报文体
     * @return
     */
    public static JSONObject encryptMsg(String chnlId, String sm4key, String prvkey, JSONObject body) {
        log.info("国密encryptMsg原始报文：{}", JSON.toJSONString(body, propertyFilter));
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("appId", chnlId);
        jsonObject.put("encType", encType);
        jsonObject.put("data", body);
        jsonObject.put("signType", signType);
        jsonObject.put("timestamp", System.currentTimeMillis());
        jsonObject.put("version", version);
        //加密后的报文
        String encData = sm4Encrypt(chnlId, sm4key, body.toJSONString());
        //签名
        String signData = signature(jsonObject.toJSONString(), sm4key, prvkey);
        jsonObject.fluentRemove("data");
        jsonObject.put("encData", encData);
        jsonObject.put("signData", signData);
        return jsonObject;
    }

    /**
     * 解密报文
     *
     * @param jsonObject 医保电子凭证响应的原始加密报文
     * @param sm4key     渠道sm4密钥
     * @param pubKey     平台公钥
     * @param chnlId     渠道id（兼容旧中台报文返回参数无渠道id参数情况）
     * @return
     */
    public static JSONObject decryptMsg(String chnlId, String sm4key, String pubKey, JSONObject jsonObject) {
        String msg = (String) jsonObject.get("encData");
        String message = (String) jsonObject.get("message");
        Object code = jsonObject.get("code");
        if (code != null && !"0".equals(code.toString())) {
            return jsonObject;
//            throw new RuntimeException(message);
        }
        //解密
        String msgS = sm4Decrypt(chnlId, sm4key, msg);
        //验签
        String signData = (String) jsonObject.get("signData");
        boolean flag = verify(msgS, jsonObject.toJSONString(), signData, sm4key, pubKey);
        if (!flag) {
            throw new RuntimeException("验签失败！！！");
        }
        jsonObject.putAll(JSON.parseObject(msgS));
        return jsonObject;
    }

    /**
     * 移除json中空值的键值对
     *
     * @param jsonObject
     */
    private static void removeEmpty(JSONObject jsonObject) {
        Iterator<Map.Entry<String, Object>> it = jsonObject.entrySet().iterator();
        while (it.hasNext()) {
            Map.Entry<String, Object> entry = it.next();
            Object value = entry.getValue();
            if (value instanceof JSONArray) {
                JSONArray jsonArray = (JSONArray) value;
                // 数组长度为0时将其处理,防止Gson转换异常
                if (jsonArray.isEmpty()) {
                    it.remove();
                } else {
                    for (Object o : jsonArray) {
                        JSONObject asJsonObject = (JSONObject) o;
                        removeEmpty(asJsonObject);
                    }
                }
            }
            if (value instanceof JSONObject) {
                JSONObject asJsonObject = (JSONObject) value;
                removeEmpty(asJsonObject);
            }
            if (value == null) {
                it.remove();
            }
            if (value instanceof String && StringUtil.isEmpty(value)) {
                it.remove();
            }
        }
    }


    public static <T> T deepClone(T src) {
        if (src instanceof Serializable) {
            return deepCloneByJson(src);
        }
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        ObjectOutputStream oos = null;
        ObjectInputStream ois = null;

        try {
            //对象写入内存
            oos = new ObjectOutputStream(baos);
            oos.writeObject(src);
            //从内存中读回来
            ois = new ObjectInputStream(new ByteArrayInputStream(baos.toByteArray()));
            return (T) ois.readObject();
        } catch (IOException | ClassNotFoundException ex) {
        } finally {
            // 关闭资源  字节数组输入输出流无需关闭
            if (ois != null) {
                try {
                    ois.close();
                } catch (IOException ignored) {
                }
            }
            if (oos != null) {
                try {
                    oos.close();
                } catch (IOException ignored) {
                }
            }
        }
        // 序列化克隆失败 则选择json克隆
        return deepCloneByJson(src);
    }

    private static <T> T deepCloneByJson(T src) {
        return (T) JSONObject.parseObject(JSON.toJSONString(src), src.getClass());
    }

}
