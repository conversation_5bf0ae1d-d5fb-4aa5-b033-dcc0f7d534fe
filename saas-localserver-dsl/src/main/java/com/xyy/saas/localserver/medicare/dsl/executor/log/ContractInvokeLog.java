package com.xyy.saas.localserver.medicare.dsl.executor.log;

import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import lombok.Data;
import org.springframework.util.StringUtils;

/**
 * @Desc
 * <AUTHOR>
 * @Date Created in 2025/02/27 20:58
 */
@Data
public class ContractInvokeLog {

    /**
     * 请求的完整URL
     */
    private String url;

    /**
     * 请求方法(GET/POST等)
     */
    private String method;

    /**
     * 请求参数
     */
    private String requestBody;

    /**
     * 原始响应体
     */
    private String responseBody;


    public void setRequestBody(String requestBody) {
        if (!StringUtils.hasLength(requestBody)) {
            return;
        }
        try {
            JSONObject resultMap = JSONUtil.parseObj(requestBody);
            this.requestBody = JSON.toJSONString(resultMap, ContractInvokeContext.propertyFilter);
        } catch (Exception e) {
            this.requestBody = responseBody;
        }
    }

    public void setResponseBody(String responseBody) {
        if (!StringUtils.hasLength(responseBody)) {
            return;
        }
        try {
            JSONObject resultMap = JSONUtil.parseObj(responseBody);
            this.responseBody = JSON.toJSONString(resultMap, ContractInvokeContext.propertyFilter);
        } catch (Exception e) {
            this.responseBody = responseBody;
        }
    }
}
