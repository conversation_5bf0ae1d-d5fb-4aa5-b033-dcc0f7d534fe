package com.xyy.saas.localserver.medicare.dsl.protocol;

import cn.hutool.core.map.MapUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.google.common.net.MediaType;
import com.xyy.saas.localserver.medicare.dsl.config.base.ContractConfig;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContext;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContextHolder;
import com.xyy.saas.localserver.medicare.dsl.format.FormatType;
import com.xyy.saas.localserver.medicare.dsl.parse.JsonParameterParser;
import com.xyy.saas.localserver.medicare.dsl.parse.ObjectValueParser;
import com.xyy.saas.localserver.medicare.dsl.protocol.request.RequestBodyBuilderStrategy;
import com.xyy.saas.localserver.medicare.dsl.protocol.response.ProtocolResponse;
import lombok.ToString;
import lombok.experimental.SuperBuilder;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.net.MalformedURLException;
import java.net.URL;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@SuperBuilder
@ToString
public class HttpFormUrlEncodedProtocolClient extends ProtocolClient{
    private static final ObjectValueParser valueParser = new JsonParameterParser();

    /**
     * @return
     */
    @Override
    public ProtocolClient toGenerateBody() {
        try {
            encryptValue();
            RequestBodyBuilderStrategy requestBodyBuilderStrategy = bodyStrategy.getClazz().getDeclaredConstructor().newInstance();
            String body = requestBodyBuilderStrategy.generateBody(inputObject);
            body = encryptBodyString(body);
            encryptBodyInHeader();
            inputObject.setBody(body);
            Map<String, String> commonHeaders = DSLContextHolder.getContext().getCommonHeader();
            if (MapUtil.isNotEmpty(commonHeaders)) {
                inputObject.addHeader(commonHeaders);
            }
        } catch (Exception e) {
            log.error("生成body数据失败,input:{}", inputObject);
            throw new RuntimeException("生成body数据失败,请联系开发");
        }
        return this;
    }


    /**
     * 加密header或者Body的某个key
     */
    private void encryptValue() {
        if (!inputObject.getContractConfig().isCommon()) {
            return;
        }
        ContractConfig.EncryptConfig encryptConfig = inputObject.getContractConfig().getCommonConfig().getEncrypt();
        if (encryptConfig == null) {
            return;
        }
        DSLContext context = DSLContextHolder.getContext();
        //加密header值
        Map<String, String> headerEncryptConfig = encryptConfig.getHeader();
        if (!CollectionUtils.isEmpty(headerEncryptConfig)) {
            headerEncryptConfig.forEach((k, v) -> {
                inputObject.getHeader().put(k, context.parseValue(v));
            });
        }
        //加密Body的某个key值
        Map<String, String> bodysEncryptConfig = encryptConfig.getBodys();
        if (!CollectionUtils.isEmpty(bodysEncryptConfig)) {
            bodysEncryptConfig.forEach((k, v) -> {
                inputObject.getBodys().put(k, context.parseValue(v));
            });
        }
    }

    /**
     * 加密整个body
     */
    private String encryptBodyString(String body) {
        if (!inputObject.getContractConfig().isCommon()) {
            return body;
        }
        ContractConfig.EncryptConfig encryptConfig = inputObject.getContractConfig().getCommonConfig().getEncrypt();
        if (encryptConfig == null) {
            return body;
        }
        if (StringUtils.hasLength(encryptConfig.getBody())) {
            DSLContext context = DSLContextHolder.getContext();
            return context.parseValue(encryptConfig.getBody());
        }
        return body;
    }

    private void encryptBodyInHeader() {
        if (!inputObject.getContractConfig().isCommon()) {
            return;
        }
        ContractConfig.EncryptConfig encryptConfig = inputObject.getContractConfig().getCommonConfig().getEncrypt();
        if (encryptConfig == null) {
            return;
        }
        DSLContext context = DSLContextHolder.getContext();
        Map<String, String> bodysEncryptInHeaderConfig = encryptConfig.getBodyInHeader();
        if (!CollectionUtils.isEmpty(bodysEncryptInHeaderConfig)) {
            bodysEncryptInHeaderConfig.forEach((k, v) -> {
                inputObject.getHeader().put(k, context.parseValue(v));
            });
        }
    }

    /**
     * @return
     */
    @Override
    public ProtocolResponse toInvoker() {
        ContractConfig.FunctionConfig functionConfig = this.inputObject.getFunctionConfig();
        String url = valueParser.parseValue(functionConfig.getDomain(), DSLContextHolder.getContext()) + valueParser.parseValue(functionConfig.getPath(), DSLContextHolder.getContext());
        int timeout = functionConfig.getTimeout() * 1000;
        Map<String, String> header = this.inputObject.getHeader();


        Map<String, String> param = new HashMap<>();
        inputObject.getBodys().forEach((key, value) ->
                param.put(key, value != null ? String.valueOf(value) : null)
        );
        String rst;
        try {
             rst = post(url, param, header,timeout);
        }catch (Exception e){
            throw new RuntimeException("HttpFormUrlEncodedProtocolClient异常");
        }
        ProtocolResponse response = ProtocolResponse.builder().code(0).body(rst).build();

        String body = decryptBodyString(rst);
        JSONObject resultMap = JSONUtil.parseObj(body);
        decryptValue(resultMap);
        response.setResultMap(resultMap);
        return response;
    }

    private static SSLConnectionSocketFactory sslsf = null;
    private static CloseableHttpClient createHttpClient(String url)
            throws KeyManagementException, NoSuchAlgorithmException, MalformedURLException {
        URL u = new URL(url);
        CloseableHttpClient httpclient = null;
        if ("https".equals(u.getProtocol())) {
            httpclient = HttpClients.custom().setSSLSocketFactory(sslsf).build();
        } else {
            httpclient = HttpClients.createDefault();
        }

        return httpclient;
    }

    public static String post(String url, Map<String, String> params, Map<String, String> heads,int timeOut)
            throws IOException, KeyManagementException, NoSuchAlgorithmException {
        CloseableHttpClient httpclient = HttpFormUrlEncodedProtocolClient.createHttpClient(url);
        HttpPost httpPost = new HttpPost(url);
        List<NameValuePair> nvps = new ArrayList<NameValuePair>();
        if (params != null) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                nvps.add(new BasicNameValuePair(entry.getKey(), entry.getValue()));
            }
        }

        if (heads != null) {
            for (Map.Entry<String, String> entry : heads.entrySet()) {
                httpPost.setHeader(entry.getKey(), entry.getValue());
            }
        }
        RequestConfig requestConfig = RequestConfig.custom()
                .setConnectTimeout(timeOut).setConnectionRequestTimeout(timeOut)
                .setSocketTimeout(timeOut).build();
        httpPost.setConfig(requestConfig);
        httpPost.setEntity(new UrlEncodedFormEntity(nvps, "utf-8"));
        CloseableHttpResponse response = httpclient.execute(httpPost);
        return EntityUtils.toString(response.getEntity());
    }


    /**
     * 对Body的某个key进行解密
     */
    private void decryptValue(Map<String, Object> resultMap) {
        if (!inputObject.getContractConfig().isCommon()) {
            return;
        }
        ContractConfig.DecryptConfig decryptConfig = inputObject.getContractConfig().getCommonConfig().getDecrypt();
        if (decryptConfig == null) {
            return;
        }
        //解密Body的某个key值
        Map<String, String> bodysDecryptConfig = decryptConfig.getBodys();
        if (!CollectionUtils.isEmpty(bodysDecryptConfig)) {
            DSLContext context = DSLContextHolder.getContext();
            ContractConfig.FunctionConfig functionConfig = this.inputObject.getFunctionConfig();
            bodysDecryptConfig.forEach((field, expression) -> {
                Object responseObject = resultMap.get(field);
                if (responseObject instanceof String s) {
                    String value = context.parseValue(expression, s);
//                    Object configObject = functionConfig.getResponse().get(field);
//                    if (configObject instanceof Map<?,?>) {
//                        ;
                    resultMap.put(field, JSONUtil.parseObj(value));
//                    }
//                    resultMap.put(field, );
                }
            });
        }
    }

    /**
     * 解密整个body
     */
    private String decryptBodyString(String body) {
        if (!inputObject.getContractConfig().isCommon()) {
            return body;
        }
        ContractConfig.DecryptConfig decryptConfig = inputObject.getContractConfig().getCommonConfig().getDecrypt();
        if (decryptConfig == null) {
            return body;
        }
        if (StringUtils.hasLength(decryptConfig.getBody())) {
            DSLContext context = DSLContextHolder.getContext();
            return context.parseValue(decryptConfig.getBody());
        }
        return body;
    }

    /**
     *
     */
    @Override
    public void parseResponseBody() {

    }
}
