package com.xyy.saas.localserver.medicare.dsl.protocol.response;

import lombok.Builder;
import lombok.Data;

import java.util.Map;

/**
 * @Desc 第三方响应
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/22 20:42
 */
@Data
@Builder
public class ProtocolResponse {


    /*请求状态码*/
    private int requestCode;

    /*业务状态码*/
    private int code;

    private String body;

    private Map<String, Object> resultMap;

}
