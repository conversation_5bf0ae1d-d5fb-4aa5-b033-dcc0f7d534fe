package com.xyy.saas.localserver.medicare.dsl.util;

import com.alibaba.fastjson.JSONObject;

import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * @Author:chenxiaoyi
 * @Date:2025/07/10 10:35
 */
public class DslBusinessUtil {


    /**
     * JSONString 序列化后的base64 字符值
     *
     * @return
     */
    public static JSONObject changeKeyBase64Value(JSONObject body, String key) {
        final Object o = body.get(key);
        final String s = Base64.getEncoder().encodeToString(JSONObject.toJSONString(o).getBytes(StandardCharsets.UTF_8));
        body.put(key, s);
        return body;
    }

}
