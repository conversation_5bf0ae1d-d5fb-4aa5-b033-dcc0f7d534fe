package com.xyy.saas.localserver.medicare.dsl;

import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.core.annotation.AliasFor;
import org.springframework.test.context.ActiveProfiles;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * @Desc 测试用例基础注解
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/14 17:35
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@SpringBootTest
@ActiveProfiles
public @interface SaaSLocalServerTest {

    String[] value() default {};

    @AliasFor(
            annotation = ActiveProfiles.class
    )
    String[] profiles() default {"test"};

    @AliasFor(
            annotation = SpringBootTest.class
    )
    SpringBootTest.WebEnvironment webEnvironment() default SpringBootTest.WebEnvironment.RANDOM_PORT;

}