package com.xyy.saas.localserver.medicare.dsl.response;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/09/05 16:55
 */
@Data
public class BalanceResult {

    @Alias("psn_no")
    private String personNo;

    /* 人员证件类型 */
    @Alias("psn_cert_type")
    private String personType;

    /* 证件号码,一定是身份证号 */
    @Alias("certno")
    private String certNo;

    /* 人员姓名 */
    @Alias("psn_name")
    private String personName;

    /* 性别 */
    @Alias("gend")
    private String sex;

    /* 民族 */
    @Alias("naty")
    private String nation;

    /* 出生日期 */
    @Alias("brdy")
    private String birthday;

    /*年龄*/
    @Alias("age")
    private String age;

    /*险种类型*/
    @Alias("insutype")
    private String insutype;

    /*就诊凭证类型:身份证、社保卡、电子凭证*/
    @Alias("mdtrt_cert_type")
    private String certType;

    /*医疗类别*/
    @Alias("med_type")
    private String medicareType;

    /*医疗费总额*/
    @Alias("medfee_sumamt")
    private BigDecimal totalAmount;

    /*全自费金额*/
    @Alias("fulamt_ownpay_amt")
    private BigDecimal ownPayAmount;

    /*超限价自费费用*/
    @Alias("overlmt_selfpay")
    private BigDecimal overlmtSelfpayAmount;

    /*先行自付金额*/
    @Alias("preselfpay_amt")
    private BigDecimal preselfPayAmount;

    /*符合政策范围金额*/
    @Alias("inscp_scp_amt")
    private BigDecimal scpAmount;

    /*实际支付起付线*/
    @Alias("act_pay_dedc")
    private BigDecimal actualPayLine;

    /*基本医疗保险统筹基金支出*/
    @Alias("hifp_pay")
    private BigDecimal poolFundPayAmount;

    /*个人账户支出*/
    @Alias("acct_pay")
    private BigDecimal accountPayAmount;

    /*个人现金支出*/
    @Alias("psn_cash_pay")
    private BigDecimal cashPayAmount;

    /*余额*/
    @Alias("balc")
    private BigDecimal balance;

    /*个人账户共济支付金额*/
    @Alias("acct_mulaid_pay")
    private BigDecimal accountSharePay;

    /*医药机构结算ID,存放发送方报文ID*/
    @Alias("medins_setl_id")
    private String balanceNo;

    /*清算经办机构*/
    @Alias("clr_optins")
    private String clearAreaCode;

    /*清算方式*/
    @Alias("clr_way")
    private String clearWay;

    /*清算类别:本地、异地、药店购药、门诊购药*/
    @Alias("clr_type")
    private String clearType;


    @Data
    public static class BalanceDetailResult {

    }


}
