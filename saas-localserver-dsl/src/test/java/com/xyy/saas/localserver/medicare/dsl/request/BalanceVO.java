package com.xyy.saas.localserver.medicare.dsl.request;

import com.xyy.saas.localserver.medicare.dsl.executor.value.DynamicBusiness;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Desc 结算信息
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/09/05 15:52
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = false)
public class BalanceVO extends DynamicBusiness {

    /*小票号(机构内唯一)、订单号*/
    private String balanceNo;

    /*实际支付总金额*/
    private BigDecimal totalAmount;

    /*商品列表*/
    private List<BalanceDetail> details;

    public BigDecimal getTotalAmount() {
        if (totalAmount == null) {
            totalAmount = details.stream().map(BalanceDetail::getSubtotal).reduce(BigDecimal.ZERO, BigDecimal::add);
        }
        return totalAmount;
    }


    @Data
    @Builder
    public static class BalanceDetail {

        /*国家医保目录编码*/
        private String medicareCode;

        /*商品编码*/
        private String productPref;

        /*小计*/
        private BigDecimal subtotal;

        /*数量*/
        private BigDecimal quantity;

        /*价格*/
        private BigDecimal price;

        /*使用方式*/
        private String usedWay;

        public BigDecimal getSubtotal() {
            if (subtotal == null) {
                subtotal = price.multiply(quantity);
            }
            return subtotal;
        }


    }


}
