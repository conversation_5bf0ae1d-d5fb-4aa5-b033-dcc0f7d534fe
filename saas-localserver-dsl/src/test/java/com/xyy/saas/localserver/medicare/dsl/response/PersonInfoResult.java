package com.xyy.saas.localserver.medicare.dsl.response;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Desc 人员信息
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2023/08/30 13:22
 */
@Data
public class PersonInfoResult {

    /* 人员编号 */
//    @Alias("aliasSubName")
    @Alias("psn_no")
    private String personNo;

    /* 人员证件类型 */
    @Alias("psn_cert_type")
    private String personType;

    /* 证件号码,一定是身份证号 */
    @<PERSON>as("certno")
    private String certNo;

    /* 人员姓名 */
    @Alias("psn_name")
    private String personName;

    /* 性别 */
    @Alias("gend")
    private String sex;

    /* 民族 */
    @Alias("naty")
    private String nation;

    /* 出生日期 */
    @Alias("brdy")
    private String birthday;

    /* 年龄 */
    @Alias("age")
    private String age;

    /* 参保人信息 */
    @Alias("insuinfo")
    private InsurantInfo[] insurantInfos;

    /* 身份信息列表 */
    @Alias("idetinfo")
    private IdentityInfo[] identityInfos;

    /**
     * 参保信息列表
     */
    @Data
    public static class InsurantInfo {

        /* 余额,个人账户 */
        @Alias("balc")
        private BigDecimal balance;

        /* 险种类型 */
        @Alias("insutype")
        private String insuredType;

        /* 人员类别 */
        @Alias("psn_type")
        private String personType;

        /* 人员参保状态 */
        @Alias("psn_insu_stas")
        private String personInsuredStatus;

        /* 人员参保日期 */
        @Alias("psn_insu_date")
        private String personInsuredDate;

        /* 暂停参保日期 */
        @Alias("paus_insu_date")
        private String pauseInsuredDate;

        /* 公务员标志 */
        @Alias("cvlserv_flag")
        private String civilServantFlag;

        /* 参保地医保区划,参保统筹区 */
        @Alias("insuplc_admdvs")
        private String areaCode;

        /* 单位名称 */
        @Alias("emp_name")
        private String employerName;
    }

    /**
     * 身份信息列表
     */
    @Data
    public static class IdentityInfo {

        /* 人员身份类别 */
        @Alias("psn_idet_type")
        private String identityType;

        /* 人员类别等级 */
        @Alias("psn_type_lv")
        private String identityLevel;

        /* 备注 */
        @Alias("memo")
        private String memo;

        /* 开始时间 */
        @Alias("begntime")
        private String beginTime;

        /* 结束时间 */
        @Alias("endtime")
        private String endTime;

    }

}
