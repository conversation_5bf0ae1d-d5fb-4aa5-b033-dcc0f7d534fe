delete
from system_tenant
where id != 1;
delete
from system_tenant_certificate
where tenant_id != 1;
delete
from system_users
where id != 1;
delete
from system_tenant_user_relation
where user_id != 1;
delete
from system_role
where tenant_id != 1;

truncate table system_tenant_param_config;
truncate table system_tenant_package;
truncate table saas_tenant_package_relation;
truncate table saas_tenant_package_cost;
truncate table saas_tenant_package_cost_log;

truncate table saas_inquiry_doctor;
truncate table saas_inquiry_doctor_practice;
truncate table saas_inquiry_doctor_status;
truncate table saas_inquiry_filing;
truncate table saas_doctor_billing;
truncate table saas_doctor_work_record;
truncate table saas_doctor_audited_record;
truncate table saas_inquiry_hospital_doctor_relation;
truncate table saas_doctor_quick_reply_msg;
truncate table saas_inquiry_hospital;
truncate table saas_inquiry_hospital_department_relation;

truncate table saas_inquiry_diagnosis_department_relation;
truncate table saas_inquiry_option_config;
truncate table saas_inquiry_hospital_setting;

truncate table saas_inquiry_pharmacist;
truncate table saas_inquiry_profession_identification;
truncate table saas_inquiry_profession_identification;
truncate table saas_inquiry_record;
truncate table saas_inquiry_record_detail;
truncate table saas_inquiry_patient_info;
truncate table saas_inquiry_prescription;
truncate table saas_inquiry_prescription_audit;
truncate table saas_inquiry_user_signature_information;
truncate table saas_inquiry_signature_ca_auth;