#门店表
DROP TABLE IF EXISTS system_tenant;
create table system_tenant
(
  id                      bigint auto_increment comment '门店编号' primary key,
  pref                    varchar(64) default ''                not null comment '编码',
  name                    varchar(64)                           not null comment '门店名',
  contact_user_id         bigint null comment '联系人的用户id',
  contact_name            varchar(32)                           not null comment '联系人',
  contact_mobile          varchar(32) null comment '联系手机',
  account_count           int                                   not null comment '账号数量',
  business_license_name   varchar(64) null comment '营业执照名称',
  business_license_number varchar(64) null comment '营业执照号',

  status                  tinyint(2)  default 0                 not null comment '门店状态（0正常 1停用）',
  province                varchar(16)                           NOT NULL DEFAULT '' COMMENT '省',
  province_code           varchar(16) DEFAULT NULL COMMENT '省编码',
  city                    varchar(16)                           NOT NULL DEFAULT '' COMMENT '市',
  city_code               varchar(16) DEFAULT NULL COMMENT '市编码',
  area                    varchar(16)                           NOT NULL DEFAULT '' COMMENT '区',
  area_code               varchar(16) DEFAULT NULL COMMENT '区编码',
  address                 varchar(256)                          NOT NULL DEFAULT '' COMMENT '药店地址',
  env_tag                 varchar(16) default 'prod'            not null comment '环境标志：prod-真实数据；test-测试数据；show-线上演示数据',

  creator                 varchar(64) default ''                not null comment '创建者',
  create_time             datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
  updater                 varchar(64) default '' null comment '更新者',
  update_time             datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted                 bit         default b'0'              not null comment '是否删除'
) comment '门店表';

alter table system_tenant
  add column type tinyint(2) default 1 not null comment '租户类型（1-单店 2连锁门店 3连锁总部）' after name,
    add column head_tenant_id bigint null comment '租户编号（总部）' after type,
    add index idx_head_tenant_id (head_tenant_id),
    add index idx_status_type (status, type);


#门店资质证件信息表
DROP TABLE IF EXISTS system_tenant_certificate;
create table system_tenant_certificate
(
  id                   int auto_increment comment '主键' primary key,
  tenant_id            bigint        default 0                 not null comment '门店编号',
  #                    biz_type tinyint(2)  default 0                     not null comment '业务类型 0-问诊,1-saas...',
  certificate_type     tinyint(2)    default 1                 not null comment '证件类型 1营业执照 2药品经营许可证 3药品经营质量管理规范认证号',
  certificate_name     varchar(256) null comment '证件名称',
  certificate_no       varchar(256)  default ''                not null comment '证件号',
  certificate_img_urls varchar(2048) default ''                not null comment '证件url地址',
  register_time        datetime null comment '注册时间',
  valid_time           datetime null comment '有效期至',

  creator              varchar(64)   default ''                not null comment '创建者',
  create_time          datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
  updater              varchar(64)   default '' null comment '更新者',
  update_time          datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted              bit           default b'0'              not null comment '是否删除'
) comment '门店资质证件信息表';
CREATE INDEX idx_tenant_id ON system_tenant_certificate (tenant_id) comment '门店id索引';


#门店参数配置表
DROP TABLE IF EXISTS system_tenant_param_config;
create table system_tenant_param_config
(
  id          int auto_increment comment '主键' primary key,
  tenant_id   bigint       default 0                 not null comment '门店编号',
  biz_type    tinyint(2)   default 0                 not null comment '系统类型 0-问诊,1-saas...',
  param_type  int          default 1                 not null comment '参数类型 eg:1荷叶问诊服务 x所属互联网医院',
  param_name  varchar(512) default 1                 not null comment '参数类型 eg:1荷叶问诊服务 x所属互联网医院',
  param_value varchar(512) default ''                not null comment '值 eg:1 开启 3 自动审方 7 盖州渤海医院',
  description varchar(512) default ''                not null comment '功能描述',

  creator     varchar(64)  default ''                not null comment '创建者',
  create_time datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater     varchar(64)  default '' null comment '更新者',
  update_time datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted     bit          default b'0'              not null comment '是否删除'
) comment '门店参数配置表';
CREATE INDEX idx_tenant_id_param_type ON system_tenant_param_config (tenant_id, param_type) comment '门店id参数类型索引';


#用户信息表
create table system_users
(
  id          bigint auto_increment comment '用户ID' primary key,
  #           tenant_id bigint       default 0                 not null comment '门店编号'，
  username    varchar(32)                            not null comment '用户账号',
  password    varchar(128) default ''                not null comment '密码',
  nickname    varchar(32)                            not null comment '用户昵称',
  remark      varchar(512) null comment '备注',
  #           dept_id bigint                                 null comment '部门ID',
  #           post_ids varchar(256)                           null comment '岗位编号数组',
  email       varchar(64)  default '' null comment '用户邮箱',
  mobile      varchar(32)  default '' null comment '手机号码',
  sex         tinyint      default 0 null comment '用户性别',
  id_card     varchar(32) null comment '身份证号',
  avatar      varchar(512) default '' null comment '头像地址',
  status      tinyint      default 0                 not null comment '帐号状态（0正常 1停用）',
  login_ip    varchar(64)  default '' null comment '最后登录IP',
  login_date  datetime null comment '最后登录时间',
  guid        varchar(64)  default '' null comment '原系统用户guid',

  creator     varchar(64)  default '' null comment '创建者',
  create_time datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater     varchar(64)  default '' null comment '更新者',
  update_time datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted     bit          default b'0'              not null comment '是否删除'
) comment '用户信息表';


DROP TABLE IF EXISTS system_tenant_user_relation;
create table system_tenant_user_relation
(
  id               bigint auto_increment comment '用户ID' primary key,
  user_id          bigint                                not null comment '用户ID',
  tenant_id        bigint      default 0                 not null comment '门店编号',
  dept_id          bigint null comment '部门ID',
  post_ids         varchar(256) null comment '岗位编号数组',
  join_time        datetime null comment '入职时间',
  resignation_time datetime null comment '离职时间',
  status           tinyint(2)                            null comment '员工状态 0在职 1冻结 2离职',

  creator          varchar(64) default ''                not null comment '创建者',
  create_time      datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
  updater          varchar(64) default '' null comment '更新者',
  update_time      datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted          bit         default b'0'              not null comment '是否删除'
) comment '门店员工关系表';
create index idx_user_id on system_tenant_user_relation (user_id) comment 'user_id索引';
create index idx_tenant_id on system_tenant_user_relation (tenant_id) comment 'tenant_id索引';


#saas
-OA用户白名单
DROP TABLE IF EXISTS saas_oa_white_list;
CREATE TABLE saas_oa_white_list
(
  id          int auto_increment comment '主键' primary key,
  biz_type    tinyint(2)   default 0                 not null comment '业务类型 0-问诊,1-saas...',
  username    varchar(32)  default ''                not null comment '用户名',
  flower_name varchar(32)  default ''                not null comment '花名',
  remark      varchar(255) default '' null comment '备注',

  creator     varchar(64)  default ''                not null comment '创建者',
  create_time datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater     varchar(64)  default '' null comment '更新者',
  update_time datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted     bit          default b'0'              not null comment '是否删除'
) COMMENT = 'saas-OA用户白名单';
create index idx_biz_type_username on saas_oa_white_list (biz_type, username) comment 'oa用户名索引';


#门店套餐表
DROP TABLE IF EXISTS system_tenant_package;
create table system_tenant_package
(
  id                    bigint auto_increment comment '套餐id' primary key,
  pref                  varchar(64)    default ''                not null comment '编码',
  name                  varchar(64)                              not null comment '套餐名',
  biz_type              tinyint(2)     default 0                 not null comment '业务类型 0-问诊,1-saas...',
  status                tinyint(2)     default 0                 not null comment '套餐状态（0正常 1停用）',
  remark                varchar(256)   default '' null comment '备注',
  menu_ids              varchar(4096)                            not null comment '关联的菜单编号',
  sorted                int            default 0 null comment '排序',
  region_arr            text null comment '可见地区编码数组为空默认全国',
  inquiry_package_items json null comment '问诊套餐其他信息(次数,年限,价格,文案)',
  hospital_prefs        varchar(1024) null comment '问诊医院ids',
  price                 decimal(10, 4) default 0.0000            not null comment '套餐价格',
  term                  int            default 1                 not null comment '套餐期限',
  term_type             int            default 1                 not null comment '套餐期限时间类型 时间枚举字典年月日',
  inquiry_way_types     varchar(1024) null comment '问诊方式类型',

  creator               varchar(64)    default ''                not null comment '创建者',
  create_time           datetime       default CURRENT_TIMESTAMP not null comment '创建时间',
  updater               varchar(64)    default '' null comment '更新者',
  update_time           datetime       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted               bit            default b'0'              not null comment '是否删除'
) comment '门店套餐表';


#门店套餐开通关系表
DROP TABLE IF EXISTS saas_tenant_package_relation;
create table saas_tenant_package_relation
(
  id                    bigint auto_increment comment '开通id'
    primary key,
  pref                  varchar(64)    default ''                not null comment '编码',
  package_id            bigint         default 0                 not null comment '门店套餐编号 0自定义套餐',
  tenant_id             bigint         default 0                 not null comment '门店编号',
  package_name          varchar(256)                             not null comment '套餐名',
  inquiry_package_items json null comment '问诊套餐其他信息(类型,次数)',
  inquiry_way_types     varchar(1024) null comment '问诊方式类型',
  hospital_prefs        varchar(1024) null comment '问诊医院ids',
  price                 decimal(10, 4) default 0.0000            not null comment '套餐价格',
  status                tinyint(2)     default 0                 not null comment '开通状态, 0正常, 1暂停, 2退款, 3作废',
  start_time            datetime null comment '服务开始时间',
  end_time              datetime null comment '服务结束时间',
  sign_time             datetime null comment '签约日期',
  payment_type          tinyint(2)     default 1                 not null comment '收款方式：0线上，1线下',
  package_nature        tinyint(2)     default 1                 not null comment '套餐包性质 0赠送 1购买 2体验',
  sign_user             varchar(64)    default ''                not null comment '签约人',
  proxy_user            varchar(64)    default ''                not null comment '代理人(自然人/代理商等)',
  sign_channel          int            default 0                 not null comment '签约渠道(eg:3智鹿)',
  actual_amount         decimal(20, 6) null comment '实收金额 ',
  collect_account       int null comment '收款账户 eg:(微信对公-成都)',
  pay_no                varchar(256)   default ''                not null comment '付款流水号',
  pay_voucher_urls      varchar(2048) null comment '支付凭证url',
  remark                varchar(256)   default '' null comment '备注',
  status_change_info    json null comment '数据状态变更信息',
  creator               varchar(64)    default ''                not null comment '创建者',
  create_time           datetime       default CURRENT_TIMESTAMP not null comment '创建时间',
  updater               varchar(64)    default '' null comment '更新者',
  update_time           datetime       default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted               bit            default b'0'              not null comment '是否删除'
) comment '门店套餐订单表';
create index idx_package on saas_tenant_package_relation (package_id) comment '套餐包id';
create index idx_sign_time on saas_tenant_package_relation (sign_time) comment '签约日期';
create index idx_tenant on saas_tenant_package_relation (tenant_id) comment '门店id';


-- 字典数据表
-- auto-generated definition
create table system_dict_data
(
  id           bigint auto_increment comment '字典编码'
    primary key,
  tenant_id    bigint       default 1                 not null comment '租户编号',
  sort         int          default 0                 not null comment '字典排序',
  label        varchar(100) default ''                not null comment '字典标签',
  value        varchar(100) default ''                not null comment '字典键值',
  dict_type    varchar(100) default ''                not null comment '字典类型',
  status       tinyint      default 0                 not null comment '状态（0正常 1停用）',
  end_node     tinyint(2)   default 0                 not null comment '状态末级节点 0是 1否',
  parent_value varchar(100) default ''                not null comment '父值,空无父值',
  color_type   varchar(100) default '' null comment '颜色类型',
  css_class    varchar(100) default '' null comment 'css 样式',
  remark       varchar(500) null comment '备注',
  creator      varchar(64)  default '' null comment '创建者',
  create_time  datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater      varchar(64)  default '' null comment '更新者',
  update_time  datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted      bit          default b'0'              not null comment '是否删除',
  constraint udx_tenant_id_dict_type_value
    unique (tenant_id, dict_type, value)
) comment '字典数据表' collate = utf8mb4_unicode_ci;

create index idx_dict_type_value
  on system_dict_data (dict_type, value);


#
app版本表
DROP TABLE IF EXISTS saas_app_version;
CREATE TABLE saas_app_version
(
  id                int auto_increment comment '主键' primary key,
  app_biz           tinyint(2)      default 0             not null comment 'app类型 0-荷叶问诊,1-智慧脸...',
  app_version       varchar(32)  default ''                not null comment 'app版本，如:v1.0.0',
  app_version_code  int          default 0                 not null comment '当前版本编码,如100',
  app_version_desc  varchar(512) default ''                not null comment 'app版本升级内容描述',
  os_type           varchar(32)  default ''                not null comment '系统类型: android 、 ios',
  min_os_type       varchar(32)  default ''                not null comment '当前版本支持的最低操作系统版本',
  download_url      varchar(512) default ''                not null comment '下载地址',
  released_channels varchar(64)  default ''                not null comment '已上架应用商店 0-腾讯 1-华为 2-OPPO  3-VIVO 4-小米',
  upgrade_type      tinyint(2)      default 0             not null comment '升级更新类型：0-强制更新  1-提示可选更新  2-不提示可选更新',
  upgrade_scope     tinyint(2)      default 0             not null comment '升级范围：0-全量升级  1-比例用户灰度   2-指定用户灰度',
  gray_ratio        int          default 0                 not null comment '灰度比例0-100',

  disable           bit          default b'0'              not null comment '是否禁用',
  creator           varchar(64)  default ''                not null comment '创建者',
  create_time       datetime     default CURRENT_TIMESTAMP not null comment '创建时间',
  updater           varchar(64)  default '' null comment '更新者',
  update_time       datetime     default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted           bit          default b'0'              not null comment '是否删除'
) COMMENT = 'App版本表';


#
app版本用户详情表 - 1
、指定用户升级时保存指定的租户   2
、用户忽略时保存指定的用户
DROP TABLE IF EXISTS saas_app_version_detail;
CREATE TABLE saas_app_version_detail
(
  id             int auto_increment comment '主键' primary key,
  app_version_id int         default 0                 not null comment 'app版本id',
  bussniss_type  tinyint(2)      default 0             not null comment '业务类型：0-灰度租户  1-用户忽略版本',
  tenant_id      bigint      default 0                 not null comment '灰度租户id',
  user_id        bigint      default 0                 not null comment '忽略用户id',

  creator        varchar(64) default ''                not null comment '创建者',
  create_time    datetime    default CURRENT_TIMESTAMP not null comment '创建时间',
  updater        varchar(64) default '' null comment '更新者',
  update_time    datetime    default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted        bit         default b'0'              not null comment '是否删除'
) COMMENT = 'app版本用户详情表';
create index idx_app_version_id_bussniss_type on saas_app_version_detail (app_version_id, bussniss_type);



#
会员用户表,小程序用户也会存这里
DROP TABLE IF EXISTS member_user;
CREATE TABLE `member_user`
(
  `id`                bigint(20) NOT NULL AUTO_INCREMENT COMMENT '编号',
  `mobile`            varchar(11)                                                  DEFAULT NULL COMMENT '手机号',
  `password`          varchar(100) NOT NULL                                        DEFAULT '' COMMENT '密码',
  `status`            tinyint(4) NOT NULL COMMENT '状态',
  `register_ip`       varchar(32)  NOT NULL COMMENT '注册 IP',
  `register_terminal` tinyint(4) DEFAULT NULL COMMENT '注册终端',
  `login_ip`          varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '最后登录IP',
  `login_date`        datetime                                                     DEFAULT NULL COMMENT '最后登录时间',
  `nickname`          varchar(30)  NOT NULL                                        DEFAULT '' COMMENT '用户昵称',
  `avatar`            varchar(512) NOT NULL                                        DEFAULT '' COMMENT '头像',
  `name`              varchar(30)                                                  DEFAULT '' COMMENT '真实名字',
  `sex`               tinyint(4) DEFAULT '0' COMMENT '用户性别',
  `area_id`           bigint(20) DEFAULT NULL COMMENT '所在地',
  `birthday`          datetime                                                     DEFAULT NULL COMMENT '出生日期',
  `mark`              varchar(255)                                                 DEFAULT NULL COMMENT '会员备注',
  `point`             int(11) NOT NULL DEFAULT '0' COMMENT '积分',
  `tag_ids`           varchar(255)                                                 DEFAULT NULL COMMENT '用户标签编号列表，以逗号分隔',
  `level_id`          bigint(20) DEFAULT NULL COMMENT '等级编号',
  `experience`        int(11) NOT NULL DEFAULT '0' COMMENT '经验',
  `group_id`          bigint(20) DEFAULT NULL COMMENT '用户分组编号',
  `creator`           varchar(64)                                                  DEFAULT '' COMMENT '创建者',
  `create_time`       datetime     NOT NULL                                        DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater`           varchar(64)                                                  DEFAULT '' COMMENT '更新者',
  `update_time`       datetime     NOT NULL                                        DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted`           bit(1)       NOT NULL                                        DEFAULT b'0' COMMENT '是否删除',
  `tenant_id`         bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=1825814820101537794 DEFAULT CHARSET=utf8mb4 COMMENT='会员用户';


#社交用户表
DROP TABLE IF EXISTS system_social_user;
CREATE TABLE `system_social_user`
(
  `id`             bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键(自增策略)',
  `type`           tinyint(4) NOT NULL COMMENT '社交平台的类型',
  `openid`         varchar(32) COLLATE utf8mb4_unicode_ci   NOT NULL COMMENT '社交 openid',
  `token`          varchar(256) COLLATE utf8mb4_unicode_ci           DEFAULT NULL COMMENT '社交 token',
  `raw_token_info` varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始 Token 数据，一般是 JSON 格式',
  `nickname`       varchar(32) COLLATE utf8mb4_unicode_ci   NOT NULL COMMENT '用户昵称',
  `avatar`         varchar(255) COLLATE utf8mb4_unicode_ci           DEFAULT NULL COMMENT '用户头像',
  `raw_user_info`  varchar(1024) COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '原始用户数据，一般是 JSON 格式',
  `code`           varchar(256) COLLATE utf8mb4_unicode_ci  NOT NULL COMMENT '最后一次的认证 code',
  `state`          varchar(256) COLLATE utf8mb4_unicode_ci           DEFAULT NULL COMMENT '最后一次的认证 state',
  `creator`        varchar(64) COLLATE utf8mb4_unicode_ci            DEFAULT '' COMMENT '创建者',
  `create_time`    datetime                                 NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater`        varchar(64) COLLATE utf8mb4_unicode_ci            DEFAULT '' COMMENT '更新者',
  `update_time`    datetime                                 NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted`        bit(1)                                   NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id`      bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='社交用户表';

社交绑定表
DROP TABLE IF EXISTS system_social_user_bind;
CREATE TABLE `system_social_user_bind`
(
  `id`             bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '主键(自增策略)',
  `user_id`        bigint(20) NOT NULL COMMENT '用户编号',
  `user_type`      tinyint(4) NOT NULL COMMENT '用户类型',
  `social_type`    tinyint(4) NOT NULL COMMENT '社交平台的类型',
  `social_user_id` bigint(20) NOT NULL COMMENT '社交用户的编号',
  `creator`        varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '创建者',
  `create_time`    datetime NOT NULL                      DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater`        varchar(64) COLLATE utf8mb4_unicode_ci DEFAULT '' COMMENT '更新者',
  `update_time`    datetime NOT NULL                      DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted`        bit(1)   NOT NULL                      DEFAULT b'0' COMMENT '是否删除',
  `tenant_id`      bigint(20) NOT NULL DEFAULT '0' COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='社交绑定表'



create table saas_migration
(
  id                 bigint auto_increment comment '主键' primary key,
  pref               varchar(64)   default ''  not null comment '机构编码',
  name               varchar(64)   default ''  not null comment '名称',
  area               varchar(64)   default ''  not null comment '地区',
  tenant_id          bigint    null comment '迁移后对应门店id',
  status             tinyint(2)  default 0  not null comment '迁移状态 0初始 1待迁移 2迁移中 3已迁移',
  start_time        datetime      default  null comment '迁移计划开始时间',
  store_end_time    datetime      default  null comment '门店迁移完成始时间',
  package_end_time   datetime      default null comment '套餐迁移完成始时间',

  patient_status       tinyint(2)      default 1   not null comment '患者状态 0未开始 1失败 2成功',
  employee_status      tinyint(2)      default 1   not null comment '员工状态 0未开始 1失败 2成功',
  pharmacist_status    tinyint(2)      default 1   not null comment '药师状态 0未开始 1失败 2成功',
  package_status       tinyint(2)      default 1   not null comment '套餐状态 0未开始 1失败 2成功',

  remark             varchar(256)                            null comment '备注',
  creator            varchar(64)   default ''                not null comment '创建者',
  create_time        datetime      default CURRENT_TIMESTAMP not null comment '创建时间',
  updater            varchar(64)   default ''                null comment '更新者',
  update_time        datetime      default CURRENT_TIMESTAMP not null on update CURRENT_TIMESTAMP comment '更新时间',
  deleted            bit           default b'0'              not null comment '是否删除'
)
  comment '荷叶老问诊迁移表';

create index idx_start_time_status
  on saas_migration (start_time, status)
  comment '时间+状态索引';