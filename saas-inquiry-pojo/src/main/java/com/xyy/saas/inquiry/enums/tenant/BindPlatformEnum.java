package com.xyy.saas.inquiry.enums.tenant;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import java.util.Arrays;


@Getter
@RequiredArgsConstructor
public enum BindPlatformEnum implements IntArrayValuable {

    UNBOUND(0, "未绑定"),

    BIND_SAAS(1, "智慧脸"),
    ;

    private final int code;

    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(BindPlatformEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

}
