package com.xyy.saas.inquiry.enums.tenant;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * @Desc 套餐收款方式 '收款方式：0线上，1线下',
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum PackagePaymentTypeEnum implements IntArrayValuable {

    ONLINE(0, "线上"),

    OFFLINE(1, "线下"),
    ;

    private final int code;

    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(PackagePaymentTypeEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static PackagePaymentTypeEnum fromStatusCode(int statusCode) {
        return Arrays.stream(values())
            .filter(value -> value.getCode() == statusCode)
            .findFirst()
            .orElse(OFFLINE);
    }

}