package com.xyy.saas.inquiry.enums.inquiry;

import lombok.Getter;

/**
 * 问诊记录查询来源
 */
@Getter
public enum QuerySourceEnum {
    APP(0, "APP端"),
    WEB(1, "WEB端"),
    ZHL(2, "ZHL端");

    private final int code;
    private final String desc;

    QuerySourceEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static QuerySourceEnum getByCode(Integer code) {

        if (code == null) {
            return null;
        }

        for (QuerySourceEnum querySourceEnum : QuerySourceEnum.values()) {
            if (querySourceEnum.code == code) {
                return querySourceEnum;
            }
        }
        return null;
    }
}
