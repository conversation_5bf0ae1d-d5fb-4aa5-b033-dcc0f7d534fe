package com.xyy.saas.inquiry.mq.inquiry;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import com.xyy.saas.inquiry.mq.inquiry.dto.InquiryEndMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: xucao
 * @Date: 2024/12/11 11:43
 * @Description: 问诊结束事件
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class InquiryEndEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "ON_INQUIRY_END";

    private InquiryEndMessage msg;

    @JsonCreator
    public InquiryEndEvent(@JsonProperty("msg") InquiryEndMessage msg) {
        this.msg = msg;
    }

    @Override
    public String getTag() {
        return "";
    }
}
