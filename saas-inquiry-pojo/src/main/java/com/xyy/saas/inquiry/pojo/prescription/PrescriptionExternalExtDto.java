package com.xyy.saas.inquiry.pojo.prescription;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;

/**
 * @Author:chenxiaoyi
 * @Date:2025/02/11 18:50
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "外配处方扩展信息ext")
public class PrescriptionExternalExtDto implements Serializable {

    private String prescriptionPdfUrl;

    private Long taskId;

    private String remark;

    @Schema(description = "电子处方审核意见")
    private String rxChkOpinions;

    @Schema(description = "医保电子签名后处方信息originalValue的签名结果值")
    private String signDigest;

    @Schema(description = "签名机构证书SN")
    private String signCertSn;

    @Schema(description = "签名机构证书DN")
    private String signCertDn;
}
