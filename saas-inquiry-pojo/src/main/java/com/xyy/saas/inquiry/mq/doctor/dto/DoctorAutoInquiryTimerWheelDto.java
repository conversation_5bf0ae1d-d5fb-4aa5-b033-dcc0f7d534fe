package com.xyy.saas.inquiry.mq.doctor.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2025/01/24 14:34
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class DoctorAutoInquiryTimerWheelDto implements java.io.Serializable {

    private String doctorPref;
    /**
     * 定时轮 key
     */
    private String wheelKey;

    /**
     * 定时轮value
     */
    private String wheelValue;

    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 是否是全天自动开方医生
     */
    private boolean autoAllInquiry;

    public DoctorAutoInquiryTimerWheelDto(String doctorPref, String wheelKey, String wheelValue, String startTime, String endTime) {
        this.doctorPref = doctorPref;
        this.wheelKey = wheelKey;
        this.wheelValue = wheelValue;
        this.startTime = startTime;
        this.endTime = endTime;
    }
}
