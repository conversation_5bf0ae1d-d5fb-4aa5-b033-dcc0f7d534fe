package com.xyy.saas.inquiry.enums.doctor;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 在线类型
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum OnlineStatusEnum implements IntArrayValuable {

    OFFLINE(0, "离线", "停诊"),

    ONLINE(1, "在线", "出诊");

    private final int code;

    private final String desc;

    private final String remark;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(OnlineStatusEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 枚举值code
     * @return 枚举值
     */
    public static OnlineStatusEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("Invalid OnlineStatusEnum code: " + code));
    }
}