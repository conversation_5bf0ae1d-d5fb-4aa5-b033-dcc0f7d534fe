package com.xyy.saas.inquiry.enums.doctor;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;

import java.util.Arrays;

public enum CooperationStatusEnum implements IntArrayValuable {

    UNCOOPERATED(0, "未合作"),  // 0未合作
    IN_COOPERATION(1, "合作中"),  // 1 合作中
    FORBIDDEN(2, "禁用合作"),  // 2禁用合作
    EXPIRED(3, "过期");  // 3过期

    private final int code;
    private final String description;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(CooperationStatusEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    CooperationStatusEnum(int code, String description) {
        this.code = code;
        this.description = description;
    }

    // 获取数值的方法
    public int getCode() {
        return code;
    }

    // 获取描述的方法
    public String getDescription() {
        return description;
    }

    // 根据数值获取枚举实例的方法（可选）
    public static CooperationStatusEnum fromCode(int code) {
        for (CooperationStatusEnum status : CooperationStatusEnum.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }


}
