package com.xyy.saas.inquiry.enums.tutorial;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import java.util.Arrays;
import lombok.Getter;

/**
 * 教程类型
 */
@Getter
public enum TutorialTypeEnum implements IntArrayValuable {

    OWN(0, "平台操作教程"),

    THIRD(1, "三方URL地址"),

    ;

    private final int code;
    private final String desc;

    TutorialTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(TutorialTypeEnum::getCode).toArray();

    public static String getDescByCode(Integer integer) {
        for (TutorialTypeEnum value : values()) {
            if (value.getCode() == integer) {
                return value.getDesc();
            }
        }
        return null;
    }

    public static TutorialTypeEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElseThrow();
    }


    @Override
    public int[] array() {
        return ARRAYS;
    }
}
