// package com.xyy.saas.inquiry.util;
//
// import com.alibaba.excel.annotation.ExcelProperty;
// import java.lang.reflect.Field;
// import java.util.List;
// import java.util.Map;
// import java.util.TreeMap;
// import org.apache.commons.lang3.math.NumberUtils;
// import org.springframework.web.multipart.MultipartFile;
//
// /**
//  * 导入Excel基础工具类
//  *
//  * @Author:chenxiaoyi
//  * @Date:2025/04/18 16:26
//  */
// public class ImportExcelUtil {
//
//
//     public static <T> List<T> checkAndGetData(MultipartFile file, Class<T> clazz, int totalCount) throws Exception {
//         return null;
//
//     }
//
//
// }
