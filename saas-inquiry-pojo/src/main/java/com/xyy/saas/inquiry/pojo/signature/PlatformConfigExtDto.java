package com.xyy.saas.inquiry.pojo.signature;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2025/02/06 14:54
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "平台扩展信息ext")
public class PlatformConfigExtDto implements java.io.Serializable {


    /**
     * 配置Id 为0 默认取第一条
     */
    private int configId;

    // 平台应用标识id
    private String appId;

    // 密钥
    private String appSecret;

    // 私有云地址
    private String privateUrl;

    // 免验证签场景码
    private String unSignBusinessId;

    // 主应用OpenCorpId
    private String mainOpenCorpId;

    // 签章授权协议模板Id
    private String signAuthAgreementTempId;

}
