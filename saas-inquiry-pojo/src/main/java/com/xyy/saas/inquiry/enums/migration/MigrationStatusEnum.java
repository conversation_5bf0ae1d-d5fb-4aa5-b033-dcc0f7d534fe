package com.xyy.saas.inquiry.enums.migration;

import lombok.Getter;
import java.util.Objects;

/**
 * 迁移状态 0待确认 1待迁移 2迁移中 3已迁移
 */
@Getter
public enum MigrationStatusEnum {
    PENDING_CONFIRMATION(0, "待确认", "未迁移"),
    PENDING_MIGRATION(1, "待迁移", "待迁移"),

    MIGRATING(2, "迁移中", "门店已迁移"),
    MIGRATED(3, "已迁移", "迁移成功"),
    PACKAGING_MIGRATION(4, "迁移中", "套餐迁移中"),
    ;

    private final int code;
    private final String desc;
    private final String oldDesc;

    MigrationStatusEnum(int code, String desc, String oldDesc) {
        this.code = code;
        this.desc = desc;
        this.oldDesc = oldDesc;
    }

    public static String getByCode(Integer code) {
        for (MigrationStatusEnum value : values()) {
            if (Objects.equals(value.getCode(), code)) {
                return value.desc;
            }
        }
        return "";
    }

}
