package com.xyy.saas.inquiry.pojo.inquiry;

import cn.hutool.core.util.ObjectUtil;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryEnum;
import com.xyy.saas.inquiry.enums.inquiry.BizChannelTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.UnableAutoReasonEnum;
import com.xyy.saas.inquiry.enums.inquiry.StreamStatus;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: xucao
 * @Date: 2024/12/09 19:45
 * @Description: 问诊单Dto
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class InquiryDto implements Serializable {

    /**
     * 主键ID
     */
    private Long id;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;
    /**
     * 问诊单号
     */
    private String pref;
    /**
     * 患者编码
     */
    private String patientPref;
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 患者年龄
     */
    private String patientAge;
    /**
     * 患者性别 1、男   2、女
     */
    private Integer patientSex;
    /**
     * 患者手机号
     */
    private String patientMobile;
    /**
     * 互联网医院编码
     */
    private String hospitalPref;
    /**
     * 互联网医院名称
     */
    private String hospitalName;
    /**
     * 科室编码
     */
    private String deptPref;
    /**
     * 科室编码
     */
    private String deptName;
    /**
     * 处方笺模版id
     */
    private Long preTempId;
    /**
     * 医生GUID
     */
    private String doctorPref;


    /**
     * 医生姓名
     */
    private String doctorName;

    /**
     * 接诊状态：0 排队中 1、患者取消问诊 2 问诊中 3问诊结束 4医生取消开方 5问诊超时取消 {@link InquiryStatusEnum }
     */
    private Integer inquiryStatus;
    /**
     * 取消开方原因
     */
    private String cancelReason;
    /**
     * 问诊方式  1、图文问诊  2、视频问诊  3、电话问诊 {@link InquiryWayTypeEnum }
     */
    private Integer inquiryWayType;
    /**
     * 问诊业务类型 1、药店问诊  2、远程审方 {@link InquiryBizTypeEnum }
     */
    private Integer inquiryBizType;
    /**
     * 客户端渠类型 0、app  1、pc  2、小程序 {@link ClientChannelTypeEnum}
     */
    private Integer clientChannelType;
    /**
     * 客户端系统类型
     */
    private String clientOsType;
    /**
     * 问诊渠道 0、荷叶 1、智慧脸  2、海典ERP {@link BizChannelTypeEnum}
     */
    private Integer bizChannelType;
    /**
     * 用药类型：0西药  、1中药 {@link MedicineTypeEnum}
     */
    private Integer medicineType;
    /**
     * 是否自动开方：0 否  、 1是 {@link AutoInquiryEnum}
     */
    private Integer autoInquiry;
    /**
     * 不走自动开方的原因  1、预购药品无用法用量 2、无自动开方医生  3、门店未开通自动开方 {@link UnableAutoReasonEnum} unableAutoReason
     */
    private Integer unableAutoReason;
    /**
     * IM平台类型  0、腾讯IM
     */
    private Integer imPlatform;
    /**
     * 订单编号
     */
    private String orderNo;
    /**
     * 问诊扩展字段
     */
    private String ext;
    /**
     * 医生接诊时间
     */
    private LocalDateTime startTime;
    /**
     * 问诊结束时间
     */
    private LocalDateTime endTime;
    /**
     * 医生录屏编码
     */
    private String doctorVideoPref;
    /**
     * MP3状态 0 未视频 1 视频中 2 已合流 3 已编码 4 已完成 {@link StreamStatus}
     */
    private Integer streamStatus;
    /**
     * 视频地址
     */
    private String mp4Url;
    /**
     * im问诊记录
     */
    private String imPdf;

    /**
     * 聊天记录内容
     */
    private String imHistory;

    /**
     * 视频混流id
     */
    private String streamId;
    /**
     * 任务ID
     */
    private String transcodingId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 是否自动开方
     *
     * @return
     */
    public Boolean isAutoInquiry() {
        return ObjectUtil.equals(this.autoInquiry, AutoInquiryEnum.YES.getCode());
    }

    /**
     * 是否图文问诊
     *
     * @return boolean
     */
    public Boolean isTextInquiry() {
        return Objects.equals(this.inquiryWayType, InquiryWayTypeEnum.TEXT.getCode());
    }

    /**
     * 是否图文问诊
     *
     * @return boolean
     */
    public Boolean isVideoInquiry() {
        return Objects.equals(this.inquiryWayType, InquiryWayTypeEnum.VIDEO.getCode());
    }

    /**
     * 是否图文自动开方
     *
     * @return boolean
     */
    public Boolean isTextAutoInquiry() {
        return Objects.equals(this.inquiryWayType, InquiryWayTypeEnum.TEXT.getCode()) && isAutoInquiry();
    }

    /**
     * 是否视频自动开方
     *
     * @return boolean
     */
    public Boolean isVideoAutoInquiry() {
        return Objects.equals(this.inquiryWayType, InquiryWayTypeEnum.VIDEO.getCode()) && isAutoInquiry();
    }
}
