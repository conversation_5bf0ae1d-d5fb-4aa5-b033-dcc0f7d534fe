package com.xyy.saas.inquiry.pojo.transmitter.his;

import com.xyy.saas.inquiry.pojo.transmitter.TransmissionReqDataBaseDTO;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2025/02/21 15:17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class RegistrationTransmitterDTO extends TransmissionReqDataBaseDTO {

    /**
     * 就诊编号
     */
    private String registrationPref;

    /**
     * 处方编号
     */
    private String prescriptionPref;

    /**
     * 问诊编号
     */
    private String inquiryPref;

    /**
     * 患者编号
     */
    private String patientPref;

    /**
     * 患者性别 1、男   2、女
     */
    private Integer patientSex;

    /**
     * 患者年龄
     */
    private String patientAge;
    /**
     * 问诊医院编码
     */
    private String hospitalPref;

    /**
     * 患者身份证号
     */
    private String idCardNo;

    /**
     * 医保就诊id
     */
    private String medicalVisitId;

    /**
     * 参保地编号
     */
    private String insuredAreaNo;

    /**
     * 参保人编号
     */
    private String psnNo;

    /**
     * 单据状态
     */
    private Integer status;
}
