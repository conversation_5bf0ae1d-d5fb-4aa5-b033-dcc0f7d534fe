package com.xyy.saas.inquiry.enums.user;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import lombok.Getter;

import java.util.Arrays;
import java.util.Objects;

/**
 * 打卡类型
 */
@Getter
public enum ClockInTypeEnum implements IntArrayValuable {
    CLOCK_IN(0, "签到"),

    CLOCK_OUT(1, "签退"),

    ;

    private final int code;
    private final String desc;

    ClockInTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(ClockInTypeEnum::getCode).toArray();

    public static String getDescByCode(Integer integer) {
        for (ClockInTypeEnum value : values()) {
            if (value.getCode() == integer) {
                return value.getDesc();
            }
        }
        return null;
    }

    public static ClockInTypeEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElseThrow();
    }

    public static int convertClockInType2Status(ClockInTypeEnum clockInTypeEnum) {
        if (Objects.equals(clockInTypeEnum, ClockInTypeEnum.CLOCK_IN)) {
            return CommonStatusEnum.ENABLE.getStatus();
        }
        return CommonStatusEnum.DISABLE.getStatus();
    }


    @Override
    public int[] array() {
        return ARRAYS;
    }
}
