package com.xyy.saas.inquiry.pojo.forward;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * 旧系统CA认证信息
 */
@Data
public class ForwardCaInfo implements Serializable {

    /**
     * 姓名
     */
    private String name;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 手机号
     */
    private String docTel;

    /**
     * 降级标识
     */
    private boolean downFlag;

    /**
     * 降级签名URL
     */
    private String downSignUrl;

    /**
     * 签名URL
     */
    private String signUrl;

    private String sealUrl;

    /**
     * 法大大兼职合同状态 1: 完成，0: 未完成, 2: 待审核
     */
    private Integer fddPartTimeAgreementStatus;

    /**
     * 法大大实名认证状态 1: 认证完成，0: 待认证，2: 认证失败
     */
    private Integer fddCertifyStatus;

    /**
     * 法大大手写签名状态 1: 已签名，0: 未签名
     */
    private Integer fddSignatureStatus;

    /**
     * 法大大授权协议签署状态 1: 已签署，0: 未签署
     */
    private Integer fddAgreementStatus;

    /**
     * 法大大免验证签署授权状态 1: 已授权，0: 未授权
     */
    private Integer fddAuthorizeFreeSignStatus;

    /**
     * 法大大免验证签署授权截止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date fddAuthorizeFreeSignDdl;

}