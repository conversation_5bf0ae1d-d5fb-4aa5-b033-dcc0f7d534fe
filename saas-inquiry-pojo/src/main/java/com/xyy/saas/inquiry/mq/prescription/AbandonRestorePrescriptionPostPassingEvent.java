package com.xyy.saas.inquiry.mq.prescription;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import com.xyy.saas.inquiry.mq.prescription.dto.PrescriptionMqCommonMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 处方恢复后置处理事件
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class AbandonRestorePrescriptionPostPassingEvent extends EventBusAbstractMessage {
    public static final String TOPIC = "ABANDON_RESTORE_PRESCRIPTION_POST_PASSING";

    private PrescriptionMqCommonMessage msg;

    @JsonCreator
    public AbandonRestorePrescriptionPostPassingEvent(@JsonProperty("msg") PrescriptionMqCommonMessage msg) {
        this.msg = msg;
    }

    @Override
    public String getTag() {
        return "";
    }
}
