package com.xyy.saas.inquiry.pojo.prescription;

import com.xyy.saas.inquiry.enums.prescription.template.TemplateFieldTypeEnum;
import com.xyy.saas.inquiry.enums.signature.SignatureSealValueTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.*;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 参与方item
 *
 * @Author:chenxiaoyi
 * @Date:2024/09/04 14:06
 */
@Data
@Schema(description = "处方模板字段")
@Valid
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class PrescriptionTemplateField implements Serializable {

    @Schema(description = "字段名称")
    private String fieldName;

    @Schema(description = "字段")
    private String field;

    /**
     * 发起方联系方式 {@link TemplateFieldTypeEnum}
     */
    @Schema(description = "字段类型")
    private Integer fieldType;

    /**
     * {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "是否必须")
    private Integer required;

    /**
     * 仅签章图片需要 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "是否接入三方平台")
    private Integer accessPlatform;

    /**
     * 签章seal取值类型 {@link SignatureSealValueTypeEnum}
     */
    @Schema(description = "签章seal取值")
    private Integer signSealValueType;

    /**
     * 仅签章图片需要
     */
    @Schema(description = "排序序号")
    private Integer sorted;


    @Schema(description = "默认值")
    private String defaultValue;

}
