package com.xyy.saas.inquiry.pojo.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "租户扩展信息ext")
@Accessors(chain = true)
public class TenantExtDto implements Serializable {

    @Schema(description = "门店总部名称")
    private String headTenantName;

    @Schema(description = "老系统机构号")
    private String organSign;

    @Schema(description = "老系统hyId")
    private String hyId;


}
