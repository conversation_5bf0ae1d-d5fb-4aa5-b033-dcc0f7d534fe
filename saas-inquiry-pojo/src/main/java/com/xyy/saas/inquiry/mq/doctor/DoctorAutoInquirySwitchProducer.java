package com.xyy.saas.inquiry.mq.doctor;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import org.springframework.stereotype.Component;

/**
 * 医师自动开方出停诊事件Producer - 改由定时任务处理
 * <p>
 * 医生新增、修改、审核通过、合作状态变更，都需要发送此mq
 *
 * @Author:chenxia<PERSON>i
 * @Date:2024/12/18 11:25
 */
@Component
@EventBusProducer(
    topic = DoctorAutoInquiryTimerWheelEvent.TOPIC
)
public class DoctorAutoInquirySwitchProducer extends EventBusRocketMQTemplate {

}
