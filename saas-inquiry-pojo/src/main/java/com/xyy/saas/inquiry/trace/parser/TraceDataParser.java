package com.xyy.saas.inquiry.trace.parser;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.springframework.core.DefaultParameterNameDiscoverer;
import java.lang.reflect.Method;

/**
 * 链路追踪数据解析器接口 用于从方法参数中解析出业务单据号和其他业务数据
 */
public abstract class TraceDataParser {

    DefaultParameterNameDiscoverer discoverer = new DefaultParameterNameDiscoverer();

    /**
     * 解析业务单据号
     *
     * @param method 被执行的方法
     * @param args   方法参数
     * @param result 方法返回值
     * @return 业务单据号
     */
    public abstract String parseBusinessNo(Method method, Object[] args, Object result, String prefLocation);

    /**
     * 解析业务数据
     *
     * @param method 被执行的方法
     * @param args   方法参数
     * @return 业务数据，可以是任意对象，会被转换为JSON字符串存储
     */
    public abstract Object parseBusinessData(Method method, Object[] args, Object result);

    /**
     * 默认解析业务单据号
     *
     * @param method       方法名
     * @param args         参数
     * @param result       出参
     * @param prefLocation 业务单号参数位置
     * @return 业务单号
     */
    public String defaultParseBusinessNo(Method method, Object[] args, Object result, String prefLocation) {
        String[] paramNames = discoverer.getParameterNames(method);
        if (args == null || args.length == 0 || StringUtils.isBlank(prefLocation)) {
            return "";
        }
        String[] params = prefLocation.split("\\.");
        // 查找第一个字符串类型的参数
        for (int i = 0; i < args.length; i++) {
            if (paramNames != null &&  StringUtils.equals(paramNames[i], params[0])) {
                if (params.length == 1 && args[i] instanceof String) {
                    return (String) args[i];
                }
                if (params.length == 2) {
                    JSONObject jsonObject = (JSONObject)JSON.toJSON(args[i]);
                    return jsonObject.getString(params[1]);
                }
                if (params.length == 3) {
                    JSONObject jsonObject = (JSONObject)JSON.toJSON(args[i]);
                    JSONObject json1 = jsonObject.getJSONObject(params[1]);
                    if(ObjectUtil.isEmpty(json1)){
                        return "";
                    }
                    return json1.getString(params[2]);
                }
            }
        }

        return null;
    }

    /**
     * 默认解析业务数据
     *
     * @param method 被执行的方法
     * @param args   方法参数
     * @return 业务数据，可以是任意对象，会被转换为JSON字符串存储
     **/
    public Object defaultParseBusinessData(Method method, Object[] args, Object result) {
        String[] paramNames = discoverer.getParameterNames(method);
        // 2. 构建带真实键名的请求JSON
        JSONObject requestJson = new JSONObject();
        if (args != null && paramNames != null) {
            for (int i = 0; i < args.length; i++) {
                // 键名=参数名
                requestJson.put(paramNames[i], args[i]);
            }
        }
        // 3. 合并结果
        JSONObject data = new JSONObject();
        data.put("request", requestJson);
        data.put("result", JSON.toJSON(result));
        return data;
    }
}