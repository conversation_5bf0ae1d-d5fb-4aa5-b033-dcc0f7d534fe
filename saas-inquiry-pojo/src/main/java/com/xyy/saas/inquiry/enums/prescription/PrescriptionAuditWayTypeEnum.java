package com.xyy.saas.inquiry.enums.prescription;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * 处方审方方式类别
 */
@Getter
@RequiredArgsConstructor
public enum PrescriptionAuditWayTypeEnum implements IntArrayValuable {

    //药店问诊
    HY(1, "荷叶审方"),
    //远程审方
    REMOTE(2, "远程审方"),

    ;

    // 枚举类的私有成员变量
    private final int code;
    private final String description;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(PrescriptionAuditWayTypeEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    // 根据整数值获取枚举实例的静态方法
    public static PrescriptionAuditWayTypeEnum fromCode(Integer code) {
        return Arrays.stream(values())
            .filter(type -> Objects.equals(type.getCode(), code))
            .findFirst()
            .orElse(HY);
    }
}

