package com.xyy.saas.inquiry.mq.prescription;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import com.xyy.saas.inquiry.mq.prescription.dto.PrescriptionSupervisionEndMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: cxy
 * @Description: 处方对接监管完成 事件
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class PrescriptionSupervisionEndEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "PRESCRIPTION_SUPERVISION_END";

    private PrescriptionSupervisionEndMessage msg;

    @JsonCreator
    public PrescriptionSupervisionEndEvent(@JsonProperty("msg") PrescriptionSupervisionEndMessage msg) {
        this.msg = msg;
    }

    @Override
    public String getTag() {
        return "";
    }
}
