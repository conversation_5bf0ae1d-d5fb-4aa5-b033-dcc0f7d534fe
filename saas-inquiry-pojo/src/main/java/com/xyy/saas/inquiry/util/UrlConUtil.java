package com.xyy.saas.inquiry.util;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLConnection;
import java.time.Clock;
import java.util.Base64;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import cn.hutool.core.io.IoUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

@Slf4j
public class UrlConUtil {


    /**
     * 下载远端资源文件
     *
     * @param url 远端资源url
     * @return 字节数组
     */
    public static String downLoadFile2Base64(String url) {
        if (StringUtils.isBlank(url)) {
            return null;
        }
        try (InputStream inputStream = UrlConUtil.getStreamRetry("GET", url, 5000)) {

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            byte[] buffer = new byte[4096];
            int bytesRead;

            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            // 使用Java 8+的Base64编码器
            return Base64.getEncoder().encodeToString(outputStream.toByteArray());

        } catch (IOException e) {
            log.error(e.getMessage(), e);
        }
        return null;
    }


    /**
     * 文件下载 带重试
     *
     * @param method  请求方式
     * @param urlPath 下载路径
     * @param timeout 超时时间，默认5秒
     * @return
     */
    public static InputStream getStreamRetry(String method, String urlPath, Integer timeout) throws IOException {
        for (int i = 0; i < 2; i++) {
            InputStream result = getStreamLimit(method, urlPath, timeout, null);
            if (Objects.isNull(result) || result.available() < 256) {
                try {
                    TimeUnit.SECONDS.sleep(1);
                } catch (Exception ignore) {
                }
                IoUtil.close(result);
                continue;
            }
            return result;
        }
        return null;
    }

    /**
     * 文件下载
     *
     * @param method  请求方式
     * @param urlPath 下载路径
     * @param timeout 超时时间，默认5秒
     * @return
     */
    public static InputStream getStreamLimit(String method, String urlPath, Integer timeout, Integer limitByte) {
        log.info("in.文件下载：UrlConUtil.getStream.method：{}；url:{};timeout:{}", method, urlPath, timeout);
        Long start = Clock.systemDefaultZone().millis();
        try {
            URL url = new URL(urlPath);// 统一资源
            URLConnection urlConnection = url.openConnection();// 连接类的父类，抽象类
            HttpURLConnection httpURLConnection = (HttpURLConnection) urlConnection;// http的连接类
            httpURLConnection.setConnectTimeout((Objects.isNull(timeout) || timeout == 0) ? 5000 : timeout);  //设置超时
            httpURLConnection.setReadTimeout((Objects.isNull(timeout) || timeout == 0) ? 5000 : timeout);
            httpURLConnection.setRequestMethod(StringUtils.isNotBlank(method) ? method : "GET"); //设置请求方式，默认是GET
            httpURLConnection.setRequestProperty("Charset", "UTF-8"); // 设置字符编码
            httpURLConnection.connect();// 打开到此 URL引用的资源的通信链接（如果尚未建立这样的连接）。
            int fileLength = httpURLConnection.getContentLength(); // 文件大小
            String fileSize = (fileLength / 1024 < 1000) ? fileLength / 1024 + "KB" : fileLength / (1024 * 1024) + "MB";
            log.info("out.文件下载：UrlConUtil.getStream.文件大小：{}", fileSize);
            if (Objects.nonNull(limitByte) && limitByte < fileLength) {
                return null;
            }
            return httpURLConnection.getInputStream();
        } catch (Exception e) {
            log.error("e.文件下载：UrlConUtil.getStream.method：{}；url:{};timeout:{};e", method, urlPath, timeout, e);
        } finally {
            Long times = Clock.systemDefaultZone().millis() - start;
            log.info("out.文件下载：UrlConUtil.getStream.end.下载时间:{}", times);
            if (times > 4990) {
                log.info("out.文件下载：UrlConUtil.getStream.end.下载时间过长:{}", times);
            } else if (times > 2990) {
                log.info("out.文件下载：UrlConUtil.getStream.end.下载时间过长:{}", times);
            }
        }
        return null;
    }

    public static void main(String[] args) {
        InputStream inputStream = getStreamLimit("GET", "https://files.ybm100.com/ebd6537c305e4d759d900b327141dd30.jpg", 5000, 1024 * 1024);
        try {
            log.info("{}", Objects.nonNull(inputStream) ? inputStream.available() : 0);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

}
