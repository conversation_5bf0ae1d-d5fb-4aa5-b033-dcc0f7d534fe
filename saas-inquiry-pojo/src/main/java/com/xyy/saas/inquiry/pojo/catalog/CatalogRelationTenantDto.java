package com.xyy.saas.inquiry.pojo.catalog;

import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.servicepack.ServicePackRelationExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "版本关联租户")
@Accessors(chain = true)
public class CatalogRelationTenantDto extends TenantDto {

    /**
     * 目录id
     */
    private Long catalogId;

    /**
     * 目录名称
     */
    private String catalogName;

    /**
     * 目录版本code
     */
    private String catalogVersionCode;

    /**
     * 门店-开通服务包关系ext
     */
    private ServicePackRelationExtDto tenantTransmissionServicePackRelationExt;

}
