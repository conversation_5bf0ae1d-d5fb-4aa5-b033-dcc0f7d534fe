package com.xyy.saas.inquiry.enums.prescription;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import java.util.Arrays;
import java.util.List;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 处方审核状态
 */
@Getter
@RequiredArgsConstructor
public enum PrescriptionAuditStatusEnum implements IntArrayValuable {
    PENDING(0, "待审核"),

    AUDITING(1, "审核中"),

    APPROVED(2, "审核通过"),

    REJECTED(3, "审核驳回"),

    TIMEOUT(4, "审核超时"),

    UNKNOWN(-1, "未知");

    // 枚举类的私有成员变量
    private final int code;
    private final String description;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(PrescriptionAuditStatusEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    // 根据整数值获取枚举实例的静态方法
    public static PrescriptionAuditStatusEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElse(null);
    }

    /**
     * 状态是否在处理中
     *
     * @param statusCode
     * @return
     */
    public static boolean isHanded(Integer statusCode) {
        return List.of(PENDING.getCode(), AUDITING.getCode()).contains(statusCode);
    }

}
