package com.xyy.saas.inquiry.pojo.transmitter.drug;

import com.xyy.saas.inquiry.enums.doctor.PharmacistTypeEnum;
import com.xyy.saas.inquiry.pojo.transmitter.TransmissionReqDataBaseDTO;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2025/02/21 15:17
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class SignInTransmitterDTO extends TransmissionReqDataBaseDTO {

    private String name;

    private String sex;

    private String idCard;

    private String mobile;

    /**
     * 执业省份
     */
    private String provinceCode;

    /**
     * 药师编码
     */
    private String pharmacistPref;

    /**
     * 药师类型 平台药师 / 门店药师 / 医院药师 {@link PharmacistTypeEnum}
     */
    private Integer pharmacistType;

    /**
     * 注册证编号
     */
    private String registrationNo;

    /**
     * 医生编码
     */
    private String doctorPref;

}
