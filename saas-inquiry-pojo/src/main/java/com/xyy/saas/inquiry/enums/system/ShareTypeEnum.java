package com.xyy.saas.inquiry.enums.system;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import java.util.Arrays;

/**
 * @Author: lgd
 * @Date: 2025/06/16 16:43
 * @Description: 共享类型枚举
 */
@Getter
@RequiredArgsConstructor
public enum ShareTypeEnum implements IntArrayValuable {

    SHARE(0, "共享菜单"),

    PACKAGE(1, "不共享菜单");

    private final int code;

    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(ShareTypeEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 枚举值code
     * @return 枚举值
     */
    public static ShareTypeEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElse(SHARE);
    }
}
