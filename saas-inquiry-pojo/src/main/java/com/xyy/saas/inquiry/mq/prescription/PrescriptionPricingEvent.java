package com.xyy.saas.inquiry.mq.prescription;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import com.xyy.saas.inquiry.mq.prescription.dto.PrescriptionPricingMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: cxy
 * @Description: 处方划价 事件
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class PrescriptionPricingEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "PRESCRIPTION_PRICING";

    private PrescriptionPricingMessage msg;

    @JsonCreator
    public PrescriptionPricingEvent(@JsonProperty("msg") PrescriptionPricingMessage msg) {
        this.msg = msg;
    }

    @Override
    public String getTag() {
        return "";
    }
}
