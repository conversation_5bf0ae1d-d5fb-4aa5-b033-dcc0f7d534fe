package com.xyy.saas.inquiry.enums.system;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * @Author: xucao
 * @Date: 2025/01/20 17:58
 * @Description: app版本升级范围枚举 升级范围：0-全量升级  1-比例用户灰度   2-指定用户灰度
 */
@Getter
@RequiredArgsConstructor
public enum AppVersionUpgradeScopeEnum {
    ALL(0,"全量升级"),
    RATIO(1,"比例用户灰度"),
    SPECIFIC(2,"指定用户灰度");

    private final Integer code;
    private final String desc;

    public static AppVersionUpgradeScopeEnum getByCode(Integer code) {
        for (AppVersionUpgradeScopeEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
