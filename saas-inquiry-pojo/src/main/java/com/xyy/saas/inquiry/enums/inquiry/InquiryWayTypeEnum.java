package com.xyy.saas.inquiry.enums.inquiry;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * @Desc 问诊形式
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/8/29 下午7:46
 */
@Getter
@RequiredArgsConstructor
public enum InquiryWayTypeEnum implements IntArrayValuable {

    UNKNOWN(-1, "", "", "", ""),

    TEXT(1, "图文问诊", "text", "图文接诊", "图文"),

    VIDEO(2, "视频问诊", "video", "视频接诊", "视频"),

    TELEPHONE(3, "电话问诊", "phone", "电话接诊", "电话");

    private final Integer code;

    private final String desc;

    private final String prefix;

    private final String descForDoctor;

    private final String descForPackage;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(InquiryWayTypeEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 枚举值code
     * @return 枚举值
     */
    public static InquiryWayTypeEnum fromCode(Integer code) {
        return Arrays.stream(values())
            .filter(type -> Objects.equals(type.getCode(), code))
            .findFirst()
            .orElse(UNKNOWN);
    }

    public static String getPrefixByCode(int code) {
        return fromCode(code).getPrefix();
    }

    public static String getDescForPackage(int code) {
        return fromCode(code).getDescForPackage();
    }
}
