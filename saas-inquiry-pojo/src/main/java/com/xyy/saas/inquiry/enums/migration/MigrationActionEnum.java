package com.xyy.saas.inquiry.enums.migration;

import java.util.Objects;

/**
 * 迁移方式
 */
public enum MigrationActionEnum {
    IMMEDIATE_MIGRATION(1, "立即迁移"),
    GENERATE_MIGRATION_PLAN(2, "生成迁移计划");

    private final int code;
    private final String desc;

    MigrationActionEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    /**
     * 是否立即迁移
     *
     * @param code
     * @return
     */
    public static boolean isImme(Integer code) {
        return Objects.equals(code, IMMEDIATE_MIGRATION.getCode());
    }

}
