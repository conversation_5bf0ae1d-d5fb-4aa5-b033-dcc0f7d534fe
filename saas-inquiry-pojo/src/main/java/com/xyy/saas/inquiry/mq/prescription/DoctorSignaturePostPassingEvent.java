package com.xyy.saas.inquiry.mq.prescription;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import com.xyy.saas.inquiry.mq.prescription.dto.PrescriptionMqCommonMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;


/**
 * 医生签章完成后置处理事件
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class DoctorSignaturePostPassingEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "DOCTOR_SIGNATURE_POST_PASSING";

    private PrescriptionMqCommonMessage msg;

    @JsonCreator
    public DoctorSignaturePostPassingEvent(@JsonProperty("msg") PrescriptionMqCommonMessage msg) {
        this.msg = msg;
    }

    @Override
    public String getTag() {
        return "";
    }

}
