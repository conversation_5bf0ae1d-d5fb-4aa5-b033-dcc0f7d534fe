package com.xyy.saas.inquiry.mq.tenant;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author:chenxiaoyi
 * @Date:2024/09/05 13:56
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class TenantPackageCostMessageDto implements Serializable {

    /**
     * 门店id
     */
    @NotNull(message = "门店id不能为空")
    private Long tenantId;

    /**
     * {@link BizTypeEnum}
     */
    @Schema(description = "系统业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer bizType;

    /**
     * 门店套餐关联id
     */
    @NotNull(message = "门店套餐关联id不能为空")
    private Long tenantPackageId;

    /**
     * 问诊医院id
     */
    private List<String> hospitalPrefs;


    @Schema(description = "问诊业务类型", example = "1")
    private Integer inquiryBizType;

    @Schema(description = "问诊审方类型", example = "1")
    private Integer inquiryAuditType;

    /**
     * 旧套餐包门店套餐关联id
     */
    private Long oldTenantPackageId;

    /**
     * 问诊套餐items
     */
    @NotNull(message = "问诊套餐items不能为空")
    private List<InquiryPackageItem> inquiryPackageItems;

    /**
     * 套餐开始时间
     */
    @NotNull(message = "问诊套餐开始时间不能为空")
    private LocalDateTime startTime;

    /**
     * 套餐结束时间
     */
    @NotNull(message = "问诊套餐结束时间不能为空")
    private LocalDateTime endTime;


    /**
     * 套餐订单状态
     */
    @NotNull(message = "问诊套餐订单状态不能为空")
    private Integer status;

    /**
     * 是否状态变更
     */
    private boolean updateStatus;

    /**
     * 创建人
     */
    private String creator;

}
