package com.xyy.saas.inquiry.enums.tenant;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * @Desc 套餐包性质
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum PackageNatureEnum implements IntArrayValuable {

    GIFT(0, "赠送"),

    BUY(1, "购买"),

    TRIAL(2, "体验"),
    ;

    private final int code;

    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(PackageNatureEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static PackageNatureEnum fromStatusCode(int statusCode) {
        return Arrays.stream(values())
            .filter(value -> value.getCode() == statusCode)
            .findFirst()
            .orElse(BUY);
    }

    public static Integer convertPackageType(String packageType) {
        if (StringUtils.equals(packageType, "0")) {
            return PackageNatureEnum.GIFT.getCode();
        }
        if (StringUtils.equals(packageType, "2")) {
            return PackageNatureEnum.TRIAL.getCode();
        }
        return PackageNatureEnum.BUY.getCode();
    }

}