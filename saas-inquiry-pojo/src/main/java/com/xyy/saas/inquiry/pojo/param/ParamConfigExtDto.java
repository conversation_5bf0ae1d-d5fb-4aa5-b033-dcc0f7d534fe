package com.xyy.saas.inquiry.pojo.param;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2025/05/14 11:20
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "参数配置扩展信息ext")
public class ParamConfigExtDto implements Serializable {

    /**
     * 门店药师超时未审核处方时间，单位s
     */
    private Integer prescriptionRemoteToHeadTime;

}
