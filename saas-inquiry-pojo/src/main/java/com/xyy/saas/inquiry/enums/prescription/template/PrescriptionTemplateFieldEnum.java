package com.xyy.saas.inquiry.enums.prescription.template;

import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import lombok.Getter;
import org.apache.commons.lang3.StringUtils;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * @Desc 处方模板字段枚举 允许新增
 * <AUTHOR>
 */
@Getter
public enum PrescriptionTemplateFieldEnum {

    /**
     * 对应处方签章参数类 {@link PrescriptionParamDto}
     */
    NO("no", "处方号", TemplateFieldTypeEnum.TXT.code, ""),
    DATE("date", "处方日期", TemplateFieldTypeEnum.TXT.code, ""),
    NAME("name", "患者名称", TemplateFieldTypeEnum.TXT.code, ""),
    SEX("sex", "患者性别", TemplateFieldTypeEnum.TXT.code, ""),
    AGE("age", "患者年龄", TemplateFieldTypeEnum.TXT.code, ""),
    idCard("idCard", "患者身份证号", TemplateFieldTypeEnum.TXT.code, ""),
    mobile("mobile", "患者手机号", TemplateFieldTypeEnum.TXT.code, ""),
    dept("dept", "科室", TemplateFieldTypeEnum.TXT.code, ""),
    allergic("allergic", "过敏史", TemplateFieldTypeEnum.TXT.code, ""),
    diagnosis("diagnosis", "诊断", TemplateFieldTypeEnum.MULTI_TXT.code, ""),
    instruction("instruction", "说明", TemplateFieldTypeEnum.MULTI_TXT.code, ""),
    remarks("remarks", "备注", TemplateFieldTypeEnum.MULTI_TXT.code, ""),
    warning("warning", "警告", TemplateFieldTypeEnum.MULTI_TXT.code, ""),
    drugs("drugs", "药品信息", TemplateFieldTypeEnum.MULTI_TXT.code, ""),
    doctorMedicareNo("doctorMedicareNo", "医生医保编码", TemplateFieldTypeEnum.TXT.code, ""),
    iptOtpNo("iptOtpNo", "门诊号", TemplateFieldTypeEnum.TXT.code, ""),
    price("price", "处方金额", TemplateFieldTypeEnum.TXT.code, ""),

    /**
     * 图片信息
     */
    waterMarkUrl("waterMarkUrl", "水印图片", TemplateFieldTypeEnum.PICTURE.code, ""),
    doctorElectronicPicture("doctorElectronicPicture", "医生电子签章", TemplateFieldTypeEnum.PICTURE.code, ""),
    // pharmacistElectronicPicture("pharmacistElectronicPicture", "药师电子签名图片", TemplateFieldTypeEnum.PICTURE.code, ""),

    /**
     * 签章信息,需要对应签章策略 {@link AbstractInquirySignaturePassingStrategy}
     */
    DOCTOR("doctorSign", "医师", TemplateFieldTypeEnum.SIGN_PICTURE.code, ""),
    PHARMACIST("pharmacistSign", "审核药师", TemplateFieldTypeEnum.SIGN_PICTURE.code, ""),
    HOS_PHARMACIST("hosPharmacistSign", "医院药师", TemplateFieldTypeEnum.SIGN_PICTURE.code, ""),
    CHECK("checkSign", "核对", TemplateFieldTypeEnum.SIGN_PICTURE.code, ""),
    ALLOCATION("allocationSign", "调配", TemplateFieldTypeEnum.SIGN_PICTURE.code, ""),
    DISPENSING("dispensingSign", "发药", TemplateFieldTypeEnum.SIGN_PICTURE.code, ""),
    ;
    /**
     * 对接签章平台 - 设置文档id
     */
    private final String field;

    /**
     * 对接签章平台 - 按照关键字来追加用户签名,比如 审核药师,就是将签章追加在 处方笺上找到 "审核药师" 字样，然后偏移量追加签名图片
     */
    private final String fieldName;

    private final Integer fieldType;

    private final String desc;

    PrescriptionTemplateFieldEnum(String field, String fieldName, Integer fieldType, String desc) {
        this.field = field;
        this.fieldName = fieldName;
        this.fieldType = fieldType;
        this.desc = desc;
    }

    public static List<String> getFieldsByType(TemplateFieldTypeEnum templateFieldTypeEnum) {
        return Arrays.stream(values()).filter(f -> Objects.equals(f.getFieldType(), templateFieldTypeEnum.code)).map(PrescriptionTemplateFieldEnum::getField).toList();
    }

    /**
     * 根据节点判断是否审核结束
     * <p>
     * 如果节点为空 || 节点属于 核对调配发药
     *
     * @param field 审核节点
     * @return true 末级审核节点
     */
    public static boolean isAuditEnd(String field) {
        return StringUtils.isBlank(field) || Stream.of(CHECK, ALLOCATION, DISPENSING).map(PrescriptionTemplateFieldEnum::getField).toList().contains(field);
    }


    public static PrescriptionTemplateFieldEnum forCode(String code) {
        if (StringUtils.isBlank(code)) {
            return null;
        }
        for (PrescriptionTemplateFieldEnum fieldEnum : values()) {
            if (StringUtils.equals(fieldEnum.getField(), code)) {
                return fieldEnum;
            }
        }
        return null;
    }


    public static String convertElectronicCode(String code) {
        if (StringUtils.isBlank(code)) {
            return "";
        }
        if (StringUtils.equals(DOCTOR.field, code)) {
            return doctorElectronicPicture.field;
        }
        // if (StringUtils.equals(PHARMACIST.field, code)) {
        //     return pharmacistElectronicPicture.field;
        // }
        return "";
    }

    /**
     * 转换审核人角色 到 签章模板字段
     *
     * @param auditorTypeEnum 审核人类型
     * @return 签章模板字段
     */
    public static PrescriptionTemplateFieldEnum convertAuditorType(AuditorTypeEnum auditorTypeEnum) {
        if (auditorTypeEnum == null) {
            return null;
        }
        return switch (auditorTypeEnum) {
            case DOCTOR -> DOCTOR;
            case DRUGSTORE_PHARMACIST, PLATFORM_PHARMACIST -> PHARMACIST;
            case HOSPITAL_PHARMACIST -> HOS_PHARMACIST;
            case CHECK -> CHECK;
            case ALLOCATION -> ALLOCATION;
            case DISPENSING -> DISPENSING;
        };
    }

    /**
     * 转换审核人角色 到 签章模板字段
     *
     * @param field 字段类型
     * @return 角色
     */
    public static RoleCodeEnum convertRole(String field) {
        if (StringUtils.equals(field, PrescriptionTemplateFieldEnum.PHARMACIST.getField())) {
            return RoleCodeEnum.PHARMACIST;
        }
        if (StringUtils.equals(field, PrescriptionTemplateFieldEnum.CHECK.getField())) {
            return RoleCodeEnum.CHECK;
        }
        if (StringUtils.equals(field, PrescriptionTemplateFieldEnum.ALLOCATION.getField())) {
            return RoleCodeEnum.ALLOCATION;
        }
        if (StringUtils.equals(field, PrescriptionTemplateFieldEnum.DISPENSING.getField())) {
            return RoleCodeEnum.DISPENSING;
        }
        return null;
    }

}
