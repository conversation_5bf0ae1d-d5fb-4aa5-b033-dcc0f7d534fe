package com.xyy.saas.inquiry.enums.tencent;

public enum TencentImMessageTypeEnum {
    TEXT("TIMTextElem","文本消息"),
    CUSTOM("TIMCustomElem","自定义消息"),
    FACE("TIMFaceElem","表情消息"),
    IMAGE("TIMImageElem","图片消息"),
    VIDEO("TIMVideoFileElem","视频消息"),
    SOUND("TIMSoundElem","语音消息"),
    LOCATION("TIMLocationElem","位置消息"),
    FILE("TIMFileElem","文件消息");

    private String type;
    private String desc;

    TencentImMessageTypeEnum(String type ,String desc) {
        this.type = type;
        this.desc = desc;
    }

    public String getType() {
        return type;
    }
}
