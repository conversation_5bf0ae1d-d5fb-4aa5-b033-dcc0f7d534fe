package com.xyy.saas.inquiry.util;

import com.aliyun.openservices.shade.com.google.common.base.Function;
import com.xyy.saas.inquiry.pojo.BaseDto;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.BiConsumer;
import java.util.stream.Stream;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;

/**
 * @Author:chenxiaoyi
 * @Date:2025/01/23 17:15
 */
public class UserUtil {

    /**
     * 填充用户信息
     *
     * @param list     需要填充的列表
     * @param supplier 执行查user的dubbo接口表达式
     * @param <T>      list数据类型
     */
    public static <T extends BaseDto> void fillUserInfo(List<T> list, Function<List<Long>, Map<Long, String>> supplier) {
        List<Long> userIdList = list.stream().flatMap(i -> Stream.of(i.getCreator(), i.getUpdater())).map(NumberUtils::toLong).filter(i -> i >= 0).toList();
        Map<Long, String> userMap = supplier.apply(userIdList);
        list.forEach(c -> {
            c.setCreator(userMap.getOrDefault(NumberUtils.toLong(c.getCreator()), StringUtils.equals(c.getCreator(), "0") ? "系统" : c.getCreator()));
            c.setUpdater(userMap.getOrDefault(NumberUtils.toLong(c.getUpdater()), StringUtils.equals(c.getUpdater(), "0") ? "系统" : c.getUpdater()));
        });
    }

    /**
     * 填充用户信息
     *
     * @param list
     * @param supplier
     * @param userStringIdFillers
     * @param <T>
     */
    @SafeVarargs
    public static <T> void fillUserInfo2(List<T> list, Function<List<Long>, Map<Long, String>> supplier, UserStringIdFiller<T>... userStringIdFillers) {
        if (userStringIdFillers == null || userStringIdFillers.length == 0) {
            return;
        }

        List<Long> userIdList = list.stream().flatMap(i -> Arrays.stream(userStringIdFillers).map(x -> x.till(i))).filter(i -> i > 0).toList();
        Map<Long, String> userMap = supplier.apply(userIdList);
        list.forEach(c -> {
            Arrays.stream(userStringIdFillers).forEach(x -> x.fill(c, userMap));
        });
    }

    // 定义一个函数式接口，用于获取用户ID和设置用户名称
    public static class UserStringIdFiller<T> {

        private final Function<T, String> userIdGetter;
        private final BiConsumer<T, String> userNameSetter;

        public UserStringIdFiller(Function<T, String> userIdGetter, BiConsumer<T, String> userNameSetter) {
            this.userIdGetter = userIdGetter;
            this.userNameSetter = userNameSetter;
        }

        public Long till(T t) {
            return NumberUtils.toLong(userIdGetter.apply(t));
        }
        public T fill(T t, Map<Long, String> userMap) {
            Long userId = till(t);
            String userName = userMap.getOrDefault(userId, "" + userId);
            userNameSetter.accept(t, userName);
            return t;
        }
    }



}
