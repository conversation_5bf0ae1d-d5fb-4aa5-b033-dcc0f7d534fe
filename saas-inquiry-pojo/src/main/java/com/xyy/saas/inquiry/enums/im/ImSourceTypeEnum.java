package com.xyy.saas.inquiry.enums.im;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.Setter;
import org.apache.commons.lang3.StringUtils;
import com.jayway.jsonpath.JsonPath;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * IM ext 消息中的 sourceType 枚举
 */
@Getter
@AllArgsConstructor
public enum ImSourceTypeEnum {
    MsgText("text", "文本消息", true, ImChatAnalysisTypeEnum.MsgBody.code, "$.msgContent.text"),
    MsgImage("img", "[图片消息]点击可查看详情", true, ImChatAnalysisTypeEnum.MsgExt.code, "$.img"),
    NotifyMedicine("drugInfo", "[卡片消息]用药申请单，点击可查看详情", true, ImChatAnalysisTypeEnum.MsgBody.code, "$.msgContent.text"),
    CardPat("diseaseInfo", "[卡片消息]病情资料，点击可查看详情", true, ImChatAnalysisTypeEnum.EnumDesc.code, ""),
    CardDoc("doctorInfo", "[卡片消息]医生资料，点击可查看详情", true, ImChatAnalysisTypeEnum.EnumDesc.code, ""),
    CardEvaForm("evaluate", "[卡片消息]问诊评价，点击可查看详情", true, ImChatAnalysisTypeEnum.EnumDesc.code, ""),
    CardPre("prescriptionInfo", "[卡片消息]处方单，点击可查看详情", true, ImChatAnalysisTypeEnum.EnumDesc.code, ""),
    BubbleNotify("bubbleNotify", "[通知消息]点击可查看详情", false, ImChatAnalysisTypeEnum.MsgBody.code, "$.msgContent.text"),
    CardClinical("clinicalCase", "[卡片消息]门诊病例", true, ImChatAnalysisTypeEnum.EnumDesc.code, ""),
    MsgAudio("audio", "[语音消息]点击可查看详情", true, ImChatAnalysisTypeEnum.MsgExt.code, "$.url"),
    ;

    private final String code;
    private final String description;
    private final boolean showInImPdf;
    private final Integer imChatAnalysisType;
    private final String jsonPath;

    @Getter
    @AllArgsConstructor
    public static enum ImChatAnalysisTypeEnum {
        MsgBody(1, "从body里面解析内容"),
        MsgExt(2, "从ext里面解析内容"),
        EnumDesc(3, "取枚举的描述"),
        ;

        private final Integer code;
        private final String description;

        public static ImChatAnalysisTypeEnum fromCode(Integer code) {
            for (ImChatAnalysisTypeEnum type : ImChatAnalysisTypeEnum.values()) {
                if (type.getCode().equals(code)) {
                    return type;
                }
            }
            return null;
        }
    }

    @Getter
    @Setter
    @Builder
    @AllArgsConstructor
    public static class ImChatAnalysisObj {

        private String sourceType;
        private ImSourceTypeEnum imSourceTypeEnum;
        private String msgBodyJsonStr;
        private String msgExtJsonStr;
    }

    private static final Map<Integer, Function<ImChatAnalysisObj, String>> imChatAnalysisMap = new HashMap<>();
    static {
        imChatAnalysisMap.put(ImChatAnalysisTypeEnum.MsgBody.getCode(), ImSourceTypeEnum::analysisMsgBodyJson);
        imChatAnalysisMap.put(ImChatAnalysisTypeEnum.MsgExt.getCode(), ImSourceTypeEnum::analysisMsgExtJson);
        imChatAnalysisMap.put(ImChatAnalysisTypeEnum.EnumDesc.getCode(), ImSourceTypeEnum::analysisEnumDesc);
    }

    public static ImSourceTypeEnum fromCode(String code) {
        for (ImSourceTypeEnum type : ImSourceTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return null;
    }

    public static String getDescriptionByCode(String code) {
        for (ImSourceTypeEnum type : ImSourceTypeEnum.values()) {
            if (type.getCode().equals(code)) {
                return type.getDescription();
            }
        }
        return "";
    }

    public static boolean getShowInImPdfByCode(String code) {
        if (StringUtils.isBlank(code)) {
            return false;
        }
        for (ImSourceTypeEnum type : values()) {
            if (type.getCode().equals(code)) {
                return type.showInImPdf;
            }
        }
        return false;
    }

    /**
     * 根据code获取IM pdf内容
     *
     * @param imChatAnalysisObj
     * @return
     */
    public static String getImPdfContentByCode(ImChatAnalysisObj imChatAnalysisObj) {

        if (imChatAnalysisObj == null || StringUtils.isBlank(imChatAnalysisObj.getSourceType())) {
            return "";
        }

        ImSourceTypeEnum imSourceTypeEnum = fromCode(imChatAnalysisObj.getSourceType());
        if (imSourceTypeEnum == null || ImChatAnalysisTypeEnum.fromCode(imSourceTypeEnum.getImChatAnalysisType()) == null) {
            return "";
        }

        imChatAnalysisObj.setImSourceTypeEnum(imSourceTypeEnum);

        return imChatAnalysisMap.get(imSourceTypeEnum.getImChatAnalysisType()).apply(imChatAnalysisObj);
    }

    /**
     * 解析MsgBody的json字符串
     *
     * @param imChatAnalysisObj
     * @return
     */
    public static String analysisMsgBodyJson(ImChatAnalysisObj imChatAnalysisObj) {

        return JsonPath.read(imChatAnalysisObj.getMsgBodyJsonStr(), imChatAnalysisObj.getImSourceTypeEnum().jsonPath);
    }

    /**
     * 解析MsgExt的json字符串
     *
     * @param imChatAnalysisObj
     * @return
     */
    public static String analysisMsgExtJson(ImChatAnalysisObj imChatAnalysisObj) {

        return JsonPath.read(imChatAnalysisObj.getMsgExtJsonStr(), imChatAnalysisObj.getImSourceTypeEnum().jsonPath);
    }

    /**
     * 返回枚举的描述
     *
     * @param imChatAnalysisObj
     * @return
     */
    public static String analysisEnumDesc(ImChatAnalysisObj imChatAnalysisObj) {

        return imChatAnalysisObj.getImSourceTypeEnum().description;
    }
}
