package com.xyy.saas.inquiry.pojo.inquiry;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/9/2 下午2:53
 */
@Data
@Schema(description = "用户问诊单 App - 商品项")
@Valid
public class InquiryItem implements Serializable {

    @Schema(description = "商品 SKU 编号", example = "2048")
    @NotNull(message = "商品 SKU 编号不能为空")
    private Long skuId;

    @Schema(description = "购买数量", example = "1")
    @Min(value = 0, message = "购买数量最小值为 {value}")
    private BigDecimal count;

    @AssertTrue(message = "商品不正确")
    @JsonIgnore
    public boolean isValid() {
        if (skuId == null && count == null) {
            return false;
        }
        // 组合一：skuId + count 使用商品 SKU
        if (skuId != null && count != null) {
            return true;
        }
        return true;
    }


}
