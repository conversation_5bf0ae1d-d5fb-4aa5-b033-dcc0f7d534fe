package com.xyy.saas.inquiry.enums.migration;

/**
 * 迁移节点状态  0待迁移 1失败 2成功
 */
public enum MigrationPointStatusEnum {
    PENDING_MIGRATION(0, "待迁移"),
    FAILED(1, "失败"),
    SUCCESS(2, "成功"),

    ;

    private final int code;
    private final String desc;

    MigrationPointStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
