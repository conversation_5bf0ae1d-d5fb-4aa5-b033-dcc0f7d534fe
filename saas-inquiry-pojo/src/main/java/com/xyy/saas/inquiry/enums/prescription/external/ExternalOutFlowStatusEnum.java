package com.xyy.saas.inquiry.enums.prescription.external;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 外配处方外流状态
 */
@Getter
@RequiredArgsConstructor
public enum ExternalOutFlowStatusEnum implements IntArrayValuable {


    AWAITING(0, "待外流"),

    PREPARE(1, "已预核验"),

    UPLOADED(2, "已上传"),

    RESCINDED(3, "已撤销"),

    ;

    // 枚举类的私有成员变量
    private final int code;
    private final String description;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(ExternalOutFlowStatusEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    // 根据整数值获取枚举实例的静态方法
    public static ExternalOutFlowStatusEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElse(AWAITING);
    }
}
