package com.xyy.saas.inquiry.enums.tenant;

import lombok.Getter;
import java.util.List;

@Getter
public enum TenantCertificateTypeEnum {

    YYZJ(1, "营业执照"),

    JYXK(2, "药品经营许可"),

    JYZLGL(3, "药品经营质量管理规范"),

    WZFWHT(4, "问诊服务合同"),

    BAHTBMXY(5, "备案合同及保密协议"),

    YCSFBA(6, "远程审方备案件"),

    ;


    private final Integer type;
    private final String description;


    TenantCertificateTypeEnum(Integer type, String description) {
        this.type = type;
        this.description = description;
    }
}
