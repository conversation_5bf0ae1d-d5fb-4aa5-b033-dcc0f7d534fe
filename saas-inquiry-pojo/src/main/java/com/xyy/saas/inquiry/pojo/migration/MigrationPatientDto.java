package com.xyy.saas.inquiry.pojo.migration;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2025/06/05 16:09
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class MigrationPatientDto implements java.io.Serializable {

    private Long tenantId;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 年龄
     */
    private String age;

    /**
     * 性别 1 男 2 女
     */
    private Byte sex;

    /**
     * 电话
     */
    private String telephone;

    /**
     * 身份证号
     */
    private String idCard;
}
