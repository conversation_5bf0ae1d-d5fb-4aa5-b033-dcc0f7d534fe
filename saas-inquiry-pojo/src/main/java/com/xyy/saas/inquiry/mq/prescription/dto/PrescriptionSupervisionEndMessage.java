package com.xyy.saas.inquiry.mq.prescription.dto;

import com.xyy.saas.inquiry.enums.registration.RegistrationStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Author: cxy
 * @Date: 2024/12/11 13:54
 * @Description: 处方对接监管完成事件
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PrescriptionSupervisionEndMessage implements Serializable {

    /**
     * 问诊单号
     */
    private String inquiryPref;

    /**
     * 就诊登记状态
     */
    private RegistrationStatusEnum registrationStatusEnum;

}
