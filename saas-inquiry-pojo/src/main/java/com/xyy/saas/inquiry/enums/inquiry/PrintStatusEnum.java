package com.xyy.saas.inquiry.enums.inquiry;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * @Desc 打印状态
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/8/30 上午9:32
 */
@Getter
@RequiredArgsConstructor
public enum PrintStatusEnum {

    NOT_PRINTED(0, "未打印"),

    PRINTED(1, "已打印"),

    UNKNOWN(-1, "未知");

    private final int statusCode;

    private final String desc;

    public static PrintStatusEnum fromStatusCode(int statusCode) {
        return Arrays.stream(values())
            .filter(value -> value.getStatusCode() == statusCode)
            .findFirst()
            .orElse(UNKNOWN);
    }
}