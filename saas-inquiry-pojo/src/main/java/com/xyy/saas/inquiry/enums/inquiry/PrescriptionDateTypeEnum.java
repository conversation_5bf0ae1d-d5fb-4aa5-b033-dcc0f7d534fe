package com.xyy.saas.inquiry.enums.inquiry;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;

/**
 * 门店 日期展示类型枚举
 *
 * @Author:chenxiaoyi
 * @Date:2024/01/04 9:43
 */
public enum PrescriptionDateTypeEnum implements IntArrayValuable {

    // UNSET(0, null, "未设置"),

    YYYY_MM_DD_HH_MM_SS(1, "yyyy-MM-dd HH:mm:ss", "年月日 时分秒"),

    YYYY_MM_DD(2, "yyyy-MM-dd", "年月日");

    private final Integer code;

    private final String format;

    private final String remark;

    PrescriptionDateTypeEnum(Integer code, String format, String remark) {
        this.code = code;
        this.format = format;
        this.remark = remark;
    }

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(PrescriptionDateTypeEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }


    public Integer getCode() {
        return code;
    }

    public String getFormat() {
        return format;
    }

    public String getRemark() {
        return remark;
    }

    public static String getDateformat(Integer code) {
        if (code == null) {
            return null;
        }
        for (PrescriptionDateTypeEnum dateTypeEnum : values()) {
            if (Objects.equals(dateTypeEnum.code, code)) {
                return dateTypeEnum.format;
            }
        }
        return null;
    }

    /**
     * 获取日期格式化
     *
     * @param code          类型
     * @param date          日期
     * @param defaultFormat 默认格式
     * @return 格式化之后的日期
     */
    public static String formatDate(Integer code, LocalDateTime date, String... defaultFormat) {
        if (date == null) {
            return "";
        }
        String dateformat = getDateformat(code);
        return DateTimeFormatter.ofPattern(StringUtils.isBlank(dateformat) ? defaultFormat.length < 1 ? YYYY_MM_DD_HH_MM_SS.format : defaultFormat[0] : dateformat).format(date);
    }


}
