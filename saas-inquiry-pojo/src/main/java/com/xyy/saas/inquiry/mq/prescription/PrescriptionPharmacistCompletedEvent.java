package com.xyy.saas.inquiry.mq.prescription;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import com.xyy.saas.inquiry.mq.prescription.dto.PrescriptionMqCommonMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 处方药师审方完成事件
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class PrescriptionPharmacistCompletedEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "PRESCRIPTION_PHARMACIST_COMPLETED";

    private PrescriptionMqCommonMessage msg;

    @JsonCreator
    public PrescriptionPharmacistCompletedEvent(@JsonProperty("msg") PrescriptionMqCommonMessage msg) {
        this.msg = msg;
    }

    @Override
    public String getTag() {
        return "";
    }
} 