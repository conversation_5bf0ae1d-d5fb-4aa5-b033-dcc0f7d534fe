package com.xyy.saas.inquiry.enums.doctor;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * 药师资格 0 中西药药师 1 西药药师 2 中药药师
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum PharmacistQualificationEnum implements IntArrayValuable {

    CW(0, "中西药药师"),
    W(1, "西药药师"),
    C(2, "中药药师"),

    ;

    private final int code;

    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(PharmacistQualificationEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 枚举值code
     * @return 枚举值
     */
    public static PharmacistQualificationEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElse(CW);
    }


    public static List<Integer> convertMedicineTypeFromCode(Integer qualification) {
        PharmacistQualificationEnum pq = fromCode(qualification);
        if (CW.equals(pq)) {
            return List.of(MedicineTypeEnum.ASIAN_MEDICINE.getCode(), MedicineTypeEnum.CHINESE_MEDICINE.getCode());
        }
        if (W.equals(pq)) {
            return List.of(MedicineTypeEnum.ASIAN_MEDICINE.getCode());
        }
        return List.of(MedicineTypeEnum.CHINESE_MEDICINE.getCode());
    }

    /**
     * 转换药品类型
     *
     * @param medicineType
     * @return
     */
    public static List<Integer> convertFromMedicineTypeCode(Integer medicineType) {

        MedicineTypeEnum medicineTypeEnum = MedicineTypeEnum.fromCode(medicineType);

        if (MedicineTypeEnum.ASIAN_MEDICINE.equals(medicineTypeEnum)) {
            return Arrays.asList(PharmacistQualificationEnum.W.getCode(), PharmacistQualificationEnum.CW.getCode());
        }
        return Arrays.asList(PharmacistQualificationEnum.C.getCode(), PharmacistQualificationEnum.CW.getCode());
    }

}