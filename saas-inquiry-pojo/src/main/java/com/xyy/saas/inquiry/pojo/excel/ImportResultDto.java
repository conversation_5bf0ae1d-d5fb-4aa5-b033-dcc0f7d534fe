package com.xyy.saas.inquiry.pojo.excel;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:chenxiaoyi
 * @Date:2025/01/22 15:49
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImportResultDto implements java.io.Serializable {

    @Schema(description = "导入总数量", example = "100")
    private Long totalCount;

    @Schema(description = "成功数量", example = "99")
    private Long successCount = 0L;

    @Schema(description = "失败数量", example = "1")
    private Long failureCount = 0L;

    @Schema(description = "待确认数量", example = "1")
    private Long confirmCount = 0L;

    @Schema(description = "失败文件现在地址", example = "http://xxx.xls")
    private String fileUrl;

    @Schema(description = "文件名称", example = "导入文件")
    private String fileName;

}
