package com.xyy.saas.inquiry.mq.prescription;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import org.springframework.stereotype.Component;


@Component
@EventBusProducer(
    topic = AbandonPrescriptionPostPassingEvent.TOPIC
)
public class AbandonPrescriptionPostPassingProducer extends EventBusRocketMQTemplate {

}
