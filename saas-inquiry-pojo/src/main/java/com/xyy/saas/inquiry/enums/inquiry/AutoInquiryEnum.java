package com.xyy.saas.inquiry.enums.inquiry;

/**
 * 自动开方标识
 */
public enum AutoInquiryEnum {
    YES(1, "是"),
    NO(0, "否");

    private final int code;
    private final String desc;

    AutoInquiryEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
