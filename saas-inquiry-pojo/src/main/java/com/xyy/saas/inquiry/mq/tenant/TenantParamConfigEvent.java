package com.xyy.saas.inquiry.mq.tenant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import java.util.List;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class TenantParamConfigEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "TENANT_PARAM_CONFIG_SAVE";

    private List<TenantParamConfigDto> msg;

    @JsonCreator
    public TenantParamConfigEvent(@JsonProperty("msg") List<TenantParamConfigDto> msg) {
        this.msg = msg;
    }

    @Override
    public String getTag() {
        return "";
    }
}
