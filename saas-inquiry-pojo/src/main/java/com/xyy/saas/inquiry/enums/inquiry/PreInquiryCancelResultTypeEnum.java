package com.xyy.saas.inquiry.enums.inquiry;

import lombok.Getter;

/**
 * @Author: xucao
 * @DateTime: 2025/5/9 14:01
 * @Description: 预问诊取消结果类型 0-取消成功 1-审核通过，等待医生接诊（此时要切换等待状态为待接诊）  2-审核通过，医生已接诊（此时应该进入IM）
 **/
@Getter
public enum PreInquiryCancelResultTypeEnum {
    CANCEL_SUCCESS(0, "预问诊取消成功"),
    AUDIT_SUCCESS_WAIT_DOCTOR_CONNECT(1, "该问诊门店审核通过，正在为您寻找医生"),
    AUDIT_SUCCESS_DOCTOR_CONNECTED(2, "该问诊已被医生接单，无法取消"),
    ;

    private Integer type;
    private String msg;

    PreInquiryCancelResultTypeEnum(Integer type, String msg) {
        this.type = type;
        this.msg = msg;
    }
}
