package com.xyy.saas.inquiry.pojo.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 门店套餐额度扩展信息 DTO 用于存储 type 和 value 等扩展信息
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantPackageCostExtDto implements Serializable {

    @Schema(description = "处方类型", example = "[0,1]")
    private List<Integer> prescriptionValue;

    /**
     * 获取默认的扩展信息 用于兼容旧数据
     *
     * @return 默认扩展信息
     */
    public static TenantPackageCostExtDto getDefault() {
        return TenantPackageCostExtDto.builder()
            .prescriptionValue(List.of())
            .build();
    }
}
