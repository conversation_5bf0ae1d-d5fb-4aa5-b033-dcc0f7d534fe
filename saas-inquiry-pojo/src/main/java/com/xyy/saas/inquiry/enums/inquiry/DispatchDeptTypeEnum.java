package com.xyy.saas.inquiry.enums.inquiry;

import lombok.Getter;

/**
 * @Author: xucao
 * @DateTime: 2025/4/11 14:10
 * @Description: 调度科室的枚举  0 诊断关联科室   1 默认科室
 **/
@Getter
public enum DispatchDeptTypeEnum {
    DIAGNOSIS_DEPARTMENT(0, "诊断关联科室"),
    DEFAULT_DEPARTMENT (1, "默认科室");

    private final int code;
    private final String desc;

    DispatchDeptTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
