package com.xyy.saas.inquiry.enums.pharmacist;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import java.util.Arrays;
import java.util.Objects;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;

/**
 * 地区审方类型
 */
@Getter
@RequiredArgsConstructor
public enum PharmacistAuditAreaTypeEnum implements IntArrayValuable {

    GLOBAL(0, "全国药师可审方"),

    LOCAL(1, "仅本地药师可审方"),
    ;

    // 枚举类的私有成员变量
    private final int code;
    private final String description;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(PharmacistAuditAreaTypeEnum::getCode).toArray();

    public static String getLocalArea(String optionValue, String provinceCode) {
        if (StringUtils.equals(LOCAL.code + "", optionValue)) {
            return provinceCode;
        }
        return "";
    }

    @Override
    public int[] array() {
        return ARRAYS;
    }

    // 根据整数值获取枚举实例的静态方法
    public static PharmacistAuditAreaTypeEnum fromCode(Integer code) {
        return Arrays.stream(values())
            .filter(type -> Objects.equals(type.getCode(), code))
            .findFirst()
            .orElse(GLOBAL);
    }
}
