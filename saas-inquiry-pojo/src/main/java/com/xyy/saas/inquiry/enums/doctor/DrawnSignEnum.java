package com.xyy.saas.inquiry.enums.doctor;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 处方签名手绘标识 是否处方手绘签名(不走认证自己绘制)
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum DrawnSignEnum implements IntArrayValuable {

    N(0, "否"),

    Y(1, "是");

    private final int code;

    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(DrawnSignEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 枚举值code
     * @return 枚举值
     */
    public static DrawnSignEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("Invalid DrawnSignEnum code: " + code));
    }
}