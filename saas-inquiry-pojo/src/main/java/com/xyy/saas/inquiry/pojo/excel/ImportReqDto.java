package com.xyy.saas.inquiry.pojo.excel;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2025/01/22 15:49
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ImportReqDto implements java.io.Serializable {

    @Schema(description = "导入的文件地址", example = "http://xxx.xls")
    @NotBlank(message = "上传文件地址不能为空")
    private String fileUrl;

    private String excelName;

    private Integer limitCount;

    private Long confirmCount = 0L;

}
