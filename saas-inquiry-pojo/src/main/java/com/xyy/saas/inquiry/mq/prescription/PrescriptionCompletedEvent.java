package com.xyy.saas.inquiry.mq.prescription;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 处方流程完成事件
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class PrescriptionCompletedEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "PRESCRIPTION_COMPLETED";

    private String msg;

    @JsonCreator
    public PrescriptionCompletedEvent(@JsonProperty("msg") String msg) {
        this.msg = msg;
    }

    @Override
    public String getTag() {
        return "";
    }
} 