package com.xyy.saas.inquiry.util;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import com.xyy.saas.inquiry.pojo.condition.ConditionGroup;
import com.xyy.saas.inquiry.pojo.condition.ConditionParamDto;
import com.xyy.saas.inquiry.pojo.condition.ConditionRuleOp;
import com.xyy.saas.inquiry.pojo.condition.ConditionRuleType;
import lombok.experimental.UtilityClass;
import java.util.List;
import java.util.Optional;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@UtilityClass
public class ConditionUtil {

    /**
     * 匹配
     *
     * @param paramDto
     * @param conditionGroupList
     * @param <T>
     * @return
     */
    public static <T extends ConditionGroup> List<T> match(ConditionParamDto paramDto, List<T> conditionGroupList) {
        if (CollUtil.isEmpty(conditionGroupList) || paramDto == null) {
            return List.of();
        }
        return conditionGroupList.stream()
            .filter(s -> Optional.ofNullable(s.getConditions()).orElse(List.of()).stream()
                .allMatch(c -> {
                    if (c.isAnd()) {
                        return Optional.ofNullable(c.getRules()).orElse(List.of()).stream()
                            .allMatch(r -> ConditionRuleType.fromType(r.getType()) != null && ConditionRuleType.fromType(r.getType()).match(r.getValue(),
                                ConditionRuleOp.fromType(r.getOp()), paramDto));
                    }
                    return Optional.ofNullable(c.getRules()).orElse(List.of()).stream()
                        .anyMatch(r -> ConditionRuleType.fromType(r.getType()) != null && ConditionRuleType.fromType(r.getType()).match(r.getValue(),
                            ConditionRuleOp.fromType(r.getOp()), paramDto));
                })).toList();
    }

    /**
     * 匹配第一个
     *
     * @param paramDto
     * @param conditionGroupList
     * @param <T>
     * @return
     */
    public static <T extends ConditionGroup> T matchFirst(ConditionParamDto paramDto, List<T> conditionGroupList) {
        List<T> list = match(paramDto, conditionGroupList);
        return CollUtil.isEmpty(list) ? null : list.getFirst();
    }

    /**
     * 是否匹配
     *
     * @param paramDto
     * @param conditionGroupList
     * @return
     */
    public static <T extends ConditionGroup> boolean isMatch(ConditionParamDto paramDto, List<T> conditionGroupList) {
        return CollUtil.isNotEmpty(match(paramDto, conditionGroupList));
    }

    public static void main(String[] args) {
        boolean match = isMatch(ConditionParamDto.builder().age("10").build(), JsonUtils.parseArray("[{\"and\":true,\"conditions\":[{\"and\":true,\"rules\":[{\"type\":\"age\",\"op\":\"eq\",\"value\":\"10\"}]}]}]", ConditionGroup.class));
        System.out.println(match);
    }
}
