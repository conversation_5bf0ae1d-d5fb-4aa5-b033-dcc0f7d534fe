package com.xyy.saas.inquiry.enums.inquiry;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * @Desc 问诊状态 mock 消息仅用于前期联调使用正式不会上线
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/8/29 下午2:29
 */
@Getter
@RequiredArgsConstructor
public enum InquiryStatusEnum implements IntArrayValuable {

    QUEUING(0, "排队中", ""),

    CANCELED(1, "已取消", "当前问诊已取消"),

    INQUIRING(2, "问诊中/已连接", "请根据症状开具处方"),

    ENDED(3, "问诊结束", "您好，您的处方已开具"),

    DOCTOR_CANCELED(4, "医生取消开方", "抱歉！无法为您开具处方"),

    TIMEOUT_CANCELED(5, "超时系统自动取消开方", "超时系统自动取消开方"),

    UNKNOWN(-1, "未知", "");

    private final int statusCode;

    private final String desc;

    //mock 消息仅用于前期联调使用正式不会上线
    private final String mockMsg;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(InquiryStatusEnum::getStatusCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static InquiryStatusEnum fromStatusCode(int statusCode) {
        return Arrays.stream(values())
            .filter(value -> value.getStatusCode() == statusCode)
            .findFirst()
            .orElse(UNKNOWN);
    }

    public static String getMockMsgByStatusCode(int statusCode) {
        return Arrays.stream(values())
            .filter(value -> value.getStatusCode() == statusCode)
            .findFirst().get().mockMsg;
    }

    /**
     * 获取问诊聚合状态
     *
     * @param statusCode
     * @return
     */
    public static Integer getInquiryGroupStatus(Integer statusCode) {

        if (statusCode == null) {
            return null;
        }

        InquiryStatusEnum inquiryStatusEnum = fromStatusCode(statusCode);

        if (inquiryStatusEnum == UNKNOWN) {
            return null;
        }

        if (InquiryStatusEnum.INQUIRING == inquiryStatusEnum) {
            return InquiryGroupStatusEnum.INQUIRING.statusCode;
        } else if (InquiryStatusEnum.QUEUING == inquiryStatusEnum) {
            return InquiryGroupStatusEnum.WAITING.statusCode;
        } else {
            return InquiryGroupStatusEnum.COMPLETED.statusCode;
        }

    }

    /**
     * 问诊聚合状态
     */
    @Getter
    @RequiredArgsConstructor
    public enum InquiryGroupStatusEnum implements IntArrayValuable {

        INQUIRING(1, "进行中"),
        WAITING(2, "待接诊"),
        COMPLETED(3, "已完成"),
        ;

        private final Integer statusCode;
        private final String desc;

        public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(InquiryGroupStatusEnum::getStatusCode).toArray();

        @Override
        public int[] array() {
            return ARRAYS;
        }

        public static InquiryGroupStatusEnum fromStatusCode(Integer statusCode) {
            return Arrays.stream(values())
                .filter(value -> value.getStatusCode().equals(statusCode))
                .findFirst()
                .orElse(null);
        }

        /**
         * 根据问诊聚合状态，获取问诊状态列表
         *
         * @param inquiryGroupStatus 问诊聚合状态
         * @return 问诊状态列表
         */
        public static List<Integer> getInquiryStatusList(Integer inquiryGroupStatus) {

            InquiryGroupStatusEnum inquiryGroupStatusEnum = fromStatusCode(inquiryGroupStatus);

            if (inquiryGroupStatusEnum == null) {
                return List.of();
            }

            switch (inquiryGroupStatusEnum) {
                case INQUIRING:
                    return List.of(InquiryStatusEnum.INQUIRING.getStatusCode());
                case WAITING:
                    return List.of(InquiryStatusEnum.QUEUING.getStatusCode());
                case COMPLETED:
                    List<Integer> notCompletedStatusList = Lists.newArrayList(InquiryStatusEnum.INQUIRING.getStatusCode(), InquiryStatusEnum.QUEUING.getStatusCode());
                    return Arrays.stream(InquiryStatusEnum.values()).map(InquiryStatusEnum::getStatusCode).filter(code -> !notCompletedStatusList.contains(code)).toList();
                default:
                    return List.of();
            }
        }

    }
}

