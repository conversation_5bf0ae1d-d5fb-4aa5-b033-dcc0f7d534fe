package com.xyy.saas.inquiry.pojo.signature;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2025/02/06 14:54
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "签章合同扩展信息ext")
public class SignatureContractExtDto implements java.io.Serializable {

    /**
     * 签章平台应用配置id-当不是自绘时 用
     */
    private Integer platformConfigId;

    /**
     * 签章平台pdfUrl
     */
    private String platformPdfUrl;

    /**
     * 备注
     */
    private String remark;

    /**
     * 问诊业务类型 仅带方审方时有
     */
    private Integer inquiryBizType;

    /**
     * 远程审方处方url原始图片
     */
    private String remotePrescriptionUrl;

    /**
     * 签名坐标,带方审方时不可为空
     */
    private Integer coordinateX;

    /**
     * 签名坐标,带方审方时不可为空
     */
    private Integer coordinateY;

}
