package com.xyy.saas.inquiry.mq.prescription;


import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import org.springframework.stereotype.Component;

@Component
@EventBusProducer(
    topic = AbandonRestorePrescriptionPostPassingEvent.TOPIC
)
public class AbandonRestorePrescriptionPostPassingProducer  extends EventBusRocketMQTemplate {

}
