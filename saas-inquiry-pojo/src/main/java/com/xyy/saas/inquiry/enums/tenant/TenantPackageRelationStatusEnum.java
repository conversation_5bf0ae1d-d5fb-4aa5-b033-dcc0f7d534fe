package com.xyy.saas.inquiry.enums.tenant;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @Desc 门店套餐订单状态 '订单状态, 0正常, 1暂停, 2退款, 3作废',
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum TenantPackageRelationStatusEnum implements IntArrayValuable {

    NORMAL(0, "正常"),

    STOP(1, "暂停"),

    REFUND(2, "退款"),

    ABANDONED(3, "作废"),

    ;

    private final int code;

    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(TenantPackageRelationStatusEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static TenantPackageRelationStatusEnum fromStatusCode(int statusCode) {
        return Arrays.stream(values())
            .filter(value -> value.getCode() == statusCode)
            .findFirst()
            .orElse(NORMAL);
    }

    /**
     * 是否是最终状态
     *
     * @param statusCode
     * @return
     */
    public static boolean finalStatus(int statusCode) {
        return Stream.of(REFUND.getCode(), ABANDONED.getCode()).collect(Collectors.toSet()).contains(statusCode);
    }


    public static Integer convertStatus(Byte validStatus) {
        if (Objects.equals(validStatus.intValue(), 4)) {
            return TenantPackageRelationStatusEnum.REFUND.getCode();
        }
        if (Objects.equals(validStatus.intValue(), 5)) {
            return TenantPackageRelationStatusEnum.ABANDONED.getCode();
        }

        return TenantPackageRelationStatusEnum.NORMAL.getCode();
    }

}