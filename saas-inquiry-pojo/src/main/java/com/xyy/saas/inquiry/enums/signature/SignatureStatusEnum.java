package com.xyy.saas.inquiry.enums.signature;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/8/30 上午11:34 参与方
 */
@Getter
@RequiredArgsConstructor
public enum SignatureStatusEnum {

    WAITING(0, "待签章"),

    SENDING(1, "发起签章"),

    SIGNED(2, "已签章"),

    UNKNOWN(-1, "未知"),
    ;

    private final int statusCode;

    private final String desc;

    public static SignatureStatusEnum fromStatusCode(int statusCode) {
        return Arrays.stream(values())
            .filter(value -> value.getStatusCode() == statusCode)
            .findFirst()
            .orElse(UNKNOWN);
    }


}
