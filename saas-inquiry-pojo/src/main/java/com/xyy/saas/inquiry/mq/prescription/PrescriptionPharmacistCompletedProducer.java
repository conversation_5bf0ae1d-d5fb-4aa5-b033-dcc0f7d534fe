package com.xyy.saas.inquiry.mq.prescription;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import org.springframework.stereotype.Component;

/**
 * 处方药师审方完成事件生产者
 *
 * <AUTHOR>
 */
@Component
@EventBusProducer(
    topic = PrescriptionPharmacistCompletedEvent.TOPIC
)
public class PrescriptionPharmacistCompletedProducer extends EventBusRocketMQTemplate {

} 