package com.xyy.saas.inquiry.pojo;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum;
import com.xyy.saas.inquiry.pojo.tenant.TenantCertificateRespDto;
import com.xyy.saas.inquiry.pojo.tenant.TenantExtDto;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2024/09/27 17:26
 */
@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TenantDto extends BaseDto {

    private Long id;

    /**
     * 编码
     */
    private String pref;
    /**
     * 门店名
     */
    private String name;

    /**
     * 租户类型（1-单店 2连锁门店 3连锁总部）
     */
    private TenantTypeEnum type;

    /**
     * 问诊 门店类型
     */
    private TenantTypeEnum wzTenantType;

    /**
     * 租户编号（总部）
     */
    private Long headTenantId;

    /**
     * 联系人的用户编号
     */
    private Long contactUserId;
    /**
     * 联系人
     */
    private String contactName;
    /**
     * 联系手机
     */
    private String contactMobile;
    /**
     * 营业执照名称
     */
    private String businessLicenseName;
    /**
     * 营业执照号
     */
    private String businessLicenseNumber;

    /**
     * 问诊系统业务类型开通
     */
    private Integer wzBizTypeStatus;
    /**
     * 账号数量
     */
    private Integer wzAccountCount;
    // /**
    //  * 门店状态（0正常 1停用） 枚举 {@link CommonStatusEnum}
    //  */
    // private Integer wzStatus;
    /**
     * 智慧脸业务线类型开通
     */
    private Integer zhlBizTypeStatus;
    /**
     * 智慧脸账号数量
     */
    private Integer zhlAccountCount;
    // /**
    //  * 智慧脸系统状态(0正常 1停用) 枚举 {@link CommonStatusEnum}
    //  */
    // private Integer zhlStatus;

    /**
     * 省
     */
    private String province;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 市
     */
    private String city;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 区
     */
    private String area;
    /**
     * 区编码
     */
    private String areaCode;
    /**
     * 药店地址
     */
    private String address;

    /**
     * 门店状态 枚举 {@link CommonStatusEnum}
     */
    private Integer status;

    /**
     * 环境
     */
    private String envTag;

    /**
     * 扩展信息
     */
    private TenantExtDto ext;

    /**
     * 绑定平台 0未绑定  1智慧脸
     */
    private Integer bindPlatform;

    /**
     * 证书信息
     */
    List<TenantCertificateRespDto> certificates;
}
