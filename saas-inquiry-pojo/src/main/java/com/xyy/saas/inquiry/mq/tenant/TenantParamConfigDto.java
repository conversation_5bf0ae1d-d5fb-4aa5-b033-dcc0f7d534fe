package com.xyy.saas.inquiry.mq.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2024/10/23 19:21
 */
@Schema(description = "用户参数配置 Request Dto")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@ToString(callSuper = true)
public class TenantParamConfigDto implements Serializable {

    /**
     * 门店id
     */
    private Long tenantId;

    /**
     * {@link com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum}
     */
    @Schema(description = "参数类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer paramType;

    @Schema(description = "参数值", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private String paramValue;

    /**
     * 创建人
     */
    private String creator;


}
