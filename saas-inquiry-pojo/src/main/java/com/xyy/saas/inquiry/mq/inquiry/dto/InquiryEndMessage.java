package com.xyy.saas.inquiry.mq.inquiry.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author: xucao
 * @Date: 2024/12/11 13:54
 * @Description: 问诊结束事件参数
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryEndMessage implements Serializable {

    /**
     * 问诊单号
     */
    private String inquiryPref;
}
