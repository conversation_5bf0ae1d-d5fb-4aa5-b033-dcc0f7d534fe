package com.xyy.saas.inquiry.enums.inquiry;

public enum BizChannelTypeEnum {

    /**
     * 荷叶问诊渠道
     */
    HEYE(0, "荷叶"),

    /**
     * 智慧脸问诊渠道
     */
    ZHIHUI_LIAN(1, "智慧脸"),

    /**
     * 小程序问诊渠道
     */
    MINI_PROGRAM(4, "小程序"),

    /**
     * 海典ERP问诊渠道
     */
    HAIDIAN_ERP(5, "海典ERP"),

    ;

    private final int code; // 渠道类型代码
    private final String name; // 渠道类型名称

    // 构造函数，用于初始化枚举项的字段
    BizChannelTypeEnum(int code, String name) {
        this.code = code;
        this.name = name;
    }

    /**
     * 获取枚举项对应的渠道类型代码
     *
     * @return 渠道类型代码
     */
    public int getCode() {
        return code;
    }

    /**
     * 获取枚举项对应的渠道类型名称
     *
     * @return 渠道类型名称
     */
    public String getName() {
        return name;
    }

    /**
     * 根据渠道类型代码获取对应的枚举类型
     *
     * @param code 渠道类型代码
     * @return 对应的枚举类型，如果不存在则返回 null
     */
    public static BizChannelTypeEnum fromCode(int code) {
        for (BizChannelTypeEnum type : values()) {
            if (type.getCode() == code) {
                return type;
            }
        }
        return null;
    }

}
