package com.xyy.saas.inquiry.enums.prescription.template;

/**
 * 模板字段类型
 *
 * @Author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date:2024/03/15 18:36
 */
public enum TemplateFieldTypeEnum {

    TXT(0, "单行文本"),
    MULTI_TXT(1, "多行文本"),
    PICTURE(2, "图片"),
    SIGN_PICTURE(3, "签章图片"),
    ;

    public Integer code;

    public String desc;

    TemplateFieldTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
