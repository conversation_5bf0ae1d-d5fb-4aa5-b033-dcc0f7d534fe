package com.xyy.saas.inquiry.mq.prescription;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import com.xyy.saas.inquiry.mq.prescription.dto.PrescriptionMqCommonMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 处方医院药师审核完成事件
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class PrescriptionHosAuditCompletedEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "PRESCRIPTION_HOS_AUDIT_COMPLETED";

    private PrescriptionMqCommonMessage msg;

    @JsonCreator
    public PrescriptionHosAuditCompletedEvent(@JsonProperty("msg") PrescriptionMqCommonMessage msg) {
        this.msg = msg;
    }

    @Override
    public String getTag() {
        return "";
    }
} 