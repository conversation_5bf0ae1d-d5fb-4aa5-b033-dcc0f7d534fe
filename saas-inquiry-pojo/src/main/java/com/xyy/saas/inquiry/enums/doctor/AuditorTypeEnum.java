package com.xyy.saas.inquiry.enums.doctor;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * 处方审核人类型
 *
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/8/30 上午11:34
 */
@Getter
@RequiredArgsConstructor
public enum AuditorTypeEnum implements IntArrayValuable {

    DOCTOR(1, "医生"),

    DRUGSTORE_PHARMACIST(2, "药店药师"),

    PLATFORM_PHARMACIST(3, "平台药师"),

    HOSPITAL_PHARMACIST(4, "医院药师"),

    CHECK(5, "核对"),

    ALLOCATION(6, "调配"),

    DISPENSING(7, "发药"),

    ;

    private final int code;

    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(AuditorTypeEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 枚举值code
     * @return 枚举值
     */
    public static AuditorTypeEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElse(DRUGSTORE_PHARMACIST);
    }


    /**
     * 转换药师类型
     *
     * @param pharmacistType
     * @return
     */
    public static AuditorTypeEnum convertPharmacistType(Integer pharmacistType) {
        if (Objects.equals(PharmacistTypeEnum.DRUGSTORE.getCode(), pharmacistType)) {
            return DRUGSTORE_PHARMACIST;
        }
        if (Objects.equals(PharmacistTypeEnum.PLATFORM.getCode(), pharmacistType)) {
            return PLATFORM_PHARMACIST;
        }
        if (Objects.equals(PharmacistTypeEnum.HOSPITAL.getCode(), pharmacistType)) {
            return HOSPITAL_PHARMACIST;
        }
        return null;
    }
}