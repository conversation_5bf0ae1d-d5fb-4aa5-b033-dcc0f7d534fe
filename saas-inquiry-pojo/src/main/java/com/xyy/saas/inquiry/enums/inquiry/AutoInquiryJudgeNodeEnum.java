package com.xyy.saas.inquiry.enums.inquiry;

import lombok.Getter;

/**
 * 自动开方判断节点枚举
 */
@Getter
public enum AutoInquiryJudgeNodeEnum {
    AUTO_INQUIRY_TO_REAL_FOR_ALLERGIC("presAutoInquiryToRealForAllergic", "过敏史是否流向真人问诊",1),
    AUTO_INQUIRY_TO_REAL_FOR_LIVER_RENAL_DYSFUNCTION("presAutoInquiryToRealForLiverRenalDysfunction", "肝肾功能异常是否流向真人问诊",2),
    AUTO_INQUIRY_TO_REAL_FOR_PREGNANCY_LACTATION("presAutoInquiryToRealForPregnancyLactation", "妊娠、哺乳期是否流向真人问诊",3),
    AUTO_INQUIRY_FORCE_TO_REAL_FOR_CONFIGURATION("presAutoInquiryToRealForForceConfig", "是否有强制走真人配置",4),
    AUTO_INQUIRY_TO_REAL_FOR_SPECIAL_AGE_RANGE("presAutoInquiryToRealForSpecialAgeRange", "特定年龄区间是否流向真人问诊",5),
    AUTO_INQUIRY_TO_REAL_FOR_USAGE_DOSAGE_MISSING ("presAutoInquiryToRealForUsageDosageMissing", "用法用量不全是否流向真人问诊",6),
    AUTO_INQUIRY_TO_REAL_FOR_MULTI_DIAGNOSE("presAutoInquiryToRealForMultiDiagnose", "多诊断是否流向真人问诊",7),
    AUTO_INQUIRY_TO_REAL_FOR_MANY_AUTO_VIDEO("presAutoInquiryToRealForManyAutoVideo", "24小时内存在视频问诊自动开方记录",8),
    AUTO_INQUIRY_TO_REAL_FOR_AUTO_VIDEO_WEIGHT ("presAutoInquiryToRealForAutoVideoWeight", "视频问诊自动开方权重判定",9);

    private final String code;
    private final String desc;
    private final Integer order;

    AutoInquiryJudgeNodeEnum(String code, String desc,Integer order) {
        this.code = code;
        this.desc = desc;
        this.order = order;
    }
}
