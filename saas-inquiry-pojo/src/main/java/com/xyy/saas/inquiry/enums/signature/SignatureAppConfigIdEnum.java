package com.xyy.saas.inquiry.enums.signature;

import java.util.Objects;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 法大大app应用配置id枚举
 *
 * @Author:chenxia<PERSON>i
 * @Date:2024/10/18 11:38
 */
@Getter
@RequiredArgsConstructor
public enum SignatureAppConfigIdEnum {

    DEFAULT(0, "", "默认应用"),

    EZ(1, "东方中讯", "东方中讯CA应用"),
    ;

    private final Integer code;

    private final String caName;

    private final String desc;


    public static boolean isDefault(Integer code) {
        return code == null || code.equals(DEFAULT.getCode());
    }

    public static String caNameForCode(Integer code) {
        for (SignatureAppConfigIdEnum configIdEnum : values()) {
            if (Objects.equals(configIdEnum.code, code)) {
                return configIdEnum.getCaName();
            }
        }
        return "";
    }


}
