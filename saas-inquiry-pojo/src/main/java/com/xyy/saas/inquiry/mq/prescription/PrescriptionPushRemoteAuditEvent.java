package com.xyy.saas.inquiry.mq.prescription;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import com.xyy.saas.inquiry.mq.prescription.dto.PrescriptionPushRemoteAuditEventMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: cxy
 * @Description: 门店处方推送远程审方 事件
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class PrescriptionPushRemoteAuditEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "PRESCRIPTION_PUSH_REMOTE_AUDIT";

    private PrescriptionPushRemoteAuditEventMessage msg;

    @JsonCreator
    public PrescriptionPushRemoteAuditEvent(@JsonProperty("msg") PrescriptionPushRemoteAuditEventMessage msg) {
        this.msg = msg;
    }

    @Override
    public String getTag() {
        return "";
    }
}
