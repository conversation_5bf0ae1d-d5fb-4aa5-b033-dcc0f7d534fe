package com.xyy.saas.inquiry.ding;

import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import com.alibaba.fastjson.JSON;
import com.google.common.collect.Lists;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executors;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.ThreadPoolExecutor.DiscardPolicy;
import java.util.concurrent.TimeUnit;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * 微信消息发送工具类
 */
@Component
@Slf4j
public class DingService {

    // 使用小容量线程池，避免阻塞
    private static final ThreadPoolExecutor THREAD_POOL_EXECUTOR = new ThreadPoolExecutor(
        2,
        4,
        10,
        TimeUnit.SECONDS,
        new LinkedBlockingQueue<>(200),
        Executors.defaultThreadFactory(),
        new DiscardPolicy() // 直接抛掉最新消息
    );

    // 问诊业务默认企业微信群机器人URL
    @Value("${inquiry.wechat.bot.webhook.url:https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=78afd7b7-fc55-4405-8637-a82ba99ca51c}")
    private String defaultWebhookUrl;

    // 默认超时时间(毫秒)
    @Value("${inquiry.wechat.bot.timeout:5000}")
    private int defaultTimeout;

    /**
     * /** 发送Markdown消息
     * <p>
     * 简单使用样例 dingService.send(Markdown.title("问诊派单") .add("问诊单", "100001") .add("门店", "xxx测试太阳大药房") .add("医院", "武汉xxx医院") .add("科室", "中西医结合科") .add("详情", "问诊单找不到医生"));
     *
     * @param markdown   Markdown
     * @param webhookUrl 可选的企业微信群机器人URL
     */
    public void send(Markdown markdown, String... webhookUrl) {
        String url = webhookUrl.length > 0 ? webhookUrl[0] : defaultWebhookUrl;
        if (StringUtils.isBlank(url)) {
            log.error("Webhook URL is not configured");
            return;
        }
        post(markdown.build(), url);
    }

    /**
     * /** 发送Text消息
     *
     * @param text       文本消息
     * @param webhookUrl 可选的企业微信群机器人URL
     */
    public void send(String text, String... webhookUrl) {
        String url = webhookUrl.length > 0 ? webhookUrl[0] : defaultWebhookUrl;
        if (StringUtils.isBlank(url)) {
            log.error("Webhook URL is not configured");
            return;
        }
        post(new InnerMsgBody(text, "text"), url);
    }

    /**
     * 内部发送方法
     */
    private void post(InnerMsgBody msgBody, String url) {
        CompletableFuture.runAsync(() -> {
            try {
                log.debug("Sending Wechat message to URL: {}", url);

                HttpResponse response = HttpRequest.post(url)
                    .header("Content-Type", "application/json; charset=utf-8")
                    .timeout(defaultTimeout)
                    .setConnectionTimeout(defaultTimeout)
                    .body(JSON.toJSONString(msgBody))
                    .execute();

                log.debug("Wechat message sent, response: {}", JSON.toJSONString(response));
            } catch (Exception e) {
                log.error("Failed to send Wechat message to URL: {}", url, e);
            }
        }, THREAD_POOL_EXECUTOR);
    }

    /**
     * Markdown消息构建器 Markdown
     */
    public static class Markdown {

        private final String title;
        private final LinkedHashMap<String, String> contentMap = new LinkedHashMap<>();
        private DingMessageColor titleColor;
        private boolean includeTimestamp = true;

        public Markdown(String title, DingMessageColor... titleColor) {
            this.title = title;
            this.titleColor = titleColor.length > 0 ? titleColor[0] : DingMessageColor.WARNING;
        }

        public static Markdown title(String title, DingMessageColor... titleColor) {
            return new Markdown(title, titleColor);
        }

        /**
         * 添加内容项
         */
        public Markdown add(String key, Object value) {
            contentMap.put(key, value != null ? value.toString() : "");
            return this;
        }

        /**
         * 是否包含时间戳
         */
        public Markdown withTimestamp(boolean include) {
            this.includeTimestamp = include;
            return this;
        }

        /**
         * 构建消息内容
         */
        public InnerMsgBody build() {
            List<WechatMarkdownMsgDto> msgs = Lists.newArrayList();

            // 添加标题
            String titleText = "[" + title + "]";
            WechatMarkdownMsgDto titleMsg = WechatMarkdownMsgDto.builder()
                .content(titleText)
                .bold(true)
                .lineEnd(true)
                .color(titleColor.getColorCode())
                .build();

            msgs.add(titleMsg);

            // 添加时间戳
            if (includeTimestamp) {
                contentMap.put("提醒时间", DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now()));
            }

            // 添加内容项
            contentMap.forEach((k, v) -> {
                msgs.add(WechatMarkdownMsgDto.builder()
                    .content(k + ": ")
                    .build());

                msgs.add(WechatMarkdownMsgDto.builder()
                    .lineEnd(true)
                    .content(v)
                    .color(DingMessageColor.COMMENT.getColorCode())
                    .build());
            });

            return new InnerMsgBody(handleMarkdown(msgs), "markdown");
        }

        /**
         * 处理Markdown格式
         */
        private String handleMarkdown(List<WechatMarkdownMsgDto> msgs) {
            StringBuilder builder = new StringBuilder();

            msgs.forEach(msg -> {
                if (StringUtils.isBlank(msg.getContent())) {
                    return;
                }

                StringBuilder contentBuilder = new StringBuilder(msg.getContent());

                // 处理链接
                if (StringUtils.isNotBlank(msg.getLinkAddress())) {
                    contentBuilder.insert(0, "[").append("](").append(msg.getLinkAddress()).append(")");
                }

                // 处理加粗
                if (msg.isBold()) {
                    contentBuilder.insert(0, "**").append("**");
                }

                // 处理颜色
                if (DingMessageColor.colorCodes().contains(msg.getColor())) {
                    contentBuilder.insert(0, "<font color=\"" + msg.getColor() + "\">")
                        .append("</font>");
                }

                // 处理标题级别
                if (msg.getTitleLevel() != null && msg.getTitleLevel() > 0 && msg.getTitleLevel() <= 6) {
                    contentBuilder.insert(0, " ");
                    for (int i = 0; i < msg.getTitleLevel(); i++) {
                        contentBuilder.insert(0, "#");
                    }
                }

                // 处理换行
                if (msg.isLineEnd()) {
                    contentBuilder.append("\n");
                }

                builder.append(contentBuilder);
            });

            return builder.toString();
        }
    }

    // 内部消息体
    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    private static class InnerMsgBody {

        /**
         * text消息体
         */
        private Map<String, String> text;
        /**
         * markdown消息体
         */
        private Map<String, String> markdown;

        private String msgtype;

        public InnerMsgBody(String content, String msgtype) {
            this.msgtype = msgtype;
            if (StringUtils.equals(msgtype, "text")) {
                this.text = Map.of("content", content);
            }
            if (StringUtils.equals(msgtype, "markdown")) {
                this.markdown = Map.of("content", content);
            }
        }
    }

    // Markdown消息DTO
    @Data
    @Builder
    public static class WechatMarkdownMsgDto {

        private String content;
        private String linkAddress;
        private boolean bold;
        private String color;
        private Integer titleLevel = 0;
        private boolean lineEnd;
    }
}
