package com.xyy.saas.inquiry.enums.signature;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import java.util.Arrays;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 签章seal取值类型
 *
 * @Author:chenxia<PERSON>i
 * @Date:2024/10/18 11:38
 */
@Getter
@RequiredArgsConstructor
public enum SignatureSealValueTypeEnum implements IntArrayValuable {

    HOS_PHA_FREE_SIGN(1, "医院药师免签审核"),

    PLAT_PHA_SIGN(2, "平台药师审核"),

    PLAT_PHA_FREE_SIGN(3, "平台药师免签"),
    ;

    private final Integer code;

    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(SignatureSealValueTypeEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }


}
