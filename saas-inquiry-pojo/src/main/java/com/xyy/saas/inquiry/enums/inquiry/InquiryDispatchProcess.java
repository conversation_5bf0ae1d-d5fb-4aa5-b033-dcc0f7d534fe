package com.xyy.saas.inquiry.enums.inquiry;

import lombok.Getter;

/**
 * @Desc 问诊单调度流转节点
 * <AUTHOR>
 */
@Getter
public enum InquiryDispatchProcess {
    PRE_CHECK_PROCESS("inquiryPreCheck", "inquiryAssignHospital", "问诊单前置校验"),
    ASSIGN_HOSPITAL_PROCESS("inquiryAssignHospital", "inquiryAssignDept", "选定问诊医院"),
    ASSIGN_DEPT_PROCESS("inquiryAssignDept", "inquiryAssignMedicalRegistration", "选定问诊科室"),
    // 问诊登记预约挂号单(对接三方)
    ASSIGN_MEDICAL_REGISTRATION("inquiryAssignMedicalRegistration", "inquiryAssignType", "问诊登记预约挂号单(三方)"),

    ASSIGN_TYPE_PROCESS("inquiryAssignType", "inquiryAssignReceptionArea", "选定问诊开方类型"),
    ASSIGN_RECEPTION_AREA_PROCESS("inquiryAssignReceptionArea", "", "将问诊单推入接诊大厅");

    private String node;
    private String next;
    private String desc;

    InquiryDispatchProcess(String node, String next, String desc) {
        this.node = node;
        this.next = next;
        this.desc = desc;
    }
}
