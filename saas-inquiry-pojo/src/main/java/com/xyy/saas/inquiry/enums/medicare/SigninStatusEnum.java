package com.xyy.saas.inquiry.enums.medicare;

import lombok.Getter;

/**
 * @Author: xucao
 * @DateTime: 2025/7/11 11:45
 * @Description: 医保签到状态 1-已签到，2-已签退
 **/
@Getter
public enum SigninStatusEnum {
    SIGNIN(1, "已签到"),
    SIGNOUT(2, "已签退");

    private final Integer code;
    private final String desc;

    SigninStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
