package com.xyy.saas.inquiry.mq.inquiry.dto;

import com.xyy.saas.inquiry.pojo.HospitalDeptDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: xucao
 * @Date: 2024/12/24 15:05
 * @Description: 问诊单创建事件消息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryCreateMessage implements Serializable {
    /**
     * 问诊单号
     */
    private String inquiryPref;

    /**
     * 可接诊医院科室信息
     */
    private HospitalDeptDto hospitalDeptDto;
}
