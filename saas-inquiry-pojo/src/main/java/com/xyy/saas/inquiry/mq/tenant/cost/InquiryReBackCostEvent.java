package com.xyy.saas.inquiry.mq.tenant.cost;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2024/12/31 15:21
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class InquiryReBackCostEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "INQUIRY_RE_BACK_COST";

    private TenantChangeCostDto msg;

    @JsonCreator
    public InquiryReBackCostEvent(@JsonProperty("msg") TenantChangeCostDto msg) {
        this.msg = msg;
    }

    @Override
    public String getTag() {
        return "";
    }
}
