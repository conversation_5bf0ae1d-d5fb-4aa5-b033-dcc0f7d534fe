package com.xyy.saas.inquiry.enums.dict;

import cn.hutool.core.util.ObjUtil;
import lombok.Getter;

/**
 * 字典配对状态
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/19 13:40
 */
@Getter
public enum DictMatchEnum {

    UN_MATCH(0, "未配对"),

    MATCH(1, "已配对"),

    UNABLE_MATCH(2, "无法配对"),
    ;

    private final Integer code;

    private final String desc;


    DictMatchEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static boolean isMatch(Integer status) {
        return ObjUtil.equal(MATCH.code, status);
    }

    public static boolean isUnMatch(Integer status) {
        return ObjUtil.equal(UN_MATCH.code, status);
    }

    public static boolean isUnableMatch(Integer status) {
        return ObjUtil.equal(UNABLE_MATCH.code, status);
    }

}
