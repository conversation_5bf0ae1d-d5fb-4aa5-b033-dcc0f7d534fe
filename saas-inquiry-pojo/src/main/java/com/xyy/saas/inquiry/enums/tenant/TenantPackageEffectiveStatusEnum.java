package com.xyy.saas.inquiry.enums.tenant;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * @Desc 门店套餐生效状态
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum TenantPackageEffectiveStatusEnum implements IntArrayValuable {


    EFFECT(0, "生效中", "有效期内,有额度"),

    UN_EFFECT(1, "待生效", "还未到有效期,或者有效期内有额度,暂停中"),

    USED_UP(2, "已用尽", "有效期内 额度用完"),

    EXPIRED(3, "已过期", "有效期外"),

    INVALID(4, "已失效", ""),

    ;

    private final int code;

    private final String desc;

    private final String remark;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(TenantPackageEffectiveStatusEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static TenantPackageEffectiveStatusEnum fromStatusCode(int statusCode) {
        return Arrays.stream(values())
            .filter(value -> value.getCode() == statusCode)
            .findFirst()
            .orElse(EFFECT);
    }


    /**
     * 获取套餐包实时有效状态
     *
     * @param startDateTime 服务开始时间
     * @param endDateTime   服务结束时间
     * @param packageStatus 套餐包状态
     * @param surplusCosts  剩余额度
     * @return 有效状态
     */
    public static Integer getEffectiveStatus(LocalDateTime startDateTime, LocalDateTime endDateTime, TenantPackageRelationStatusEnum packageStatus, List<InquiryPackageItem> surplusCosts) {
        if (startDateTime == null || endDateTime == null || packageStatus == null
            || Objects.equals(packageStatus, TenantPackageRelationStatusEnum.REFUND)
            || Objects.equals(packageStatus, TenantPackageRelationStatusEnum.ABANDONED)
            || LocalDateTime.now().isAfter(endDateTime) || CollUtil.isEmpty(surplusCosts)) {
            return INVALID.code;
        }
        // 生效中 有效期内
        if (LocalDateTime.now().isAfter(startDateTime) && LocalDateTime.now().isBefore(endDateTime)) {
            // 修复：使用兼容的额度检查方法，同时检查count和unlimited字段
            if (surplusCosts.stream().anyMatch(InquiryPackageItem::hasAvailableQuota)) {
                return Objects.equals(packageStatus, TenantPackageRelationStatusEnum.NORMAL) ? EFFECT.code : UN_EFFECT.code;
            }
            return USED_UP.code;
        }
        // 未到有效期 且有额度
        if (LocalDateTime.now().isBefore(startDateTime)) {
            // 修复：使用兼容的额度检查方法，同时检查count和unlimited字段
            if (surplusCosts.stream().anyMatch(InquiryPackageItem::hasAvailableQuota)) {
                return UN_EFFECT.code;
            }
            return USED_UP.code;
        }
        // 过期 : 结束时间比当前还早
        if (LocalDateTime.now().isAfter(endDateTime)) {
            return EXPIRED.code;
        }
        return UN_EFFECT.code;
    }

    /**
     * 获取套餐包实时有效状态
     *
     * @param startDateTime 服务开始时间
     * @param endDateTime   服务结束时间
     * @param packageStatus 套餐包状态
     * @param surplusCosts  剩余额度
     * @return 有效状态
     */
    public static Integer getEffectiveStatus(Integer bizType, LocalDateTime startDateTime, LocalDateTime endDateTime, TenantPackageRelationStatusEnum packageStatus, List<InquiryPackageItem> surplusCosts) {
        if (startDateTime == null || endDateTime == null || packageStatus == null
            || Objects.equals(packageStatus, TenantPackageRelationStatusEnum.REFUND)
            || Objects.equals(packageStatus, TenantPackageRelationStatusEnum.ABANDONED)
            || LocalDateTime.now().isAfter(endDateTime) || (CollUtil.isEmpty(surplusCosts) && BizTypeEnum.HYWZ.getCode() == bizType)) {
            return INVALID.code;
        }

        // 生效中 有效期内
        if (LocalDateTime.now().isAfter(startDateTime) && LocalDateTime.now().isBefore(endDateTime)) {
            // 有额度
            if ((BizTypeEnum.ZHL.getCode() == bizType) || surplusCosts.stream().anyMatch(InquiryPackageItem::hasAvailableQuota)) {
                return Objects.equals(packageStatus, TenantPackageRelationStatusEnum.NORMAL) ? EFFECT.code : UN_EFFECT.code;
            }
            return USED_UP.code;
        }
        // 未到有效期 且有额度
        if (LocalDateTime.now().isBefore(startDateTime)) {
            if ((BizTypeEnum.ZHL.getCode() == bizType) || surplusCosts.stream().anyMatch(InquiryPackageItem::hasAvailableQuota)) {
                return UN_EFFECT.code;
            }
            return USED_UP.code;
        }
        // 过期 : 结束时间比当前还早
        if (LocalDateTime.now().isAfter(endDateTime)) {
            return EXPIRED.code;
        }
        return UN_EFFECT.code;
    }

}