package com.xyy.saas.inquiry.mq.prescription;


import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import org.springframework.stereotype.Component;


/**
 * 医生签章完成后置生产者
 */
@Component
@EventBusProducer(
    topic = DoctorSignaturePostPassingEvent.TOPIC
)
public class DoctorSignaturePostPassingProducer extends EventBusRocketMQTemplate {

}
