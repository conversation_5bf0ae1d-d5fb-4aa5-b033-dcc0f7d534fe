package com.xyy.saas.inquiry.pojo.migration;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2025/06/05 16:09
 */
@Data
@Accessors(chain = true)
public class MigrationDrugStoreRespDto implements java.io.Serializable {

    /**
     * 门店ID
     */
    private Long tenantId;

    /**
     * 药店机构标识
     */
    private String organSign;

    /**
     * 荷叶id
     */
    private String hyid;

    /**
     * 智慧脸药店标识
     */
    private String smartfaceOrganSign;

    /**
     * 药店名称
     */
    private String drugstoreName;

    /**
     * 药店负责人姓名
     */
    private String managerName;

    /**
     * 药店联系电话
     */
    private String contactPhone;

    /**
     * 省
     */
    private String province;

    /**
     * 省编码
     */
    private String provinceCode;

    /**
     * 市
     */
    private String city;

    /**
     * 市编码
     */
    private String cityCode;

    /**
     * 区
     */
    private String area;

    /**
     * 区编码
     */
    private String areaCode;

    /**
     * 药店地址
     */
    private String address;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 营业执照名称
     */
    private String businessLicenseName;

    /**
     * 营业执照号
     */
    private String businessLicenseNumber;

    /**
     * 营业执照
     */
    private String businessLicenseImg;


    /**
     * 药品经营许可证注册 编号
     */
    private String storeNo;

    /**
     * 药品经营许可证注册 注册日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pharmaceuticalTradingLicenseRegistryTime;

    /**
     * 药品经营许可证注册 有效期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pharmaceuticalTradingLicenseValidityPeriod;

    /**
     * 药品经营许可证 - 图片
     */
    private String pharmaceuticalTradingLicenseImg;

    /**
     * 药品经营质量管理规范认证证书
     */
    private String qualityManagementLicenseImg;

    /**
     * 药品经营质量管理规范认证号
     */
    private String qualityManagementLicenseNumber;

    /**
     * 灵芝管理合同
     */
    private String lzManagementLicense;

    /**
     * 备案合同及保密协议
     */
    private String recordContractsAndConfidentialityAgreements;

    /**
     * 远程审方备案件
     */
    private String remoteAuditArchiveDocuments;


    /**
     * 日期类型 0 、未设置  1 年月日-时分秒；2、年月日
     */
    private Integer dateType;

    /**
     * 远程处方服务 1处方 0处方+审方 药店审方:1 平台审方:0
     *
     * @see com.xyy.saas.remote.web.core.enums.ServerTypeEnum
     */
    private Byte serverType;

    /**
     * 荷叶问诊服务配置状态： 0 开启 1 关闭
     */
    private Integer hyInquiryConfigStatus;

    /**
     * 灵芝问诊服务状态 1开 2关
     */
    private Integer status;

    /**
     * 西药是否带出上次药品(0否 1是)
     */
    private Integer westernMedicineBring;

    /**
     * 中药是否带出上次药品(0否 1是)
     */
    private Integer chineseMedicineBring;

    /**
     * 所属互联网医院
     */
    private Integer hospitalCode;

    /**
     * 医院名称
     */
    private String hospitalName;
}
