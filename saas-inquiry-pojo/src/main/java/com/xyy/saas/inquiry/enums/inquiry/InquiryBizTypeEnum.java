package com.xyy.saas.inquiry.enums.inquiry;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * @Desc 问诊商业类型
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/8/29 下午7:51
 */
@Getter
@RequiredArgsConstructor
public enum InquiryBizTypeEnum implements IntArrayValuable {

    //药店问诊
    DRUGSTORE_INQUIRY(1, "药店问诊"),
    //远程审方(带方审方)
    REMOTE_INQUIRY(2, "远程审方(带方审方)"),
    ;

    private final int code;

    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(InquiryBizTypeEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 枚举值code
     * @return 枚举值
     */
    public static InquiryBizTypeEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("Invalid InquiryBizTypeEnum code: " + code));
    }

}