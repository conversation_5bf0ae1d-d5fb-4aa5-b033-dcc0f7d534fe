package com.xyy.saas.inquiry.pojo.medicare;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 医保签到扩展信息 DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "医保签到扩展信息")
public class MedicareSignInDto implements Serializable {

    @Schema(description = "签到号")
    private String signNo;

    @Schema(description = "两定机构编码")
    private String medicareInstitutionCode;

    @Schema(description = "操作员编码")
    private String operatorCode;

    @Schema(description = "操作员姓名")
    private String operatorName;

    @Schema(description = "签到状态：1-已签到，0-未签到")
    private Integer signinStatus;

    @Schema(description = "签到时间")
    private LocalDateTime signinTime;

    @Schema(description = "就医地编号")
    private String tenantAreaNo;

} 