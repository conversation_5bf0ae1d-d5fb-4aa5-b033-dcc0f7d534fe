package com.xyy.saas.inquiry.util;

import org.apache.commons.lang3.StringUtils;
import java.util.Random;

/**
 * @Author: xucao
 * @Date: 2024/12/23 14:54
 * @Description: 统计、计算工具类
 */
public class MathUtil {

    private static final Random RANDOM = new Random();

    /**
     * 根据给定的比例生成一个 true 或 false 的结果
     *
     * @param probability 0-100 之间的比例数字
     * @return 根据比例生成的 true 或 false
     */
    public static boolean generateResult(int probability) {
        if (probability < 0 || probability > 100) {
            throw new IllegalArgumentException("概率必须在 0 到 100 之间");
        }
        return RANDOM.nextInt(100) < probability;
    }

    /**
     * 获取一个区间随机数
     *
     * @param min 最小值
     * @param max 最大值
     * @return 随机数
     */
    public static int getRandomNumber(int min, int max) {
        if (min >= max) {
            throw new IllegalArgumentException("最小值必须小于最大值");
        }
        return RANDOM.nextInt(max - min + 1) + min;
    }

    /**
     * 格式化数字，如果数字为空，则返回默认值
     *
     * @param numStr     数字字符串
     * @param defaultNum 默认值
     * @return 格式化后的数字
     */
    public static int formatNumberWithDefault(String numStr, int defaultNum) {
        if (StringUtils.isBlank(numStr)) {
            return defaultNum;
        }
        try {
            return Integer.parseInt(numStr);
        } catch (Exception e) {
            return defaultNum;
        }
    }

    public static int randomNextInt(int size) {
        return RANDOM.nextInt(size);
    }
}
