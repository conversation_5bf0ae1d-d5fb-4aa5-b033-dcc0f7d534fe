package com.xyy.saas.inquiry.pojo.migration;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2025/06/05 16:09
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class MigrationPackageRespDto implements java.io.Serializable {

    /**
     * 服务包ID
     */
    private Long id;

    /**
     * 医院编码
     */
    private String hospitalPref;

    /**
     * 门店id
     */
    private Long tenantId;

    /**
     * 服务包编号
     */
    private String guid;

    /**
     * 门店机构号
     */
    private String organSign;

    /**
     * 剩余条数
     */
    private Integer surplusLimit;

    /**
     * 限制条数
     */
    private Integer countLimit;

    /**
     * 是否无限,0无限，1有限
     */
    private Integer isInfinite;

    /**
     * 限制规则
     */
    private List<CountLimitRuleDto> countLimitRule;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date serverStart;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date serverEnd;

    /**
     * 销售产品类型，0软件版，1软件+硬件版
     */
    private Byte productType;

    /**
     * 签约渠道，0智慧脸，1宜块钱，2EC，3智鹿
     */
    private Byte signChannel;

    /**
     * 原签约来源：0App,1-PC,2-公众号，3-小程序 v5.1改造支付来源 0灵芝APP 1灵芝PC 2公众号 3小程序 4智慧脸PC 5海报 6运营线下提单
     */
    private Integer signSource;

    /**
     * 订单生效状态，0未生效,1生效中,2已过期,3已用尽 4已退款  5已作废
     */
    private Byte validStatus;

    /**
     * 支付中心订单号-查支付状态
     */
    private String thirdPayorderNo;

    /**
     * 套餐sku-第三方商品组
     */
    private Integer thirdPackageId;

    /**
     * 是否使用套餐：0否，1是，
     */
    private Byte isCustomize;

    /**
     * 收款方式：0上线，1线下
     */
    private Byte receivablesType;

    /**
     * 签约日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date signTime;

    /**
     * 签约人
     */
    private String signUser;

    /**
     * 签约人工号
     */
    private String signUserno;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 套餐包名
     */
    private String packageName;

    /**
     * 问诊类型  0 视频+图文，1视频，2图文
     **/
    private Byte inquiryType;

    /**
     * 服务包类型
     *
     * @see com.xyy.saas.remote.web.core.enums.PackageTypeEnum
     */
    private String packageType;

    /**
     * 支付时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date payTime;

    /**
     * 支付渠道，40 微信聚合、50 支付宝聚合
     */
    private String payChannel;

    private String promoteOrganSign;

    /**
     * 支付状态  10 待支付；20 支付中； 30 已支付；40 退款中；50 退款成功；60 支付失败； 70 退款失败
     */
    private Integer payStatus;

    /**
     * 绑定门店状态 1:已绑定 0:未绑定
     */
    private Integer bindDrugstoreStatus;

    /**
     * 邀请码
     */
    private String inviteCode;

    /**
     * 客户端类型
     *
     * @see com.xyy.saas.remote.web.core.enums.ClientTypeEnum
     */
    private Integer clientType;

    /**
     * 提单人
     */
    private String createUser;


    /**
     * 实收金额
     */
    private BigDecimal packageActualAmount;
    /**
     * 收款账户 0-微信聚合（海南） 1-支付宝聚合（海南） 2-对公打款（海南） 3-对公打款（成都） 4-微信聚合（成都） 5-武汉宜贰叁（五和） {@link PackagePayorderAccountEnum}
     */
    private String packageCollectAccount;
    /**
     * 付款流水号
     */
    private String packagePayorderNo;
    /**
     * 备注
     */
    private String remark;

    /**
     * 套餐价格
     */
    private BigDecimal packagePrice;

    /**
     * 退款时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date refundTime;
    /**
     * 退款原因
     */
    private String refundReason;

    /**
     * 退款类型 REAL_REFUND(0), PACKAGE_CHANGE(1);
     */
    private String refundType;

    /**
     * 退款金额
     */
    private BigDecimal refundAmount;

    /**
     * 作废时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date obsoleteTime;
    /**
     * 作废原因
     */
    private String obsoleteReason;


}
