package com.xyy.saas.inquiry.enums.doctor;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 医生接诊类型枚举
 */
@Getter
@RequiredArgsConstructor
public enum DoctorInquiryTypeEnum {
    MANUAL_INQUIRY(0, "手动开方","manual"),
    AUTO_INQUIRY(1, "自动开方","auto");

    private final int code;
    private final String desc;
    private final String prefix;

    public static DoctorInquiryTypeEnum fromCode(int code) {
        for (DoctorInquiryTypeEnum item : values()) {
            if (item.getCode() == code) {
                return item;
            }
        }
        return null;
    }

    public static String getPrefixByCode(int code) {
        for (DoctorInquiryTypeEnum item : values()) {
            if (item.getCode() == code) {
                return item.getPrefix();
            }
        }
        return null;
    }
}
