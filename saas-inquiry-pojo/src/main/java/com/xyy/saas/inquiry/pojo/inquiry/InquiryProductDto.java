package com.xyy.saas.inquiry.pojo.inquiry;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.*;
import lombok.experimental.Accessors;
import org.hibernate.validator.constraints.Length;

import java.io.Serializable;
import java.util.List;

/**
 * @Author:chenxiaoyi
 * @Date:2024/11/28 15:41
 */
@Data
@Schema(description = "问诊药品Dto")
@Valid
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class InquiryProductDto implements Serializable {

    /**
     * 问诊药品
     */
    @Schema(description = "问诊药品集合")
    private List<InquiryProductDetailDto> inquiryProductInfos;

    // 中药 *******************
    /**
     * 总剂量  eg:10 副
     */
    @Schema(description = "中药总剂量  eg:10 副")
    @Length(max = 1000, message = "总剂量最大长度1000")
    private String tcmTotalDosage;

    /**
     * 每x日 eg: 每tcmDaily日tcmDailyDosage剂
     */
    @Schema(description = "每x日  eg:2")
    @Length(max = 1000, message = "总剂量最大长度1000")
    private String tcmDaily;

    /**
     * 每日剂量 eg:每日3 剂
     */
    @Schema(description = "中药每x日剂量 eg:每日3 剂")
    @Length(max = 1000, message = "每x日剂量最大长度1000")
    private String tcmDailyDosage;

    /**
     * 每剂几次用药 eg:每剂2 次用药
     */
    @Schema(description = "中药每剂几次用药 eg:每剂2 次用药")
    @Length(max = 1000, message = "每剂几次用药最大长度1000")
    private String tcmUsage;

    /**
     * 加工方法 eg: 打粉冲服
     */
    @Schema(description = "中药加工方法 eg: 打粉冲服")
    @Length(max = 1000, message = "加工方法最大长度1000")
    private String tcmProcessingMethod;

    /**
     * 用法 eg: 温服
     */
    @Schema(description = "中药用法 eg: 温服")
    @Length(max = 1000, message = "用法最大长度1000")
    private String tcmDirections;


}
