package com.xyy.saas.inquiry.enums.inquiry;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;
import java.util.Objects;

/**
 * @Desc 处方审核类型
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum PrescriptionAuditTypeEnum implements IntArrayValuable {

    DRUGSTORE(1, "药店审方"),

    OFFLINE(2, "线下审方"),

    PLATFORM(3, "平台审方"),

    // 医院药师审方

    ;

    private final int statusCode;

    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(PrescriptionAuditTypeEnum::getStatusCode).toArray();

    /**
     * 获取审核类型 如果是平台审方直接返回 否则判断类型是否为平台,为平台则走药店,否则走自有类型
     *
     * @param inquiryPresAuditType 当前审方类型
     * @param isPlatformReview     是否平台审方
     * @return
     */
    public static PrescriptionAuditTypeEnum getAuditType(Integer inquiryPresAuditType, boolean isPlatformReview) {
        if (isPlatformReview) {
            return PrescriptionAuditTypeEnum.PLATFORM;
        }
        return Objects.equals(PrescriptionAuditTypeEnum.fromStatusCode(inquiryPresAuditType), PrescriptionAuditTypeEnum.PLATFORM)
            ? PrescriptionAuditTypeEnum.DRUGSTORE : PrescriptionAuditTypeEnum.fromStatusCode(inquiryPresAuditType);
    }

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static PrescriptionAuditTypeEnum fromStatusCode(Integer statusCode) {
        return Arrays.stream(values())
            .filter(value -> value.getStatusCode() == statusCode)
            .findFirst()
            .orElse(DRUGSTORE);
    }

}