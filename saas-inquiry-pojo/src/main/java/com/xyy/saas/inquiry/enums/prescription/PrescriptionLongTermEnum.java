package com.xyy.saas.inquiry.enums.prescription;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 长处方标识
 */
@Getter
@RequiredArgsConstructor
public enum PrescriptionLongTermEnum implements IntArrayValuable {

    N(0, "否"),
    Y(1, "是"),

    ;

    // 枚举类的私有成员变量
    private final int code;
    private final String description;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(PrescriptionLongTermEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    // 根据整数值获取枚举实例的静态方法
    public static PrescriptionLongTermEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElse(N);
    }
}
