package com.xyy.saas.inquiry.enums.inquiry;

public enum UnableAutoReasonEnum {
    /**
     * 预购药品无用法用量
     */
    NO_DOSAGE_INSTRUCTIONS(1, "预购药品无用法用量"),

    /**
     * 无自动开方医生
     */
    NO_PRESCRIBING_DOCTOR(2, "无自动开方医生"),

    /**
     * 门店未开通自动开方
     */
    STORE_NOT_ENABLED_FOR_AUTO_PRESCRIPTION(3, "门店未开通自动开方"),

    /**
     * 患者存在过敏史
     */
    PATIENT_HAD_ALLERGY(4, "患者存在过敏史"),

    /**
     * 门店或所在区域未开通视频自动开方
     */
    STORE_OR_AREA_NOT_ENABLED_VIDEO_AUTO_INQUIRY(5, "门店或所在区域未开通视频自动开方"),

    /**
     * 根据权重配置未触发视频自动开方
     */
    VIDEO_AUTO_INQUIRY_WEIGHT_NOT_MATCH(6, "根据权重配置未触发视频自动开方"),

    /**
     * 患者存在肝肾功能异常
     */
    PATIENT_HAD_LIVER_KIDNEY_FUNCTION_ABNORMAL(7, "患者存在肝肾功能异常"),

    /**
     * 患者24小时内存在视频自动开方记录
     */
    PATIENT_HAD_24HOURS_VIDEO_AUTO_INQUIRY(8, "患者24小时内存在视频自动开方记录"),

    /**
     * 存在多诊断或诊断跨科室
     */
    MANY_DIAGNOSES_OR_DIAGNOSIS_TRANS_DEPT(9, "存在多诊断或诊断跨科室"),

    /**
     * 患者存在妊娠哺乳期
     */
    PATIENT_HAD_PREGNANCY_LACTATION(10, "患者存在妊娠哺乳期"),

    /**
     * 特定年龄段患者不走自动开方
     */
    SPECIFIC_AGE_RANGE_PATIENT(11, "特定年龄段患者不走自动开方"),

    /**
     * 存在用法用量缺失
     */
    MISSING_DOSAGE_INSTRUCTIONS(12, "存在用法用量缺失"),

    /**
     * 预购药品中存在临时商品
     */
    TEMPORARY_PRODUCT(13, "预购药品中存在临时商品或无药品"),

    /**
     * 区域或门店配置强制走真人问诊
     */
    AREA_OR_STORE_FORCE_TO_REAL(14, "区域或门店配置强制走真人问诊");


    private final int code; // 原因代码
    private final String desc; // 原因描述

    // 构造函数，用于初始化枚举项的字段
    UnableAutoReasonEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    /**
     * 获取枚举项对应的原因代码
     *
     * @return 原因代码
     */
    public int getCode() {
        return code;
    }

    /**
     * 获取枚举项对应的原因描述
     *
     * @return 原因描述
     */
    public String getDesc() {
        return desc;
    }

    /**
     * 根据原因代码获取对应的枚举类型
     *
     * @param code 原因代码
     * @return 对应的枚举类型，如果不存在则返回 null
     */
    public static UnableAutoReasonEnum fromCode(int code) {
        for (UnableAutoReasonEnum reason : values()) {
            if (reason.getCode() == code) {
                return reason;
            }
        }
        return null;
    }
}
