package com.xyy.saas.inquiry.mq.prescription.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Author: cxy
 * @Description: 处方Mq消息
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PrescriptionMqCommonMessage implements Serializable {

    /**
     * 处方单号
     */
    private String prescriptionPref;

    /**
     * 处方笺图片url
     */
    private String prescriptionImgUrl;

    /**
     * 处方笺PDFurl
     */
    private String prescriptionPdfUrl;

    /**
     * {@link com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum}
     */
    @Schema(description = "当前审方人类型 1-医生,2-药店,3-平台,4-医院", example = "2")
    private Integer auditorType;
}
