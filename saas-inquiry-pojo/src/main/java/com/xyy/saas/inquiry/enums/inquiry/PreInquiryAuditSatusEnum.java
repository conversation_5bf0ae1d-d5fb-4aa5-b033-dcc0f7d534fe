package com.xyy.saas.inquiry.enums.inquiry;

import lombok.Getter;

/**
 * @Author: xucao
 * @DateTime: 2025/4/18 17:30
 * @Description: 预问诊审核状态枚举 0待审核 1已审核
 * @Version: 1.0
 **/
@Getter
public enum PreInquiryAuditSatusEnum {
    PENDING_AUDIT(0, "待审核"),
    AUDITED(1, "已审核");
    private PreInquiryAuditSatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
    private final Integer code;
    private final String desc;
    public static PreInquiryAuditSatusEnum fromCode(Integer code) {
        for (PreInquiryAuditSatusEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
