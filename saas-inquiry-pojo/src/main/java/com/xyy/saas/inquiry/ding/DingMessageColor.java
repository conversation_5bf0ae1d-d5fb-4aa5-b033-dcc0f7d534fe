package com.xyy.saas.inquiry.ding;

import java.util.Arrays;
import java.util.List;
import lombok.Getter;

// 消息颜色映射
public enum DingMessageColor {
    WARNING("warning", "黄色"),
    INFO("info", "绿色"),
    COMMENT("comment", "灰色");

    @Getter
    private final String colorCode;
    private final String description;

    DingMessageColor(String colorCode, String description) {
        this.colorCode = colorCode;
        this.description = description;
    }

    public static List<String> colorCodes() {
        return Arrays.asList(DingMessageColor.values()).stream()
            .map(DingMessageColor::getColorCode)
            .toList();
    }

}