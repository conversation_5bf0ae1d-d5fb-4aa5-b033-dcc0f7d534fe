package com.xyy.saas.inquiry.enums.tenant;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import java.util.Arrays;

/**
 * @Author: lgd
 * @Date: 2025/06/30 13:47
 * @Description:
 */
@Getter
@RequiredArgsConstructor
public enum TenantPackageEnum implements IntArrayValuable {

    SINGLE_STORE_PACKAGE(1, "单体门店"),
    CHAIN_HEADQUARTERS_PACKAGE(3, "连锁总部");

    private final int code;
    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(TenantPackageEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static TenantPackageEnum fromCode(Integer code) {
        return Arrays.stream(values())
            .filter(value -> value.getCode() == code)
            .findFirst()
            .orElse(null);
    }

}
