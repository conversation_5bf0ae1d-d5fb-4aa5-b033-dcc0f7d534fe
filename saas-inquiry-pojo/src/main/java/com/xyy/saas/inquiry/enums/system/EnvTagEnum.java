package com.xyy.saas.inquiry.enums.system;

import lombok.Getter;

/**
 * @Author:chen<PERSON><PERSON>i
 * @Date:2024/12/19 13:40
 */
@Getter
public enum EnvTagEnum {

    PROD("prod", "真实数据"),

    TEST("test", "测试数据"),

    SHOW("show", "线上演示数据"),

    ;

    private final String env;

    private final String desc;


    EnvTagEnum(String env, String desc) {
        this.env = env;
        this.desc = desc;
    }

    public static EnvTagEnum getByEnv(String env) {
        for (EnvTagEnum e : values()) {
            if (e.env.equals(env)) {
                return e;
            }
        }
        return EnvTagEnum.PROD;
    }
}
