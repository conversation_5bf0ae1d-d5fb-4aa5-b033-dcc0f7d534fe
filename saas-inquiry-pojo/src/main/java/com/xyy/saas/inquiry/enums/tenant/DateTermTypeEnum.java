package com.xyy.saas.inquiry.enums.tenant;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.Optional;

/**
 * @Desc 套餐期限类型枚举
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum DateTermTypeEnum implements IntArrayValuable {

    YEAR(1, "年") {
        @Override
        public LocalDateTime calculationTermDate(Integer term) {
            if (term == null) {
                return null;
            }
            return LocalDateTime.now().plusYears(term).withHour(23).withMinute(59).withSecond(59);
        }

        @Override
        public LocalDateTime calculationTermDate(LocalDateTime time, Integer term) {
            if (term == null) {
                return null;
            }
            return Optional.ofNullable(time).orElse(LocalDateTime.now()).plusYears(term).withHour(23).withMinute(59).withSecond(59);
        }
    },

    MONTH(2, "月") {
        @Override
        public LocalDateTime calculationTermDate(Integer term) {
            if (term == null) {
                return null;
            }
            return LocalDateTime.now().plusMonths(term).withHour(23).withMinute(59).withSecond(59);
        }

        @Override
        public LocalDateTime calculationTermDate(LocalDateTime time, Integer term) {
            if (term == null) {
                return null;
            }
            return Optional.ofNullable(time).orElse(LocalDateTime.now()).plusMonths(term).withHour(23).withMinute(59).withSecond(59);
        }
    },

    DAY(3, "日") {
        @Override
        public LocalDateTime calculationTermDate(Integer term) {
            if (term == null) {
                return null;
            }
            return LocalDateTime.now().plusDays(term).withHour(23).withMinute(59).withSecond(59);
        }

        @Override
        public LocalDateTime calculationTermDate(LocalDateTime time, Integer term) {
            if (term == null) {
                return null;
            }
            return Optional.ofNullable(time).orElse(LocalDateTime.now()).plusDays(term).withHour(23).withMinute(59).withSecond(59);
        }
    },
    ;

    private final Integer code;


    private final String desc;

    /**
     * 计算当前时间 换算时间单位后的时间
     *
     * @param term 期限value
     * @return
     */
    public abstract LocalDateTime calculationTermDate(Integer term);

    /**
     * 计算从某个时间，换算时间单位后的时间
     *
     * @param time 某个时间
     * @param term 期限value
     * @return
     */
    public abstract LocalDateTime calculationTermDate(LocalDateTime time, Integer term);


    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(DateTermTypeEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 枚举值code
     * @return 枚举值
     */
    public static DateTermTypeEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElse(YEAR);
    }

}
