package com.xyy.saas.inquiry.pojo.forward;

import java.io.Serializable;
import lombok.Data;

/**
 * 旧系统CA认证信息
 */
@Data
public class ForwardPersonInfo implements Serializable {

    /**
     * 自增主键
     */
    private Long id;
    /**
     * 个人用户在应用中的唯一标识
     */
    private String clientUserId;
    /**
     * 法大大平台为该用户在该应用appId范围内分配的唯一标识
     */
    private String openUserId;
    /**
     * 个人用户的法大大帐号，仅限手机号或邮箱
     */
    private String accountName;
    /**
     * 个人用户真实姓名
     */
    private String userName;
    /**
     * 个人手机号
     */
    private String mobile;
    /**
     * 个人银行账户号
     */
    private String bankAccountNo;
    /**
     * 证件类型 ：id_card: 身份证;passport: 护照;hk_macao: 港澳居民来往内地通行证;taiwan: 台湾居民来往大陆通行证
     */
    private String userIdentType;
    /**
     * 证件号。跟证件类型关联
     */
    private String userIdentNo;
    /**
     * 法大大签名印章ID
     */
    private String sealId;
    /**
     * 0-未认证,1-已认证,3已设置签名
     */
    private String userStatus;


}