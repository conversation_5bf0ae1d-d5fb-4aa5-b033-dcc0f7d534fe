package com.xyy.saas.inquiry.mq.tenant.cost;

import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.CostRecordTypeEnum;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 门店变更额度Dto
 *
 * @Author:chenxiaoyi
 * @Date:2024/09/05 13:56
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TenantChangeCostDto implements Serializable {

    /**
     * 门店id
     */
    private Long tenantId;

    /**
     * 额度记录Id
     */
    private Long costId;


    /**
     * 系统业务类型 {@link BizTypeEnum}
     */
    private Integer bizType;

    /**
     * 门店套餐id
     */
    private Long tenantPackageId;

    /**
     * 业务Id
     */
    private String bizId;

    /**
     * 业务Ids
     */
    private List<String> bizIds;

    /**
     * 问诊类型 1图文 2视频 {@link InquiryWayTypeEnum}
     */
    private Integer wayType;

    /**
     * 变更额度
     */
    private Long changeCost;

    /**
     * 记录类型 0初始 {@link CostRecordTypeEnum}
     */
    private Integer recordType;

    /**
     * 记录类型 0初始 {@link CostRecordTypeEnum}
     */
    private Integer reBackRecordType;

    /**
     * 原始数据记录version
     */
    private Long version;

}
