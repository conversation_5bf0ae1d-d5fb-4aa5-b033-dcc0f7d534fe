package com.xyy.saas.inquiry.util;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.ReflectUtil;
import com.xyy.saas.inquiry.annotation.FieldCompare;
import com.xyy.saas.inquiry.model.FieldChangeRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 字段比较工具类
 */
@Slf4j
public class FieldCompareUtil {

    /**
     * 比较两个对象的字段差异
     *
     * @param newObj     新对象
     * @param oldObj     原对象
     * @param ignoreNull 是否忽略null值比较
     * @return 变更记录列表
     */
    public static List<FieldChangeRecord> compare(String group, Object newObj, Object oldObj, boolean ignoreNull) {
        return compare(group, newObj, oldObj, ignoreNull, "");
    }

    /**
     * 比较两个对象的字段差异
     *
     * @param group      指定分组比较
     * @param newObj     新对象
     * @param oldObj     原对象
     * @param ignoreNull 是否忽略null值比较
     * @param parentPath 父级路径(用于嵌套对象)
     * @return 变更记录列表
     */
    private static List<FieldChangeRecord> compare(String group, Object newObj, Object oldObj, boolean ignoreNull, String parentPath) {
        List<FieldChangeRecord> changes = new ArrayList<>();
        if (newObj == null || oldObj == null || group == null) {
            return changes;
        }

        // 获取所有字段(包括父类)
        Field[] fields = ReflectUtil.getFields(newObj.getClass());
        for (Field field : fields) {
            try {
                field.setAccessible(true);
                // 获取字段注解
                FieldCompare annotation = field.getAnnotation(FieldCompare.class);
                if (annotation == null) {
                    continue;
                }

                // 获取比较的字段名
                String compareFieldName = StringUtils.defaultIfEmpty(annotation.compareField(), field.getName());

                // 获取比较字段
                Field compareField = ReflectUtil.getField(oldObj.getClass(), compareFieldName);
                if (compareField == null) {
                    continue;
                }
                compareField.setAccessible(true);

                // 获取字段值
                Object newValue = field.get(newObj);
                Object oldValue = compareField.get(oldObj);

                // 是否忽略null值比较
                boolean nullIgnore = (annotation.ignoreNull() || ignoreNull) && newValue == null;
                // 比较值是否相等
                if (ObjectUtil.equals(newValue, oldValue) || nullIgnore) {
                    continue;
                }

                // 判断数字是否相等 （BigDecimal不比较精度 1.0 == 1.00 ）
                if (newValue instanceof Number && oldValue instanceof Number
                    && NumberUtil.equals((Number) newValue, (Number) oldValue)) {
                    continue;
                }

                // 构建当前字段路径
                String currentPath = parentPath.isEmpty() ? field.getName() : parentPath + "." + field.getName();

                if (annotation.recursive()) {
                    // 递归比较
                    if (newValue != null && oldValue != null && !ObjectUtil.isBasicType(newValue.getClass())) {
                        changes.addAll(compare(group, newValue, oldValue, ignoreNull, currentPath));
                    }
                    continue;
                } else if (!Arrays.asList(annotation.group()).contains(group)) {
                    // 字段不在指定分组内
                    continue;
                }

                changes.add(FieldChangeRecord.builder()
                    .fieldName(field.getName())
                    .description(annotation.description())
                    .oldValue(oldValue)
                    .newValue(newValue)
                    .path(currentPath)
                    .build());
            } catch (Exception e) {
                log.error("Compare field error", e);
            }
        }
        return changes;
    }
} 