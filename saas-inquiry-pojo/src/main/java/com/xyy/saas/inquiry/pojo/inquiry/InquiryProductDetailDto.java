package com.xyy.saas.inquiry.pojo.inquiry;

import com.xyy.saas.inquiry.pojo.ProductBaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2024/11/28 15:41
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "问诊商品Dto(含西药用法用量)")
@Valid
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class InquiryProductDetailDto extends ProductBaseDto {

    /**
     * 用药方法 - 西药
     */
    @Schema(description = "用药方法 - 西药")
    private String directions;
    /**
     * 用药方法 - 西药
     */
    @Schema(description = "用药方法字典Value - 西药")
    private String directionsValue;

    /**
     * 单次剂量 - 西药
     */
    @Schema(description = "单次剂量 - 西药")
    private String singleDose;

    /**
     * 单次剂量单位 - 西药
     */
    @Schema(description = "单次剂量单位 - 西药")
    private String singleUnit;

    /**
     * 单次剂量单位 - 西药
     */
    @Schema(description = "单次剂量单位字典Value - 西药")
    private String singleUnitValue;

    /**
     * 使用频次 - 西药
     */
    @Schema(description = "使用频次 - 西药")
    private String useFrequency;

    /**
     * 使用频次Value - 西药
     */
    @Schema(description = "使用频次Value - 西药")
    private String useFrequencyValue;

    /**
     * 标准库id
     */
    @Schema(description = "标准库id - 西药")
    private String standardId;

}
