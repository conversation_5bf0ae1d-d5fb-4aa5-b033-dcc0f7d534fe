package com.xyy.saas.inquiry.enums.prescription;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import java.util.Arrays;
import java.util.Objects;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 处方划价状态
 */
@Getter
@RequiredArgsConstructor
public enum PrescriptionPricingTypeEnum implements IntArrayValuable {

    DEFAULT(0, "默认初始状态-无特殊要求-划总价"),
    NEED_PRICING(1, "需要划明细价-监管"),

    PRICED_TOTAL(90, "已划总价"),
    PRICED_DETAIL(91, "已划明细价"),

    ;

    // 枚举类的私有成员变量
    private final int code;
    private final String description;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(PrescriptionPricingTypeEnum::getCode).toArray();


    @Override
    public int[] array() {
        return ARRAYS;
    }

    // 根据整数值获取枚举实例的静态方法
    public static PrescriptionPricingTypeEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElse(DEFAULT);
    }

    /**
     * 是否划价完成
     *
     * @param code
     * @return
     */
    public static boolean isPriced(Integer code) {
        return Objects.equals(PRICED_TOTAL.code, code) || Objects.equals(PRICED_DETAIL.code, code);
    }

    /**
     * 获取划价结束状态
     *
     * @param code
     * @return
     */
    public static Integer endPriced(Integer code) {
        if (Objects.equals(NEED_PRICING.code, code)) {
            return PRICED_DETAIL.code;
        }
        return PRICED_TOTAL.code;
    }
}
