package com.xyy.saas.inquiry.mq.tenant;

import com.xyy.saas.inquiry.pojo.tenant.TenantExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 门店创建/修改 Request VO")
@Data
@Accessors(chain = true)
public class TenantInfoUpdateMessageDto implements Serializable {

    @Schema(description = "门店编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "14411")
    private Long id;

    @Schema(description = "租户类型（1-单店 2连锁门店 3连锁总部）", example = "1")
    private Integer type;

    @Schema(description = "问诊 门店类型", example = "1")
    private Integer wzTenantType;

    @Schema(description = "总部名称（总部）", example = "1")
    private Long headTenantId;

    @Schema(description = "门店状态（0正常 1停用）", example = "1")
    private Integer status;

    /**
     * {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "问诊系统业务类型开通", example = "0")
    private Integer wzBizTypeStatus;

    @Schema(description = "账号数量", example = "26469")
    private Integer wzAccountCount;


    @Schema(description = "智慧脸业务线类型开通", example = "2")
    private Integer zhlBizTypeStatus;

    @Schema(description = "智慧脸账号数量", example = "17503")
    private Integer zhlAccountCount;


    @Schema(description = "门店名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    private String name;

    @Schema(description = "联系人的用户id", example = "11333")
    private Long contactUserId;

    @Schema(description = "联系人", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String contactName;

    @Schema(description = "联系手机")
    private String contactMobile;

    @Schema(description = "营业执照名称", example = "张三")
    private String businessLicenseName;

    @Schema(description = "营业执照号")
    private String businessLicenseNumber;

    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED)
    private String province;

    @Schema(description = "省编码")
    private String provinceCode;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED)
    private String city;

    @Schema(description = "市编码")
    private String cityCode;

    @Schema(description = "区", requiredMode = Schema.RequiredMode.REQUIRED)
    private String area;

    @Schema(description = "区编码")
    private String areaCode;

    @Schema(description = "地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String address;

    @Schema(description = "问诊参数信息")
    private List<TenantParamConfigDto> inquiryParamConfigs;

    @Schema(description = "环境标志：prod-真实数据；test-测试数据；show-线上演示数据")
    private String envTag;

    @Schema(description = "租户拓展字段")
    private TenantExtDto ext;


}
