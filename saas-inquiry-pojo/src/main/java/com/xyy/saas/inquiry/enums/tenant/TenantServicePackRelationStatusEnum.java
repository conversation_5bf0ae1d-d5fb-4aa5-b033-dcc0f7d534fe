package com.xyy.saas.inquiry.enums.tenant;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import java.util.Arrays;
import java.util.Objects;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * @Desc 门店服务包开通状态
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum TenantServicePackRelationStatusEnum implements IntArrayValuable {

    UN_OPEN(0, "未开通", "禁用"),

    OPEN(1, "已开通", "启用"),

    ;

    private final int code;

    private final String desc;

    private final String operateDesc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(TenantServicePackRelationStatusEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static TenantServicePackRelationStatusEnum fromStatusCode(int statusCode) {
        return Arrays.stream(values())
            .filter(value -> value.getCode() == statusCode)
            .findFirst()
            .orElse(UN_OPEN);
    }

    public static boolean isUnOpen(Integer status) {
        return Objects.equals(UN_OPEN.code, status);
    }

    public static boolean isOpen(Integer status) {
        return Objects.equals(OPEN.code, status);
    }

    public static Integer convertBoolStatus(Boolean open) {
        return BooleanUtil.isTrue(open) ? OPEN.code : UN_OPEN.code;
    }

}