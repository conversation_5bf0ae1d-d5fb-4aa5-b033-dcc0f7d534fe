package com.xyy.saas.inquiry.pojo.inquiry;

import cn.iocoder.yudao.framework.common.validation.InEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 问诊套餐项目明细
 * <p>
 * 用于解决 InquiryPackageItem 循环引用问题 将原来的 items 字段中的嵌套对象单独提取为此类
 *
 * @Author: chenxiaoyi
 * @Date: 2024/09/04 14:06
 */
@Data
@Schema(description = "问诊套餐项目明细")
@Valid
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquiryPackageItems implements Serializable {


    @Schema(description = "是否选中", example = "false")
    @Builder.Default
    private boolean checked = true;

    /**
     * {@link InquiryWayTypeEnum}
     */
    @Schema(description = "问诊类型", example = "1")
    @InEnum(value = InquiryWayTypeEnum.class, message = "问诊类型必须是 {value}")
    private Integer inquiryWayType;

    @Schema(description = "额度数量", example = "100")
    @Min(value = 0)
    private Long count;

    @Schema(description = "是否不限", example = "false")
    private boolean unlimited;

    public Long getCount() {
        return count == null ? 0L : count;
    }

    /**
     * 从 InquiryPackageItem 转换为 InquiryPackageItems
     *
     * @param item 原始项目
     * @return 转换后的项目明细
     */
    public static InquiryPackageItems fromInquiryPackageItem(InquiryPackageItem item) {
        if (item == null) {
            return null;
        }
        return InquiryPackageItems.builder()
            .inquiryWayType(item.getInquiryWayType())
            .count(item.getCount())
            .unlimited(item.isUnlimited())
            .build();
    }

    /**
     * 转换为 InquiryPackageItem（用于兼容现有逻辑）
     *
     * @return InquiryPackageItem 对象
     */
    public InquiryPackageItem toInquiryPackageItem() {
        return InquiryPackageItem.builder()
            .inquiryWayType(this.inquiryWayType)
            .count(this.count)
            .unlimited(this.unlimited)
            .build();
    }
}
