package com.xyy.saas.inquiry.mq.doctor;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import com.xyy.saas.inquiry.mq.doctor.dto.DoctorAutoInquiryTimerWheelMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 医师自动开方出停诊事件
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class DoctorAutoInquiryTimerWheelEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "DOCTOR_AUTO_INQUIRY_TIMER_WHEEL";

    private DoctorAutoInquiryTimerWheelMessage msg;

    @JsonCreator
    public DoctorAutoInquiryTimerWheelEvent(@JsonProperty("msg") DoctorAutoInquiryTimerWheelMessage msg) {
        this.msg = msg;
    }


    @Override
    public String getTag() {
        return "";
    }

}
