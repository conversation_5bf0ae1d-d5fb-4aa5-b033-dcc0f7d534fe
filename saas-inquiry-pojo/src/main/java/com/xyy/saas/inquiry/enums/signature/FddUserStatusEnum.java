package com.xyy.saas.inquiry.enums.signature;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;

import java.util.Arrays;

/**
 * 法大大用户认证状态
 */
public enum FddUserStatusEnum implements IntArrayValuable {

    UNAUTHORIZED(0, "未认证"),
    AUTHORIZED(1, "已认证"),
    // CERTIFICATED(2,"已创建证书"),
    SIGNED(3, "已设置签名");
    private Integer code;
    private String desc;

    FddUserStatusEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }


    public Integer getCode() {
        return code;
    }

    public void setCode(Integer code) {
        this.code = code;
    }

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(FddUserStatusEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

}
