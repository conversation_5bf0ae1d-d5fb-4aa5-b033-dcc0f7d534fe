package com.xyy.saas.inquiry.test;

import jakarta.annotation.Resource;
import lombok.AllArgsConstructor;
import org.apache.commons.lang3.reflect.FieldUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.lang.NonNull;

import java.lang.reflect.Field;
import java.util.List;
import java.util.Map;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@AllArgsConstructor
public class MockerFactory implements BeanPostProcessor {

    private static final Logger log = LoggerFactory.getLogger(MockerFactory.class);
    private final ApplicationContext applicationContext;

    @Override
    public Object postProcessBeforeInitialization(@NonNull Object bean, @NonNull String beanName) throws BeansException {
        return bean;
    }

    @Override
    public Object postProcessAfterInitialization(@NonNull Object bean, @NonNull String beanName) throws BeansException {
        Package pkg = bean.getClass().getPackage();
        // 只处理测试类
        if (pkg == null || !(pkg.getName().contains("yudao") || pkg.getName().contains("inquiry"))) {
            return bean;
        }

        List<Field> fields = FieldUtils.getAllFieldsList(bean.getClass());
        for (Field field : fields) {
            // 注入 DrugstoreConfig 中的 dubbo 接口
            // 判断字段是否有注解，并且注解不是 Autowired  Resource  Bean
            if (!field.getType().getName().startsWith("cn.iocoder.yudao.module.system.api")
                || field.getAnnotation(Autowired.class) != null
                || field.getAnnotation(Resource.class) != null
                || field.getAnnotation(Bean.class) != null) {
                continue;
            }
            log.info("MockerFactory.postProcessAfterInitialization.beanName:{}", beanName);
            // DrugstoreConfig 统一注入了，其他地方使用的 Resource  Autowired 注解
            Map<String, ?> beans = applicationContext.getBeansOfType(field.getType());
            // 将用到dubbo接口的地方替换为mock代理的实例
            if (beans.size() > 1) {
                setMockFieldToBean(bean, field, beans);
            }
        }
        return bean;
    }

    // 忽略sonar扫描 S3011 规则
    // sonar 提示：使用反射来设置字段的值，可能会导致反射调用失败，从而导致程序崩溃。
    @SuppressWarnings("squid:S3011")
    private void setMockFieldToBean(Object bean, Field field, Map<String, ?> beans) {
        for (Object fieldBean : beans.values()) {
            if (fieldBean.getClass().getName().contains("$MockitoMock$")) {
                try {
                    field.setAccessible(true);
                    field.set(bean, fieldBean);
                } catch (IllegalAccessException e) {
                    log.error("MockerFactory.setMockFieldToBean {}.{} error: {}", bean.getClass().getName(), field.getName(), e.getMessage(), e);
                }
            }
        }
    }
}
