package com.xyy.saas.inquiry.enums.patient;

import lombok.Getter;

/**
 * 患者妊娠哺乳期取值
 */
@Getter
public enum PregnancyLactationEnum {
    NORMAL(0, "正常"),
    PREGNANCY(1, "妊娠期"),
    LACTATION(2, "哺乳期"),
    PREGNANCY_LACTATION(3, "妊娠哺乳期");

    private final int code;
    private final String desc;

    PregnancyLactationEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer code) {

        if (code == null) {
            return "";
        }

        for (PregnancyLactationEnum value : PregnancyLactationEnum.values()) {
            if (value.getCode() == code) {
                return value.getDesc();
            }
        }
        return "";
    }
}
