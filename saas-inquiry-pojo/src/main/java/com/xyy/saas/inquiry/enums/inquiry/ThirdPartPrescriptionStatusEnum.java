package com.xyy.saas.inquiry.enums.inquiry;

import com.google.common.collect.Lists;
import lombok.Getter;
import java.util.Objects;

/**
 * 三方处方状态
 *
 * <AUTHOR>
 * @Date 6/11/24 1:44 PM
 */
@Getter
public enum ThirdPartPrescriptionStatusEnum {

    PHYSICIAN_PREPARING_PRESCRIPTION(1, "医生待开方"),
    PHYSICIAN_PRESCRIPTION_FAILED(2, "医生开方失败"),
    PHYSICIAN_PRESCRIPTION_COMPLETED(3, "医生开方完成"),
    PHARMACIST_REVIEWING(4, "药师待审核"),
    PHARMACIST_REVIEW_FAILED(5, "药师审核失败"),
    PHARMACIST_REVIEW_COMPLETED(6, "药师审核完成");

    private final Integer code;

    private final String value;

    ThirdPartPrescriptionStatusEnum(Integer code, String value) {
        this.code = code;
        this.value = value;
    }

    /**
     * 根据问诊状态转换
     *
     * @param inquiryStatus
     * @return
     */
    public static ThirdPartPrescriptionStatusEnum transitionInquiryStatusEnum(Integer inquiryStatus) {

        if (inquiryStatus == null) {
            return PHYSICIAN_PRESCRIPTION_FAILED;
        }

        if (InquiryStatusEnum.QUEUING.getStatusCode() == inquiryStatus) {
            return PHYSICIAN_PREPARING_PRESCRIPTION;
        }

        if (Lists.newArrayList(InquiryStatusEnum.CANCELED.getStatusCode(), InquiryStatusEnum.DOCTOR_CANCELED.getStatusCode(), InquiryStatusEnum.TIMEOUT_CANCELED.getStatusCode()).contains(inquiryStatus)) {
            return PHYSICIAN_PRESCRIPTION_FAILED;
        }

        return PHYSICIAN_PREPARING_PRESCRIPTION;
    }

    /**
     * 根据处方状态转换
     *
     * @param prescriptionStatus
     * @return
     */
    public static ThirdPartPrescriptionStatusEnum transitionPrescriptionStatusEnum(Integer prescriptionStatus) {

        // 待开方
        if (Objects.equals(PrescriptionStatusEnum.WAITING.getStatusCode(), prescriptionStatus)) {
            return PHYSICIAN_PREPARING_PRESCRIPTION;
        }

        // 已取消
        if (Objects.equals(PrescriptionStatusEnum.CANCELED.getStatusCode(), prescriptionStatus)) {
            return PHYSICIAN_PRESCRIPTION_FAILED;
        }

        // 待审核
        if (Objects.equals(PrescriptionStatusEnum.WAIT_APPROVAL.getStatusCode(), prescriptionStatus)) {
            return PHYSICIAN_PRESCRIPTION_COMPLETED;
        }

        // 审核中
        if (Objects.equals(PrescriptionStatusEnum.APPROVAL_ING.getStatusCode(), prescriptionStatus)) {
            return PHYSICIAN_PRESCRIPTION_COMPLETED;
        }

        // 已审核完结(通过)-(药师)
        if (Objects.equals(PrescriptionStatusEnum.APPROVAL.getStatusCode(), prescriptionStatus)) {
            return PHARMACIST_REVIEW_COMPLETED;
        }

        // 审核驳回
        if (Objects.equals(PrescriptionStatusEnum.APPROVAL_REJECTED.getStatusCode(), prescriptionStatus)) {
            return PHARMACIST_REVIEW_FAILED;
        }

        return PHYSICIAN_PRESCRIPTION_FAILED;
    }
}