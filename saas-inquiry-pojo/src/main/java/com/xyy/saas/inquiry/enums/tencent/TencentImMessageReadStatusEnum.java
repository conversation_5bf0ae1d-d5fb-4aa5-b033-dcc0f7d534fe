package com.xyy.saas.inquiry.enums.tencent;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * @Author: xucao
 * @Description:腾讯IM消息阅读状态
 */
@Getter
@RequiredArgsConstructor
public enum TencentImMessageReadStatusEnum {
    UNREAD(0, "未读"),
    READ(1, "已读"),
    ;

    private final Integer code;
    private final String desc;

    public static TencentImMessageReadStatusEnum fromCode(Integer code) {
        for (TencentImMessageReadStatusEnum item : values()) {
            if (item.getCode().equals(code)) {
                return item;
            }
        }
        return null;
    }
}
