package com.xyy.saas.inquiry.util;

import com.alibaba.fastjson.JSON;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;

/**
 * @Author:chenxiaoyi
 * @Date:2025/04/17 13:58
 */
@Slf4j
public class DingUtil {

    public static void sendTextMsgByUrl(String content, String url) {
        if (StringUtils.isBlank(url)) {
            return;
        }
        sendTxTextMsgForParams(content, (String) null, false, false, url);
    }

    private static void sendTxTextMsgForParams(String content, String phones, Boolean atPhone, Boolean atAll, String url) {
        if (StringUtils.isNotBlank(url)) {
            CompletableFuture.runAsync(() -> {
                try {
                    log.debug("in.WeChatRobot.url:{}; phones:{}; content:{}; atAll:{}; atAll:{}", new Object[]{url, phones, content, atPhone, atAll});
                    Map<String, String> heads = new HashMap<>();
                    heads.put("Content-Type", "application/json");
                    Map<String, Object> params = buildParams(content, (String[]) null);
                    String result = postJson(url, JSON.toJSONString(params), heads);
                    log.debug("out.WeChatRobot.url:{}; phones:{}; content:{}; atPhone:{}; atAll:{}; result:{}", new Object[]{url, phones, content, atPhone, atAll, JSON.toJSONString(result)});
                } catch (Exception e) {
                    log.error("e.WeChatRobot.phones:{}; content:{}; atPhone:{}; atAll:{};e: ", new Object[]{phones, content, atPhone, atAll, e});
                }

            });
        }
    }

    private static Map<String, Object> buildParams(String content, String[] atMobile) {
        Map<String, String> param = new HashMap<>();
        Map<String, Object> params = new HashMap<>();
        param.put("content", content);
        params.put("msgtype", "text");
        params.put("text", param);
        return params;
    }

    private static List<String> buildPhoneList(Boolean atPhone, String phones, String defaultPhones) {
        List<String> phoneList = new ArrayList<>();
        if (atPhone) {
            if (StringUtils.isNotBlank(phones)) {
                String[] phoneArr = phones.split(",");

                for (int i = 0; i < phoneArr.length; ++i) {
                    phoneList.add(phoneArr[i]);
                }
            } else if (null != defaultPhones && !"".equals(defaultPhones)) {
                String[] phoneArr = defaultPhones.split(",");

                for (int i = 0; i < phoneArr.length; ++i) {
                    phoneList.add(phoneArr[i]);
                }
            }
        }

        return phoneList;
    }

    private static String postJson(String url, String json, Map<String, String> headers) {
        URL u = null;
        HttpURLConnection con = null;
        OutputStreamWriter osw = null;

        try {
            u = new URL(url);
            con = (HttpURLConnection) u.openConnection();
            con.setRequestMethod("POST");
            con.setDoOutput(true);
            con.setDoInput(true);
            con.setUseCaches(false);
            con.setRequestProperty("Content-Type", "application/json");
            con.setConnectTimeout(30000);
            con.setReadTimeout(30000);

            for (String header : headers.keySet()) {
                con.setRequestProperty(header, (String) headers.get(header));
            }

            osw = new OutputStreamWriter(con.getOutputStream(), "UTF-8");
            osw.write(json);
            osw.flush();
            osw.close();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (con != null) {
                con.disconnect();
            }

            if (osw != null) {
                try {
                    osw.close();
                } catch (Exception ex) {
                    ex.printStackTrace();
                }
            }

        }

        StringBuffer buffer = new StringBuffer();

        try {
            BufferedReader br = new BufferedReader(new InputStreamReader(con.getInputStream(), "UTF-8"));

            String temp;
            while ((temp = br.readLine()) != null) {
                buffer.append(temp);
                buffer.append("\n");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }

        return buffer.toString();
    }
}
