package com.xyy.saas.inquiry.util;

import org.apache.commons.collections4.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * 内存分页工具
 *
 * <AUTHOR>
 * @Date 3/15/22 3:49 PM
 */
public class ListPageUtil {

    /**
     * 内存分页
     *
     * @param list
     * @param pageNum
     * @param pageSize
     * @return java.util.List<T>
     * <AUTHOR> 3/15/22 3:50 PM
     */
    public static <T> List<T> startPageList(List<T> list, Integer pageNum, Integer pageSize) {

        if (CollectionUtils.isEmpty(list) || pageNum == null || pageNum <= 0 || pageSize == null || pageSize <= 0) {
            return new ArrayList<>();
        }

        // 截取元素的起始位置,包含该索引位置元素
        int beginIndex = (pageNum - 1) * pageSize;
        // 截取元素的结束位置,不包含该索引位置元素
        int endIndex = beginIndex + pageSize;

        if (beginIndex > list.size()) {
            return new ArrayList<>();
        }

        if (endIndex > list.size()) {
            endIndex = list.size();
        }

        // List<Object> pageList = list.stream().skip(begin).limit(end - begin).collect(Collectors.toList());

        return list.subList(beginIndex, endIndex);
    }
}
