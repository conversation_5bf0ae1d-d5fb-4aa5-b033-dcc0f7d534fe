package com.xyy.saas.inquiry.util;


/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public class SqlUtil {

    // mysql全文索引-分词字符最短长度限制
    private static final int MYSQL_FULL_TEXT_NGRAM_TOKEN_SIZE = 2;



    /**
     * 清理全文本搜索查询字符串
     *
     * @param query
     * @return
     */
    public static String cleanFullTextSearchQuery(String query) {
        if (query == null || query.trim().isEmpty()) {
            return null;
        }
        // 1. 移除或替换所有布尔模式下的特殊字符
        // 正则表达式 [+\\-*\"<>~()@] 匹配所有特殊字符
        // 注意 - 需要转义为 \\-
        String cleanedQuery = query.replaceAll("[+\\-*\"<>~()@]", " ").trim();

        // 2. (可选) 将多个空格替换为单个空格
        cleanedQuery = cleanedQuery.replaceAll("\\s+", " ");

        // 3. (可选) 在每个词前加上 '+'，要求必须包含
        if (!cleanedQuery.isEmpty()) {
            String[] words = cleanedQuery.split(" ");
            StringBuilder requiredQuery = new StringBuilder();
            for (String word : words) {
                // 过滤掉单个词语（小于最小分词数，无法查询到数据）
                if (word.trim().length() >= MYSQL_FULL_TEXT_NGRAM_TOKEN_SIZE) {
                    requiredQuery.append("+").append(word).append(" ");
                }
            }
            return requiredQuery.toString().trim();
        }

        return null;
    }
}
