package com.xyy.saas.inquiry.mq.tenant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class TenantInfoUpdateEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "TENANT_INFO_UPDATE";

    private TenantInfoUpdateMessageDto msg; // 改之前的tenant信息

    @JsonCreator
    public TenantInfoUpdateEvent(@JsonProperty("msg") TenantInfoUpdateMessageDto msg) {
        this.msg = msg;
    }

    @Override
    public String getTag() {
        return "";
    }
}
