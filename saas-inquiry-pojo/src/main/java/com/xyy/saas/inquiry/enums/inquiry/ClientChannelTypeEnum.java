package com.xyy.saas.inquiry.enums.inquiry;

import lombok.Getter;
import java.util.Objects;

@Getter
public enum ClientChannelTypeEnum {

    /**
     * APP 客户端
     */
    APP(0, "app", "APP"),

    /**
     * PC 客户端
     */
    PC(1, "pc", "PC端"),
    /**
     * 小程序客户端
     */
    MINI_PROGRAM(2, "wechat", "小程序"),

    /**
     * 第三方
     */
    THIRD(3,"third", "第三方"),

    ;

    private final int code; // 整数字段
    private final String suffix;
    private final String description; // 字符串字段

    // 构造函数，用于初始化枚举项的字段
    ClientChannelTypeEnum(int code, String suffix, String description) {
        this.code = code;
        this.suffix = suffix;
        this.description = description;
    }

    /**
     * 根据整数代码获取对应的枚举类型
     *
     * @param code 整数代码
     * @return 对应的枚举类型，如果不存在则返回 null
     */
    public static ClientChannelTypeEnum fromCode(Integer code) {
        for (ClientChannelTypeEnum channel : values()) {
            if (Objects.equals(channel.getCode(), code)) {
                return channel;
            }
        }
        return null;
    }

    public static ClientChannelTypeEnum fromCode(String code) {
        for (ClientChannelTypeEnum channel : values()) {
            if (Objects.equals(channel.getCode() + "", code)) {
                return channel;
            }
        }
        return null;
    }


    public static boolean isPc(Integer code) {
        return Objects.equals(ClientChannelTypeEnum.PC, fromCode(code));
    }

    public static boolean isApp(Integer code) {
        return Objects.equals(ClientChannelTypeEnum.APP, fromCode(code));
    }


    /**
     * 根据平台获取对应的枚举类型
     *
     * @param platform
     * @return
     */
    public static ClientChannelTypeEnum fromPlatform(String platform) {
        if ("admin-api".equalsIgnoreCase(platform)) {
            return PC;
        } else if ("app-api".equalsIgnoreCase(platform)) {
            return APP;
        }
        return null;
    }

    /**
     * 根据userId 和 客户端类型获取im账号
     *
     * @param userId                用户ID
     * @param clientChannelTypeEnum 客户端类型
     * @return IM账号
     */
    public String getImAccount(Long userId, ClientChannelTypeEnum clientChannelTypeEnum) {
        return userId + "_" + clientChannelTypeEnum.suffix;
    }
}
