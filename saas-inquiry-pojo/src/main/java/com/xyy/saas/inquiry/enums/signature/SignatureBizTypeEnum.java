package com.xyy.saas.inquiry.enums.signature;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 签章业务类型
 *
 * @Author:chenxiaoyi
 * @Date:2024/10/18 11:38
 */
@Getter
@RequiredArgsConstructor
public enum SignatureBizTypeEnum implements IntArrayValuable {

    USER_HAND_DRAWN_SIGN(1, "用户手绘签名"),

    AUTHORIZATION_CONTRACT(2, "授权合同"),

    USER_ELE_SIGN(3, "用户签名电子章"),
    ;

    private final Integer code;

    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(SignatureBizTypeEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }


}
