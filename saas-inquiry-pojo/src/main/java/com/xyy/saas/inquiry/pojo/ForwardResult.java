package com.xyy.saas.inquiry.pojo;

import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import java.io.Serializable;
import java.util.Objects;
import lombok.Data;

/**
 * @Author:chenxiaoyi
 * @Date:2024/11/26 9:41
 */
@Data
public class ForwardResult<T> implements Serializable {

    private int code;
    private String msg;
    private T result;

    public boolean isSuccess() {
        return Objects.equals(this.code, GlobalErrorCodeConstants.SUCCESS.getCode());
    }


    public static <T> CommonResult<T> convertSuccess(ForwardResult<T> result) {
        return CommonResult.success(result.result);
    }

}
