package com.xyy.saas.inquiry.enums.medicare;

import lombok.Getter;

/**
 * @Author: xucao
 * @DateTime: 2025/7/11 11:42
 * @Description: 医药机构类型 1、药店  2、医院
 **/
@Getter
public enum InstitutionTypeEnum {
    PHARMACY(1, "药店"),
    HOSPITAL(2, "医院");
    private final Integer code;
    private final String desc;

    InstitutionTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
