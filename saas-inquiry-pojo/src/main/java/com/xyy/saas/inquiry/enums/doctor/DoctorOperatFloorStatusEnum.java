package com.xyy.saas.inquiry.enums.doctor;

import lombok.Getter;

/**
 * 医生接诊工作台状态
 */
@Getter
public enum DoctorOperatFloorStatusEnum {
    /**
     * 医生接诊工作台状态
     */
    WAIT_RECEIPT(0, "待接诊"),
    RECEIPTING(1, "进行中"),
    ;

    private Integer status;
    private String desc;

    DoctorOperatFloorStatusEnum(Integer status, String desc) {
        this.status = status;
        this.desc = desc;
    }
}
