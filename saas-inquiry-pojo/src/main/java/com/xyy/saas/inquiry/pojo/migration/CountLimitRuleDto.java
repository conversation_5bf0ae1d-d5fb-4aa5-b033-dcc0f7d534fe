package com.xyy.saas.inquiry.pojo.migration;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.Map;

/**
 * 问诊套餐次数限制规则
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CountLimitRuleDto implements Serializable {

    /**
     * 1-视频、2-图文
     */
    private Integer countLimitType;

    /**
     * 视频or图文
     */
    private String countLimitTypeName;

    /**
     * 0-无限制、1-有限制
     */
    private Integer isInfinite;

    /**
     * 限制条数
     */
    private Integer countLimit;

    /**
     * 剩余条数
     */
    private Integer surplusLimit;


    /**
     * 问诊条数限制是否无限：0-无限、1-有限
     */
    private Integer inquiryCountLimitIsInfinite;

    /**
     * 问诊条数限制  超过条数问诊将延迟派单
     */
    private Integer inquiryCountLimit;

    /**
     * 服务包中对应类型 累计使用次数
     */
    private Integer inquiryLimitAlreadyUseCount;

    /**
     * 统筹规则
     */
    private Map<String, CountLimitRuleDto> overallPlanRule;
}
