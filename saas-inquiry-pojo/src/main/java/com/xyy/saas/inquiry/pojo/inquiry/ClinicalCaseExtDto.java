package com.xyy.saas.inquiry.pojo.inquiry;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: cxy
 * @Description: 病例扩展信息ext
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "病例扩展信息ext")
public class ClinicalCaseExtDto implements Serializable {

    @Schema(description = "肝肾功能异常")
    private Integer liverKidneyValue;

    @Schema(description = "妊娠哺乳期")
    private Integer gestationLactationValue;

    @Schema(description = "门诊诊断说明")
    private String outpatientDiagnosisDesc;

    @Schema(description = "中医四诊描述")
    private String tcmFourDiagnosticDesc;

    @Schema(description = "是否上传舌象 0否 1是")
    private Integer tcmUploadTongueImage;

    @Schema(description = "中医辨证分析")
    private String tcmDialecticalAnalysis;

    /**
     * 医生签名图片
     */
    private String doctorSignatureUrl;

}
