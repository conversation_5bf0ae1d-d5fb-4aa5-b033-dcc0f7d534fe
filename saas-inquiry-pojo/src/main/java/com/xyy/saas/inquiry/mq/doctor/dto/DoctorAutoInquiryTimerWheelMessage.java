package com.xyy.saas.inquiry.mq.doctor.dto;

import lombok.*;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: cxy
 * @Date: 2024/12/26 10:00
 * @Description: 医生自动开方时间轮消息体
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DoctorAutoInquiryTimerWheelMessage implements Serializable {


    /**
     * 处理医生自动开方时间轮Type 1.正常 2.免签过期-个人 3.免签过期-全天医生
     */
    private Integer type;

    /**
     * 医生编号
     */
    private String doctorPref;

    /**
     * 开放池key
     */
    private String realKey;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 原始时间轮列表
     */
    private List<DoctorAutoInquiryTimerWheelDto> originWheels;

    @Getter
    public static enum DoctorAutoInquiryTimerWheelEnum {
        NORMAL(1, "常规"),
        PERSONNEL_CHECK_CA(2, "自动开方医生校验CA"),
        ;

        int code;
        String msg;

        DoctorAutoInquiryTimerWheelEnum(int code, String msg) {
            this.code = code;
            this.msg = msg;
        }
    }

}
