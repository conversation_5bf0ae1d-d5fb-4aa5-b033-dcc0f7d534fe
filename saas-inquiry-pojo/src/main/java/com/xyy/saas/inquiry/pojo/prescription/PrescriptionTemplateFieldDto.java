package com.xyy.saas.inquiry.pojo.prescription;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 参与方item
 *
 * @Author:chenxia<PERSON>i
 * @Date:2024/09/04 14:06
 */
@Data
@Schema(description = "处方模板字段Dto")
@Valid
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class PrescriptionTemplateFieldDto extends PrescriptionTemplateField {

    /**
     * 冗余一份用签名图片url
     */
    private String userSignImgUrl;
}
