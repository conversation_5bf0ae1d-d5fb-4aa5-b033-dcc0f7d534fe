package com.xyy.saas.inquiry.mq.inquiry;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import com.xyy.saas.inquiry.mq.inquiry.dto.InquiryDoctorGrabbingPrescriptionMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: cxy
 * @Date: 2024/12/11 11:43
 * @Description: 问诊医生抢单成功MQ
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class InquiryDoctorGrabbingPrescriptionEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "INQUIRY_DOCTOR_GRABBING_PRESCRIPTION";

    private InquiryDoctorGrabbingPrescriptionMessage msg;

    @JsonCreator
    public InquiryDoctorGrabbingPrescriptionEvent(@JsonProperty("msg") InquiryDoctorGrabbingPrescriptionMessage msg) {
        this.msg = msg;
    }

    @Override
    public String getTag() {
        return "";
    }
}
