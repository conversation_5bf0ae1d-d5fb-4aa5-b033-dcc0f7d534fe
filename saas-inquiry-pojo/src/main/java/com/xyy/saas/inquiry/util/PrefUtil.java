package com.xyy.saas.inquiry.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.xyy.saas.inquiry.enums.signature.ContractTypeEnum;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.TreeSet;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.data.redis.core.RedisTemplate;

/**
 * 自增pref编码(redis)
 *
 * @Author:chenxiaoyi
 * @Date:2024/11/01 13:21
 */
@Slf4j
public class PrefUtil {

    /**
     * 获取门店pref
     */
    public static String getMdPref() {
        return getPref("MD");
    }

    /**
     * 获取套餐pref
     */
    public static String getTcPref() {
        return getPref("TC");
    }

    /**
     * 获取套餐包pref
     */
    public static String getTcbPref() {
        return getPref("TCB");
    }

    /**
     * 获取医院pref
     */
    public static String getHospitalPref() {
        return getPref("H");
    }

    /**
     * 获取医院科室pref
     */
    public static String getHospitalDeptPref() {
        return getPref("KS");
    }

    /**
     * 获取医生pref
     */
    public static String getDoctorPref() {
        return getPref("D");
    }

    /**
     * 获取医生录屏pref
     *
     * @return
     */
    public static String getDoctorVideoPref() {
        return getPref("LP");
    }

    /**
     * 获取药师pref
     */
    public static String getPharmacistPref() {
        return getPref("P");
    }

    /**
     * 获取预问诊单pref
     */
    public static String getPreInquiryPref() {
        return getPref("YWZ");
    }


    /**
     * 获取问诊单pref
     */
    public static String getInquiryPref() {
        return getPref("");
    }

    /**
     * 获取处方单pref : HYWZ
     */
    public static String getPrescriptionPref() {
        return getPref(ContractTypeEnum.PRESCRIPTION.getPrefix());
    }

    /**
     * 获取患者pref
     */
    public static String getPatientPref() {
        return getPref("HZ");
    }

    /**
     * 获取合同pref
     */
    public static String getContractPref() {
        return getPref("HT");
    }

    /**
     * 获取角色自增id
     *
     * @return
     */
    public static String getRolePref() {
        return getPref("JS");
    }

    /**
     * 获取商品自增pref
     *
     * @return
     */

    public static String getProductPref() {
        return getPref(PREF_KEY_PREFIX_PRODUCT_PREF);
    }

    /**
     * 获取质量变更申请自增pref
     *
     * @return
     */
    public static String getProductQualityChangePref() {
        return getPref("ZLBG");
    }

    /**
     * 获取售价调整单自增pref
     *
     * @return
     */
    public static String getProductPriceAdjustmentPref() {
        return getPref("SJTZ");
    }

    /**
     * 获取机构网络配置自增id
     *
     * @return
     */
    public static String getTransmissionOrganNetworkConfigPref() {
        return getPref("TNOC");
    }

    /**
     * 获取病例编号
     *
     * @return
     */
    public static String getClinicalCasePref() {
        return getPref("BL");
    }

    /**
     * 获取就诊挂号
     *
     * @return
     */
    public static String getMedicalRegistrationPref() {
        return getPref("JZ");
    }

    /**
     * 获取外配处方
     *
     * @return
     */
    public static String getExternalPref() {
        return getPref("WP");
    }

    /**
     * 互联网监管目录
     */
    public static final String INTERNET_REGULATION_PREF = "HLWJG";

    /**
     * 医保目录
     */
    public static final String MEDICAL_CATALOG_PREF = "YBML";


    /**
     * 获取自增编码 根据当前类+方法全路径保持唯一key 自增value从100000开始
     *
     * @param prefix 编码前缀
     * @return prefix + 自增ID
     */
    private static String getPref(String prefix) {
        String key = PrefUtil.class.getName() + "." + Thread.currentThread().getStackTrace()[2].getMethodName();
        RedisTemplate<String, Long> redisTemplate = getRedisTemplate();
        Long increment = redisTemplate.opsForValue().increment(key);

        if (Objects.equals(increment, 1L)) {
            increment = 100000L;
            redisTemplate.opsForValue().set(key, increment);
        }
        return prefix + increment.toString();
    }

    /**
     * 获取自增编码 by 步长
     *
     * @param prefix
     * @param delta
     * @return
     */
    public static Long getPrefIncrementDelta(String prefix, long delta) {
        String key = PrefUtil.class.getName() + "." + prefix;
        RedisTemplate<String, Long> redisTemplate = getRedisTemplate();
        return redisTemplate.opsForValue().increment(key, delta);
    }


    public static final String APP_VERSION_CODE_SEQ = "app_version_code_seq";

    /**
     * 获取app版本code
     *
     * @return key
     */
    public static Integer getAppVersionCode() {
        RedisTemplate<String, Long> redisTemplate = getRedisTemplate();
        Long increment = redisTemplate.opsForValue().increment(APP_VERSION_CODE_SEQ);
        return increment.intValue();
    }

    /**
     * 处理用户CA授权编码 - 加前缀,方便旧业务区分回调
     *
     * @param userId
     * @return
     */
    public static String getCaSqPref(Long userId) {
        return ContractTypeEnum.AUTHORIZATION_CONTRACT.getPrefix() + userId;
    }

    public static String getCaSqPref(String pref) {
        return StringUtils.replace(pref, ContractTypeEnum.AUTHORIZATION_CONTRACT.getPrefix(), "");
    }

    static RedisTemplate<String, Long> redisTemplate;

    @SuppressWarnings("unchecked")
    public static RedisTemplate<String, Long> getRedisTemplate() {
        if (redisTemplate == null) {
            redisTemplate = (RedisTemplate<String, Long>) SpringUtil.getBean(RedisTemplate.class);
        }
        return redisTemplate;
    }


    /**
     * 商品内码前缀，例如：SP000001
     */
    public static final String PREF_KEY_PREFIX_PRODUCT_PREF = "SP";

    /**
     * 商品外码前缀，例如：SKU112
     */
    public static final String SHOW_PREF_KEY_PREFIX_PRODUCT = "SKU";
    /**
     * 拆零商品外码追加后缀，例如：SKU112CL2
     */
    public static final String SHOW_PREF_KEY_PREFIX_PRODUCT_APPEND_UNBUNDLED = "CL";
    /**
     * 拆零商品名称追加后缀，例如：阿莫西林胶囊（拆零）
     */
    public static final String NAME_SUFFIX_PRODUCT_APPEND_UNBUNDLED = "（拆零）";

    /**
     * 获取商品外码 - 机构内唯一 使用布隆过滤器存储已使用的编码，找到最小可用编码 （可能会误判已存在的，但不会漏判，保证了唯一性）
     *
     * @param tenantId 租户ID
     * @return 最小可用的商品编码
     */
    public static String getProductShortPref(long tenantId, Supplier<List<String>> initAllPrefSp) {
        return getProductShortPrefList(tenantId, 1, null, initAllPrefSp).getFirst();
    }

    /**
     * 获取拆零商品外码
     *
     * @param tenantId
     * @param sourceShowPref
     * @param initAllPrefSp
     * @return
     */
    public static String getUnbundledProductShortPref(long tenantId, String sourceShowPref, Supplier<List<String>> initAllPrefSp) {
        // 拆零商品外码追加后缀，例如：SKU112CL2， 如果是 CL1 则返回 SKU112CL
        return getProductShortPrefList(tenantId, 1, sourceShowPref + SHOW_PREF_KEY_PREFIX_PRODUCT_APPEND_UNBUNDLED, initAllPrefSp).getFirst()
            // .replaceAll(PREF_KEY_PREFIX_PRODUCT_APPEND_UNBUNDLED + "1$", PREF_KEY_PREFIX_PRODUCT_APPEND_UNBUNDLED)
            ;
    }

    public static List<String> getProductShortPrefList(long tenantId, int batchSize, String prefix, Supplier<List<String>> initAllPrefSp) {
        if (batchSize <= 0 || initAllPrefSp == null) {
            return List.of();
        }
        List<String> prefList = initAllPrefSp.get();
        String finalPrefix = prefix == null ? SHOW_PREF_KEY_PREFIX_PRODUCT : prefix;
        // prefList 去掉SKU前缀，然后转成int集合，获取其中不存在的最小值，根据batchSize获取多个
        Set<Integer> integerSet = prefList.stream().filter(s -> s.startsWith(finalPrefix))
            .map(s -> NumberUtils.toInt(s.substring(finalPrefix.length())))
            .collect(Collectors.toSet());

        // redis 获取可能存在空隙（事务回滚等场景导致）
        // return getShortPrefFromRedis("product:showPref", tenantId, batchSize, integerSet).stream().map(i -> PREF_KEY_PREFIX_PRODUCT + i).toList();

        // 数据库 获取当前租户全部商品编码，内存查找最小编码
        return getShortPrefFromDB(integerSet, batchSize, i -> finalPrefix + i);
    }

    /**
     * 从数据库获取商品编码, 然后内存排序查找最小的n个不冲突的数字
     *
     * @param integerSet
     * @param batchSize
     * @return
     */
    public static <T> List<T> getShortPrefFromDB(Set<Integer> integerSet, int batchSize, Function<Integer, T> convert) {
        List<T> result = new ArrayList<>();
        int num = 1;

        while (result.size() < batchSize) {
            if (!integerSet.contains(num)) {
                result.add(convert.apply(num));
            }
            num++;
        }

        return result;
    }


    /**
     * 获取商品外码 - 机构内唯一 使用布隆过滤器存储已使用的编码，找到最小可用编码 （可能会误判已存在的，但不会漏判，保证了唯一性）
     *
     * @param tenantId 租户ID
     * @return 最小可用的商品编码
     */
    public static TreeSet<Integer> getShortPrefFromRedis(String redisKeyPrefix, long tenantId, int batchSize, Set<Integer> integerSet) {
        RedisTemplate<String, Long> redisTemplate = getRedisTemplate();
        String bloomFilterKey = initOrPutBloomFilter(redisKeyPrefix, tenantId, integerSet);
        String counterKey = redisKeyPrefix + ":counter:" + tenantId;

        TreeSet<Integer> newPrefSet = new TreeSet<>();
        // 循环直到找到未使用的编码
        do {
            // 计数器（单个租户内数字不能超过21亿） - 不要使用管道和事务，避免空指针！！！
            int candidate = redisTemplate.opsForValue().increment(counterKey).intValue();
            // 注意：redis bitmaps实际存储的是字符串，最大长度512MB（适合最多2^32位，刚好是int类型，不能存储超过int）
            boolean exists = Boolean.TRUE.equals(redisTemplate.opsForValue().getBit(bloomFilterKey, candidate));
            if (!exists) {
                newPrefSet.add(candidate);
            }
        } while (batchSize > newPrefSet.size());

        // 找到可用编码，更新布隆过滤器
        initOrPutBloomFilter(bloomFilterKey, tenantId, newPrefSet);
        return newPrefSet;
    }

    /**
     * 初始化 & 增量入库 布隆过滤器
     *
     * @param redisKeyPrefix 布隆过滤器前缀
     * @param tenantId       租户ID
     * @param initAllPrefSet 现存编码集合
     * @return 布隆过滤器key
     */
    public static String initOrPutBloomFilter(String redisKeyPrefix, long tenantId, Set<Integer> initAllPrefSet) {
        String bloomFilterKey = redisKeyPrefix + ":bloom:" + tenantId;

        // 如果有现存编码集合，初始化布隆过滤器和计数器
        if (CollUtil.isNotEmpty(initAllPrefSet)) {
            RedisTemplate<String, Long> redisTemplate = getRedisTemplate();
            // 记录初始化信息
            log.warn("BloomFilter入库：{}, tenantId：{}, sets：{}", bloomFilterKey, tenantId, initAllPrefSet);
            // 将所有编码添加到布隆过滤器
            for (Integer code : initAllPrefSet) {
                redisTemplate.opsForValue().setBit(bloomFilterKey, code, true);
            }
        }
        return bloomFilterKey;
    }

}
