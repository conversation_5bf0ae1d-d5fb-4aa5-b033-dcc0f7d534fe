package com.xyy.saas.inquiry.mq.tenant.cost;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2025/01/09 14:18
 * @Description: 问诊额度退回
 */
@Component
@EventBusProducer(
    topic = InquiryReBackCostEvent.TOPIC
)
public class InquiryReBackProducer extends EventBusRocketMQTemplate {

}
