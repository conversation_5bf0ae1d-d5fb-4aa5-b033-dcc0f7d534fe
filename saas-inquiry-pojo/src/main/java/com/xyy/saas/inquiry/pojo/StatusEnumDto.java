package com.xyy.saas.inquiry.pojo;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 状态类枚举Dto
 *
 * @Author:chen<PERSON><PERSON>i
 * @Date:2025/01/13 21:23
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class StatusEnumDto implements Serializable {

    /**
     * 状态
     */
    private Integer status;

    /**
     * 展示名称
     */
    private String name;

    /**
     * 说明
     */
    private String desc;

    /**
     * 图标样式 0绿勾  1绿...  2红色x  3灰色...
     */
    private int type;


    public StatusEnumDto(Integer status, String name, String desc) {
        this.status = status;
        this.name = name;
        this.desc = desc;
    }
}
