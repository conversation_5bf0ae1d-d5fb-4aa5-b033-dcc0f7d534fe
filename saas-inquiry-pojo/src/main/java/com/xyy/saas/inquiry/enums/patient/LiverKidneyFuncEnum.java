package com.xyy.saas.inquiry.enums.patient;

import lombok.Getter;

/**
 * 肝肾功能枚举
 */
@Getter
public enum LiverKidneyFuncEnum {

    NORMAL(0, "正常"),

    ABNORMAL_LIVER_FUNCTION(1, "肝功能异常"),

    ABNORMAL_KIDNEY_FUNCTION(2, "肾功能异常"),

    ABNORMAL_LIVER_KIDNEY_FUNCTION(3, "肝肾功能异常");

    private final int code;
    private final String desc;

    LiverKidneyFuncEnum (int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static LiverKidneyFuncEnum fromCode(int code) {
        for (LiverKidneyFuncEnum e : LiverKidneyFuncEnum.values()) {
            if (e.code == code) {
                return e;
            }
        }
        return null;
    }

    public static String getDesc(int code) {
        for (LiverKidneyFuncEnum e : LiverKidneyFuncEnum.values()) {
            if (e.code == code) {
                return e.desc;
            }
        }
        return "";
    }
}
