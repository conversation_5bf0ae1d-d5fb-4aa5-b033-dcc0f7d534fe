package com.xyy.saas.inquiry.pojo.excel;

import com.alibaba.excel.annotation.ExcelProperty;

/**
 * 1.子类需要 单独写set方法 2.子类设置  @Accessors(chain = true) 会导致读取到的全是 null
 *
 * @Author:chen<PERSON><PERSON>i
 * @Date:2025/01/22 15:49
 */
public class ImportExcelVoDto implements java.io.Serializable {

    @ExcelProperty(value = "失败原因")
    protected String errMsg = "";

    /**
     * 基础字段校验
     */
    public void valid() {

    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }
}
