package com.xyy.saas.inquiry.pojo;

import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import lombok.Data;
import java.io.Serializable;
import java.util.Objects;

@Data
public class SyncSaasResult<T> implements Serializable {
    private int errorcode;
    private String msg;
    private T result;

    public boolean isSuccess() {
        return Objects.equals(this.errorcode, GlobalErrorCodeConstants.SUCCESS.getCode());
    }


    public static <T> CommonResult<T> convertSuccess(SyncSaasResult<T> result) {
        return CommonResult.success(result.result);
    }

}
