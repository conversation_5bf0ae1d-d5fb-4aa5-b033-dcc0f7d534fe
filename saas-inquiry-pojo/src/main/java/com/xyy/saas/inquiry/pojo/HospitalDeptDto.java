package com.xyy.saas.inquiry.pojo;

import java.io.Serializable;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: xucao
 * @Date: 2024/12/24 9:55
 * @Description: 医院科室Dto
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class HospitalDeptDto implements Serializable {

    /**
     * 医院编码
     */
    private String hospitalPref;

    /**
     * 医院名称
     */
    private String hospitalName;

    /**
     * 问诊对应科室列表
     */
    private List<Dept> inquiryDeptList;

    /**
     * 当前调度的科室索引
     */
    @Builder.Default
    private Integer currentDispatchDeptIndex = 0;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Dept implements Serializable {

        /**
         * 科室编码
         */
        private String deptPref;

        /**
         * 科室名称
         */
        private String deptName;

        /**
         * 科室调度类型 {@link com.xyy.saas.inquiry.enums.inquiry.DispatchDeptTypeEnum}
         */
        private Integer dispatchDeptType;
    }

    /**
     * 当前调度科室索引自增
     */
    public void incrementCurrentDispatchDeptIndex() {
        this.currentDispatchDeptIndex++;
    }

    // 保证顺序
    @JsonIgnore
    public List<Dept> getSortInquiryDeptList() {
        inquiryDeptList.sort((o1, o2) -> o1.getDispatchDeptType().compareTo(o2.getDispatchDeptType()));
        return inquiryDeptList;
    }

    /**
     * 重置当前调度科室索引为初始值
     */
    public void restDispatchDeptIndex() {
        this.currentDispatchDeptIndex = 0;
    }
}
