package com.xyy.saas.inquiry.pojo.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Schema(description = "管理后台 - 门店资质证件信息 Response VO")
@Data
public class TenantCertificateRespDto implements Serializable {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "1572")
    private Long id;

    @Schema(description = "门店id", example = "2")
    private Long tenantId;

    @Schema(description = "证件类型 1营业执照 2药品经营许可证 3药品经营质量管理规范认证号", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer certificateType;

    @Schema(description = "证件名称", example = "芋艿")
    private String certificateName;

    @Schema(description = "证件号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String certificateNo;

    @Schema(description = "证件url地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> certificateImgUrls;

    @Schema(description = "注册时间")
    private LocalDateTime registerTime;

    @Schema(description = "有效期至")
    private LocalDateTime validTime;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

}