package com.xyy.saas.inquiry.util;


import cn.iocoder.yudao.framework.common.exception.ServiceException;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.ObjectUtils;
import java.util.Collection;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public class AssertUtils {

    /**
     * 判断对象是非空，如果对象是空，就抛异常
     *
     * @param object       判断对象
     * @param errorMessage 异常提示信息
     */
    public static void notNull(@Nullable Object object, String errorMessage) {
        if (null == object) {
            throw new IllegalArgumentException(errorMessage);
        }
    }

    /**
     * 判断对象是否为空，如果为空，就抛出异常。
     *
     * @param object       对象（支持：字符串、数组、集合、Map、Optional）
     * @param errorMessage 错误提示信息
     */
    public static void notEmpty(@Nullable Object object, String errorMessage) {
        if (ObjectUtils.isEmpty(object)) {
            throw new IllegalArgumentException(errorMessage);
        }
    }

    /**
     * 判断对象是否为空，如果不为空，就抛出异常。
     *
     * @param object       对象（支持：字符串、数组、集合、Map、Optional）
     * @param errorMessage 错误提示信息
     */
    public static void isEmpty(@Nullable Object object, String errorMessage) {
        if (ObjectUtils.isNotEmpty(object)) {
            throw new IllegalArgumentException(errorMessage);
        }
    }

    /**
     * 判读对象是不是空，如果是非空，就抛异常
     *
     * @param object       判断对象
     * @param errorMessage 异常提示信息
     */
    public static void isNull(@Nullable Object object, String errorMessage) {
        if (null != object) {
            throw new IllegalArgumentException(errorMessage);
        }
    }

    /**
     * 判断是否是真，如果对象是false，就抛出异常
     *
     * @param expression   判断对象
     * @param errorMessage 异常提示信息
     */
    public static void isTrue(boolean expression, String errorMessage) {
        if (!expression) {
            throw new IllegalArgumentException(errorMessage);
        }
    }

    /**
     * 判断是否是真，如果对象是false，就抛出异常。
     *
     * @param expression   判断对象
     * @param errorMessage 异常提示信息
     */
    public static void isTrue(Boolean expression, String errorMessage) {
        if (Boolean.FALSE.equals(expression)) {
            throw new IllegalArgumentException(errorMessage);
        }
    }

}
