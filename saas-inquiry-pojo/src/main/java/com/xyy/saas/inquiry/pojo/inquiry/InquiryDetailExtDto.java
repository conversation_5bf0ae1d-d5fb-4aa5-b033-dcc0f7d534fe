package com.xyy.saas.inquiry.pojo.inquiry;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author: cxy
 * @Description: 问诊详情扩展信息ext
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "问诊详情扩展信息ext")
public class InquiryDetailExtDto implements Serializable {

    @Schema(description = "是否为复诊 0否  1是")
    private Integer followUp;

    @Schema(description = "患者家庭住址")
    private String patientAddress;

    @Schema(description = "监护人姓名")
    private String guardianName;

    @Schema(description = "监护人身份证号")
    private String guardianIdCard;

    @Schema(description = "中药西医诊断编码")
    private List<String> diagnosisCode;

    @Schema(description = "中药西医诊断名称")
    private List<String> diagnosisName;

    @Schema(description = "中医辨证名称")
    private String tcmSyndromeName;

    @Schema(description = "中医辨证代码")
    private String tcmSyndromeCode;

    @Schema(description = "中医治法名称")
    private String tcmTreatmentMethodName;

    @Schema(description = "中医治法代码")
    private String tcmTreatmentMethodCode;

    @Schema(description = "图文问诊问答信息列表")
    private List<QuestionAnswerInfoDto> questionAnswerList;

    @Schema(description = "处方审核是否视频")
    private boolean prescriptionAuditVideo;

    /**
     * 医保参保人信息
     */
    private Long medicareInsuranceId;
}
