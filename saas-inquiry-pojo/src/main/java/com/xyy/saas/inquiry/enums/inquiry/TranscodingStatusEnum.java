package com.xyy.saas.inquiry.enums.inquiry;

import lombok.Getter;

/**
 * @author: xucao
 * @date: 2024/12/11 15:47
 * @description: 转码状态枚举
 */
@Getter
public enum TranscodingStatusEnum {
    PENDING_TRANSCODING(0, "待转码"),
    FILE_ID_NOT_FOUND(1, "未查询到文件id"),
    TRANSCODING_START_FAILED(2, "开启转码失败"),
    TRANSCODING_STARTED(3, "已开启转码"),
    TRANSCODING_RESULT_NOT_FOUND(4, "未查询到转码结果");

    private final int code;
    private final String desc;

    TranscodingStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static TranscodingStatusEnum fromCode(int code) {
        for (TranscodingStatusEnum status : TranscodingStatusEnum.values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        throw new IllegalArgumentException("Unknown code: " + code);
    }
}
