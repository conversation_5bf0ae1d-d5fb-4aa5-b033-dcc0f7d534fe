package com.xyy.saas.inquiry.pojo.medical;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:chenxiaoyi
 */
@Data
@Builder
@Schema(description = "医保订单扩展信息ext")
@AllArgsConstructor
@NoArgsConstructor
public class MedicalInsuranceOrderExtDto implements Serializable {

    @Schema(description = "基本医疗保险统筹基金支出")
    private BigDecimal hifpPay;

    @Schema(description = "居民大病保险资金支出")
    private BigDecimal hifmiPay;

    @Schema(description = "职工大额医疗费用补助基金支出")
    private BigDecimal hifobPay;

    @Schema(description = "企业补充医疗保险基金支出")
    private BigDecimal hifesPay;

    @Schema(description = "公务员医疗补助资金支出")
    private BigDecimal cvlservPay;

    @Schema(description = "医疗救助基金支出")
    private BigDecimal mafPay;

    @Schema(description = "伤残人员医疗保障基金支出")
    private BigDecimal hifdmPay;

    @Schema(description = "全自费金额")
    private BigDecimal fulamtOwnpayAmt;

    @Schema(description = "符合政策范围金额")
    private BigDecimal inscpScpAmt;

    @Schema(description = "超限价自费费用")
    private BigDecimal overlmtSelfpay;

    @Schema(description = "先行自付金额")
    private BigDecimal preselfpayAmt;

    @Schema(description = "实际支付起付线")
    private BigDecimal actPayDedc;

    @Schema(description = "基本医疗保险统筹基金支付比例")
    private BigDecimal poolPropSelfpay;

}
