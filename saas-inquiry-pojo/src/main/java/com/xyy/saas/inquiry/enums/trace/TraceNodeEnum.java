package com.xyy.saas.inquiry.enums.trace;

import lombok.Getter;
import org.apache.commons.lang3.StringUtils;

/**
 * 链路追踪节点枚举
 * 定义所有业务流程中的节点
 */
@Getter
public enum TraceNodeEnum {

    // 问诊单创建相关节点
    CREATE_INQUIRY("CREATE_INQUIRY", "用户请求发起问诊", "com.xyy.saas.inquiry.kernel.trace.parser.InquiryCreateTraceParser", "com.xyy.saas.inquiry.kernel.trace.parser.InquiryTraceContentParser"),
    PRE_CHECK("PRE_CHECK", "问诊前置校验", "", "com.xyy.saas.inquiry.kernel.trace.parser.InquiryTraceContentParser"),
    ASSIGN_HOSPITAL("ASSIGN_HOSPITAL", "选定问诊医院", "", "com.xyy.saas.inquiry.kernel.trace.parser.InquiryTraceContentParser"),
    ASSIGN_DEPT("ASSIGN_DEPT", "选定问诊科室", "", "com.xyy.saas.inquiry.kernel.trace.parser.InquiryTraceContentParser"),
    ASSIGN_TYPE("ASSIGN_TYPE", "选定问诊开方类型", "", "com.xyy.saas.inquiry.kernel.trace.parser.InquiryTraceContentParser"),
    // 自动开方判定相关节点
    AUOTO_INQUIRY_ALLERGIC("AUOTO_INQUIRY_ALLERGIC", "自动开方判定-过敏史", "com.xyy.saas.inquiry.kernel.trace.parser.AssignInquiryTypeTraceParser", "com.xyy.saas.inquiry.kernel.trace.parser.InquiryTraceContentParser"),
    AUOTO_INQUIRY_LIVERKIDNEY("AUOTO_INQUIRY_LIVERKIDNEY", "自动开方判定-肝肾功能", "com.xyy.saas.inquiry.kernel.trace.parser.AssignInquiryTypeTraceParser", "com.xyy.saas.inquiry.kernel.trace.parser.InquiryTraceContentParser"),
    AUOTO_INQUIRY_PREGNANCYLACTATION("AUOTO_INQUIRY_PREGNANCYLACTATION", "自动开方判定-妊娠哺乳期", "com.xyy.saas.inquiry.kernel.trace.parser.AssignInquiryTypeTraceParser", "com.xyy.saas.inquiry.kernel.trace.parser.InquiryTraceContentParser"),
    AUOTO_INQUIRY_FORCETOREAL("AUOTO_INQUIRY_FORCETOREAL", "自动开方判定-强制走真人", "com.xyy.saas.inquiry.kernel.trace.parser.AssignInquiryTypeTraceParser", "com.xyy.saas.inquiry.kernel.trace.parser.InquiryTraceContentParser"),
    AUOTO_INQUIRY_SPECIALAGE("AUOTO_INQUIRY_SPECIALAGE", "自动开方判定-特殊年龄区间权重", "com.xyy.saas.inquiry.kernel.trace.parser.AssignInquiryTypeTraceParser","com.xyy.saas.inquiry.kernel.trace.parser.InquiryTraceContentParser"),
    AUOTO_INQUIRY_PRODUCT_USAGEDOSAGE("AUOTO_INQUIRY_PRODUCT_USAGEDOSAGE", "自动开方判定-用法用量缺失、临时商品", "com.xyy.saas.inquiry.kernel.trace.parser.AssignInquiryTypeTraceParser","com.xyy.saas.inquiry.kernel.trace.parser.InquiryTraceContentParser"),
    AUOTO_INQUIRY_MULTIDIAGNOSE("AUOTO_INQUIRY_MULTIDIAGNOSE", "自动开方判定-多诊断", "com.xyy.saas.inquiry.kernel.trace.parser.AssignInquiryTypeTraceParser","com.xyy.saas.inquiry.kernel.trace.parser.InquiryTraceContentParser"),
    AUOTO_INQUIRY_MANYAUTOVIDEO("AUOTO_INQUIRY_MANYAUTOVIDEO", "自动开方判定-24小时内存在录屏问诊", "com.xyy.saas.inquiry.kernel.trace.parser.AssignInquiryTypeTraceParser","com.xyy.saas.inquiry.kernel.trace.parser.InquiryTraceContentParser"),
    AUOTO_INQUIRY_AUTOVIDEOWEIGHT("AUOTO_INQUIRY_AUTOVIDEOWEIGHT", "自动开方判定-录屏问诊权重", "com.xyy.saas.inquiry.kernel.trace.parser.AssignInquiryTypeTraceParser","com.xyy.saas.inquiry.kernel.trace.parser.InquiryTraceContentParser"),

    SAVE_INQUIRY_AND_PUSH_RECEPTIONAREA("SAVE_INQUIRY_AND_PUSH_RECEPTIONAREA", "保存问诊单据信息", "", "com.xyy.saas.inquiry.kernel.trace.parser.InquiryTraceContentParser"),

    RECEPTION_AREA_DISTRIBUTE("RECEPTION_AREA_DISTRIBUTE", "问诊推入接诊大厅", "", "com.xyy.saas.inquiry.kernel.trace.parser.HospitalTraceContentParser"),

    MATCH_DOCTOR("MATCH_DOCTOR", "接诊大厅为问诊分派医生", "", "com.xyy.saas.inquiry.kernel.trace.parser.HospitalTraceContentParser"),

    // 医生过滤节点
    DOCTOR_FILTER_MEDICARE("DOCTOR_FILTER_MEDICARE", "自动开方医生医保编码过滤", "", "com.xyy.saas.inquiry.kernel.trace.parser.HospitalTraceContentParser"),
    DOCTOR_FILTER_INTERVAL("DOCTOR_FILTER_INTERVAL", "自动开方医生签名使用间隔过滤医生", "", "com.xyy.saas.inquiry.kernel.trace.parser.HospitalTraceContentParser"),
    DOCTOR_FILTER_PROVINCE("DOCTOR_FILTER_PROVINCE", "自动开方同省医生过滤", "", "com.xyy.saas.inquiry.kernel.trace.parser.HospitalTraceContentParser"),
    DOCTOR_FILTER_REPEATDISTRIBUTE("DOCTOR_FILTER_REPEATDISTRIBUTE", "真人问诊已派单医生过滤", "", "com.xyy.saas.inquiry.kernel.trace.parser.HospitalTraceContentParser"),
    DOCTOR_FILTER_GRABFIRST("DOCTOR_FILTER_GRABFIRST", "真人问诊抢单优先模式过滤（默认不开启）", "", "com.xyy.saas.inquiry.kernel.trace.parser.HospitalTraceContentParser"),
    DOCTOR_FILTER_SEND_NUM("DOCTOR_FILTER_SEND_NUM", "问诊派单医生数量过滤", "", "com.xyy.saas.inquiry.kernel.trace.parser.HospitalTraceContentParser"),
    DOCTOR_FILTER_AUTOGRABCHECK("DOCTOR_FILTER_AUTOGRABCHECK", "自动抢单医生检查过滤", "", "com.xyy.saas.inquiry.kernel.trace.parser.HospitalTraceContentParser"),
    DOCTOR_FILTER_AUTOVIDEOCHOOSE("DOCTOR_FILTER_AUTOVIDEOCHOOSE", "视频自动开方医生录屏选取", "", "com.xyy.saas.inquiry.kernel.trace.parser.HospitalTraceContentParser"),

    // 医生调度相关
    DOCTOR_DISTRIBUTE_AUTO_INQUIRY("DOCTOR_DISTRIBUTE_AUTO_INQUIRY", "自动开方问诊医生调度","", "com.xyy.saas.inquiry.kernel.trace.parser.HospitalTraceContentParser"),
    DOCTOR_DISTRIBUTE_MANUAL_INQUIRY("DOCTOR_DISTRIBUTE_MANUAL_INQUIRY", "真人问诊医生调度","", "com.xyy.saas.inquiry.kernel.trace.parser.HospitalTraceContentParser"),

    // 医生接诊相关节点
    DOCTOR_RECEPTION_AUTO_INQUIRY("DOCTOR_RECEPTION_AUTO_INQUIRY", "自动开方医生接诊", "", "com.xyy.saas.inquiry.kernel.trace.parser.HospitalTraceContentParser"),
    DOCTOR_RECEPTION_INQUIRY("DOCTOR_RECEPTION_INQUIRY", "真人医生接诊", "", "com.xyy.saas.inquiry.kernel.trace.parser.HospitalTraceContentParser"),
    DOCTOR_AUTOAUTOGRAB_INQUIRY("DOCTOR_AUTOAUTOGRAB_INQUIRY", "真人医生自动抢单接诊", "", "com.xyy.saas.inquiry.kernel.trace.parser.HospitalTraceContentParser"),
    DOCTOR_AUTOAUTOGRAB_FOR_INQUIRY_END("DOCTOR_AUTOAUTOGRAB_FOR_INQUIRY_END", "自动抢单医生完诊后去接诊大厅拉单抢单", "", "com.xyy.saas.inquiry.kernel.trace.parser.HospitalTraceContentParser"),

    DOCTOR_RECEIPT_CHANGE("DOCTOR_RECEIPT_CHANGE", "医生相关变化", "com.xyy.saas.inquiry.kernel.trace.parser.DoctorChangeTraceParser", "com.xyy.saas.inquiry.kernel.trace.parser.HospitalTraceContentParser"),


    // 处方相关节点
    ISSUE_PRESCRIPTION_AUTO_INQUIRY("ISSUE_PRESCRIPTION_AUTO_INQUIRY", "自动开方问诊开具处方", "", "com.xyy.saas.inquiry.kernel.trace.parser.HospitalTraceContentParser"),
    DOCTOR_ISSUE_PRESCRIPTION("DOCTOR_ISSUE_PRESCRIPTION", "医生开具处方", "", "com.xyy.saas.inquiry.kernel.trace.parser.HospitalTraceContentParser"),
    DOCTOR_CANCEL_PRESCRIPTION("DOCTOR_CANCEL_PRESCRIPTION", "医生取消开方", "", "com.xyy.saas.inquiry.kernel.trace.parser.HospitalTraceContentParser"),
    PRESCRIPTION_ISSUE_TIME_OUT("PRESCRIPTION_ISSUE_TIME_OUT", "医生开方超时取消问诊", "", "com.xyy.saas.inquiry.kernel.trace.parser.HospitalTraceContentParser"),

    // 患者相关节点
    PATIENT_CANCEL_INQUIRY("PATIENT_CANCEL_INQUIRY", "患者取消问诊", "", "com.xyy.saas.inquiry.kernel.trace.parser.InquiryTraceContentParser"),
    
    // 问诊超时无医生接诊取消
    TIMEOUT_CANCEL_INQUIRY("TIMEOUT_CANCEL_INQUIRY", "问诊超时未接诊取消", "", "com.xyy.saas.inquiry.kernel.trace.parser.InquiryTraceContentParser");
    
    private final String code;
    private final String name;
    private final String parser;
    private final String contentParser;

    
    TraceNodeEnum(String code, String name, String parser, String contentParser) {
        this.code = code;
        this.name = name;
        this.parser = parser;
        this.contentParser = contentParser;
    }

    /**
     * 根据nodeCode查找对应的TraceNodeEnum
     *
     * @param nodeCode 节点编码
     * @return TraceNodeEnum枚举值
     */
    public static TraceNodeEnum findTraceNodeEnumByCode(String nodeCode) {
        if (StringUtils.isBlank(nodeCode)) {
            return null;
        }

        for (TraceNodeEnum nodeEnum : TraceNodeEnum.values()) {
            if (nodeCode.equals(nodeEnum.getCode())) {
                return nodeEnum;
            }
        }
        return null;
    }
} 