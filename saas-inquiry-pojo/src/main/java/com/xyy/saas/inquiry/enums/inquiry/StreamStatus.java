package com.xyy.saas.inquiry.enums.inquiry;

public enum StreamStatus {

    /**
     * 未视频
     */
    NOT_STARTED(0, "未视频"),

    /**
     * 已推流
     */
    PUSH_STREAM(1, "已推流"),

    /**
     * 已开启转推
     */
    STARTED_PUBLISH_STREAM(2, "已开启转推"),

    /**
     * 已编码
     */
    STOPED_PUBLISH_STREAM(3, "已结束转推");

    private final int code; // 状态代码
    private final String description; // 状态描述

    // 构造函数，用于初始化枚举项的字段
    StreamStatus(int code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取枚举项对应的状态代码
     *
     * @return 状态代码
     */
    public int getCode() {
        return code;
    }

    /**
     * 获取枚举项对应的状态描述
     *
     * @return 状态描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据状态代码获取对应的枚举类型
     *
     * @param code 状态代码
     * @return 对应的枚举类型，如果不存在则返回 null
     */
    public static StreamStatus fromCode(int code) {
        for (StreamStatus status : values()) {
            if (status.getCode() == code) {
                return status;
            }
        }
        return null;
    }


}
