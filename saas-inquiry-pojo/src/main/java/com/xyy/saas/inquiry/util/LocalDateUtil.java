package com.xyy.saas.inquiry.util;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Objects;

/**
 * @Author:chenxiaoyi
 * @Date:2025/04/23 17:27
 */
public class LocalDateUtil {

    /**
     * 将时间戳转换为毫秒，-用于redis zSet score
     *
     * @param time 时间
     * @return
     */
    public static double convertScore(LocalDateTime time) {
        return (double) Objects.requireNonNullElseGet(time, LocalDateTime::now).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
    }


    public static String nowStr() {
        return DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(LocalDateTime.now());
    }

}
