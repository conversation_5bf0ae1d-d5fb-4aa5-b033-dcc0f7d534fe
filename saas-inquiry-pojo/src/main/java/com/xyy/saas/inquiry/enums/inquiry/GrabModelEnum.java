package com.xyy.saas.inquiry.enums.inquiry;

import lombok.Getter;

/**
 * 抢单模式枚举
 */
@Getter
public enum GrabModelEnum {

    /**
     * 抢单优先模式-医生调度派单时，优先过滤出医生队列中开启了自动抢单的医生进行指派问诊单
     */
    GRAB_FIRST(0,"抢单优先"),

    /**
     * 派单优先模式-医生调度派单时，按照正常调度顺序进行调度，如果本次调度中包含开启了自动抢单的医生，则针对此医生进行指派
     */
    DISPATCH_FIRST(1,"派单优先"),
    ;

    private int code;
    private String desc;

    GrabModelEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
