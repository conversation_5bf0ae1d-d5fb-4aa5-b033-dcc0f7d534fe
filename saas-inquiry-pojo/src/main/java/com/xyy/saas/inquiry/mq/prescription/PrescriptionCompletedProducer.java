package com.xyy.saas.inquiry.mq.prescription;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import org.springframework.stereotype.Component;

/**
 * 处方流程完成事件生产者
 *
 * <AUTHOR>
 */
@Component
@EventBusProducer(
    topic = PrescriptionCompletedEvent.TOPIC
)
public class PrescriptionCompletedProducer extends EventBusRocketMQTemplate {

} 