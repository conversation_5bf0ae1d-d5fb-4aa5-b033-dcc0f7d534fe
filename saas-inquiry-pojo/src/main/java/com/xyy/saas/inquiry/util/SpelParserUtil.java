package com.xyy.saas.inquiry.util;

import lombok.extern.slf4j.Slf4j;
import org.springframework.expression.ParserContext;
import org.springframework.expression.common.TemplateParserContext;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;

/**
 * spel表达式解析工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class SpelParserUtil {

    private static final SpelExpressionParser spelExpressionParser = new SpelExpressionParser();
    private static final ParserContext PARSER_CONTEXT = new TemplateParserContext();


    public static boolean parseBoolean(String expression, Object target) {
        try {
            return Boolean.TRUE.equals(spelExpressionParser.parseExpression(expression).getValue(target, Boolean.class));
        } catch (Exception e) {
            log.error("SpelParserUtil.parseBoolean,解析失败,expression:{},target:{},msg:{}", expression, target, e.getMessage(), e);
            return false;
        }
    }

    public static <T> T invoke(final Object target, String expression, Class<T> returnTypeClass) {
        return spelExpressionParser.parseExpression(expression, PARSER_CONTEXT).getValue(new StandardEvaluationContext(target), returnTypeClass);
    }

}
