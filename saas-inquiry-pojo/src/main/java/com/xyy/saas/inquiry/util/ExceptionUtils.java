package com.xyy.saas.inquiry.util;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.apache.commons.lang3.StringUtils;

/**
 * @Author:chenxiaoyi
 * @Date:2025/05/21 19:55
 */
public class ExceptionUtils {

    public static String getRpcServiceExceptionMsg(Throwable ex) {
        // 兼容处理ServiceException
        if (StringUtils.contains(ex.getMessage(), ServiceException.class.getName())) {
            Matcher matcher = Pattern.compile("ServiceException\\(super=([^,]+),\\s*code=(\\d+),\\s*message=([\\s\\S]*?)\\)").matcher(ex.getMessage());
            if (matcher.find()) {
                return matcher.group(3);
            }
        }
        return ex.getMessage();
    }

}
