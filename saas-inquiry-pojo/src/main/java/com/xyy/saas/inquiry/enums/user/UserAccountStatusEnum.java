package com.xyy.saas.inquiry.enums.user;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import java.util.Arrays;

/**
 * @Author: xucao
 * @DateTime: 2025/4/9 11:33
 * @Description: 用户账号状态 0 启用  1 禁用  2 注销
 **/
@Getter
public enum UserAccountStatusEnum implements IntArrayValuable {
    ENABLE(0, "启用"),
    DISABLE(1, "禁用"),
    QUIT(2, "注销");

    private final int code;
    private final String desc;

    UserAccountStatusEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(UserAccountStatusEnum::getCode).toArray();

    public static String getDescByCode(Integer integer) {
        for (UserAccountStatusEnum value : values()) {
            if (value.getCode() == integer) {
                return value.getDesc();
            }
        }
        return null;
    }

    @Override
    public int[] array() {
        return ARRAYS;
    }
}
