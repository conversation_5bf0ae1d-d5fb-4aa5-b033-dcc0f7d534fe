package com.xyy.saas.inquiry.enums.prescription.external;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import java.util.Arrays;

/**
 * 外配处方审核状态
 */
@Getter
@RequiredArgsConstructor
public enum ExternalPrescriptionAuditStatusEnum implements IntArrayValuable {


    PENDING(0, "待审核"),

    APPROVED(1, "审核通过"),

    REJECTED(2, "审核不通过"),

    ;

    // 枚举类的私有成员变量
    private final int code;
    private final String description;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(ExternalPrescriptionAuditStatusEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    // 根据整数值获取枚举实例的静态方法
    public static ExternalPrescriptionAuditStatusEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElse(PENDING);
    }
}
