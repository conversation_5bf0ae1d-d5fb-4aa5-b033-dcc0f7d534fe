package com.xyy.saas.inquiry.enums.prescription;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import java.util.Arrays;
import java.util.Objects;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * 远程审方审方类型
 */
@Getter
@RequiredArgsConstructor
public enum PrescriptionRemoteAuditTypeEnum implements IntArrayValuable {

    HEAD_AUDIT(0, "总部审方"),

    STORE_AUDIT(1, "门店审方"),

    ;

    // 枚举类的私有成员变量
    private final int code;
    private final String description;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(PrescriptionRemoteAuditTypeEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    // 根据整数值获取枚举实例的静态方法
    public static PrescriptionRemoteAuditTypeEnum fromCode(Integer code) {
        return Arrays.stream(values())
            .filter(type -> Objects.equals(type.getCode(), code))
            .findFirst()
            .orElse(HEAD_AUDIT);
    }
}

