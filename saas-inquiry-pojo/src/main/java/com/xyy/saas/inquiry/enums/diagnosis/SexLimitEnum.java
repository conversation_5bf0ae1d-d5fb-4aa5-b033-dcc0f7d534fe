package com.xyy.saas.inquiry.enums.diagnosis;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 处方审核人类型
 *
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/8/30 上午11:34
 */
@Getter
@RequiredArgsConstructor
public enum SexLimitEnum implements IntArrayValuable {

    UN_LIMIT(0, "无限制"),

    MAN(1, "限制男限"),

    WOMAN(2, "限制女性"),

    ;

    private final int code;

    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(SexLimitEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 枚举值code
     * @return 枚举值
     */
    public static SexLimitEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElse(UN_LIMIT);
    }
}