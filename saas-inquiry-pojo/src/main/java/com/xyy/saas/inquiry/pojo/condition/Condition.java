package com.xyy.saas.inquiry.pojo.condition;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author:ch<PERSON><PERSON><PERSON>i
 * @Date:2024/11/15 18:06
 */
@Data
public class Condition implements Serializable {

    /**
     * 条件是否并列, 默认为or
     */
    private boolean and;
    /**
     * 规则
     */
    private List<ConditionRule> rules;
    /**
     * 序号
     */
    private int seq;
}
