package com.xyy.saas.inquiry.enums.prescription.external;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * 医保处方状态
 */
@Getter
@RequiredArgsConstructor
public enum MedicarePrescriptionStatusEnum implements IntArrayValuable {


    INIT(0, "初始"),

    EFFECTIVE(1, "有效"),

    INVALID(2, "已失效"),

    RESCINDED(3, "已撤销"),

    ;

    // 枚举类的私有成员变量
    private final int code;
    private final String description;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(MedicarePrescriptionStatusEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    // 根据整数值获取枚举实例的静态方法
    public static MedicarePrescriptionStatusEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElse(INIT);
    }
}
