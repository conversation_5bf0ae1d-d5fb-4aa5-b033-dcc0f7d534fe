package com.xyy.saas.inquiry.mq.prescription.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Author: cxy
 * @Description: 门店处方推送远程审方消息
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PrescriptionPushRemoteAuditEventMessage implements Serializable {

    /**
     * 处方单号
     */
    private String prescriptionPref;

    /**
     * 门店Id
     */
    private Long tenantId;

    /**
     * 处方图片地址
     */
    private String prescriptionImageUrl;

}
