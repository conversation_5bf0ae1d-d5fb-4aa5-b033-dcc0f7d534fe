package com.xyy.saas.inquiry.enums.signature;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * @Desc 签章合同类型
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum ContractTypeEnum {

    PRESCRIPTION(1, "问诊处方", "HYWZ"),

    AUTHORIZATION_CONTRACT(2, "CA授权合同", "CASQ"),

    ;

    private final int code;

    private final String desc;

    private final String prefix;

    public static ContractTypeEnum fromStatusCode(int code) {
        return Arrays.stream(values())
            .filter(value -> value.getCode() == code)
            .findFirst()
            .orElse(PRESCRIPTION);
    }


}
