package com.xyy.saas.inquiry.mq.prescription.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * @Author: cxy
 * @Description: 处方审核通过事件
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PrescriptionApprovedMessage implements Serializable {

    /**
     * 处方单号
     */
    private String prescriptionPref;

    /**
     * 问诊单号
     */
    private String inquiryPref;
}
