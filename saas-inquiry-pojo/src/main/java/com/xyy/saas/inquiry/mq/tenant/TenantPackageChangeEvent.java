package com.xyy.saas.inquiry.mq.tenant;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class TenantPackageChangeEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "TENANT_PACKAGE_CHANGE";

    private TenantPackageCostMessageDto msg;

    @JsonCreator
    public TenantPackageChangeEvent(@JsonProperty("msg") TenantPackageCostMessageDto msg) {
        this.msg = msg;
    }


    @Override
    public String getTag() {
        return "";
    }

}
