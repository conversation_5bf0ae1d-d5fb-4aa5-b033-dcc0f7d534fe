package com.xyy.saas.inquiry.mq.user;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 用户基础信息修改mq
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class UserBaseInfoChangeEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "USER_BASE_INFO_CHANGE";

    private UserBaseInfoDto msg;

    @JsonCreator
    public UserBaseInfoChangeEvent(@JsonProperty("msg") UserBaseInfoDto msg) {
        this.msg = msg;
    }

    @Override
    public String getTag() {
        return "";
    }
}
