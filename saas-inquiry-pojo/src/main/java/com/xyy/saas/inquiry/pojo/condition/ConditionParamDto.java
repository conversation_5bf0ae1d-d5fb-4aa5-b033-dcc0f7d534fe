package com.xyy.saas.inquiry.pojo.condition;

import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 特定处方笺模板参数dto
 */
@Data
@ToString(callSuper = true)
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ConditionParamDto implements Serializable {

    /**
     * 门店所在区域
     */
    private List<String> areaCodes;
    /**
     * 患者年龄
     */
    private String age;
    /**
     * 药品分类
     */
    private List<String> drugCategories;

    /**
     * 药品类型
     */
    private Integer medicineType;

    /**
     * 开方科室
     */
    private String dept;
    /**
     * 是否慢病
     */
    private Integer slowDisease;

    /**
     * 处方类型 eg:职工,慢病
     */
    private Integer prescriptionType;

    /**
     * 诊断数量
     */
    private Integer diagnosisCount;

    /**
     * 诊断跨科室数量
     */
    private Integer diagnosisTransDeptCount;


}