package com.xyy.saas.inquiry.pojo.inquiry;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * @Author: xucao
 * @Date: 2025/02/06 16:02
 * @Description: 图文问诊问答信息
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class QuestionAnswerInfoDto implements Serializable {

    @Schema(description = "问题")
    private String question;

    @Schema(description = "提问时间")
    @DateTimeFormat()
    private LocalDateTime questionTime;

    @Schema(description = "回答")
    private String answer;

    @Schema(description = "答复时间")
    private LocalDateTime answerTime;
}
