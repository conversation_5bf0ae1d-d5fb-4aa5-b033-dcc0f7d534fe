package com.xyy.saas.inquiry.enums.patient;

import lombok.Getter;

/**
 * 枚举名：PatientQuerySenceEnum
 * 功能描述：多维度查询患者信息枚举
 * 作者：xucao
 * 创建时间：2025/3/4 09:40
 * 版本：v1.0
 */
@Getter
public enum PatientQuerySenceEnum {
    TENANT_QUERY(0, "租户查询"),
    DOCTOR_QUERY(1, "医生查询"),
    PHARMACIST_QUERY(2, "药师查询"),
    SOCIAL_USER_QUERY(3, "社交用户查询");

    private final int code;
    private final String desc;

    PatientQuerySenceEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}