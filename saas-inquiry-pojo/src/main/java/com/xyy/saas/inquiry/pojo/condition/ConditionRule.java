package com.xyy.saas.inquiry.pojo.condition;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author:ch<PERSON><PERSON><PERSON>i
 * @Date:2024/11/15 18:06
 */
@Data
public class ConditionRule implements Serializable {

    /**
     * 条件类型 {@link ConditionRuleType}
     */
    private String type;

    /**
     * 条件关系 {@link ConditionRuleOp}
     */
    private String op;

    /**
     * 条件目标值
     */
    private String value;

}
