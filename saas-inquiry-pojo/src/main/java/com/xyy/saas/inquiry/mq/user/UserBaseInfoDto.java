package com.xyy.saas.inquiry.mq.user;

import java.io.Serializable;
import lombok.Builder;
import lombok.Data;

/**
 * 用户基础信息修改mq
 *
 * <AUTHOR>
 */
@Builder
@Data
public class UserBaseInfoDto implements Serializable {

    /**
     * 门店id
     */
    private Long tenantId;

    private Long userId;
    /**
     * 姓名
     */
    private String nickname;
    /**
     * 身份证号
     */
    private String idCard;
    /**
     * 性别
     */
    private Integer sex;
    /**
     * 手机号
     */
    private String mobile;

}
