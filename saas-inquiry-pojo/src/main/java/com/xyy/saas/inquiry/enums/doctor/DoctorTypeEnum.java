package com.xyy.saas.inquiry.enums.doctor;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * doctor类型
 *
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/8/30 上午11:34
 */
@Getter
@RequiredArgsConstructor
public enum DoctorTypeEnum implements IntArrayValuable {

    DOCTOR(1, "医生"),

    PHARMACIST(2, "药师");

    private final int code;

    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(DoctorTypeEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    /**
     * 根据code获取枚举值
     *
     * @param code 枚举值code
     * @return 枚举值
     */
    public static DoctorTypeEnum fromCode(int code) {
        return Arrays.stream(values())
            .filter(type -> type.getCode() == code)
            .findFirst()
            .orElseThrow(() -> new IllegalArgumentException("Invalid DoctorTypeEnum code: " + code));
    }
}