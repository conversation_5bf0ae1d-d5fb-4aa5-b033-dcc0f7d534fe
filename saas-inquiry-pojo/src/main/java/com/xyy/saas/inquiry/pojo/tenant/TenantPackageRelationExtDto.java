package com.xyy.saas.inquiry.pojo.tenant;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Schema(description = "门店套餐包扩展信息ext")
@Accessors(chain = true)
public class TenantPackageRelationExtDto implements Serializable {


    @Schema(description = "老系统套餐包guid")
    private String guid;

    @Schema(description = "老系统套餐包规则")
    private String countLimitRule;
}
