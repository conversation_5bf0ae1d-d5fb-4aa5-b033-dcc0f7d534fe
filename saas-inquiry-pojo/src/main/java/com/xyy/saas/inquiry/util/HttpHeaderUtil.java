package com.xyy.saas.inquiry.util;

import cn.hutool.core.util.NumberUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * HTTP 请求头处理工具类
 */
public class HttpHeaderUtil {

    public static HttpServletRequest getRequest() {
        RequestAttributes var0 = RequestContextHolder.getRequestAttributes();
        if (!(var0 instanceof ServletRequestAttributes var1)) {
            return null;
        } else {
            return var1.getRequest();
        }
    }

    public static String getHeaderParam(String key) {
        HttpServletRequest request = getRequest();
        if (request == null) {
            return null;
        }
        return request.getHeader(key);
    }

    /**
     * 获取客户端渠道类型
     *
     * @return
     */
    public static Integer getClientChannelType() {
        return getHeaderParam("clientChannelType") == null ? null : NumberUtil.parseInt(getHeaderParam("clientChannelType"), null);
    }

}