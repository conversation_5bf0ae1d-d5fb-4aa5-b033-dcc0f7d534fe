package com.xyy.saas.inquiry.enums.migration;

/**
 * 迁移类型
 */
public enum MigrationTypeEnum {
    STORE(1, "门店"),
    PACKAGE(2, "套餐");

    private final int code;
    private final String desc;

    MigrationTypeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public int getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}
