package com.xyy.saas.inquiry.enums.signature;

import lombok.Getter;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.Objects;

public class FddCaConstantEnum {

    @Getter
    public enum CertifyStatusEnum {
        NO_FINISH(0, "未完成"),
        FINISH(1, "完成"),
        FAILED(2, "失败");
        int code;
        String msg;

        CertifyStatusEnum(int code, String msg) {
            this.code = code;
            this.msg = msg;
        }
    }

    @Getter
    public enum SignatureStatus {
        NO_FINISH(0, "未完成"),
        FINISH(1, "完成");
        int code;
        String msg;

        SignatureStatus(int code, String msg) {
            this.code = code;
            this.msg = msg;
        }
    }

    @Getter
    public enum AgreementStatus {
        NO_FINISH(0, "未完成"),
        FINISH(1, "完成");
        int code;
        String msg;

        AgreementStatus(int code, String msg) {
            this.code = code;
            this.msg = msg;
        }
    }

    @Getter
    public enum PartTimeAgreementStatus {
        TO_BE_REVIEWED(2, "待审核"),
        NO_FINISH(0, "未完成"),
        FINISH(1, "完成");
        int code;
        String msg;

        PartTimeAgreementStatus(int code, String msg) {
            this.code = code;
            this.msg = msg;
        }
    }

    @Getter
    public enum AuthorizeFreeSignStatus {
        UNAUTHORIZED(0, "未授权"),
        AUTHORIZED(1, "已授权");
        int code;
        String msg;

        AuthorizeFreeSignStatus(int code, String msg) {
            this.code = code;
            this.msg = msg;
        }
    }

    @Getter
    public enum VisaFreeStatusEnum {

        UNDONE(1, "未完成"),
        DONE(2, "已完成"),
        AUTHORIZATION_DEADLINE(3, "授权临期");

        Integer code;
        String msg;

        VisaFreeStatusEnum(Integer code, String msg) {
            this.code = code;
            this.msg = msg;
        }
    }

    /**
     * 是否免签且有效,免签有效期冗余300s
     *
     * @param authorizeFreeSignStatus
     * @param authorizeFreeSignDdl
     * @return true 表示免签且有效
     */
    public static boolean isFreeSignAndValid(Integer authorizeFreeSignStatus, LocalDateTime authorizeFreeSignDdl) {
        return Objects.equals(authorizeFreeSignStatus, AuthorizeFreeSignStatus.AUTHORIZED.getCode()) && authorizeFreeSignDdl != null
            && Duration.between(LocalDateTime.now(), authorizeFreeSignDdl).getSeconds() > 300;
    }

    public static boolean isFreeSignAndValid(Integer authorizeFreeSignStatus, LocalDateTime authorizeFreeSignDdl, int time) {
        return Objects.equals(authorizeFreeSignStatus, AuthorizeFreeSignStatus.AUTHORIZED.getCode()) && authorizeFreeSignDdl != null
            && Duration.between(LocalDateTime.now(), authorizeFreeSignDdl).getSeconds() > time;
    }

}
