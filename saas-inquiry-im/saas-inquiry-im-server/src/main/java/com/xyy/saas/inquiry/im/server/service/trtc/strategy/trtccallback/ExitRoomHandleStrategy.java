package com.xyy.saas.inquiry.im.server.service.trtc.strategy.trtccallback;

import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.enums.inquiry.StreamStatus;
import com.xyy.saas.inquiry.enums.tencent.TencentTrtcCallBackEventEnum;
import com.xyy.saas.inquiry.im.server.controller.app.callback.vo.TencentTrtcCallBackReqVO;
import com.xyy.saas.inquiry.im.server.controller.app.callback.vo.TencentTrtcCallBackReqVO.EventInfo;
import com.xyy.saas.inquiry.im.server.mq.message.InquiryTrtcEndEvent;
import com.xyy.saas.inquiry.im.server.mq.producer.InquiryTrtcEndProducer;
import com.xyy.saas.inquiry.im.server.service.tencent.TencentTrtcClient;
import com.xyy.saas.inquiry.mq.inquiry.dto.InquiryEndMessage;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author: xucao
 * @Date: 2024/12/09 17:06
 * @Description: 退出房间回调处理
 */
@Component
@Slf4j
public class ExitRoomHandleStrategy extends TencentTrtcCallBackHandleStrategy {

    @Resource
    private InquiryApi inquiryApi;


    @Resource
    private TencentTrtcClient tencentTrtcClient;

    @Resource
    private InquiryTrtcEndProducer inquiryTrtcEndProducer;


    /**
     * 策略执行器
     *
     * @param callBackReqVO 回调参数
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public Boolean execute(TencentTrtcCallBackReqVO callBackReqVO) {
        log.info("退出房间事件处理:{}", JSON.toJSONString(callBackReqVO));
        // 校验参数
        callBackReqVO.checkEvent();
        // 事件参数
        EventInfo eventInfo = callBackReqVO.getEventInfo();
        // 根据房间号查询问诊单信息
        InquiryRecordDto inquiryRecordDto = inquiryApi.getInquiryDtoByPref(eventInfo.getRoomId());
        if(inquiryRecordDto == null){
            log.warn("问诊单号:{},退出房间事件处理:未查询到问诊单信息", eventInfo.getRoomId());
            return Boolean.TRUE;
        }
        // 1V1单聊场景，无论是是谁退出房间都是结束问诊，需要停止混流转推
        tencentTrtcClient.stopPublishStreamCdn(inquiryRecordDto.getTranscodingId());
        // 回更问诊单相关参数
        Boolean updateSuccess =  inquiryApi.updateInquiryEndTime(InquiryRecordDto.builder().id(inquiryRecordDto.getId()).endTime(LocalDateTime.now()).streamStatus(StreamStatus.STOPED_PUBLISH_STREAM.getCode()).build());
        if(updateSuccess){
            // 发送问诊trtc通话结束的mq
            inquiryTrtcEndProducer.sendMessage(InquiryTrtcEndEvent.builder().msg(InquiryEndMessage.builder().inquiryPref(inquiryRecordDto.getPref()).build()).build());
        }
        return Boolean.TRUE;
    }

    /**
     * 获取策略对应的事件
     *
     * @return 事件
     */
    @Override
    public TencentTrtcCallBackEventEnum getEvent() {
        return TencentTrtcCallBackEventEnum.EVENT_TYPE_EXIT_ROOM;
    }
}
