package com.xyy.saas.inquiry.im.server.controller.app.callback;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.xyy.saas.inquiry.im.server.controller.app.callback.vo.TencentCallBackRespVO;
import com.xyy.saas.inquiry.im.server.controller.app.callback.vo.TencentImCallBackReqVO;
import com.xyy.saas.inquiry.im.server.service.message.InquiryImMessageService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import java.util.Map;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: xucao
 * @Date: 2024/11/28 17:19
 * @Description: 腾讯Im 回调
 */
@Tag(name = "app - 腾讯IM回调接口")
@RestController
@RequestMapping("/im/callback")
@Validated
public class TencentImCallBackController {

    private static final Logger log = LoggerFactory.getLogger(TencentImCallBackController.class);
    @Resource
    private InquiryImMessageService inquiryImMessageService;

    @PostMapping("/message")
    @Operation(summary = "IM 消息回调")
    @PermitAll
    @ApiAccessLog(enable = false)
    public TencentCallBackRespVO onCallBackMessage(@RequestBody Map<String, Object> params) throws JsonProcessingException {
        log.info("腾讯IM回调,入参：{}", JSON.toJSONString(params));
        ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule()).configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
        TencentImCallBackReqVO reqVO = objectMapper.readValue(JSON.toJSONString(params), TencentImCallBackReqVO.class);
        inquiryImMessageService.onCallBackMessage(reqVO);
        return TencentCallBackRespVO.callBackSuccess();
    }

    @PostMapping("/alert")
    @Operation(summary = "告警通知")
    @PermitAll
    public TencentCallBackRespVO alert(@RequestBody Map<String, Object> params) throws JsonProcessingException {
        log.info("Skywalking推送告警,入参：{}", JSON.toJSONString(params));
        return TencentCallBackRespVO.callBackSuccess();
    }
}
