<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.im.server.dal.mysql.message.InquiryImArchiveMessageMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->

  <sql id="Base_Column_List">
    msg_key, msg_seq, inquiry_pref, msg_from, msg_to, msg_time, read_offset, msg_body, msg_ext, deleted,  create_time, update_time, creator, updater
  </sql>

  <insert id="batchInsertByXml" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO saas_inquiry_im_archive_message
    (
      id , <include refid="Base_Column_List"/>
    )
    VALUES
    <foreach collection="list" index="index" item="item" open="" close="" separator=",">
      (
      #{item.id,jdbcType=BIGINT},
      #{item.msgKey,jdbcType=VARCHAR},
      #{item.msgSeq,jdbcType=BIGINT},
      #{item.inquiryPref,jdbcType=VARCHAR},
      #{item.msgFrom,jdbcType=VARCHAR},
      #{item.msgTo,jdbcType=VARCHAR},
      #{item.msgTime,jdbcType=TIMESTAMP},
      #{item.readOffset,jdbcType=INTEGER},
      #{item.msgBody,jdbcType=VARCHAR},
      #{item.msgExt,jdbcType=VARCHAR},
      0,
      #{item.createTime,jdbcType=TIMESTAMP},
      #{item.updateTime,jdbcType=TIMESTAMP},
      #{item.creator,jdbcType=VARCHAR},
      #{item.updater,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>
</mapper>