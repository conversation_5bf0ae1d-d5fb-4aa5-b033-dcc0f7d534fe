<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.im.server.dal.mysql.message.InquiryImMessageMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->

  <delete id="deleteByInquiryPref" parameterType="java.lang.String">
    delete from saas_inquiry_im_message where inquiry_pref = #{inquiryPref}
  </delete>

  <delete id="physicalDeleteByInquiryPrefList">
    delete from saas_inquiry_im_message where inquiry_pref in
    <foreach collection="inquiryPrefList" index="index"
      item="inquiryPref" open="(" close=")" separator=",">
      #{inquiryPref}
    </foreach>
  </delete>

  <select id="selectLastMessageByInquiryPrefs" resultType="com.xyy.saas.inquiry.im.api.message.dto.InquiryLastMessageDto">
    SELECT
      m1.inquiry_pref AS inquiryPref,
      m1.msg_body AS lastMsg,
      m1.msg_ext AS msgExt
    FROM saas_inquiry_im_message m1
    INNER JOIN (
      SELECT m2.inquiry_pref, MAX(m2.msg_seq) AS max_seq
      FROM saas_inquiry_im_message m2
      INNER JOIN (
        SELECT inquiry_pref, MAX(msg_time) AS max_time
        FROM saas_inquiry_im_message
        WHERE inquiry_pref IN
        <foreach collection="inquiryPrefs" item="inquiryPref" open="(" separator="," close=")">
          #{inquiryPref}
        </foreach>
        GROUP BY inquiry_pref
        ) t1 ON m2.inquiry_pref = t1.inquiry_pref AND m2.msg_time = t1.max_time
      GROUP BY m2.inquiry_pref
      ) t2 ON m1.inquiry_pref = t2.inquiry_pref AND m1.msg_seq = t2.max_seq
    WHERE m1.inquiry_pref IN
    <foreach collection="inquiryPrefs" item="inquiryPref" open="(" separator="," close=")">
      #{inquiryPref}
    </foreach>
  </select>

</mapper>