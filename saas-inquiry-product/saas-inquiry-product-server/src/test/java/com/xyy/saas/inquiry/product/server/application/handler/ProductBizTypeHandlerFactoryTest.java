package com.xyy.saas.inquiry.product.server.application.handler;

import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.server.application.handler.impl.*;
import com.xyy.saas.inquiry.product.server.test.BaseUnitTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

/**
 * 商品业务类型处理器工厂单元测试
 * 
 * <AUTHOR> Assistant
 */
class ProductBizTypeHandlerFactoryTest extends BaseUnitTest {
    
    @Mock
    private AddProductHandler mockAddProductHandler;
    
    @Mock
    private EditProductHandler mockEditProductHandler;
    
    @Mock
    private FirstProductHandler mockFirstProductHandler;
    
    @Mock
    private MidStdlibAddHandler mockMidStdlibAddHandler;
    
    @Mock
    private ChainStoreAddHandler mockChainStoreAddHandler;
    
    private ProductBizTypeHandlerFactory handlerFactory;
    
    @BeforeEach
    @Override
    protected void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 设置处理器支持的业务类型
        when(mockAddProductHandler.supports()).thenReturn(ProductBizTypeEnum.ADD_PRODUCT);
        when(mockEditProductHandler.supports()).thenReturn(ProductBizTypeEnum.EDIT_PRODUCT);
        when(mockFirstProductHandler.supports()).thenReturn(ProductBizTypeEnum.FIRST_PRODUCT);
        when(mockMidStdlibAddHandler.supports()).thenReturn(ProductBizTypeEnum.MID_STDLIB_ADD);
        when(mockChainStoreAddHandler.supports()).thenReturn(ProductBizTypeEnum.CHAIN_STORE_ADD);
        
        List<ProductBizTypeHandler> handlers = Arrays.asList(
            mockAddProductHandler,
            mockEditProductHandler,
            mockFirstProductHandler,
            mockMidStdlibAddHandler,
            mockChainStoreAddHandler
        );
        
        handlerFactory = new ProductBizTypeHandlerFactory(handlers);
    }
    
    @Test
    void testGetHandler_WithValidBizType_ReturnsCorrectHandler() {
        // When & Then
        assertEquals(mockAddProductHandler, handlerFactory.getHandler(ProductBizTypeEnum.ADD_PRODUCT));
        assertEquals(mockEditProductHandler, handlerFactory.getHandler(ProductBizTypeEnum.EDIT_PRODUCT));
        assertEquals(mockFirstProductHandler, handlerFactory.getHandler(ProductBizTypeEnum.FIRST_PRODUCT));
        assertEquals(mockMidStdlibAddHandler, handlerFactory.getHandler(ProductBizTypeEnum.MID_STDLIB_ADD));
        assertEquals(mockChainStoreAddHandler, handlerFactory.getHandler(ProductBizTypeEnum.CHAIN_STORE_ADD));
    }
    
    @Test
    void testGetHandler_WithUnsupportedBizType_ThrowsException() {
        // Given - 没有处理器支持这个业务类型
        ProductBizTypeEnum unsupportedBizType = ProductBizTypeEnum.MID_STDLIB_EDIT;
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> handlerFactory.getHandler(unsupportedBizType)
        );
        
        assertEquals("不支持的业务类型: " + unsupportedBizType, exception.getMessage());
    }
    
    @Test
    void testGetHandler_WithNullBizType_ThrowsException() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> handlerFactory.getHandler(null)
        );
        
        assertEquals("业务类型不能为空", exception.getMessage());
    }
    
    @Test
    void testConstructor_WithEmptyHandlerList_Success() {
        // Given
        List<ProductBizTypeHandler> emptyHandlers = Collections.emptyList();
        
        // When
        ProductBizTypeHandlerFactory emptyFactory = new ProductBizTypeHandlerFactory(emptyHandlers);
        
        // Then
        assertNotNull(emptyFactory);
        
        // 尝试获取处理器应该抛出异常
        assertThrows(
            IllegalArgumentException.class,
            () -> emptyFactory.getHandler(ProductBizTypeEnum.ADD_PRODUCT)
        );
    }
    
    @Test
    void testConstructor_WithNullHandlerList_ThrowsException() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> new ProductBizTypeHandlerFactory(null)
        );
        
        assertEquals("处理器列表不能为空", exception.getMessage());
    }
    
    @Test
    void testGetHandler_WithDuplicateHandlers_UsesFirst() {
        // Given - 创建两个支持相同业务类型的处理器
        ProductBizTypeHandler duplicateHandler = org.mockito.Mockito.mock(ProductBizTypeHandler.class);
        when(duplicateHandler.supports()).thenReturn(ProductBizTypeEnum.ADD_PRODUCT);
        
        List<ProductBizTypeHandler> handlersWithDuplicate = Arrays.asList(
            mockAddProductHandler, // 第一个
            duplicateHandler,       // 重复的
            mockEditProductHandler
        );
        
        ProductBizTypeHandlerFactory factoryWithDuplicate = new ProductBizTypeHandlerFactory(handlersWithDuplicate);
        
        // When
        ProductBizTypeHandler result = factoryWithDuplicate.getHandler(ProductBizTypeEnum.ADD_PRODUCT);
        
        // Then - 应该返回第一个注册的处理器
        assertEquals(mockAddProductHandler, result);
        assertNotEquals(duplicateHandler, result);
    }
    
    @Test
    void testFactoryInitialization_HandlersRegisteredCorrectly() {
        // When - 验证所有业务类型都能找到对应处理器
        assertDoesNotThrow(() -> {
            handlerFactory.getHandler(ProductBizTypeEnum.ADD_PRODUCT);
            handlerFactory.getHandler(ProductBizTypeEnum.EDIT_PRODUCT);
            handlerFactory.getHandler(ProductBizTypeEnum.FIRST_PRODUCT);
            handlerFactory.getHandler(ProductBizTypeEnum.MID_STDLIB_ADD);
            handlerFactory.getHandler(ProductBizTypeEnum.CHAIN_STORE_ADD);
        });
    }
    
    @Test
    void testGetHandler_SameHandlerMultipleCalls_ReturnsSameInstance() {
        // When
        ProductBizTypeHandler handler1 = handlerFactory.getHandler(ProductBizTypeEnum.ADD_PRODUCT);
        ProductBizTypeHandler handler2 = handlerFactory.getHandler(ProductBizTypeEnum.ADD_PRODUCT);
        
        // Then
        assertSame(handler1, handler2);
        assertEquals(mockAddProductHandler, handler1);
        assertEquals(mockAddProductHandler, handler2);
    }
    
    @Test
    void testGetHandler_AllBizTypeEnumValues_Coverage() {
        // 测试所有已支持的业务类型枚举值
        ProductBizTypeEnum[] supportedTypes = {
            ProductBizTypeEnum.ADD_PRODUCT,
            ProductBizTypeEnum.EDIT_PRODUCT,
            ProductBizTypeEnum.FIRST_PRODUCT,
            ProductBizTypeEnum.MID_STDLIB_ADD,
            ProductBizTypeEnum.CHAIN_STORE_ADD
        };
        
        // When & Then - 所有支持的类型都应该能找到处理器
        for (ProductBizTypeEnum bizType : supportedTypes) {
            assertNotNull(handlerFactory.getHandler(bizType), 
                "业务类型 " + bizType + " 应该有对应的处理器");
        }
    }
    
    @Test
    void testHandlerRegistration_OrderIndependent() {
        // Given - 改变处理器注册顺序
        List<ProductBizTypeHandler> reversedHandlers = Arrays.asList(
            mockChainStoreAddHandler,
            mockMidStdlibAddHandler,
            mockFirstProductHandler,
            mockEditProductHandler,
            mockAddProductHandler
        );
        
        ProductBizTypeHandlerFactory reversedFactory = new ProductBizTypeHandlerFactory(reversedHandlers);
        
        // When & Then - 顺序不应该影响结果
        assertEquals(mockAddProductHandler, reversedFactory.getHandler(ProductBizTypeEnum.ADD_PRODUCT));
        assertEquals(mockEditProductHandler, reversedFactory.getHandler(ProductBizTypeEnum.EDIT_PRODUCT));
        assertEquals(mockChainStoreAddHandler, reversedFactory.getHandler(ProductBizTypeEnum.CHAIN_STORE_ADD));
    }
}