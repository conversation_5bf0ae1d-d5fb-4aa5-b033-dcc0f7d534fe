package com.xyy.saas.inquiry.product.server.performance;

import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.server.application.service.ProductApplicationService;
import com.xyy.saas.inquiry.product.server.builder.ProductTestDataBuilder;
import com.xyy.saas.inquiry.product.server.test.BaseIntegrationTest;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.EnabledIfSystemProperty;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 商品服务性能测试
 * 
 * 使用系统属性 -Dperformance.test.enabled=true 来启用性能测试
 * 
 * <AUTHOR> Assistant
 */
@EnabledIfSystemProperty(named = "performance.test.enabled", matches = "true")
class ProductServicePerformanceTest extends BaseIntegrationTest {
    
    private static final Logger log = LoggerFactory.getLogger(ProductServicePerformanceTest.class);
    
    @Resource
    private ProductApplicationService productApplicationService;
    
    @Test
    void testSingleProductCreationPerformance() {
        // Given
        ProductInfoDto dto = getProductBuilder()
            .commonName("性能测试商品")
            .manufacturer("性能测试厂家")
            .productPref("PERF_001")
            .buildDto();
        
        // When - 测量单个商品创建的性能
        long startTime = System.nanoTime();
        Long productId = productApplicationService.saveOrUpdateProduct(dto, ProductBizTypeEnum.ADD_PRODUCT);
        long endTime = System.nanoTime();
        
        // Then
        assertNotNull(productId);
        
        long durationMs = (endTime - startTime) / 1_000_000;
        log.info("单个商品创建耗时: {}ms", durationMs);
        
        // 断言单个商品创建应该在合理时间内完成（例如1秒）
        assertTrue(durationMs < 1000, "单个商品创建耗时过长: " + durationMs + "ms");
    }
    
    @Test
    void testBatchProductCreationPerformance() {
        // Given
        final int batchSize = 100;
        List<ProductInfoDto> products = new ArrayList<>();
        
        for (int i = 0; i < batchSize; i++) {
            products.add(getProductBuilder()
                .commonName("批量商品" + i)
                .productPref("BATCH_" + String.format("%03d", i))
                .buildDto());
        }
        
        // When - 测量批量创建性能
        long startTime = System.nanoTime();
        
        List<Long> productIds = new ArrayList<>();
        for (ProductInfoDto dto : products) {
            Long productId = productApplicationService.saveOrUpdateProduct(dto, ProductBizTypeEnum.ADD_PRODUCT);
            productIds.add(productId);
        }
        
        long endTime = System.nanoTime();
        
        // Then
        assertEquals(batchSize, productIds.size());
        productIds.forEach(id -> assertNotNull(id));
        
        long totalDurationMs = (endTime - startTime) / 1_000_000;
        double avgDurationMs = (double) totalDurationMs / batchSize;
        
        log.info("批量创建{}个商品总耗时: {}ms", batchSize, totalDurationMs);
        log.info("平均单个商品创建耗时: {:.2f}ms", avgDurationMs);
        
        // 断言平均创建时间应该合理
        assertTrue(avgDurationMs < 100, "平均商品创建耗时过长: " + avgDurationMs + "ms");
        assertTrue(totalDurationMs < 10000, "批量创建总耗时过长: " + totalDurationMs + "ms");
    }
    
    @Test
    void testConcurrentProductCreationPerformance() throws InterruptedException {
        // Given
        final int threadCount = 10;
        final int productsPerThread = 20;
        final int totalProducts = threadCount * productsPerThread;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch startLatch = new CountDownLatch(1);
        CountDownLatch completionLatch = new CountDownLatch(threadCount);
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicInteger errorCount = new AtomicInteger(0);
        AtomicLong totalDuration = new AtomicLong(0);
        
        // When - 并发创建商品
        for (int t = 0; t < threadCount; t++) {
            final int threadIndex = t;
            executor.submit(() -> {
                try {
                    startLatch.await(); // 等待统一开始信号
                    
                    long threadStartTime = System.nanoTime();
                    
                    runWithTenant(DEFAULT_TENANT_ID, () -> {
                        for (int i = 0; i < productsPerThread; i++) {
                            try {
                                ProductInfoDto dto = getProductBuilder()
                                    .commonName("并发商品T" + threadIndex + "P" + i)
                                    .productPref("CONCURRENT_T" + threadIndex + "_P" + String.format("%03d", i))
                                    .buildDto();
                                
                                Long productId = productApplicationService.saveOrUpdateProduct(dto, ProductBizTypeEnum.ADD_PRODUCT);
                                if (productId != null) {
                                    successCount.incrementAndGet();
                                }
                            } catch (Exception e) {
                                log.error("并发创建商品失败: T{}P{}", threadIndex, i, e);
                                errorCount.incrementAndGet();
                            }
                        }
                    });
                    
                    long threadEndTime = System.nanoTime();
                    totalDuration.addAndGet(threadEndTime - threadStartTime);
                    
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                    log.error("线程被中断", e);
                } finally {
                    completionLatch.countDown();
                }
            });
        }
        
        // 开始测试
        long testStartTime = System.nanoTime();
        startLatch.countDown();
        
        // 等待所有线程完成
        boolean completed = completionLatch.await(60, TimeUnit.SECONDS);
        long testEndTime = System.nanoTime();
        
        executor.shutdown();
        
        // Then
        assertTrue(completed, "并发测试未在规定时间内完成");
        
        long totalTestDurationMs = (testEndTime - testStartTime) / 1_000_000;
        double avgThreadDurationMs = (totalDuration.get() / threadCount) / 1_000_000.0;
        double throughputPerSecond = (double) successCount.get() / (totalTestDurationMs / 1000.0);
        
        log.info("并发性能测试结果:");
        log.info("- 线程数: {}", threadCount);
        log.info("- 每线程商品数: {}", productsPerThread);
        log.info("- 总商品数: {}", totalProducts);
        log.info("- 成功创建: {}", successCount.get());
        log.info("- 失败次数: {}", errorCount.get());
        log.info("- 总测试时间: {}ms", totalTestDurationMs);
        log.info("- 平均线程执行时间: {:.2f}ms", avgThreadDurationMs);
        log.info("- 吞吐量: {:.2f} 商品/秒", throughputPerSecond);
        
        // 性能断言
        assertTrue(successCount.get() >= totalProducts * 0.95, 
            "成功率过低: " + (double) successCount.get() / totalProducts * 100 + "%");
        assertTrue(errorCount.get() < totalProducts * 0.05, 
            "错误率过高: " + (double) errorCount.get() / totalProducts * 100 + "%");
        assertTrue(throughputPerSecond > 10, 
            "吞吐量过低: " + throughputPerSecond + " 商品/秒");
        assertTrue(totalTestDurationMs < 30000, 
            "总测试时间过长: " + totalTestDurationMs + "ms");
        
        // 验证数据库中的记录数
        assertRecordCount("product_info", successCount.get());
    }
    
    @Test
    void testMemoryUsageUnderLoad() {
        // Given
        final int iterations = 500;
        Runtime runtime = Runtime.getRuntime();
        
        // 强制垃圾回收并记录初始内存使用
        System.gc();
        long initialMemory = runtime.totalMemory() - runtime.freeMemory();
        
        log.info("初始内存使用: {} MB", initialMemory / 1024 / 1024);
        
        // When - 执行大量商品创建操作
        for (int i = 0; i < iterations; i++) {
            ProductInfoDto dto = getProductBuilder()
                .commonName("内存测试商品" + i)
                .productPref("MEM_TEST_" + String.format("%04d", i))
                .buildDto();
            
            Long productId = productApplicationService.saveOrUpdateProduct(dto, ProductBizTypeEnum.ADD_PRODUCT);
            assertNotNull(productId);
            
            // 每100次操作检查一次内存使用
            if (i % 100 == 0) {
                long currentMemory = runtime.totalMemory() - runtime.freeMemory();
                log.debug("第{}次操作后内存使用: {} MB", i, currentMemory / 1024 / 1024);
            }
        }
        
        // Then - 检查最终内存使用
        System.gc(); // 强制垃圾回收
        long finalMemory = runtime.totalMemory() - runtime.freeMemory();
        long memoryIncrease = finalMemory - initialMemory;
        
        log.info("最终内存使用: {} MB", finalMemory / 1024 / 1024);
        log.info("内存增长: {} MB", memoryIncrease / 1024 / 1024);
        
        // 断言内存增长在合理范围内（例如不超过200MB）
        assertTrue(memoryIncrease < 200 * 1024 * 1024, 
            "内存增长过多: " + memoryIncrease / 1024 / 1024 + " MB");
        
        // 验证所有商品都创建成功
        assertRecordCount("product_info", iterations);
    }
    
    @Test
    void testDatabaseConnectionPoolPerformance() throws InterruptedException {
        // Given
        final int threadCount = 20;
        final int operationsPerThread = 10;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        CountDownLatch completionLatch = new CountDownLatch(threadCount);
        
        AtomicInteger successCount = new AtomicInteger(0);
        AtomicLong totalResponseTime = new AtomicLong(0);
        
        // When - 测试数据库连接池在高并发下的性能
        for (int t = 0; t < threadCount; t++) {
            final int threadIndex = t;
            executor.submit(() -> {
                try {
                    runWithTenant(DEFAULT_TENANT_ID, () -> {
                        for (int i = 0; i < operationsPerThread; i++) {
                            long operationStart = System.nanoTime();
                            
                            ProductInfoDto dto = getProductBuilder()
                                .commonName("连接池测试商品T" + threadIndex + "O" + i)
                                .productPref("CONN_T" + threadIndex + "_O" + String.format("%03d", i))
                                .buildDto();
                            
                            Long productId = productApplicationService.saveOrUpdateProduct(dto, ProductBizTypeEnum.ADD_PRODUCT);
                            
                            long operationEnd = System.nanoTime();
                            long operationDuration = operationEnd - operationStart;
                            
                            if (productId != null) {
                                successCount.incrementAndGet();
                                totalResponseTime.addAndGet(operationDuration);
                            }
                        }
                    });
                } catch (Exception e) {
                    log.error("连接池测试线程{}失败", threadIndex, e);
                } finally {
                    completionLatch.countDown();
                }
            });
        }
        
        // 等待所有线程完成
        boolean completed = completionLatch.await(60, TimeUnit.SECONDS);
        executor.shutdown();
        
        // Then
        assertTrue(completed, "连接池测试未在规定时间内完成");
        
        int expectedOperations = threadCount * operationsPerThread;
        double avgResponseTimeMs = (totalResponseTime.get() / successCount.get()) / 1_000_000.0;
        
        log.info("数据库连接池性能测试结果:");
        log.info("- 并发线程数: {}", threadCount);
        log.info("- 每线程操作数: {}", operationsPerThread);
        log.info("- 预期总操作数: {}", expectedOperations);
        log.info("- 成功操作数: {}", successCount.get());
        log.info("- 平均响应时间: {:.2f}ms", avgResponseTimeMs);
        
        // 性能断言
        assertTrue(successCount.get() >= expectedOperations * 0.98, 
            "成功率过低，可能存在连接池问题");
        assertTrue(avgResponseTimeMs < 200, 
            "平均响应时间过长，可能存在连接池瓶颈: " + avgResponseTimeMs + "ms");
        
        // 验证数据完整性
        assertRecordCount("product_info", successCount.get());
    }
    
    @Override
    protected void cleanupTestData() {
        // 性能测试后清理大量数据
        super.cleanupTestData();
        
        // 强制垃圾回收释放内存
        System.gc();
        
        log.info("性能测试数据清理完成");
    }
}