package com.xyy.saas.inquiry.product.server.util;

import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.jdbc.JdbcTestUtils;

import javax.sql.DataSource;
import java.util.List;
import java.util.Map;

/**
 * 数据库测试工具类
 * 
 * <AUTHOR> Assistant
 */
public class DatabaseTestUtil {
    
    private final JdbcTemplate jdbcTemplate;
    
    public DatabaseTestUtil(DataSource dataSource) {
        this.jdbcTemplate = new JdbcTemplate(dataSource);
    }
    
    /**
     * 清空表数据
     */
    public void clearTable(String tableName) {
        JdbcTestUtils.deleteFromTables(jdbcTemplate, tableName);
    }
    
    /**
     * 清空多个表数据
     */
    public void clearTables(String... tableNames) {
        JdbcTestUtils.deleteFromTables(jdbcTemplate, tableNames);
    }
    
    /**
     * 获取表中记录数量
     */
    public int countRowsInTable(String tableName) {
        return JdbcTestUtils.countRowsInTable(jdbcTemplate, tableName);
    }
    
    /**
     * 按条件获取表中记录数量
     */
    public int countRowsInTableWhere(String tableName, String whereClause) {
        return JdbcTestUtils.countRowsInTableWhere(jdbcTemplate, tableName, whereClause);
    }
    
    /**
     * 执行SQL脚本
     */
    public void executeSqlScript(String sqlScript) {
        String[] statements = sqlScript.split(";");
        for (String statement : statements) {
            if (statement.trim().length() > 0) {
                jdbcTemplate.execute(statement.trim());
            }
        }
    }
    
    /**
     * 插入测试数据
     */
    public void insertTestData(String tableName, Map<String, Object> data) {
        StringBuilder sql = new StringBuilder("INSERT INTO " + tableName + " (");
        StringBuilder values = new StringBuilder(" VALUES (");
        
        Object[] params = new Object[data.size()];
        int index = 0;
        
        for (Map.Entry<String, Object> entry : data.entrySet()) {
            if (index > 0) {
                sql.append(", ");
                values.append(", ");
            }
            sql.append(entry.getKey());
            values.append("?");
            params[index++] = entry.getValue();
        }
        
        sql.append(")").append(values).append(")");
        jdbcTemplate.update(sql.toString(), params);
    }
    
    /**
     * 查询单条记录
     */
    public Map<String, Object> queryForMap(String sql, Object... params) {
        return jdbcTemplate.queryForMap(sql, params);
    }
    
    /**
     * 查询多条记录
     */
    public List<Map<String, Object>> queryForList(String sql, Object... params) {
        return jdbcTemplate.queryForList(sql, params);
    }
    
    /**
     * 检查记录是否存在
     */
    public boolean recordExists(String tableName, String whereClause, Object... params) {
        String sql = "SELECT COUNT(*) FROM " + tableName + " WHERE " + whereClause;
        Integer count = jdbcTemplate.queryForObject(sql, Integer.class, params);
        return count != null && count > 0;
    }
    
    /**
     * 检查商品是否存在
     */
    public boolean productExists(String productPref, Long tenantId) {
        return recordExists("product_info", "product_pref = ? AND tenant_id = ?", productPref, tenantId);
    }
    
    /**
     * 检查标准库商品是否存在
     */
    public boolean stdlibProductExists(String midProductPref, Long tenantId) {
        return recordExists("product_stdlib", "mid_product_pref = ? AND tenant_id = ?", midProductPref, tenantId);
    }
    
    /**
     * 获取商品状态
     */
    public String getProductStatus(String productPref, Long tenantId) {
        String sql = "SELECT status FROM product_info WHERE product_pref = ? AND tenant_id = ?";
        return jdbcTemplate.queryForObject(sql, String.class, productPref, tenantId);
    }
    
    /**
     * 获取最新插入的商品ID
     */
    public Long getLastInsertedProductId() {
        String sql = "SELECT LAST_INSERT_ID()";
        return jdbcTemplate.queryForObject(sql, Long.class);
    }
    
    /**
     * 重置自增ID
     */
    public void resetAutoIncrement(String tableName) {
        String sql = "ALTER TABLE " + tableName + " AUTO_INCREMENT = 1";
        jdbcTemplate.execute(sql);
    }
    
    /**
     * 创建测试数据快照
     */
    public void createSnapshot(String snapshotName) {
        // 可以实现数据快照功能，用于复杂测试场景的数据回滚
        // 这里只是一个示例框架
    }
    
    /**
     * 恢复测试数据快照
     */
    public void restoreSnapshot(String snapshotName) {
        // 恢复数据快照
    }
}