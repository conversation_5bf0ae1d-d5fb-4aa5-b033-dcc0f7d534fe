package com.xyy.saas.inquiry.product.server.test;

import com.xyy.saas.inquiry.product.server.builder.ProductTestDataBuilder;
import com.xyy.saas.inquiry.product.server.builder.TenantTestDataBuilder;
import com.xyy.saas.inquiry.product.server.util.DatabaseTestUtil;
import com.xyy.saas.inquiry.product.server.util.MockTenantContextUtil;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.transaction.annotation.Transactional;

import javax.sql.DataSource;

/**
 * 集成测试基类
 * 
 * <AUTHOR> Assistant
 */
@SpringBootTest
@ActiveProfiles("test")
@TestPropertySource(properties = {
    "product.service.refactored.enabled=true",
    "spring.jpa.hibernate.ddl-auto=none",
    "logging.level.com.xyy.saas.inquiry.product=DEBUG"
})
@Transactional
@Rollback
public abstract class BaseIntegrationTest {
    
    protected final Logger log = LoggerFactory.getLogger(getClass());
    
    protected static final Long DEFAULT_TENANT_ID = 1L;
    protected static final String DEFAULT_TENANT_CODE = "TEST_TENANT";
    
    @Resource
    private DataSource dataSource;
    
    protected DatabaseTestUtil databaseTestUtil;
    
    @BeforeEach
    void setUpBase() {
        log.info("开始集成测试: {}", getClass().getSimpleName());
        
        // 初始化数据库工具
        this.databaseTestUtil = new DatabaseTestUtil(dataSource);
        
        // 设置默认租户上下文
        MockTenantContextUtil.setCurrentTenantId(DEFAULT_TENANT_ID);
        
        // 清理测试数据
        cleanupTestData();
        
        // 准备测试数据
        prepareTestData();
        
        // 子类初始化
        setUp();
    }
    
    @AfterEach
    void tearDownBase() {
        // 子类清理
        tearDown();
        
        // 清理测试数据
        cleanupTestData();
        
        // 清理租户上下文
        MockTenantContextUtil.clearCurrentTenantId();
        
        log.info("结束集成测试: {}", getClass().getSimpleName());
    }
    
    /**
     * 子类实现具体的初始化逻辑
     */
    protected void setUp() {
        // 子类可以重写此方法
    }
    
    /**
     * 子类实现具体的清理逻辑
     */
    protected void tearDown() {
        // 子类可以重写此方法
    }
    
    /**
     * 准备测试数据
     */
    protected void prepareTestData() {
        // 子类可以重写此方法准备特定的测试数据
    }
    
    /**
     * 清理测试数据
     */
    protected void cleanupTestData() {
        // 清理相关表数据（按依赖关系逆序清理）
        databaseTestUtil.clearTables(
            "product_use_info",
            "product_qualification", 
            "product_stdlib",
            "product_info"
        );
    }
    
    /**
     * 获取测试商品数据构建器
     */
    protected ProductTestDataBuilder getProductBuilder() {
        return ProductTestDataBuilder.builder()
            .tenantId(DEFAULT_TENANT_ID)
            .tenantCode(DEFAULT_TENANT_CODE);
    }
    
    /**
     * 获取测试租户数据构建器
     */
    protected TenantTestDataBuilder getTenantBuilder() {
        return TenantTestDataBuilder.builder()
            .tenantId(DEFAULT_TENANT_ID)
            .tenantCode(DEFAULT_TENANT_CODE);
    }
    
    /**
     * 在指定租户上下文中执行
     */
    protected void runWithTenant(Long tenantId, Runnable action) {
        MockTenantContextUtil.runWithTenant(tenantId, action);
    }
    
    /**
     * 在指定租户上下文中执行并返回结果
     */
    protected <T> T runWithTenant(Long tenantId, java.util.function.Supplier<T> supplier) {
        return MockTenantContextUtil.runWithTenant(tenantId, supplier);
    }
    
    /**
     * 获取数据库工具
     */
    protected DatabaseTestUtil getDatabaseTestUtil() {
        return databaseTestUtil;
    }
    
    /**
     * 断言数据库中记录存在
     */
    protected void assertRecordExists(String tableName, String whereClause, Object... params) {
        boolean exists = databaseTestUtil.recordExists(tableName, whereClause, params);
        if (!exists) {
            throw new AssertionError(
                String.format("期望在表 %s 中找到满足条件 %s 的记录，但没有找到", tableName, whereClause)
            );
        }
    }
    
    /**
     * 断言数据库中记录不存在
     */
    protected void assertRecordNotExists(String tableName, String whereClause, Object... params) {
        boolean exists = databaseTestUtil.recordExists(tableName, whereClause, params);
        if (exists) {
            throw new AssertionError(
                String.format("期望在表 %s 中不存在满足条件 %s 的记录，但找到了", tableName, whereClause)
            );
        }
    }
    
    /**
     * 断言表中记录数量
     */
    protected void assertRecordCount(String tableName, int expectedCount) {
        int actualCount = databaseTestUtil.countRowsInTable(tableName);
        if (actualCount != expectedCount) {
            throw new AssertionError(
                String.format("期望表 %s 中有 %d 条记录，但实际有 %d 条", tableName, expectedCount, actualCount)
            );
        }
    }
    
    /**
     * 断言商品存在
     */
    protected void assertProductExists(String productPref) {
        assertProductExists(productPref, DEFAULT_TENANT_ID);
    }
    
    /**
     * 断言商品存在
     */
    protected void assertProductExists(String productPref, Long tenantId) {
        boolean exists = databaseTestUtil.productExists(productPref, tenantId);
        if (!exists) {
            throw new AssertionError(
                String.format("期望商品 %s (租户ID: %d) 存在，但没有找到", productPref, tenantId)
            );
        }
    }
    
    /**
     * 断言商品不存在
     */
    protected void assertProductNotExists(String productPref) {
        assertProductNotExists(productPref, DEFAULT_TENANT_ID);
    }
    
    /**
     * 断言商品不存在
     */
    protected void assertProductNotExists(String productPref, Long tenantId) {
        boolean exists = databaseTestUtil.productExists(productPref, tenantId);
        if (exists) {
            throw new AssertionError(
                String.format("期望商品 %s (租户ID: %d) 不存在，但找到了", productPref, tenantId)
            );
        }
    }
}