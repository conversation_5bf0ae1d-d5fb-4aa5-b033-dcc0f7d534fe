package com.xyy.saas.inquiry.product.server.domain.event;

import com.xyy.saas.inquiry.product.enums.ProductStatusEnum;
import com.xyy.saas.inquiry.product.server.builder.TenantTestDataBuilder;
import com.xyy.saas.inquiry.product.server.domain.valueobject.TenantId;
import com.xyy.saas.inquiry.product.server.test.BaseUnitTest;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 商品状态变更事件单元测试
 * 
 * <AUTHOR> Assistant
 */
class ProductStatusChangedEventTest extends BaseUnitTest {
    
    @Test
    void testConstructor_Success() {
        // Given
        String productPref = "TEST001";
        TenantId tenantId = getTenantBuilder().buildTenantId();
        ProductStatusEnum oldStatus = ProductStatusEnum.FIRST_AUDITING;
        ProductStatusEnum newStatus = ProductStatusEnum.USING;
        String changeReason = "审核通过";
        String operatorId = "admin";
        
        // When
        ProductStatusChangedEvent event = new ProductStatusChangedEvent(
            productPref, tenantId, oldStatus, newStatus, changeReason, operatorId
        );
        
        // Then
        assertEquals(productPref, event.getProductPref());
        assertEquals(tenantId, event.getTenantId());
        assertEquals(oldStatus, event.getOldStatus());
        assertEquals(newStatus, event.getNewStatus());
        assertEquals(changeReason, event.getChangeReason());
        assertEquals(operatorId, event.getOperatorId());
        assertEquals("ProductStatusChanged", event.getEventType());
        assertNotNull(event.getTimestamp());
    }
    
    @Test
    void testIsApprovalPassed_FromFirstAuditingToUsing_ReturnsTrue() {
        // Given
        ProductStatusChangedEvent event = createEvent(
            ProductStatusEnum.FIRST_AUDITING, 
            ProductStatusEnum.USING
        );
        
        // When & Then
        assertTrue(event.isApprovalPassed());
    }
    
    @Test
    void testIsApprovalPassed_FromHeadAuditingToUsing_ReturnsTrue() {
        // Given
        ProductStatusChangedEvent event = createEvent(
            ProductStatusEnum.HEAD_AUDITING, 
            ProductStatusEnum.USING
        );
        
        // When & Then
        assertTrue(event.isApprovalPassed());
    }
    
    @Test
    void testIsApprovalPassed_FromMidAuditingToUsing_ReturnsTrue() {
        // Given
        ProductStatusChangedEvent event = createEvent(
            ProductStatusEnum.MID_AUDITING, 
            ProductStatusEnum.USING
        );
        
        // When & Then
        assertTrue(event.isApprovalPassed());
    }
    
    @Test
    void testIsApprovalPassed_FromUsingToStopSale_ReturnsFalse() {
        // Given
        ProductStatusChangedEvent event = createEvent(
            ProductStatusEnum.USING, 
            ProductStatusEnum.STOP_SALE
        );
        
        // When & Then
        assertFalse(event.isApprovalPassed());
    }
    
    @Test
    void testIsApprovalPassed_FromNonAuditStatus_ReturnsFalse() {
        // Given
        ProductStatusChangedEvent event = createEvent(
            ProductStatusEnum.STOP_SALE, 
            ProductStatusEnum.USING
        );
        
        // When & Then
        assertFalse(event.isApprovalPassed());
    }
    
    @Test
    void testIsApprovalRejected_FromFirstAuditingToFirstReject_ReturnsTrue() {
        // Given
        ProductStatusChangedEvent event = createEvent(
            ProductStatusEnum.FIRST_AUDITING, 
            ProductStatusEnum.FIRST_AUDIT_REJECT
        );
        
        // When & Then
        assertTrue(event.isApprovalRejected());
    }
    
    @Test
    void testIsApprovalRejected_FromHeadAuditingToHeadReject_ReturnsTrue() {
        // Given
        ProductStatusChangedEvent event = createEvent(
            ProductStatusEnum.HEAD_AUDITING, 
            ProductStatusEnum.HEAD_AUDIT_REJECT
        );
        
        // When & Then
        assertTrue(event.isApprovalRejected());
    }
    
    @Test
    void testIsApprovalRejected_FromMidAuditingToMidReject_ReturnsTrue() {
        // Given
        ProductStatusChangedEvent event = createEvent(
            ProductStatusEnum.MID_AUDITING, 
            ProductStatusEnum.MID_AUDIT_REJECT
        );
        
        // When & Then
        assertTrue(event.isApprovalRejected());
    }
    
    @Test
    void testIsApprovalRejected_FromNonAuditStatus_ReturnsFalse() {
        // Given
        ProductStatusChangedEvent event = createEvent(
            ProductStatusEnum.USING, 
            ProductStatusEnum.FIRST_AUDIT_REJECT
        );
        
        // When & Then
        assertFalse(event.isApprovalRejected());
    }
    
    @Test
    void testIsApprovalRejected_ToNonRejectStatus_ReturnsFalse() {
        // Given
        ProductStatusChangedEvent event = createEvent(
            ProductStatusEnum.FIRST_AUDITING, 
            ProductStatusEnum.USING
        );
        
        // When & Then
        assertFalse(event.isApprovalRejected());
    }
    
    @Test
    void testIsAuditStatus_AuditingStatuses_ReturnsTrue() {
        // When & Then
        ProductStatusChangedEvent firstAuditEvent = createEvent(
            ProductStatusEnum.FIRST_AUDITING, 
            ProductStatusEnum.USING
        );
        ProductStatusChangedEvent headAuditEvent = createEvent(
            ProductStatusEnum.HEAD_AUDITING, 
            ProductStatusEnum.USING
        );
        ProductStatusChangedEvent midAuditEvent = createEvent(
            ProductStatusEnum.MID_AUDITING, 
            ProductStatusEnum.USING
        );
        
        // 这些事件的 isApprovalPassed 应该返回 true，证明 isAuditStatus 工作正常
        assertTrue(firstAuditEvent.isApprovalPassed());
        assertTrue(headAuditEvent.isApprovalPassed());
        assertTrue(midAuditEvent.isApprovalPassed());
    }
    
    @Test
    void testEventInheritance_Success() {
        // Given
        ProductStatusChangedEvent event = createEvent(
            ProductStatusEnum.FIRST_AUDITING, 
            ProductStatusEnum.USING
        );
        
        // When & Then
        assertTrue(event instanceof ProductDomainEvent);
        assertNotNull(event.getProductPref());
        assertNotNull(event.getTenantId());
        assertNotNull(event.getTimestamp());
        assertEquals("ProductStatusChanged", event.getEventType());
    }
    
    @Test
    void testEquals_SameValues_ReturnsTrue() {
        // Given
        String productPref = "TEST001";
        TenantId tenantId = getTenantBuilder().buildTenantId();
        ProductStatusEnum oldStatus = ProductStatusEnum.FIRST_AUDITING;
        ProductStatusEnum newStatus = ProductStatusEnum.USING;
        String changeReason = "审核通过";
        String operatorId = "admin";
        
        ProductStatusChangedEvent event1 = new ProductStatusChangedEvent(
            productPref, tenantId, oldStatus, newStatus, changeReason, operatorId
        );
        ProductStatusChangedEvent event2 = new ProductStatusChangedEvent(
            productPref, tenantId, oldStatus, newStatus, changeReason, operatorId
        );
        
        // When & Then
        assertEquals(event1.getProductPref(), event2.getProductPref());
        assertEquals(event1.getTenantId(), event2.getTenantId());
        assertEquals(event1.getOldStatus(), event2.getOldStatus());
        assertEquals(event1.getNewStatus(), event2.getNewStatus());
        assertEquals(event1.getChangeReason(), event2.getChangeReason());
        assertEquals(event1.getOperatorId(), event2.getOperatorId());
    }
    
    @Test
    void testAllStatusTransitions_Success() {
        // Test all possible status transitions for comprehensive coverage
        ProductStatusEnum[] auditingStatuses = {
            ProductStatusEnum.FIRST_AUDITING,
            ProductStatusEnum.HEAD_AUDITING, 
            ProductStatusEnum.MID_AUDITING
        };
        
        ProductStatusEnum[] rejectStatuses = {
            ProductStatusEnum.FIRST_AUDIT_REJECT,
            ProductStatusEnum.HEAD_AUDIT_REJECT,
            ProductStatusEnum.MID_AUDIT_REJECT
        };
        
        // 测试审核通过场景
        for (ProductStatusEnum auditingStatus : auditingStatuses) {
            ProductStatusChangedEvent approvalEvent = createEvent(auditingStatus, ProductStatusEnum.USING);
            assertTrue(approvalEvent.isApprovalPassed(), 
                "从 " + auditingStatus + " 到 USING 应该是审核通过");
            assertFalse(approvalEvent.isApprovalRejected(), 
                "从 " + auditingStatus + " 到 USING 不应该是审核驳回");
        }
        
        // 测试审核驳回场景
        for (int i = 0; i < auditingStatuses.length; i++) {
            ProductStatusChangedEvent rejectEvent = createEvent(auditingStatuses[i], rejectStatuses[i]);
            assertTrue(rejectEvent.isApprovalRejected(), 
                "从 " + auditingStatuses[i] + " 到 " + rejectStatuses[i] + " 应该是审核驳回");
            assertFalse(rejectEvent.isApprovalPassed(), 
                "从 " + auditingStatuses[i] + " 到 " + rejectStatuses[i] + " 不应该是审核通过");
        }
    }
    
    /**
     * 创建测试用的状态变更事件
     */
    private ProductStatusChangedEvent createEvent(ProductStatusEnum oldStatus, ProductStatusEnum newStatus) {
        return new ProductStatusChangedEvent(
            "TEST001",
            getTenantBuilder().buildTenantId(),
            oldStatus,
            newStatus,
            "测试状态变更",
            "admin"
        );
    }
}