package com.xyy.saas.inquiry.product.server.util;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

/**
 * 模拟租户上下文工具类
 * 
 * <AUTHOR> Assistant
 */
public class MockTenantContextUtil {
    
    /**
     * 设置当前租户ID
     */
    public static void setCurrentTenantId(Long tenantId) {
        TenantContextHolder.setTenantId(tenantId);
    }
    
    /**
     * 清除当前租户ID
     */
    public static void clearCurrentTenantId() {
        TenantContextHolder.clear();
    }
    
    /**
     * 模拟租户上下文执行
     */
    public static void runWithTenant(Long tenantId, Runnable action) {
        Long originalTenantId = TenantContextHolder.getTenantId();
        try {
            TenantContextHolder.setTenantId(tenantId);
            action.run();
        } finally {
            if (originalTenantId != null) {
                TenantContextHolder.setTenantId(originalTenantId);
            } else {
                TenantContextHolder.clear();
            }
        }
    }
    
    /**
     * 模拟租户上下文执行并返回结果
     */
    public static <T> T runWithTenant(Long tenantId, java.util.function.Supplier<T> supplier) {
        Long originalTenantId = TenantContextHolder.getTenantId();
        try {
            TenantContextHolder.setTenantId(tenantId);
            return supplier.get();
        } finally {
            if (originalTenantId != null) {
                TenantContextHolder.setTenantId(originalTenantId);
            } else {
                TenantContextHolder.clear();
            }
        }
    }
    
    /**
     * 创建MockedStatic用于测试
     */
    public static MockedStatic<TenantContextHolder> mockTenantContext(Long tenantId) {
        MockedStatic<TenantContextHolder> mockedStatic = Mockito.mockStatic(TenantContextHolder.class);
        mockedStatic.when(TenantContextHolder::getTenantId).thenReturn(tenantId);
        return mockedStatic;
    }
    
    /**
     * 创建MockedStatic用于测试（无租户）
     */
    public static MockedStatic<TenantContextHolder> mockNoTenantContext() {
        MockedStatic<TenantContextHolder> mockedStatic = Mockito.mockStatic(TenantContextHolder.class);
        mockedStatic.when(TenantContextHolder::getTenantId).thenReturn(null);
        return mockedStatic;
    }
}