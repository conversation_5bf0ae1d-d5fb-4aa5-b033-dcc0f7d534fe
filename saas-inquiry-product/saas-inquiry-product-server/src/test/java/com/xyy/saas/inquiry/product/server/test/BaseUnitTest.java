package com.xyy.saas.inquiry.product.server.test;

import com.xyy.saas.inquiry.product.server.builder.ProductTestDataBuilder;
import com.xyy.saas.inquiry.product.server.builder.TenantTestDataBuilder;
import com.xyy.saas.inquiry.product.server.util.MockTenantContextUtil;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 单元测试基类
 * 
 * <AUTHOR> Assistant
 */
@ExtendWith(MockitoExtension.class)
public abstract class BaseUnitTest {
    
    protected final Logger log = LoggerFactory.getLogger(getClass());
    
    protected static final Long DEFAULT_TENANT_ID = 1L;
    protected static final String DEFAULT_TENANT_CODE = "TEST_TENANT";
    
    @BeforeEach
    void setUpBase() {
        log.debug("开始单元测试: {}", getClass().getSimpleName());
        // 设置默认租户上下文
        MockTenantContextUtil.setCurrentTenantId(DEFAULT_TENANT_ID);
        setUp();
    }
    
    @AfterEach
    void tearDownBase() {
        tearDown();
        // 清理租户上下文
        MockTenantContextUtil.clearCurrentTenantId();
        log.debug("结束单元测试: {}", getClass().getSimpleName());
    }
    
    /**
     * 子类实现具体的初始化逻辑
     */
    protected void setUp() {
        // 子类可以重写此方法
    }
    
    /**
     * 子类实现具体的清理逻辑
     */
    protected void tearDown() {
        // 子类可以重写此方法
    }
    
    /**
     * 获取测试商品数据构建器
     */
    protected ProductTestDataBuilder getProductBuilder() {
        return ProductTestDataBuilder.builder()
            .tenantId(DEFAULT_TENANT_ID)
            .tenantCode(DEFAULT_TENANT_CODE);
    }
    
    /**
     * 获取测试租户数据构建器
     */
    protected TenantTestDataBuilder getTenantBuilder() {
        return TenantTestDataBuilder.builder()
            .tenantId(DEFAULT_TENANT_ID)
            .tenantCode(DEFAULT_TENANT_CODE);
    }
    
    /**
     * 在指定租户上下文中执行
     */
    protected void runWithTenant(Long tenantId, Runnable action) {
        MockTenantContextUtil.runWithTenant(tenantId, action);
    }
    
    /**
     * 在指定租户上下文中执行并返回结果
     */
    protected <T> T runWithTenant(Long tenantId, java.util.function.Supplier<T> supplier) {
        return MockTenantContextUtil.runWithTenant(tenantId, supplier);
    }
    
    /**
     * 断言异常信息包含指定文本
     */
    protected void assertExceptionContains(Exception exception, String expectedMessage) {
        String actualMessage = exception.getMessage();
        if (actualMessage == null || !actualMessage.contains(expectedMessage)) {
            throw new AssertionError(
                String.format("期望异常信息包含 '%s'，但实际为 '%s'", expectedMessage, actualMessage)
            );
        }
    }
    
    /**
     * 创建测试用的异常
     */
    protected RuntimeException createTestException(String message) {
        return new RuntimeException("测试异常: " + message);
    }
    
    /**
     * 模拟延迟（用于测试超时等场景）
     */
    protected void simulateDelay(long millis) {
        try {
            Thread.sleep(millis);
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
            throw new RuntimeException("测试延迟被中断", e);
        }
    }
}