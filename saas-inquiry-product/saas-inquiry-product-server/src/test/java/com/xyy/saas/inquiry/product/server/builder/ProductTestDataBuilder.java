package com.xyy.saas.inquiry.product.server.builder;

import com.xyy.saas.inquiry.product.api.product.dto.ProductFlag;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductQualification;
import com.xyy.saas.inquiry.product.api.product.dto.ProductUseInfo;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.enums.ProductStatusEnum;
import com.xyy.saas.inquiry.product.server.domain.aggregate.ProductAggregate;
import com.xyy.saas.inquiry.product.server.domain.entity.ProductInfo;
import com.xyy.saas.inquiry.product.server.domain.entity.ProductStdlib;
import com.xyy.saas.inquiry.product.server.domain.valueobject.ProductId;
import com.xyy.saas.inquiry.product.server.domain.valueobject.TenantId;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品测试数据构建器
 * 
 * <AUTHOR> Assistant
 */
public class ProductTestDataBuilder {
    
    private String commonName = "测试商品";
    private String manufacturer = "测试厂家";
    private String unit = "盒";
    private String specification = "10mg*20片";
    private String productPref = "TEST001";
    private String barcode = "1234567890123";
    private BigDecimal price = new BigDecimal("25.50");
    private ProductStatusEnum status = ProductStatusEnum.USING;
    private String tenantCode = "TEST_TENANT";
    private Long tenantId = 1L;
    private Long id = 1L;
    private String approvalNumber = "国药准字*********";
    private String dosageForm = "片剂";
    private Boolean isOtc = false;
    private String midProductPref = "MID_TEST001";
    
    public static ProductTestDataBuilder builder() {
        return new ProductTestDataBuilder();
    }
    
    public ProductTestDataBuilder commonName(String commonName) {
        this.commonName = commonName;
        return this;
    }
    
    public ProductTestDataBuilder manufacturer(String manufacturer) {
        this.manufacturer = manufacturer;
        return this;
    }
    
    public ProductTestDataBuilder unit(String unit) {
        this.unit = unit;
        return this;
    }
    
    public ProductTestDataBuilder specification(String specification) {
        this.specification = specification;
        return this;
    }
    
    public ProductTestDataBuilder productPref(String productPref) {
        this.productPref = productPref;
        return this;
    }
    
    public ProductTestDataBuilder barcode(String barcode) {
        this.barcode = barcode;
        return this;
    }
    
    public ProductTestDataBuilder price(BigDecimal price) {
        this.price = price;
        return this;
    }
    
    public ProductTestDataBuilder status(ProductStatusEnum status) {
        this.status = status;
        return this;
    }
    
    public ProductTestDataBuilder tenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
        return this;
    }
    
    public ProductTestDataBuilder tenantId(Long tenantId) {
        this.tenantId = tenantId;
        return this;
    }
    
    public ProductTestDataBuilder id(Long id) {
        this.id = id;
        return this;
    }
    
    public ProductTestDataBuilder approvalNumber(String approvalNumber) {
        this.approvalNumber = approvalNumber;
        return this;
    }
    
    public ProductTestDataBuilder dosageForm(String dosageForm) {
        this.dosageForm = dosageForm;
        return this;
    }
    
    public ProductTestDataBuilder isOtc(Boolean isOtc) {
        this.isOtc = isOtc;
        return this;
    }
    
    public ProductTestDataBuilder midProductPref(String midProductPref) {
        this.midProductPref = midProductPref;
        return this;
    }
    
    /**
     * 构建ProductInfoDto
     */
    public ProductInfoDto buildDto() {
        ProductInfoDto dto = new ProductInfoDto();
        dto.setId(this.id);
        dto.setCommonName(this.commonName);
        dto.setManufacturer(this.manufacturer);
        dto.setUnit(this.unit);
        dto.setSpecification(this.specification);
        dto.setProductPref(this.productPref);
        dto.setBarcode(this.barcode);
        dto.setPrice(this.price);
        dto.setStatus(this.status);
        dto.setTenantCode(this.tenantCode);
        dto.setApprovalNumber(this.approvalNumber);
        dto.setDosageForm(this.dosageForm);
        dto.setIsOtc(this.isOtc);
        dto.setMidProductPref(this.midProductPref);
        
        // 设置标志
        ProductFlag flag = new ProductFlag();
        flag.setStopSale(false);
        flag.setSpecialPrice(false);
        flag.setIntegral(true);
        flag.setUnbundled(false);
        flag.setMidSyncSkipped(false);
        flag.setMidDeactivated(false);
        dto.setProductFlag(flag);
        
        // 设置用药信息
        ProductUseInfo useInfo = new ProductUseInfo();
        useInfo.setUsage("口服");
        useInfo.setDosage("一次1-2片，一日3次");
        useInfo.setIndication("用于感冒发热");
        useInfo.setContraindication("孕妇禁用");
        useInfo.setAdverseReaction("偶见皮疹");
        useInfo.setAttention("饭后服用");
        dto.setProductUseInfo(useInfo);
        
        // 设置资质信息
        ProductQualification qualification = new ProductQualification();
        qualification.setGspCertified(true);
        qualification.setGmpCertified(true);
        qualification.setDrugLicense("药品经营许可证123");
        qualification.setMedicalDeviceLicense("医疗器械许可证456");
        dto.setProductQualification(qualification);
        
        return dto;
    }
    
    /**
     * 构建ProductInfo实体
     */
    public ProductInfo buildEntity() {
        ProductInfo entity = new ProductInfo();
        entity.setId(this.id);
        entity.setCommonName(this.commonName);
        entity.setManufacturer(this.manufacturer);
        entity.setUnit(this.unit);
        entity.setSpecification(this.specification);
        entity.setProductPref(this.productPref);
        entity.setBarcode(this.barcode);
        entity.setPrice(this.price);
        entity.setStatus(this.status);
        entity.setTenantId(this.tenantId);
        entity.setApprovalNumber(this.approvalNumber);
        entity.setDosageForm(this.dosageForm);
        entity.setIsOtc(this.isOtc);
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        return entity;
    }
    
    /**
     * 构建ProductStdlib实体
     */
    public ProductStdlib buildStdlibEntity() {
        ProductStdlib entity = new ProductStdlib();
        entity.setId(this.id);
        entity.setCommonName(this.commonName);
        entity.setManufacturer(this.manufacturer);
        entity.setUnit(this.unit);
        entity.setSpecification(this.specification);
        entity.setMidProductPref(this.midProductPref);
        entity.setApprovalNumber(this.approvalNumber);
        entity.setDosageForm(this.dosageForm);
        entity.setIsOtc(this.isOtc);
        entity.setTenantId(this.tenantId);
        entity.setCreateTime(LocalDateTime.now());
        entity.setUpdateTime(LocalDateTime.now());
        return entity;
    }
    
    /**
     * 构建ProductAggregate聚合根
     */
    public ProductAggregate buildAggregate() {
        ProductInfo productInfo = buildEntity();
        ProductStdlib productStdlib = buildStdlibEntity();
        TenantId tenantIdVO = new TenantId(this.tenantId, this.tenantCode);
        
        return ProductAggregate.create(
            productInfo,
            productStdlib,
            buildDto().getProductUseInfo(),
            buildDto().getProductQualification(),
            com.xyy.saas.inquiry.product.server.domain.valueobject.ProductFlag.fromOriginal(buildDto().getProductFlag()),
            tenantIdVO
        );
    }
    
    /**
     * 预设数据：普通商品
     */
    public static ProductTestDataBuilder normalProduct() {
        return builder()
            .commonName("阿莫西林胶囊")
            .manufacturer("华北制药")
            .unit("盒")
            .specification("0.25g*20粒")
            .productPref("AMX001")
            .price(new BigDecimal("18.50"))
            .status(ProductStatusEnum.USING)
            .isOtc(true);
    }
    
    /**
     * 预设数据：处方药
     */
    public static ProductTestDataBuilder prescriptionDrug() {
        return builder()
            .commonName("头孢克肟胶囊")
            .manufacturer("石药集团")
            .unit("盒")
            .specification("0.1g*6粒")
            .productPref("CFX001")
            .price(new BigDecimal("35.80"))
            .status(ProductStatusEnum.USING)
            .isOtc(false)
            .approvalNumber("国药准字H20050682");
    }
    
    /**
     * 预设数据：停售商品
     */
    public static ProductTestDataBuilder discontinuedProduct() {
        return builder()
            .commonName("过期商品")
            .manufacturer("测试厂家")
            .unit("盒")
            .specification("规格")
            .productPref("EXP001")
            .price(new BigDecimal("10.00"))
            .status(ProductStatusEnum.STOP_SALE);
    }
    
    /**
     * 预设数据：审核中商品
     */
    public static ProductTestDataBuilder auditingProduct() {
        return builder()
            .commonName("审核中商品")
            .manufacturer("测试厂家")
            .unit("盒")
            .specification("规格")
            .productPref("AUD001")
            .price(new BigDecimal("20.00"))
            .status(ProductStatusEnum.FIRST_AUDITING);
    }
}