package com.xyy.saas.inquiry.product.server.application.orchestrator;

import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.server.application.command.ProductSaveCommand;
import com.xyy.saas.inquiry.product.server.application.step.ProcessingStep;
import com.xyy.saas.inquiry.product.server.builder.ProductTestDataBuilder;
import com.xyy.saas.inquiry.product.server.builder.TenantTestDataBuilder;
import com.xyy.saas.inquiry.product.server.test.BaseUnitTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * 商品保存编排器单元测试
 * 
 * <AUTHOR> Assistant
 */
class ProductSaveOrchestratorTest extends BaseUnitTest {
    
    @Mock
    private ProcessingStep mockStep1;
    
    @Mock
    private ProcessingStep mockStep2;
    
    @Mock
    private ProcessingStep mockStep3;
    
    private ProductSaveOrchestrator orchestrator;
    private List<ProcessingStep> processingSteps;
    
    @BeforeEach
    @Override
    protected void setUp() {
        MockitoAnnotations.openMocks(this);
        
        // 设置步骤执行顺序
        when(mockStep1.getOrder()).thenReturn(100);
        when(mockStep2.getOrder()).thenReturn(200);
        when(mockStep3.getOrder()).thenReturn(300);
        
        processingSteps = Arrays.asList(mockStep1, mockStep2, mockStep3);
        orchestrator = new ProductSaveOrchestrator(processingSteps);
    }
    
    @Test
    void testExecute_AllStepsSuccess_ReturnsProductId() {
        // Given
        ProductInfoDto dto = getProductBuilder().buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.EDIT_PRODUCT);
        Long expectedProductId = 123L;
        
        // 设置命令执行结果
        command.setProductId(expectedProductId);
        
        // When
        Long result = orchestrator.execute(command);
        
        // Then
        assertEquals(expectedProductId, result);
        
        // 验证所有步骤都被执行
        verify(mockStep1).execute(command);
        verify(mockStep2).execute(command);
        verify(mockStep3).execute(command);
        
        // 验证执行顺序（通过inOrder验证）
        var inOrder = inOrder(mockStep1, mockStep2, mockStep3);
        inOrder.verify(mockStep1).execute(command);
        inOrder.verify(mockStep2).execute(command);
        inOrder.verify(mockStep3).execute(command);
    }
    
    @Test
    void testExecute_WithNullCommand_ThrowsException() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> orchestrator.execute(null)
        );
        
        assertEquals("命令不能为空", exception.getMessage());
        
        // 验证没有步骤被执行
        verifyNoInteractions(mockStep1, mockStep2, mockStep3);
    }
    
    @Test
    void testExecute_StepThrowsException_PropagatesException() {
        // Given
        ProductInfoDto dto = getProductBuilder().buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.EDIT_PRODUCT);
        RuntimeException testException = new RuntimeException("步骤执行失败");
        
        // 设置第二个步骤抛出异常
        doThrow(testException).when(mockStep2).execute(command);
        
        // When & Then
        RuntimeException thrownException = assertThrows(
            RuntimeException.class,
            () -> orchestrator.execute(command)
        );
        
        assertEquals("步骤执行失败", thrownException.getMessage());
        
        // 验证第一个步骤被执行，第二个步骤抛出异常，第三个步骤没有被执行
        verify(mockStep1).execute(command);
        verify(mockStep2).execute(command);
        verify(mockStep3, never()).execute(command);
    }
    
    @Test
    void testExecute_EmptyStepList_ReturnsNull() {
        // Given
        ProductSaveOrchestrator emptyOrchestrator = new ProductSaveOrchestrator(Arrays.asList());
        ProductInfoDto dto = getProductBuilder().buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.EDIT_PRODUCT);
        
        // When
        Long result = emptyOrchestrator.execute(command);
        
        // Then
        assertNull(result);
    }
    
    @Test
    void testExecute_StepsExecutedInCorrectOrder() {
        // Given
        ProductInfoDto dto = getProductBuilder().buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.EDIT_PRODUCT);
        
        // 创建一个执行顺序记录器
        StringBuilder executionOrder = new StringBuilder();
        
        doAnswer(invocation -> {
            executionOrder.append("1");
            return null;
        }).when(mockStep1).execute(any());
        
        doAnswer(invocation -> {
            executionOrder.append("2");
            return null;
        }).when(mockStep2).execute(any());
        
        doAnswer(invocation -> {
            executionOrder.append("3");
            return null;
        }).when(mockStep3).execute(any());
        
        // When
        orchestrator.execute(command);
        
        // Then
        assertEquals("123", executionOrder.toString());
    }
    
    @Test
    void testExecute_StepModifiesCommand_ChangesVisibleToLaterSteps() {
        // Given
        ProductInfoDto dto = getProductBuilder().buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.EDIT_PRODUCT);
        
        // 设置第一个步骤修改命令
        doAnswer(invocation -> {
            ProductSaveCommand cmd = invocation.getArgument(0);
            cmd.setProductId(999L);
            return null;
        }).when(mockStep1).execute(any());
        
        // 设置第二个步骤验证修改是否可见
        doAnswer(invocation -> {
            ProductSaveCommand cmd = invocation.getArgument(0);
            assertEquals(999L, cmd.getProductId());
            return null;
        }).when(mockStep2).execute(any());
        
        // When
        Long result = orchestrator.execute(command);
        
        // Then
        assertEquals(999L, result);
        verify(mockStep1).execute(command);
        verify(mockStep2).execute(command);
        verify(mockStep3).execute(command);
    }
    
    @Test
    void testConstructor_WithNullSteps_ThrowsException() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> new ProductSaveOrchestrator(null)
        );
        
        assertEquals("处理步骤列表不能为空", exception.getMessage());
    }
    
    @Test
    void testStepsSortedByOrder() {
        // Given - 创建顺序混乱的步骤
        ProcessingStep step100 = mock(ProcessingStep.class);
        ProcessingStep step50 = mock(ProcessingStep.class);
        ProcessingStep step200 = mock(ProcessingStep.class);
        
        when(step100.getOrder()).thenReturn(100);
        when(step50.getOrder()).thenReturn(50);
        when(step200.getOrder()).thenReturn(200);
        
        List<ProcessingStep> unsortedSteps = Arrays.asList(step100, step50, step200);
        ProductSaveOrchestrator testOrchestrator = new ProductSaveOrchestrator(unsortedSteps);
        
        ProductInfoDto dto = getProductBuilder().buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.EDIT_PRODUCT);
        
        // 记录执行顺序
        StringBuilder executionOrder = new StringBuilder();
        
        doAnswer(invocation -> {
            executionOrder.append("50,");
            return null;
        }).when(step50).execute(any());
        
        doAnswer(invocation -> {
            executionOrder.append("100,");
            return null;
        }).when(step100).execute(any());
        
        doAnswer(invocation -> {
            executionOrder.append("200,");
            return null;
        }).when(step200).execute(any());
        
        // When
        testOrchestrator.execute(command);
        
        // Then - 验证按Order值升序执行
        assertEquals("50,100,200,", executionOrder.toString());
    }
    
    @Test
    void testExecute_CommandContainsRequiredData() {
        // Given
        ProductInfoDto dto = getProductBuilder()
            .commonName("测试商品")
            .manufacturer("测试厂家")
            .buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.ADD_PRODUCT);
        
        // 验证命令数据
        doAnswer(invocation -> {
            ProductSaveCommand cmd = invocation.getArgument(0);
            assertNotNull(cmd.getDto());
            assertEquals("测试商品", cmd.getDto().getCommonName());
            assertEquals("测试厂家", cmd.getDto().getManufacturer());
            assertEquals(ProductBizTypeEnum.ADD_PRODUCT, cmd.getBizType());
            return null;
        }).when(mockStep1).execute(any());
        
        // When
        orchestrator.execute(command);
        
        // Then
        verify(mockStep1).execute(command);
    }
}