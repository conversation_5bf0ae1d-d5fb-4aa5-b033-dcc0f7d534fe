package com.xyy.saas.inquiry.product.server.application.handler;

import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.enums.ProductStatusEnum;
import com.xyy.saas.inquiry.product.server.application.command.ProductSaveCommand;
import com.xyy.saas.inquiry.product.server.application.handler.impl.AddProductHandler;
import com.xyy.saas.inquiry.product.server.builder.ProductTestDataBuilder;
import com.xyy.saas.inquiry.product.server.test.BaseUnitTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 新增商品处理器单元测试
 * 
 * <AUTHOR> Assistant
 */
class AddProductHandlerTest extends BaseUnitTest {
    
    private AddProductHandler addProductHandler;
    
    @BeforeEach
    @Override
    protected void setUp() {
        MockitoAnnotations.openMocks(this);
        addProductHandler = new AddProductHandler();
    }
    
    @Test
    void testSupports_ReturnsCorrectBizType() {
        // When
        ProductBizTypeEnum supportedType = addProductHandler.supports();
        
        // Then
        assertEquals(ProductBizTypeEnum.ADD_PRODUCT, supportedType);
    }
    
    @Test
    void testHandle_WithValidCommand_Success() {
        // Given
        ProductInfoDto dto = getProductBuilder()
            .commonName("新增商品")
            .manufacturer("新增厂家")
            .productPref("ADD001")
            .buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.ADD_PRODUCT);
        
        // When
        assertDoesNotThrow(() -> addProductHandler.handle(command));
        
        // Then - 验证商品状态被设置为待审核
        assertEquals(ProductStatusEnum.FIRST_AUDITING, command.getDto().getStatus());
    }
    
    @Test
    void testHandle_WithNullCommand_ThrowsException() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> addProductHandler.handle(null)
        );
        
        assertEquals("命令不能为空", exception.getMessage());
    }
    
    @Test
    void testHandle_WithNullDto_ThrowsException() {
        // Given
        ProductSaveCommand command = new ProductSaveCommand(null, ProductBizTypeEnum.ADD_PRODUCT);
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> addProductHandler.handle(command)
        );
        
        assertEquals("商品信息不能为空", exception.getMessage());
    }
    
    @Test
    void testHandle_WithWrongBizType_ThrowsException() {
        // Given
        ProductInfoDto dto = getProductBuilder().buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.EDIT_PRODUCT);
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> addProductHandler.handle(command)
        );
        
        assertEquals("不支持的业务类型: " + ProductBizTypeEnum.EDIT_PRODUCT, exception.getMessage());
    }
    
    @Test
    void testHandle_SetsCorrectProductStatus() {
        // Given
        ProductInfoDto dto = getProductBuilder()
            .status(ProductStatusEnum.USING) // 初始状态
            .buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.ADD_PRODUCT);
        
        // When
        addProductHandler.handle(command);
        
        // Then - 新增商品应该设置为初审状态
        assertEquals(ProductStatusEnum.FIRST_AUDITING, command.getDto().getStatus());
    }
    
    @Test
    void testHandle_PreservesOtherDtoFields() {
        // Given
        ProductInfoDto dto = getProductBuilder()
            .commonName("测试商品")
            .manufacturer("测试厂家")
            .unit("盒")
            .specification("规格")
            .productPref("TEST001")
            .barcode("1234567890")
            .buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.ADD_PRODUCT);
        
        // When
        addProductHandler.handle(command);
        
        // Then - 其他字段应该保持不变
        assertEquals("测试商品", command.getDto().getCommonName());
        assertEquals("测试厂家", command.getDto().getManufacturer());
        assertEquals("盒", command.getDto().getUnit());
        assertEquals("规格", command.getDto().getSpecification());
        assertEquals("TEST001", command.getDto().getProductPref());
        assertEquals("1234567890", command.getDto().getBarcode());
        // 只有状态被修改
        assertEquals(ProductStatusEnum.FIRST_AUDITING, command.getDto().getStatus());
    }
    
    @Test
    void testHandle_WithDifferentInitialStatuses_AlwaysSetsToFirstAuditing() {
        // 测试不同初始状态的商品都应该被设置为初审状态
        ProductStatusEnum[] initialStatuses = {
            ProductStatusEnum.USING,
            ProductStatusEnum.STOP_SELL,
            ProductStatusEnum.HEAD_AUDITING,
            ProductStatusEnum.MID_AUDITING
        };
        
        for (ProductStatusEnum initialStatus : initialStatuses) {
            // Given
            ProductInfoDto dto = getProductBuilder()
                .status(initialStatus)
                .productPref("TEST_" + initialStatus.name())
                .buildDto();
            ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.ADD_PRODUCT);
            
            // When
            addProductHandler.handle(command);
            
            // Then
            assertEquals(ProductStatusEnum.FIRST_AUDITING, command.getDto().getStatus(),
                "初始状态为 " + initialStatus + " 的商品应该被设置为初审状态");
        }
    }
    
    @Test
    void testHandle_WithComplexProductDto_Success() {
        // Given - 复杂的商品DTO
        ProductInfoDto dto = getProductBuilder()
            .commonName("复杂新增商品")
            .manufacturer("复杂厂家")
            .unit("片")
            .specification("0.25g*20片")
            .productPref("COMPLEX_ADD_001")
            .barcode("1234567890123")
            .approvalNumber("国药准字*********")
            .dosageForm("片剂")
            .isOtc(false)
            .status(ProductStatusEnum.USING)
            .buildDto();
        
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.ADD_PRODUCT);
        
        // When
        addProductHandler.handle(command);
        
        // Then - 验证复杂商品的处理结果
        assertEquals(ProductStatusEnum.FIRST_AUDITING, command.getDto().getStatus());
        assertEquals("复杂新增商品", command.getDto().getCommonName());
        assertEquals("复杂厂家", command.getDto().getManufacturer());
        assertEquals("片", command.getDto().getUnit());
        assertEquals("0.25g*20片", command.getDto().getSpecification());
        assertEquals("COMPLEX_ADD_001", command.getDto().getProductPref());
        assertEquals("1234567890123", command.getDto().getBarcode());
        assertEquals("国药准字*********", command.getDto().getApprovalNumber());
        assertEquals("片剂", command.getDto().getDosageForm());
        assertFalse(command.getDto().getIsOtc());
    }
    
    @Test
    void testHandle_MultipleCallsSameCommand_ConsistentResult() {
        // Given
        ProductInfoDto dto = getProductBuilder()
            .status(ProductStatusEnum.USING)
            .buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.ADD_PRODUCT);
        
        // When - 多次调用处理器
        addProductHandler.handle(command);
        ProductStatusEnum firstResult = command.getDto().getStatus();
        
        addProductHandler.handle(command);
        ProductStatusEnum secondResult = command.getDto().getStatus();
        
        // Then - 结果应该一致
        assertEquals(ProductStatusEnum.FIRST_AUDITING, firstResult);
        assertEquals(ProductStatusEnum.FIRST_AUDITING, secondResult);
        assertEquals(firstResult, secondResult);
    }
    
    @Test
    void testHandle_CommandModificationVisibility() {
        // Given
        ProductInfoDto dto = getProductBuilder()
            .commonName("原始商品名")
            .status(ProductStatusEnum.USING)
            .buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.ADD_PRODUCT);
        
        // 验证处理前状态
        assertEquals(ProductStatusEnum.USING, command.getDto().getStatus());
        
        // When
        addProductHandler.handle(command);
        
        // Then - 命令的修改应该对外可见
        assertEquals(ProductStatusEnum.FIRST_AUDITING, command.getDto().getStatus());
        // 通过DTO引用验证修改确实发生
        assertEquals(ProductStatusEnum.FIRST_AUDITING, dto.getStatus());
    }
}