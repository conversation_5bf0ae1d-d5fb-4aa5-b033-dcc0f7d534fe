package com.xyy.saas.inquiry.product.server.application.step;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.api.tenant.dto.TenantInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.server.application.command.ProductSaveCommand;
import com.xyy.saas.inquiry.product.server.application.step.impl.TenantInfoAssemblyStep;
import com.xyy.saas.inquiry.product.server.builder.ProductTestDataBuilder;
import com.xyy.saas.inquiry.product.server.builder.TenantTestDataBuilder;
import com.xyy.saas.inquiry.product.server.domain.service.TenantDomainService;
import com.xyy.saas.inquiry.product.server.test.BaseUnitTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 租户信息装配步骤单元测试
 * 
 * <AUTHOR> Assistant
 */
class TenantInfoAssemblyStepTest extends BaseUnitTest {
    
    @Mock
    private TenantDomainService tenantDomainService;
    
    private TenantInfoAssemblyStep assemblyStep;
    
    @BeforeEach
    @Override
    protected void setUp() {
        MockitoAnnotations.openMocks(this);
        assemblyStep = new TenantInfoAssemblyStep(tenantDomainService);
    }
    
    @Test
    void testExecute_WithValidTenantContext_Success() {
        // Given
        Long tenantId = 1L;
        ProductInfoDto dto = getProductBuilder().buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.EDIT_PRODUCT);
        
        TenantInfoDto tenantInfo = getTenantBuilder()
            .tenantId(tenantId)
            .tenantCode("TEST_TENANT")
            .tenantName("测试租户")
            .buildDto();
        
        when(tenantDomainService.getTenantInfo(tenantId)).thenReturn(tenantInfo);
        
        try (MockedStatic<TenantContextHolder> mockedStatic = mockStatic(TenantContextHolder.class)) {
            mockedStatic.when(TenantContextHolder::getTenantId).thenReturn(tenantId);
            
            // When
            assemblyStep.execute(command);
            
            // Then
            assertNotNull(command.getTenantInfo());
            assertEquals(tenantId, command.getTenantInfo().getTenantId());
            assertEquals("TEST_TENANT", command.getTenantInfo().getTenantCode());
            assertEquals("测试租户", command.getTenantInfo().getTenantName());
            
            verify(tenantDomainService).getTenantInfo(tenantId);
        }
    }
    
    @Test
    void testExecute_WithNullTenantContext_ThrowsException() {
        // Given
        ProductInfoDto dto = getProductBuilder().buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.EDIT_PRODUCT);
        
        try (MockedStatic<TenantContextHolder> mockedStatic = mockStatic(TenantContextHolder.class)) {
            mockedStatic.when(TenantContextHolder::getTenantId).thenReturn(null);
            
            // When & Then
            RuntimeException exception = assertThrows(
                RuntimeException.class,
                () -> assemblyStep.execute(command)
            );
            
            assertEquals("当前租户上下文为空", exception.getMessage());
            
            // 验证没有调用租户服务
            verify(tenantDomainService, never()).getTenantInfo(any());
        }
    }
    
    @Test
    void testExecute_TenantServiceThrowsException_PropagatesException() {
        // Given
        Long tenantId = 1L;
        ProductInfoDto dto = getProductBuilder().buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.EDIT_PRODUCT);
        
        RuntimeException serviceException = new RuntimeException("租户服务异常");
        when(tenantDomainService.getTenantInfo(tenantId)).thenThrow(serviceException);
        
        try (MockedStatic<TenantContextHolder> mockedStatic = mockStatic(TenantContextHolder.class)) {
            mockedStatic.when(TenantContextHolder::getTenantId).thenReturn(tenantId);
            
            // When & Then
            RuntimeException thrownException = assertThrows(
                RuntimeException.class,
                () -> assemblyStep.execute(command)
            );
            
            assertEquals("租户服务异常", thrownException.getMessage());
            assertSame(serviceException, thrownException);
            
            verify(tenantDomainService).getTenantInfo(tenantId);
        }
    }
    
    @Test
    void testExecute_TenantServiceReturnsNull_ThrowsException() {
        // Given
        Long tenantId = 1L;
        ProductInfoDto dto = getProductBuilder().buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.EDIT_PRODUCT);
        
        when(tenantDomainService.getTenantInfo(tenantId)).thenReturn(null);
        
        try (MockedStatic<TenantContextHolder> mockedStatic = mockStatic(TenantContextHolder.class)) {
            mockedStatic.when(TenantContextHolder::getTenantId).thenReturn(tenantId);
            
            // When & Then
            RuntimeException exception = assertThrows(
                RuntimeException.class,
                () -> assemblyStep.execute(command)
            );
            
            assertEquals("租户信息不存在: " + tenantId, exception.getMessage());
            
            verify(tenantDomainService).getTenantInfo(tenantId);
        }
    }
    
    @Test
    void testExecute_WithDtoAlreadyHasTenantCode_UsesDtoTenantCode() {
        // Given
        Long tenantId = 1L;
        ProductInfoDto dto = getProductBuilder()
            .tenantCode("DTO_TENANT") // DTO中已有租户编码
            .buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.EDIT_PRODUCT);
        
        TenantInfoDto tenantInfo = getTenantBuilder()
            .tenantId(tenantId)
            .tenantCode("CONTEXT_TENANT") // 上下文中的租户编码
            .buildDto();
        
        when(tenantDomainService.getTenantInfo(tenantId)).thenReturn(tenantInfo);
        
        try (MockedStatic<TenantContextHolder> mockedStatic = mockStatic(TenantContextHolder.class)) {
            mockedStatic.when(TenantContextHolder::getTenantId).thenReturn(tenantId);
            
            // When
            assemblyStep.execute(command);
            
            // Then
            assertNotNull(command.getTenantInfo());
            assertEquals(tenantId, command.getTenantInfo().getTenantId());
            // 应该使用从服务获取的租户信息，而不是DTO中的
            assertEquals("CONTEXT_TENANT", command.getTenantInfo().getTenantCode());
            
            verify(tenantDomainService).getTenantInfo(tenantId);
        }
    }
    
    @Test
    void testExecute_WithNullCommand_ThrowsException() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> assemblyStep.execute(null)
        );
        
        assertEquals("命令不能为空", exception.getMessage());
        
        // 验证没有调用任何服务
        verify(tenantDomainService, never()).getTenantInfo(any());
    }
    
    @Test
    void testGetOrder_ReturnsCorrectValue() {
        // When
        int order = assemblyStep.getOrder();
        
        // Then
        assertEquals(100, order);
    }
    
    @Test
    void testExecute_CommandTenantInfoSetCorrectly() {
        // Given
        Long tenantId = 999L;
        ProductInfoDto dto = getProductBuilder().buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.ADD_PRODUCT);
        
        TenantInfoDto tenantInfo = getTenantBuilder()
            .tenantId(tenantId)
            .tenantCode("SPECIAL_TENANT")
            .tenantName("特殊租户")
            .businessType("HOSPITAL")
            .region("上海市")
            .active(true)
            .buildDto();
        
        when(tenantDomainService.getTenantInfo(tenantId)).thenReturn(tenantInfo);
        
        try (MockedStatic<TenantContextHolder> mockedStatic = mockStatic(TenantContextHolder.class)) {
            mockedStatic.when(TenantContextHolder::getTenantId).thenReturn(tenantId);
            
            // When
            assemblyStep.execute(command);
            
            // Then
            TenantInfoDto resultTenantInfo = command.getTenantInfo();
            assertNotNull(resultTenantInfo);
            assertEquals(tenantId, resultTenantInfo.getTenantId());
            assertEquals("SPECIAL_TENANT", resultTenantInfo.getTenantCode());
            assertEquals("特殊租户", resultTenantInfo.getTenantName());
            assertEquals("HOSPITAL", resultTenantInfo.getBusinessType());
            assertEquals("上海市", resultTenantInfo.getRegion());
            assertTrue(resultTenantInfo.getActive());
        }
    }
    
    @Test
    void testExecute_MultipleCallsSameCommand_OnlyCallsServiceOnce() {
        // Given
        Long tenantId = 1L;
        ProductInfoDto dto = getProductBuilder().buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.EDIT_PRODUCT);
        
        TenantInfoDto tenantInfo = getTenantBuilder()
            .tenantId(tenantId)
            .buildDto();
        
        when(tenantDomainService.getTenantInfo(tenantId)).thenReturn(tenantInfo);
        
        try (MockedStatic<TenantContextHolder> mockedStatic = mockStatic(TenantContextHolder.class)) {
            mockedStatic.when(TenantContextHolder::getTenantId).thenReturn(tenantId);
            
            // When - 第一次执行
            assemblyStep.execute(command);
            
            // 设置命令已有租户信息，模拟重复执行
            assertNotNull(command.getTenantInfo());
            
            // Then
            verify(tenantDomainService, times(1)).getTenantInfo(tenantId);
        }
    }
}