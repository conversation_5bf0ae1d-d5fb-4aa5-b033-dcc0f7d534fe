package com.xyy.saas.inquiry.product.server.builder;

import com.xyy.saas.inquiry.product.api.tenant.dto.TenantInfoDto;
import com.xyy.saas.inquiry.product.server.domain.valueobject.TenantId;

/**
 * 租户测试数据构建器
 * 
 * <AUTHOR> Assistant
 */
public class TenantTestDataBuilder {
    
    private Long tenantId = 1L;
    private String tenantCode = "TEST_TENANT";
    private String tenantName = "测试租户";
    private String businessType = "DRUGSTORE";
    private String region = "北京市";
    private String address = "北京市朝阳区测试街道123号";
    private String contactPerson = "张三";
    private String contactPhone = "***********";
    private Boolean active = true;
    
    public static TenantTestDataBuilder builder() {
        return new TenantTestDataBuilder();
    }
    
    public TenantTestDataBuilder tenantId(Long tenantId) {
        this.tenantId = tenantId;
        return this;
    }
    
    public TenantTestDataBuilder tenantCode(String tenantCode) {
        this.tenantCode = tenantCode;
        return this;
    }
    
    public TenantTestDataBuilder tenantName(String tenantName) {
        this.tenantName = tenantName;
        return this;
    }
    
    public TenantTestDataBuilder businessType(String businessType) {
        this.businessType = businessType;
        return this;
    }
    
    public TenantTestDataBuilder region(String region) {
        this.region = region;
        return this;
    }
    
    public TenantTestDataBuilder address(String address) {
        this.address = address;
        return this;
    }
    
    public TenantTestDataBuilder contactPerson(String contactPerson) {
        this.contactPerson = contactPerson;
        return this;
    }
    
    public TenantTestDataBuilder contactPhone(String contactPhone) {
        this.contactPhone = contactPhone;
        return this;
    }
    
    public TenantTestDataBuilder active(Boolean active) {
        this.active = active;
        return this;
    }
    
    /**
     * 构建TenantInfoDto
     */
    public TenantInfoDto buildDto() {
        TenantInfoDto dto = new TenantInfoDto();
        dto.setTenantId(this.tenantId);
        dto.setTenantCode(this.tenantCode);
        dto.setTenantName(this.tenantName);
        dto.setBusinessType(this.businessType);
        dto.setRegion(this.region);
        dto.setAddress(this.address);
        dto.setContactPerson(this.contactPerson);
        dto.setContactPhone(this.contactPhone);
        dto.setActive(this.active);
        return dto;
    }
    
    /**
     * 构建TenantId值对象
     */
    public TenantId buildTenantId() {
        return new TenantId(this.tenantId, this.tenantCode);
    }
    
    /**
     * 预设数据：药店租户
     */
    public static TenantTestDataBuilder drugstore() {
        return builder()
            .tenantId(1L)
            .tenantCode("DRUGSTORE_001")
            .tenantName("康德乐大药房")
            .businessType("DRUGSTORE")
            .region("上海市")
            .address("上海市浦东新区张江高科技园区");
    }
    
    /**
     * 预设数据：医院租户
     */
    public static TenantTestDataBuilder hospital() {
        return builder()
            .tenantId(2L)
            .tenantCode("HOSPITAL_001")
            .tenantName("北京协和医院")
            .businessType("HOSPITAL")
            .region("北京市")
            .address("北京市东城区东单北大街53号");
    }
    
    /**
     * 预设数据：连锁店租户
     */
    public static TenantTestDataBuilder chainStore() {
        return builder()
            .tenantId(3L)
            .tenantCode("CHAIN_001")
            .tenantName("海王星辰连锁药店")
            .businessType("CHAIN_STORE")
            .region("深圳市")
            .address("深圳市福田区中心五路23号");
    }
    
    /**
     * 预设数据：停用租户
     */
    public static TenantTestDataBuilder inactiveTenant() {
        return builder()
            .tenantId(999L)
            .tenantCode("INACTIVE_001")
            .tenantName("停用租户")
            .businessType("DRUGSTORE")
            .active(false);
    }
}