package com.xyy.saas.inquiry.product.server.application.step;

import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.server.application.command.ProductSaveCommand;
import com.xyy.saas.inquiry.product.server.application.step.impl.ProductValidationStep;
import com.xyy.saas.inquiry.product.server.builder.ProductTestDataBuilder;
import com.xyy.saas.inquiry.product.server.domain.service.ProductValidationService;
import com.xyy.saas.inquiry.product.server.test.BaseUnitTest;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * 商品验证步骤单元测试
 * 
 * <AUTHOR> Assistant
 */
class ProductValidationStepTest extends BaseUnitTest {
    
    @Mock
    private ProductValidationService validationService;
    
    private ProductValidationStep validationStep;
    
    @BeforeEach
    @Override
    protected void setUp() {
        MockitoAnnotations.openMocks(this);
        validationStep = new ProductValidationStep(validationService);
    }
    
    @Test
    void testExecute_ValidProduct_Success() {
        // Given
        ProductInfoDto dto = getProductBuilder()
            .commonName("有效商品")
            .manufacturer("有效厂家")
            .productPref("VALID001")
            .buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.ADD_PRODUCT);
        
        // When
        assertDoesNotThrow(() -> validationStep.execute(command));
        
        // Then
        verify(validationService).validateProductInfo(dto);
        verify(validationService).validateProductUniqueness(dto, ProductBizTypeEnum.ADD_PRODUCT);
        verify(validationService).validateBusinessRules(dto, ProductBizTypeEnum.ADD_PRODUCT);
    }
    
    @Test
    void testExecute_InvalidProductInfo_ThrowsException() {
        // Given
        ProductInfoDto dto = getProductBuilder()
            .commonName("") // 无效的商品名称
            .buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.ADD_PRODUCT);
        
        RuntimeException validationException = new RuntimeException("商品名称不能为空");
        doThrow(validationException).when(validationService).validateProductInfo(dto);
        
        // When & Then
        RuntimeException thrownException = assertThrows(
            RuntimeException.class,
            () -> validationStep.execute(command)
        );
        
        assertEquals("商品名称不能为空", thrownException.getMessage());
        
        verify(validationService).validateProductInfo(dto);
        // 第一个验证失败后，后续验证不应执行
        verify(validationService, never()).validateProductUniqueness(any(), any());
        verify(validationService, never()).validateBusinessRules(any(), any());
    }
    
    @Test
    void testExecute_ProductNotUnique_ThrowsException() {
        // Given
        ProductInfoDto dto = getProductBuilder()
            .productPref("DUPLICATE001")
            .buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.ADD_PRODUCT);
        
        RuntimeException uniquenessException = new RuntimeException("商品编码已存在");
        doThrow(uniquenessException).when(validationService).validateProductUniqueness(dto, ProductBizTypeEnum.ADD_PRODUCT);
        
        // When & Then
        RuntimeException thrownException = assertThrows(
            RuntimeException.class,
            () -> validationStep.execute(command)
        );
        
        assertEquals("商品编码已存在", thrownException.getMessage());
        
        verify(validationService).validateProductInfo(dto);
        verify(validationService).validateProductUniqueness(dto, ProductBizTypeEnum.ADD_PRODUCT);
        // 第二个验证失败后，后续验证不应执行
        verify(validationService, never()).validateBusinessRules(any(), any());
    }
    
    @Test
    void testExecute_BusinessRulesViolation_ThrowsException() {
        // Given
        ProductInfoDto dto = getProductBuilder()
            .buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.EDIT_PRODUCT);
        
        RuntimeException businessRuleException = new RuntimeException("不能编辑已停售商品");
        doThrow(businessRuleException).when(validationService).validateBusinessRules(dto, ProductBizTypeEnum.EDIT_PRODUCT);
        
        // When & Then
        RuntimeException thrownException = assertThrows(
            RuntimeException.class,
            () -> validationStep.execute(command)
        );
        
        assertEquals("不能编辑已停售商品", thrownException.getMessage());
        
        verify(validationService).validateProductInfo(dto);
        verify(validationService).validateProductUniqueness(dto, ProductBizTypeEnum.EDIT_PRODUCT);
        verify(validationService).validateBusinessRules(dto, ProductBizTypeEnum.EDIT_PRODUCT);
    }
    
    @Test
    void testExecute_WithNullCommand_ThrowsException() {
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> validationStep.execute(null)
        );
        
        assertEquals("命令不能为空", exception.getMessage());
        
        // 验证没有调用任何验证服务
        verifyNoInteractions(validationService);
    }
    
    @Test
    void testExecute_WithNullDto_ThrowsException() {
        // Given
        ProductSaveCommand command = new ProductSaveCommand(null, ProductBizTypeEnum.ADD_PRODUCT);
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> validationStep.execute(command)
        );
        
        assertEquals("商品信息不能为空", exception.getMessage());
        
        // 验证没有调用任何验证服务
        verifyNoInteractions(validationService);
    }
    
    @Test
    void testExecute_WithNullBizType_ThrowsException() {
        // Given
        ProductInfoDto dto = getProductBuilder().buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, null);
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> validationStep.execute(command)
        );
        
        assertEquals("业务类型不能为空", exception.getMessage());
        
        // 验证没有调用任何验证服务
        verifyNoInteractions(validationService);
    }
    
    @Test
    void testGetOrder_ReturnsCorrectValue() {
        // When
        int order = validationStep.getOrder();
        
        // Then
        assertEquals(300, order);
    }
    
    @Test
    void testExecute_DifferentBizTypes_CallsCorrectValidations() {
        // Given
        ProductInfoDto dto = getProductBuilder().buildDto();
        
        // Test ADD_PRODUCT
        ProductSaveCommand addCommand = new ProductSaveCommand(dto, ProductBizTypeEnum.ADD_PRODUCT);
        validationStep.execute(addCommand);
        
        verify(validationService).validateProductInfo(dto);
        verify(validationService).validateProductUniqueness(dto, ProductBizTypeEnum.ADD_PRODUCT);
        verify(validationService).validateBusinessRules(dto, ProductBizTypeEnum.ADD_PRODUCT);
        
        // Reset mocks
        reset(validationService);
        
        // Test EDIT_PRODUCT
        ProductSaveCommand editCommand = new ProductSaveCommand(dto, ProductBizTypeEnum.EDIT_PRODUCT);
        validationStep.execute(editCommand);
        
        verify(validationService).validateProductInfo(dto);
        verify(validationService).validateProductUniqueness(dto, ProductBizTypeEnum.EDIT_PRODUCT);
        verify(validationService).validateBusinessRules(dto, ProductBizTypeEnum.EDIT_PRODUCT);
    }
    
    @Test
    void testExecute_ValidationOrder_Correct() {
        // Given
        ProductInfoDto dto = getProductBuilder().buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.ADD_PRODUCT);
        
        // When
        validationStep.execute(command);
        
        // Then - 使用InOrder验证调用顺序
        var inOrder = inOrder(validationService);
        inOrder.verify(validationService).validateProductInfo(dto);
        inOrder.verify(validationService).validateProductUniqueness(dto, ProductBizTypeEnum.ADD_PRODUCT);
        inOrder.verify(validationService).validateBusinessRules(dto, ProductBizTypeEnum.ADD_PRODUCT);
    }
    
    @Test
    void testExecute_ExceptionFromFirstValidation_StopsExecution() {
        // Given
        ProductInfoDto dto = getProductBuilder().buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.ADD_PRODUCT);
        
        RuntimeException firstException = new RuntimeException("第一个验证失败");
        doThrow(firstException).when(validationService).validateProductInfo(dto);
        
        // When & Then
        assertThrows(RuntimeException.class, () -> validationStep.execute(command));
        
        // 验证只调用了第一个验证方法
        verify(validationService).validateProductInfo(dto);
        verify(validationService, never()).validateProductUniqueness(any(), any());
        verify(validationService, never()).validateBusinessRules(any(), any());
    }
    
    @Test
    void testExecute_SpecialCharactersInProductData_HandledCorrectly() {
        // Given - 包含特殊字符的商品数据
        ProductInfoDto dto = getProductBuilder()
            .commonName("特殊商品@#$%")
            .manufacturer("厂家（中国）有限公司")
            .productPref("SPECIAL-001")
            .buildDto();
        ProductSaveCommand command = new ProductSaveCommand(dto, ProductBizTypeEnum.ADD_PRODUCT);
        
        // When
        assertDoesNotThrow(() -> validationStep.execute(command));
        
        // Then
        verify(validationService).validateProductInfo(dto);
        verify(validationService).validateProductUniqueness(dto, ProductBizTypeEnum.ADD_PRODUCT);
        verify(validationService).validateBusinessRules(dto, ProductBizTypeEnum.ADD_PRODUCT);
    }
}