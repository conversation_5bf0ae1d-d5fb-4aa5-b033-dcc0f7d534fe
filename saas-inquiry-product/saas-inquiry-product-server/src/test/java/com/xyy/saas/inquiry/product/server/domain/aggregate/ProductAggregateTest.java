package com.xyy.saas.inquiry.product.server.domain.aggregate;

import com.xyy.saas.inquiry.product.api.product.dto.ProductUseInfo;
import com.xyy.saas.inquiry.product.enums.ProductStatusEnum;
import com.xyy.saas.inquiry.product.server.builder.ProductTestDataBuilder;
import com.xyy.saas.inquiry.product.server.builder.TenantTestDataBuilder;
import com.xyy.saas.inquiry.product.server.domain.entity.ProductInfo;
import com.xyy.saas.inquiry.product.server.domain.entity.ProductStdlib;
import com.xyy.saas.inquiry.product.server.domain.event.ProductCreatedEvent;
import com.xyy.saas.inquiry.product.server.domain.event.ProductStatusChangedEvent;
import com.xyy.saas.inquiry.product.server.domain.event.ProductUpdatedEvent;
import com.xyy.saas.inquiry.product.server.domain.valueobject.ProductFlag;
import com.xyy.saas.inquiry.product.server.domain.valueobject.TenantId;
import com.xyy.saas.inquiry.product.server.test.BaseUnitTest;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 商品聚合根单元测试
 * 
 * <AUTHOR> Assistant
 */
class ProductAggregateTest extends BaseUnitTest {
    
    @Test
    void testCreate_Success() {
        // Given
        ProductInfo productInfo = getProductBuilder().buildEntity();
        ProductStdlib productStdlib = getProductBuilder().buildStdlibEntity();
        ProductUseInfo useInfo = new ProductUseInfo();
        com.xyy.saas.inquiry.product.api.product.dto.ProductQualification qualification = 
            new com.xyy.saas.inquiry.product.api.product.dto.ProductQualification();
        ProductFlag flag = ProductFlag.defaultFlag();
        TenantId tenantId = getTenantBuilder().buildTenantId();
        
        // When
        ProductAggregate aggregate = ProductAggregate.create(
            productInfo, productStdlib, useInfo, qualification, flag, tenantId
        );
        
        // Then
        assertNotNull(aggregate);
        assertEquals(productInfo, aggregate.getProductInfo());
        assertEquals(productStdlib, aggregate.getProductStdlib());
        assertEquals(useInfo, aggregate.getProductUseInfo());
        assertEquals(qualification, aggregate.getProductQualification());
        assertEquals(flag, aggregate.getProductFlag());
        assertEquals(tenantId, aggregate.getTenantId());
        
        // 验证领域事件
        List<Object> domainEvents = aggregate.getDomainEvents();
        assertEquals(1, domainEvents.size());
        assertTrue(domainEvents.get(0) instanceof ProductCreatedEvent);
        
        ProductCreatedEvent event = (ProductCreatedEvent) domainEvents.get(0);
        assertEquals(productInfo.getProductPref(), event.getProductPref());
        assertEquals(tenantId, event.getTenantId());
    }
    
    @Test
    void testCreate_WithNullProductInfo_ThrowsException() {
        // Given
        ProductStdlib productStdlib = getProductBuilder().buildStdlibEntity();
        ProductUseInfo useInfo = new ProductUseInfo();
        com.xyy.saas.inquiry.product.api.product.dto.ProductQualification qualification = 
            new com.xyy.saas.inquiry.product.api.product.dto.ProductQualification();
        ProductFlag flag = ProductFlag.defaultFlag();
        TenantId tenantId = getTenantBuilder().buildTenantId();
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> ProductAggregate.create(null, productStdlib, useInfo, qualification, flag, tenantId)
        );
        
        assertEquals("商品信息不能为空", exception.getMessage());
    }
    
    @Test
    void testCreate_WithNullTenantId_ThrowsException() {
        // Given
        ProductInfo productInfo = getProductBuilder().buildEntity();
        ProductStdlib productStdlib = getProductBuilder().buildStdlibEntity();
        ProductUseInfo useInfo = new ProductUseInfo();
        com.xyy.saas.inquiry.product.api.product.dto.ProductQualification qualification = 
            new com.xyy.saas.inquiry.product.api.product.dto.ProductQualification();
        ProductFlag flag = ProductFlag.defaultFlag();
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> ProductAggregate.create(productInfo, productStdlib, useInfo, qualification, flag, null)
        );
        
        assertEquals("租户ID不能为空", exception.getMessage());
    }
    
    @Test
    void testReconstruct_Success() {
        // Given
        ProductInfo productInfo = getProductBuilder().buildEntity();
        ProductStdlib productStdlib = getProductBuilder().buildStdlibEntity();
        ProductUseInfo useInfo = new ProductUseInfo();
        com.xyy.saas.inquiry.product.api.product.dto.ProductQualification qualification = 
            new com.xyy.saas.inquiry.product.api.product.dto.ProductQualification();
        ProductFlag flag = ProductFlag.defaultFlag();
        TenantId tenantId = getTenantBuilder().buildTenantId();
        
        // When
        ProductAggregate aggregate = ProductAggregate.reconstruct(
            productInfo, productStdlib, useInfo, qualification, flag, tenantId
        );
        
        // Then
        assertNotNull(aggregate);
        assertEquals(productInfo, aggregate.getProductInfo());
        
        // 重构时不应产生领域事件
        List<Object> domainEvents = aggregate.getDomainEvents();
        assertTrue(domainEvents.isEmpty());
    }
    
    @Test
    void testUpdateProductInfo_Success() {
        // Given
        ProductAggregate aggregate = getProductBuilder().buildAggregate();
        ProductInfo newProductInfo = getProductBuilder()
            .commonName("更新后的商品名称")
            .price(new BigDecimal("30.00"))
            .buildEntity();
        
        // When
        aggregate.updateProductInfo(newProductInfo);
        
        // Then
        assertEquals("更新后的商品名称", aggregate.getProductInfo().getCommonName());
        assertEquals(new BigDecimal("30.00"), aggregate.getProductInfo().getPrice());
        
        // 验证领域事件
        List<Object> domainEvents = aggregate.getDomainEvents();
        assertEquals(2, domainEvents.size()); // 创建事件 + 更新事件
        assertTrue(domainEvents.get(1) instanceof ProductUpdatedEvent);
    }
    
    @Test
    void testUpdateProductInfo_WithNull_ThrowsException() {
        // Given
        ProductAggregate aggregate = getProductBuilder().buildAggregate();
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            () -> aggregate.updateProductInfo(null)
        );
        
        assertEquals("商品信息不能为空", exception.getMessage());
    }
    
    @Test
    void testChangeStatus_Success() {
        // Given
        ProductAggregate aggregate = getProductBuilder().buildAggregate();
        ProductStatusEnum oldStatus = aggregate.getProductInfo().getStatus();
        ProductStatusEnum newStatus = ProductStatusEnum.FIRST_AUDITING;
        String changeReason = "提交审核";
        String operatorId = "admin";
        
        // When
        aggregate.changeStatus(newStatus, changeReason, operatorId);
        
        // Then
        assertEquals(newStatus, aggregate.getProductInfo().getStatus());
        
        // 验证领域事件
        List<Object> domainEvents = aggregate.getDomainEvents();
        assertEquals(2, domainEvents.size()); // 创建事件 + 状态变更事件
        assertTrue(domainEvents.get(1) instanceof ProductStatusChangedEvent);
        
        ProductStatusChangedEvent event = (ProductStatusChangedEvent) domainEvents.get(1);
        assertEquals(oldStatus, event.getOldStatus());
        assertEquals(newStatus, event.getNewStatus());
        assertEquals(changeReason, event.getChangeReason());
        assertEquals(operatorId, event.getOperatorId());
    }
    
    @Test
    void testChangeStatus_SameStatus_NoEvent() {
        // Given
        ProductAggregate aggregate = getProductBuilder().buildAggregate();
        ProductStatusEnum currentStatus = aggregate.getProductInfo().getStatus();
        
        // When
        aggregate.changeStatus(currentStatus, "无变化", "admin");
        
        // Then
        assertEquals(currentStatus, aggregate.getProductInfo().getStatus());
        
        // 验证不产生状态变更事件
        List<Object> domainEvents = aggregate.getDomainEvents();
        assertEquals(1, domainEvents.size()); // 只有创建事件
        assertTrue(domainEvents.get(0) instanceof ProductCreatedEvent);
    }
    
    @Test
    void testUpdateProductFlag_Success() {
        // Given
        ProductAggregate aggregate = getProductBuilder().buildAggregate();
        ProductFlag newFlag = ProductFlag.builder()
            .stopSale(true)
            .specialPrice(true)
            .build();
        
        // When
        aggregate.updateProductFlag(newFlag);
        
        // Then
        assertEquals(newFlag, aggregate.getProductFlag());
        assertTrue(aggregate.getProductFlag().getStopSale());
        assertTrue(aggregate.getProductFlag().getSpecialPrice());
    }
    
    @Test
    void testUpdateProductUseInfo_Success() {
        // Given
        ProductAggregate aggregate = getProductBuilder().buildAggregate();
        ProductUseInfo newUseInfo = new ProductUseInfo();
        newUseInfo.setUsage("新的用法");
        newUseInfo.setDosage("新的剂量");
        
        // When
        aggregate.updateProductUseInfo(newUseInfo);
        
        // Then
        assertEquals(newUseInfo, aggregate.getProductUseInfo());
        assertEquals("新的用法", aggregate.getProductUseInfo().getUsage());
        assertEquals("新的剂量", aggregate.getProductUseInfo().getDosage());
    }
    
    @Test
    void testClearDomainEvents_Success() {
        // Given
        ProductAggregate aggregate = getProductBuilder().buildAggregate();
        
        // 产生一些事件
        aggregate.changeStatus(ProductStatusEnum.FIRST_AUDITING, "测试", "admin");
        
        // When
        List<Object> events = aggregate.getDomainEvents();
        aggregate.clearDomainEvents();
        
        // Then
        assertFalse(events.isEmpty()); // 获取到的事件不为空
        assertTrue(aggregate.getDomainEvents().isEmpty()); // 清理后为空
    }
    
    @Test
    void testIsNew_Success() {
        // Given - 新建聚合
        ProductAggregate newAggregate = getProductBuilder()
            .id(null)
            .buildAggregate();
        
        // Given - 已存在聚合
        ProductAggregate existingAggregate = getProductBuilder()
            .id(1L)
            .buildAggregate();
        
        // When & Then
        assertTrue(newAggregate.isNew());
        assertFalse(existingAggregate.isNew());
    }
    
    @Test
    void testValidate_ValidProduct_Success() {
        // Given
        ProductAggregate aggregate = getProductBuilder()
            .commonName("有效商品")
            .manufacturer("有效厂家")
            .productPref("VALID001")
            .buildAggregate();
        
        // When & Then - 不应抛出异常
        assertDoesNotThrow(aggregate::validate);
    }
    
    @Test
    void testValidate_InvalidProduct_ThrowsException() {
        // Given - 商品名称为空
        ProductAggregate aggregate = getProductBuilder()
            .commonName("")
            .buildAggregate();
        
        // When & Then
        IllegalArgumentException exception = assertThrows(
            IllegalArgumentException.class,
            aggregate::validate
        );
        
        assertTrue(exception.getMessage().contains("商品名称不能为空"));
    }
}