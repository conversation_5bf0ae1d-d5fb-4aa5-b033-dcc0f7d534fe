# æµè¯è¿è¡å¨éç½®æä»¶

# æµè¯åç±»éç½®
test.categories.unit=unit
test.categories.integration=integration
test.categories.performance=performance

# æµè¯æ°æ®éç½®
test.data.cleanup.enabled=true
test.data.reset.sequence=true

# æ§è½æµè¯éç½®
performance.test.enabled=false
performance.test.thread.count=10
performance.test.batch.size=100
performance.test.timeout.seconds=60

# å¹¶åæµè¯éç½®
concurrent.test.enabled=true
concurrent.test.thread.count=5
concurrent.test.operations.per.thread=20

# æ°æ®åºæµè¯éç½®
database.test.show.sql=false
database.test.format.sql=true
database.test.connection.pool.size=5

# Mockéç½®
mock.external.services=true
mock.tenant.service=true
mock.validation.service=false

# æ¥åéç½®
test.report.enabled=true
test.report.format=html,xml
test.report.output.dir=target/test-reports

# è¦ççéç½®
coverage.enabled=true
coverage.threshold.line=80
coverage.threshold.branch=70
coverage.threshold.method=85

# JVMéç½®ï¼ç¨äºæµè¯è¿è¡ï¼
test.jvm.args=-Xmx2g -XX:+UseG1GC -XX:+PrintGCDetails