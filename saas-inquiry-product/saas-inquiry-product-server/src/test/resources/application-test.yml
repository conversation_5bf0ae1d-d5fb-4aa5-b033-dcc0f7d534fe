# 测试环境配置
spring:
  profiles:
    active: test
    
  # 数据源配置
  datasource:
    driver-class-name: org.h2.Driver
    url: jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    username: sa
    password: 
    
  # H2 数据库控制台（仅测试环境）
  h2:
    console:
      enabled: true
      path: /h2-console
      
  # JPA配置
  jpa:
    hibernate:
      ddl-auto: create-drop
    show-sql: false
    properties:
      hibernate:
        format_sql: true
        use_sql_comments: false
        
  # 事务配置
  transaction:
    rollback-on-commit-failure: true
    
  # 缓存配置（测试环境禁用）
  cache:
    type: none
    
# 业务配置
product:
  service:
    refactored:
      enabled: true  # 启用重构后的服务
      
# 租户配置
tenant:
  enable: true
  
# 日志配置
logging:
  level:
    com.xyy.saas.inquiry.product: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
    org.springframework.transaction: DEBUG
    
  pattern:
    console: "%d{HH:mm:ss.SSS} [%thread] %-5level %logger{36} - %msg%n"
    
# 测试配置
test:
  database:
    cleanup-after-test: true
    show-sql: false
    
  performance:
    enabled: false  # 默认不启用性能测试
    
# MyBatis Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.slf4j.Slf4jImpl
  global-config:
    db-config:
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0
      
# 线程池配置（测试环境使用较小的线程池）
async:
  executor:
    thread:
      core-pool-size: 2
      max-pool-size: 4
      queue-capacity: 10
      keep-alive-seconds: 60
      thread-name-prefix: test-async-
      
# 监控配置（测试环境禁用）
management:
  endpoints:
    enabled-by-default: false
  endpoint:
    health:
      enabled: true
  health:
    defaults:
      enabled: false