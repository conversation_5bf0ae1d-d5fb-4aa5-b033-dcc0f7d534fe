package com.xyy.saas.inquiry.product.server.service.bpm.validator;

import com.xyy.saas.inquiry.product.enums.BpmBusinessTypeEnum;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmApprovalValidateResult;

import java.util.Map;

/**
 * 审批验证器接口
 */
public interface BpmApprovalValidator {
    /**
     * 验证审批
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param params 验证参数
     * @return 验证结果
     */
    BpmApprovalValidateResult validate(String taskId, Long userId, Map<String, Object> params);
    
    /**
     * 获取支持的业务类型
     * @return 业务类型
     */
    BpmBusinessTypeEnum[] supportBusinessTypes();
    
    /**
     * 获取验证器类型
     * @return 验证器类型
     */
    String getType();
} 