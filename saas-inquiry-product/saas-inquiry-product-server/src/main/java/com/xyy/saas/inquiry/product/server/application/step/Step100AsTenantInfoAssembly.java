package com.xyy.saas.inquiry.product.server.application.step;

import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.product.server.application.context.ProductSaveContext;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 租户信息组装步骤
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
@Order(100) // 优先级最高，最先执行
public class Step100AsTenantInfoAssembly implements ProductSaveStep {
    
    @Resource
    private TenantApi tenantApi;
    
    @Override
    public boolean isApplicable(ProductSaveContext context) {
        // 总是适用
        return true;
    }
    
    @Override
    public void execute(ProductSaveContext context) {
        log.info("[Step100AsTenantInfoAssembly] 开始组装租户信息");
        
        try {
            // 获取租户信息
            TenantDto tenant;
            if (context.getCurrentDto().getTenantId() == null) {
                // 使用当前租户
                tenant = tenantApi.getTenant();
                context.getCurrentDto().setTenant(tenant);
            } else {
                // 使用指定租户
                tenant = tenantApi.getTenant(context.getCurrentDto().getTenantId());
                context.getCurrentDto().setTenant(tenant);
            }
            
            // 设置总部租户ID
            if (context.getCurrentDto().getHeadTenantId() == null) {
                context.getCurrentDto().setHeadTenantId(tenant.getHeadTenantId());
            }
            
            // 保存到上下文
            context.setTenant(tenant);
            
            log.info("[Step100AsTenantInfoAssembly] 租户信息组装完成, tenantId={}, headTenantId={}",
                tenant.getId(), tenant.getHeadTenantId());
                
        } catch (Exception e) {
            log.error("[Step100AsTenantInfoAssembly] 租户信息组装失败", e);
            context.markFailure("租户信息组装失败: " + e.getMessage());
        }
    }
    
    @Override
    public String getStepDescription() {
        return "组装租户信息";
    }
}