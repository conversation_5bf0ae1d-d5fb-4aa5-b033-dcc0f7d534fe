package com.xyy.saas.inquiry.product.server.service.forward.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 用法用量药品规则
 *
 * <AUTHOR>
 * @Date 9/9/24 4:44 PM
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UsageAndDosageDrugRuleForwardDto implements Serializable {

    private List<UsageAndDosageDrugRuleDetailDto> usageAndDosageDrugRuleDetailDtoList;

    @Data
    @Builder
    public static class UsageAndDosageDrugRuleDetailDto implements Serializable {

        /**
         * 药品名称
         */
        private String commonName;

        /**
         * 规格
         */
        private String specification;

        /**
         * 单次剂量最低值
         */
        private BigDecimal minSingleDose;

        /**
         * 单次剂量最高值
         */
        private BigDecimal maxSingleDose;

        /**
         * 用药频次集合
         */
        private List<String> useFrequencyListList;

        /**
         * 给药单位
         */
        private String singleUnit;

        /**
         * 给药途径
         */
        private String directions;

    }

}
