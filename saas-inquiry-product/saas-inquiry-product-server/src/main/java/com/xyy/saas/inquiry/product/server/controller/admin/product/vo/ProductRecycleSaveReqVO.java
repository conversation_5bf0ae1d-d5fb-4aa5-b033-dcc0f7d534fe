package com.xyy.saas.inquiry.product.server.controller.admin.product.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Schema(description = "管理后台 - 商品回收站保存 Request VO")
@Data
public class ProductRecycleSaveReqVO {

    @Schema(description = "商品ID列表")
    @NotEmpty(message = "商品ID列表不能为空")
    private List<Long> idList;

    @Schema(description = "删除类型", example = "1")
    @NotNull(message = "删除类型不能为空")
    private Integer deleteType;

} 