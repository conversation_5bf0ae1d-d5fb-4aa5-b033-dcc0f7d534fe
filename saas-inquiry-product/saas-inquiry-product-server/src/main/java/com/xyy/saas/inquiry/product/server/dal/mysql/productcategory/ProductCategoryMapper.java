package com.xyy.saas.inquiry.product.server.dal.mysql.productcategory;

import java.util.*;

import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.inquiry.product.server.dal.dataobject.productcategory.ProductCategoryDO;
import com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo.ProductCategoryListReqVO;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;

/**
 * 商品六级分类 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductCategoryMapper extends BaseMapperX<ProductCategoryDO> {

    default List<ProductCategoryDO> selectList(ProductCategoryListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<ProductCategoryDO>()
                .likeIfPresent(ProductCategoryDO::getName, reqVO.getName())
                .eqIfPresent(ProductCategoryDO::getDictId, reqVO.getDictId())
                .eqIfPresent(ProductCategoryDO::getParentDictId, reqVO.getParentDictId())
                .eqIfPresent(ProductCategoryDO::getSortOrder, reqVO.getSortOrder())
                .eqIfPresent(ProductCategoryDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(ProductCategoryDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProductCategoryDO::getId));
    }

	default ProductCategoryDO selectByParentDictIdAndName(Long parentDictId, String name) {
	    return selectOne(ProductCategoryDO::getParentDictId, parentDictId, ProductCategoryDO::getName, name);
	}

	default ProductCategoryDO selectByDictId(Long dictId) {
	    return selectOne(ProductCategoryDO::getDictId, dictId);
	}

	default List<ProductCategoryDO> selectByDictIdList(List<Long> dictIdList) {
        if (CollectionUtils.isEmpty(dictIdList)) {
            return List.of();
        }
	    return selectList(ProductCategoryDO::getDictId, dictIdList);
	}

    default Long selectCountByParentDictId(Long parentDictId) {
        return selectCount(ProductCategoryDO::getParentDictId, parentDictId);
    }

}