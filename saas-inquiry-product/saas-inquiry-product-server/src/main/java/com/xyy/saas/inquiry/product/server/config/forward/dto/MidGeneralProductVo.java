package com.xyy.saas.inquiry.product.server.config.forward.dto;


import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class MidGeneralProductVo implements Serializable {
    @Serial
    private static final long serialVersionUID = -4170878237647676475L;
    private String productId;
    private String businessCode;
    private String originalProductCode;
    private List<String> productIdList;
    private List<String> businessCodeList;
    private List<String> originalProductCodeList;
    private String traceId;
    private Boolean isFilterSauAttr = false;

}
