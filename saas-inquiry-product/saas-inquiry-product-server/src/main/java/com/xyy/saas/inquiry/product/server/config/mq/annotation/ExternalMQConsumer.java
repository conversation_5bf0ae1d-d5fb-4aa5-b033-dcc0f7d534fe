package com.xyy.saas.inquiry.product.server.config.mq.annotation;

import com.xyy.saas.inquiry.product.server.config.mq.enums.ExternalMQClusterEnum;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;

@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
public @interface ExternalMQConsumer {
    /**
     * 消费者组
     */
    String consumerGroup();

    /**
     * Topic
     */
    String topic();

    /**
     * 集群
     */
    ExternalMQClusterEnum cluster();
} 