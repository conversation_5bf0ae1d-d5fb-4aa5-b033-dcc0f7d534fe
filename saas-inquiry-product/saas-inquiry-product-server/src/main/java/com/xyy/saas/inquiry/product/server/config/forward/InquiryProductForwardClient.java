package com.xyy.saas.inquiry.product.server.config.forward;

import com.xyy.saas.inquiry.pojo.ForwardResult;
import com.xyy.saas.inquiry.product.api.search.dto.InquiryDiagnosticsSearchRespDto;
import com.xyy.saas.inquiry.product.server.controller.app.search.vo.InquiryProductAddReqVO;
import com.xyy.saas.inquiry.product.server.service.forward.dto.DiagnosticsForwardDto;
import com.xyy.saas.inquiry.product.server.service.forward.dto.ProductForwardDto;
import com.xyy.saas.inquiry.product.server.service.forward.dto.ProductStandardForwardDto;
import java.util.List;
import com.xyy.saas.inquiry.product.server.service.forward.dto.UsageAndDosageDrugRuleForwardDto;
import com.xyy.saas.inquiry.product.server.service.forward.dto.UsageAndDosageRuleDrugQueryForwardDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

/**
 * @Author:chenxiaoyi
 * @Date:2024/11/27 20:41
 */


@HttpExchange(accept = "application/json", contentType = "application/json")
public interface InquiryProductForwardClient {

    /**
     * 转发 商品推荐词搜索
     *
     * @return ForwardResult
     */
    @PostExchange("/product/forward/getSuggestNamesByProductsNameV2")
    ForwardResult<List<String>> getSuggestNamesByProductsNameV2(@RequestBody ProductForwardDto productForwardDto);

    /**
     * 转发 搜索商品信息
     *
     * @return ForwardResult
     */
    @PostExchange("/product/forward/getProductsByNameAndSpecV2")
    ForwardResult<List<ProductStandardForwardDto>> getProductsByNameAndSpecV2(@RequestBody ProductForwardDto productForwardDto);

    /**
     * 转发 搜索商品根据69码
     *
     * @return ForwardResult
     */
    @PostExchange("/product/forward/getMedicinesByBarcode")
    ForwardResult<List<ProductStandardForwardDto>> getMedicinesByBarcode(@RequestBody ProductForwardDto productForwardDto);

    /**
     * 转发 搜索商品关联诊断信息
     *
     * @return ForwardResult
     */
    @PostExchange("/product/forward/getDiagnostics")
    ForwardResult<List<InquiryDiagnosticsSearchRespDto>> getDiagnostics(@RequestBody DiagnosticsForwardDto diagnosticsForwardDto);

    /**
     * 转发 手动新增商品信息
     *
     * @return ForwardResult
     */
    @PostExchange("/product/forward/addProductForDrugstore")
    ForwardResult<String> addProductForDrugstore(@RequestBody InquiryProductAddReqVO inquiryProductAddReqVO);

    /**
     * 转发 根据标准库id搜索商品信息
     *
     * @return ForwardResult
     */
    @PostExchange("/product/forward/queryProductStandardListByStandardIds")
    ForwardResult<List<ProductStandardForwardDto>> queryProductStandardListByStandardIds(@RequestBody List<String> standardIds);

    /**
     * 转发 根据商品名称和规格查询商品用法用量规则
     *
     * @return ForwardResult
     */
    @PostExchange("/product/forward/queryProductUsageAndDosageRule")
    ForwardResult<UsageAndDosageDrugRuleForwardDto> queryProductUsageAndDosageRule(@RequestBody UsageAndDosageRuleDrugQueryForwardDto usageAndDosageRuleDrugQueryForwardDto);

    /**
     * 转发 根据69码查询商品用法用量规则
     *
     * @return ForwardResult
     */
    @PostExchange("/product/forward/getProductStandardListByBarcodeList")
    ForwardResult<List<ProductStandardForwardDto>> getProductStandardListByBarcodeList(@RequestBody List<String> barCodeList);

    /**
     * 转发 根据批准文号查询商品用法用量规则
     *
     * @return ForwardResult
     */
    @PostExchange("/product/forward/getProductStandardListByApprovalNoList")
    ForwardResult<List<ProductStandardForwardDto>> getProductStandardListByApprovalNoList(@RequestBody List<String> approvalNoList);

    /**
     * 转发 根据商品名称和类型批量查询商品
     *
     * @return ForwardResult
     */
    @PostExchange("/product/forward/getProductStandardByProductNamesAndType")
    ForwardResult<List<ProductStandardForwardDto>> getProductStandardByProductNamesAndType(@RequestBody ProductForwardDto productForwardDto);

}
