package com.xyy.saas.inquiry.product.server.controller.admin.product.vo;

import com.xyy.saas.inquiry.product.server.config.forward.dto.MidSaasWithMixSearchInfoDto;
import com.xyy.saas.inquiry.product.server.service.product.mid.MeProductFiledDescUtil;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;

import static com.xyy.saas.inquiry.constant.SystemConstant.VALIDITY_UNIT_DAY;
import static com.xyy.saas.inquiry.constant.SystemConstant.VALIDITY_UNIT_MONTH;
import static com.xyy.saas.inquiry.constant.SystemConstant.VALIDITY_UNIT_YEAR;


@Data
@Schema(description = "管理后台 - 中台标准库商品信息 VO")
public class StdlibProductVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @Schema(description = "商品id")
    private String id;
    @Schema(description = "商品名称")
    private String brandName;
    @Schema(description = "通用名")
    private String commonName;
    @Schema(description = "规格")
    private String spec;
    @Schema(description = "单位")
    private String unit;
    @Schema(description = "生产厂家")
    private String manufacturer;
    @Schema(description = "剂型")
    private String dosageForm;
    @Schema(description = "条码")
    private String barcode;
    @Schema(description = "批准文号")
    private String approvalNumber;
    @Schema(description = "是否含特殊药品制剂")
    private String containingHempYn;
    @Schema(description = "产地")
    private String producingArea;
    @Schema(description = "本位码")
    private String standardCode;
    @Schema(description = "商品系统分类")
    private Integer systemType;
    @Schema(description = "商品六级分类")
    private String splitCategory;
    @Schema(description = "适应症")
    private String indication;
    @Schema(description = "用法用量")
    private String usageAndDosage;
    @Schema(description = "存储条件")
    private String storageCondition;
    @Schema(description = "商品大类")
    private Integer spuCategory;
    @Schema(description = "商品大类名称")
    private String systemTypeName;
    @Schema(description = "商品六级分类名称")
    private String splitCategoryName;
    @Schema(description = "商品大类名称")
    private String spuCategoryName;
    @Schema(description = "保质期")
    private String productValidity;

    // region 其它属性，显示合并在一起   码数、颜色、受托生产厂家、处方分类、口味、件包装、销售渠道、合并商品
    @Schema(description = "其它属性")
    private String otherAttr;
    
    @Schema(description = "码数")
    private String codeNumber;
    @Schema(description = "颜色")
    private String color;
    @Schema(description = "受托生产厂家")
    private String commissionedManufacturer;
    @Schema(description = "处方分类")
    private String prescriptionClassification;
    @Schema(description = "口味")
    private String taste;
    @Schema(description = "件包装")
    private String packageSau;
    @Schema(description = "销售渠道")
    private String salesChannel;
    @Schema(description = "合并商品")
    private String mergeProduct;
    @Schema(description = "管理属性-处方分类")
    private String manaPrescriptionClassification;

    // endregion

    @Schema(description = "存储属性")
    private Byte shadingAttr;
    @Schema(description = "存储属性")
    private String shadingAttrStr;
    @Schema(description = "进项税率")
    private BigDecimal inputTaxRate;
    @Schema(description = "销项税率")
    private BigDecimal outputTaxRate;
    @Schema(description = "经营范围")
    private String businessScope;
    @Schema(description = "经营范围名称")
    private String businessScopeName;
    @Schema(description = "税务分类编码")
    private String taxCategoryCode;

    @Schema(description = "商品封面图")
    private List<String> coverImages;

    @Schema(description = "商品外包装图")
    private List<String> outerPackageImages;

    @Schema(description = "商品说明书图")
    private List<String> instructionImages;

    @Schema(description = "中台标准库ID")
    private Long midStdlibId;
    @Schema(description = "标准库ID")
    private Long stdlibId;
    @Schema(description = "商品ID")
    private Long productId;
    @Schema(description = "商品外码")
    private String showPref;


    public String getOtherAttr() {
        //组装
        StringBuffer sb = new StringBuffer();
        if(!StringUtils.isEmpty(codeNumber)){
            sb.append(",码数:").append(codeNumber);
        }
        if(!StringUtils.isEmpty(color)){
            sb.append(",颜色:").append(color);
        }
        if(!StringUtils.isEmpty(commissionedManufacturer)){
            sb   .append(",受托生产厂家:").append(commissionedManufacturer);
        }
        if(!StringUtils.isEmpty(manaPrescriptionClassification)){
            sb.append(",处方分类:").append(manaPrescriptionClassification);
        }
        if(!StringUtils.isEmpty(taste)){
            sb.append(",口味:").append(taste);
        }
        if(!StringUtils.isEmpty(packageSau)){
            sb.append(",件包装:").append(packageSau);
        }
        if(!StringUtils.isEmpty(salesChannel)){
            sb.append(",销售渠道:").append(salesChannel);
        }
        if(!StringUtils.isEmpty(mergeProduct)){
            sb.append(",合并商品:").append(mergeProduct);
        }
        return sb.toString().replaceFirst(",","");
    }


    /**
     * 中台标准库数据映射
     * @param item
     * @return
     */
    public static StdlibProductVo of(MidSaasWithMixSearchInfoDto item){
        StdlibProductVo slpvd = new StdlibProductVo();
        slpvd.setCommonName(item.getProductname());
        slpvd.setBrandName(item.getShangpinmingcheng());
        slpvd.setSpec(item.getGuige());
        slpvd.setUnit(item.getDanwei());
        slpvd.setManufacturer(item.getShengchanchangjia());
        slpvd.setDosageForm(item.getJixing());
        slpvd.setPrescriptionClassification(item.getChufangfenlei());
        int shifouhanma = item.getShifouhanma();
        slpvd.setContainingHempYn("" + shifouhanma);
        slpvd.setBarcode(item.getTiaoma());
        slpvd.setApprovalNumber(item.getPizhunwenhao());
        slpvd.setProducingArea(item.getChandi());
        slpvd.setMidStdlibId(item.getProductid());
        // 商品系统分类
        slpvd.setSystemType(item.getFirstCategoryId());
        // 商品系统分类名称
        slpvd.setSystemTypeName(item.getFirstCategoryName());
        slpvd.setSplitCategoryName(item.getSplitCategoryName());
        String stringBuffer = item.getFirstCategory()
            + "," + item.getSecondCategory()
            + "," + item.getThirdCategory()
            + "," + item.getFourthCategory()
            + "," + item.getFiveCategory()
            + "," + item.getSixCategory();
        slpvd.setSplitCategory(stringBuffer);
        slpvd.setIndication(item.getIndication());
        slpvd.setUsageAndDosage(item.getUsageDosage());
        slpvd.setSpuCategory(MeProductFiledDescUtil.spuForPlatformMAp.get(item.getChanpinleibiebianhao()));
        slpvd.setSpuCategoryName(MeProductFiledDescUtil.spuCategoryMap.get(slpvd.getSpuCategory()));
        //>>>>>>>>>>标准库优化新增
        slpvd.setStorageCondition(item.getCunchutiaojian());
        slpvd.setCodeNumber(item.getCodeNumber());
        slpvd.setColor(item.getColor());
        slpvd.setCommissionedManufacturer(item.getCommissionedManufacturer());
        slpvd.setTaste(item.getTaste());
        slpvd.setPackageSau(item.getPackageSau());
        slpvd.setSalesChannel(item.getSalesChannel());
        slpvd.setMergeProduct(item.getMergeProduct());
        //有效期
        slpvd.setProductValidity(getProductValidity(item));
        slpvd.setManaPrescriptionClassification(item.getPrescriptionClassification());//管理属性-处方分类
        slpvd.setShadingAttr(Optional.ofNullable(item.getShadingAttrStr()).map(MeProductFiledDescUtil.shadingAttrRevMap::get).orElse(null));
        slpvd.setShadingAttrStr(item.getShadingAttrStr());
        //<<<<<<<<<<标准库优化新增 结束

        //进项税率
        if (ObjectUtils.isNotEmpty(item.getInRateStr())) {
            String inRate = item.getInRateStr().replaceAll("%", "");
            if (NumberUtils.isCreatable(inRate)) {
                slpvd.setInputTaxRate(new BigDecimal(inRate));
            }
        }
        if (ObjectUtils.isNotEmpty(item.getOutRateStr())) {
            String outRate = item.getOutRateStr().replaceAll("%", "");
            if (NumberUtils.isCreatable(outRate)) {
                slpvd.setOutputTaxRate(new BigDecimal(outRate));
            }
        }
        slpvd.setBusinessScopeName(item.getBusinessScopeName());
        slpvd.setTaxCategoryCode(item.getTaxCategoryCode());
        return slpvd;
    }

    /**
     * 根据主副类型取有效期，都转换成单位月
     * @param item
     * @return
     */
    private static String getProductValidity(MidSaasWithMixSearchInfoDto item){
        String skuType = item.getSkuPrimaryType();
        if(ObjectUtils.isEmpty(skuType)){
            return null;
        }
        Integer usageDay = item.getUsageDay();
        if(usageDay == null){
            return null;
        }
        String unit = item.getUsageDayUnit();
        //转成月
        int month = switch (unit) {
            case VALIDITY_UNIT_YEAR -> usageDay * 12;
            case VALIDITY_UNIT_MONTH -> usageDay;
            case VALIDITY_UNIT_DAY -> usageDay <= 0 ? 0 : Math.max(usageDay / 30, 1);
            default -> 0;
        };
        return month + "";
    }


}
