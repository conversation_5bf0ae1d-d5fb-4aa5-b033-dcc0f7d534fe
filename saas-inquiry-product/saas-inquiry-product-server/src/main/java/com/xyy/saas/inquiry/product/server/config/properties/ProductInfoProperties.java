package com.xyy.saas.inquiry.product.server.config.properties;


import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "product.info")
public class ProductInfoProperties {

    /**
     * 连锁商品条数最多5W（可配置化）
     */
    private long chainProductCountLimit = 50_000L;

    /**
     * 单店商品条数最多1W（可配置化）
     */
    private long storeProductCountLimit = 10_000L;


}
