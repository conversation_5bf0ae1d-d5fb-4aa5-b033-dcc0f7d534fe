package com.xyy.saas.inquiry.product.server.application.step;

import com.xyy.saas.inquiry.product.server.application.context.ProductSaveContext;
import com.xyy.saas.inquiry.product.server.application.strategy.ProductBizTypeHandler;
import com.xyy.saas.inquiry.product.server.application.strategy.ProductBizTypeHandlerFactory;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibDO;
import com.xyy.saas.inquiry.product.server.service.product.ProductStdlibService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 标准库处理步骤
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
@Order(500)
public class Step500AsStdlibProcessing implements ProductSaveStep {
    
    @Resource
    private ProductStdlibService stdlibService;
    
    @Resource
    private ProductBizTypeHandlerFactory bizTypeHandlerFactory;
    
    @Override
    public boolean isApplicable(ProductSaveContext context) {
        // 检查是否需要处理标准库
        return Boolean.TRUE.equals(context.getNeedProcessStdlib());
    }
    
    @Override
    public void execute(ProductSaveContext context) {
        log.info("[Step500AsStdlibProcessing] 开始处理商品标准库");
        
        try {
            // 获取业务类型处理器
            ProductBizTypeHandler handler = bizTypeHandlerFactory.getHandler(context.getBizType());
            
            // 检查是否需要处理标准库
            if (handler != null && !handler.shouldProcessStdlib()) {
                log.info("[Step500AsStdlibProcessing] 业务类型 {} 不需要处理标准库", context.getBizType());
                return;
            }
            
            // 校验商品标准库id是否重复
            boolean throwExceptionIfUnique = handler != null && handler.shouldThrowStdlibUniqueException();
            validateProductStdlibUnique(context, throwExceptionIfUnique);
            
            // 处理标准库
            ProductStdlibDO stdlibDO = stdlibService.saveOrUpdateStdlib(context.getCurrentDto(), true);
            
            // 更新DTO中的标准库信息
            if (stdlibDO != null) {
                context.getCurrentDto().setStdlibId(stdlibDO.getId());
                context.getCurrentDto().setMidStdlibId(stdlibDO.getMidStdlibId());
            }
            
            log.info("[Step500AsStdlibProcessing] 标准库处理完成, stdlibId={}, midStdlibId={}",
                stdlibDO != null ? stdlibDO.getId() : null, 
                stdlibDO != null ? stdlibDO.getMidStdlibId() : null);
            
        } catch (Exception e) {
            log.error("[Step500AsStdlibProcessing] 标准库处理失败", e);
            context.markFailure("标准库处理失败: " + e.getMessage());
        }
    }
    
    /**
     * 校验商品标准库ID唯一性
     */
    private void validateProductStdlibUnique(ProductSaveContext context, boolean throwExceptionIfUnique) {
        // 这里简化处理，实际应该调用原有的校验逻辑
        // 如果标准库id重复，根据throwExceptionIfUnique决定是抛异常还是改为修改数据
        log.debug("[Step500AsStdlibProcessing] 校验标准库唯一性, throwExceptionIfUnique={}", throwExceptionIfUnique);
        
        // TODO: 实现具体的唯一性校验逻辑
        // 可以参考原有的 validateProductStdlibUnique 方法
    }
    
    @Override
    public String getStepDescription() {
        return "处理商品标准库信息";
    }
}