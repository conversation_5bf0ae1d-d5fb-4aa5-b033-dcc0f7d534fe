package com.xyy.saas.inquiry.product.server.application.strategy;

import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 商品业务类型处理器工厂
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class ProductBizTypeHandlerFactory {
    
    private final Map<ProductBizTypeEnum, ProductBizTypeHandler> handlerMap;
    
    /**
     * 构造函数 - Spring自动注入所有处理器
     */
    @Autowired
    public ProductBizTypeHandlerFactory(List<ProductBizTypeHandler> handlers) {
        this.handlerMap = handlers.stream()
            .collect(Collectors.toMap(
                ProductBizTypeHandler::getSupportedBizType,
                Function.identity(),
                (existing, replacement) -> {
                    log.warn("发现重复的业务类型处理器: {} 和 {}, 使用: {}", 
                        existing.getHandlerName(), replacement.getHandlerName(), existing.getHandlerName());
                    return existing;
                }
            ));
        
        log.info("初始化业务类型处理器工厂, 共注册 {} 个处理器: {}", 
            handlerMap.size(), 
            handlerMap.values().stream()
                .map(ProductBizTypeHandler::getHandlerName)
                .collect(Collectors.joining(", ")));
    }
    
    /**
     * 获取业务类型处理器
     * 
     * @param bizType 业务类型
     * @return 处理器实例，如果没有找到则返回null
     */
    public ProductBizTypeHandler getHandler(ProductBizTypeEnum bizType) {
        if (bizType == null) {
            return null;
        }
        
        ProductBizTypeHandler handler = handlerMap.get(bizType);
        if (handler == null) {
            log.warn("未找到业务类型 {} 对应的处理器", bizType);
        }
        
        return handler;
    }
    
    /**
     * 检查是否支持指定的业务类型
     */
    public boolean isSupported(ProductBizTypeEnum bizType) {
        return bizType != null && handlerMap.containsKey(bizType);
    }
    
    /**
     * 获取所有支持的业务类型
     */
    public java.util.Set<ProductBizTypeEnum> getSupportedBizTypes() {
        return handlerMap.keySet();
    }
}