package com.xyy.saas.inquiry.product.server.controller.admin.product.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static com.xyy.saas.inquiry.product.enums.ProductMixedQueryTypeEnum.fromCode;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.xyy.saas.inquiry.product.api.product.dto.ProductFlag;
import com.xyy.saas.inquiry.product.enums.ProductMixedQueryTypeEnum;
import com.xyy.saas.inquiry.product.enums.ProductStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 商品基本信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductInfoPageReqVO extends PageParam {

    @Schema(description = "商品ID列表")
    private List<Long> idList;
    @Schema(description = "商品编码列表")
    private List<String> prefList;

    @Schema(description = "租户编码")
    private Long tenantId;

    @Schema(description = "租户编码（总部）")
    private Long headTenantId;

    @Schema(description = "标准库ID", example = "21163")
    private List<Long> stdlibIdList;
    @Schema(description = "标准库ID（中台）", example = "21163")
    private List<Long> midStdlibIdList;
    @Schema(description = "商品编码列表")
    private List<String> showPrefList;
    @Schema(description = "条形码", example = "21163")
    private List<String> barcodeList;
    @Schema(description = "通用名（模糊）", example = "21163")
    private String commonNameLike;
    @Schema(description = "品牌名（模糊）", example = "21163")
    private String brandNameLike;

    @Schema(description = "1 - 多个条形码；2 - 多个商品编码；3 - 多个标准库ID；4 - 单个通用名称；5 - 单个品牌名称", example = "21163")
    private Integer mixedQueryType;
    @Schema(description = "商品信息（商品编码，通用名，品牌名，条形码，助记码，批准文号，生产厂家）", example = "21163")
    private String mixedQuery;

    @Schema(description = "商品分类（一级分类）", example = "23148")
    private String firstCategory;

    @Schema(description = "单位", example = "23148")
    private String unit;

    @Schema(description = "剂型", example = "21146")
    private String dosageForm;

    @Schema(description = "所属范围", example = "6843")
    private String businessScope;

    @Schema(description = "处方分类", example = "8689")
    private String presCategory;

    @Schema(description = "储存条件", example = "1588")
    private String storageWay;

    @Schema(description = "生产厂家")
    private String manufacturer;

    @Schema(description = "状态", example = "1")
    private Integer status;
    @Schema(description = "状态集合", example = "1")
    private List<Integer> statusList;



    @Schema(description = "排除状态集合", example = "1")
    private List<Integer> excludeStatusList;

    @Schema(description = "是否禁用", example = "2")
    private Boolean disable;

    @Schema(description = "删除类型", example = "1")
    private Integer deleteType;

    @Schema(description = "删除时间")
    private LocalDateTime[] deletedAt;

    @Schema(description = "是否删除")
    private Boolean deleted = false;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;



    // 多属性标志
    @Schema(description = "多属性标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductFlag productFlag;

    // 禁采
    @Schema(description = "禁采状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer purchaseDisabled;
    // 基础信息属性（门店）
    @Schema(description = "是否拆零品", example = "true")
    private Boolean unbundled;
    @Schema(description = "是否停售", example = "true")
    private Boolean stopSale;
    @Schema(description = "是否总部禁采", example = "true")
    private Boolean headPurchaseDisabled;
    @Schema(description = "是否门店禁采", example = "true")
    private Boolean storePurchaseDisabled;
    @Schema(description = "是否特价商品", example = "true")
    private Boolean specialPrice;
    @Schema(description = "是否积分商品", example = "true")
    private Boolean integral;

    // 标准库商品属性（全局）
    @Schema(description = "是否进口（非国产）", example = "true")
    private Boolean imported;
    @Schema(description = "是否含特殊药品复方制剂", example = "true")
    private Boolean hasSpecialDrugCompound;
    @Schema(description = "中台已停用", example = "true")
    private Boolean midDeactivated;
    @Schema(description = "中台同步是否跳过（不覆盖）", example = "true")
    private Boolean midSyncSkipped;




    @Schema(description = "审批状态 1-待审批 2-已通过 3-已驳回", example = "1")
    private Integer approvalStatus;


    /**
     * 排除暂存状态
     * @return
     */
    public ProductInfoPageReqVO excludeTempStatus() {
        // 过滤问诊临时建品
        if (this.status == null && this.statusList == null && this.excludeStatusList == null) {
            this.excludeStatusList = List.of(ProductStatusEnum.TEMP.code);
        }
        return this;
    }

    /**
     * 转换审批状态
     */
    public ProductInfoPageReqVO transformApprovalStatus() {
        if (this.approvalStatus == null || this.statusList != null) {
            return this;
        }
        return switch (this.approvalStatus) {
            case 1 -> this.setStatusList(List.of(ProductStatusEnum.MID_AUDITING.code, ProductStatusEnum.HEAD_AUDITING.code, ProductStatusEnum.FIRST_AUDITING.code));
            case 2 -> this.setStatusList(List.of(ProductStatusEnum.USING.code));
            case 3 -> this.setStatusList(List.of(ProductStatusEnum.MID_AUDIT_REJECT.code, ProductStatusEnum.HEAD_AUDIT_REJECT.code, ProductStatusEnum.FIRST_AUDIT_REJECT.code));
            default -> this;
        };
    }

    /**
     * 转换混合查询关键字
     */
    public ProductInfoPageReqVO transformMixedQuery() {
        if (StringUtils.isBlank(this.mixedQuery)) {
            return this;
        }
        ProductMixedQueryTypeEnum productMixedQueryTypeEnum = fromCode(this.mixedQueryType);
        if (productMixedQueryTypeEnum == null) {
            return this;
        }
        return switch (productMixedQueryTypeEnum) {
            case MULTI_BARCODE -> this.setBarcodeList(Arrays.stream(this.mixedQuery.split(",")).filter(StringUtils::isNotBlank).toList());
            case MULTI_SHOW_PREF -> this.setShowPrefList(Arrays.stream(this.mixedQuery.split(",")).filter(StringUtils::isNotBlank).toList());
            case MULTI_MID_STDLIB_ID -> this.setMidStdlibIdList(Arrays.stream(this.mixedQuery.split(",")).map(NumberUtils::toLong).filter(i -> i > 0).toList());
            case COMMON_NAME -> this.setCommonNameLike(this.mixedQuery);
            case BRAND_NAME -> this.setBrandNameLike(this.mixedQuery);
            default -> this;
        };
    }

    /**
     * productFlag 转换
     */
    public ProductInfoPageReqVO transformProductFlag() {
        // 转换禁采状态
        if (this.purchaseDisabled != null) {
            this.productFlag = Optional.ofNullable(this.getProductFlag()).orElseGet(ProductFlag::new).mappingPurchaseDisabled(this.purchaseDisabled);
        }

        ProductFlag queryPf = BeanUtil.copyProperties(this, ProductFlag.class);
        this.productFlag = ProductFlag.cover(this.productFlag, queryPf);
        return this;
    }
}