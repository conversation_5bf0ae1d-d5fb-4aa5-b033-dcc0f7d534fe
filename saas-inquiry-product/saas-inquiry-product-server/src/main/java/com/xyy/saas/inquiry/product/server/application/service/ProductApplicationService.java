package com.xyy.saas.inquiry.product.server.application.service;

import com.xyy.saas.inquiry.annotation.MethodArgumentTrim;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.server.application.command.ProductSaveCommand;
import com.xyy.saas.inquiry.product.server.application.context.ProductSaveContext;
import com.xyy.saas.inquiry.product.server.application.orchestrator.ProductSaveOrchestrator;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * 商品应用服务 - 主要协调器
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Service
public class ProductApplicationService {
    
    @Resource
    private ProductSaveOrchestrator productSaveOrchestrator;
    
    /**
     * 保存或更新商品 - 重构后的主要方法
     * 
     * @param dto 商品信息
     * @param bizType 业务类型
     * @return 商品ID
     */
    @Transactional(rollbackFor = Exception.class)
    public Long saveOrUpdateProduct(@MethodArgumentTrim @Nonnull ProductInfoDto dto, ProductBizTypeEnum bizType) {
        log.info("[ProductApplicationService] 开始处理商品保存, dto.id={}, bizType={}", dto.getId(), bizType);
        
        try {
            // 1. 构建保存命令
            ProductSaveCommand command = ProductSaveCommand.builder()
                .productDto(dto)
                .bizType(bizType)
                .operatorId(getCurrentOperatorId()) // 从上下文获取操作人
                .operationSource("API")
                .build();
            
            // 2. 验证命令
            command.validate();
            
            // 3. 执行保存流程
            Long productId = productSaveOrchestrator.execute(command);
            
            log.info("[ProductApplicationService] 商品保存完成, productId={}", productId);
            return productId;
            
        } catch (Exception e) {
            log.error("[ProductApplicationService] 商品保存失败, dto.id={}, bizType={}", 
                dto.getId(), bizType, e);
            throw e;
        }
    }
    
    /**
     * 异步保存商品
     */
    public void saveOrUpdateProductAsync(@Nonnull ProductInfoDto dto, ProductBizTypeEnum bizType) {
        log.info("[ProductApplicationService] 开始异步处理商品保存, dto.id={}, bizType={}", dto.getId(), bizType);
        
        // 构建异步处理命令
        ProductSaveCommand command = ProductSaveCommand.builder()
            .productDto(dto)
            .bizType(bizType)
            .operatorId(getCurrentOperatorId())
            .operationSource("ASYNC")
            .asyncProcessing(true)
            .build();
        
        // 提交到异步处理队列
        // TODO: 实现异步处理逻辑
        log.info("[ProductApplicationService] 商品异步保存任务已提交");
    }
    
    /**
     * 批量保存商品
     */
    @Transactional(rollbackFor = Exception.class)
    public java.util.List<Long> batchSaveProducts(java.util.List<ProductInfoDto> dtoList, 
                                                  ProductBizTypeEnum bizType) {
        log.info("[ProductApplicationService] 开始批量保存商品, count={}, bizType={}", 
            dtoList.size(), bizType);
        
        return dtoList.stream()
            .map(dto -> saveOrUpdateProduct(dto, bizType))
            .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 获取当前操作人ID
     */
    private String getCurrentOperatorId() {
        // 从安全上下文或请求上下文获取当前用户ID
        // 这里简化处理，实际应该从WebFrameworkUtils.getLoginUserId()获取
        try {
            Long userId = cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.getLoginUserId();
            return userId != null ? userId.toString() : "SYSTEM";
        } catch (Exception e) {
            log.warn("[ProductApplicationService] 获取当前用户ID失败", e);
            return "SYSTEM";
        }
    }
}