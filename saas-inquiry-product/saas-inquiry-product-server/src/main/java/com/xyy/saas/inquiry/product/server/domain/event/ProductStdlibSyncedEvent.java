package com.xyy.saas.inquiry.product.server.domain.event;

import com.xyy.saas.inquiry.product.server.domain.valueobject.TenantId;
import lombok.Getter;

import java.util.List;

/**
 * 商品标准库同步事件
 * 
 * <AUTHOR> Assistant
 */
@Getter
public class ProductStdlibSyncedEvent extends ProductDomainEvent {
    
    /**
     * 中台标准库ID
     */
    private final Long midStdlibId;
    
    /**
     * 标准库ID
     */
    private final Long stdlibId;
    
    /**
     * 同步类型：create, update, image_update
     */
    private final String syncType;
    
    /**
     * 同步的字段列表
     */
    private final List<String> syncedFields;
    
    /**
     * 是否同步了图片
     */
    private final Boolean imageSynced;
    
    public ProductStdlibSyncedEvent(String productPref, TenantId tenantId,
                                   Long midStdlibId, Long stdlibId,
                                   String syncType, List<String> syncedFields,
                                   Boolean imageSynced) {
        super(productPref, tenantId);
        this.midStdlibId = midStdlibId;
        this.stdlibId = stdlibId;
        this.syncType = syncType;
        this.syncedFields = syncedFields != null ? List.copyOf(syncedFields) : List.of();
        this.imageSynced = imageSynced;
    }
    
    @Override
    public String getEventType() {
        return "ProductStdlibSynced";
    }
    
    /**
     * 检查是否为创建同步
     */
    public boolean isCreateSync() {
        return "create".equals(syncType);
    }
    
    /**
     * 检查是否为更新同步
     */
    public boolean isUpdateSync() {
        return "update".equals(syncType);
    }
    
    /**
     * 检查是否为图片同步
     */
    public boolean isImageSync() {
        return "image_update".equals(syncType) || Boolean.TRUE.equals(imageSynced);
    }
    
    /**
     * 检查是否同步了指定字段
     */
    public boolean isSyncedField(String fieldName) {
        return syncedFields.contains(fieldName);
    }
}