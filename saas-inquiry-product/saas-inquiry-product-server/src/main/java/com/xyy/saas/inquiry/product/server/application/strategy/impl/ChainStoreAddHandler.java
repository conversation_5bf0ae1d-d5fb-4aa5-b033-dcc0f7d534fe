package com.xyy.saas.inquiry.product.server.application.strategy.impl;

import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.enums.ProductStatusEnum;
import com.xyy.saas.inquiry.product.server.application.strategy.ProductBizTypeHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 门店建品处理器
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class ChainStoreAddHandler implements ProductBizTypeHandler {
    
    @Override
    public ProductBizTypeEnum getSupportedBizType() {
        return ProductBizTypeEnum.CHAIN_STORE_ADD;
    }
    
    @Override
    public void handleProductStatusAndFlag(ProductInfoDto dto) {
        log.info("[ChainStoreAddHandler] 处理门店建品逻辑");
        
        // 设置为总部审核状态
        dto.setStatus(ProductStatusEnum.HEAD_AUDITING.code);
        
        log.debug("[ChainStoreAddHandler] 商品状态设置为总部审核中: {}", ProductStatusEnum.HEAD_AUDITING.code);
    }
}