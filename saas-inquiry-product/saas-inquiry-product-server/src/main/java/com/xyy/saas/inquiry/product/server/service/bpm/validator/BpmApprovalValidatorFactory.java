package com.xyy.saas.inquiry.product.server.service.bpm.validator;

import com.xyy.saas.inquiry.product.enums.BpmBusinessTypeEnum;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 审批验证器工厂
 */
@Component
@Slf4j
public class BpmApprovalValidatorFactory {
    
    @Resource
    private List<BpmApprovalValidator> validators;
    
    private final Map<String, Map<Integer, BpmApprovalValidator>> validatorMap = new ConcurrentHashMap<>();
    
    @PostConstruct
    public void init() {
        validators.forEach(validator -> {
            BpmBusinessTypeEnum[] supportBusinessTypes = validator.supportBusinessTypes();
            if (supportBusinessTypes == null || supportBusinessTypes.length == 0) {
                log.warn("[BpmValidator][{}的supportBusinessTypes不能为空]", validator.getClass().getSimpleName());
                return;
            }
            
            String type = validator.getType();
            for (BpmBusinessTypeEnum businessType : supportBusinessTypes) {
                validatorMap.computeIfAbsent(type, k -> new ConcurrentHashMap<>())
                    .put(businessType.code, validator);
            }
        });
        
        log.info("[init][初始化BpmApprovalValidatorFactory成功，共注册{}个验证器]", validators.size());
    }
    
    /**
     * 获取验证器
     * @param type 验证器类型
     * @param businessType 业务类型
     * @return 验证器
     */
    public BpmApprovalValidator getValidator(String type, Integer businessType) {
        Map<Integer, BpmApprovalValidator> typeValidators = validatorMap.get(type);
        if (typeValidators == null) {
            return null;
        }
        return typeValidators.get(businessType);
    }
} 