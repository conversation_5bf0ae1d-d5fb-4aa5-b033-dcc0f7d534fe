package com.xyy.saas.inquiry.product.server.api.catalog;

import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.system.api.tenant.TenantServicePackRelationApi;
import com.xyy.saas.inquiry.enums.transmitter.OrganTypeEnum;
import com.xyy.saas.inquiry.product.api.catalog.MedicalCatalogDetailApi;
import com.xyy.saas.inquiry.product.api.catalog.dto.MedicalCatalogDetailDTO;
import com.xyy.saas.inquiry.product.server.convert.catalog.MedicalCatalogDetailConvert;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.MedicalCatalogDetailDO;
import com.xyy.saas.inquiry.product.server.service.catalog.MedicalCatalogDetailService;
import jakarta.annotation.Resource;
import java.util.List;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * 目录详情
 */
@DubboService
public class MedicalCatalogDetailApiImpl implements MedicalCatalogDetailApi {

    @Resource
    private MedicalCatalogDetailService medicalCatalogDetailService;

    @DubboReference
    private TenantServicePackRelationApi tenantServicePackRelationApi;

    @Override
    public List<MedicalCatalogDetailDTO> getCatalogDetailByProjectCodes(Long tenantId, List<String> projectCodes) {

        Long catalogId = TenantUtils.execute(tenantId, this::getMedicalInsuranceCatalogId);

        List<MedicalCatalogDetailDO> regulatoryCatalogDetailDOS = medicalCatalogDetailService.getCatalogDetailListByProjectCodes(catalogId, projectCodes);

        return MedicalCatalogDetailConvert.INSTANCE.convertDo2DtoList(regulatoryCatalogDetailDOS);
    }

    /**
     * 查询当前TenantId门店 互联网监管目录id 用于搜索过滤
     *
     * @return
     */
    private Long getMedicalInsuranceCatalogId() {
        return tenantServicePackRelationApi.getTenantOrganCatalogId(OrganTypeEnum.MEDICAL_INSURANCE);
    }

}
