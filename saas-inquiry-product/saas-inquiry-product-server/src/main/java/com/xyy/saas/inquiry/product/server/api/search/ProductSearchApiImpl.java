package com.xyy.saas.inquiry.product.server.api.search;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDetailDto;
import com.xyy.saas.inquiry.product.api.search.ProductSearchApi;
import com.xyy.saas.inquiry.product.api.search.dto.InquiryDiagnosticsSearchReqDto;
import com.xyy.saas.inquiry.product.api.search.dto.InquiryDiagnosticsSearchRespDto;
import com.xyy.saas.inquiry.product.api.search.dto.InquiryProductSearchReqDto;
import com.xyy.saas.inquiry.product.server.service.search.InquiryProductSearchService;
import jakarta.annotation.Resource;
import java.util.List;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * @Author:chenxiaoyi
 * @Date:2024/12/20 15:49
 */
@DubboService
public class ProductSearchApiImpl implements ProductSearchApi {

    @Resource
    private InquiryProductSearchService inquiryProductSearchService;

    @Override
    public CommonResult<List<InquiryProductDetailDto>> queryProductStandardListByStandardIds(List<String> standardIds) {
        return inquiryProductSearchService.queryProductStandardListByStandardIds(standardIds);
    }

    @Override
    public CommonResult<List<InquiryDiagnosticsSearchRespDto>> productDiagnostics(InquiryDiagnosticsSearchReqDto reqDto) {
        return inquiryProductSearchService.productDiagnostics(reqDto);
    }

    @Override
    public CommonResult<List<InquiryProductDetailDto>> getProductStandardListByBarcodeList(List<String> barCodeList, Integer medicineType) {
        return inquiryProductSearchService.getProductStandardListByBarcodeList(barCodeList, medicineType);
    }

    @Override
    public CommonResult<List<InquiryProductDetailDto>> getProductStandardListByApprovalNoList(List<String> approvalNoList, Integer medicineType) {
        return inquiryProductSearchService.getProductStandardListByApprovalNoList(approvalNoList, medicineType);
    }

    @Override
    public CommonResult<List<InquiryProductDetailDto>> getProductStandardByProductNamesAndType(List<String> commonNameList, Integer medicineType) {
        return inquiryProductSearchService.getProductStandardByProductNamesAndType(commonNameList, medicineType);
    }
}