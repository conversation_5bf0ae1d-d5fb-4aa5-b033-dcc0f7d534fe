package com.xyy.saas.inquiry.product.server.controller.app.search.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2024/11/25 20:41
 */
@Schema(description = "App - 问诊商品新增 Request VO")
@Data
@ToString(callSuper = true)
public class InquiryProductAddReqVO {

    /**
     * 冗余转发服务 机构号参数 必填给默认
     */
    private String organSign = "1";

    @Schema(description = "商品编码")
    private String pref;

    @Schema(description = "商品类型 0西 1中")
    private Integer medicineType;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "商品通用名称")
    private String commonName;

    @Schema(description = "条形码")
    private String barcode;

    @Schema(description = "使用频次")
    private String useFrequency;

    @Schema(description = "规格")
    private String attributeSpecification;

    @Schema(description = "单次剂量")
    private String singleDose;

    @Schema(description = "单次剂量单位")
    private String singleUnit;

    @Schema(description = "用药方法")
    private String directions;

    @Schema(description = "批准文号")
    private String approvalNumber;

    @Schema(description = "单位名称")
    private String unitName;


}
