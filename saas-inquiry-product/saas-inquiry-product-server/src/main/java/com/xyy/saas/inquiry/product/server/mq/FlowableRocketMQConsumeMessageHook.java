package com.xyy.saas.inquiry.product.server.mq;


import static cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils.REQUEST_ATTRIBUTE_LOGIN_USER_ID;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.module.bpm.framework.flowable.core.util.FlowableUtils;
import java.util.List;
import org.apache.rocketmq.client.hook.ConsumeMessageContext;
import org.apache.rocketmq.client.hook.ConsumeMessageHook;
import org.apache.rocketmq.common.message.MessageExt;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * desc
 * 消息补充审批流相关上下文信息
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public class FlowableRocketMQConsumeMessageHook implements ConsumeMessageHook {

    private static final Logger log = LoggerFactory.getLogger(FlowableRocketMQConsumeMessageHook.class);

    @Override
    public String hookName() {
        return getClass().getSimpleName();
    }

    @Override
    public void consumeMessageBefore(ConsumeMessageContext context) {
        // 校验，消息必须是单条，不然设置租户可能不正确
        List<MessageExt> messages = context.getMsgList();
        Assert.isTrue(messages.size() == 1, "消息条数({})不正确", messages.size());
        // 设置用户信息
        String loginUserId = messages.getFirst().getUserProperty(REQUEST_ATTRIBUTE_LOGIN_USER_ID);
        if (StrUtil.isNotEmpty(loginUserId)) {
            FlowableUtils.setAuthenticatedUserId(Long.parseLong(loginUserId));
        }
    }

    @Override
    public void consumeMessageAfter(ConsumeMessageContext context) {
        FlowableUtils.clearAuthenticatedUserId();
    }

}
