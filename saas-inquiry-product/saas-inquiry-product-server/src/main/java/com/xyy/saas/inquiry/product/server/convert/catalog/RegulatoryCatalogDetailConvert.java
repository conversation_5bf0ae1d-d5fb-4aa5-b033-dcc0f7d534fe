package com.xyy.saas.inquiry.product.server.convert.catalog;

import com.xyy.saas.inquiry.product.api.catalog.dto.RegulatoryCatalogDetailDTO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.RegulatoryCatalogDetailExcelVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.RegulatoryCatalogDetailRespVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.RegulatoryCatalogDetailDO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface RegulatoryCatalogDetailConvert {

    RegulatoryCatalogDetailConvert INSTANCE = Mappers.getMapper(RegulatoryCatalogDetailConvert.class);

    @Mapping(source = "projectCode", target = "standardId")
    @Mapping(source = "commonName", target = "drugName")
    RegulatoryCatalogDetailExcelVO convertDOList2ExcelVOList(RegulatoryCatalogDetailDO regulatoryCatalogDetailDO);

    List<RegulatoryCatalogDetailExcelVO> convertDOList2ExcelVOList(List<RegulatoryCatalogDetailDO> regulatoryCatalogDetailDOList);

    List<RegulatoryCatalogDetailDTO> convertDto(List<RegulatoryCatalogDetailDO> regulatoryCatalogDetailDOS);


    /**
     * DO 转 RespVO
     */
    RegulatoryCatalogDetailRespVO convert(RegulatoryCatalogDetailDO bean);

    /**
     * DO列表 转 RespVO列表
     */
    List<RegulatoryCatalogDetailRespVO> convertList(List<RegulatoryCatalogDetailDO> list);
}
