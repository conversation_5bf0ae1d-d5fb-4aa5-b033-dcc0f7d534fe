package com.xyy.saas.inquiry.product.server.application.orchestrator;

import com.xyy.saas.inquiry.product.server.application.command.ProductSaveCommand;
import com.xyy.saas.inquiry.product.server.application.context.ProductSaveContext;
import com.xyy.saas.inquiry.product.server.application.step.ProductSaveStep;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 商品保存编排器 - 使用责任链模式协调各个处理步骤
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class ProductSaveOrchestrator {
    
    /**
     * 注入所有处理步骤（Spring会自动按@Order排序）
     */
    @Resource
    private List<ProductSaveStep> steps;
    
    /**
     * 执行商品保存流程
     * 
     * @param command 保存命令
     * @return 商品ID
     */
    public Long execute(ProductSaveCommand command) {
        log.info("[ProductSaveOrchestrator] 开始执行商品保存流程, isCreate={}", command.isCreateOperation());
        
        // 1. 创建上下文
        ProductSaveContext context = createContext(command);
        
        // 2. 执行各个步骤
        executeSteps(context);
        
        // 3. 检查执行结果
        if (context.isFailed()) {
            throw new RuntimeException("商品保存失败: " + context.getErrorMessage());
        }
        
        if (!context.isSuccess()) {
            throw new RuntimeException("商品保存未完成，状态: " + context.getStatus());
        }
        
        log.info("[ProductSaveOrchestrator] 商品保存流程完成, productId={}", context.getProductId());
        return context.getProductId();
    }
    
    /**
     * 创建处理上下文
     */
    private ProductSaveContext createContext(ProductSaveCommand command) {
        ProductSaveContext context = new ProductSaveContext(
            command.getProductDto(),
            command.getBizType(),
            command.getOperatorId()
        );
        
        // 设置扩展属性
        if (command.getExtParams() != null) {
            command.getExtParams().forEach(context::setAttribute);
        }
        
        return context;
    }
    
    /**
     * 执行所有处理步骤
     */
    private void executeSteps(ProductSaveContext context) {
        for (ProductSaveStep step : steps) {
            try {
                // 检查步骤是否适用
                if (!step.isApplicable(context)) {
                    log.debug("[ProductSaveOrchestrator] 跳过步骤: {}", step.getClass().getSimpleName());
                    continue;
                }
                
                log.info("[ProductSaveOrchestrator] 执行步骤: {}", step.getClass().getSimpleName());
                
                // 执行步骤
                step.execute(context);
                
                // 检查是否应该停止
                if (context.shouldStop()) {
                    log.info("[ProductSaveOrchestrator] 流程提前终止, 原因: {}", 
                        context.isFailed() ? context.getErrorMessage() : "业务逻辑要求停止");
                    break;
                }
                
            } catch (Exception e) {
                log.error("[ProductSaveOrchestrator] 步骤执行失败: {}", step.getClass().getSimpleName(), e);
                context.markFailure("步骤执行失败: " + step.getClass().getSimpleName() + ", " + e.getMessage());
                break;
            }
        }
    }
}