package com.xyy.saas.inquiry.product.server.controller.app.search;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.xyy.saas.inquiry.product.api.search.dto.InquiryDiagnosticsSearchReqDto;
import com.xyy.saas.inquiry.product.api.search.dto.InquiryDiagnosticsSearchRespDto;
import com.xyy.saas.inquiry.product.server.controller.app.search.vo.InquiryProductAddReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.search.vo.InquiryProductSearchReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.search.vo.InquiryProductSearchRespVO;
import com.xyy.saas.inquiry.product.server.controller.app.search.vo.InquiryProductSearchUsageAndDosageRuleReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.search.vo.InquiryProductUsageAndDosageRuleRespVO;
import com.xyy.saas.inquiry.product.server.service.search.InquiryProductSearchService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "APP+PC - 商品搜索")
@RestController
@RequestMapping(value = {"/admin-api/product/search", "/app-api/product/search"})
@Validated
public class AppInquiryProductSearchController {

    @Resource
    private InquiryProductSearchService inquiryProductSearchService;

    @GetMapping("/recommend-common-products")
    @Operation(summary = "常用商品推荐")
    public CommonResult<List<InquiryProductSearchRespVO>> recommendCommonProducts(@Valid InquiryProductSearchReqVO reqVO) {
        return inquiryProductSearchService.recommendCommonProducts(reqVO);
    }


    @GetMapping("/suggest-names-by-products-name")
    @Operation(summary = "搜索商品推荐名称")
    public CommonResult<List<String>> suggestNamesByProductsName(@Valid InquiryProductSearchReqVO reqVO) {
        return inquiryProductSearchService.suggestNamesByProductsName(reqVO);
    }

    @GetMapping("/products-by-name-spec")
    @Operation(summary = "搜索商品信息")
    public CommonResult<PageResult<InquiryProductSearchRespVO>> productsByNameSpec(@Valid InquiryProductSearchReqVO reqVO) {
        return inquiryProductSearchService.productsByNameSpec(reqVO);
    }

    @GetMapping("/product-by-barCode")
    @Operation(summary = "搜索商品根据69码")
    public CommonResult<List<InquiryProductSearchRespVO>> productByBarcode(@Valid InquiryProductSearchReqVO reqVO) {
        return inquiryProductSearchService.productByBarcode(reqVO);
    }

    @PostMapping("/product-diagnostics")
    @Operation(summary = "搜索商品关联诊断信息")
    public CommonResult<List<InquiryDiagnosticsSearchRespDto>> productDiagnostics(@RequestBody @Valid InquiryDiagnosticsSearchReqDto reqVO) {
        return inquiryProductSearchService.productDiagnostics(reqVO);
    }

    @PostMapping("/manual-add-product")
    @Operation(summary = "手动新增商品信息")
    public CommonResult<String> manualAddProduct(@Valid @RequestBody InquiryProductAddReqVO reqVO) {
        return inquiryProductSearchService.manualAddProduct(reqVO);
    }

    @PostMapping("/usage-and-dosage-rule")
    @Operation(summary = "查询商品用法用量")
    @ApiAccessLog(enable = false)
    public CommonResult<InquiryProductUsageAndDosageRuleRespVO> searchProductUsageAndDosageRule(@Valid @RequestBody InquiryProductSearchUsageAndDosageRuleReqVO reqVO) {
        reqVO.setTenantId(TenantContextHolder.getRequiredTenantId());
        return inquiryProductSearchService.searchProductUsageAndDosageRule(reqVO);
    }
}