package com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Schema(description = "管理后台 - 质量变更申请操作记录新增/修改 Request VO")
@Data
public class ProductQualityChangeRecordSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "17994")
    private Long id;

    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "17994")
    private Long tenantId;

    @Schema(description = "申请人", requiredMode = Schema.RequiredMode.REQUIRED, example = "17994")
    private String applicant;

    @Schema(description = "申请人", requiredMode = Schema.RequiredMode.REQUIRED, example = "17994")
    private String currentHandler;

    @Schema(description = "审批状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer approvalStatus;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @NotEmpty(message = "备注不能为空")
    private String remark;

    @Schema(description = "明细", requiredMode = Schema.RequiredMode.REQUIRED, example = "[]")
    @NotEmpty(message = "明细不能为空")
    private List<ProductQualityChangeDetailSaveReqVO> details;

}