package com.xyy.saas.inquiry.product.server.domain.event;

import com.xyy.saas.inquiry.product.enums.ProductStatusEnum;
import com.xyy.saas.inquiry.product.server.domain.valueobject.TenantId;
import lombok.Getter;

/**
 * 商品状态变更事件
 * 
 * <AUTHOR> Assistant
 */
@Getter
public class ProductStatusChangedEvent extends ProductDomainEvent {
    
    /**
     * 原状态
     */
    private final ProductStatusEnum oldStatus;
    
    /**
     * 新状态
     */
    private final ProductStatusEnum newStatus;
    
    /**
     * 变更原因
     */
    private final String changeReason;
    
    /**
     * 操作人ID
     */
    private final String operatorId;
    
    public ProductStatusChangedEvent(String productPref, TenantId tenantId,
                                   ProductStatusEnum oldStatus, ProductStatusEnum newStatus,
                                   String changeReason, String operatorId) {
        super(productPref, tenantId);
        this.oldStatus = oldStatus;
        this.newStatus = newStatus;
        this.changeReason = changeReason;
        this.operatorId = operatorId;
    }
    
    @Override
    public String getEventType() {
        return "ProductStatusChanged";
    }
    
    /**
     * 检查是否为审批通过
     */
    public boolean isApprovalPassed() {
        return isAuditStatus(oldStatus) && newStatus == ProductStatusEnum.USING;
    }
    
    /**
     * 检查是否为审批驳回
     */
    public boolean isApprovalRejected() {
        return isAuditStatus(oldStatus) && 
               (newStatus == ProductStatusEnum.FIRST_AUDIT_REJECT ||
                newStatus == ProductStatusEnum.HEAD_AUDIT_REJECT ||
                newStatus == ProductStatusEnum.MID_AUDIT_REJECT);
    }
    
    /**
     * 检查状态是否为审核状态
     */
    private boolean isAuditStatus(ProductStatusEnum status) {
        return status == ProductStatusEnum.FIRST_AUDITING ||
               status == ProductStatusEnum.HEAD_AUDITING ||
               status == ProductStatusEnum.MID_AUDITING;
    }
}