package com.xyy.saas.inquiry.product.server.config.forward.dto;

import lombok.Data;
import java.io.Serializable;
import java.util.Map;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class MidSaasWithMixSearchInfoDto implements Serializable {
    private static final long serialVersionUID = -5577409774336508503L;

    private long productid;
    private String productname;
    private String shangpinmingcheng;
    private String guige;
    private String danwei;
    private String shengchanchangjia;
    private String jixing;
    private String tiaoma;
    private String pizhunwenhao;
    private String chufangfenlei;
    private int shifouhanma;
    private String chandi;
    private String cunchutiaojian;
    private String chanpinleibiebianhao;
    private String chanpinleibiemingcheng;
    private String xinshangpinbianma;
    private String jiushangpinbianma;
    private int tingyongzhuangtai;
    private String categoryCode;
    private String packageUnitStr;
    private String prescriptionCategoryStr;
    private String entrustedManufacturerStr;
    private String storageCondStr;
    private String qualityStandard;
    private Short validity;
    private Byte validityUnit;

    private String treatSymptom = "";
    private Integer treatCourse = 0;
    private Integer usageDay = 0;
    private String usageDayUnit = "";
    private String usageDayDesc = "";
    private String ingredient = "";
    private String indication = "";
    private String usageDosage = "";
    private String adverseReaction = "";
    private String standardCodes = "";
    private String firstCategoryName = "";
    private int firstCategoryId;
    protected Integer firstCategory = 0;
    protected Integer secondCategory = 0;
    protected Integer thirdCategory = 0;
    protected Integer fourthCategory = 0;
    protected Integer fiveCategory = 0;
    protected Integer sixCategory = 0;
    protected String secondCategoryName = "";
    protected String thirdCategoryName = "";
    protected String fourthCategoryName = "";
    protected String fiveCategoryName = "";
    protected String sixCategoryName = "";
    protected String splitCategoryName = "";
    protected String storageConditions = "";
    protected String codeNumber = "";
    protected String color = "";
    protected String commissionedManufacturer = "";
    protected String prescriptionClassification = "";
    protected String periodValiditySau = "";
    protected String qualityStandardSau = "";
    protected String packingUnit = "";
    protected String taste = "";
    protected String packageSau = "";
    protected String salesChannel = "";
    protected String MergeProduct = "";
    protected String skuPrimaryType = "";
    protected String shadingAttrStr = "";
    protected Integer inRate = 0;
    protected Integer outRate = 0;
    protected String inRateStr = "";
    protected String outRateStr = "";
    protected String businessScopeName;
    protected String taxCategoryCode;
    protected Map<String, String> sauAttrMap;

    private String mixCondition;
    private String approvalNumber;
    private String manufacturer;
}
