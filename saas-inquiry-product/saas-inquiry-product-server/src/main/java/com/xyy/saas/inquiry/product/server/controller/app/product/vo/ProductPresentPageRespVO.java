package com.xyy.saas.inquiry.product.server.controller.app.product.vo;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xyy.saas.inquiry.product.enums.ProductTransferStatusEnum;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoDO2;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Stack;

@Schema(description = "APP后台 - 商品提报记录 Response VO")
@Data
public class ProductPresentPageRespVO {

    // 统计待审核、已通过、已驳回数量
    @Schema(description = "待审核数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long approvingCount = 0L;
    @Schema(description = "已驳回数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long passedCount = 0L;
    @Schema(description = "已驳回数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long rejectedCount = 0L;

    @Schema(description = "分页数据", example = "{}")
    private PageResult<ProductPresentRecordRespVO> page;
} 