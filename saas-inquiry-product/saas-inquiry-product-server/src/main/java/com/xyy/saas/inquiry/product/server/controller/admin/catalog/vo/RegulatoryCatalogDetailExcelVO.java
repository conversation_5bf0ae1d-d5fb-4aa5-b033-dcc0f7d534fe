package com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = false)
public class RegulatoryCatalogDetailExcelVO {

    @ExcelProperty(index = 0, value = "序号（L）")
    private String a;

    @ExcelProperty(index = 1, value = "医疗机构名称（L）")
    private String b;

    @ExcelProperty(index = 2, value = "药品分类（L）")
    private String c;

    @ExcelProperty(index = 3, value = "院内药品唯一码（L）")
    private String standardId;

    @ExcelProperty(index = 4, value = "药品名称（L）")
    private String drugName;

    @ExcelProperty(index = 5, value = "剂型名称（L）")
    private String f;

    @ExcelProperty(index = 6, value = "制剂规格（L）")
    private String spec;

    @ExcelProperty(index = 7, value = "生产企业（L）")
    private String manufacturer;

    @ExcelProperty(index = 8, value = "通用名（L）")
    private String i;

    @ExcelProperty(index = 9, value = "商品名（L）")
    private String j;

    @ExcelProperty(index = 10, value = "转换系数（L）")
    private String k;

    @ExcelProperty(index = 11, value = "制剂单位（L）")
    private String l;

    @ExcelProperty(index = 12, value = "最小销售包装单位（L）")
    private String m;

    @ExcelProperty(index = 13, value = "批准文号（L）")
    private String n;

    @ExcelProperty(index = 14, value = "本位码（L）")
    private String o;

    @ExcelProperty(index = 15, value = "备注（L）")
    private String p;

    @ExcelProperty(index = 16, value = "比对类型")
    private String q;

    @ExcelProperty(index = 17, value = "匹配类型")
    private String r;

    @ExcelProperty(index = 18, value = "匹配度")
    private String s;

    @ExcelProperty(index = 19, value = "药品分类")
    private String t;

    @ExcelProperty(index = 20, value = "药品编码")
    private String u;

    @ExcelProperty(index = 21, value = "药品名称")
    private String v;

    @ExcelProperty(index = 22, value = "剂型名称")
    private String w;

    @ExcelProperty(index = 23, value = "制剂规格")
    private String x;

    @ExcelProperty(index = 24, value = "生产企业")
    private String y;

    @ExcelProperty(index = 25, value = "通用名")
    private String z;

    @ExcelProperty(index = 26, value = "商品名")
    private String aa;

    @ExcelProperty(index = 27, value = "转换系数")
    private String ab;

    @ExcelProperty(index = 28, value = "制剂单位")
    private String ac;

    @ExcelProperty(index = 29, value = "最小销售包装单位")
    private String ad;

    @ExcelProperty(index = 30, value = "批准文号")
    private String ae;

    @ExcelProperty(index = 31, value = "本位码")
    private String af;

    @ExcelProperty(index = 32, value = "错误信息")
    private String errorMsg;

}
