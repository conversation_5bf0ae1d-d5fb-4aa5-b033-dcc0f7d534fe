package com.xyy.saas.inquiry.product.server.config.forward.dto;


import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class MidTotalDictionaryReadDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    private Integer id;
    private String type;
    /** 名称 */
    private String dictName;
    /** 字典id */
    private String dictId;
    private Byte level;
    private Byte levelNode;
    private Integer parentId;
    private String parentDictName;
    /** 是否和规格合并: 不合并_0, 合并_1 */
    private Byte isMerge;
    /** 是否显示: 不显示_0, 显示_1 */
    private Byte isShow;
    /** 显示内容 */
    private String showContent;
    private String simpleCode;
    /** 助记码 */
    private String mnemonicCode;
    /** 关键词 */
    private String keywords;
    private Integer sectionOfficeId;
    /** 所属科室 */
    private String sectionOfficeName;
    private String diseaseName;
    /** 是否停用: 停用_0, 启用_1 */
    private Byte isValid;
    /** 备注 */
    private String remark;
    private String manufacturerCategoryName;
    private List<MidTotalDictionaryReadDto> totalDictionaryReadDtos;
    private Integer externalId;


}
