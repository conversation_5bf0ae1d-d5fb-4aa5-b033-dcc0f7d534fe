package com.xyy.saas.inquiry.product.server.controller.admin.catalog;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PARAM_INVALID;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.RegulatoryCatalogDetailExcelVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.RegulatoryCatalogDetailPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.RegulatoryCatalogDetailRespVO;
import com.xyy.saas.inquiry.product.server.convert.catalog.RegulatoryCatalogDetailConvert;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.RegulatoryCatalogDetailDO;
import com.xyy.saas.inquiry.product.server.service.catalog.RegulatoryCatalogDetailService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 监管目录药品信息")
@RestController
@RequestMapping("/product/regulatory-catalog-detail")
@Validated
public class RegulatoryCatalogDetailController {

    @Resource
    private RegulatoryCatalogDetailService regulatoryCatalogDetailService;

    @GetMapping("/regulatory-catalog-detail/list-by-catalog-id")
    @Operation(summary = "获得监管目录明细列表")
    @Parameter(name = "catalogId", description = "目录ID")
    @PreAuthorize("@ss.hasPermission('saas:product:catalog:query')")
    public CommonResult<List<RegulatoryCatalogDetailDO>> getRegulatoryCatalogDetailListByCatalogId(@RequestParam("catalogId") Long catalogId) {
        return success(regulatoryCatalogDetailService.getRegulatoryCatalogDetailListByCatalogId(catalogId));
    }

    @GetMapping("/page-regulatory-catalog-detail")
    @Operation(summary = "监管目录明细分页")
    @PreAuthorize("@ss.hasPermission('saas:product:catalog:query')")
    public CommonResult<PageResult<RegulatoryCatalogDetailRespVO>> getRegulatoryCatalogDetailPage(@Valid @ParameterObject RegulatoryCatalogDetailPageReqVO pageReqVO) {
        PageResult<RegulatoryCatalogDetailDO> pageResult = regulatoryCatalogDetailService.getRegulatoryCatalogDetailPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, RegulatoryCatalogDetailRespVO.class));
    }

    @PostMapping("/update-status")
    @Operation(summary = "修改状态")
    @PreAuthorize("@ss.hasPermission('saas:product:catalog:query')")
    public CommonResult<Boolean> updateStatus(@RequestBody RegulatoryCatalogDetailPageReqVO pageReqVO) {
        return success(regulatoryCatalogDetailService.updateStatus(pageReqVO));
    }

    @PostMapping("/importExcel")
    @Operation(summary = "导入")
    @PreAuthorize("@ss.hasPermission('saas:product:catalog:query')")
    public CommonResult<CatalogRespVO> importExcel(@RequestBody CatalogSaveReqVO pageReqVO) {
        return success(regulatoryCatalogDetailService.importExcel(pageReqVO));
    }

    @GetMapping("/get-import-template")
    @Operation(summary = "获得目录明细导入模板")
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 输出
        ExcelUtils.write(response, "目录明细.xls", "数据", RegulatoryCatalogDetailExcelVO.class, Lists.newArrayList(RegulatoryCatalogDetailExcelVO.builder().build()));
    }

    @GetMapping("/export-all-data-excel")
    @Operation(summary = "导出目录 Excel")
    @PreAuthorize("@ss.hasPermission('saas:product:catalog:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportCatalogExcel(@Valid RegulatoryCatalogDetailPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        if (pageReqVO == null || pageReqVO.getCatalogId() == null) {
            throw exception(PARAM_INVALID, "catalogId", "不能为空");
        }
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        pageReqVO.setDisable(false);
        PageResult<RegulatoryCatalogDetailDO> pageResult = regulatoryCatalogDetailService.getRegulatoryCatalogDetailPage(pageReqVO);
        // 导出 Excel
        ExcelUtils.write(response, "目录明细.xls", "数据", RegulatoryCatalogDetailExcelVO.class, RegulatoryCatalogDetailConvert.INSTANCE.convertDOList2ExcelVOList(pageResult.getList()));
    }

}