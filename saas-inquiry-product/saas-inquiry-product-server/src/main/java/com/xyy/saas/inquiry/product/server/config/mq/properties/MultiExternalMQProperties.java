package com.xyy.saas.inquiry.product.server.config.mq.properties;

import lombok.Data;
import org.apache.rocketmq.spring.autoconfigure.RocketMQProperties;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

@Data
@Configuration
@ConfigurationProperties(prefix = "external.rocketmq")
public class MultiExternalMQProperties {

    private RocketMQProperties saas;

    private RocketMQProperties middle;
} 