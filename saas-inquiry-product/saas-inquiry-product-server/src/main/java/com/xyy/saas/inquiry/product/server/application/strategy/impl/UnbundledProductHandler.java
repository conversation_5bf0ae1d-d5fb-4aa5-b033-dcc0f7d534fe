package com.xyy.saas.inquiry.product.server.application.strategy.impl;

import com.xyy.saas.inquiry.product.api.product.dto.ProductFlag;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.server.application.strategy.ProductBizTypeHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 商品拆零处理器
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class UnbundledProductHandler implements ProductBizTypeHandler {
    
    @Override
    public ProductBizTypeEnum getSupportedBizType() {
        return ProductBizTypeEnum.UNBUNDLED_PRODUCT;
    }
    
    @Override
    public void handleProductStatusAndFlag(ProductInfoDto dto) {
        log.info("[UnbundledProductHandler] 处理商品拆零逻辑");
        
        // 设置拆零标识
        ProductFlag productFlag = Optional.ofNullable(dto.getProductFlag())
            .orElseGet(ProductFlag::new);
        productFlag.setUnbundled(true);
        dto.setProductFlag(productFlag);
        
        log.debug("[UnbundledProductHandler] 设置拆零标识完成");
    }
    
    @Override
    public boolean shouldProcessStdlib() {
        // 拆零商品不处理标准库
        return false;
    }
}