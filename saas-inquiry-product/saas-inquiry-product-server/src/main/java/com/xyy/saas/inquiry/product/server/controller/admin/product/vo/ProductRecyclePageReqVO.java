package com.xyy.saas.inquiry.product.server.controller.admin.product.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;

@Schema(description = "管理后台 - 商品回收站分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductRecyclePageReqVO extends PageParam {

    @Schema(description = "租户编码")
    private Long tenantId;

    @Schema(description = "商品信息", example = "芬必得")
    private String mixedQuery;

    @Schema(description = "删除类型", example = "1")
    private Integer deleteType;

    @Schema(description = "状态", example = "1")
    private Integer status;

}