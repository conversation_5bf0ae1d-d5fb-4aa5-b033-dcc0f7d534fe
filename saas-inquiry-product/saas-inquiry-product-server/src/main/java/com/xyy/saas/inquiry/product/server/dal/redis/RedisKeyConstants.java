package com.xyy.saas.inquiry.product.server.dal.redis;

/**
 * System Redis Key 枚举类
 *
 * <AUTHOR>
 */
public interface RedisKeyConstants {

    /**
     * 商品六级分类树结构缓存
     * <p>
     */
    String PRODUCT_CATEGORY_TREE_KEY = "product:category_tree";

    /**
     * 商品分类热点数据缓存（1-3级分类）
     * <p>
     * KEY 格式：product:category:tree:hot VALUE 数据格式：List<ProductCategoryRespVO> 热点分类树
     */
    String PRODUCT_CATEGORY_TREE_HOT = "product:category:tree:hot";

    /**
     * 商品分类子分类列表缓存
     * <p>
     * KEY 格式：product:category:children:{parentId} VALUE 数据格式：List<ProductCategoryRespVO> 子分类列表
     */
    String PRODUCT_CATEGORY_CHILDREN = "product:category:children:";

    /**
     * 商品分类详情缓存
     * <p>
     * KEY 格式：product:category:detail:{id} VALUE 数据格式：ProductCategoryRespVO 分类详情
     */
    String PRODUCT_CATEGORY_DETAIL = "product:category:detail:";

    /**
     * 商品分类按层级缓存
     * <p>
     * KEY 格式：product:category:level:{level} VALUE 数据格式：List<ProductCategoryRespVO> 指定层级分类列表
     */
    String PRODUCT_CATEGORY_LEVEL_PREFIX = "product:category:level:";

    /**
     * 商品标准库同步锁
     * <p>
     * KEY 格式：product:stdlib_sync:lock:{guid} VALUE 数据格式：String
     */
    String PRODUCT_STDLIB_SYNC_LOCK_KEY = "product:stdlib_sync:lock:";
}
