package com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 售价调整单明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductPriceAdjustmentDetailPageReqVO extends PageParam {

    @Schema(description = "商品信息", example = "32264")
    private String mixedQuery;

    @Schema(description = "租户ID", example = "32264")
    private Long tenantId;

    @Schema(description = "单据编号", example = "16932")
    private String recordPref;

    @Schema(description = "适用门店", example = "2585")
    private Long applicableTenantId;

    @Schema(description = "适用门店列表", example = "2585")
    private List<Long> applicableTenantIdList;

    @Schema(description = "商品编码", example = "22067")
    private String productPref;

    @Schema(description = "商品编码列表", example = "22067")
    private List<String> productPrefList;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}