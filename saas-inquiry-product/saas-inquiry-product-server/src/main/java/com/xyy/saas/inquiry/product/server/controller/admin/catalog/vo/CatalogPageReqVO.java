package com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 目录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class CatalogPageReqVO extends PageParam {

    private Long id;

    @Schema(description = "目录编码")
    private String pref;

    @Schema(description = "目录名称", example = "王五")
    private String name;

    @Schema(description = "业务类型（1:医保 3:互联网监管）", example = "1")
    private Integer type;

    @Schema(description = "项目编码类型（11:医保项目编码 31:自建标准库ID 32:中台标准库ID）", example = "2")
    private Integer projectCodeType;

    @Schema(description = "版本号")
    private Integer version;

    @Schema(description = "版本编码")
    private String versionCode;

    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED)
    private String province;

    @Schema(description = "省编码")
    private String provinceCode;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED)
    private String city;

    @Schema(description = "市编码")
    private String cityCode;

    @Schema(description = "区", requiredMode = Schema.RequiredMode.REQUIRED)
    private String area;

    @Schema(description = "区编码")
    private String areaCode;

    @Schema(description = "上传文件链接", example = "https://www.iocoder.cn")
    private String uploadUrl;

    @Schema(description = "是否需要下载")
    private Boolean needDownload;

    @Schema(description = "下载文件链接", example = "https://www.iocoder.cn")
    private String downloadUrl;

    @Schema(description = "目录总数", example = "15495")
    private Integer totalCount;

    @Schema(description = "已匹配数", example = "14781")
    private Integer matchedCount;

    @Schema(description = "未匹配数", example = "30548")
    private Integer unmatchedCount;

    @Schema(description = "是否禁用，默认否")
    private Boolean disable;

    @Schema(description = "环境（1测试、2灰度、3上线）")
    private Integer env;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    @Schema(description = "租户名字或者pref")
    private String tenantNameOrPref;

    @Schema(description = "租户联系人手机号")
    private String tenantContactMobile;

    @Schema(description = "目录版本id集合")
    private List<Long> catalogIdList;

    @Schema(description = "创建人")
    private String creator;

    @Schema(description = "创建人集合")
    private List<String> creatorList;

}