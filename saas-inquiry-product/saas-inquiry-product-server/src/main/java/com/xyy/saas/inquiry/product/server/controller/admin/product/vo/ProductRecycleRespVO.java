package com.xyy.saas.inquiry.product.server.controller.admin.product.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

@Schema(description = "管理后台 - 商品回收站 Response VO")
@Data
public class ProductRecycleRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "通用名称", example = "芬必得")
    private String commonName;

    @Schema(description = "品牌名称", example = "芬必得")
    private String brandName;

    @Schema(description = "规格", example = "10mg*10片")
    private String spec;

    @Schema(description = "生产厂家", example = "云南白药集团")
    private String manufacturer;

    @Schema(description = "批准文号", example = "国药准字H12345678")
    private String approvalNumber;

    @Schema(description = "条形码", example = "6901234567890")
    private String barcode;

    @Schema(description = "商品分类", example = "1")
    private String category;

    @Schema(description = "删除类型", example = "1")
    private Integer deleteType;

    @Schema(description = "状态", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "创建人ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String creator;
    @Schema(description = "创建人名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String creatorName;
    @Schema(description = "更新人ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String updater;
    @Schema(description = "更新人名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String updaterName;

}