package com.xyy.saas.inquiry.product.server.mq.producer.mid;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.server.mq.message.mid.MidStdlibInteractiveEvent;
import com.xyy.saas.inquiry.product.server.mq.message.mid.dto.MidStdlibInteractiveMessage;
import com.xyy.saas.inquiry.product.server.mq.message.mid.dto.MidStdlibInteractiveMessage.Type;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author: xucao
 * @Date: 2024/12/26 10:03
 * @Description: 中台标准库数据交互事件（新品提报，匹配）
 */
@Component
@EventBusProducer(
    topic = MidStdlibInteractiveEvent.TOPIC
)
public class MidStdlibInteractiveProducer extends EventBusRocketMQTemplate {


    /**
     * 发送新品提报消息
     */
    public void sendDelayMessage4Match(List<String> prefList, int delaySeconds) {
        sendDelayMessage(prefList, Type.MATCH.code, delaySeconds);
    }


    /**
     * 发送新品提报消息
     */
    public void sendDelayMessage4Report(List<String> prefList, int delaySeconds) {
        sendDelayMessage(prefList, Type.REPORT.code, delaySeconds);
    }

    /**
     * 发送消息
     */
    private void sendDelayMessage(List<String> prefList, int type, int delaySeconds) {
        if (CollectionUtils.isEmpty(prefList)) {
            return;
        }
        MidStdlibInteractiveMessage msg = MidStdlibInteractiveMessage.builder()
            .type(type)
            .productPrefList(prefList)
            .build();

        this.sendMessage(
            MidStdlibInteractiveEvent.builder().msg(msg).build(),
            LocalDateTime.now().plusSeconds(delaySeconds)
        );
    }
}
