package com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 商品六级分类 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProductCategoryRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "分类名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("分类名称")
    private String name;

    @Schema(description = "字典id（中台）", requiredMode = Schema.RequiredMode.REQUIRED, example = "20775")
    @ExcelProperty("字典id（中台）")
    private Long dictId;

    @Schema(description = "字典父id（中台）", requiredMode = Schema.RequiredMode.REQUIRED, example = "6099")
    @ExcelProperty("字典父id（中台）")
    private Long parentDictId;

    @Schema(description = "排序权重", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("排序权重")
    private Integer sortOrder;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("更新时间")
    private LocalDateTime updateTime;

    @Schema(description = "子分类列表")
    private List<ProductCategoryRespVO> children;

    /**
     * 构建树结构
     * @param nodeList
     * @return
     */
    public static List<ProductCategoryRespVO> buildTree(List<ProductCategoryRespVO> nodeList) {
        // Initialize a list to hold root nodes
        List<ProductCategoryRespVO> roots = new ArrayList<>();

        // Iterate through the nodeList to find root nodes (nodes with no parent or parent ID of 0)
        for (ProductCategoryRespVO node : nodeList) {
            if (node.getParentDictId() == null || node.getParentDictId() == 0L) {
                roots.add(node);
            }
        }

        // Sort the root nodes by update time in descending order
        roots.sort((a, b) -> b.getUpdateTime().compareTo(a.getUpdateTime()));

        // Initialize a queue for processing nodes in a breadth-first manner, starting with the root nodes
        Queue<ProductCategoryRespVO> processQueue = new LinkedList<>(roots);

        // Initialize a set to keep track of processed node IDs to avoid duplicates
        Set<Long> processedIds = new HashSet<>();

        // Initialize a list to hold the final tree structure
        List<ProductCategoryRespVO> resultTree = new ArrayList<>();

        // Process nodes in the queue until it is empty
        while (!processQueue.isEmpty()) {
            // Dequeue the next node to process
            ProductCategoryRespVO currentNode = processQueue.poll();

            // Skip processing if the node has already been processed
            if (processedIds.contains(currentNode.getDictId())) {
                continue;
            }

            // Add the current node to the result tree if it is a root node
            if (roots.contains(currentNode)) {
                resultTree.add(currentNode);
            }

            // Find and process child nodes of the current node
            nodeList.stream()
                .filter(n -> Objects.equals(n.getParentDictId(), currentNode.getDictId()))
                .sorted((a, b) -> b.getUpdateTime().compareTo(a.getUpdateTime())) // Sort child nodes by update time in descending order
                .forEachOrdered(child -> {
                    // Add the child node to the current node's children list
                    if (currentNode.getChildren() == null) {
                        currentNode.setChildren(new ArrayList<>());
                    }
                    currentNode.getChildren().add(child);
                    // Enqueue the child node for further processing
                    processQueue.offer(child);
                });

            // Mark the current node as processed
            processedIds.add(currentNode.getDictId());
        }

        // Return the final tree structure
        return resultTree;
    }
}