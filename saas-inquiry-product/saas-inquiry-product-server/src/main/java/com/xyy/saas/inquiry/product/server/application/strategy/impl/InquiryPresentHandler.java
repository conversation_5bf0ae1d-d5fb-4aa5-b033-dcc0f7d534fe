package com.xyy.saas.inquiry.product.server.application.strategy.impl;

import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.enums.ProductStatusEnum;
import com.xyy.saas.inquiry.product.server.application.strategy.ProductBizTypeHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 问诊提报商品处理器
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class InquiryPresentHandler implements ProductBizTypeHandler {
    
    @Override
    public ProductBizTypeEnum getSupportedBizType() {
        return ProductBizTypeEnum.INQUIRY_PRESENT;
    }
    
    @Override
    public void handleProductStatusAndFlag(ProductInfoDto dto) {
        log.info("[InquiryPresentHandler] 处理问诊提报商品逻辑");
        
        // 设置为中台审核状态
        dto.setStatus(ProductStatusEnum.MID_AUDITING.code);
        
        log.debug("[InquiryPresentHandler] 商品状态设置为中台审核中: {}", ProductStatusEnum.MID_AUDITING.code);
    }
}