package com.xyy.saas.inquiry.product.server.mq.message.mid.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Title: MeProductMqMsgVo
 * @Description:
 * @Company: ybm100.com
 * @Created on 2021/1/28
 * @ModifiedBy:
 * @Copyright: Copyright (c) 2021
 */
@Data
public class MidProductMsg implements Serializable {
    @Serial
    private static final long serialVersionUID = -4164098149413646076L;
    private String businessCode;
    private Integer businessType;
    private String createInstitutionName;
    private String generalName;
    private Integer disableStatus;
    private String originalProductCode;
    private Integer preOperateStatus;
    private String productId;
    private Integer sendDirection;
    private Integer sendType;
    private String skuName;
    private Integer systemType;
    private String timeStamp;
    private Integer type;
}
