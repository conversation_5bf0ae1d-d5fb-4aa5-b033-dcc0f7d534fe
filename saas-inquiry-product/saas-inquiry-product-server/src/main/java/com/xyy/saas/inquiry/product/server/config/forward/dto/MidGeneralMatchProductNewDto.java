package com.xyy.saas.inquiry.product.server.config.forward.dto;

import lombok.Data;
import java.io.Serializable;
import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class MidGeneralMatchProductNewDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 入参编码
     */
    private String oldBusinessCode;

    /**
     * 入参商品分类id
     */
    private Integer oldSpuCategory;

    /**
     * 入参通用名
     */
    private String oldGeneralName;

    /**
     * 入参批准文号
     */
    private String oldApprovalNo;


    /**
     * 入参生产厂家名称
     */
    private String oldManufacturerName;


    /**
     * 入参规格/型号
     */
    private String oldSpec;

    /**
     * 入参小包装条码
     */
    private String oldSmallPackageCode;

    private String oldPackageUnitStr;

    /**
     * 商品编码
     */
    private String businessCode;

    /**
     * 原商品编码
     */
    private String originalProductCode;

    /**
     * 标准库id
     */
    private String productId;

    /**
     * 商品分类id
     */
    private Integer spuCategory;

    /**
     * 通用名
     */
    private String generalName;

    /**
     * 批准文号
     */
    private String approvalNo;


    /**
     * 生产厂家名称
     */
    private String manufacturerName;

    /**
     * 生产厂家id
     */
    private Integer manufacturerId;


    /**
     * 规格/型号
     */
    private String spec;

    /**
     * 小包装条码
     */
    private String smallPackageCode;

    /**
     * 参数校验结果类型0匹配成功1初始状态 2校验未用过3 匹配到多条  4未匹配到
     */
    private Integer checkCodeType = 1;
    /**
     * 参数校验结果类型0匹配成功 1初始状态 2校验未用过 3 匹配到多条  4未匹配到
     */
    private Integer checkSpecType = 1;

    /**
     * 两要素匹配状态 0匹配成功 1初始状态  2校验未用过  3 匹配到多条  4未匹配到
     */
    private Integer twoElementsType = 1;

    /**
     * 三要素匹配状态 0匹配成功 1初始状态  2校验未用过 3 匹配到多条 4未匹配到
     */
    private Integer threeElementsType = 1;

    /**
     * 正则规格匹配 0匹配成功 1初始状态  2校验未用过 3 匹配到多条 4未匹配到
     */
    private Integer regxType = 1;

    /**
     * 学习匹配 0匹配成功 1初始状态  2校验未用过 3 匹配到多条 4未匹配到
     */
    private Integer learnType = 1;

    /**
     * 最终状态  0匹配成功 1初始状态  2匹配失败
     */
    private Integer resultType = 1;

    /**
     * 匹配到的商品列表
     */
    private List<MidGeneralMatchProductNewDto> matchList;

    /**
     * 停用状态(0:停用, 1:启用) sku-sau
     */
    private Byte disableStatus;
}
