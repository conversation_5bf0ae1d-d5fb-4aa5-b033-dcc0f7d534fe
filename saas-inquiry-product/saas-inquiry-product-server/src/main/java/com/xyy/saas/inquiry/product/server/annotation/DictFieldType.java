package com.xyy.saas.inquiry.product.server.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 字典字段注解
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface DictFieldType {

    /**
     * 字典类型
     */
    String value();

    /**
     * 多值字典间隔：用逗号分隔
     */
    String[] multiValueGap() default "";
} 