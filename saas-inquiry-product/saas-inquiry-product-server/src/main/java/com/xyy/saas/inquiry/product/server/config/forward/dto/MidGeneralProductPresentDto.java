package com.xyy.saas.inquiry.product.server.config.forward.dto;

import lombok.Data;
import java.io.Serializable;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class MidGeneralProductPresentDto implements Serializable {

    private static final long serialVersionUID = -2166487826970258408L;

    /**
     * 标准库id
     */
    private String productId;

    /**
     * 通用名
     */
    private String generalName;

    /**
     * 规格/型号
     */
    private String spec;

    /**
     * 批准文号/注册证号
     */
    private String approvalNo;

    /**
     * 生产厂家
     * @convert
     */
    private Integer manufacturerId;
    /**
     * 生产厂家
     * @convert
     */
    private String manufacturerName;

    /**
     * 小包装条码
     */
    private String smallPackageCode;

}