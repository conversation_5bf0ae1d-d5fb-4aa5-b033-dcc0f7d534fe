package com.xyy.saas.inquiry.product.server.domain.event;

import com.xyy.saas.inquiry.product.server.domain.valueobject.TenantId;
import lombok.Getter;

import java.util.Map;

/**
 * 商品更新事件
 * 
 * <AUTHOR> Assistant
 */
@Getter
public class ProductUpdatedEvent extends ProductDomainEvent {
    
    /**
     * 更新的字段及其新值
     */
    private final Map<String, Object> updatedFields;
    
    /**
     * 更新的字段及其旧值
     */
    private final Map<String, Object> oldValues;
    
    /**
     * 操作人ID
     */
    private final String operatorId;
    
    public ProductUpdatedEvent(String productPref, TenantId tenantId,
                              Map<String, Object> updatedFields, Map<String, Object> oldValues,
                              String operatorId) {
        super(productPref, tenantId);
        this.updatedFields = Map.copyOf(updatedFields);
        this.oldValues = Map.copyOf(oldValues);
        this.operatorId = operatorId;
    }
    
    @Override
    public String getEventType() {
        return "ProductUpdated";
    }
    
    /**
     * 检查是否更新了价格信息
     */
    public boolean isPriceUpdated() {
        return updatedFields.containsKey("retailPrice") || 
               updatedFields.containsKey("memberPrice") ||
               updatedFields.containsKey("purchasePrice");
    }
    
    /**
     * 检查是否更新了商品标志
     */
    public boolean isFlagUpdated() {
        return updatedFields.containsKey("productFlag");
    }
    
    /**
     * 检查是否更新了基本信息
     */
    public boolean isBasicInfoUpdated() {
        return updatedFields.containsKey("commonName") ||
               updatedFields.containsKey("brandName") ||
               updatedFields.containsKey("spec") ||
               updatedFields.containsKey("manufacturer") ||
               updatedFields.containsKey("approvalNumber");
    }
    
    /**
     * 获取指定字段的新值
     */
    public Object getNewValue(String fieldName) {
        return updatedFields.get(fieldName);
    }
    
    /**
     * 获取指定字段的旧值
     */
    public Object getOldValue(String fieldName) {
        return oldValues.get(fieldName);
    }
    
    /**
     * 检查是否更新了指定字段
     */
    public boolean isFieldUpdated(String fieldName) {
        return updatedFields.containsKey(fieldName);
    }
}