package com.xyy.saas.inquiry.product.server.dal.mysql.product;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibSyncDO;
import com.xyy.saas.inquiry.product.server.enums.StdlibSyncStatusEnum;
import com.xyy.saas.inquiry.product.server.enums.StdlibSyncTypeEnum;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Arrays;
import java.util.List;

@Mapper
public interface ProductStdlibSyncMapper extends BaseMapperX<ProductStdlibSyncDO> {

    default ProductStdlibSyncDO selectByGuid(String guid) {
        return selectOne(new LambdaQueryWrapperX<ProductStdlibSyncDO>()
            .eq(ProductStdlibSyncDO::getGuid, guid));
    }

    default List<ProductStdlibSyncDO> selectLatestList(int limit) {
        return selectList(new LambdaQueryWrapperX<ProductStdlibSyncDO>()
            .orderByDesc(ProductStdlibSyncDO::getId)
            .last("LIMIT " + limit));
    }


    default List<ProductStdlibSyncDO> selectRunningFullSync(StdlibSyncStatusEnum... statusEnums) {
        if (statusEnums == null || statusEnums.length == 0) {
            statusEnums = new StdlibSyncStatusEnum[]{
                StdlibSyncStatusEnum.NOT_START, StdlibSyncStatusEnum.RUNNING
            };
        }
        return selectList(new LambdaQueryWrapperX<ProductStdlibSyncDO>()
            .eq(ProductStdlibSyncDO::getType, StdlibSyncTypeEnum.FULL.getCode())
            .in(ProductStdlibSyncDO::getStatus, Arrays.stream(statusEnums).map(StdlibSyncStatusEnum::getCode).toList())
            .orderByDesc(ProductStdlibSyncDO::getId));
    }

    ProductStdlibSyncDO selectByGuidWithLock(@Param("guid") String guid, @Param("forUpdate") boolean forUpdate);

    int updateStatus(@Param("guid") String guid, @Param("status") Integer status, @Param("oldStatus") Integer oldStatus);

    int insertOrUpdateBatch(List<ProductStdlibSyncDO> list);
} 