package com.xyy.saas.inquiry.product.server.mq.message.mid.dto;

import lombok.Data;

/**
 * 中台商品回调消息
 */
@Data
public class MidProductPresentResponseMsg {
    /**
     * 外部商品编码(商品内码)
     */
    private String outsideCode;

    /**
     * 标准库id
     */
    private String productId;

    /**
     * 发送方向 1：宜块钱 2：saas智鹿 4-POP
     */
    private Byte sendType;

    /**
     * 状态值 0:成功 2:驳回 3:审核中 4:审核不通过
     */
    private Integer statusCode;

    /**
     * 审核原因
     */
    private String remark;

    /**
     * remarkCode = 9 时 remark 为自定义错误原因
     */
    private String remarkCode;

    /**
     * 时间戳
     */
    private String timeStamp;
} 