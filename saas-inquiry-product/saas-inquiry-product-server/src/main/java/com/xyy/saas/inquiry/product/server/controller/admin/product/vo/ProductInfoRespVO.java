package com.xyy.saas.inquiry.product.server.controller.admin.product.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.product.api.product.dto.ProductFlag;
import com.xyy.saas.inquiry.product.api.product.dto.ProductUseInfoDto;
import com.xyy.saas.inquiry.product.api.product.dto.qualification.ProductQualificationInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductStatusEnum;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmBusinessRelationRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Schema(description = "管理后台 - 商品基本信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProductInfoRespVO {

    @Schema(description = "商品编号（自动生成，主键）", requiredMode = Schema.RequiredMode.REQUIRED, example = "21893")
    @ExcelProperty("商品编号（自动生成，主键）")
    private Long id;

    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编码")
    private String pref;

    @Schema(description = "商品外码（自动生成或填写，机构唯一）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品外码（自动生成或填写，机构唯一）")
    private String showPref;

    @Schema(description = "源商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sourceProductPref;

    @Schema(description = "源商品外码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sourceProductShowPref;

    @Schema(description = "中台标准库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21163")
    @ExcelProperty("中台标准库ID")
    private Long midStdlibId;

    @Schema(description = "标准库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21163")
    @ExcelProperty("标准库ID")
    private Long stdlibId;

    @Schema(description = "助记码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("助记码")
    private String mnemonicCode;

    @Schema(description = "通用名", requiredMode = Schema.RequiredMode.REQUIRED, example = "李四")
    @ExcelProperty("通用名")
    private String commonName;

    @Schema(description = "品牌名", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    @ExcelProperty("品牌名")
    private String brandName;

    @Schema(description = "规格/型号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("规格/型号")
    private String spec;

    @Schema(description = "条形码（基本单位条形码）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("条形码")
    private String barcode;

    @Schema(description = "单位", requiredMode = Schema.RequiredMode.REQUIRED, example = "23148")
    @ExcelProperty("单位")
    private String unit;

    @Schema(description = "剂型", requiredMode = Schema.RequiredMode.REQUIRED, example = "21146")
    @ExcelProperty("剂型")
    private String dosageForm;

    @Schema(description = "所属范围", requiredMode = Schema.RequiredMode.REQUIRED, example = "6843")
    @ExcelProperty("所属范围")
    private String businessScope;

    @Schema(description = "处方分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "8689")
    @ExcelProperty("处方分类")
    private String presCategory;

    @Schema(description = "储存条件", requiredMode = Schema.RequiredMode.REQUIRED, example = "1588")
    @ExcelProperty("储存条件")
    private String storageWay;

    @Schema(description = "产地", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("产地")
    private String origin;

    @Schema(description = "生产厂家", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("生产厂家")
    private String manufacturer;

    @Schema(description = "生产企业社会信用代码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("生产企业社会信用代码")
    private String manufacturerUscc;

    @Schema(description = "有效期,例12个月,36个月", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("有效期,例12个月,36个月")
    private String productValidity;

    @Schema(description = "批准文号（商品分类为医疗器械时，变为备案/注册证）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("批准文号（商品分类为医疗器械时，变为备案/注册证）")
    private String approvalNumber;

    @Schema(description = "批准文号有效期")
    @ExcelProperty("批准文号有效期")
    private LocalDate approvalValidityPeriod;

    @Schema(description = "上市许可持有人", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("上市许可持有人")
    private String marketingAuthorityHolder;

    @Schema(description = "进项税率")
    @ExcelProperty("进项税率")
    private BigDecimal inputTaxRate;

    @Schema(description = "销项税率")
    @ExcelProperty("销项税率")
    private BigDecimal outputTaxRate;

    @Schema(description = "药品标识码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("药品标识码")
    private String drugIdentCode;

    @Schema(description = "用药方式")
    @ExcelProperty("用药方式")
    private String usageMethod;
    @Schema(description = "用药频次")
    @ExcelProperty("用药频次")
    private String usageFrequency;
    @Schema(description = "单次使用剂量")
    @ExcelProperty("单次使用剂量")
    private String singleDosage;
    @Schema(description = "单次使用剂量单位")
    @ExcelProperty("单次使用剂量单位")
    private String singleDosageUnit;

    @Schema(description = "一级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @ExcelProperty("一级分类")
    private String firstCategory;
    @Schema(description = "二级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @ExcelProperty("二级分类")
    private String secondCategory;
    @Schema(description = "三级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @ExcelProperty("三级分类")
    private String thirdCategory;
    @Schema(description = "四级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @ExcelProperty("四级分类")
    private String fourthCategory;
    @Schema(description = "五级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @ExcelProperty("五级分类")
    private String fiveCategory;
    @Schema(description = "六级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    @ExcelProperty("六级分类")
    private String sixCategory;

    @Schema(description = "拆零数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("拆零数量")
    private Integer unbundledQuantity;

    @Schema(description = "状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("状态")
    private Integer status;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("是否禁用")
    private Boolean disable;

    @Schema(description = "删除时间")
    @ExcelProperty("删除时间")
    private LocalDateTime deletedAt;

    @Schema(description = "删除类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("删除类型")
    private Integer deleteType;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
    @Schema(description = "更新时间", requiredMode = Schema.RequiredMode.REQUIRED, example = "2025-01-01 00:00:00")
    private LocalDateTime updateTime;
    @Schema(description = "创建人ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String creator;
    @Schema(description = "创建人名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String creatorName;
    @Schema(description = "更新人ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String updater;
    @Schema(description = "更新人名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private String updaterName;


    @Schema(description = "商品封面图", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> coverImages;

    @Schema(description = "商品外包装图", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> outerPackageImages;

    @Schema(description = "商品说明书图", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> instructionImages;

    // 禁采
    @Schema(description = "禁采状态", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer purchaseDisabled;

    // 多属性标志
    @Schema(description = "多属性标志", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductFlag productFlag;

    // 使用信息（价格，医保匹配信息）
    @Schema(description = "使用信息（价格，医保匹配信息）", requiredMode = RequiredMode.REQUIRED, example = "{}")
    private ProductUseInfoDto useInfo;

    // 资质信息
    @Schema(description = "资质信息", requiredMode = RequiredMode.REQUIRED, example = "{}")
    private ProductQualificationInfoDto qualificationInfo;

    // 首营审批流程
    @Schema(description = "首营审批流程", requiredMode = RequiredMode.REQUIRED, example = "{}")
    private BpmBusinessRelationRespVO firstAprBizRlt;
    // 总部审批流程
    @Schema(description = "总部审批流程", requiredMode = RequiredMode.REQUIRED, example = "{}")
    private BpmBusinessRelationRespVO headAprBizRlt;


    @Schema(description = "审批状态 1-待审批 2-已通过 3-已驳回", example = "1")
    private Integer approvalStatus;


    /**
     * 转换审批状态
     */
    public void transformApprovalStatus() {
        if (this.approvalStatus != null) {
            return;
        }
        ProductStatusEnum productStatusEnum = ProductStatusEnum.getByCode(this.status);
        if (productStatusEnum == null) {
            return;
        }
        this.approvalStatus = switch (productStatusEnum) {
            case ProductStatusEnum.MID_AUDITING, ProductStatusEnum.HEAD_AUDITING, ProductStatusEnum.FIRST_AUDITING -> 1;
            case ProductStatusEnum.USING -> 2;
            case ProductStatusEnum.MID_AUDIT_REJECT, ProductStatusEnum.HEAD_AUDIT_REJECT, ProductStatusEnum.FIRST_AUDIT_REJECT -> 3;
            default -> null;
        };
    }
}