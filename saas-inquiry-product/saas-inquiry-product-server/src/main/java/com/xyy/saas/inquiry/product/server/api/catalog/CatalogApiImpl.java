package com.xyy.saas.inquiry.product.server.api.catalog;

import cn.hutool.core.collection.CollUtil;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.product.api.catalog.CatalogApi;
import com.xyy.saas.inquiry.product.api.catalog.dto.CatalogRespDTO;
import com.xyy.saas.inquiry.product.server.convert.catalog.CatalogConvert;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.CatalogDO;
import com.xyy.saas.inquiry.product.server.service.catalog.CatalogService;
import jakarta.annotation.Resource;
import java.util.List;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * 目录
 */
@DubboService
public class CatalogApiImpl implements CatalogApi {

    @Resource
    private CatalogService catalogService;

    @Override
    public List<CatalogRespDTO> getCatalogByIdList(List<Long> idList) {

        List<CatalogDO> catalogDOList = catalogService.getCatalog(idList);

        return CollUtil.isEmpty(catalogDOList) ? Lists.newArrayList() : CatalogConvert.INSTANCE.convertDOList2DTOList(catalogDOList);
    }
}
