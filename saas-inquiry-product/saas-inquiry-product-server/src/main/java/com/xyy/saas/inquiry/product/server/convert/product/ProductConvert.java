package com.xyy.saas.inquiry.product.server.convert.product;


import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDetailDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibDto;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidGeneralProductDto;
import com.xyy.saas.inquiry.product.server.controller.app.search.vo.InquiryProductAddReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.search.vo.InquiryProductSearchRespVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibDO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Mapper
public interface ProductConvert {

    ProductConvert INSTANCE = Mappers.getMapper(ProductConvert.class);

    @Mapping(target = "productFlag", expression = "java(com.xyy.saas.inquiry.product.api.product.dto.ProductFlag.ofStdlib(stdlibDO.getMultiFlag()))")
    @Mapping(target = "coverImages", source = "ext.coverImages")
    @Mapping(target = "outerPackageImages", source = "ext.outerPackageImages")
    @Mapping(target = "instructionImages", source = "ext.instructionImages")
    ProductStdlibDto ProductStdlibDO2Dto(ProductStdlibDO stdlibDO);

    List<ProductStdlibDto> ProductStdlibDO2DtoList(List<ProductStdlibDO> doList);


    @Mapping(target = "id", ignore = true)
    @Mapping(target = "status", ignore = true)
    @Mapping(target = "disable", ignore = true)
    @Mapping(target = "productFlag", ignore = true)
    @Mapping(target = "multiFlag", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "createTime", ignore = true)
    @Mapping(target = "updateTime", ignore = true)
    @Mapping(target = "creator", ignore = true)
    @Mapping(target = "updater", ignore = true)
    @Mapping(target = "coverImages", source = "ext.coverImages")
    @Mapping(target = "outerPackageImages", source = "ext.outerPackageImages")
    @Mapping(target = "instructionImages", source = "ext.instructionImages")
    @Mapping(target = "stdlibId", source = "id")
    void ProductStdlibDO2ProductInfoDto(ProductStdlibDO stdlibDO, @MappingTarget ProductInfoDto dto);


    @Mapping(target = "status", ignore = true)
    @Mapping(target = "disable", ignore = true)
    @Mapping(target = "multiFlag", ignore = true)
    @Mapping(target = "remark", ignore = true)
    @Mapping(target = "ext.coverImages", source = "coverImages")
    @Mapping(target = "ext.outerPackageImages", source = "outerPackageImages")
    @Mapping(target = "ext.instructionImages", source = "instructionImages")
    @Mapping(target = "id", source = "stdlibId")
    ProductStdlibDO ProductInfoDto2ProductStdlibDO(ProductInfoDto dto);

    @Mapping(target = "ext.coverImages", source = "coverImages")
    @Mapping(target = "ext.outerPackageImages", source = "outerPackageImages")
    @Mapping(target = "ext.instructionImages", source = "instructionImages")
    ProductStdlibDO ProductInfoDto2ProductStdlibDO(ProductStdlibDto dto);


    @Mapping(target = "midStdlibId", source = "productId")
    @Mapping(target = "commonName", source = "generalName")
    @Mapping(target = "brandName", source = "brand")
    @Mapping(target = "spec", source = "spec")
    @Mapping(target = "barcode", source = "smallPackageCode")
    @Mapping(target = "manufacturer", source = "manufacturerName")
    @Mapping(target = "approvalNumber", expression = "java(org.apache.commons.lang3.StringUtils.truncate(dto.getApprovalNo(), 256))")
    // @Mapping(target = "minPackageNum", source = "productId")
    @Mapping(target = "spuCategory", source = "spuCategoryName")
    @Mapping(target = "unit", source = "packageUnitName")
    @Mapping(target = "dosageForm", source = "dosageFormName")
    @Mapping(target = "businessScope", source = "businessScopeMultiName")
    @Mapping(target = "presCategory", source = "prescriptionCategoryName")
    @Mapping(target = "storageWay", source = "storageCondName")
    @Mapping(target = "origin", source = "originPlace")
    @Mapping(target = "manufacturerUscc", source = "manufacturingLicenseNo")
    @Mapping(target = "productValidity", source = "validity")
    // @Mapping(target = "approvalValidityPeriod", source = "productId")
    // @Mapping(target = "marketingAuthorityHolder", source = "productId")
    @Mapping(target = "usageMethod", source = "medicationMethodName")
    @Mapping(target = "usageFrequency", source = "medicationFrequencyName")
    // @Mapping(target = "singleDosage", source = "medicationDosage")
    @Mapping(target = "singleDosage", expression = "java(org.apache.commons.lang3.StringUtils.equals(\"适量\",dto.getMiniUnitName()) ? \"\" : dto.getMedicationDosage())")
    @Mapping(target = "singleDosageUnit", source = "miniUnitName")
    @Mapping(target = "firstCategory", source = "firstCategoryName")
    @Mapping(target = "secondCategory", source = "secondCategoryName")
    @Mapping(target = "thirdCategory", source = "thirdCategoryName")
    @Mapping(target = "fourthCategory", source = "fourthCategoryName")
    @Mapping(target = "fiveCategory", source = "fiveCategoryName")
    @Mapping(target = "sixCategory", source = "sixCategoryName")
    @Mapping(target = "ext.firstCategoryId", source = "firstCategory")
    @Mapping(target = "ext.secondCategoryId", source = "secondCategory")
    @Mapping(target = "ext.thirdCategoryId", source = "thirdCategory")
    @Mapping(target = "ext.fourthCategoryId", source = "fourthCategory")
    @Mapping(target = "ext.fiveCategoryId", source = "fiveCategory")
    @Mapping(target = "ext.sixCategoryId", source = "sixCategory")
    @Mapping(target = "multiFlag", expression = "java(com.xyy.saas.inquiry.product.api.product.dto.ProductFlag.toStdlibFlag(null, dto.toStdlibMultiFlag()))")
    @Mapping(target = "status", expression = "java(com.xyy.saas.inquiry.product.enums.ProductStdlibStatusEnum.USING.code)")
    ProductStdlibDO MidGeneralProductDto2ProductStdlibDO(MidGeneralProductDto dto);


    @Mapping(target = "spuCategory", source = "medicineType")
    @Mapping(target = "commonName", source = "commonName")
    @Mapping(target = "brandName", source = "productName")
    @Mapping(target = "barcode", source = "barcode")
    @Mapping(target = "spec", source = "attributeSpecification")
    @Mapping(target = "approvalNumber", source = "approvalNumber")
    @Mapping(target = "unit", source = "unitName")
    @Mapping(target = "usageMethod", source = "directions")
    @Mapping(target = "usageFrequency", source = "useFrequency")
    @Mapping(target = "singleDosage", source = "singleDose")
    @Mapping(target = "singleDosageUnit", source = "singleUnit")
    ProductInfoDto InquiryProductAddReqVO2ProductInfoDto(InquiryProductAddReqVO vo);

    @Mapping(target = "pref", source = "id")
    @Mapping(target = "standardId", source = "midStdlibId")
    @Mapping(target = "productName", source = "brandName")
    @Mapping(target = "commonName", source = "commonName")
    @Mapping(target = "attributeSpecification", source = "spec")
    @Mapping(target = "manufacturer", source = "manufacturer")
    @Mapping(target = "approvalNumber", source = "approvalNumber")
    @Mapping(target = "unitName", source = "unit")
    @Mapping(target = "dosageForm", source = "dosageForm")
    @Mapping(target = "directions", source = "usageMethod")
    @Mapping(target = "useFrequency", source = "usageFrequency")
    @Mapping(target = "useFrequencyValue", source = "usageFrequency")
    @Mapping(target = "singleDose", source = "singleDosage")
    @Mapping(target = "singleUnit", source = "singleDosageUnit")
    InquiryProductSearchRespVO ProductStdlibDto2InquiryProductSearchRespVO(ProductStdlibDto dto);

    @Mapping(target = "pref", source = "id")
    @Mapping(target = "standardId", source = "midStdlibId")
    @Mapping(target = "productName", source = "brandName")
    @Mapping(target = "commonName", source = "commonName")
    @Mapping(target = "attributeSpecification", source = "spec")
    @Mapping(target = "manufacturer", source = "manufacturer")
    @Mapping(target = "approvalNumber", source = "approvalNumber")
    @Mapping(target = "unitName", source = "unit")
    @Mapping(target = "dosageForm", source = "dosageForm")
    @Mapping(target = "directions", source = "usageMethod")
    @Mapping(target = "useFrequency", source = "usageFrequency")
    @Mapping(target = "useFrequencyValue", source = "usageFrequency")
    @Mapping(target = "singleDose", source = "singleDosage")
    @Mapping(target = "singleUnit", source = "singleDosageUnit")
    @Mapping(target = "barCode", source = "barcode")
    InquiryProductDetailDto ProductStdlibDto2InquiryProductDetailDto(ProductStdlibDto dto);
}
