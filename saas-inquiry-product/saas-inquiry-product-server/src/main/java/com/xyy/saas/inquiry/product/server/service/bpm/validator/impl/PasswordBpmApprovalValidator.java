package com.xyy.saas.inquiry.product.server.service.bpm.validator.impl;

import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.xyy.saas.inquiry.product.enums.BpmBusinessTypeEnum;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmApprovalValidateResult;
import com.xyy.saas.inquiry.product.server.service.bpm.validator.BpmApprovalValidator;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 密码验证器实现
 */
@Component
@Slf4j
public class PasswordBpmApprovalValidator implements BpmApprovalValidator {
    
    @Resource
    private AdminUserApi adminUserApi;
    
    @Override
    public BpmApprovalValidateResult validate(String taskId, Long userId, Map<String, Object> params) {
        String password = (String) params.get("password");
        if (StringUtils.isEmpty(password)) {
            return BpmApprovalValidateResult.error("密码不能为空");
        }

        AdminUserRespDTO user = adminUserApi.getUser(userId);
        if (user == null) {
            return BpmApprovalValidateResult.error("用户不存在");
        }

        // 调用用户服务验证密码
        boolean valid = adminUserApi.validPassword(userId, password);
        if (!valid) {
            return BpmApprovalValidateResult.error("密码不正确");
        }
        
        return BpmApprovalValidateResult.success();
    }
    
    @Override
    public BpmBusinessTypeEnum[] supportBusinessTypes() {
        return new BpmBusinessTypeEnum[]{
            BpmBusinessTypeEnum.PRODUCT_FIRST_APPROVE,
            BpmBusinessTypeEnum.PRODUCT_TO_HEADQUARTERS_APPROVE
        };
    }
    
    @Override
    public String getType() {
        return "password";
    }
} 