package com.xyy.saas.inquiry.product.server.application.step;

import com.xyy.saas.inquiry.product.server.application.context.ProductSaveContext;
import com.xyy.saas.inquiry.product.server.application.strategy.ProductBizTypeHandler;
import com.xyy.saas.inquiry.product.server.application.strategy.ProductBizTypeHandlerFactory;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 后置处理步骤 - 执行业务类型特定的后置逻辑
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
@Order(999) // 最后执行
public class Step999AsPostProcessing implements ProductSaveStep {
    
    @Resource
    private ProductBizTypeHandlerFactory bizTypeHandlerFactory;
    
    @Override
    public boolean isApplicable(ProductSaveContext context) {
        // 只有在前面的步骤都成功的情况下才执行后置处理
        return context.getProductId() != null;
    }
    
    @Override
    public void execute(ProductSaveContext context) {
        log.info("[Step999AsPostProcessing] 开始执行后置处理");
        
        try {
            // 获取业务类型处理器
            ProductBizTypeHandler handler = bizTypeHandlerFactory.getHandler(context.getBizType());
            if (handler != null) {
                // 执行后置处理
                handler.executePostProcessing(context);
            }
            
            // 标记为成功
            context.markSuccess(context.getProductId());
            
            log.info("[Step999AsPostProcessing] 后置处理完成");
            
        } catch (Exception e) {
            log.error("[Step999AsPostProcessing] 后置处理失败", e);
            // 后置处理失败不影响主流程，只记录警告
            log.warn("[Step999AsPostProcessing] 后置处理失败，但主流程已完成: {}", e.getMessage());
            context.setAttribute("postProcessingError", e.getMessage());
            
            // 仍然标记为成功
            context.markSuccess(context.getProductId());
        }
    }
    
    @Override
    public String getStepDescription() {
        return "执行业务类型特定的后置处理逻辑";
    }
}