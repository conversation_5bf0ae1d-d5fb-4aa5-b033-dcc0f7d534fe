package com.xyy.saas.inquiry.product.server.application.strategy;

import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.server.application.context.ProductSaveContext;

/**
 * 商品业务类型处理策略接口
 * 
 * <AUTHOR> Assistant
 */
public interface ProductBizTypeHandler {
    
    /**
     * 获取支持的业务类型
     */
    ProductBizTypeEnum getSupportedBizType();
    
    /**
     * 处理商品状态和标志
     * 
     * @param dto 商品信息
     */
    void handleProductStatusAndFlag(ProductInfoDto dto);
    
    /**
     * 是否需要处理标准库
     */
    default boolean shouldProcessStdlib() {
        return !getSupportedBizType().ignoreHandleStdlib();
    }
    
    /**
     * 是否抛出标准库唯一性异常
     */
    default boolean shouldThrowStdlibUniqueException() {
        return getSupportedBizType().throwExceptionIfStdlibUnique();
    }
    
    /**
     * 执行后置处理逻辑
     * 
     * @param context 处理上下文
     */
    default void executePostProcessing(ProductSaveContext context) {
        // 默认无后置处理
    }
    
    /**
     * 获取处理器名称
     */
    default String getHandlerName() {
        return this.getClass().getSimpleName();
    }
}