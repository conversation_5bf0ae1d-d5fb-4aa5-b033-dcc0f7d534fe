package com.xyy.saas.inquiry.product.server.controller.app.product.vo;

import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.mybatis.core.type.StringListTypeHandler;
import com.alibaba.excel.annotation.ExcelProperty;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.core.type.TypeReference;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.product.api.product.dto.ProductFlag;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.api.transfer.dto.ProductTransferRecordExtDto;
import com.xyy.saas.inquiry.product.enums.ProductTransferStatusEnum;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidResponse2;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoDO2;
import com.xyy.saas.inquiry.product.server.mq.message.mid.dto.MidProductPresentResponseMsg;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Stack;

@Schema(description = "APP后台 - 商品提报记录 Response VO")
@Data
public class ProductPresentRecordRespVO {

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "商品编码", example = "P202403010001")
    private String productPref;

    @Schema(description = "同步类型", example = "1")
    private Integer type;

    @Schema(description = "源租户编号", example = "1")
    private Long sourceTenantId;

    @Schema(description = "目标租户编号", example = "1")
    private Long targetTenantId;

    @Schema(description = "标准库ID（中台）", example = "xxx")
    private Long midStdlibId;

    @Schema(description = "商品外码", example = "xxx")
    private String showPref;
    @Schema(description = "助记码", example = "xxx")
    private String mnemonicCode;
    @Schema(description = "通用名", example = "xxx")
    private String commonName;
    @Schema(description = "品牌名称", example = "xxx")
    private String brandName;
    @Schema(description = "规格/型号", example = "xxx")
    private String spec;
    @Schema(description = "条形码", example = "xxx")
    private String barcode;
    @Schema(description = "包装单位", example = "xxx")
    private String unit;
    @Schema(description = "生产厂家", example = "xxx")
    private String manufacturer;
    @Schema(description = "批准文号", example = "xxx")
    private String approvalNumber;

    /**
     * 扩展信息
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private ProductTransferRecordExtDto ext;



    @Schema(description = "商品封面图", example = "[]")
    private List<String> coverImages;
    @Schema(description = "商品外包装图", example = "[]")
    private List<String> outerPackageImages;
    @Schema(description = "商品说明书图", example = "[]")
    private List<String> instructionImages;

    @Schema(description = "审核状态(0-待审核 1-审核通过 2-审核驳回)", example = "1")
    private Integer approveStatus;

    @Schema(description = "状态")
    private Integer status;
    @Schema(description = "同步结果")
    private String result;
    @Schema(description = "错误信息")
    private String errorMsg;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "源门店名称")
    private String sourceTenantName;
    @Schema(description = "源门店编码")
    private String sourceTenantPref;
    @Schema(description = "门店所在省")
    private String sourceTenantProvince;
    @Schema(description = "门店所在市")
    private String sourceTenantCity;
    @Schema(description = "门店所在区")
    private String sourceTenantArea;
    @Schema(description = "门店所在地区")
    private String sourceTenantRegion;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    // @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    // private String pref;
    // @Schema(description = "标准库ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21163")
    // private Long stdlibId;
    // @Schema(description = "剂型", requiredMode = Schema.RequiredMode.REQUIRED, example = "21146")
    // private String dosageForm;
    // @Schema(description = "所属范围", requiredMode = Schema.RequiredMode.REQUIRED, example = "6843")
    // private String businessScope;
    @Schema(description = "处方分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "8689")
    private String presCategory;
    // @Schema(description = "储存条件", requiredMode = Schema.RequiredMode.REQUIRED, example = "1588")
    // private String storageWay;
    // @Schema(description = "产地", requiredMode = Schema.RequiredMode.REQUIRED)
    // private String origin;
    // @Schema(description = "生产企业社会信用代码", requiredMode = Schema.RequiredMode.REQUIRED)
    // private String manufacturerUscc;
    // @Schema(description = "有效期,例12个月,36个月", requiredMode = Schema.RequiredMode.REQUIRED)
    // private String productValidity;
    // @Schema(description = "批准文号有效期")
    // private LocalDate approvalValidityPeriod;
    // @Schema(description = "上市许可持有人", requiredMode = Schema.RequiredMode.REQUIRED)
    // private String marketingAuthorityHolder;
    // @Schema(description = "进项税率")
    // private BigDecimal inputTaxRate;
    // @Schema(description = "销项税率")
    // private BigDecimal outputTaxRate;
    // @Schema(description = "药品标识码", requiredMode = Schema.RequiredMode.REQUIRED)
    // private String drugIdentCode;
    //
    // @Schema(description = "用药方式")
    // private String usageMethod;
    // @Schema(description = "用药频次")
    // private String usageFrequency;
    // @Schema(description = "单次使用剂量")
    // private String singleDosage;
    // @Schema(description = "单次使用剂量单位")
    // private String singleDosageUnit;
    //
    // @Schema(description = "一级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    // private String firstCategory;
    // @Schema(description = "二级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    // private String secondCategory;
    // @Schema(description = "三级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    // private String thirdCategory;
    // @Schema(description = "四级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    // private String fourthCategory;
    // @Schema(description = "五级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    // private String fiveCategory;
    // @Schema(description = "六级分类", requiredMode = Schema.RequiredMode.REQUIRED, example = "12764")
    // private String sixCategory;


    /**
     * 组装扩展信息
     * @return
     */
    public void assembleExtInfo() {
        if (ext == null) {
            return;
        }
        this.unit = this.ext.getUnit();
        this.coverImages = this.ext.getCoverImages();
        this.outerPackageImages = this.ext.getOuterPackageImages();
        this.instructionImages = this.ext.getInstructionImages();
    }

    /**
     * 组装商品信息
     * @param productInfoDO2
     * @return
     */
    public void assembleProductInfo(ProductInfoDO2 productInfoDO2) {
        if (productInfoDO2 == null || productInfoDO2.getStdlib() == null) {
            return;
        }
        this.unit = productInfoDO2.getStdlib().getUnit();
        this.midStdlibId = productInfoDO2.getStdlib().getMidStdlibId();
        this.presCategory = productInfoDO2.getStdlib().getPresCategory();

        if (productInfoDO2.getStdlib().getExt() != null) {
            this.coverImages = productInfoDO2.getStdlib().getExt().getCoverImages();
            this.outerPackageImages = productInfoDO2.getStdlib().getExt().getOuterPackageImages();
            this.instructionImages = productInfoDO2.getStdlib().getExt().getInstructionImages();
        }

        // this.approveStatus = ProductTransferStatusEnum.getByCode(status).map(i -> i.approveStatus).orElse(null);
        // // 审批通过，取商品中台数据（mq消息消费时更新数据库，这里不需要再取）
        // if (Objects.equals(this.approveStatus, ProductTransferStatusEnum.SUCCESS.approveStatus)) {
        //     this.commonName = productInfoDO2.getStdlib().getCommonName();
        //     this.brandName = productInfoDO2.getStdlib().getBrandName();
        //     this.barcode = productInfoDO2.getStdlib().getBarcode();
        //     this.spec = productInfoDO2.getStdlib().getSpec();
        //     this.manufacturer = productInfoDO2.getStdlib().getManufacturer();
        //     this.approvalNumber = productInfoDO2.getStdlib().getApprovalNumber();
        // }
    }

    /**
     * 解析错误信息
     * @param result
     * @return
     */
    public static String parseErrorMsg(String result) {
        // 上报中台接口返回结果
        MidResponse2<?> midResponse2 = JsonUtils.parseObject2(result, MidResponse2.class);
        if (midResponse2 != null && StringUtils.isNotBlank(midResponse2.getRetMsg())) {
            return midResponse2.getRetMsg();
        }
        // 中台回调消息结果
        MidProductPresentResponseMsg presentResponseMsg = JsonUtils.parseObject2(result, MidProductPresentResponseMsg.class);
        if (presentResponseMsg != null && StringUtils.isNotBlank(presentResponseMsg.getRemark())) {
            return presentResponseMsg.getRemark();
        }
        return null;
    }

    /**
     * 组装全部信息：扩展信息、商品信息、错误信息、省市区
     * @return
     */
    public ProductPresentRecordRespVO assembleFullInfo(ProductInfoDO2 productInfoDO2) {
        this.approveStatus = ProductTransferStatusEnum.getByCode(status).map(i -> i.approveStatus).orElse(null);
        // 扩展信息展开（需要在 assembleProductInfo 之前）
        assembleExtInfo();

        // 审批通过，取商品中台数据
        if (Objects.equals(this.approveStatus, ProductTransferStatusEnum.SUCCESS.approveStatus)) {
            assembleProductInfo(productInfoDO2);
        }

        if (this.errorMsg == null) {
            this.errorMsg = ProductPresentRecordRespVO.parseErrorMsg(this.result);
        }
        // 省市区-拼接，入栈：空的忽略，与栈顶相同的忽略，然后全部取出 用-拼接
        Stack<String> stack = new Stack<>();
        if (StringUtils.isNotBlank(sourceTenantProvince)) {
            stack.push(sourceTenantProvince);
        }
        if (StringUtils.isNotBlank(sourceTenantCity) && (stack.isEmpty() || !stack.peek().equals(sourceTenantCity))) {
            stack.push(sourceTenantCity);
        }
        if (StringUtils.isNotBlank(sourceTenantArea) && (stack.isEmpty() || !stack.peek().equals(sourceTenantArea))) {
            stack.push(sourceTenantArea);
        }
        this.sourceTenantRegion = String.join("-", stack);
        return this;
    }
} 