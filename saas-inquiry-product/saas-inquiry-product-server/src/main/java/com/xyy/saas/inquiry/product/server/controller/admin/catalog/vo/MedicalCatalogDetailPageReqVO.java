package com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import java.io.Serializable;
import java.util.List;

@Schema(description = "管理后台 - 医保目录明细分页 Request VO")
@Data
@ToString(callSuper = true)
public class MedicalCatalogDetailPageReqVO extends PageParam {

    @Schema(description = "主键ID")
    private Long id;
    @Schema(description = "主键ID列表")
    private List<Long> idList;

    @Schema(description = "目录ID")
    private Long catalogId;

    @Schema(description = "是否禁用，默认否")
    private Boolean disable;

    @Schema(description = "混合名称查询（用于全文搜索）")
    private String mixedNameQuery;
    @Schema(description = "混合名称查询（用于全文搜索）")
    private String natureNameQuery;

    @Schema(description = "医疗目录编码")
    private String projectCode;

    @Schema(description = "医疗目录名称")
    private String projectName;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "医疗目录类别")
    private String projectType;

    @Schema(description = "医疗目录等级")
    private String projectLevel;

    @Schema(description = "生产企业")
    private String manufacturer;

    @Schema(description = "批准文号")
    private String approvalNumber;

    @Schema(description = "本位码")
    private String standardCode;

}
