package com.xyy.saas.inquiry.product.server.service.bpm.listener;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.yudao.module.bpm.event.BpmProcessInstanceStatusEvent;
import com.xyy.saas.inquiry.product.api.bpm.BpmBusinessRelationDto;
import com.xyy.saas.inquiry.product.enums.BpmBusinessTypeEnum;
import com.xyy.saas.inquiry.product.server.dal.dataobject.bpm.BpmBusinessRelationDO;
import com.xyy.saas.inquiry.product.server.service.bpm.BpmBusinessRelationService;
import com.xyy.saas.inquiry.product.api.bpm.handler.BpmApprovalHandler;
import jakarta.annotation.Nonnull;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 审批流统监听器实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class BpmBusinessStatusListener implements ApplicationListener<BpmProcessInstanceStatusEvent> {

    @Resource
    private BpmBusinessRelationService businessRelationService;

    @Resource
    private List<BpmApprovalHandler> bpmApprovalHandlers;

    private final Map<Integer, LinkedHashSet<BpmApprovalHandler>> handlerMap = new ConcurrentHashMap<>();

    @PostConstruct
    public void init() {
        bpmApprovalHandlers.forEach(handler -> {
            BpmBusinessTypeEnum[] supportBusinessTypes = handler.supportBusinessTypes();
            if (supportBusinessTypes == null || supportBusinessTypes.length == 0) {
                log.warn("[BpmEvent][BpmApprovalHandler({}) 的 supportBusinessTypes 不能为空]", handler.getClass().getSimpleName());
                return;
            }

            for (BpmBusinessTypeEnum businessTypeEnum : supportBusinessTypes) {
                // businessTypeEnum 去重后添加到map中，支持业务类型可对应多个处理器
                handlerMap.computeIfAbsent(businessTypeEnum.code, key -> new LinkedHashSet<>()).add(handler);
            }
        });
        log.info("[init][初始化 BpmBusinessStatusListener 成功，共注册 {} 个 BpmApprovalHandler]", bpmApprovalHandlers.size());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void onApplicationEvent(@Nonnull BpmProcessInstanceStatusEvent event) {
        onEvent(event);
    }


    public void onEvent(@Nonnull BpmProcessInstanceStatusEvent event) {
        // 根据 流程定义的 key，判断是否是处理
        BpmBusinessTypeEnum businessTypeEnum = BpmBusinessTypeEnum.getByProcessDefinitionKey(event.getProcessDefinitionKey());
        if (businessTypeEnum == null) {
            log.warn("[BpmEvent][event({}) 不是 BpmBusinessTypeEnum 类型，不处理]", event.getProcessDefinitionKey());
            return;
        }

        // 业务关联表状态更新
        BpmBusinessRelationDto bpmBusinessRelationDto = new BpmBusinessRelationDto()
            .setId(Long.parseLong(event.getBusinessKey()))
            .setBusinessType(businessTypeEnum.code)
            .setApprovalStatus(event.getStatus());
        BpmBusinessRelationDO relationDO = businessRelationService.updateProcessInstanceStatus(bpmBusinessRelationDto);

        // 处理业务数据
        LinkedHashSet<BpmApprovalHandler> handlers = handlerMap.get(businessTypeEnum.code);
        if (CollectionUtils.isEmpty(handlers)) {
            log.warn("[BpmEvent][{} 没有对应的 BpmApprovalHandler]", businessTypeEnum.desc);
            return;
        }

        for (BpmApprovalHandler handler : handlers) {
            // 创建 BpmBusinessRelationDto 对象
            BpmBusinessRelationDto businessDto = BeanUtil.toBean(relationDO, BpmBusinessRelationDto.class);
            // 执行
            handler.handleApproval(businessDto);
        }

        log.info("[BpmEvent][{} 处理成功]", businessTypeEnum.desc);
    }
}
