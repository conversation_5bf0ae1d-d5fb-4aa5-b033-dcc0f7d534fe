package com.xyy.saas.inquiry.product.server.config.forward.dto;


import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serial;
import java.io.Serializable;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MidGeneralProductCorrectMediaVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 8440074799740244861L;

    private Byte mediaType;
    private String mediaUrl;
    private String originalMediaUrl;
    private Integer pictureOrdinal;
}
