package com.xyy.saas.inquiry.product.server.controller.app.product;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.idempotent.core.annotation.Idempotent;
import cn.iocoder.yudao.framework.idempotent.core.keyresolver.impl.ExpressionIdempotentKeyResolver;
import cn.iocoder.yudao.framework.idempotent.core.keyresolver.impl.UserIdempotentKeyResolver;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductSearchDto;
import com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentPageRespVO;
import com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentRecordRespVO;
import com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentSaveReqVO;
import com.xyy.saas.inquiry.product.server.service.product.ProductInfoService;
import com.xyy.saas.inquiry.product.server.service.product.ProductStdlibService;
import com.xyy.saas.inquiry.product.server.service.transfer.ProductTransferRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "APP后台 - 商品提报")
@RestController
@RequestMapping("/product/present")
@Validated
@Slf4j
public class AppProductPresentController {

    @Resource
    private ProductStdlibService stdlibService;

    @Resource
    private ProductInfoService productInfoService;

    @Resource
    private ProductTransferRecordService transferRecordService;

    // 根据条形码查询自建标准库商品
    @GetMapping("/self-by-barcode")
    @Operation(summary = "根据条形码查询自建标准库商品")
    @Parameter(name = "barcode", description = "条形码", required = true, example = "1024")
    public CommonResult<List<ProductStdlibDto>> getSelfProductInfoByBarcode(@RequestParam String barcode) {
        return success(stdlibService.searchStdlibByBarcode(barcode));
    }


    // 六要素防重复提交
    @Idempotent(timeout = 10, keyResolver = ExpressionIdempotentKeyResolver.class,
        keyArg = "#reqVO.commonName + #reqVO.brandName + #reqVO.spec + #reqVO.barcode + #reqVO.manufacturer + #reqVO.approvalNumber",
        message = "提报信息重复，请稍后再试") // 幂等、防止重复提交
    @PostMapping("/save")
    @Operation(summary = "保存商品提报")
    public CommonResult<Long> savePresent(@Valid @RequestBody ProductPresentSaveReqVO reqVO) {
        return success(productInfoService.saveProductPresent(reqVO));
    }

    @GetMapping("/page")
    @Operation(summary = "获得商品提报分页")
    public CommonResult<ProductPresentPageRespVO> getPresentPage(@Valid @ParameterObject ProductPresentPageReqVO pageVO) {
        if (pageVO.getSourceTenantId() == null) {
            pageVO.setSourceTenantId(TenantContextHolder.getRequiredTenantId());
        }
        return success(transferRecordService.getLatestPresentPageWithStatistics(pageVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获得商品提报详情")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<ProductPresentRecordRespVO> getPresentDetail(@RequestParam("id") Long id) {
        return success(transferRecordService.getLatestPresentById(id));
    }
}