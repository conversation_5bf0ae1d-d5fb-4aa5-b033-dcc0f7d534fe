package com.xyy.saas.inquiry.product.server.application.step;

import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.server.application.context.ProductSaveContext;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 业务类型解析步骤
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
@Order(200)
public class Step200AsBizTypeResolution implements ProductSaveStep {
    
    @Override
    public boolean isApplicable(ProductSaveContext context) {
        // 总是适用
        return true;
    }
    
    @Override
    public void execute(ProductSaveContext context) {
        log.info("[Step200AsBizTypeResolution] 开始解析业务类型");
        
        try {
            // 如果没有指定业务类型，则根据参数判断
            ProductBizTypeEnum bizType = context.getBizType();
            if (bizType == null) {
                bizType = ProductBizTypeEnum.checkIfNullBizType(context.getCurrentDto(), null);
                context.setBizType(bizType);
            }
            
            // 设置上下文标志
            setContextFlags(context, bizType);
            
            log.info("[Step200AsBizTypeResolution] 业务类型解析完成, bizType={}", bizType);
            
        } catch (Exception e) {
            log.error("[Step200AsBizTypeResolution] 业务类型解析失败", e);
            context.markFailure("业务类型解析失败: " + e.getMessage());
        }
    }
    
    /**
     * 根据业务类型设置上下文标志
     */
    private void setContextFlags(ProductSaveContext context, ProductBizTypeEnum bizType) {
        // 是否需要处理标准库
        context.setNeedProcessStdlib(!bizType.ignoreHandleStdlib());
        
        // 是否需要上报中台
        context.setNeedReportToMid(shouldReportToMid(bizType, context.getIsCreate()));
        
        // 是否需要创建审批流
        context.setNeedCreateApproval(shouldCreateApproval(bizType));
        
        log.debug("[Step200AsBizTypeResolution] 上下文标志设置: needProcessStdlib={}, needReportToMid={}, needCreateApproval={}",
            context.getNeedProcessStdlib(), context.getNeedReportToMid(), context.getNeedCreateApproval());
    }
    
    /**
     * 判断是否需要上报中台
     */
    private boolean shouldReportToMid(ProductBizTypeEnum bizType, boolean isCreate) {
        return isCreate && bizType == ProductBizTypeEnum.MID_STDLIB_ADD;
    }
    
    /**
     * 判断是否需要创建审批流
     */
    private boolean shouldCreateApproval(ProductBizTypeEnum bizType) {
        return bizType == ProductBizTypeEnum.FIRST_PRODUCT ||
               bizType == ProductBizTypeEnum.CHAIN_STORE_ADD ||
               bizType == ProductBizTypeEnum.MID_STDLIB_ADD ||
               bizType == ProductBizTypeEnum.INQUIRY_PRESENT;
    }
    
    @Override
    public String getStepDescription() {
        return "解析业务类型并设置处理标志";
    }
}