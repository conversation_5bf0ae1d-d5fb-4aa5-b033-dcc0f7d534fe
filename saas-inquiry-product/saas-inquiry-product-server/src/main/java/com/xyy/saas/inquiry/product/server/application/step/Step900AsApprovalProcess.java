package com.xyy.saas.inquiry.product.server.application.step;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_NOT_EXISTS;

import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import com.xyy.saas.inquiry.product.api.bpm.BpmBusinessRelationDto;
import com.xyy.saas.inquiry.product.enums.BpmBusinessTypeEnum;
import com.xyy.saas.inquiry.product.server.application.context.ProductSaveContext;
import com.xyy.saas.inquiry.product.server.service.bpm.BpmBusinessRelationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 审批流程创建步骤
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
@Order(900)
public class Step900AsApprovalProcess implements ProductSaveStep {
    
    @Resource
    private BpmBusinessRelationService bpmBusinessRelationService;
    
    @Override
    public boolean isApplicable(ProductSaveContext context) {
        // 检查是否需要创建审批流
        return Boolean.TRUE.equals(context.getNeedCreateApproval());
    }
    
    @Override
    public void execute(ProductSaveContext context) {
        log.info("[Step900AsApprovalProcess] 开始创建审批流程");
        
        try {
            createApprovalProcess(context);
            log.info("[Step900AsApprovalProcess] 审批流程创建完成");
            
        } catch (Exception e) {
            log.error("[Step900AsApprovalProcess] 审批流程创建失败", e);
            context.markFailure("审批流程创建失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建审批流程
     */
    private void createApprovalProcess(ProductSaveContext context) {
        var dto = context.getCurrentDto();
        
        // 根据商品状态确定业务类型
        BpmBusinessTypeEnum businessTypeEnum = BpmBusinessTypeEnum.getByAuditingStatus(dto.getStatus());
        if (businessTypeEnum == null) {
            log.debug("[Step900AsApprovalProcess] 商品状态 {} 不需要创建审批流", dto.getStatus());
            return;
        }
        
        // 获取当前登录用户
        Long loginUserId = WebFrameworkUtils.getLoginUserId();
        if (loginUserId == null) {
            throw exception(USER_NOT_EXISTS);
        }
        
        // 创建审批流
        BpmBusinessRelationDto bpmDto = new BpmBusinessRelationDto()
            .setBusinessPref(dto.getPref())
            .setBusinessType(businessTypeEnum.code)
            .setTenantId(dto.getTenantId())
            .setHeadTenantId(dto.getHeadTenantId())
            .setApplicant(loginUserId.toString());
        
        bpmBusinessRelationService.createProcessInstance(loginUserId, bpmDto);
        
        log.debug("[Step900AsApprovalProcess] 审批流创建完成, businessType={}, applicant={}",
            businessTypeEnum.code, loginUserId);
    }
    
    @Override
    public String getStepDescription() {
        return "创建商品审批流程";
    }
}