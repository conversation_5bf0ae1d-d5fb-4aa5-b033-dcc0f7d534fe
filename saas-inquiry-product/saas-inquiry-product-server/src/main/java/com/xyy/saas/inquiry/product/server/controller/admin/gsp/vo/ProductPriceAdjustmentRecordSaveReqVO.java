package com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import java.util.List;

@Schema(description = "管理后台 - 售价调整单新增/修改 Request VO")
@Data
public class ProductPriceAdjustmentRecordSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1608")
    private Long id;

    @Schema(description = "单据编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String pref;

    @Schema(description = "操作门店", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;

    @Schema(description = "适用门店", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "适用门店不能为空")
    private List<Long> applicableTenantIdList;

    @Schema(description = "调价原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "不好")
    @NotEmpty(message = "调价原因不能为空")
    private String adjustmentReason;

    @Schema(description = "审批状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer approvalStatus;

    @Schema(description = "提交/暂存", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Boolean submit;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    private String remark;

    @Schema(description = "明细", requiredMode = Schema.RequiredMode.REQUIRED, example = "[]")
    @NotEmpty(message = "明细不能为空")
    private List<ProductPriceAdjustmentDetailSaveReqVO> details;

}