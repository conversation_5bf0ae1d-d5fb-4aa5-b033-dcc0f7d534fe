package com.xyy.saas.inquiry.product.server.config.forward;

import java.time.Duration;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import jakarta.annotation.Nonnull;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.HttpStatus;
import org.springframework.web.reactive.function.client.ClientRequest;
import org.springframework.web.reactive.function.client.ExchangeFilterFunction;
import org.springframework.web.reactive.function.client.ExchangeStrategies;
import org.springframework.web.reactive.function.client.WebClient;
import org.springframework.web.reactive.function.client.WebClient.Builder;
import org.springframework.web.reactive.function.client.support.WebClientAdapter;
import org.springframework.web.service.invoker.HttpServiceProxyFactory;
import reactor.core.publisher.Mono;

/**
 * 转发旧问诊服务配置
 *
 * @Author: cxy
 */
@Slf4j
@Configuration
@EnableConfigurationProperties({InquiryForwardProperties.class})
public class ForwardWebClientConfig {

    @Bean(name = "forwardWebClient")
    public WebClient webClient(InquiryForwardProperties inquiryForwardProperties) {
        return getWebClient(inquiryForwardProperties);
    }


    @Bean
    public InquiryProductForwardClient inquiryRationalForwardClient(@Qualifier("forwardWebClient") WebClient webClient, InquiryForwardProperties inquiryForwardProperties) {
        return getHttpServiceClient(webClient, inquiryForwardProperties, InquiryProductForwardClient.class);
    }

    @Bean(name = "dubboForwardWebClient")
    public WebClient dubboForwardWebClient(InquiryForwardProperties inquiryForwardProperties) {
        return getWebClient(inquiryForwardProperties.getDubbo());
    }


    @Bean
    public InquiryDubboForwardClient inquiryDubboForwardClient(@Qualifier("dubboForwardWebClient") WebClient webClient, InquiryForwardProperties inquiryForwardProperties) {
        return getHttpServiceClient(webClient, inquiryForwardProperties.getDubbo(), InquiryDubboForwardClient.class);
    }



    private static @Nonnull WebClient getWebClient(InquiryForwardProperties inquiryForwardProperties) {
        String timeOutMsg = "请求超时，请检查网络连接或服务端状态";
        // 设置更大的缓冲区大小
        ExchangeStrategies strategies = ExchangeStrategies.builder()
            // 例如，设置为1MB
            .codecs(configurer -> configurer.defaultCodecs().maxInMemorySize(32 * 1024 * 1024))
            .build();
        // 请求超时处理器
        ExchangeFilterFunction responseTimeoutFilterFunction = ExchangeFilterFunction.ofResponseProcessor(clientResponse -> {
            if (clientResponse.statusCode().is4xxClientError() || clientResponse.statusCode().is5xxServerError()) {
                log.error("请求失败，状态码：{}，错误信息：{}", clientResponse.statusCode(), clientResponse.bodyToMono(String.class).block());
                return Mono.error(new RuntimeException(timeOutMsg));
            }
            if (clientResponse.statusCode().is2xxSuccessful()) {
                return Mono.just(clientResponse);
            }
            if (clientResponse.statusCode().is3xxRedirection()) {
                log.error("请求失败，状态码：{}，错误信息：{}", clientResponse.statusCode(), clientResponse.bodyToMono(String.class).block());
                return Mono.error(new RuntimeException(timeOutMsg));
            }
            return Mono.just(clientResponse);
        });
        Builder builder = WebClient.builder()
            .baseUrl(inquiryForwardProperties.getDomain())
            .defaultHeader("Content-Type", "application/json");
        Optional.ofNullable(inquiryForwardProperties.getSign()).ifPresent(sign -> builder.defaultHeader("sign", sign));
        return builder
            .exchangeStrategies(strategies)
            .filter(responseTimeoutFilterFunction)
            .filter(ExchangeFilterFunction.ofRequestProcessor(request -> {
                // 请求处理
                ClientRequest clientRequest = ClientRequest.from(request).build();
                log.info("请求地址：{}", clientRequest.url());
                return Mono.just(clientRequest);
            }).andThen(ExchangeFilterFunction.ofResponseProcessor(response -> {
                // 响应处理
                if (response.statusCode() == HttpStatus.REQUEST_TIMEOUT) {
                    return Mono.error(new RuntimeException(timeOutMsg));
                }
                // 处理连接异常
                if (response.statusCode().is4xxClientError() || response.statusCode().is5xxServerError()) {
                    return Mono.error(new RuntimeException("请求失败，状态码：" + response.statusCode() + "，错误信息：" + response.bodyToMono(String.class).block()));
                }
                return Mono.just(response);
            }))).build();
    }

    private static <T> @Nonnull T getHttpServiceClient(WebClient webClient, InquiryForwardProperties inquiryForwardProperties, Class<T> clazz) {
        WebClientAdapter webClientAdapter = WebClientAdapter.create(webClient);
        // 设置超时时间
        Duration timeout = Duration.ofMillis(inquiryForwardProperties.getHttpTimeOut() * 1000L);
        webClientAdapter.setBlockTimeout(timeout);

        HttpServiceProxyFactory httpServiceProxyFactory = HttpServiceProxyFactory
            .builderFor(webClientAdapter)
            .build();
        return httpServiceProxyFactory.createClient(clazz);
    }
}
