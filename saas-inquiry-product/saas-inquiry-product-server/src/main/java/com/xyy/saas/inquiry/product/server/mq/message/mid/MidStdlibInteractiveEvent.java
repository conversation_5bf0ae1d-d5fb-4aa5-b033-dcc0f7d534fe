package com.xyy.saas.inquiry.product.server.mq.message.mid;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import com.xyy.saas.inquiry.product.server.mq.message.mid.dto.MidStdlibInteractiveMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 中台标准库数据交互事件（新品提报，匹配）
 */
@Data
@Builder
@EqualsAndHashCode(callSuper = true)
public class MidStdlibInteractiveEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "MID_STDLIB_INTERACTIVE";

    private MidStdlibInteractiveMessage msg;

    @JsonCreator
    public MidStdlibInteractiveEvent(@JsonProperty("msg") MidStdlibInteractiveMessage msg) {
        this.msg = msg;
    }

    /**
     * 消息tag
     */
    @Override
    public String getTag() {
        return "";
    }
}
