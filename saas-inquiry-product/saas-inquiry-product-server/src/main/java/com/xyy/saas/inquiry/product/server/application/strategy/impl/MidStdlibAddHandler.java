package com.xyy.saas.inquiry.product.server.application.strategy.impl;

import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.enums.ProductStatusEnum;
import com.xyy.saas.inquiry.product.server.application.context.ProductSaveContext;
import com.xyy.saas.inquiry.product.server.application.strategy.ProductBizTypeHandler;
import com.xyy.saas.inquiry.product.server.service.product.ProductStdlibService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 新建标品处理器
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class MidStdlibAddHandler implements ProductBizTypeHandler {
    
    @Resource
    private ProductStdlibService stdlibService;
    
    @Override
    public ProductBizTypeEnum getSupportedBizType() {
        return ProductBizTypeEnum.MID_STDLIB_ADD;
    }
    
    @Override
    public void handleProductStatusAndFlag(ProductInfoDto dto) {
        log.info("[MidStdlibAddHandler] 处理新建标品逻辑");
        
        // 设置为中台审核状态
        dto.setStatus(ProductStatusEnum.MID_AUDITING.code);
        
        log.debug("[MidStdlibAddHandler] 商品状态设置为中台审核中: {}", ProductStatusEnum.MID_AUDITING.code);
    }
    
    @Override
    public void executePostProcessing(ProductSaveContext context) {
        if (!context.getIsCreate()) {
            return;
        }
        
        log.info("[MidStdlibAddHandler] 执行新建标品后置处理 - 直接上报中台");
        
        try {
            // 新建标品后直接上报中台（不调用匹配接口）
            stdlibService.reportProduct2MidStdlib(context.getCurrentDto());
            
            log.info("[MidStdlibAddHandler] 商品上报中台完成");
            
        } catch (Exception e) {
            log.error("[MidStdlibAddHandler] 商品上报中台失败", e);
            // 不阻断主流程，记录错误即可
            context.setAttribute("midReportError", e.getMessage());
        }
    }
}