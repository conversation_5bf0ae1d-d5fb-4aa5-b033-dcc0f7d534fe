package com.xyy.saas.inquiry.product.server.application.step;

import com.xyy.saas.inquiry.product.server.application.context.ProductSaveContext;
import com.xyy.saas.inquiry.product.server.application.strategy.ProductBizTypeHandler;
import com.xyy.saas.inquiry.product.server.application.strategy.ProductBizTypeHandlerFactory;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 业务类型处理步骤 - 使用策略模式
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
@Order(400)
public class Step400AsBizTypeHandler implements ProductSaveStep {
    
    @Resource
    private ProductBizTypeHandlerFactory bizTypeHandlerFactory;
    
    @Override
    public boolean isApplicable(ProductSaveContext context) {
        // 总是适用
        return true;
    }
    
    @Override
    public void execute(ProductSaveContext context) {
        log.info("[Step400AsBizTypeHandler] 开始处理业务类型逻辑, bizType={}", context.getBizType());
        
        try {
            // 获取业务类型处理器
            ProductBizTypeHandler handler = bizTypeHandlerFactory.getHandler(context.getBizType());
            if (handler == null) {
                log.warn("[Step400AsBizTypeHandler] 未找到业务类型处理器: {}", context.getBizType());
                return;
            }
            
            // 执行业务类型特定逻辑
            handler.handleProductStatusAndFlag(context.getCurrentDto());
            
            log.info("[Step400AsBizTypeHandler] 业务类型处理完成, 商品状态: {}", context.getCurrentDto().getStatus());
            
        } catch (Exception e) {
            log.error("[Step400AsBizTypeHandler] 业务类型处理失败", e);
            context.markFailure("业务类型处理失败: " + e.getMessage());
        }
    }
    
    @Override
    public String getStepDescription() {
        return "根据业务类型处理商品状态和标志";
    }
}