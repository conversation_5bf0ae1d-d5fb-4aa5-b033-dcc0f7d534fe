package com.xyy.saas.inquiry.product.server.service.search;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDetailDto;
import com.xyy.saas.inquiry.product.api.search.dto.InquiryDiagnosticsSearchReqDto;
import com.xyy.saas.inquiry.product.api.search.dto.InquiryDiagnosticsSearchRespDto;
import com.xyy.saas.inquiry.product.api.search.dto.InquiryProductSearchReqDto;
import com.xyy.saas.inquiry.product.server.controller.app.search.vo.InquiryProductAddReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.search.vo.InquiryProductSearchReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.search.vo.InquiryProductSearchRespVO;
import com.xyy.saas.inquiry.product.server.controller.app.search.vo.InquiryProductSearchUsageAndDosageRuleReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.search.vo.InquiryProductUsageAndDosageRuleRespVO;
import java.util.List;

/**
 * @Author:chenxiaoyi
 * @Date:2024/11/25 20:33
 */
public interface InquiryProductSearchService {

    /**
     * 常用药品推荐
     *
     * @param reqVO
     * @return
     */
    CommonResult<List<InquiryProductSearchRespVO>> recommendCommonProducts(InquiryProductSearchReqVO reqVO);

    /**
     * 查询商品名称推荐词搜搜
     *
     * @param reqVO
     * @return
     */
    CommonResult<List<String>> suggestNamesByProductsName(InquiryProductSearchReqVO reqVO);

    /**
     * 商品搜索
     *
     * @param reqVO
     * @return
     */
    CommonResult<PageResult<InquiryProductSearchRespVO>> productsByNameSpec(InquiryProductSearchReqVO reqVO);

    /**
     * 根据商品69码搜索
     *
     * @param reqVO
     * @return
     */
    CommonResult<List<InquiryProductSearchRespVO>> productByBarcode(InquiryProductSearchReqVO reqVO);

    /**
     * 药品关联诊断
     *
     * @param reqVO
     * @return
     */
    CommonResult<List<InquiryDiagnosticsSearchRespDto>> productDiagnostics(InquiryDiagnosticsSearchReqDto reqDto);

    /**
     * 手动添加商品 返回商品pref
     *
     * @param reqVO
     * @return
     */
    CommonResult<String> manualAddProduct(InquiryProductAddReqVO reqVO);

    /**
     * 根据标准库id搜索商品
     *
     * @param standardIds 标准库id
     * @return 商品列表
     */
    CommonResult<List<InquiryProductDetailDto>> queryProductStandardListByStandardIds(List<String> standardIds);

    /**
     * 根据商品通用名和规格搜索商品用法用量规则
     *
     * @param reqVO
     * @return
     */
    CommonResult<InquiryProductUsageAndDosageRuleRespVO> searchProductUsageAndDosageRule(InquiryProductSearchUsageAndDosageRuleReqVO reqVO);

    /**
     * 根据69码搜索商品
     *
     * @param barCodeList 69码
     * @return 商品列表
     */
    CommonResult<List<InquiryProductDetailDto>> getProductStandardListByBarcodeList(List<String> barCodeList, Integer medicineType);

    /**
     * 根据批准文号搜索商品
     *
     * @param approvalNoList 批准文号
     * @return 商品列表
     */
    CommonResult<List<InquiryProductDetailDto>> getProductStandardListByApprovalNoList(List<String> approvalNoList, Integer medicineType);

    CommonResult<List<InquiryProductDetailDto>> getProductStandardByProductNamesAndType(List<String> commonNameList, Integer medicineType);
}
