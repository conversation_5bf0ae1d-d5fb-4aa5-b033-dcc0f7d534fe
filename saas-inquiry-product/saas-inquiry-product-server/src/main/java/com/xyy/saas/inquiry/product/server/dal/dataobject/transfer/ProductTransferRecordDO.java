package com.xyy.saas.inquiry.product.server.dal.dataobject.transfer;

import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.JsonNode;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibExtDto;
import com.xyy.saas.inquiry.product.api.transfer.dto.ProductTransferRecordExtDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.common.logger.FluentLogger.S;
import java.util.Map;

/**
 * 商品流转记录 DO
 *
 * <AUTHOR>
 */
@TableName("saas_product_transfer_record")
@KeySequence("saas_product_transfer_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductTransferRecordDO extends BaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 商品编号
     */
    private String productPref;

    /**
     * 同步类型
     */
    private Integer type;
    /**
     * 源租户编号
     */
    private Long sourceTenantId;
    /**
     * 目标租户编号
     */
    private Long targetTenantId;

    /**
     * 商品外码
     */
    private String showPref;

    /**
     * 助记码
     */
    private String mnemonicCode;

    /**
     * 通用名
     */
    private String commonName;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 规格/型号
     */
    private String spec;

    /**
     * 条形码
     */
    private String barcode;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 批准文号
     */
    private String approvalNumber;

    /**
     * 扩展信息
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private ProductTransferRecordExtDto ext;

    /**
     * 同步状态
     */
    private Integer status;
    /**
     * 同步结果
     */
    private String result;

    /**
     * 是否禁用
     */
    private Boolean disable;
    /**
     * 备注
     */
    private String remark;

    // /**
    //  * MySQL8 全文索引可能会跳过空值字段匹配
    //  *   目前重建索引解决
    //  * @param brandName
    //  * @return
    //  */
    // public ProductTransferRecordDO setBrandName(String brandName) {
    //     this.brandName = StringUtils.isBlank(brandName) ? "null" : brandName;
    //     return this;
    // }
    // public String getBrandName() {
    //     return "null".equals(brandName) ? "" : brandName;
    // }

}