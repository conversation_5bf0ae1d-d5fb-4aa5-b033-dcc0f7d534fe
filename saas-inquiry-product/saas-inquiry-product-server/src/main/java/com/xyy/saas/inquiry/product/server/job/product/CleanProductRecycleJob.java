package com.xyy.saas.inquiry.product.server.job.product;

import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import com.xyy.saas.inquiry.product.server.service.product.ProductRecycleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

import jakarta.annotation.Resource;

/**
 * 清理商品回收站定时任务
 */
@Slf4j
@Component
@RefreshScope // 支持 Nacos 的配置刷新
public class CleanProductRecycleJob implements JobHandler {

    /**
     * 回收站数据保留天数，默认30天
     */
    @Value("${saas.product.recycle.retain-days:30}")
    private Integer retainDays;

    @Resource
    private ProductRecycleService productRecycleService;

    @Override
    public String execute(String param) {
        log.info("[execute][清理商品回收站数据 - 开始]");
        try {
            // 清理超过指定天数的回收站数据
            int count = productRecycleService.cleanExpiredRecycleData(retainDays);
            log.info("[execute][清理商品回收站数据 - 完成，清理数据量:{}]", count);
            return String.format("清理数据量:%d", count);
        } catch (Exception ex) {
            log.error("[execute][清理商品回收站数据 - 异常]", ex);
            return "执行异常:" + ex.getMessage();
        }
    }

} 