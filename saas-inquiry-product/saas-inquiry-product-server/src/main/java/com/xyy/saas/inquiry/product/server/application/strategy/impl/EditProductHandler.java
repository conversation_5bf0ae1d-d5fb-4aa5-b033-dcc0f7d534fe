package com.xyy.saas.inquiry.product.server.application.strategy.impl;

import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.enums.ProductStatusEnum;
import com.xyy.saas.inquiry.product.server.application.strategy.ProductBizTypeHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * 编辑商品处理器
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class EditProductHandler implements ProductBizTypeHandler {
    
    @Override
    public ProductBizTypeEnum getSupportedBizType() {
        return ProductBizTypeEnum.EDIT_PRODUCT;
    }
    
    @Override
    public void handleProductStatusAndFlag(ProductInfoDto dto) {
        log.info("[EditProductHandler] 处理编辑商品逻辑");
        
        // 编辑商品：如果没有指定状态，默认使用中
        Integer status = Optional.ofNullable(dto.getStatus())
            .orElse(ProductStatusEnum.USING.code);
        dto.setStatus(status);
        
        log.debug("[EditProductHandler] 商品状态设置为: {}", status);
    }
}