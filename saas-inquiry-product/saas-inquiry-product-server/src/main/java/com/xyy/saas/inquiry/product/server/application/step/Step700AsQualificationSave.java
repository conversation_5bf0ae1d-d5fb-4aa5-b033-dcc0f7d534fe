package com.xyy.saas.inquiry.product.server.application.step;

import cn.hutool.core.bean.BeanUtil;
import com.xyy.saas.inquiry.product.api.product.dto.qualification.ProductQualificationInfoDto;
import com.xyy.saas.inquiry.product.server.application.context.ProductSaveContext;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductQualificationInfoDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductQualificationInfoMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 资质信息保存步骤
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
@Order(700)
public class Step700AsQualificationSave implements ProductSaveStep {
    
    @Resource
    private ProductQualificationInfoMapper productQualificationInfoMapper;
    
    @Override
    public boolean isApplicable(ProductSaveContext context) {
        // 检查是否有资质信息需要保存
        return context.getCurrentDto().getId() != null && 
               context.getCurrentDto().getQualificationInfo() != null;
    }
    
    @Override
    public void execute(ProductSaveContext context) {
        log.info("[Step700AsQualificationSave] 开始保存商品资质信息");
        
        try {
            saveOrUpdateQualificationInfo(context);
            log.info("[Step700AsQualificationSave] 商品资质信息保存完成");
            
        } catch (Exception e) {
            log.error("[Step700AsQualificationSave] 商品资质信息保存失败", e);
            context.markFailure("商品资质信息保存失败: " + e.getMessage());
        }
    }
    
    /**
     * 保存或更新资质信息
     */
    private void saveOrUpdateQualificationInfo(ProductSaveContext context) {
        var dto = context.getCurrentDto();
        ProductQualificationInfoDto qualificationInfo = dto.getQualificationInfo();
        
        if (qualificationInfo == null) {
            return;
        }
        
        // 设置商品编码
        qualificationInfo.setProductPref(dto.getPref());
        
        // 转换为DO对象
        List<ProductQualificationInfoDO> qualificationInfoDOList = 
            BeanUtil.copyToList(qualificationInfo.toInfoList(), ProductQualificationInfoDO.class);
        
        if (qualificationInfoDOList.isEmpty()) {
            return;
        }
        
        // 批量插入或更新
        productQualificationInfoMapper.insertOrUpdateOnDuplicate(qualificationInfoDOList);
        
        log.debug("[Step700AsQualificationSave] 保存资质信息 {} 条", qualificationInfoDOList.size());
    }
    
    @Override
    public String getStepDescription() {
        return "保存商品资质信息";
    }
}