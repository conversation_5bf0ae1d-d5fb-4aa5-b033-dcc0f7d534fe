package com.xyy.saas.inquiry.product.server.job.product;

import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibSyncDO;
import com.xyy.saas.inquiry.product.server.service.product.ProductStdlibSyncService;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Component;

/**
 * 商品标准库同步 Job
 */
@Slf4j
@Component
@RefreshScope // 支持 Nacos 的配置刷新
public class ProductStdlibFullSyncJob implements JobHandler {

    @Resource
    private ProductStdlibSyncService stdlibSyncService;

    @Override
    public String execute(String params) {
        log.info("[execute][商品标准库全量同步 - 开始]");
        try {
            List<ProductStdlibSyncDO> taskList = stdlibSyncService.runNotStartFullSyncTasks();
            log.info("[execute][商品标准库全量同步 - 完成] taskList:{}", taskList);
            return "执行任务进度:" + JsonUtils.toJsonPrettyString(taskList);
        } catch (Exception ex) {
            log.error("[execute][商品标准库全量同步 - 异常]", ex);
            return "执行异常:" + ex.getMessage();
        }
    }
} 