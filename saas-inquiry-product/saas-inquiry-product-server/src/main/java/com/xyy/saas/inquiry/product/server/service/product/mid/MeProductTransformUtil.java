package com.xyy.saas.inquiry.product.server.service.product.mid;

import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 商品基本信息与中台属性转换工具类
 */
public class MeProductTransformUtil {

    //处方分类转换
    public static Map<Integer, Integer> prescriptionClassificationMap = new HashMap<>();
    //特殊属性转换
    public static Map<Integer, Integer> specialAttributesMap = new HashMap<>();
    //养护类别
    public static Map<Integer, String> maintenanceType = new HashMap<>();

    static {
        prescriptionClassificationMap.put(302, 0);
        prescriptionClassificationMap.put(303, 1);
        prescriptionClassificationMap.put(304, 2);
        prescriptionClassificationMap.put(305, 3);


        specialAttributesMap.put(1,20);
        specialAttributesMap.put(2,21);
        specialAttributesMap.put(3,22);
        specialAttributesMap.put(4,23);
        specialAttributesMap.put(5,24);

        maintenanceType.put(569,"2");
        maintenanceType.put(570,"1");
    }

    public static Integer getMidPrescriptionClassification(Integer pc) {
        return prescriptionClassificationMap.getOrDefault(pc, 3);
    }



    /**
     * 生成调用中台traceId
     * @return
     */
    public static String callTraceId() {
        return "SIP_"+ UUID.randomUUID();
    }

    /**
     * 上报来源： 默认2-智鹿
     * @return
     */
    public static byte getSource() {
        return (byte) 2;
    }
}
