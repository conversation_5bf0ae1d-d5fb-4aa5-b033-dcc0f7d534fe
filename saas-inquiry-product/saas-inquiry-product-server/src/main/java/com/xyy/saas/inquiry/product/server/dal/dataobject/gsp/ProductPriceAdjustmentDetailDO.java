package com.xyy.saas.inquiry.product.server.dal.dataobject.gsp;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 售价调整单明细 DO
 *
 * <AUTHOR>
 */
@TableName("saas_product_price_adjustment_detail")
@KeySequence("saas_product_price_adjustment_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductPriceAdjustmentDetailDO extends TenantBaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 单据编号
     */
    private String recordPref;
    /**
     * 适用门店
     */
    private Long applicableTenantId;
    /**
     * 商品编码
     */
    private String productPref;
    /**
     * 商品外码
     */
    private String showPref;
    /**
     * 原零售价
     */
    private BigDecimal oldRetailPrice;
    /**
     * 原会员价
     */
    private BigDecimal oldMemberPrice;
    /**
     * 新零售价
     */
    private BigDecimal newRetailPrice;
    /**
     * 新会员价
     */
    private BigDecimal newMemberPrice;
    /**
     * 备注
     */
    private String remark;

}