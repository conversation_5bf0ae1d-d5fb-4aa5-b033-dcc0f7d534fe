package com.xyy.saas.inquiry.product.server.application.step;

import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.product.api.product.dto.ProductUseInfoDto;
import com.xyy.saas.inquiry.product.server.application.context.ProductSaveContext;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductUseInfoDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductUseInfoMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 使用信息保存步骤
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
@Order(800)
public class Step800AsUseInfoSave implements ProductSaveStep {
    
    @Resource
    private ProductUseInfoMapper productUseInfoMapper;
    
    @Override
    public boolean isApplicable(ProductSaveContext context) {
        // 检查是否有使用信息需要保存
        return context.getCurrentDto().getId() != null && 
               context.getCurrentDto().getUseInfo() != null;
    }
    
    @Override
    public void execute(ProductSaveContext context) {
        log.info("[Step800AsUseInfoSave] 开始保存商品使用信息");
        
        try {
            saveOrUpdateUseInfo(context);
            log.info("[Step800AsUseInfoSave] 商品使用信息保存完成");
            
        } catch (Exception e) {
            log.error("[Step800AsUseInfoSave] 商品使用信息保存失败", e);
            context.markFailure("商品使用信息保存失败: " + e.getMessage());
        }
    }
    
    /**
     * 保存或更新使用信息
     */
    private void saveOrUpdateUseInfo(ProductSaveContext context) {
        var dto = context.getCurrentDto();
        ProductUseInfoDto useInfo = dto.getUseInfo();
        
        if (useInfo == null) {
            return;
        }
        
        // 设置基本信息
        useInfo.setProductPref(dto.getPref());
        useInfo.setTenantId(dto.getTenantId());
        useInfo.setHeadTenantId(dto.getHeadTenantId());
        
        // 查询修改前的数据
        ProductUseInfoDO beforeUseInfo = productUseInfoMapper.selectOne(
            new LambdaQueryWrapperX<ProductUseInfoDO>()
                .eq(ProductUseInfoDO::getProductPref, dto.getPref())
                .eq(ProductUseInfoDO::getTenantId, dto.getTenantId())
        );
        
        // 转换为DO对象
        ProductUseInfoDO useInfoDO = BeanUtils.toBean(useInfo, ProductUseInfoDO.class);
        
        // 记录上一次数据（可恢复）
        useInfoDO.setRedoData(useInfoDO.compareBeforeToRedo(beforeUseInfo));
        
        if (beforeUseInfo != null) {
            useInfoDO.setId(beforeUseInfo.getId());
        }
        
        // 插入或更新
        productUseInfoMapper.insertOrUpdate(useInfoDO);
        
        log.debug("[Step800AsUseInfoSave] 保存使用信息完成, isUpdate={}", beforeUseInfo != null);
    }
    
    @Override
    public String getStepDescription() {
        return "保存商品使用信息";
    }
}