package com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

@Schema(description = "管理后台 - 质量变更申请操作记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProductQualityChangeRecordRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "17994")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "变更类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("变更类型")
    private Integer type;

    @Schema(description = "单据号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单据号")
    private String pref;

    @Schema(description = "申请人", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("申请人")
    private String applicant;

    @Schema(description = "申请时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("申请时间")
    private LocalDateTime applicationTime;

    @Schema(description = "审批状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("审批状态")
    private Integer approvalStatus;

    @Schema(description = "当前待办人", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("当前待办人")
    private String currentHandler;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}