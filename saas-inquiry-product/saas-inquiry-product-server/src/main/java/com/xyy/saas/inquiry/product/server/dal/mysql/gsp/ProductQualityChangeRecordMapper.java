package com.xyy.saas.inquiry.product.server.dal.mysql.gsp;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductQualityChangeRecordPageReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductQualityChangeRecordDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 质量变更申请操作记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductQualityChangeRecordMapper extends BaseMapperX<ProductQualityChangeRecordDO> {

    default PageResult<ProductQualityChangeRecordDO> selectPage(ProductQualityChangeRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProductQualityChangeRecordDO>()
                .eqIfPresent(ProductQualityChangeRecordDO::getType, reqVO.getType())
                .eqIfPresent(ProductQualityChangeRecordDO::getPref, reqVO.getPref())
                .eqIfPresent(ProductQualityChangeRecordDO::getApplicant, reqVO.getApplicant())
                .betweenIfPresent(ProductQualityChangeRecordDO::getApplicationTime, reqVO.getApplicationTime())
                .eqIfPresent(ProductQualityChangeRecordDO::getApprovalStatus, reqVO.getApprovalStatus())
                .eqIfPresent(ProductQualityChangeRecordDO::getCurrentHandler, reqVO.getCurrentHandler())
                .eqIfPresent(ProductQualityChangeRecordDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(ProductQualityChangeRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProductQualityChangeRecordDO::getId));
    }

}