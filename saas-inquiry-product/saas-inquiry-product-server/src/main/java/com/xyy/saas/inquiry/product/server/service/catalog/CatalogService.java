package com.xyy.saas.inquiry.product.server.service.catalog;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.pojo.catalog.CatalogRelationTenantDto;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogSaveReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.CatalogDO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 目录 Service 接口
 *
 * <AUTHOR>
 */
public interface CatalogService {

    /**
     * 创建目录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    CatalogRespVO createCatalog(@Valid CatalogSaveReqVO createReqVO);

    /**
     * 更新目录
     *
     * @param updateReqVO 更新信息
     */
    void updateCatalog(@Valid CatalogSaveReqVO updateReqVO);

    /**
     * 删除目录
     *
     * @param id 编号
     */
    void deleteCatalog(Long id);

    /**
     * 获得目录
     *
     * @param id 编号
     * @return 目录
     */
    CatalogDO getCatalog(Long id);

    /**
     * 获得目录分页
     *
     * @param pageReqVO 分页查询
     * @return 目录分页
     */
    PageResult<CatalogRespVO> getCatalogPage(CatalogPageReqVO pageReqVO);

    // ==================== 子表（监管目录明细） ====================

    /**
     * 获取版本分页数据
     *
     * @param pageReqVO
     * @return
     */
    PageResult<CatalogRespVO> getCatalogVersionPage(CatalogPageReqVO pageReqVO);

    /**
     * 获得目录集合
     *
     * @param idList 编号
     * @return 目录
     */
    List<CatalogDO> getCatalog(List<Long> idList);

    /**
     * 获得目录关联的租户
     *
     * @param pageReqVO
     * @return
     */
    PageResult<CatalogRelationTenantDto> pageRelationTenant(@Valid CatalogPageReqVO pageReqVO);
}