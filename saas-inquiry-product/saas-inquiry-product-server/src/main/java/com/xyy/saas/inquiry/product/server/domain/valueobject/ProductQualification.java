package com.xyy.saas.inquiry.product.server.domain.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.time.LocalDate;
import java.util.List;

/**
 * 商品资质信息值对象
 * 
 * <AUTHOR> Assistant
 */
@Getter
@EqualsAndHashCode
public class ProductQualification {
    
    /**
     * 资质项目列表
     */
    private final List<QualificationItem> items;
    
    private ProductQualification(List<QualificationItem> items) {
        this.items = items != null ? List.copyOf(items) : List.of();
    }
    
    /**
     * 创建空的资质信息
     */
    public static ProductQualification empty() {
        return new ProductQualification(List.of());
    }
    
    /**
     * 创建资质信息
     */
    public static ProductQualification of(List<QualificationItem> items) {
        return new ProductQualification(items);
    }
    
    /**
     * 添加资质项目
     */
    public ProductQualification addItem(QualificationItem item) {
        List<QualificationItem> newItems = new java.util.ArrayList<>(this.items);
        newItems.add(item);
        return new ProductQualification(newItems);
    }
    
    /**
     * 检查是否为空
     */
    public boolean isEmpty() {
        return items.isEmpty();
    }
    
    /**
     * 获取资质项目数量
     */
    public int size() {
        return items.size();
    }
    
    /**
     * 资质项目值对象
     */
    @Getter
    @EqualsAndHashCode
    public static class QualificationItem {
        
        /**
         * 资质类型
         */
        private final String qualificationType;
        
        /**
         * 资质编号
         */
        private final String qualificationNumber;
        
        /**
         * 资质名称
         */
        private final String qualificationName;
        
        /**
         * 有效期开始日期
         */
        private final LocalDate validFrom;
        
        /**
         * 有效期结束日期
         */
        private final LocalDate validTo;
        
        /**
         * 资质文件URL
         */
        private final String fileUrl;
        
        private QualificationItem(String qualificationType, String qualificationNumber, 
                                String qualificationName, LocalDate validFrom, 
                                LocalDate validTo, String fileUrl) {
            this.qualificationType = qualificationType;
            this.qualificationNumber = qualificationNumber;
            this.qualificationName = qualificationName;
            this.validFrom = validFrom;
            this.validTo = validTo;
            this.fileUrl = fileUrl;
        }
        
        /**
         * 创建资质项目
         */
        public static QualificationItem of(String type, String number, String name, 
                                         LocalDate validFrom, LocalDate validTo, String fileUrl) {
            return new QualificationItem(type, number, name, validFrom, validTo, fileUrl);
        }
        
        /**
         * 检查资质是否有效
         */
        public boolean isValid() {
            LocalDate now = LocalDate.now();
            return (validFrom == null || !now.isBefore(validFrom)) && 
                   (validTo == null || !now.isAfter(validTo));
        }
        
        /**
         * 检查资质是否即将过期（30天内）
         */
        public boolean isExpiringSoon() {
            if (validTo == null) {
                return false;
            }
            LocalDate thirtyDaysLater = LocalDate.now().plusDays(30);
            return !validTo.isAfter(thirtyDaysLater);
        }
    }
}