package com.xyy.saas.inquiry.product.server.mq.message.mid.dto;

import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 中台标准库数据交互事件（新品提报，匹配）
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MidStdlibInteractiveMessage implements Serializable {

    /**
     * 商品编号
     */
    private List<String> productPrefList;

    /**
     * 租户ID
     */
    // private Long tenantId;

    /**
     * 租户ID（总部）
     */
    // private Long headTenantId;

    /**
     * 类型，1：匹配，2：新品提报
     */
    private int type;

    public enum Type {
        MATCH(1, "匹配"),
        REPORT(2, "新品提报"),
        ;

        public final int code;
        public final String desc;

        Type(int code, String desc) {
            this.code = code;
            this.desc = desc;
        }

        public static Type getByCode(int code) {
            for (Type type : values()) {
                if (type.code == code) {
                    return type;
                }
            }
            return null;
        }
    }
}
