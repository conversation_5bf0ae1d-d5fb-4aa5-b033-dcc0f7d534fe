package com.xyy.saas.inquiry.product.server.dal.dataobject.product;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.xyy.saas.inquiry.product.utils.ProductUtil;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * 商品基本信息 DO
 *
 * <AUTHOR>
 */
@TableName("saas_product_info")
@KeySequence("saas_product_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductInfoDO extends TenantBaseDO {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 商品编码
     */
    private String pref;

    /**
     * 商品外码
     */
    private String showPref;

    /**
     * 源商品编码
     */
    private String sourceProductPref;

    /**
     * 标准库ID
     */
    private Long stdlibId;

    /**
     * 助记码
     */
    private String mnemonicCode;

    /**
     * 通用名
     */
    private String commonName;

    /**
     * 品牌名称
     */
    private String brandName;

    /**
     * 规格/型号
     */
    private String spec;

    /**
     * 条形码
     */
    private String barcode;

    /**
     * 生产厂家
     */
    private String manufacturer;

    /**
     * 批准文号
     */
    private String approvalNumber;

    /**
     * 单位ID
     */
    private String unit;

    /**
     * 进项税率
     */
    private BigDecimal inputTaxRate;

    /**
     * 销项税率
     */
    private BigDecimal outputTaxRate;

    /**
     * 拆零数量
     */
    private Integer unbundledQuantity;

    /**
     * 状态
     */
    private Integer status;

    /**
     * 是否禁用
     */
    private Boolean disable;

    /**
     * 多属性标志
     */
    private Long multiFlag;

    /**
     * 删除时间
     */
    private LocalDateTime deletedAt;

    /**
     * 删除类型
     */
    private Integer deleteType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 商品已删除提示前往回收站
     * @return
     */
    public String tips4Deleted() {
        return Boolean.TRUE.equals(getDeleted()) ? "（请前往商品回收站检查）" : "";
    }


    /**
     * 组装助记码、要素hash值等字段
     */
    public void calcMnemonicCode() {
        if (StringUtils.isBlank(mnemonicCode)) {
            this.mnemonicCode = ProductUtil.getMnemonicCode(this.commonName, this.brandName);
        }
    }

}