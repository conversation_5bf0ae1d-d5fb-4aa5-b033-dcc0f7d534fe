package com.xyy.saas.inquiry.product.server.dal.dataobject.gsp;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 质量变更申请操作记录 DO
 *
 * <AUTHOR>
 */
@TableName("saas_product_quality_change_record")
@KeySequence("saas_product_quality_change_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductQualityChangeRecordDO extends TenantBaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 变更类型
     */
    private Integer type;
    /**
     * 单据号
     */
    private String pref;
    /**
     * 申请人
     */
    private String applicant;
    /**
     * 申请时间
     */
    private LocalDateTime applicationTime;
    /**
     * 审批状态
     */
    private Integer approvalStatus;
    /**
     * 当前待办人
     */
    private String currentHandler;
    /**
     * 备注
     */
    private String remark;

}