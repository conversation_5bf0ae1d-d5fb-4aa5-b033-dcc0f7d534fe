package com.xyy.saas.inquiry.product.server.dal.mysql.catalog;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogPageReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.CatalogDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 目录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface CatalogMapper extends BaseMapperX<CatalogDO> {

    default PageResult<CatalogDO> selectPage(CatalogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<CatalogDO>()
            .eqIfPresent(CatalogDO::getId, reqVO.getId())
            .eqIfPresent(CatalogDO::getPref, reqVO.getPref())
            .likeIfPresent(CatalogDO::getName, reqVO.getName())
            .eqIfPresent(CatalogDO::getType, reqVO.getType())
            .eqIfPresent(CatalogDO::getProjectCodeType, reqVO.getProjectCodeType())
            .eqIfPresent(CatalogDO::getVersion, reqVO.getVersion())
            .eqIfPresent(CatalogDO::getVersionCode, reqVO.getVersionCode())
            .eqIfPresent(CatalogDO::getUploadUrl, reqVO.getUploadUrl())
            .eqIfPresent(CatalogDO::getNeedDownload, reqVO.getNeedDownload())
            .eqIfPresent(CatalogDO::getDownloadUrl, reqVO.getDownloadUrl())
            .eqIfPresent(CatalogDO::getTotalCount, reqVO.getTotalCount())
            .eqIfPresent(CatalogDO::getMatchedCount, reqVO.getMatchedCount())
            .eqIfPresent(CatalogDO::getUnmatchedCount, reqVO.getUnmatchedCount())
            .eqIfPresent(CatalogDO::getDisable, reqVO.getDisable())
            .eqIfPresent(CatalogDO::getEnv, reqVO.getEnv())
            .eqIfPresent(CatalogDO::getRemark, reqVO.getRemark())
            .inIfPresent(CatalogDO::getCreator, reqVO.getCreatorList())
            .betweenIfPresent(CatalogDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(CatalogDO::getId));
    }

    default CatalogDO selectMaxByPrefLikeRight(String pref) {
        return selectOne(new LambdaQueryWrapperX<CatalogDO>()
            .eq(CatalogDO::getDeleted, false)
            .likeRight(CatalogDO::getPref, pref)
            .orderByDesc(CatalogDO::getPref), false);
    }

    default CatalogDO selectMaxVersionByPref(String pref) {
        return selectOne(new LambdaQueryWrapperX<CatalogDO>()
            .eq(CatalogDO::getDeleted, false)
            .likeRight(CatalogDO::getPref, pref)
            .orderByDesc(CatalogDO::getVersion), false);
    }

    default List<CatalogDO> selectByPref(String pref) {
        return selectList(new LambdaQueryWrapperX<CatalogDO>()
            .eq(CatalogDO::getDeleted, false)
            .eq(CatalogDO::getPref, pref));
    }

    IPage<CatalogDO> getCatalogPage(Page<Object> objectPage, CatalogPageReqVO pageReqVO);

    void accumulationCatalogTotalCount(@Param("catalogId") Long catalogId, @Param("count") Integer count);

    /**
     * 根据目录ID获取目录类型
     *
     * @param catalogId 目录ID
     * @return 目录类型
     */
    default Integer selectCatalogTypeById(Long catalogId) {
        CatalogDO catalog = selectById(catalogId);
        return catalog != null ? catalog.getType() : null;
    }

}