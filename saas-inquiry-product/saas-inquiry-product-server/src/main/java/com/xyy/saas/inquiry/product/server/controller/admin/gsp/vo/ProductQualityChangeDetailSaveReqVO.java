package com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 质量变更申请明细记录新增/修改 Request VO")
@Data
public class ProductQualityChangeDetailSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "20566")
    private Long id;

    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "6253")
    private String itemPref;

    @Schema(description = "变更内容（JSON格式）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "变更内容（JSON格式）不能为空")
    private String content;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    private String remark;

}