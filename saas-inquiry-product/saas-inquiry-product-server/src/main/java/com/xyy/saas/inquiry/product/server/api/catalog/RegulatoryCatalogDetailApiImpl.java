package com.xyy.saas.inquiry.product.server.api.catalog;

import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.system.api.tenant.TenantServicePackRelationApi;
import com.xyy.saas.inquiry.enums.transmitter.OrganTypeEnum;
import com.xyy.saas.inquiry.product.api.catalog.RegulatoryCatalogDetailApi;
import com.xyy.saas.inquiry.product.api.catalog.dto.RegulatoryCatalogDetailDTO;
import com.xyy.saas.inquiry.product.server.convert.catalog.RegulatoryCatalogDetailConvert;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.RegulatoryCatalogDetailDO;
import com.xyy.saas.inquiry.product.server.service.catalog.RegulatoryCatalogDetailService;
import jakarta.annotation.Resource;
import java.util.List;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * 目录详情
 */
@DubboService
public class RegulatoryCatalogDetailApiImpl implements RegulatoryCatalogDetailApi {

    @Resource
    private RegulatoryCatalogDetailService regulatoryCatalogDetailService;

    @DubboReference
    private TenantServicePackRelationApi tenantServicePackRelationApi;

    @Override
    public List<RegulatoryCatalogDetailDTO> getCatalogDetailByProjectCodes(Long tenantId, List<Long> projectCodes) {

        Long catalogId = TenantUtils.execute(tenantId, this::getInternetSuperVisionCatalogId);

        List<RegulatoryCatalogDetailDO> regulatoryCatalogDetailDOS = regulatoryCatalogDetailService.getRegulatoryCatalogDetailListByProjectCodes(catalogId, projectCodes);

        return RegulatoryCatalogDetailConvert.INSTANCE.convertDto(regulatoryCatalogDetailDOS);
    }

    /**
     * 查询当前TenantId门店 互联网监管目录id 用于搜索过滤
     *
     * @return
     */
    private Long getInternetSuperVisionCatalogId() {
        return tenantServicePackRelationApi.getTenantOrganCatalogId(OrganTypeEnum.INTERNET_SUPERVISION);
    }

}
