package com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 目录药品详情 Request VO")
@Data
@ToString(callSuper = true)
public class RegulatoryCatalogDetailRespVO {

    @Schema(description = "主键ID")
    private Long id;

    @Schema(description = "目录ID")
    private Long catalogId;

    @Schema(description = "项目编码（标准库ID）")
    private Long projectCode;

    @Schema(description = "通用名")
    private String commonName;

    @Schema(description = "品牌名称")
    private String brandName;

    @Schema(description = "规格/型号")
    private String spec;

    @Schema(description = "条形码")
    private String barcode;

    @Schema(description = "生产厂家")
    private String manufacturer;

    @Schema(description = "批准文号")
    private String approvalNumber;

    @Schema(description = "是否禁用，默认否")
    private Boolean disable;

    @Schema(description = "备注")
    private String remark;

}
