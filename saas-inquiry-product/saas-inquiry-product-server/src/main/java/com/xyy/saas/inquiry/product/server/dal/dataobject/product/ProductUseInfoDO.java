package com.xyy.saas.inquiry.product.server.dal.dataobject.product;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.Objects;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.apache.commons.lang3.StringUtils;

/**
 * 商品使用信息 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_product_use_info", autoResultMap = true)
@KeySequence("saas_product_use_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductUseInfoDO extends TenantBaseDO {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 商品编号
     */
    private String productPref;
    /**
     * 租户编号（总部）
     */
    private Long headTenantId;
    /**
     * 零售价
     */
    private BigDecimal retailPrice;
    /**
     * 会员价
     */
    private BigDecimal memberPrice;
    /**
     * 医保项目编码
     */
    private String medicareProjectCode;
    /**
     * 医保项目名称
     */
    private String medicareProjectName;
    /**
     * 医保项目等级
     */
    private Integer medicareProjectLevel;
    /**
     * 医保匹配状态
     */
    private Integer medicareMatchStatus;
    /**
     * 匹配关系上传状态
     */
    private Integer medicareUploadStatus;
    /**
     * 记录上一次数据（可恢复）
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private ProductUseInfoDO redoData;
    /**
     * 备注
     */
    private String remark;

    /**
     * 比较当前数据与上一次数据，记录需要恢复的数据
     * @param before
     * @return
     */
    public ProductUseInfoDO compareBeforeToRedo(ProductUseInfoDO before) {
        // 医保项目编码变更了，需要记录
        if (before != null && medicareProjectCode != null
            && !Objects.equals(before.getMedicareProjectCode(), medicareProjectCode)
            && StringUtils.isNotBlank(before.getMedicareProjectCode()) ) {
            // 构建 redo信息
            return ProductUseInfoDO.builder()
                .medicareProjectCode(before.getMedicareProjectCode())
                .medicareProjectName(before.getMedicareProjectName())
                .medicareProjectLevel(before.getMedicareProjectLevel())
                .medicareMatchStatus(before.getMedicareMatchStatus())
                .medicareUploadStatus(before.getMedicareUploadStatus())
                .build();
        }
        return new ProductUseInfoDO();
    }

}