package com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;
import lombok.ToString;

@Schema(description = "管理后台 - 审批流关联业务取消 Request VO")
@Data
@ToString(callSuper = true)
public class BpmBusinessRelationCancelReqVO {

    @Schema(description = "租户编号", example = "1")
    @NotNull(message = "租户编号不能为空")
    private Long tenantId;

    @Schema(description = "业务类型", example = "1")
    // @NotNull(message = "业务类型不能为空")
    private Integer businessType;

    @Schema(description = "业务单据编号", example = "22519")
    @NotEmpty(message = "业务单据编号不能为空")
    private List<String> businessPrefList;

    @Schema(description = "取消原因", example = "你猜")
    private String reason;

}