package com.xyy.saas.inquiry.product.server.config.forward.dto;

import lombok.Data;

import java.io.Serial;
import java.io.Serializable;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class MidResponse3<T> implements Serializable {
    @Serial
    private static final long serialVersionUID = -4576531610490315532L;
    private static final int SUCCESS_CODE = 0;
    private Integer retCode;
    private String retMsg;
    private T data;
    private Integer dataSize;

    public boolean isSuccess() {
        return this.retCode == 0;
    }

    public boolean isFailure() {
        return !this.isSuccess();
    }
}
