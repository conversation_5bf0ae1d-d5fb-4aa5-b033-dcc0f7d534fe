package com.xyy.saas.inquiry.product.server.controller.admin.transfer.vo;

import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.api.transfer.dto.ProductTransferRecordExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 商品流转记录新增/修改 Request VO")
@Data
public class ProductTransferRecordSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15559")
    private Long id;

    @Schema(description = "商品编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "9472")
    @NotNull(message = "商品编号不能为空")
    private String productPref;

    @Schema(description = "同步类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "同步类型不能为空")
    private Integer type;

    @Schema(description = "源租户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "24064")
    @NotNull(message = "源租户编号不能为空")
    private Long sourceTenantId;

    @Schema(description = "目标租户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "14352")
    @NotNull(message = "目标租户编号不能为空")
    private Long targetTenantId;

    /**
     * 商品外码
     */
    @Schema(description = "商品外码", requiredMode = RequiredMode.REQUIRED, example = "SP112")
    @NotEmpty(message = "商品外码不能为空")
    private String showPref;

    /**
     * 助记码
     */
    @Schema(description = "助记码", requiredMode = RequiredMode.REQUIRED, example = "xxx")
    @NotEmpty(message = "助记码不能为空")
    private String mnemonicCode;

    /**
     * 通用名
     */
    @Schema(description = "通用名", requiredMode = RequiredMode.REQUIRED, example = "xxx")
    @NotEmpty(message = "通用名不能为空")
    private String commonName;

    /**
     * 品牌名称
     */
    @Schema(description = "品牌名称", requiredMode = RequiredMode.REQUIRED, example = "xxx")
    // @NotEmpty(message = "品牌名称不能为空")
    private String brandName;

    /**
     * 规格/型号
     */
    @Schema(description = "规格/型号", requiredMode = RequiredMode.REQUIRED, example = "xxx")
    @NotEmpty(message = "规格/型号不能为空")
    private String spec;

    /**
     * 条形码
     */
    @Schema(description = "条形码", requiredMode = RequiredMode.REQUIRED, example = "xxx")
    @NotEmpty(message = "条形码不能为空")
    private String barcode;

    /**
     * 生产厂家
     */
    @Schema(description = "生产厂家", requiredMode = RequiredMode.REQUIRED, example = "xxx")
    @NotEmpty(message = "生产厂家不能为空")
    private String manufacturer;

    /**
     * 批准文号
     */
    @Schema(description = "批准文号", requiredMode = RequiredMode.REQUIRED, example = "xxx")
    @NotEmpty(message = "批准文号不能为空")
    private String approvalNumber;

    /**
     * 扩展信息
     */
    @Schema(description = "扩展信息", requiredMode = RequiredMode.REQUIRED, example = "xxx")
    private ProductTransferRecordExtDto ext;

    @Schema(description = "同步状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "同步状态不能为空")
    private Integer status;

    @Schema(description = "同步结果", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "同步结果不能为空")
    private String result;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    private String remark;

    /**
     * 组装商品信息
     * @param dto
     * @return
     */
    public ProductTransferRecordSaveReqVO of(ProductInfoDto dto) {
        return this.setProductPref(dto.getPref())
            .setShowPref(dto.getShowPref())
            .setMnemonicCode(dto.getMnemonicCode())
            .setCommonName(dto.getCommonName())
            .setBrandName(dto.getBrandName())
            .setSpec(dto.getSpec())
            .setBarcode(dto.getBarcode())
            .setManufacturer(dto.getManufacturer())
            .setApprovalNumber(dto.getApprovalNumber())
            .setExt(new ProductTransferRecordExtDto()
                .setUnit(dto.getUnit())
                .setCoverImages(dto.getCoverImages())
                .setOuterPackageImages(dto.getOuterPackageImages())
                .setInstructionImages(dto.getInstructionImages())
            );
    }
}