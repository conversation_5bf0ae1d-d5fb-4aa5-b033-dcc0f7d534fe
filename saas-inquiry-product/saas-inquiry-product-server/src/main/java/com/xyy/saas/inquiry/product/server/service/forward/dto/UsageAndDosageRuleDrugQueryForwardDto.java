package com.xyy.saas.inquiry.product.server.service.forward.dto;

import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 用法用量规则药品查询对象
 *
 * <AUTHOR>
 * @Date 9/9/24 4:20 PM
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
public class UsageAndDosageRuleDrugQueryForwardDto {

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 药品和规格集合
     */
    private List<UsageAndDosageRuleDrugDetailQueryDto> usageAndDosageRuleDrugDetailQueryDtoList;

    @Data
    @Builder
    public static class UsageAndDosageRuleDrugDetailQueryDto implements Serializable {

        /**
         * 药品名称
         */
        private String commonName;

        /**
         * 规格
         */
        private String specification;

    }
}
