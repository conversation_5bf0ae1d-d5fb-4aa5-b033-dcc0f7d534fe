package com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import lombok.Data;

@Schema(description = "管理后台 - 售价调整单明细新增/修改 Request VO")
@Data
public class ProductPriceAdjustmentDetailSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13165")
    private Long id;

    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "22067")
    @NotNull(message = "商品编码不能为空")
    private String productPref;

    @Schema(description = "原零售价", requiredMode = Schema.RequiredMode.REQUIRED, example = "14358")
    @NotNull(message = "原零售价不能为空")
    private BigDecimal oldRetailPrice;

    @Schema(description = "原会员价", requiredMode = Schema.RequiredMode.REQUIRED, example = "27197")
    @NotNull(message = "原会员价不能为空")
    private BigDecimal oldMemberPrice;

    @Schema(description = "新零售价", requiredMode = Schema.RequiredMode.REQUIRED, example = "3727")
    @NotNull(message = "新零售价不能为空")
    private BigDecimal newRetailPrice;

    @Schema(description = "新会员价", requiredMode = Schema.RequiredMode.REQUIRED, example = "31530")
    @NotNull(message = "新会员价不能为空")
    private BigDecimal newMemberPrice;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    @NotEmpty(message = "备注不能为空")
    private String remark;

}