package com.xyy.saas.inquiry.product.server.domain.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 租户ID值对象
 * 
 * <AUTHOR> Assistant
 */
@Getter
@EqualsAndHashCode
public class TenantId {
    
    private final Long value;
    
    private TenantId(Long value) {
        this.value = value;
    }
    
    /**
     * 创建租户ID
     */
    public static TenantId of(Long tenantId) {
        if (tenantId == null) {
            throw new IllegalArgumentException("TenantId cannot be null");
        }
        return new TenantId(tenantId);
    }
    
    /**
     * 检查是否为连锁总部租户
     */
    public boolean isHeadTenant(TenantId headTenantId) {
        return this.equals(headTenantId);
    }
    
    @Override
    public String toString() {
        return String.format("TenantId{value=%d}", value);
    }
}