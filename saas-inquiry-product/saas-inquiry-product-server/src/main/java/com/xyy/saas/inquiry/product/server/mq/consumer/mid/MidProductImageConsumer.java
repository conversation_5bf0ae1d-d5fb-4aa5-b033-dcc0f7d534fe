package com.xyy.saas.inquiry.product.server.mq.consumer.mid;

import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.product.server.config.mq.annotation.ExternalMQConsumer;
import com.xyy.saas.inquiry.product.server.config.mq.enums.ExternalMQClusterEnum;
import com.xyy.saas.inquiry.product.server.config.mq.handler.ExternalMessageHandler;
import com.xyy.saas.inquiry.product.server.mq.message.mid.dto.MidProductImageMsg;
import com.xyy.saas.inquiry.product.server.service.product.ProductStdlibService;
import jakarta.annotation.Resource;
import java.nio.charset.StandardCharsets;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 中台商品图片消息：
 */
@Slf4j
@Component
@ExternalMQConsumer(
    topic = "XYY_ME_PRODUCT_PICTURE_MECHANISM_ES_TOPIC_YKQ",
    consumerGroup = "com_xyy_saas_inquiry_product_server_mq_consumer_mid_MidProductImageConsumer",
    cluster = ExternalMQClusterEnum.MIDDLE
)
public class MidProductImageConsumer implements ExternalMessageHandler {

    @Resource
    private ProductStdlibService productStdlibService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public ConsumeConcurrentlyStatus handleMessage(List<MessageExt> msgList) throws Exception {
        if (CollectionUtils.isEmpty(msgList)) {
            return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
        }

        for (MessageExt msg : msgList) {
            String body = new String(msg.getBody(), StandardCharsets.UTF_8);
            log.info("【中台商品图片】收到消息, msgId: {}, body: {}", msg.getMsgId(), body);

            try {
                // 1. 解析消息
                MidProductImageMsg imageMsg = JSON.parseObject(body, MidProductImageMsg.class);
                if (imageMsg == null) {
                    log.error("【中台商品图片】解析消息失败, msgId: {}, body: {}", msg.getMsgId(), body);
                    continue;
                }

                // 2. 校验消息
                long productId = NumberUtils.toLong(imageMsg.getProductId(), -1);
                if (productId < 0) {
                    log.error("【中台商品图片】不支持的数据类型, msgId: {}, midStdlibId：{}", msg.getMsgId(), imageMsg.getProductId());
                    continue;
                }

                // 3. 更新图片
                productStdlibService.updateStdlibImageFromMid(List.of(productId));
                log.info("【中台商品图片】处理消息成功, msgId: {}, body: {}", msg.getMsgId(), body);
            } catch (Exception e) {
                log.error("【中台商品图片】处理消息异常, msgId: {}, body: {}, error: {}",
                    msg.getMsgId(), body, e.getMessage(), e);
                throw e; // 抛出异常触发重试
            }
        }
        return ConsumeConcurrentlyStatus.CONSUME_SUCCESS;
    }
} 