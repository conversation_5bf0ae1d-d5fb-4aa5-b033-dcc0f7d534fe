package com.xyy.saas.inquiry.product.server.controller.admin.product.vo;

import com.xyy.saas.inquiry.constant.ValidateGroup.Add;
import com.xyy.saas.inquiry.constant.ValidateGroup.Update;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 商品拆零规则创建 Request VO")
@Data
public class ProductUnbundledSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15559")
    @NotNull(message = "ID不能为空", groups = Update.class)
    private Long id;

    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "SKU001")
    private String productPref;

    @Schema(description = "源商品编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "SKU001")
    @NotEmpty(message = "源商品编码不能为空", groups = Add.class)
    private String sourceProductPref;

    @Schema(description = "拆零数量", requiredMode = Schema.RequiredMode.REQUIRED, example = "10")
    @NotNull(message = "拆零数量不能为空", groups = Add.class)
    private Integer unbundledQuantity;

    @Schema(description = "拆零规格", requiredMode = Schema.RequiredMode.REQUIRED, example = "10片/盒")
    @NotEmpty(message = "拆零规格不能为空", groups = Add.class)
    private String unbundledSpec;

    @Schema(description = "拆零单位", requiredMode = Schema.RequiredMode.REQUIRED, example = "盒")
    @NotNull(message = "拆零单位不能为空", groups = Add.class)
    private String unbundledUnit;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Boolean disable;

    @Schema(description = "备注")
    private String remark;
} 