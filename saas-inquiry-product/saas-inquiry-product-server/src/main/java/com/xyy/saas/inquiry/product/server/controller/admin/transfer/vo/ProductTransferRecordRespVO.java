package com.xyy.saas.inquiry.product.server.controller.admin.transfer.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

@Schema(description = "管理后台 - 商品流转记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ProductTransferRecordRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15559")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "商品编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "9472")
    @ExcelProperty("商品编号")
    private String productPref;

    @Schema(description = "同步类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("同步类型")
    private Integer type;

    @Schema(description = "源租户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "24064")
    @ExcelProperty("源租户编号")
    private Long sourceTenantId;

    @Schema(description = "目标租户编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "14352")
    @ExcelProperty("目标租户编号")
    private Long targetTenantId;

    @Schema(description = "同步状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("同步状态")
    private Integer status;

    @Schema(description = "同步结果", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("同步结果")
    private String result;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}