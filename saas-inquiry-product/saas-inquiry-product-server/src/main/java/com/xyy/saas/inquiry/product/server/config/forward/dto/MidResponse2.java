package com.xyy.saas.inquiry.product.server.config.forward.dto;

import java.io.Serial;
import java.io.Serializable;
import lombok.Data;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class MidResponse2<T> implements Serializable {
    @Serial
    private static final long serialVersionUID = 4349949604100685624L;

    private static final int SUCCESS_CODE = 0;
    private Integer retCode;
    private String retMsg;
    private T data;

    public boolean isSuccess() {
        return this.retCode == 0;
    }

    public boolean isFailure() {
        return !this.isSuccess();
    }
}
