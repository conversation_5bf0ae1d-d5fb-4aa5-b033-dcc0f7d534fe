package com.xyy.saas.inquiry.product.server.controller.app.product.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "APP后台 - 商品提报分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ProductPresentPageReqVO extends PageParam {

    @Schema(description = "ID列表", example = "9472")
    private List<Long> idList;

    @Schema(description = "商品编号", example = "9472")
    private List<String> productPrefList;

    @Schema(description = "商品信息", example = "9472")
    private String mixedQuery;

    @Schema(description = "条形码", example = "9472")
    private String barcode;
    @Schema(description = "通用名", example = "xxx")
    private String commonName;
    @Schema(description = "品牌名称", example = "xxx")
    private String brandName;
    @Schema(description = "规格/型号", example = "xxx")
    private String spec;
    @Schema(description = "生产厂家", example = "xxx")
    private String manufacturer;
    @Schema(description = "批准文号", example = "xxx")
    private String approvalNumber;

    @Schema(description = "源租户编号", example = "24064")
    private Long sourceTenantId;

    @Schema(description = "省编码")
    private String provinceCode;
    @Schema(description = "市编码")
    private String cityCode;
    @Schema(description = "区编码")
    private String areaCode;

    @Schema(description = "目标租户编号", example = "14352")
    private Long targetTenantId;

    @Schema(description = "审核状态(0-待审核 1-审核通过 2-审核驳回)", example = "1")
    private Integer approveStatus;
    @Schema(description = "状态列表", example = "[0]")
    private List<Integer> statusList;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

} 