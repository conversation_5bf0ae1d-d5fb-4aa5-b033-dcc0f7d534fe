package com.xyy.saas.inquiry.product.server.dal.mysql.product;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductUseInfoDO;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.ibatis.annotations.Mapper;

/**
 * 商品使用信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductUseInfoMapper extends BaseMapperX<ProductUseInfoDO> {

    /**
     * 根据租户和商品编号查询商品使用信息
     * @param tenantIds  如果传
     * @param productPrefList
     * @return
     */
    default List<ProductUseInfoDO> selectList(List<Long> tenantIds, List<String> productPrefList) {
        if (CollectionUtils.isEmpty(productPrefList)) {
            return List.of();
        }
        return selectList(new LambdaQueryWrapperX<ProductUseInfoDO>()
            .in(ProductUseInfoDO::getProductPref, productPrefList.stream().distinct().toList())
            .inIfPresent(ProductUseInfoDO::getTenantId, tenantIds)
            .orderByDesc(ProductUseInfoDO::getCreateTime));
    }
    /**
     * 根据租户和商品编号查询商品使用信息, 如果门店有数据则使用门店的，如果没有则使用总部的
     * @param tenantId
     * @param headTenantId
     * @param productPrefList
     * @return
     */
    default Map<String, ProductUseInfoDO> selectMapList(List<String> productPrefList, Long tenantId, Long headTenantId) {
        if (CollectionUtils.isEmpty(productPrefList) || tenantId == null || headTenantId == null) {
            return Map.of();
        }
        boolean sameTenant = tenantId.equals(headTenantId);
        List<ProductUseInfoDO> list = selectList(new LambdaQueryWrapperX<ProductUseInfoDO>()
            .in(ProductUseInfoDO::getProductPref, productPrefList.stream().distinct().toList())
            .inIfPresent(ProductUseInfoDO::getTenantId, sameTenant ? List.of(tenantId) : List.of(tenantId, headTenantId))
            .orderByDesc(ProductUseInfoDO::getCreateTime));

        return list.stream().collect(Collectors.toMap(ProductUseInfoDO::getProductPref, Function.identity(), (a, b) -> {
            if (Objects.equals(a.getTenantId(), tenantId)) {
                return a;
            } else {
                return b;
            }
        }));
    }

    default void deleteByProductPref(String productPref) {
        delete(ProductUseInfoDO::getProductPref, productPref);
    }


    /**
     * 根据商品pref列表物理删除数据
     * @param prefList
     * @return
     */
    int hardDeleteByProductPrefList(List<String> prefList);

}