package com.xyy.saas.inquiry.product.server.dal.mysql.gsp;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.inquiry.product.server.controller.admin.gsp.vo.ProductPriceAdjustmentRecordPageReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.gsp.ProductPriceAdjustmentRecordDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 售价调整单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductPriceAdjustmentRecordMapper extends BaseMapperX<ProductPriceAdjustmentRecordDO> {

    default PageResult<ProductPriceAdjustmentRecordDO> selectPage(ProductPriceAdjustmentRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<ProductPriceAdjustmentRecordDO>()
                .eqIfPresent(ProductPriceAdjustmentRecordDO::getPref, reqVO.getPref())
                .eqIfPresent(ProductPriceAdjustmentRecordDO::getApplicableTenantIds, reqVO.getApplicableTenantIdList())
                .eqIfPresent(ProductPriceAdjustmentRecordDO::getApprovalStatus, reqVO.getApprovalStatus())
                .eqIfPresent(ProductPriceAdjustmentRecordDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(ProductPriceAdjustmentRecordDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(ProductPriceAdjustmentRecordDO::getId));
    }

}