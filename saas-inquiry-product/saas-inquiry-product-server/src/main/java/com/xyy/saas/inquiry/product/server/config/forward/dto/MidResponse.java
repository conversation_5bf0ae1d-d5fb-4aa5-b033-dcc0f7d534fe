package com.xyy.saas.inquiry.product.server.config.forward.dto;

import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
import java.util.LinkedList;
import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class MidResponse<T> implements Serializable {
    @Serial
    private static final long serialVersionUID = -7201361362599600199L;

    private String msg = "OK";
    private long count = 0L;
    private int status = 0;
    private List<T> data = new LinkedList<>();

}
