package com.xyy.saas.inquiry.product.server.config.forward.dto;

import com.alibaba.nacos.common.utils.StringUtils;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.server.service.product.mid.MeProductTransformUtil;
import lombok.Data;
import org.apache.commons.lang3.math.NumberUtils;
import java.io.Serial;
import java.io.Serializable;
import java.util.Date;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class MidGeneralProductPresentInfoVo implements Serializable {

    @Serial
    private static final long serialVersionUID = 7132180173086574747L;

    /**
     * 唯一标识
     */
    private String traceId;

    /**
     * 	外部商品编码
     */
    private String outsideCode;

    /**
     * 来源 1:荷叶健康 2:saas智鹿
     */
    private Byte source;

    /**
     * 上报人ID
     */
    private String createUserId;
    /**
     * 上报人
     */
    private String createUser;
    /**
     * 上报人机构ID
     */
    private String createInstitutionId;
    /**
     * 上报人机构名称
     */
    private String createInstitutionName;

    /**
     * 商品分类(1:普通药品, 2:中药饮片, 3:医疗器械, 4:非药品, 5:赠品)
     * @convert
     */
    private Integer spuCategory;
    /**
     * 通用名
     */
    private String generalName;
    /**
     * 	通用名助记码
     */
    private String generalNamecode;
    /**
     * 商品名称
     */
    private String productName;
    /**
     * 商品名称助记码
     */
    private String productNameCode;
    /**
     * 所属经营范围
     */
    private String businessScopeMulti;
    /**
     * 中包装条码
     */
    private String mediumPackageCode;
    /**
     * 件包装条码
     */
    private String piecePackageCode;

    /**
     * 上市许可人
     */
    private String marketAuthor;

    /**
     * 是否监管
     */
    private Byte whetherSupervision;

    /**
     * 存储属性
     */
    private Byte shadingAttr;
    /**
     * 进项税率
     */
    private String inRate;
    /**
     * 出项税率
     */
    private String outRate;

    /**
     * 规格/型号
     */
    private String spec;

    /**
     * 批准文号/注册证号
     */
    private String approvalNo;

    /**
     * 生产厂家
     * @convert
     */
    private String manufacturerName;

    /**
     * 小包装条码
     */
    private String smallPackageCode;

    /**
     * 产地
     */
    private String originPlace;

    /**
     * 剂型文本
     * @convert
     */
    private String dosageFormName;
    /**
     * 存储条件
     * @convert
     */
    private Integer storageCond;

    /**
     * 药品本位码
     */
    private String standardCodes;

    /**
     * 包装单位文本
     * @convert
     */
    private String packageUnitName;

    /**
     * 处方分类
     * @convert
     */
    private Integer prescriptionCategory;


    /**
     * 品牌/商标
     */
    private String brand;

    /**
     * 有效期
     */
    private Short validity;

    /**
     * 质量标准
     */
    private String qualityStandard;

    /**
     * 是否需要精修（1 是 0 否）
     */
    private Integer needTruing;

    /**
     * 是否委托生产(0:否, 1:是)
     */
    private Byte delegationProduct;

    /**
     * 受委托生产厂家名称
     */
    private String entrustedManufacturerName;

    /**
     * 标准库id
     */
    private String productId;
    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 货主/渠道编号
     */
    private String channelId;

    /**
     * 货主/渠道名称
     */
    private String channelName;

    /**
     * 中包装数量
     */
    private String mediumPackageNumberMin;

    /**
     * 件包装数量
     */
    private String piecePackageNumberMin;

    /**
     * 限销状态
     */
    private Byte limitedPinState;

    /**
     * 限采状态
     */
    private Byte baseLimitedProductionState;

    /**
     * 停用状态
     */
    private Byte baseDisableState;

    /**
     * 特殊属性
     */
    private String specialAttributes;

    /**
     * 是否可拆零
     */
    private Byte scatteredYn;

    /**
     * 养护类别
     */
    private String keyConservationCategories;

    /**
     * 养护周期
     */
    private Integer maintenancePeriod;

    /**
     * 首营时间
     */
    private Date applicationTime;

    /**
     * 批件名称
     */
    private String batchnameVal;

    /**
     * 批件编号
     */
    private String batchCode;

    /**
     * 核准内容
     */
    private String approvedContent;

    /**
     * 签发日期
     */
    private Date issueDate;

    /**
     * 有效期至
     */
    private Date validityDate;

    /**
     * 签发机关
     */
    private String signingIssuingOrganization;

    /**
     * 附件
     */
    private String annex;

    public static MidGeneralProductPresentInfoVo of(ProductInfoDto productBaseInfoPo){
        MidGeneralProductPresentInfoVo generalProductPresentInfoVo = new MidGeneralProductPresentInfoVo();
        //基本属性
        //商品内码
        generalProductPresentInfoVo.setOutsideCode(productBaseInfoPo.getPref());
        //上报来源
        generalProductPresentInfoVo.setSource(MeProductTransformUtil.getSource());
        //提交人姓名
        generalProductPresentInfoVo.setCreateUser(productBaseInfoPo.getCreatorName());
        //商品大类
        // generalProductPresentInfoVo.setSpuCategory(spuCategoryMap.get(productBaseInfoPo.getSpuCategory()));
        //通用名
        generalProductPresentInfoVo.setGeneralName(productBaseInfoPo.getCommonName());
        //通用名助记码
        String[] mnemonicCodeArray = productBaseInfoPo.getMnemonicCode().split("\\|");
        String commonCode = mnemonicCodeArray[0];
        generalProductPresentInfoVo.setGeneralNamecode(commonCode);
        //商品名称
        generalProductPresentInfoVo.setProductName(productBaseInfoPo.getBrandName());
        //商品名称助记码
        String productNameCode = mnemonicCodeArray[1]==null?"": mnemonicCodeArray[1];
        generalProductPresentInfoVo.setProductNameCode(productNameCode);
        //所属经营范围
//        Map<String, String> businessScopeMap = dictDataApi.getDictDataList(productBaseInfoPo.getTenantId(), DictTypeConstants.PRODUCT_BUSINESS_SCOPE)
//            .stream().collect(Collectors.toMap(DictDataRespDTO::getValue, DictDataRespDTO::getLabel, (v1, v2) -> v2));
//        generalProductPresentInfoVo.setBusinessScopeMulti(businessScopeMap.get("" + productBaseInfoPo.getBusinessScopeId()));
        generalProductPresentInfoVo.setBusinessScopeMulti(productBaseInfoPo.getBusinessScope());

        //中包装条码
        // generalProductPresentInfoVo.setMediumPackageCode(productBaseInfoPo.getMiddleUnitBarCode());
        //件(大)包装条码
        // generalProductPresentInfoVo.setPiecePackageCode(productBaseInfoPo.getBigUnitBarCode());
        //上市许可人
        generalProductPresentInfoVo.setMarketAuthor(productBaseInfoPo.getMarketingAuthorityHolder());
        //是否监管
        // generalProductPresentInfoVo.setWhetherSupervision(productBaseInfoPo.getProductFlag().getHasSpecialDrugCompound());
        //存储属性
        // generalProductPresentInfoVo.setShadingAttr(productBaseInfoPo.getShadingAttr());
        //进项税率
        generalProductPresentInfoVo.setInRate(productBaseInfoPo.getInputTaxRate().stripTrailingZeros().toPlainString()+"%");
        //出项税率
        generalProductPresentInfoVo.setOutRate(productBaseInfoPo.getOutputTaxRate().stripTrailingZeros().toPlainString()+"%");
        //规格/型号
        generalProductPresentInfoVo.setSpec(productBaseInfoPo.getSpec());
        //批准文号
        generalProductPresentInfoVo.setApprovalNo(productBaseInfoPo.getApprovalNumber());
        //生产厂家
        generalProductPresentInfoVo.setManufacturerName(productBaseInfoPo.getManufacturer());
        //小包装条码
        generalProductPresentInfoVo.setSmallPackageCode(StringUtils.defaultIfEmpty(productBaseInfoPo.getBarcode(), "0"));
        //产地
        generalProductPresentInfoVo.setOriginPlace(productBaseInfoPo.getOrigin());
        //剂型
//        Map<String, String> dosageFormMap = dictDataApi.getDictDataList(productBaseInfoPo.getTenantId(), DictTypeConstants.PRODUCT_DOSAGE_FORM)
//            .stream().collect(Collectors.toMap(DictDataRespDTO::getValue, DictDataRespDTO::getLabel, (v1, v2) -> v2));
//        generalProductPresentInfoVo.setDosageFormName(dosageFormMap.getOrDefault("" + productBaseInfoPo.getDosageFormId(), ""));
        generalProductPresentInfoVo.setDosageFormName(productBaseInfoPo.getDosageForm());
        // todo 存储条件转换
        //        generalProductPresentInfoVo.setStorageCond(productBaseInfoPo.getStorageCondition());
        //本位码
        // generalProductPresentInfoVo.setStandardCodes(productBaseInfoPo.getStandardCode());
        //包装单位
//        Map<String, String> unitMap = dictDataApi.getDictDataList(productBaseInfoPo.getTenantId(), DictTypeConstants.PRODUCT_UNIT)
//            .stream().collect(Collectors.toMap(DictDataRespDTO::getValue, DictDataRespDTO::getLabel, (v1, v2) -> v2));
//        generalProductPresentInfoVo.setPackageUnitName(unitMap.getOrDefault("" + productBaseInfoPo.getUnitId(), ""));
        generalProductPresentInfoVo.setPackageUnitName(productBaseInfoPo.getUnit());
        //处方分类
        // generalProductPresentInfoVo.setPrescriptionCategory(MeProductTransformUtil.getMidPrescriptionClassification(productBaseInfoPo.getPresCategoryId()));
        //有效期不为空，且非数字
        if(StringUtils.isNotEmpty(productBaseInfoPo.getProductValidity()) && NumberUtils.toShort(productBaseInfoPo.getProductValidity()) <= 0){
            throw new RuntimeException("当前商品有效期字段填写有误，请调整后重新同步");
        }
        //有效期 没有填0
        generalProductPresentInfoVo.setValidity(NumberUtils.toShort(productBaseInfoPo.getProductValidity()));
        //标准库id
        if (productBaseInfoPo.getMidStdlibId()!=null){
            generalProductPresentInfoVo.setProductId(productBaseInfoPo.getMidStdlibId()+"");
        }
        //运营属性
        //货主编号
        // generalProductPresentInfoVo.setChannelId(map.get(productBaseInfoPo.getOrganSign()).getChannelId()==null?"55":map.get(productBaseInfoPo.getOrganSign()).getChannelId());
        //货主名称
        // generalProductPresentInfoVo.setChannelName(map.get(productBaseInfoPo.getOrganSign()).getChannelName()==null?"智鹿":map.get(productBaseInfoPo.getOrganSign()).getChannelName());

        //这里是包装换算比率直接用湖北智鹿的值,没查到就用原本的
        // if (!org.apache.commons.lang3.StringUtils.isEmpty(zhiluOrgan)) {
        //     if (null != productBaseInfoPo.getStandardLibraryId() || null != productBaseInfoPo.getVirtualStandardLibraryId()){
        //         if (null == productBaseInfoPo.getStandardLibraryId()){
        //             productBaseInfoPo.setStandardLibraryId(productBaseInfoPo.getVirtualStandardLibraryId());
        //         }
        //         List<ProductBaseInfoPo> packByStand = productBaseInfoService.getPackByStand(zhiluOrgan, productBaseInfoPo.getStandardLibraryId());
        //         if (org.apache.commons.collections.CollectionUtils.isNotEmpty(packByStand)) {
        //             ProductBaseInfoPo zhiluPo = packByStand.get(0);
        //             // 数据库默认值为0,所以不可能为空,如果湖北智鹿的商品中包装、件包装有数据的话用湖北智鹿的
        //             if (null != zhiluPo.getMiddleSmallUnitRate() && BigDecimal.ZERO.compareTo(zhiluPo.getMiddleSmallUnitRate()) < 0) {
        //                 productBaseInfoPo.setMiddleSmallUnitRate(zhiluPo.getMiddleSmallUnitRate());
        //             }
        //             if (null != zhiluPo.getBigMiddleUnitRate() && BigDecimal.ZERO.compareTo(zhiluPo.getBigMiddleUnitRate()) < 0) {
        //                 productBaseInfoPo.setBigMiddleUnitRate(zhiluPo.getBigMiddleUnitRate());
        //             }
        //         }
        //     }
        // }

        //中包装数量
        // generalProductPresentInfoVo.setMediumPackageNumberMin(productBaseInfoPo.getMiddleSmallUnitRate() == null ? "1" : productBaseInfoPo.getMiddleSmallUnitRate().intValue() + "");
        //件包装数量
        // generalProductPresentInfoVo.setPiecePackageNumberMin((productBaseInfoPo.getBigMiddleUnitRate() == null || productBaseInfoPo.getMiddleSmallUnitRate() == null) ? "1" : productBaseInfoPo.getBigMiddleUnitRate().multiply(productBaseInfoPo.getMiddleSmallUnitRate()).intValue() + "");
        // generalProductPresentInfoVo.setPiecePackageNumberMin((productBaseInfoPo.getBigMiddleUnitRate() == null) ? "1" : productBaseInfoPo.getBigMiddleUnitRate().intValue()+"");
        //限销状态
        generalProductPresentInfoVo.setLimitedPinState((byte)0);
        //限采状态
        generalProductPresentInfoVo.setBaseLimitedProductionState((byte)0);
        //停用状态
        generalProductPresentInfoVo.setBaseDisableState((byte)0);
        //特殊属性
        // if (!StringUtil.isEmpty(productBaseInfoPo.getSpecialAttributes())){
        //     StringBuffer buffer = new StringBuffer();
        //     String[] specialAttributes = productBaseInfoPo.getSpecialAttributes().split(",");
        //     for (String specialAttribute: specialAttributes){
        //         buffer.append(MeProductTransformUtil.specialAttributesMap.get(Integer.parseInt(specialAttribute)));
        //         buffer.append(",");
        //     }
        //     buffer.substring(0, buffer.length()-1).toString();
        //     generalProductPresentInfoVo.setSpecialAttributes(buffer.substring(0, buffer.length()-1).toString());
        // }

        //是否可拆零
        generalProductPresentInfoVo.setScatteredYn((byte)1);
        //养护类别
        // if (productBaseInfoPo.getMaintenanceType()!=null){
        //     generalProductPresentInfoVo.setKeyConservationCategories(ProductAndMiddleTransforUtil.maintenanceType.get(productBaseInfoPo.getMaintenanceType()));
        //     //养护周期
        //     Integer MaintenancePeriod = productBaseInfoPo.getMaintenanceType()==569?90:30;
        //     generalProductPresentInfoVo.setMaintenancePeriod(MaintenancePeriod);
        // }
        //机构编码
        // generalProductPresentInfoVo.setOrgCode(map.get(productBaseInfoPo.getOrganSign()).getEcCode());
        return generalProductPresentInfoVo;
    }
}
