package com.xyy.saas.inquiry.product.server.dal.dataobject.gsp;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 质量变更申请明细记录 DO
 *
 * <AUTHOR>
 */
@TableName("saas_product_quality_change_detail")
@KeySequence("saas_product_quality_change_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductQualityChangeDetailDO extends TenantBaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 单据编号
     */
    private String recordPref;
    /**
     * 变更类型
     */
    private Integer type;
    /**
     * 商品/供应商编码
     */
    private String itemPref;
    /**
     * 外码
     */
    private String itemCode;
    /**
     * 名称
     */
    private String itemName;
    /**
     * 变更内容（JSON格式）
     */
    private String content;
    /**
     * 备注
     */
    private String remark;

}