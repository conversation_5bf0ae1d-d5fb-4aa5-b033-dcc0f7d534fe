package com.xyy.saas.inquiry.product.server.application.context;

import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.server.domain.aggregate.ProductAggregate;
import com.xyy.saas.inquiry.product.server.domain.entity.ProductInfo;
import com.xyy.saas.inquiry.product.server.domain.entity.ProductStdlib;
import lombok.Getter;
import lombok.Setter;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.Map;

/**
 * 商品保存上下文
 * 
 * <AUTHOR> Assistant
 */
@Getter
@Setter
@Accessors(chain = true)
public class ProductSaveContext {
    
    /**
     * 原始请求数据
     */
    private ProductInfoDto originalDto;
    
    /**
     * 当前处理的商品数据
     */
    private ProductInfoDto currentDto;
    
    /**
     * 业务类型
     */
    private ProductBizTypeEnum bizType;
    
    /**
     * 租户信息
     */
    private TenantDto tenant;
    
    /**
     * 操作人ID
     */
    private String operatorId;
    
    /**
     * 原始商品信息（用于更新场景）
     */
    private ProductInfoDto originalProduct;
    
    /**
     * 商品聚合根
     */
    private ProductAggregate productAggregate;
    
    /**
     * 商品领域实体
     */
    private ProductInfo productInfo;
    
    /**
     * 标准库实体
     */
    private ProductStdlib productStdlib;
    
    /**
     * 是否为创建操作
     */
    private Boolean isCreate;
    
    /**
     * 是否需要处理标准库
     */
    private Boolean needProcessStdlib;
    
    /**
     * 是否需要创建审批流
     */
    private Boolean needCreateApproval;
    
    /**
     * 是否需要上报中台
     */
    private Boolean needReportToMid;
    
    /**
     * 处理结果：商品ID
     */
    private Long productId;
    
    /**
     * 处理状态
     */
    private ProcessStatus status = ProcessStatus.INITIALIZED;
    
    /**
     * 错误信息
     */
    private String errorMessage;
    
    /**
     * 上下文属性（用于步骤间传递数据）
     */
    private final Map<String, Object> attributes = new HashMap<>();
    
    /**
     * 是否应该停止处理
     */
    private Boolean shouldStop = false;
    
    /**
     * 构造函数
     */
    public ProductSaveContext(ProductInfoDto dto, ProductBizTypeEnum bizType, String operatorId) {
        this.originalDto = dto;
        this.currentDto = dto;
        this.bizType = bizType;
        this.operatorId = operatorId;
        this.isCreate = dto.getId() == null;
    }
    
    /**
     * 设置属性
     */
    public ProductSaveContext setAttribute(String key, Object value) {
        this.attributes.put(key, value);
        return this;
    }
    
    /**
     * 获取属性
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key) {
        return (T) this.attributes.get(key);
    }
    
    /**
     * 获取属性（带默认值）
     */
    @SuppressWarnings("unchecked")
    public <T> T getAttribute(String key, T defaultValue) {
        T value = (T) this.attributes.get(key);
        return value != null ? value : defaultValue;
    }
    
    /**
     * 检查是否包含属性
     */
    public boolean hasAttribute(String key) {
        return this.attributes.containsKey(key);
    }
    
    /**
     * 标记为成功
     */
    public ProductSaveContext markSuccess(Long productId) {
        this.status = ProcessStatus.SUCCESS;
        this.productId = productId;
        return this;
    }
    
    /**
     * 标记为失败
     */
    public ProductSaveContext markFailure(String errorMessage) {
        this.status = ProcessStatus.FAILED;
        this.errorMessage = errorMessage;
        this.shouldStop = true;
        return this;
    }
    
    /**
     * 标记为跳过
     */
    public ProductSaveContext markSkipped(String reason) {
        this.status = ProcessStatus.SKIPPED;
        this.setAttribute("skipReason", reason);
        return this;
    }
    
    /**
     * 检查是否成功
     */
    public boolean isSuccess() {
        return status == ProcessStatus.SUCCESS;
    }
    
    /**
     * 检查是否失败
     */
    public boolean isFailed() {
        return status == ProcessStatus.FAILED;
    }
    
    /**
     * 检查是否应该停止处理
     */
    public boolean shouldStop() {
        return Boolean.TRUE.equals(shouldStop) || isFailed();
    }
    
    /**
     * 处理状态枚举
     */
    public enum ProcessStatus {
        INITIALIZED,    // 已初始化
        PROCESSING,     // 处理中
        SUCCESS,        // 成功
        FAILED,         // 失败
        SKIPPED         // 跳过
    }
}