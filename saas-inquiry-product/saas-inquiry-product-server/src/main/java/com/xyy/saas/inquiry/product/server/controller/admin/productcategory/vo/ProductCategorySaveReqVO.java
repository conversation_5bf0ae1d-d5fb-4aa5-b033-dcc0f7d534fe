package com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 商品六级分类新增/修改 Request VO")
@Data
public class ProductCategorySaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18343")
    private Long id;

    @Schema(description = "分类名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "分类名称不能为空")
    private String name;

    @Schema(description = "字典id（中台）", requiredMode = Schema.RequiredMode.REQUIRED, example = "20775")
    @NotNull(message = "字典id（中台）不能为空")
    private Long dictId;

    @Schema(description = "字典父id（中台）", requiredMode = Schema.RequiredMode.REQUIRED, example = "6099")
    @NotNull(message = "字典父id（中台）不能为空")
    private Long parentDictId;

    @Schema(description = "排序权重", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "排序权重不能为空")
    private Integer sortOrder;

    @Schema(description = "备注", example = "你猜")
    private String remark;

}