package com.xyy.saas.inquiry.product.server.application.command;

import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import lombok.Builder;
import lombok.Getter;

/**
 * 商品保存命令
 * 
 * <AUTHOR> Assistant
 */
@Getter
@Builder
public class ProductSaveCommand {
    
    /**
     * 商品信息DTO
     */
    private final ProductInfoDto productDto;
    
    /**
     * 业务类型
     */
    private final ProductBizTypeEnum bizType;
    
    /**
     * 操作人ID
     */
    private final String operatorId;
    
    /**
     * 操作来源
     */
    private final String operationSource;
    
    /**
     * 是否跳过验证
     */
    private final Boolean skipValidation;
    
    /**
     * 是否异步处理
     */
    private final Boolean asyncProcessing;
    
    /**
     * 扩展参数
     */
    private final java.util.Map<String, Object> extParams;
    
    /**
     * 验证命令有效性
     */
    public void validate() {
        if (productDto == null) {
            throw new IllegalArgumentException("ProductDto cannot be null");
        }
        if (operatorId == null || operatorId.trim().isEmpty()) {
            throw new IllegalArgumentException("OperatorId cannot be null or empty");
        }
    }
    
    /**
     * 检查是否为新建操作
     */
    public boolean isCreateOperation() {
        return productDto.getId() == null;
    }
    
    /**
     * 检查是否为更新操作
     */
    public boolean isUpdateOperation() {
        return productDto.getId() != null;
    }
    
    /**
     * 获取扩展参数
     */
    public Object getExtParam(String key) {
        return extParams != null ? extParams.get(key) : null;
    }
    
    /**
     * 检查是否包含扩展参数
     */
    public boolean hasExtParam(String key) {
        return extParams != null && extParams.containsKey(key);
    }
}