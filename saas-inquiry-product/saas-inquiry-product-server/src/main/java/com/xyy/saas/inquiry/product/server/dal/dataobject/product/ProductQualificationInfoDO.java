package com.xyy.saas.inquiry.product.server.dal.dataobject.product;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDate;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.product.api.product.dto.qualification.ProductQualificationExtInfo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 商品资质信息 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_product_qualification_info", autoResultMap = true)
@KeySequence("saas_product_qualification_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductQualificationInfoDO extends BaseDO {
    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 商品编号
     */
    private String productPref;
    /**
     * 资质类型
     */
    private Integer qualificationType;
    /**
     * 资质信息
     */
    private String qualificationInfo;
    /**
     * 开始日期
     */
    private LocalDate startDate;
    /**
     * 结束日期
     */
    private LocalDate endDate;
    /**
     * 扩展信息，比如图片链接（支持多个）
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private ProductQualificationExtInfo ext;
    /**
     * 备注
     */
    private String remark;

}