package com.xyy.saas.inquiry.product.server.domain.aggregate;

import com.xyy.saas.inquiry.product.server.domain.entity.ProductInfo;
import com.xyy.saas.inquiry.product.server.domain.entity.ProductStdlib;
import com.xyy.saas.inquiry.product.server.domain.event.ProductDomainEvent;
import com.xyy.saas.inquiry.product.server.domain.valueobject.ProductFlag;
import com.xyy.saas.inquiry.product.server.domain.valueobject.ProductId;
import com.xyy.saas.inquiry.product.server.domain.valueobject.ProductQualification;
import com.xyy.saas.inquiry.product.server.domain.valueobject.ProductUseInfo;
import com.xyy.saas.inquiry.product.server.domain.valueobject.TenantId;
import lombok.Getter;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;

/**
 * 商品聚合根
 * 
 * <AUTHOR> Assistant
 */
@Getter
@Accessors(chain = true)
public class ProductAggregate {
    
    /**
     * 聚合标识
     */
    private ProductId productId;
    
    /**
     * 商品基本信息 - 聚合根实体
     */
    private ProductInfo productInfo;
    
    /**
     * 商品标准库信息 - 实体
     */
    private ProductStdlib productStdlib;
    
    /**
     * 商品使用信息 - 值对象
     */
    private ProductUseInfo useInfo;
    
    /**
     * 商品资质信息 - 值对象
     */
    private ProductQualification qualification;
    
    /**
     * 商品业务标志 - 值对象  
     */
    private ProductFlag productFlag;
    
    /**
     * 租户信息
     */
    private TenantId tenantId;
    private TenantId headTenantId;
    
    /**
     * 领域事件列表
     */
    private final List<ProductDomainEvent> domainEvents = new ArrayList<>();
    
    /**
     * 版本号 - 用于乐观锁
     */
    private Long version;
    
    /**
     * 创建时间
     */
    private LocalDateTime createTime;
    
    /**
     * 更新时间  
     */
    private LocalDateTime updateTime;
    
    private ProductAggregate() {
        // 私有构造器，通过工厂方法创建
    }
    
    /**
     * 创建新的商品聚合
     */
    public static ProductAggregate create(ProductInfo productInfo, TenantId tenantId) {
        ProductAggregate aggregate = new ProductAggregate();
        aggregate.productId = ProductId.generate();
        aggregate.productInfo = productInfo;
        aggregate.tenantId = tenantId;
        aggregate.createTime = LocalDateTime.now();
        aggregate.updateTime = LocalDateTime.now();
        aggregate.version = 0L;
        
        return aggregate;
    }
    
    /**
     * 从已有数据重建聚合
     */
    public static ProductAggregate reconstruct(ProductId productId, ProductInfo productInfo, 
                                             ProductStdlib productStdlib, ProductUseInfo useInfo,
                                             ProductQualification qualification, ProductFlag productFlag,
                                             TenantId tenantId, TenantId headTenantId,
                                             Long version, LocalDateTime createTime, LocalDateTime updateTime) {
        ProductAggregate aggregate = new ProductAggregate();
        aggregate.productId = productId;
        aggregate.productInfo = productInfo;
        aggregate.productStdlib = productStdlib;
        aggregate.useInfo = useInfo;
        aggregate.qualification = qualification;
        aggregate.productFlag = productFlag;
        aggregate.tenantId = tenantId;
        aggregate.headTenantId = headTenantId;
        aggregate.version = version;
        aggregate.createTime = createTime;
        aggregate.updateTime = updateTime;
        
        return aggregate;
    }
    
    /**
     * 更新商品信息
     */
    public void updateProductInfo(ProductInfo newProductInfo) {
        this.productInfo = newProductInfo;
        this.updateTime = LocalDateTime.now();
        this.version++;
    }
    
    /**
     * 设置标准库信息
     */
    public void setStdlib(ProductStdlib stdlib) {
        this.productStdlib = stdlib;
        this.updateTime = LocalDateTime.now();
        this.version++;
    }
    
    /**
     * 更新使用信息
     */
    public void updateUseInfo(ProductUseInfo newUseInfo) {
        this.useInfo = newUseInfo;
        this.updateTime = LocalDateTime.now();
        this.version++;
    }
    
    /**
     * 更新资质信息
     */
    public void updateQualification(ProductQualification newQualification) {
        this.qualification = newQualification;
        this.updateTime = LocalDateTime.now();
        this.version++;
    }
    
    /**
     * 更新商品标志
     */
    public void updateProductFlag(ProductFlag newFlag) {
        this.productFlag = newFlag;
        this.updateTime = LocalDateTime.now();
        this.version++;
    }
    
    /**
     * 添加领域事件
     */
    public void addDomainEvent(ProductDomainEvent event) {
        this.domainEvents.add(event);
    }
    
    /**
     * 清除领域事件
     */
    public void clearDomainEvents() {
        this.domainEvents.clear();
    }
    
    /**
     * 获取所有领域事件
     */
    public List<ProductDomainEvent> getDomainEvents() {
        return new ArrayList<>(this.domainEvents);
    }
    
    /**
     * 检查聚合是否为新创建的
     */
    public boolean isNew() {
        return this.version == 0L;
    }
    
    /**
     * 检查是否有标准库信息
     */
    public boolean hasStdlib() {
        return this.productStdlib != null;
    }
    
    /**
     * 检查是否有使用信息
     */
    public boolean hasUseInfo() {
        return this.useInfo != null;
    }
    
    /**
     * 检查是否有资质信息
     */
    public boolean hasQualification() {
        return this.qualification != null;
    }
}