package com.xyy.saas.inquiry.product.server.controller.admin.product.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;
import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;


@Data
@Schema(description = "管理后台 - 标准库商品同步请求 VO")
public class StdlibProductSyncReqVo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;

    @Schema(description = "中台标准库id列表", requiredMode = RequiredMode.REQUIRED, example = "[1,2,3]")
    @NotEmpty(message = "中台标准库id列表不能为空")
    private List<Long> midStdlibIdList;


}
