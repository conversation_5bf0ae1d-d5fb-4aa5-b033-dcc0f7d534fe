package com.xyy.saas.inquiry.product.server.convert.forward;

import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDetailDto;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibDto;
import com.xyy.saas.inquiry.product.api.search.dto.InquiryDiagnosticsSearchReqDto;
import com.xyy.saas.inquiry.product.api.search.dto.InquiryProductSearchReqDto;
import com.xyy.saas.inquiry.product.server.controller.app.search.vo.InquiryProductSearchReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.search.vo.InquiryProductSearchRespVO;
import com.xyy.saas.inquiry.product.server.service.forward.dto.DiagnosticsForwardDto;
import com.xyy.saas.inquiry.product.server.service.forward.dto.ProductForwardDto;
import com.xyy.saas.inquiry.product.server.service.forward.dto.ProductStandardForwardDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
 * sms Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface ProductForwardConvert {

    ProductForwardConvert INSTANCE = Mappers.getMapper(ProductForwardConvert.class);

    @Mapping(target = "productType", source = "medicineType")
    ProductForwardDto convertProductDto(InquiryProductSearchReqVO reqVO);


    @Mapping(target = "druginfoList", source = "productSearchList")
    DiagnosticsForwardDto convertDiagnosticsDto(InquiryDiagnosticsSearchReqDto reqVO);


    // 定义单个DTO对象的转换方法，并指定具体的字段映射
    @Mapping(target = "singleUnit", source = "minUseUtil")
    @Mapping(target = "pref", source = "productCode")
    @Mapping(target = "useFrequencyValue", source = "useFrequency")
    InquiryProductDetailDto convertToProductDetailDto(ProductStandardForwardDto dto);

    List<InquiryProductDetailDto> convertProductDetailDto(List<ProductStandardForwardDto> result);


    @Mapping(target = "standardId", source = "pref")
    @Mapping(target = "useFrequencyValue", source = "useFrequency")
    InquiryProductSearchRespVO convertSearchRespDto(ProductStandardForwardDto dto);

    List<InquiryProductSearchRespVO> convertSearchRespDtos(List<ProductStandardForwardDto> result);

    @Mapping(target = "productType", source = "medicineType")
    ProductForwardDto convertProductDto(InquiryProductSearchReqDto reqVO);


    List<InquiryProductDetailDto> convertProductDetailStdDto(List<ProductStdlibDto> productStdlibDtos);


    // 定义单个DTO对象的转换方法，并指定具体的字段映射
    @Mapping(target = "standardId", source = "midStdlibId")
    @Mapping(target = "singleDose", source = "singleDosage")
    @Mapping(target = "singleUnit", source = "singleDosageUnit")
    @Mapping(target = "singleUnitValue", source = "singleDosageUnit")
    @Mapping(target = "pref", source = "id")
    @Mapping(target = "useFrequencyValue", source = "usageFrequency")
    @Mapping(target = "useFrequency", source = "usageFrequency")
    @Mapping(target = "directions", source = "usageMethod")
    @Mapping(target = "directionsValue", source = "usageMethod")

    @Mapping(target = "productName", source = "commonName")
    @Mapping(target = "commonName", source = "commonName")
    @Mapping(target = "attributeSpecification", source = "spec")
    @Mapping(target = "unitName", source = "unit")
    InquiryProductDetailDto convertProductDetailStdDto(ProductStdlibDto dto);


}
