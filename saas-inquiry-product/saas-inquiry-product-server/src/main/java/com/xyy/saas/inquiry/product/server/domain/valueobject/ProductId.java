package com.xyy.saas.inquiry.product.server.domain.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.util.UUID;

/**
 * 商品ID值对象
 * 
 * <AUTHOR> Assistant
 */
@Getter
@EqualsAndHashCode
public class ProductId {
    
    private final Long value;
    private final String pref;
    
    private ProductId(Long value, String pref) {
        this.value = value;
        this.pref = pref;
    }
    
    /**
     * 通过数据库ID创建
     */
    public static ProductId of(Long id, String pref) {
        if (id == null) {
            throw new IllegalArgumentException("ProductId cannot be null");
        }
        return new ProductId(id, pref);
    }
    
    /**
     * 生成新的商品ID（用于新建商品）
     */
    public static ProductId generate() {
        // 这里先返回null，实际ID会在数据库插入时生成
        String pref = generatePref();
        return new ProductId(null, pref);
    }
    
    /**
     * 生成商品编码
     */
    private static String generatePref() {
        return "PRD_" + UUID.randomUUID().toString().replace("-", "").toUpperCase().substring(0, 16);
    }
    
    /**
     * 创建带ID的ProductId（用于持久化后的对象）
     */
    public ProductId withId(Long id) {
        return new ProductId(id, this.pref);
    }
    
    @Override
    public String toString() {
        return String.format("ProductId{id=%d, pref='%s'}", value, pref);
    }
}