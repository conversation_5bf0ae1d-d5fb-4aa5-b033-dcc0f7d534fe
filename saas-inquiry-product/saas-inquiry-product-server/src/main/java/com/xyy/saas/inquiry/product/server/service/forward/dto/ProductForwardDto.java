package com.xyy.saas.inquiry.product.server.service.forward.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * @Author:chenxiaoyi
 * @Date:2024/11/26 9:47
 */
@Data
public class ProductForwardDto implements Serializable /*extends ProductBaseDto */ {

    /**
     * 商品编码
     */
    private String pref;

    /**
     * 商品名称
     */
    private String productName;

    /**
     * 规格/型号
     */
    private String attributeSpecification;

    /**
     * 药品类型  0：西药、1:中药
     */
    private Integer productType;


    /**
     * 条形码
     */
    private String barCode;

    /**
     * 商品名称集合
     */
    private List<String> productNameList;

}
