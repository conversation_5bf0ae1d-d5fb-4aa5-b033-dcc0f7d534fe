package com.xyy.saas.inquiry.product.server.domain.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;

/**
 * 商品标志值对象 - 基于原有ProductFlag重构
 * 
 * <AUTHOR> Assistant
 */
@Getter
@EqualsAndHashCode
public class ProductFlag {
    
    /**
     * 禁采标识
     */
    private final Boolean purchaseDisabled;
    
    /**
     * 停售标识
     */
    private final Boolean stopSale;
    
    /**
     * 特价标识
     */
    private final Boolean specialPrice;
    
    /**
     * 积分标识
     */
    private final Boolean integral;
    
    /**
     * 拆零标识
     */
    private final Boolean unbundled;
    
    /**
     * 中台同步跳过标识
     */
    private final Boolean midSyncSkipped;
    
    /**
     * 中台停用标识
     */
    private final Boolean midDeactivated;
    
    private ProductFlag(Boolean purchaseDisabled, Boolean stopSale, Boolean specialPrice, 
                       Boolean integral, Boolean unbundled, Boolean midSyncSkipped, Boolean midDeactivated) {
        this.purchaseDisabled = purchaseDisabled;
        this.stopSale = stopSale;
        this.specialPrice = specialPrice;
        this.integral = integral;
        this.unbundled = unbundled;
        this.midSyncSkipped = midSyncSkipped;
        this.midDeactivated = midDeactivated;
    }
    
    /**
     * 创建默认标志
     */
    public static ProductFlag defaultFlag() {
        return new ProductFlag(false, false, false, false, false, false, false);
    }
    
    /**
     * 构建器
     */
    public static Builder builder() {
        return new Builder();
    }
    
    /**
     * 从原有ProductFlag转换
     */
    public static ProductFlag fromOriginal(com.xyy.saas.inquiry.product.api.product.dto.ProductFlag original) {
        if (original == null) {
            return defaultFlag();
        }
        return builder()
            // .purchaseDisabled(original.getPurchaseDisabled())
            .stopSale(original.getStopSale())
            .specialPrice(original.getSpecialPrice())
            .integral(original.getIntegral())
            .unbundled(original.getUnbundled())
            .midSyncSkipped(original.getMidSyncSkipped())
            .midDeactivated(original.getMidDeactivated())
            .build();
    }
    
    /**
     * 转换为原有ProductFlag
     */
    public com.xyy.saas.inquiry.product.api.product.dto.ProductFlag toOriginal() {
        return new com.xyy.saas.inquiry.product.api.product.dto.ProductFlag()
            // .setPurchaseDisabled(this.purchaseDisabled)
            .setStopSale(this.stopSale)
            .setSpecialPrice(this.specialPrice)
            .setIntegral(this.integral)
            .setUnbundled(this.unbundled)
            .setMidSyncSkipped(this.midSyncSkipped)
            .setMidDeactivated(this.midDeactivated);
    }
    
    /**
     * 设置禁采状态
     */
    public ProductFlag withPurchaseDisabled(Boolean purchaseDisabled) {
        return new ProductFlag(purchaseDisabled, this.stopSale, this.specialPrice, 
                              this.integral, this.unbundled, this.midSyncSkipped, this.midDeactivated);
    }
    
    /**
     * 设置停售状态
     */
    public ProductFlag withStopSale(Boolean stopSale) {
        return new ProductFlag(this.purchaseDisabled, stopSale, this.specialPrice,
                              this.integral, this.unbundled, this.midSyncSkipped, this.midDeactivated);
    }
    
    /**
     * 设置拆零标识
     */
    public ProductFlag withUnbundled(Boolean unbundled) {
        return new ProductFlag(this.purchaseDisabled, this.stopSale, this.specialPrice,
                              this.integral, unbundled, this.midSyncSkipped, this.midDeactivated);
    }
    
    /**
     * 合并标志（用于更新操作）
     */
    public ProductFlag merge(ProductFlag other) {
        if (other == null) {
            return this;
        }
        return new ProductFlag(
            other.purchaseDisabled != null ? other.purchaseDisabled : this.purchaseDisabled,
            other.stopSale != null ? other.stopSale : this.stopSale,
            other.specialPrice != null ? other.specialPrice : this.specialPrice,
            other.integral != null ? other.integral : this.integral,
            other.unbundled != null ? other.unbundled : this.unbundled,
            other.midSyncSkipped != null ? other.midSyncSkipped : this.midSyncSkipped,
            other.midDeactivated != null ? other.midDeactivated : this.midDeactivated
        );
    }
    
    /**
     * 构建器类
     */
    public static class Builder {
        private Boolean purchaseDisabled;
        private Boolean stopSale;
        private Boolean specialPrice;
        private Boolean integral;
        private Boolean unbundled;
        private Boolean midSyncSkipped;
        private Boolean midDeactivated;
        
        public Builder purchaseDisabled(Boolean purchaseDisabled) {
            this.purchaseDisabled = purchaseDisabled;
            return this;
        }
        
        public Builder stopSale(Boolean stopSale) {
            this.stopSale = stopSale;
            return this;
        }
        
        public Builder specialPrice(Boolean specialPrice) {
            this.specialPrice = specialPrice;
            return this;
        }
        
        public Builder integral(Boolean integral) {
            this.integral = integral;
            return this;
        }
        
        public Builder unbundled(Boolean unbundled) {
            this.unbundled = unbundled;
            return this;
        }
        
        public Builder midSyncSkipped(Boolean midSyncSkipped) {
            this.midSyncSkipped = midSyncSkipped;
            return this;
        }
        
        public Builder midDeactivated(Boolean midDeactivated) {
            this.midDeactivated = midDeactivated;
            return this;
        }
        
        public ProductFlag build() {
            return new ProductFlag(purchaseDisabled, stopSale, specialPrice, 
                                  integral, unbundled, midSyncSkipped, midDeactivated);
        }
    }
}