package com.xyy.saas.inquiry.product.server.config.forward.dto;


import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class MidDictionaryVo implements Serializable {
    private Integer id;
    private String type;
    private String dictName;
    private Byte levelNode;
    private Integer parentId;
    private Integer pageNo;
    private Integer limit;
    private String traceId;
    private List<Byte> levelNodeList;
    private List<Integer> idList;
}
