package com.xyy.saas.inquiry.product.server.domain.entity;

import com.xyy.saas.inquiry.product.enums.ProductStdlibStatusEnum;
import com.xyy.saas.inquiry.product.server.domain.valueobject.ProductFlag;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

/**
 * 商品标准库实体
 * 
 * <AUTHOR> Assistant
 */
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class ProductStdlib {
    
    /**
     * 标准库ID - 实体标识
     */
    @EqualsAndHashCode.Include
    private final Long stdlibId;
    
    /**
     * 中台标准库ID
     */
    private final Long midStdlibId;
    
    /**
     * 助记码
     */
    private final String mnemonicCode;
    
    /**
     * 要素字段hash值
     */
    private final String keyPointHash;
    
    /**
     * 通用名
     */
    private final String commonName;
    
    /**
     * 品牌名
     */
    private final String brandName;
    
    /**
     * 规格/型号
     */
    private final String spec;
    
    /**
     * 条形码
     */
    private final String barcode;
    
    /**
     * 生产厂家
     */
    private final String manufacturer;
    
    /**
     * 批准文号
     */
    private final String approvalNumber;
    
    /**
     * 商品封面图片
     */
    private final List<String> coverImages;
    
    /**
     * 商品外包装图片
     */
    private final List<String> outerPackageImages;
    
    /**
     * 商品说明书图片
     */
    private final List<String> instructionImages;
    
    /**
     * 最小包装数量
     */
    private final BigDecimal minPackageNum;
    
    /**
     * 商品大类
     */
    private final String spuCategory;
    
    /**
     * 单位
     */
    private final String unit;
    
    /**
     * 剂型
     */
    private final String dosageForm;
    
    /**
     * 所属范围
     */
    private final String businessScope;
    
    /**
     * 处方分类
     */
    private final String presCategory;
    
    /**
     * 储存条件
     */
    private final String storageWay;
    
    /**
     * 产地
     */
    private final String origin;
    
    /**
     * 生产企业社会信用代码
     */
    private final String manufacturerUscc;
    
    /**
     * 有效期
     */
    private final String productValidity;
    
    /**
     * 批准文号有效期
     */
    private final LocalDate approvalValidityPeriod;
    
    /**
     * 上市许可持有人
     */
    private final String marketingAuthorityHolder;
    
    /**
     * 药品标识码
     */
    private final String drugIdentCode;
    
    /**
     * 用药方式
     */
    private final String usageMethod;
    
    /**
     * 用药频次
     */
    private final String usageFrequency;
    
    /**
     * 单次使用剂量
     */
    private final String singleDosage;
    
    /**
     * 单次使用剂量单位
     */
    private final String singleDosageUnit;
    
    /**
     * 分类信息
     */
    private final String firstCategory;
    private final String secondCategory;
    private final String thirdCategory;
    private final String fourthCategory;
    private final String fiveCategory;
    private final String sixCategory;
    
    /**
     * 税率信息
     */
    private final BigDecimal inputTaxRate;
    private final BigDecimal outputTaxRate;
    
    /**
     * 标准库状态
     */
    private final ProductStdlibStatusEnum status;
    
    /**
     * 标准库标志
     */
    private final ProductFlag productFlag;
    
    private ProductStdlib(Builder builder) {
        this.stdlibId = builder.stdlibId;
        this.midStdlibId = builder.midStdlibId;
        this.mnemonicCode = builder.mnemonicCode;
        this.keyPointHash = builder.keyPointHash;
        this.commonName = builder.commonName;
        this.brandName = builder.brandName;
        this.spec = builder.spec;
        this.barcode = builder.barcode;
        this.manufacturer = builder.manufacturer;
        this.approvalNumber = builder.approvalNumber;
        this.coverImages = builder.coverImages != null ? List.copyOf(builder.coverImages) : List.of();
        this.outerPackageImages = builder.outerPackageImages != null ? List.copyOf(builder.outerPackageImages) : List.of();
        this.instructionImages = builder.instructionImages != null ? List.copyOf(builder.instructionImages) : List.of();
        this.minPackageNum = builder.minPackageNum;
        this.spuCategory = builder.spuCategory;
        this.unit = builder.unit;
        this.dosageForm = builder.dosageForm;
        this.businessScope = builder.businessScope;
        this.presCategory = builder.presCategory;
        this.storageWay = builder.storageWay;
        this.origin = builder.origin;
        this.manufacturerUscc = builder.manufacturerUscc;
        this.productValidity = builder.productValidity;
        this.approvalValidityPeriod = builder.approvalValidityPeriod;
        this.marketingAuthorityHolder = builder.marketingAuthorityHolder;
        this.drugIdentCode = builder.drugIdentCode;
        this.usageMethod = builder.usageMethod;
        this.usageFrequency = builder.usageFrequency;
        this.singleDosage = builder.singleDosage;
        this.singleDosageUnit = builder.singleDosageUnit;
        this.firstCategory = builder.firstCategory;
        this.secondCategory = builder.secondCategory;
        this.thirdCategory = builder.thirdCategory;
        this.fourthCategory = builder.fourthCategory;
        this.fiveCategory = builder.fiveCategory;
        this.sixCategory = builder.sixCategory;
        this.inputTaxRate = builder.inputTaxRate;
        this.outputTaxRate = builder.outputTaxRate;
        this.status = builder.status;
        this.productFlag = builder.productFlag;
    }
    
    /**
     * 创建构建器
     */
    public static Builder builder() {
        return new Builder();
    }
    
    /**
     * 检查是否为中台标准库
     */
    public boolean isFromMidPlatform() {
        return midStdlibId != null;
    }
    
    /**
     * 检查是否可以同步到中台
     */
    public boolean canSyncToMid() {
        return productFlag == null || productFlag.getMidSyncSkipped() != Boolean.TRUE;
    }
    
    /**
     * 检查标准库是否可用
     */
    public boolean isUsable() {
        return status == ProductStdlibStatusEnum.USING;
    }
    
    /**
     * 生成六要素唯一标识
     */
    public String generateKeyPointHash() {
        // 这里应该实现具体的hash生成逻辑，与原有逻辑保持一致
        return String.format("%s|%s|%s|%s|%s|%s", 
            commonName != null ? commonName : "",
            brandName != null ? brandName : "",
            spec != null ? spec : "",
            manufacturer != null ? manufacturer : "",
            approvalNumber != null ? approvalNumber : "",
            barcode != null ? barcode : "");
    }
    
    /**
     * 更新图片信息
     */
    public ProductStdlib updateImages(List<String> newCoverImages, 
                                    List<String> newOuterPackageImages,
                                    List<String> newInstructionImages) {
        return this.toBuilder()
            .coverImages(newCoverImages)
            .outerPackageImages(newOuterPackageImages)
            .instructionImages(newInstructionImages)
            .build();
    }
    
    /**
     * 转换为构建器（用于更新）
     */
    public Builder toBuilder() {
        return new Builder()
            .stdlibId(this.stdlibId)
            .midStdlibId(this.midStdlibId)
            .mnemonicCode(this.mnemonicCode)
            .keyPointHash(this.keyPointHash)
            .commonName(this.commonName)
            .brandName(this.brandName)
            .spec(this.spec)
            .barcode(this.barcode)
            .manufacturer(this.manufacturer)
            .approvalNumber(this.approvalNumber)
            .coverImages(this.coverImages)
            .outerPackageImages(this.outerPackageImages)
            .instructionImages(this.instructionImages)
            .minPackageNum(this.minPackageNum)
            .spuCategory(this.spuCategory)
            .unit(this.unit)
            .dosageForm(this.dosageForm)
            .businessScope(this.businessScope)
            .presCategory(this.presCategory)
            .storageWay(this.storageWay)
            .origin(this.origin)
            .manufacturerUscc(this.manufacturerUscc)
            .productValidity(this.productValidity)
            .approvalValidityPeriod(this.approvalValidityPeriod)
            .marketingAuthorityHolder(this.marketingAuthorityHolder)
            .drugIdentCode(this.drugIdentCode)
            .usageMethod(this.usageMethod)
            .usageFrequency(this.usageFrequency)
            .singleDosage(this.singleDosage)
            .singleDosageUnit(this.singleDosageUnit)
            .firstCategory(this.firstCategory)
            .secondCategory(this.secondCategory)
            .thirdCategory(this.thirdCategory)
            .fourthCategory(this.fourthCategory)
            .fiveCategory(this.fiveCategory)
            .sixCategory(this.sixCategory)
            .inputTaxRate(this.inputTaxRate)
            .outputTaxRate(this.outputTaxRate)
            .status(this.status)
            .productFlag(this.productFlag);
    }
    
    /**
     * 构建器类
     */
    public static class Builder {
        private Long stdlibId;
        private Long midStdlibId;
        private String mnemonicCode;
        private String keyPointHash;
        private String commonName;
        private String brandName;
        private String spec;
        private String barcode;
        private String manufacturer;
        private String approvalNumber;
        private List<String> coverImages;
        private List<String> outerPackageImages;
        private List<String> instructionImages;
        private BigDecimal minPackageNum;
        private String spuCategory;
        private String unit;
        private String dosageForm;
        private String businessScope;
        private String presCategory;
        private String storageWay;
        private String origin;
        private String manufacturerUscc;
        private String productValidity;
        private LocalDate approvalValidityPeriod;
        private String marketingAuthorityHolder;
        private String drugIdentCode;
        private String usageMethod;
        private String usageFrequency;
        private String singleDosage;
        private String singleDosageUnit;
        private String firstCategory;
        private String secondCategory;
        private String thirdCategory;
        private String fourthCategory;
        private String fiveCategory;
        private String sixCategory;
        private BigDecimal inputTaxRate;
        private BigDecimal outputTaxRate;
        private ProductStdlibStatusEnum status;
        private ProductFlag productFlag;
        
        public Builder stdlibId(Long stdlibId) {
            this.stdlibId = stdlibId;
            return this;
        }
        
        public Builder midStdlibId(Long midStdlibId) {
            this.midStdlibId = midStdlibId;
            return this;
        }
        
        public Builder mnemonicCode(String mnemonicCode) {
            this.mnemonicCode = mnemonicCode;
            return this;
        }
        
        public Builder keyPointHash(String keyPointHash) {
            this.keyPointHash = keyPointHash;
            return this;
        }
        
        public Builder commonName(String commonName) {
            this.commonName = commonName;
            return this;
        }
        
        public Builder brandName(String brandName) {
            this.brandName = brandName;
            return this;
        }
        
        public Builder spec(String spec) {
            this.spec = spec;
            return this;
        }
        
        public Builder barcode(String barcode) {
            this.barcode = barcode;
            return this;
        }
        
        public Builder manufacturer(String manufacturer) {
            this.manufacturer = manufacturer;
            return this;
        }
        
        public Builder approvalNumber(String approvalNumber) {
            this.approvalNumber = approvalNumber;
            return this;
        }
        
        public Builder coverImages(List<String> coverImages) {
            this.coverImages = coverImages;
            return this;
        }
        
        public Builder outerPackageImages(List<String> outerPackageImages) {
            this.outerPackageImages = outerPackageImages;
            return this;
        }
        
        public Builder instructionImages(List<String> instructionImages) {
            this.instructionImages = instructionImages;
            return this;
        }
        
        public Builder minPackageNum(BigDecimal minPackageNum) {
            this.minPackageNum = minPackageNum;
            return this;
        }
        
        public Builder spuCategory(String spuCategory) {
            this.spuCategory = spuCategory;
            return this;
        }
        
        public Builder unit(String unit) {
            this.unit = unit;
            return this;
        }
        
        public Builder dosageForm(String dosageForm) {
            this.dosageForm = dosageForm;
            return this;
        }
        
        public Builder businessScope(String businessScope) {
            this.businessScope = businessScope;
            return this;
        }
        
        public Builder presCategory(String presCategory) {
            this.presCategory = presCategory;
            return this;
        }
        
        public Builder storageWay(String storageWay) {
            this.storageWay = storageWay;
            return this;
        }
        
        public Builder origin(String origin) {
            this.origin = origin;
            return this;
        }
        
        public Builder manufacturerUscc(String manufacturerUscc) {
            this.manufacturerUscc = manufacturerUscc;
            return this;
        }
        
        public Builder productValidity(String productValidity) {
            this.productValidity = productValidity;
            return this;
        }
        
        public Builder approvalValidityPeriod(LocalDate approvalValidityPeriod) {
            this.approvalValidityPeriod = approvalValidityPeriod;
            return this;
        }
        
        public Builder marketingAuthorityHolder(String marketingAuthorityHolder) {
            this.marketingAuthorityHolder = marketingAuthorityHolder;
            return this;
        }
        
        public Builder drugIdentCode(String drugIdentCode) {
            this.drugIdentCode = drugIdentCode;
            return this;
        }
        
        public Builder usageMethod(String usageMethod) {
            this.usageMethod = usageMethod;
            return this;
        }
        
        public Builder usageFrequency(String usageFrequency) {
            this.usageFrequency = usageFrequency;
            return this;
        }
        
        public Builder singleDosage(String singleDosage) {
            this.singleDosage = singleDosage;
            return this;
        }
        
        public Builder singleDosageUnit(String singleDosageUnit) {
            this.singleDosageUnit = singleDosageUnit;
            return this;
        }
        
        public Builder firstCategory(String firstCategory) {
            this.firstCategory = firstCategory;
            return this;
        }
        
        public Builder secondCategory(String secondCategory) {
            this.secondCategory = secondCategory;
            return this;
        }
        
        public Builder thirdCategory(String thirdCategory) {
            this.thirdCategory = thirdCategory;
            return this;
        }
        
        public Builder fourthCategory(String fourthCategory) {
            this.fourthCategory = fourthCategory;
            return this;
        }
        
        public Builder fiveCategory(String fiveCategory) {
            this.fiveCategory = fiveCategory;
            return this;
        }
        
        public Builder sixCategory(String sixCategory) {
            this.sixCategory = sixCategory;
            return this;
        }
        
        public Builder inputTaxRate(BigDecimal inputTaxRate) {
            this.inputTaxRate = inputTaxRate;
            return this;
        }
        
        public Builder outputTaxRate(BigDecimal outputTaxRate) {
            this.outputTaxRate = outputTaxRate;
            return this;
        }
        
        public Builder status(ProductStdlibStatusEnum status) {
            this.status = status;
            return this;
        }
        
        public Builder productFlag(ProductFlag productFlag) {
            this.productFlag = productFlag;
            return this;
        }
        
        public ProductStdlib build() {
            // 基本验证
            if (commonName == null || commonName.trim().isEmpty()) {
                throw new IllegalArgumentException("CommonName cannot be null or empty");
            }
            
            return new ProductStdlib(this);
        }
    }
}