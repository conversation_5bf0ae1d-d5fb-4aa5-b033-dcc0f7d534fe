package com.xyy.saas.inquiry.product.server.dal.dataobject.bpm;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 审批流关联业务 DO
 *
 * <AUTHOR>
 */
@TableName("saas_bpm_business_relation")
@KeySequence("saas_bpm_business_relation_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BpmBusinessRelationDO extends TenantBaseDO {

    /**
     * ID
     */
    @TableId
    private Long id;
    /**
     * 租户编号（总部）
     */
    private Long headTenantId;
    /**
     * 业务类型
     */
    private Integer businessType;
    /**
     * 业务单据编号
     */
    private String businessPref;
    /**
     * 流程实例的编号
     */
    private String processInstanceId;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 申请人
     */
    private String applicant;
    /**
     * 审批状态
     */
    private Integer approvalStatus;
    /**
     * 备注
     */
    private String remark;

}