package com.xyy.saas.inquiry.product.server.application.strategy.impl;

import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.enums.ProductStatusEnum;
import com.xyy.saas.inquiry.product.server.application.strategy.ProductBizTypeHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 首营审批处理器
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class FirstProductHandler implements ProductBizTypeHandler {
    
    @Override
    public ProductBizTypeEnum getSupportedBizType() {
        return ProductBizTypeEnum.FIRST_PRODUCT;
    }
    
    @Override
    public void handleProductStatusAndFlag(ProductInfoDto dto) {
        log.info("[FirstProductHandler] 处理首营审批逻辑");
        
        // 设置为首营审核状态
        dto.setStatus(ProductStatusEnum.FIRST_AUDITING.code);
        
        log.debug("[FirstProductHandler] 商品状态设置为首营审核中: {}", ProductStatusEnum.FIRST_AUDITING.code);
    }
}