package com.xyy.saas.inquiry.product.server.service.transfer;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.product.server.controller.admin.transfer.vo.ProductTransferRecordPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.transfer.vo.ProductTransferRecordSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentPageRespVO;
import com.xyy.saas.inquiry.product.server.controller.app.product.vo.ProductPresentRecordRespVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.transfer.ProductTransferRecordDO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 商品流转记录 Service 接口
 *
 * <AUTHOR>
 */
public interface ProductTransferRecordService {

    /**
     * 创建商品流转记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    ProductTransferRecordDO createProductTransferRecord(@Valid ProductTransferRecordSaveReqVO createReqVO);

    /**
     * 更新商品流转记录
     *
     * @param updateReqVO 更新信息
     */
    void updateProductTransferRecord(@Valid ProductTransferRecordSaveReqVO updateReqVO);

    /**
     * 禁用商品流转记录
     *
     * @param id 编号
     */
    void disableProductTransferRecord(Long id);

    /**
     * 删除商品流转记录
     *
     * @param id 编号
     */
    void deleteProductTransferRecord(Long id);

    /**
     * 获得商品流转记录
     *
     * @param id 编号
     * @return 商品流转记录
     */
    ProductTransferRecordDO getProductTransferRecord(Long id);

    /**
     * 获得商品流转记录分页
     *
     * @param pageReqVO 分页查询
     * @return 商品流转记录分页
     */
    PageResult<ProductTransferRecordDO> getProductTransferRecordPage(ProductTransferRecordPageReqVO pageReqVO);


    /**
     * 获得商品流转记录
     *
     * @param reqVO 查询
     * @return 商品流转记录
     */
    List<ProductTransferRecordDO> listPresentTransferRecord(ProductPresentPageReqVO reqVO);

    /**
     * 获取最新提报记录
     *
     * @param pageReqVO 分页查询 条件
     * @return 商品流转记录分页
     */
    PageResult<ProductPresentRecordRespVO> getLatestPresentPage(ProductPresentPageReqVO pageReqVO);

    /**
     * 获取最新提报记录（移动端需要统计待审核、已通过、已驳回数量）
     *
     * @param pageReqVO 分页查询 条件
     * @return 商品流转记录分页
     */
    ProductPresentPageRespVO getLatestPresentPageWithStatistics(ProductPresentPageReqVO pageReqVO);

    /**
     * 获取最新提报记录
     *
     * @param id
     * @return 商品流转记录分页
     */
    ProductPresentRecordRespVO getLatestPresentById(Long id);


}