package com.xyy.saas.inquiry.product.server.controller.admin.product;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.product.api.product.dto.ProductFlag;
import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoPageReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductInfoRespVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductUnbundledBatchSaveReqVO;
import com.xyy.saas.inquiry.product.server.controller.admin.product.vo.ProductUnbundledSaveReqVO;
import com.xyy.saas.inquiry.product.server.service.product.ProductInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import java.util.Optional;
import org.springdoc.core.annotations.ParameterObject;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理后台 - 拆零商品")
@RestController
@RequestMapping("/product/unbundled")
@Validated
public class ProductUnbundledController {

    @Resource
    private ProductInfoService productInfoService;

    @PostMapping("/create")
    @Operation(summary = "创建拆零商品")
    @PreAuthorize("@ss.hasPermission('saas:product:unbundled:create')")
    public CommonResult<List<Long>> createProductUnbundled(@Valid @RequestBody ProductUnbundledBatchSaveReqVO batchSaveReqVO) {
        return success(productInfoService.saveOrUpdateUnbundledProduct(batchSaveReqVO));
    }

    @GetMapping("/get")
    @Operation(summary = "获得拆零商品")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:product:unbundled:query')")
    public CommonResult<ProductInfoRespVO> getProductUnbundled(@RequestParam("id") Long id) {
        ProductInfoDto productInfo = productInfoService.getProductInfo(id, true);
        return success(BeanUtils.toBean(productInfo, ProductInfoRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得拆零商品分页")
    @PreAuthorize("@ss.hasPermission('saas:product:unbundled:query')")
    public CommonResult<PageResult<ProductInfoRespVO>> getProductUnbundledPage(@Valid @ParameterObject ProductInfoPageReqVO pageReqVO) {
        // 拆零标志
        pageReqVO.setProductFlag(Optional.ofNullable(pageReqVO.getProductFlag()).orElseGet(ProductFlag::new).setUnbundled(true));

        PageResult<ProductInfoDto> productInfoPage = productInfoService.getProductInfoPage(pageReqVO);
        return success(BeanUtils.toBean(productInfoPage, ProductInfoRespVO.class));
    }


    @GetMapping("/export-excel")
    @Operation(summary = "导出拆零商品")
    @PreAuthorize("@ss.hasPermission('saas:product:unbundled:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportProductUnbundledExcel(@Valid ProductInfoPageReqVO pageReqVO, HttpServletResponse response) throws IOException {
        // 拆零标志
        pageReqVO.setProductFlag(Optional.ofNullable(pageReqVO.getProductFlag()).orElseGet(ProductFlag::new).setUnbundled(true));

        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ProductInfoDto> list = productInfoService.getProductInfoPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "拆零商品记录.xls", "数据", ProductInfoRespVO.class,
            BeanUtils.toBean(list, ProductInfoRespVO.class));
    }
} 
