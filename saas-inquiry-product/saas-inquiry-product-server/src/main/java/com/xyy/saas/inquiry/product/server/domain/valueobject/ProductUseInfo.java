package com.xyy.saas.inquiry.product.server.domain.valueobject;

import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.math.BigDecimal;

/**
 * 商品使用信息值对象
 * 
 * <AUTHOR> Assistant
 */
@Getter
@EqualsAndHashCode
public class ProductUseInfo {
    
    /**
     * 零售价
     */
    private final BigDecimal retailPrice;
    
    /**
     * 会员价
     */
    private final BigDecimal memberPrice;
    
    /**
     * 进价
     */
    private final BigDecimal purchasePrice;
    
    /**
     * 医保编码
     */
    private final String medicalInsuranceCode;
    
    /**
     * 医保名称
     */
    private final String medicalInsuranceName;
    
    /**
     * 医保类型
     */
    private final String medicalInsuranceType;
    
    /**
     * 是否医保
     */
    private final Boolean isMedicalInsurance;
    
    private ProductUseInfo(BigDecimal retailPrice, BigDecimal memberPrice, BigDecimal purchasePrice,
                          String medicalInsuranceCode, String medicalInsuranceName, 
                          String medicalInsuranceType, Boolean isMedicalInsurance) {
        this.retailPrice = retailPrice;
        this.memberPrice = memberPrice;
        this.purchasePrice = purchasePrice;
        this.medicalInsuranceCode = medicalInsuranceCode;
        this.medicalInsuranceName = medicalInsuranceName;
        this.medicalInsuranceType = medicalInsuranceType;
        this.isMedicalInsurance = isMedicalInsurance;
    }
    
    /**
     * 创建空的使用信息
     */
    public static ProductUseInfo empty() {
        return new ProductUseInfo(null, null, null, null, null, null, false);
    }
    
    /**
     * 构建器
     */
    public static Builder builder() {
        return new Builder();
    }
    
    /**
     * 更新价格信息
     */
    public ProductUseInfo updatePrices(BigDecimal newRetailPrice, BigDecimal newMemberPrice) {
        return new ProductUseInfo(
            newRetailPrice != null ? newRetailPrice : this.retailPrice,
            newMemberPrice != null ? newMemberPrice : this.memberPrice,
            this.purchasePrice,
            this.medicalInsuranceCode,
            this.medicalInsuranceName,
            this.medicalInsuranceType,
            this.isMedicalInsurance
        );
    }
    
    /**
     * 更新医保信息
     */
    public ProductUseInfo updateMedicalInsurance(String code, String name, String type, Boolean isMedical) {
        return new ProductUseInfo(
            this.retailPrice,
            this.memberPrice,
            this.purchasePrice,
            code,
            name,
            type,
            isMedical
        );
    }
    
    /**
     * 检查是否有价格信息
     */
    public boolean hasPriceInfo() {
        return retailPrice != null || memberPrice != null || purchasePrice != null;
    }
    
    /**
     * 检查是否有医保信息
     */
    public boolean hasMedicalInsuranceInfo() {
        return medicalInsuranceCode != null || medicalInsuranceName != null;
    }
    
    /**
     * 构建器类
     */
    public static class Builder {
        private BigDecimal retailPrice;
        private BigDecimal memberPrice;
        private BigDecimal purchasePrice;
        private String medicalInsuranceCode;
        private String medicalInsuranceName;
        private String medicalInsuranceType;
        private Boolean isMedicalInsurance = false;
        
        public Builder retailPrice(BigDecimal retailPrice) {
            this.retailPrice = retailPrice;
            return this;
        }
        
        public Builder memberPrice(BigDecimal memberPrice) {
            this.memberPrice = memberPrice;
            return this;
        }
        
        public Builder purchasePrice(BigDecimal purchasePrice) {
            this.purchasePrice = purchasePrice;
            return this;
        }
        
        public Builder medicalInsuranceCode(String medicalInsuranceCode) {
            this.medicalInsuranceCode = medicalInsuranceCode;
            return this;
        }
        
        public Builder medicalInsuranceName(String medicalInsuranceName) {
            this.medicalInsuranceName = medicalInsuranceName;
            return this;
        }
        
        public Builder medicalInsuranceType(String medicalInsuranceType) {
            this.medicalInsuranceType = medicalInsuranceType;
            return this;
        }
        
        public Builder isMedicalInsurance(Boolean isMedicalInsurance) {
            this.isMedicalInsurance = isMedicalInsurance;
            return this;
        }
        
        public ProductUseInfo build() {
            return new ProductUseInfo(retailPrice, memberPrice, purchasePrice,
                                    medicalInsuranceCode, medicalInsuranceName, 
                                    medicalInsuranceType, isMedicalInsurance);
        }
    }
}