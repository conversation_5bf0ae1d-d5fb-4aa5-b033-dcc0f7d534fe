package com.xyy.saas.inquiry.product.server.application.step;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.product.api.product.dto.ProductFlag;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.server.application.context.ProductSaveContext;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductInfoDO;
import com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoMapper;
import com.xyy.saas.inquiry.product.server.service.gsp.ProductPriceAdjustmentRecordService;
import com.xyy.saas.inquiry.product.server.service.gsp.ProductQualityChangeRecordService;
import com.xyy.saas.inquiry.util.PrefUtil;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

/**
 * 商品持久化步骤
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
@Order(600)
public class Step600AsProductPersistence implements ProductSaveStep {
    
    @Resource
    private ProductInfoMapper productInfoMapper;
    
    @Resource
    private ProductQualityChangeRecordService qualityChangeRecordService;
    
    @Resource
    private ProductPriceAdjustmentRecordService priceAdjustmentRecordService;
    
    @Override
    public boolean isApplicable(ProductSaveContext context) {
        // 总是适用
        return true;
    }
    
    @Override
    public void execute(ProductSaveContext context) {
        log.info("[Step600AsProductPersistence] 开始持久化商品信息");
        
        try {
            ProductInfoDO productInfo;
            
            if (context.getIsCreate()) {
                // 新建商品
                productInfo = createProduct(context);
            } else {
                // 更新商品
                productInfo = updateProduct(context);
            }
            
            // 设置结果
            context.setProductId(productInfo.getId());
            
            // 更新DTO中的基本信息
            context.getCurrentDto().setId(productInfo.getId());
            context.getCurrentDto().setPref(productInfo.getPref());
            context.getCurrentDto().setShowPref(productInfo.getShowPref());
            context.getCurrentDto().setMnemonicCode(productInfo.getMnemonicCode());
            
            log.info("[Step600AsProductPersistence] 商品持久化完成, productId={}, pref={}",
                productInfo.getId(), productInfo.getPref());
            
        } catch (Exception e) {
            log.error("[Step600AsProductPersistence] 商品持久化失败", e);
            context.markFailure("商品持久化失败: " + e.getMessage());
        }
    }
    
    /**
     * 创建商品
     */
    private ProductInfoDO createProduct(ProductSaveContext context) {
        log.info("[Step600AsProductPersistence] 创建新商品");
        
        ProductInfoDO productInfo = assembleCreateProductInfo(context);
        productInfoMapper.insert(productInfo);
        
        return productInfo;
    }
    
    /**
     * 更新商品
     */
    private ProductInfoDO updateProduct(ProductSaveContext context) {
        log.info("[Step600AsProductPersistence] 更新商品信息");
        
        // 设置原商品的基本信息
        var originalProduct = context.getOriginalProduct();
        var currentDto = context.getCurrentDto();
        
        currentDto.setPref(originalProduct.getPref());
        currentDto.setShowPref(originalProduct.getShowPref());
        currentDto.setMnemonicCode(originalProduct.getMnemonicCode());
        
        // 生成GSP记录（质量变更记录和价格调整记录）
        qualityChangeRecordService.saveQualityChangeRecord(currentDto, originalProduct);
        priceAdjustmentRecordService.savePriceAdjustmentRecord(currentDto, originalProduct, List.of(currentDto.getTenantId()));
        
        // 更新商品信息
        ProductInfoDO productInfo = assembleUpdateProductInfo(context);
        productInfoMapper.updateById(productInfo);
        
        return productInfo;
    }
    
    /**
     * 组装创建商品模型
     */
    private ProductInfoDO assembleCreateProductInfo(ProductSaveContext context) {
        var dto = context.getCurrentDto();
        var bizType = context.getBizType();
        
        ProductInfoDO productInfo = BeanUtils.toBean(dto, ProductInfoDO.class);
        
        // 设置租户ID
        productInfo.setTenantId(Objects.requireNonNullElse(dto.getHeadTenantId(), dto.getTenantId()));
        
        // 生成商品编码
        if (dto.getShowPref() == null || dto.getShowPref().trim().isEmpty()) {
            String showPref = generateShowPref(context, bizType);
            productInfo.setShowPref(showPref);
        }
        
        // 校验商品编码唯一性
        validateProductShowPrefUnique(productInfo);
        
        // 设置商品内码
        productInfo.setPref(PrefUtil.getProductPref());
        
        // 生成助记码
        productInfo.calcMnemonicCode();
        
        // 多属性转换为数值
        long multiFlag = ProductFlag.toFlag(null, dto.getProductFlag());
        productInfo.setMultiFlag(multiFlag);
        
        return productInfo;
    }
    
    /**
     * 组装更新商品模型
     */
    private ProductInfoDO assembleUpdateProductInfo(ProductSaveContext context) {
        var dto = context.getCurrentDto();
        var originalProduct = context.getOriginalProduct();
        
        ProductInfoDO productInfo = BeanUtils.toBean(dto, ProductInfoDO.class);
        
        // 租户 + 商品编号 不允许变更
        productInfo.setTenantId(originalProduct.getTenantId());
        productInfo.setShowPref(originalProduct.getShowPref());
        
        // 多属性转换为数值: 非新增场景：需要合并原有多属性
        long multiFlag = ProductFlag.toFlag(originalProduct.getMultiFlag(), dto.getProductFlag());
        productInfo.setMultiFlag(multiFlag);
        
        return productInfo;
    }
    
    /**
     * 生成商品外码
     */
    private String generateShowPref(ProductSaveContext context, ProductBizTypeEnum bizType) {
        var dto = context.getCurrentDto();
        
        // 查询所有商品外码的供应器
        java.util.function.Supplier<List<String>> initAllPrefSp = () -> 
            productInfoMapper.listShowPref(dto.getHeadTenantId());
        
        // 拆零商品外码：源商品外码+CL
        if (bizType == ProductBizTypeEnum.UNBUNDLED_PRODUCT) {
            return PrefUtil.getUnbundledProductShortPref(
                dto.getHeadTenantId(), 
                dto.getSourceProductShowPref(), 
                initAllPrefSp
            );
        } else {
            return PrefUtil.getProductShortPref(dto.getHeadTenantId(), initAllPrefSp);
        }
    }
    
    /**
     * 校验商品外码唯一性
     */
    private void validateProductShowPrefUnique(ProductInfoDO productInfo) {
        if (productInfo == null) {
            return;
        }
        
        // 机构+外码唯一性校验（忽略当前id）
        ProductInfoDO exists = productInfoMapper.uniqueIndexExists(
            productInfo.getTenantId(), 
            productInfo.getShowPref(), 
            null
        );
        
        if (exists != null && !Objects.equals(productInfo.getId(), exists.getId())) {
            throw new RuntimeException("商品外码重复: " + productInfo.getShowPref());
        }
    }
    
    @Override
    public String getStepDescription() {
        return "持久化商品基本信息";
    }
}