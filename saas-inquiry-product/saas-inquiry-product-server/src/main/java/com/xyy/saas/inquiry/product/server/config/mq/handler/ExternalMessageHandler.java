package com.xyy.saas.inquiry.product.server.config.mq.handler;

import org.apache.rocketmq.client.consumer.listener.ConsumeConcurrentlyStatus;
import org.apache.rocketmq.common.message.MessageExt;
import java.util.List;

/**
 * 外部RocketMQ消息处理接口
 */
public interface ExternalMessageHandler {

    /**
     * 处理消息
     *
     * @param msgList 消息列表
     * @throws Exception 处理异常
     */
    ConsumeConcurrentlyStatus handleMessage(List<MessageExt> msgList) throws Exception;
} 