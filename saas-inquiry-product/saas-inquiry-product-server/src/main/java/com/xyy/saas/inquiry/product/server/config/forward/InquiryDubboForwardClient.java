
package com.xyy.saas.inquiry.product.server.config.forward;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidGeneralMatchProductNewDto;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidGeneralProductDto;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidGeneralProductPresentDto;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidPictureProResult;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidResponse;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidResponse2;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidResponse3;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidSaasWithMixSearchInfoDto;
import com.xyy.saas.inquiry.product.server.config.forward.dto.MidTotalDictionaryReadDto;
import com.xyy.saas.inquiry.product.server.config.forward.dto.Param;
import java.util.List;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

/**
 * @Author:chenxiaoyi
 * @Date:2024/11/27 20:41
 */


@HttpExchange(accept = "application/json", contentType = "application/json")
public interface InquiryDubboForwardClient {

    /**
     * 转发 快速录入搜索中台标准库商品信息
     * @param param
     * @return
     */
    @PostExchange("/dubbo/generic/me/com.xyy.me.product.saas.api.facade.SaasFacade/1.0.0/fuzzySearchPlus")
    MidResponse<MidSaasWithMixSearchInfoDto> fuzzySearchPlus(@RequestBody Param param);


    /**
     * 转发 中台标准库id列表批量查询中台标准库商品信息
     * @param param
     * @return
     */
    @PostExchange("/dubbo/generic/me/com.xyy.me.product.saas.api.facade.SaasFacade/1.0.0/getInfoByStandardLibIdList")
    MidResponse<MidSaasWithMixSearchInfoDto> getInfoByStandardLibIdList(@RequestBody Param param);

    /**
     * 转发 匹配中台标准库商品信息
     * @param param
     * @return
     */
    @PostExchange("/dubbo/generic/me/com.xyy.me.product.general.api.newer.facade.product.sku.NewerSkuGenReadApi/1.0.0/getGeneralMatchProductNew")
    MidResponse2<List<MidGeneralMatchProductNewDto>> getGeneralMatchProductNew(@RequestBody Param param);

    /**
     * 转发 新品提报
     * @param param
     * @return
     */
    @PostExchange("/dubbo/generic/me/com.xyy.me.product.general.api.newer.facade.product.present.NewerProductPresentGenApi/1.0.0/addProductPresent")
    MidResponse2<List<MidGeneralProductPresentDto>> addProductPresent(@RequestBody Param param);

    /**
     * 转发 三方外采数据上报
     * @param param
     * @return
     */
    @PostExchange("/dubbo/generic/me/com.xyy.me.product.general.api.facade.product.present.ProductPresentGenApi/1.0.0/addProductPresentAndErpInfo")
    MidResponse2<Boolean> addProductPresentAndErpInfo(@RequestBody Param param);

    /**
     * 转发 批量查询中台标品
     * @param param
     * @return
     */
    @PostExchange("/dubbo/generic/me/com.xyy.me.product.general.api.facade.product.sku.SkuGenReadApi/1.0.0/getGeneralProductList")
    MidResponse2<List<MidGeneralProductDto>> getGeneralProductList(@RequestBody Param param);

    /**
     * 转发 批量查询中台标品图片
     * @param param
     * @return
     */
    @PostExchange("/dubbo/generic/me/com.xyy.me.product.general.api.facade.picture.ProductPictureFacade/1.0.0/findProPicture")
    MidResponse3<List<MidPictureProResult>> findProPicture(@RequestBody Param param);

    /**
     * 转发 分页查询中台字典数据
     * @param param
     * @return
     */
    @PostExchange("/dubbo/generic/me/com.xyy.me.product.general.api.facade.dictionary.DictionaryFacade/1.0.0/pageQueryDictionaryUpgrade")
    MidResponse2<PageResult<MidTotalDictionaryReadDto>> pageQueryDictionaryUpgrade(@RequestBody Param param);



}
