package com.xyy.saas.inquiry.product.server.config.forward.dto;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
@Accessors(chain = true)
public class Param {
    private String[] typeList;
    private Object[] valueList;

    public Param typeList(String... typeList) {
        this.typeList = typeList;
        return this;
    }

    public Param valueList(Object... valueList) {
        this.valueList = valueList;
        return this;
    }
}