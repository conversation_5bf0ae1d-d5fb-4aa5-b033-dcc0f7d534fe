package com.xyy.saas.inquiry.product.server.config.forward.dto;


import com.xyy.saas.inquiry.product.api.product.dto.ProductFlag;
import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
import java.util.Map;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class MidGeneralProductDto implements Serializable {
    @Serial
    private static final long serialVersionUID = -3823274251185617532L;
    private String businessCode;
    private String originalProductCode;
    private String productId;
    private String spuCode;
    private String generalName;
    private String generalNameCode;
    private Integer productType;
    private Integer spuCategory;
    private String spuCategoryName;
    private String instructionSpec;
    private String approvalNo;
    private String manufacturingLicenseNo;
    private Integer manufacturer;
    private String manufacturerName;
    private String erpManufacturerName;
    private String productionAddress;
    private String brandEnterprise;
    private Integer dosageForm;
    private String dosageFormName;
    private String businessScopeMulti;
    private String businessScopeMultiName;
    private Integer storageCond;
    private String storageCondName;
    private Byte shadingAttr;
    private String storage;
    private String originPlace;
    private String specialAttr;
    private String specialAttrName;
    private Integer firstCategory;
    private String firstCategoryName;
    private Integer secondCategory;
    private String secondCategoryName;
    private Integer thirdCategory;
    private String thirdCategoryName;
    private Integer fourthCategory;
    private String fourthCategoryName;
    private Integer fiveCategory;
    private String fiveCategoryName;
    private Integer sixCategory;
    private String sixCategoryName;
    private String categoryCode;
    private String taxCategoryCode;
    private Integer inRate;
    private String inRateName;
    private Integer outRate;
    private String outRateName;
    private String marketAuthor;
    private String marketAuthorAddress;
    private Integer skuCount;
    private Byte whetherSupervision;
    private String standardCodes;
    private String remark;
    private String businessName;
    private String businessNameCode;
    private String businessAliasText;
    private String businessAliasCodeText;
    private String spec;
    private Integer packageUnit;
    private String packageUnitName;
    private Integer prescriptionCategory;
    private String prescriptionCategoryName;
    private String smallPackageCode;
    private String mediumPackageCode;
    private String piecePackageCode;
    private String brand;
    private Short validity;
    private Byte validityUnit;
    private Byte delegationProduct;
    private Integer entrustedManufacturer;
    private String entrustedManufacturerName;
    private String delegationProductAddress;
    private String generalManufacturerName;
    private Integer generalManufactureId;
    private String suggestedPrice;
    private Short businessDisableStatus;
    private String businessSource;
    private Short preOperateStatus;
    private String qualityStandard;
    private Integer brandCategory;
    private String brandCategoryName;
    private String sauAttr;
    private String attributeText;
    private String createInstitutionName;
    private String treatSymptom;
    private Short patentMedicineType;
    private Short madeType;
    private Short medicationCrowd;
    private Integer medicationMethod;
    private String medicationMethodName;
    private Integer medicationFrequency;
    private String medicationFrequencyName;
    private String medicationDosage;
    private Integer miniUnit;
    private String miniUnitName;
    private String ingredient;
    private String indication;
    private String usageDosage;
    private String adverseReaction;
    private String precautions;
    private String taboos;
    private String extendRemark;
    private Integer prescription;
    private Integer medicinesEphedrine;
    private Byte chronicDiseasesVariety;
    private Byte conEvaluateVariety;
    private Byte hospitalvVariety;
    private Integer manufacturerCategory;
    private String manufacturerCategoryName;
    private Map<String, String> sauAttrMap;
    private Integer skuPrimaryType;
    private Boolean UnreasonableProduct = false;
    private Integer isWashed;
    private String colorName;
    private String tasteName;
    private String sizeName;
    private String packageUnitStr;
    private String prescriptionCategoryStr;
    private String entrustedManufacturerStr;
    private String storageCondStr;
    private String filingsAuthor;
    private Integer disableType;
    private String disableTypeName;
    private String disableNote;

    /**
     * 转换为标准库的多标志
     *
     * @return 标准库的多标志
     */
    public ProductFlag toStdlibMultiFlag() {
        // 中台停用状态(0-停用 1-启用 2-删除)
        return new ProductFlag()
                .setMidDeactivated(this.businessDisableStatus != 1);
    }
}
