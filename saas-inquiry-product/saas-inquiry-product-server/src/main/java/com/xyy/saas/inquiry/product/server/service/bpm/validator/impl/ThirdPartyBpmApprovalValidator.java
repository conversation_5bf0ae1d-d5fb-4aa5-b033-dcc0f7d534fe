package com.xyy.saas.inquiry.product.server.service.bpm.validator.impl;

import com.xyy.saas.inquiry.product.enums.BpmBusinessTypeEnum;
import com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo.BpmApprovalValidateResult;
import com.xyy.saas.inquiry.product.server.service.bpm.validator.BpmApprovalValidator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Map;

/**
 * 三方确认验证器实现
 */
@Component
@Slf4j
public class ThirdPartyBpmApprovalValidator implements BpmApprovalValidator {
    
    @Override
    public BpmApprovalValidateResult validate(String taskId, Long userId, Map<String, Object> params) {
        String token = (String) params.get("token");
        if (StringUtils.isEmpty(token)) {
            return BpmApprovalValidateResult.error("授权令牌不能为空");
        }
        
        // TODO: 调用三方服务验证授权令牌
        // 这里模拟验证逻辑
        if (!"valid_token".equals(token)) {
            return BpmApprovalValidateResult.error("授权令牌无效");
        }
        
        return BpmApprovalValidateResult.success();
    }
    
    @Override
    public BpmBusinessTypeEnum[] supportBusinessTypes() {
        return new BpmBusinessTypeEnum[]{
            BpmBusinessTypeEnum.PRODUCT_FIRST_APPROVE,
            BpmBusinessTypeEnum.PRODUCT_TO_HEADQUARTERS_APPROVE
        };
    }
    
    @Override
    public String getType() {
        return "third_party";
    }
} 