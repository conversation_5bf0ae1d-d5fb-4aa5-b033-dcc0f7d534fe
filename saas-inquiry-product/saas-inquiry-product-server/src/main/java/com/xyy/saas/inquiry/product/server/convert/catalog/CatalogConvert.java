package com.xyy.saas.inquiry.product.server.convert.catalog;

import com.xyy.saas.inquiry.product.api.catalog.dto.CatalogRespDTO;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.CatalogRespVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.CatalogDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

@Mapper
public interface CatalogConvert {

    CatalogConvert INSTANCE = Mappers.getMapper(CatalogConvert.class);

    List<CatalogRespDTO> convertDOList2DTOList(List<CatalogDO> catalogDOList);

    List<CatalogRespVO> convertDOList2VOList(List<CatalogDO> catalogDOList);

}
