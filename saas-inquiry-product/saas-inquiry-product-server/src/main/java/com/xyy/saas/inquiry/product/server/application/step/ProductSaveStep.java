package com.xyy.saas.inquiry.product.server.application.step;

import com.xyy.saas.inquiry.product.server.application.context.ProductSaveContext;

/**
 * 商品保存步骤接口
 * 
 * <AUTHOR> Assistant
 */
public interface ProductSaveStep {
    
    /**
     * 检查步骤是否适用于当前上下文
     * 
     * @param context 处理上下文
     * @return true if applicable
     */
    boolean isApplicable(ProductSaveContext context);
    
    /**
     * 执行处理步骤
     * 
     * @param context 处理上下文
     */
    void execute(ProductSaveContext context);
    
    /**
     * 获取步骤名称（用于日志和调试）
     */
    default String getStepName() {
        return this.getClass().getSimpleName();
    }
    
    /**
     * 获取步骤描述
     */
    default String getStepDescription() {
        return getStepName();
    }
}