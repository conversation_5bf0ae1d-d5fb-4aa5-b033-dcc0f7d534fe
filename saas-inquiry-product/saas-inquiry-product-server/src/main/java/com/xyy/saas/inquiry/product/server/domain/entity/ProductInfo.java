package com.xyy.saas.inquiry.product.server.domain.entity;

import com.xyy.saas.inquiry.product.enums.ProductStatusEnum;
import com.xyy.saas.inquiry.product.server.domain.valueobject.ProductFlag;
import com.xyy.saas.inquiry.product.server.domain.valueobject.ProductId;
import lombok.EqualsAndHashCode;
import lombok.Getter;

import java.math.BigDecimal;
import java.time.LocalDate;

/**
 * 商品基本信息实体
 * 
 * <AUTHOR> Assistant
 */
@Getter
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
public class ProductInfo {
    
    /**
     * 实体标识
     */
    @EqualsAndHashCode.Include
    private final ProductId productId;
    
    /**
     * 商品外码（显示编码）
     */
    private final String showPref;
    
    /**
     * 助记码
     */
    private final String mnemonicCode;
    
    /**
     * 通用名
     */
    private final String commonName;
    
    /**
     * 品牌名
     */
    private final String brandName;
    
    /**
     * 规格/型号
     */
    private final String spec;
    
    /**
     * 条形码
     */
    private final String barcode;
    
    /**
     * 生产厂家
     */
    private final String manufacturer;
    
    /**
     * 批准文号
     */
    private final String approvalNumber;
    
    /**
     * 最小包装数量
     */
    private final BigDecimal minPackageNum;
    
    /**
     * 商品大类
     */
    private final String spuCategory;
    
    /**
     * 单位
     */
    private final String unit;
    
    /**
     * 剂型
     */
    private final String dosageForm;
    
    /**
     * 所属范围
     */
    private final String businessScope;
    
    /**
     * 处方分类
     */
    private final String presCategory;
    
    /**
     * 储存条件
     */
    private final String storageWay;
    
    /**
     * 产地
     */
    private final String origin;
    
    /**
     * 生产企业社会信用代码
     */
    private final String manufacturerUscc;
    
    /**
     * 有效期
     */
    private final String productValidity;
    
    /**
     * 批准文号有效期
     */
    private final LocalDate approvalValidityPeriod;
    
    /**
     * 上市许可持有人
     */
    private final String marketingAuthorityHolder;
    
    /**
     * 药品标识码
     */
    private final String drugIdentCode;
    
    /**
     * 商品状态
     */
    private final ProductStatusEnum status;
    
    /**
     * 商品标志
     */
    private final ProductFlag productFlag;
    
    /**
     * 拆零数量
     */
    private final Integer unbundledQuantity;
    
    /**
     * 源商品编码（用于拆零商品）
     */
    private final String sourceProductPref;
    
    /**
     * 备注
     */
    private final String remark;
    
    private ProductInfo(Builder builder) {
        this.productId = builder.productId;
        this.showPref = builder.showPref;
        this.mnemonicCode = builder.mnemonicCode;
        this.commonName = builder.commonName;
        this.brandName = builder.brandName;
        this.spec = builder.spec;
        this.barcode = builder.barcode;
        this.manufacturer = builder.manufacturer;
        this.approvalNumber = builder.approvalNumber;
        this.minPackageNum = builder.minPackageNum;
        this.spuCategory = builder.spuCategory;
        this.unit = builder.unit;
        this.dosageForm = builder.dosageForm;
        this.businessScope = builder.businessScope;
        this.presCategory = builder.presCategory;
        this.storageWay = builder.storageWay;
        this.origin = builder.origin;
        this.manufacturerUscc = builder.manufacturerUscc;
        this.productValidity = builder.productValidity;
        this.approvalValidityPeriod = builder.approvalValidityPeriod;
        this.marketingAuthorityHolder = builder.marketingAuthorityHolder;
        this.drugIdentCode = builder.drugIdentCode;
        this.status = builder.status;
        this.productFlag = builder.productFlag;
        this.unbundledQuantity = builder.unbundledQuantity;
        this.sourceProductPref = builder.sourceProductPref;
        this.remark = builder.remark;
    }
    
    /**
     * 创建构建器
     */
    public static Builder builder() {
        return new Builder();
    }
    
    /**
     * 更新状态
     */
    public ProductInfo withStatus(ProductStatusEnum newStatus) {
        return this.toBuilder().status(newStatus).build();
    }
    
    /**
     * 更新商品标志
     */
    public ProductInfo withProductFlag(ProductFlag newFlag) {
        return this.toBuilder().productFlag(newFlag).build();
    }
    
    /**
     * 转换为构建器（用于更新）
     */
    public Builder toBuilder() {
        return new Builder()
            .productId(this.productId)
            .showPref(this.showPref)
            .mnemonicCode(this.mnemonicCode)
            .commonName(this.commonName)
            .brandName(this.brandName)
            .spec(this.spec)
            .barcode(this.barcode)
            .manufacturer(this.manufacturer)
            .approvalNumber(this.approvalNumber)
            .minPackageNum(this.minPackageNum)
            .spuCategory(this.spuCategory)
            .unit(this.unit)
            .dosageForm(this.dosageForm)
            .businessScope(this.businessScope)
            .presCategory(this.presCategory)
            .storageWay(this.storageWay)
            .origin(this.origin)
            .manufacturerUscc(this.manufacturerUscc)
            .productValidity(this.productValidity)
            .approvalValidityPeriod(this.approvalValidityPeriod)
            .marketingAuthorityHolder(this.marketingAuthorityHolder)
            .drugIdentCode(this.drugIdentCode)
            .status(this.status)
            .productFlag(this.productFlag)
            .unbundledQuantity(this.unbundledQuantity)
            .sourceProductPref(this.sourceProductPref)
            .remark(this.remark);
    }
    
    /**
     * 检查是否为拆零商品
     */
    public boolean isUnbundledProduct() {
        return sourceProductPref != null && unbundledQuantity != null;
    }
    
    /**
     * 检查商品是否可用
     */
    public boolean isUsable() {
        return status == ProductStatusEnum.USING;
    }
    
    /**
     * 检查商品是否在审核中
     */
    public boolean isInAudit() {
        return status == ProductStatusEnum.FIRST_AUDITING || 
               status == ProductStatusEnum.HEAD_AUDITING ||
               status == ProductStatusEnum.MID_AUDITING;
    }
    
    /**
     * 构建器类
     */
    public static class Builder {
        private ProductId productId;
        private String showPref;
        private String mnemonicCode;
        private String commonName;
        private String brandName;
        private String spec;
        private String barcode;
        private String manufacturer;
        private String approvalNumber;
        private BigDecimal minPackageNum;
        private String spuCategory;
        private String unit;
        private String dosageForm;
        private String businessScope;
        private String presCategory;
        private String storageWay;
        private String origin;
        private String manufacturerUscc;
        private String productValidity;
        private LocalDate approvalValidityPeriod;
        private String marketingAuthorityHolder;
        private String drugIdentCode;
        private ProductStatusEnum status;
        private ProductFlag productFlag;
        private Integer unbundledQuantity;
        private String sourceProductPref;
        private String remark;
        
        public Builder productId(ProductId productId) {
            this.productId = productId;
            return this;
        }
        
        public Builder showPref(String showPref) {
            this.showPref = showPref;
            return this;
        }
        
        public Builder mnemonicCode(String mnemonicCode) {
            this.mnemonicCode = mnemonicCode;
            return this;
        }
        
        public Builder commonName(String commonName) {
            this.commonName = commonName;
            return this;
        }
        
        public Builder brandName(String brandName) {
            this.brandName = brandName;
            return this;
        }
        
        public Builder spec(String spec) {
            this.spec = spec;
            return this;
        }
        
        public Builder barcode(String barcode) {
            this.barcode = barcode;
            return this;
        }
        
        public Builder manufacturer(String manufacturer) {
            this.manufacturer = manufacturer;
            return this;
        }
        
        public Builder approvalNumber(String approvalNumber) {
            this.approvalNumber = approvalNumber;
            return this;
        }
        
        public Builder minPackageNum(BigDecimal minPackageNum) {
            this.minPackageNum = minPackageNum;
            return this;
        }
        
        public Builder spuCategory(String spuCategory) {
            this.spuCategory = spuCategory;
            return this;
        }
        
        public Builder unit(String unit) {
            this.unit = unit;
            return this;
        }
        
        public Builder dosageForm(String dosageForm) {
            this.dosageForm = dosageForm;
            return this;
        }
        
        public Builder businessScope(String businessScope) {
            this.businessScope = businessScope;
            return this;
        }
        
        public Builder presCategory(String presCategory) {
            this.presCategory = presCategory;
            return this;
        }
        
        public Builder storageWay(String storageWay) {
            this.storageWay = storageWay;
            return this;
        }
        
        public Builder origin(String origin) {
            this.origin = origin;
            return this;
        }
        
        public Builder manufacturerUscc(String manufacturerUscc) {
            this.manufacturerUscc = manufacturerUscc;
            return this;
        }
        
        public Builder productValidity(String productValidity) {
            this.productValidity = productValidity;
            return this;
        }
        
        public Builder approvalValidityPeriod(LocalDate approvalValidityPeriod) {
            this.approvalValidityPeriod = approvalValidityPeriod;
            return this;
        }
        
        public Builder marketingAuthorityHolder(String marketingAuthorityHolder) {
            this.marketingAuthorityHolder = marketingAuthorityHolder;
            return this;
        }
        
        public Builder drugIdentCode(String drugIdentCode) {
            this.drugIdentCode = drugIdentCode;
            return this;
        }
        
        public Builder status(ProductStatusEnum status) {
            this.status = status;
            return this;
        }
        
        public Builder productFlag(ProductFlag productFlag) {
            this.productFlag = productFlag;
            return this;
        }
        
        public Builder unbundledQuantity(Integer unbundledQuantity) {
            this.unbundledQuantity = unbundledQuantity;
            return this;
        }
        
        public Builder sourceProductPref(String sourceProductPref) {
            this.sourceProductPref = sourceProductPref;
            return this;
        }
        
        public Builder remark(String remark) {
            this.remark = remark;
            return this;
        }
        
        public ProductInfo build() {
            // 基本验证
            if (productId == null) {
                throw new IllegalArgumentException("ProductId cannot be null");
            }
            if (commonName == null || commonName.trim().isEmpty()) {
                throw new IllegalArgumentException("CommonName cannot be null or empty");
            }
            
            return new ProductInfo(this);
        }
    }
}