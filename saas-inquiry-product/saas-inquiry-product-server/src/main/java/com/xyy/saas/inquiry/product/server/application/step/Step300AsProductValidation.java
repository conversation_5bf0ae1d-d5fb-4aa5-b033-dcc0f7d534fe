package com.xyy.saas.inquiry.product.server.application.step;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.product.enums.ErrorCodeConstants.PRODUCT_INFO_NOT_EXISTS;

import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.server.application.context.ProductSaveContext;
import com.xyy.saas.inquiry.product.server.service.product.ProductInfoService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;

/**
 * 商品数据验证步骤
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
@Order(300)
public class Step300AsProductValidation implements ProductSaveStep {
    
    @Resource
    private ProductInfoService productInfoService;
    
    @Override
    public boolean isApplicable(ProductSaveContext context) {
        // 总是适用
        return true;
    }
    
    @Override
    public void execute(ProductSaveContext context) {
        log.info("[Step300AsProductValidation] 开始验证商品数据");
        
        try {
            // 1. 基础数据验证
            validateBasicData(context);
            
            // 2. 更新场景验证
            if (!context.getIsCreate()) {
                validateUpdateScenario(context);
            }
            
            // 3. 业务规则验证
            validateBusinessRules(context);
            
            log.info("[Step300AsProductValidation] 商品数据验证完成");
            
        } catch (Exception e) {
            log.error("[Step300AsProductValidation] 商品数据验证失败", e);
            context.markFailure("商品数据验证失败: " + e.getMessage());
        }
    }
    
    /**
     * 基础数据验证
     */
    private void validateBasicData(ProductSaveContext context) {
        ProductInfoDto dto = context.getCurrentDto();
        
        // 必填字段验证
        if (dto.getCommonName() == null || dto.getCommonName().trim().isEmpty()) {
            throw new IllegalArgumentException("商品通用名不能为空");
        }
        
        if (dto.getManufacturer() == null || dto.getManufacturer().trim().isEmpty()) {
            throw new IllegalArgumentException("生产厂家不能为空");
        }
        
        if (dto.getUnit() == null || dto.getUnit().trim().isEmpty()) {
            throw new IllegalArgumentException("商品单位不能为空");
        }
        
        log.debug("[Step300AsProductValidation] 基础数据验证通过");
    }
    
    /**
     * 更新场景验证
     */
    private void validateUpdateScenario(ProductSaveContext context) {
        ProductInfoDto dto = context.getCurrentDto();
        
        // 查询原始商品信息
        ProductInfoDto originalProduct = productInfoService.getProductInfo(dto.getId(), context.getTenant());
        if (originalProduct == null) {
            throw exception(PRODUCT_INFO_NOT_EXISTS);
        }
        
        // 保存原始商品信息到上下文
        context.setOriginalProduct(originalProduct);
        
        log.debug("[Step300AsProductValidation] 更新场景验证通过, 原商品状态: {}", originalProduct.getStatus());
    }
    
    /**
     * 业务规则验证
     */
    private void validateBusinessRules(ProductSaveContext context) {
        ProductInfoDto dto = context.getCurrentDto();
        
        // 禁采状态转换验证
        if (dto.getPurchaseDisabled() != null) {
            // 设置商品标志
            var productFlag = dto.getProductFlag();
            if (productFlag == null) {
                productFlag = new com.xyy.saas.inquiry.product.api.product.dto.ProductFlag();
            }
            dto.setProductFlag(productFlag.mappingPurchaseDisabled(dto.getPurchaseDisabled()));
        }
        
        // 商品数量限制验证（新建场景）
        if (context.getIsCreate()) {
            productInfoService.validTenantProductCountLimit(dto.getTenantId(), 1);
        }
        
        log.debug("[Step300AsProductValidation] 业务规则验证通过");
    }
    
    @Override
    public String getStepDescription() {
        return "验证商品数据的完整性和业务规则";
    }
}