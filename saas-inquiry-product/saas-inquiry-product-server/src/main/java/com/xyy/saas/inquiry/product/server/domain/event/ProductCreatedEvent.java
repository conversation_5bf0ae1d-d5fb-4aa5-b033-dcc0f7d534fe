package com.xyy.saas.inquiry.product.server.domain.event;

import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.enums.ProductStatusEnum;
import com.xyy.saas.inquiry.product.server.domain.valueobject.TenantId;
import lombok.Getter;

/**
 * 商品创建事件
 * 
 * <AUTHOR> Assistant
 */
@Getter
public class ProductCreatedEvent extends ProductDomainEvent {
    
    /**
     * 业务类型
     */
    private final ProductBizTypeEnum bizType;
    
    /**
     * 商品状态
     */
    private final ProductStatusEnum status;
    
    /**
     * 商品通用名
     */
    private final String commonName;
    
    /**
     * 品牌名
     */
    private final String brandName;
    
    /**
     * 规格
     */
    private final String spec;
    
    /**
     * 生产厂家
     */
    private final String manufacturer;
    
    /**
     * 总部租户ID
     */
    private final TenantId headTenantId;
    
    public ProductCreatedEvent(String productPref, TenantId tenantId, TenantId headTenantId,
                              ProductBizTypeEnum bizType, ProductStatusEnum status,
                              String commonName, String brandName, String spec, String manufacturer) {
        super(productPref, tenantId);
        this.bizType = bizType;
        this.status = status;
        this.commonName = commonName;
        this.brandName = brandName;
        this.spec = spec;
        this.manufacturer = manufacturer;
        this.headTenantId = headTenantId;
    }
    
    @Override
    public String getEventType() {
        return "ProductCreated";
    }
    
    /**
     * 检查是否需要上报中台
     */
    public boolean needReportToMid() {
        return bizType == ProductBizTypeEnum.MID_STDLIB_ADD || 
               bizType == ProductBizTypeEnum.INQUIRY_PRESENT;
    }
    
    /**
     * 检查是否需要创建审批流
     */
    public boolean needCreateApproval() {
        return status == ProductStatusEnum.FIRST_AUDITING ||
               status == ProductStatusEnum.HEAD_AUDITING ||
               status == ProductStatusEnum.MID_AUDITING;
    }
    
    /**
     * 检查是否为连锁门店创建
     */
    public boolean isChainStoreCreation() {
        return bizType == ProductBizTypeEnum.CHAIN_STORE_ADD;
    }
}