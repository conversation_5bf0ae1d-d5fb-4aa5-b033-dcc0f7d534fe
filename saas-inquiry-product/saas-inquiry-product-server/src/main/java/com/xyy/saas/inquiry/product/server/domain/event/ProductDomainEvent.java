package com.xyy.saas.inquiry.product.server.domain.event;

import com.xyy.saas.inquiry.product.server.domain.valueobject.TenantId;
import lombok.Getter;

import java.time.LocalDateTime;
import java.util.UUID;

/**
 * 商品领域事件基类
 * 
 * <AUTHOR> Assistant
 */
@Getter
public abstract class ProductDomainEvent {
    
    /**
     * 事件ID
     */
    private final String eventId;
    
    /**
     * 商品编码
     */
    private final String productPref;
    
    /**
     * 租户ID
     */
    private final TenantId tenantId;
    
    /**
     * 事件发生时间
     */
    private final LocalDateTime occurredAt;
    
    /**
     * 事件版本
     */
    private final int version;
    
    protected ProductDomainEvent(String productPref, TenantId tenantId) {
        this.eventId = UUID.randomUUID().toString();
        this.productPref = productPref;
        this.tenantId = tenantId;
        this.occurredAt = LocalDateTime.now();
        this.version = 1;
    }
    
    /**
     * 获取事件类型
     */
    public abstract String getEventType();
    
    /**
     * 检查事件是否为指定类型
     */
    public boolean isEventType(String eventType) {
        return getEventType().equals(eventType);
    }
}