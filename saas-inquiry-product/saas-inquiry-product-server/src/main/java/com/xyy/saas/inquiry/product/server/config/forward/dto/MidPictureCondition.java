package com.xyy.saas.inquiry.product.server.config.forward.dto;


import lombok.Data;
import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
public class MidPictureCondition implements Serializable {
    @Serial
    private static final long serialVersionUID = -5585054687183152246L;

    private List<String> productCodes;
    private List<String> productIds;
    private String mechanism;
    private Integer orgCodeType;
    private String warehouseNumber;
    private String pictureVersion;
}
