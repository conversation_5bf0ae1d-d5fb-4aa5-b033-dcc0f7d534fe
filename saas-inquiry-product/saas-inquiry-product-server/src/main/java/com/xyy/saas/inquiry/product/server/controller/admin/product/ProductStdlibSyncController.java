package com.xyy.saas.inquiry.product.server.controller.admin.product;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.product.server.service.product.ProductStdlibSyncService;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibSyncDto;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 商品标准库信息同步")
@RestController
@RequestMapping("/product/stdlib/sync")
@Validated
@Slf4j
public class ProductStdlibSyncController {

    @Resource
    private ProductStdlibSyncService stdlibSyncService;

    @GetMapping("/full")
    @Operation(summary = "全量同步中台标准库数据")
    @PreAuthorize("@ss.hasPermission('saas:product:stdlib:sync:create')")
    public CommonResult<ProductStdlibSyncDto> fullSyncFromMid() {
        ProductStdlibSyncDto progress = stdlibSyncService.startFullSync();
        return success(progress);
    }

    @PostMapping("/cancel")
    @Operation(summary = "取消同步任务")
    @PreAuthorize("@ss.hasPermission('saas:product:stdlib:sync:cancel')")
    public CommonResult<Boolean> cancelSync(@RequestParam String guid) {
        boolean result = stdlibSyncService.cancelSync(guid);
        return success(result);
    }

    @GetMapping("/progress")
    @Operation(summary = "查询同步进度")
    @PreAuthorize("@ss.hasPermission('saas:product:stdlib:sync:query')")
    public CommonResult<ProductStdlibSyncDto> getSyncProgress(@RequestParam(required = false) String guid) {
        ProductStdlibSyncDto progress;
        if (StringUtils.hasText(guid)) {
            progress = stdlibSyncService.getProgress(guid);
        } else {
            List<ProductStdlibSyncDto> progressList = stdlibSyncService.getProgressList();
            progress = !progressList.isEmpty() ? progressList.getFirst() : null;
        }
        return success(progress);
    }

    @GetMapping("/progress/list")
    @Operation(summary = "查询同步进度历史")
    @PreAuthorize("@ss.hasPermission('saas:product:stdlib:sync:query')")
    public CommonResult<List<ProductStdlibSyncDto>> getSyncProgressList() {
        return success(stdlibSyncService.getProgressList());
    }
}