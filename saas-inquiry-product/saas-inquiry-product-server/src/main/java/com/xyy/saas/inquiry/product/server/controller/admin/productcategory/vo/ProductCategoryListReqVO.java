package com.xyy.saas.inquiry.product.server.controller.admin.productcategory.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.time.LocalDateTime;
import org.springframework.format.annotation.DateTimeFormat;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商品六级分类列表 Request VO")
@Data
public class ProductCategoryListReqVO {

    @Schema(description = "分类名称", example = "张三")
    private String name;

    @Schema(description = "字典id（中台）", example = "20775")
    private Long dictId;

    @Schema(description = "字典父id（中台）", example = "6099")
    private Long parentDictId;

    @Schema(description = "排序权重")
    private Integer sortOrder;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}