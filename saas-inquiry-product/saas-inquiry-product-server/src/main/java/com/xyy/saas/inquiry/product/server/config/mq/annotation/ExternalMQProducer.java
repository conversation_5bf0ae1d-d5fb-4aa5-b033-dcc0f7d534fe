package com.xyy.saas.inquiry.product.server.config.mq.annotation;

import com.xyy.saas.inquiry.product.server.config.mq.enums.ExternalMQClusterEnum;
import org.springframework.stereotype.Component;

import java.lang.annotation.*;

/**
 * 接入外部RocketMQ（saas，中台等），原则上只允许消费，不允许发送
 */
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
@Component
@Deprecated
public @interface ExternalMQProducer {
    /**
     * Topic
     */
    String topic();

    /**
     * 集群
     */
    ExternalMQClusterEnum cluster();
} 