package com.xyy.saas.inquiry.product.server.application.strategy.impl;

import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.inquiry.product.enums.ProductBizTypeEnum;
import com.xyy.saas.inquiry.product.enums.ProductStatusEnum;
import com.xyy.saas.inquiry.product.server.application.strategy.ProductBizTypeHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 问诊临时建品处理器
 * 
 * <AUTHOR> Assistant
 */
@Slf4j
@Component
public class InquiryTemporaryHandler implements ProductBizTypeHandler {
    
    @Override
    public ProductBizTypeEnum getSupportedBizType() {
        return ProductBizTypeEnum.INQUIRY_ADD_TEMPORARY;
    }
    
    @Override
    public void handleProductStatusAndFlag(ProductInfoDto dto) {
        log.info("[InquiryTemporaryHandler] 处理问诊临时建品逻辑");
        
        // 设置为暂存状态
        dto.setStatus(ProductStatusEnum.TEMP.code);
        
        log.debug("[InquiryTemporaryHandler] 商品状态设置为暂存: {}", ProductStatusEnum.TEMP.code);
    }
}