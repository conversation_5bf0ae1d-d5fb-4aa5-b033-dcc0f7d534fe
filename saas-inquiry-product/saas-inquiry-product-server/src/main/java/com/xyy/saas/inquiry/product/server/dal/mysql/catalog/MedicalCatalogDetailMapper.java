package com.xyy.saas.inquiry.product.server.dal.mysql.catalog;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.product.api.catalog.dto.MedicalCatalogProductSearchDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductSearchDto;
import com.xyy.saas.inquiry.product.server.controller.admin.catalog.vo.MedicalCatalogDetailPageReqVO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.MedicalCatalogDetailDO;
import com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibDO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

/**
 * 医保目录明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MedicalCatalogDetailMapper extends BaseMapperX<MedicalCatalogDetailDO> {

    /**
     * 根据目录ID查询明细列表
     *
     * @param catalogId 目录ID
     * @return 明细列表
     */
    default List<MedicalCatalogDetailDO> selectListByCatalogId(Long catalogId) {
        return selectList(MedicalCatalogDetailDO::getCatalogId, catalogId);
    }

    /**
     * 根据项目编码列表查询明细
     *
     * @param catalogId 目录ID
     * @param projectCodes 项目编码列表
     * @return 明细列表
     */
    default List<MedicalCatalogDetailDO> selectListByProjectCodes(Long catalogId, List<String> projectCodes) {
        return selectList(new LambdaQueryWrapperX<MedicalCatalogDetailDO>()
            .eq(MedicalCatalogDetailDO::getCatalogId, catalogId)
            .in(MedicalCatalogDetailDO::getProjectCode, projectCodes));
    }

    /**
     * 根据目录ID删除明细
     *
     * @param catalogId 目录ID
     * @return 删除数量
     */
    default int deleteByCatalogId(Long catalogId) {
        return delete(MedicalCatalogDetailDO::getCatalogId, catalogId);
    }

    /**
     * 分页查询
     *
     * @param reqVO 查询条件
     * @return 分页结果
     */
    default PageResult<MedicalCatalogDetailDO> selectPage(MedicalCatalogDetailPageReqVO reqVO) {
        LambdaQueryWrapperX<MedicalCatalogDetailDO> wrapper = new LambdaQueryWrapperX<MedicalCatalogDetailDO>()
            .eqIfPresent(MedicalCatalogDetailDO::getCatalogId, reqVO.getCatalogId())
            .eqIfPresent(MedicalCatalogDetailDO::getId, reqVO.getId())
            .inIfPresent(MedicalCatalogDetailDO::getId, reqVO.getIdList())
            .eqIfPresent(MedicalCatalogDetailDO::getProjectCode, reqVO.getProjectCode())
            .eqIfPresent(MedicalCatalogDetailDO::getProjectName, reqVO.getProjectName())
            .eqIfPresent(MedicalCatalogDetailDO::getProjectType, reqVO.getProjectType())
            .eqIfPresent(MedicalCatalogDetailDO::getBrandName, reqVO.getBrandName())
            .eqIfPresent(MedicalCatalogDetailDO::getManufacturer, reqVO.getManufacturer())
            .eqIfPresent(MedicalCatalogDetailDO::getApprovalNumber, reqVO.getApprovalNumber())
            .eqIfPresent(MedicalCatalogDetailDO::getStandardCode, reqVO.getStandardCode())
            .eqIfPresent(MedicalCatalogDetailDO::getDisable, reqVO.getDisable())
            .orderByDesc(MedicalCatalogDetailDO::getId);

        if (StringUtils.isNotBlank(reqVO.getMixedNameQuery())) {
            wrapper.apply("MATCH (project_name, brand_name, mnemonic_code) AGAINST ({0,javaType=String,jdbcType=VARCHAR} IN BOOLEAN MODE)", reqVO.getMixedNameQuery());
        }
        if (StringUtils.isNotBlank(reqVO.getNatureNameQuery())) {
            wrapper.apply("MATCH (project_name, brand_name, mnemonic_code) AGAINST ({0,javaType=String,jdbcType=VARCHAR})", reqVO.getNatureNameQuery());
        }

        return selectPage(reqVO, wrapper);
    }


    /**
     * 批量插入
     *
     * @param list 数据列表
     */
    void batchInsertByXml(List<MedicalCatalogDetailDO> list);

    /**
     * 根据目录ID查询项目编码集合
     *
     * @param catalogId 目录ID
     * @return 项目编码集合
     */
    Set<String> getProjectCodeSetByCatalogId(Long catalogId);

    /**
     * 医保目录商品名称推荐搜索
     *
     * @return 推荐名称列表
     */
    List<String> selectSuggestNamesByProductsName(@Param("param") MedicalCatalogProductSearchDto param, @Param("limit") int limit, @Param("offset") Integer offset);

    /**
     * 医保目录商品搜索（支持规格过滤）
     *
     * @return 商品列表
     */
    List<MedicalCatalogDetailDO> selectProductsByNameSpec(@Param("param") MedicalCatalogProductSearchDto param, @Param("limit") int limit, @Param("offset") Integer offset);

}
