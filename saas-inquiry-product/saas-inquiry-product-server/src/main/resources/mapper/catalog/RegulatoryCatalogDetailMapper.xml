<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.product.server.dal.mysql.catalog.RegulatoryCatalogDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

  <resultMap id="BaseResultMap" type="com.xyy.saas.inquiry.product.server.dal.dataobject.catalog.RegulatoryCatalogDetailDO">
    <id column="id" property="id"/>
    <result property="catalogId" column="catalog_id"/>
    <result property="projectCode" column="project_code"/>
    <result property="commonName" column="common_name"/>
    <result property="brandName" column="brand_name"/>
    <result property="spec" column="spec"/>
    <result property="barcode" column="barcode"/>
    <result property="manufacturer" column="manufacturer"/>
    <result property="approvalNumber" column="approval_number"/>
    <result property="disable" column="disable"/>
    <result property="remark" column="remark"/>
    <result property="createTime" column="create_time"/>
    <result property="updateTime" column="update_time"/>
    <result property="deleted" column="deleted"/>
    <result property="creator" column="creator"/>
    <result property="updater" column="updater"/>
  </resultMap>

  <sql id="Base_Column_List">
    catalog_id, project_code, common_name, brand_name, spec, barcode, manufacturer, approval_number, disable, remark, create_time, update_time, deleted, creator, updater
  </sql>

  <insert id="batchInsertByXml" useGeneratedKeys="true" keyProperty="id">
    INSERT INTO saas_regulatory_catalog_detail
    (
    <include refid="Base_Column_List"/>
    )
    VALUES
    <foreach collection="list" index="index" item="item" open="" close="" separator=",">
      (
      #{item.catalogId,jdbcType=BIGINT},
      #{item.projectCode,jdbcType=BIGINT},
      #{item.commonName,jdbcType=VARCHAR},
      #{item.brandName,jdbcType=VARCHAR},
      #{item.spec,jdbcType=VARCHAR},
      #{item.barcode,jdbcType=VARCHAR},
      #{item.manufacturer,jdbcType=VARCHAR},
      #{item.approvalNumber,jdbcType=VARCHAR},
      #{item.disable,jdbcType=BIT},
      #{item.remark,jdbcType=VARCHAR},
      #{item.createTime,jdbcType=TIMESTAMP},
      #{item.updateTime,jdbcType=TIMESTAMP},
      0,
      #{item.creator,jdbcType=VARCHAR},
      #{item.updater,jdbcType=VARCHAR}
      )
    </foreach>
  </insert>

</mapper>