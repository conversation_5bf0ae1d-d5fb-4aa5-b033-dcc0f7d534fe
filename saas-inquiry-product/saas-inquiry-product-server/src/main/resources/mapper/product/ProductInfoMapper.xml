<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

  <resultMap id="BaseResultMap" type="com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductInfoDO">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="pref" jdbcType="VARCHAR" property="pref" />
    <result column="show_pref" jdbcType="VARCHAR" property="showPref" />
    <result column="source_product_pref" jdbcType="BIGINT" property="sourceProductPref" />
    <result column="stdlib_id" jdbcType="BIGINT" property="stdlibId" />
    <result column="mnemonic_code" jdbcType="VARCHAR" property="mnemonicCode" />
    <result column="common_name" jdbcType="VARCHAR" property="commonName" />
    <result column="brand_name" jdbcType="VARCHAR" property="brandName" />
    <result column="spec" jdbcType="VARCHAR" property="spec" />
    <result column="barcode" jdbcType="VARCHAR" property="barcode" />
    <result column="manufacturer" jdbcType="VARCHAR" property="manufacturer" />
    <result column="approval_number" jdbcType="VARCHAR" property="approvalNumber" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="input_tax_rate" jdbcType="DECIMAL" property="inputTaxRate" />
    <result column="output_tax_rate" jdbcType="DECIMAL" property="outputTaxRate" />
    <result column="unbundled_quantity" jdbcType="INTEGER" property="unbundledQuantity" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="disable" jdbcType="BIT" property="disable" />
    <result column="multi_flag" jdbcType="BIGINT" property="multiFlag" />
    <result column="deleted_at" jdbcType="TIMESTAMP" property="deletedAt" />
    <result column="delete_type" jdbcType="INTEGER" property="deleteType" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>

  <resultMap id="BaseResultMap2" type="com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductInfoDO2" extends="BaseResultMap">
    <!-- 商品使用信息关联映射 -->
    <association property="useInfo" javaType="com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductUseInfoDO">
        <result column="useInfo_product_pref" property="productPref"/>
        <result column="useInfo_tenant_id" property="tenantId"/>
        <result column="useInfo_head_tenant_id" property="headTenantId"/>
        <result column="useInfo_retail_price" property="retailPrice"/>
        <result column="useInfo_member_price" property="memberPrice"/>
        <result column="useInfo_medicare_project_code" property="medicareProjectCode"/>
        <result column="useInfo_medicare_project_name" property="medicareProjectName"/>
        <result column="useInfo_medicare_project_level" property="medicareProjectLevel"/>
        <result column="useInfo_medicare_match_status" property="medicareMatchStatus"/>
        <result column="useInfo_medicare_upload_status" property="medicareUploadStatus"/>
        <result column="useInfo_redo_data" property="redoData" typeHandler="com.xyy.saas.inquiry.annotation.JsonTypeHandler" />
        <result column="useInfo_remark" property="remark"/>
        <result column="useInfo_create_time" property="createTime"/>
        <result column="useInfo_update_time" property="updateTime"/>
        <result column="useInfo_creator" property="creator"/>
        <result column="useInfo_updater" property="updater"/>
        <result column="useInfo_deleted" property="deleted"/>
    </association>
    
    <!-- 商品标准库关联映射 -->
    <association property="stdlib" javaType="com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibDO">
        <result column="stdlib_id" property="id"/>
        <result column="stdlib_mid_stdlib_id" property="midStdlibId"/>
        <result column="stdlib_mid_stdlib_id_bak" property="midStdlibIdBak"/>
        <result column="stdlib_common_name" property="commonName"/>
        <result column="stdlib_brand_name" property="brandName"/>
        <result column="stdlib_spec" property="spec"/>
        <result column="stdlib_barcode" property="barcode"/>
        <result column="stdlib_manufacturer" property="manufacturer"/>
        <result column="stdlib_approval_number" property="approvalNumber"/>
        <result column="stdlib_key_point_hash" property="keyPointHash"/>
        <result column="stdlib_mnemonic_code" property="mnemonicCode"/>
        <result column="stdlib_min_package_num" property="minPackageNum"/>
        <result column="stdlib_spu_category" property="spuCategory"/>
        <result column="stdlib_unit" property="unit"/>
        <result column="stdlib_dosage_form" property="dosageForm"/>
        <result column="stdlib_business_scope" property="businessScope"/>
        <result column="stdlib_pres_category" property="presCategory"/>
        <result column="stdlib_storage_way" property="storageWay"/>
        <result column="stdlib_origin" property="origin"/>
        <result column="stdlib_manufacturer_uscc" property="manufacturerUscc"/>
        <result column="stdlib_product_validity" property="productValidity"/>
        <result column="stdlib_approval_validity_period" property="approvalValidityPeriod"/>
        <result column="stdlib_marketing_authority_holder" property="marketingAuthorityHolder"/>
        <result column="stdlib_drug_ident_code" property="drugIdentCode"/>
        <result column="stdlib_usage_method" property="usageMethod"/>
        <result column="stdlib_usage_frequency" property="usageFrequency"/>
        <result column="stdlib_single_dosage" property="singleDosage"/>
        <result column="stdlib_single_dosage_unit" property="singleDosageUnit"/>
        <result column="stdlib_first_category" property="firstCategory"/>
        <result column="stdlib_second_category" property="secondCategory"/>
        <result column="stdlib_third_category" property="thirdCategory"/>
        <result column="stdlib_fourth_category" property="fourthCategory"/>
        <result column="stdlib_five_category" property="fiveCategory"/>
        <result column="stdlib_six_category" property="sixCategory"/>
        <result column="stdlib_ext" property="ext" typeHandler="com.xyy.saas.inquiry.annotation.JsonTypeHandler" />
        <result column="stdlib_multi_flag" property="multiFlag"/>
        <result column="stdlib_status" property="status"/>
        <result column="stdlib_remark" property="remark"/>
        <result column="stdlib_creator" property="creator"/>
        <result column="stdlib_create_time" property="createTime"/>
        <result column="stdlib_updater" property="updater"/>
        <result column="stdlib_update_time" property="updateTime"/>
        <result column="stdlib_deleted" property="deleted"/>
    </association>
  </resultMap>

  <sql id="Select_Columns_UserInfo">
    pui.product_pref as useInfo_product_pref,
    pui.tenant_id as useInfo_tenant_id,
    pui.head_tenant_id as useInfo_head_tenant_id,
    pui.retail_price as useInfo_retail_price,
    pui.member_price as useInfo_member_price,
    pui.medicare_project_code as useInfo_medicare_project_code,
    pui.medicare_project_name as useInfo_medicare_project_name,
    pui.medicare_project_level as useInfo_medicare_project_level,
    pui.medicare_match_status as useInfo_medicare_match_status,
    pui.medicare_upload_status as useInfo_medicare_upload_status,
    pui.redo_data as useInfo_redo_data,
    pui.remark as useInfo_remark,
    pui.create_time as useInfo_create_time,
    pui.update_time as useInfo_update_time,
    pui.creator as useInfo_creator,
    pui.updater as useInfo_updater,
    pui.deleted as useInfo_deleted
  </sql>

  <sql id="Select_Columns_Stdlib">
    stdlib.id as stdlib_id,
    stdlib.mid_stdlib_id as stdlib_mid_stdlib_id,
    stdlib.mid_stdlib_id_bak as stdlib_mid_stdlib_id_bak,
    stdlib.common_name as stdlib_common_name,
    stdlib.brand_name as stdlib_brand_name,
    stdlib.spec as stdlib_spec,
    stdlib.barcode as stdlib_barcode,
    stdlib.manufacturer as stdlib_manufacturer,
    stdlib.approval_number as stdlib_approval_number,
    stdlib.key_point_hash as stdlib_key_point_hash,
    stdlib.mnemonic_code as stdlib_mnemonic_code,
    stdlib.min_package_num as stdlib_min_package_num,
    stdlib.spu_category as stdlib_spu_category,
    stdlib.unit as stdlib_unit,
    stdlib.dosage_form as stdlib_dosage_form,
    stdlib.business_scope as stdlib_business_scope,
    stdlib.pres_category as stdlib_pres_category,
    stdlib.storage_way as stdlib_storage_way,
    stdlib.origin as stdlib_origin,
    stdlib.manufacturer_uscc as stdlib_manufacturer_uscc,
    stdlib.product_validity as stdlib_product_validity,
    stdlib.approval_validity_period as stdlib_approval_validity_period,
    stdlib.marketing_authority_holder as stdlib_marketing_authority_holder,
    stdlib.drug_ident_code as stdlib_drug_ident_code,
    stdlib.usage_method as stdlib_usage_method,
    stdlib.usage_frequency as stdlib_usage_frequency,
    stdlib.single_dosage as stdlib_single_dosage,
    stdlib.single_dosage_unit as stdlib_single_dosage_unit,
    stdlib.first_category as stdlib_first_category,
    stdlib.second_category as stdlib_second_category,
    stdlib.third_category as stdlib_third_category,
    stdlib.fourth_category as stdlib_fourth_category,
    stdlib.five_category as stdlib_five_category,
    stdlib.six_category as stdlib_six_category,
    stdlib.ext as stdlib_ext,
    stdlib.multi_flag as stdlib_multi_flag,
    stdlib.status as stdlib_status,
    stdlib.remark as stdlib_remark,
    stdlib.creator as stdlib_creator,
    stdlib.create_time as stdlib_create_time,
    stdlib.updater as stdlib_updater,
    stdlib.update_time as stdlib_update_time,
    stdlib.deleted as stdlib_deleted
  </sql>

  <select id="getTenantProductCount" resultType="java.lang.Long">
    SELECT count(*) FROM saas_product_info WHERE tenant_id = #{tenantId}
  </select>

  <select id="listShowPref" resultType="java.lang.String">
    SELECT show_pref FROM saas_product_info WHERE tenant_id = #{tenantId}
  </select>

  <select id="uniqueIndexExists" resultMap="BaseResultMap">
    SELECT * FROM saas_product_info WHERE tenant_id = #{tenantId}
    <if test="showPref != null">
      AND show_pref = #{showPref}
    </if>
    <if test="stdlibId != null">
      AND stdlib_id = #{stdlibId}
    </if>
    limit 1
  </select>

  <!-- 不支持使用信息表作为查询条件（联表为门店商品使用信息） -->
  <select id="selectPageByParam" resultMap="BaseResultMap2">
    select p.*,
    <include refid="Select_Columns_Stdlib" />
    <if test="param.tenantId != null">
      , <include refid="Select_Columns_UserInfo" />
    </if>
    from saas_product_info p
      left join saas_product_stdlib stdlib on p.stdlib_id = stdlib.id
      <if test="param.tenantId != null">
        left join saas_product_use_info pui on p.pref = pui.product_pref AND pui.tenant_id = #{param.tenantId}
      </if>
    <where>
      <if test="param.deleted != null">
        AND p.deleted = ${param.deleted}
      </if>
      <if test="param.headTenantId != null">
        AND p.tenant_id = #{param.headTenantId}
      </if>
      <if test="param.idList != null and param.idList.size() > 0">
        AND p.id IN
        <foreach collection="param.idList" item="id" open="(" separator="," close=")">
          #{id}
        </foreach>
      </if>
      <if test="param.prefList != null and param.prefList.size() > 0">
        AND p.pref IN
        <foreach collection="param.prefList" item="pref" open="(" separator="," close=")">
          #{pref}
        </foreach>
      </if>
      <if test="param.showPrefList != null and param.showPrefList.size() > 0">
        AND p.show_pref IN
        <foreach collection="param.showPrefList" item="showPref" open="(" separator="," close=")">
          #{showPref}
        </foreach>
      </if>
      <if test="param.barcodeList != null and param.barcodeList.size() > 0">
        AND p.barcode IN
        <foreach collection="param.barcodeList" item="barcode" open="(" separator="," close=")">
          #{barcode}
        </foreach>
      </if>
      <if test="param.commonNameLike != null and param.commonNameLike != ''">
        AND p.common_name like concat('%', #{param.commonNameLike}, '%')
      </if>
      <if test="param.brandNameLike != null and param.brandNameLike != ''">
        AND p.brand_name like concat('%', #{param.brandNameLike}, '%')
      </if>
      <if test="param.mixedQuery != null and param.mixedQuery != ''">
        AND match(p.show_pref, p.common_name, p.brand_name, p.barcode, p.mnemonic_code, p.approval_number, p.manufacturer) against( #{param.mixedQuery} in BOOLEAN MODE )
      </if>
      <if test="param.stdlibIdList != null and param.stdlibIdList.size() > 0">
        AND stdlib.id IN
        <foreach collection="param.stdlibIdList" item="stdlibId" open="(" separator="," close=")">
          #{stdlibId}
        </foreach>
      </if>
      <if test="param.midStdlibIdList != null and param.midStdlibIdList.size() > 0">
        AND stdlib.mid_stdlib_id IN
        <foreach collection="param.midStdlibIdList" item="midStdlibId" open="(" separator="," close=")">
          #{midStdlibId}
        </foreach>
      </if>
      <if test="param.unit != null and param.unit != ''">
        AND p.unit = #{param.unit}
      </if>
      <if test="param.firstCategory != null and param.firstCategory != ''">
        AND stdlib.first_category = #{param.firstCategory}
      </if>
      <if test="param.dosageForm != null and param.dosageForm != ''">
        AND stdlib.dosage_form = #{param.dosageForm}
      </if>
      <if test="param.businessScope != null and param.businessScope != ''">
        AND FIND_IN_SET(#{param.businessScope}, stdlib.business_scope) > 0
      </if>
      <if test="param.presCategory != null and param.presCategory != ''">
        AND stdlib.pres_category = #{param.presCategory}
      </if>
      <if test="param.storageWay != null and param.storageWay != ''">
        AND stdlib.storage_way = #{param.storageWay}
      </if>
      <if test="param.manufacturer != null and param.manufacturer != ''">
        AND p.manufacturer = #{param.manufacturer}
      </if>
      <if test="param.status != null and param.status >= 0">
        AND p.status = #{param.status}
      </if>
      <if test="param.statusList != null and param.statusList.size() > 0">
        AND p.status IN
        <foreach collection="param.statusList" item="status" open="(" separator="," close=")">
          #{status}
        </foreach>
      </if>
      <if test="param.excludeStatusList != null and param.excludeStatusList.size() > 0">
        AND p.status NOT IN
        <foreach collection="param.excludeStatusList" item="status" open="(" separator="," close=")">
          #{status}
        </foreach>
      </if>
      <if test="param.disable != null">
        AND p.disable = #{param.disable}
      </if>
      <if test="param.deleteType != null and param.deleteType > 0">
        AND p.delete_type = #{param.deleteType}
      </if>
      <if test="param.deletedAt != null and param.deletedAt.length > 1">
        AND p.deleted_at BETWEEN #{param.deletedAt[0]} AND #{param.deletedAt[1]}
      </if>
      <if test="param.createTime != null and param.createTime.length > 1">
        AND p.create_time BETWEEN #{param.createTime[0]} AND #{param.createTime[1]}
      </if>
      <if test="param.productFlag != null">
        AND ${@com.xyy.saas.inquiry.product.api.product.dto.ProductFlag@toFlagSql(param.productFlag, 'p.multi_flag')}
        AND ${@com.xyy.saas.inquiry.product.api.product.dto.ProductFlag@toStdlibFlagSql(param.productFlag, 'stdlib.multi_flag')}
      </if>
    </where>
    order by p.create_time desc
  </select>

  <update id="batchUpdateDeleted">
    UPDATE saas_product_info
    SET deleted = #{deleted}
      <if test="deleted">
        , deleted_at = now(), delete_type = #{deleteType}
      </if>
    WHERE id IN
    <foreach collection="idList" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </update>

  <delete id="hardDeleteByPrefList" parameterType="java.util.List">
    DELETE FROM saas_product_info WHERE pref IN
    <foreach collection="prefList" item="pref" open="(" separator="," close=")">
      #{pref}
    </foreach>
  </delete>

  <select id="listExpiredRecyclePref" resultType="java.lang.String">
    SELECT pref
    FROM saas_product_info
    WHERE deleted = 1 AND deleted_at &lt;= #{expireTime}
  </select>

  <select id="findUnbundledProductsBySourceProducts" resultMap="BaseResultMap">
    SELECT *
    FROM saas_product_info 
    WHERE deleted = 0
    AND <![CDATA[ (multi_flag & 1) ]]> = 1
    AND source_product_pref IN
      <foreach collection="sourceProductPrefList" item="pref" open="(" separator="," close=")">
        #{pref}
      </foreach>
  </select>
</mapper>