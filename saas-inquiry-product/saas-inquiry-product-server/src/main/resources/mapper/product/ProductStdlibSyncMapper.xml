<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductStdlibSyncMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->
  <resultMap id="BaseResultMap" type="com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibSyncDO">
    <id column="id" property="id" jdbcType="BIGINT"/>
    <result column="guid" property="guid" jdbcType="VARCHAR"/>
    <result column="type" property="type" jdbcType="TINYINT"/>
    <result column="status" property="status" jdbcType="TINYINT"/>
    <result column="start_id" property="startId" jdbcType="BIGINT"/>
    <result column="end_id" property="endId" jdbcType="BIGINT"/>
    <result column="`current`" property="current" jdbcType="BIGINT"/>
    <result column="start_time" property="startTime" jdbcType="TIMESTAMP"/>
    <result column="end_time" property="endTime" jdbcType="TIMESTAMP"/>
    <result column="error_msg" property="errorMsg" jdbcType="VARCHAR"/>
    <result column="remark" property="remark" jdbcType="VARCHAR" />
    <result column="create_time" property="createTime" jdbcType="TIMESTAMP" />
    <result column="update_time" property="updateTime" jdbcType="TIMESTAMP" />
    <result column="creator" property="creator" jdbcType="VARCHAR" />
    <result column="updater" property="updater" jdbcType="VARCHAR" />
    <result column="deleted" property="deleted" jdbcType="BIT" />
  </resultMap>


  <select id="selectByGuidWithLock" resultMap="BaseResultMap" >
    SELECT * FROM saas_product_stdlib_sync WHERE guid = #{guid}
    <if test="forUpdate">
      FOR UPDATE
    </if>
  </select>

  <update id="updateStatus">
    UPDATE saas_product_stdlib_sync SET status = #{status}
    WHERE guid = #{guid} AND status = #{oldStatus}
  </update>

  <insert id="insertOrUpdateBatch" parameterType="com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductStdlibSyncDO">
    INSERT INTO saas_product_stdlib_sync ( guid, type, status, start_id, end_id, `current`, start_time, end_time, error_msg, remark )
    VALUES
    <foreach collection="list" item="item" index="index" separator=",">
      ( #{item.guid}, #{item.type}, #{item.status}, #{item.startId}, #{item.endId}, #{item.current}, #{item.startTime}, #{item.endTime}, #{item.errorMsg}, #{item.remark} )
    </foreach>
  </insert>
</mapper>