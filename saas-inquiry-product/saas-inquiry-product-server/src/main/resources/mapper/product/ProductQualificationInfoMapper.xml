<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.product.server.dal.mysql.product.ProductQualificationInfoMapper">

  <resultMap id="BaseResultMap" type="com.xyy.saas.inquiry.product.server.dal.dataobject.product.ProductQualificationInfoDO">
    <id column="id" jdbcType="BIGINT" property="id"/>
    <result column="product_pref" jdbcType="VARCHAR" property="productPref"/>
    <result column="qualification_type" jdbcType="INTEGER" property="qualificationType"/>
    <result column="qualification_info" jdbcType="VARCHAR" property="qualificationInfo"/>
    <result column="start_date" jdbcType="TIMESTAMP" property="startDate"/>
    <result column="end_date" jdbcType="TIMESTAMP" property="endDate"/>
    <result column="ext" jdbcType="VARCHAR" property="ext" typeHandler="com.xyy.saas.inquiry.annotation.JsonTypeHandler" />
    <result column="remark" jdbcType="VARCHAR" property="remark"/>
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime"/>
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime"/>
    <result column="creator" jdbcType="VARCHAR" property="creator"/>
    <result column="updater" jdbcType="VARCHAR" property="updater"/>
    <result column="deleted" jdbcType="BIT" property="deleted"/>
  </resultMap>

  <sql id="Base_Column_List">
    id, product_pref, qualification_type, qualification_info, start_date, end_date, ext,
      remark, create_time, update_time, creator, updater, deleted
  </sql>

  <!-- 批量插入或更新 -->
  <insert id="insertOrUpdateOnDuplicate">
    INSERT INTO saas_product_qualification_info (
    product_pref, qualification_type, qualification_info, start_date, end_date, ext,
    remark, creator, updater
    ) VALUES
    <foreach collection="list" item="item" separator=",">
      (
      #{item.productPref},
      #{item.qualificationType},
      #{item.qualificationInfo},
      #{item.startDate},
      #{item.endDate},
      #{item.ext, jdbcType=OTHER, typeHandler=com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler},
      #{item.remark},
      #{item.creator},
      #{item.updater}
      )
    </foreach>
    ON DUPLICATE KEY UPDATE
    qualification_info = VALUES(qualification_info),
    start_date = VALUES(start_date),
    end_date = VALUES(end_date),
    ext = VALUES(ext),
    remark = VALUES(remark),
    updater = VALUES(updater)
  </insert>

  <delete id="hardDeleteByProductPrefList" parameterType="java.util.List">
    DELETE FROM saas_product_qualification_info WHERE product_pref IN
    <foreach collection="prefList" item="pref" open="(" separator="," close=")">
      #{pref}
    </foreach>
  </delete>
</mapper> 