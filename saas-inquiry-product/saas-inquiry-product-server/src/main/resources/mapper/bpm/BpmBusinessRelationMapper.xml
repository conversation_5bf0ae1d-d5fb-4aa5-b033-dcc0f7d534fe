<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.product.server.dal.mysql.bpm.BpmBusinessRelationMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->

  <resultMap id="BaseResultMap" type="com.xyy.saas.inquiry.product.server.dal.dataobject.bpm.BpmBusinessRelationDO">
    <result column="id" jdbcType="BIGINT" property="id" />
    <result column="tenant_id" jdbcType="BIGINT" property="tenantId" />
    <result column="head_tenant_id" jdbcType="BIGINT" property="headTenantId" />
    <result column="business_type" jdbcType="INTEGER" property="businessType" />
    <result column="business_pref" jdbcType="VARCHAR" property="businessPref" />
    <result column="process_instance_id" jdbcType="VARCHAR" property="processInstanceId" />
    <result column="start_time" jdbcType="TIMESTAMP" property="startTime" />
    <result column="end_time" jdbcType="TIMESTAMP" property="endTime" />
    <result column="applicant" jdbcType="VARCHAR" property="applicant" />
    <result column="approval_status" jdbcType="INTEGER" property="approvalStatus" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="updater" jdbcType="VARCHAR" property="updater" />
    <result column="deleted" jdbcType="BIT" property="deleted" />
  </resultMap>

  <sql id="Where_Search_ProductInfo_AS_p">
    <if test="req.productInfo != null">
      <if test="req.productInfo.deleted != null">
        AND p.deleted = ${req.productInfo.deleted}
      </if>
      <if test="req.productInfo.headTenantId != null">
        AND p.tenant_id = #{req.productInfo.headTenantId}
      </if>
      <if test="req.productInfo.idList != null and req.productInfo.idList.size() > 0">
        AND p.id IN
        <foreach collection="req.productInfo.idList" item="id" open="(" separator="," close=")">
          #{id}
        </foreach>
      </if>
      <if test="req.productInfo.prefList != null and req.productInfo.prefList.size() > 0">
        AND p.pref IN
        <foreach collection="req.productInfo.prefList" item="pref" open="(" separator="," close=")">
          #{pref}
        </foreach>
      </if>
      <if test="req.productInfo.showPrefList != null and req.productInfo.showPrefList.size() > 0">
        AND p.show_pref IN
        <foreach collection="req.productInfo.showPrefList" item="showPref" open="(" separator="," close=")">
          #{showPref}
        </foreach>
      </if>
      <if test="req.productInfo.barcodeList != null and req.productInfo.barcodeList.size() > 0">
        AND p.barcode IN
        <foreach collection="req.productInfo.barcodeList" item="barcode" open="(" separator="," close=")">
          #{barcode}
        </foreach>
      </if>
      <if test="req.productInfo.commonNameLike != null and req.productInfo.commonNameLike != ''">
        AND p.common_name like concat('%', #{req.productInfo.commonNameLike}, '%')
      </if>
      <if test="req.productInfo.brandNameLike != null and req.productInfo.brandNameLike != ''">
        AND p.brand_name like concat('%', #{req.productInfo.brandNameLike}, '%')
      </if>
      <if test="req.productInfo.mixedQuery != null and req.productInfo.mixedQuery != ''">
        AND match(p.show_pref, p.common_name, p.brand_name, p.barcode, p.mnemonic_code, p.approval_number, p.manufacturer) against( #{req.productInfo.mixedQuery} in BOOLEAN MODE )
      </if>
      <if test="req.productInfo.stdlibIdList != null and req.productInfo.stdlibIdList.size() > 0">
        AND stdlib.id IN
        <foreach collection="req.productInfo.stdlibIdList" item="stdlibId" open="(" separator="," close=")">
          #{stdlibId}
        </foreach>
      </if>
      <if test="req.productInfo.midStdlibIdList != null and req.productInfo.midStdlibIdList.size() > 0">
        AND stdlib.mid_stdlib_id IN
        <foreach collection="req.productInfo.midStdlibIdList" item="midStdlibId" open="(" separator="," close=")">
          #{midStdlibId}
        </foreach>
      </if>
      <if test="req.productInfo.unit != null and req.productInfo.unit != ''">
        AND p.unit = #{req.productInfo.unit}
      </if>
      <if test="req.productInfo.firstCategory != null and req.productInfo.firstCategory != ''">
        AND stdlib.first_category = #{req.productInfo.firstCategory}
      </if>
      <if test="req.productInfo.dosageForm != null and req.productInfo.dosageForm != ''">
        AND stdlib.dosage_form = #{req.productInfo.dosageForm}
      </if>
      <if test="req.productInfo.businessScope != null and req.productInfo.businessScope != ''">
        AND FIND_IN_SET(#{req.productInfo.businessScope}, stdlib.business_scope) > 0
      </if>
      <if test="req.productInfo.presCategory != null and req.productInfo.presCategory != ''">
        AND stdlib.pres_category = #{req.productInfo.presCategory}
      </if>
      <if test="req.productInfo.storageWay != null and req.productInfo.storageWay != ''">
        AND stdlib.storage_way = #{req.productInfo.storageWay}
      </if>
      <if test="req.productInfo.manufacturer != null and req.productInfo.manufacturer != ''">
        AND p.manufacturer = #{req.productInfo.manufacturer}
      </if>
      <if test="req.productInfo.status != null and req.productInfo.status > 0">
        AND p.status = #{req.productInfo.status}
      </if>
      <if test="req.productInfo.statusList != null and req.productInfo.statusList.size() > 0">
        AND p.status IN
        <foreach collection="req.productInfo.statusList" item="status" open="(" separator="," close=")">
          #{status}
        </foreach>
      </if>
      <if test="req.productInfo.excludeStatusList != null and req.productInfo.excludeStatusList.size() > 0">
        AND p.status NOT IN
        <foreach collection="req.productInfo.excludeStatusList" item="status" open="(" separator="," close=")">
          #{status}
        </foreach>
      </if>
      <if test="req.productInfo.disable != null">
        AND p.disable = #{req.productInfo.disable}
      </if>
      <if test="req.productInfo.deleteType != null and req.productInfo.deleteType > 0">
        AND p.delete_type = #{req.productInfo.deleteType}
      </if>
      <if test="req.productInfo.deletedAt != null and req.productInfo.deletedAt.length > 1">
        AND p.deleted_at BETWEEN #{req.productInfo.deletedAt[0]} AND #{req.productInfo.deletedAt[1]}
      </if>
      <if test="req.productInfo.createTime != null and req.productInfo.createTime.length > 1">
        AND p.create_time BETWEEN #{req.productInfo.createTime[0]} AND #{req.productInfo.createTime[1]}
      </if>
      <if test="req.productInfo.productFlag != null">
        AND ${@com.xyy.saas.inquiry.product.api.product.dto.ProductFlag@toFlagSql(req.productInfo.productFlag, 'p.multi_flag')}
        AND ${@com.xyy.saas.inquiry.product.api.product.dto.ProductFlag@toStdlibFlagSql(req.productInfo.productFlag, 'stdlib.multi_flag')}
      </if>
    </if>
  </sql>

  <sql id="Where_Search_BpmBusinessRelation_AS_br">
    <if test="req.tenantId != null">
      AND br.tenant_id = #{req.tenantId}
    </if>
    <if test="req.headTenantId != null">
      AND br.head_tenant_id = #{req.headTenantId}
    </if>
    <if test="req.businessType != null">
      AND br.business_type = #{req.businessType}
    </if>
    <if test="req.businessTypeList != null and req.businessTypeList.size() > 0">
      AND br.business_type IN
      <foreach collection="req.businessTypeList" item="pref" open="(" separator="," close=")">
        #{pref}
      </foreach>
    </if>
    <if test="req.businessPref != null and req.businessPref != ''">
      AND br.business_pref = #{req.businessPref}
    </if>
    <if test="req.businessPrefList != null and req.businessPrefList.size() > 0">
      AND br.business_pref IN
      <foreach collection="req.businessPrefList" item="pref" open="(" separator="," close=")">
        #{pref}
      </foreach>
    </if>
    <if test="req.processInstanceId != null and req.processInstanceId != ''">
      AND br.process_instance_id = #{req.processInstanceId}
    </if>
    <if test="req.startTime != null and req.startTime.length > 1">
      AND br.start_time BETWEEN #{req.startTime[0]} AND #{req.startTime[1]}
    </if>
    <if test="req.endTime != null and req.endTime.length > 1">
      AND br.end_time BETWEEN #{req.endTime[0]} AND #{req.endTime[1]}
    </if>
    <if test="req.applicant != null and req.applicant != ''">
      AND br.applicant = #{req.applicant}
    </if>
    <if test="req.approvalStatus != null">
      AND br.approval_status = #{req.approvalStatus}
    </if>
    <if test="req.remark != null and req.remark != ''">
      AND br.remark = #{req.remark}
    </if>
    <if test="req.createTime != null and req.createTime.length > 1">
      AND br.create_time BETWEEN #{req.createTime[0]} AND #{req.createTime[1]}
    </if>
  </sql>

  <!-- 连表查询审批流关联业务和商品信息 -->
  <select id="selectPageWithProductInfo" resultMap="BaseResultMap">
    SELECT br.*
    FROM saas_bpm_business_relation br
      JOIN saas_product_info p ON p.pref = br.business_pref
      JOIN saas_product_stdlib stdlib ON stdlib.id = p.stdlib_id
    <where>
      AND br.deleted = 0 AND p.deleted = 0
      <include refid="Where_Search_BpmBusinessRelation_AS_br"/>
      <include refid="Where_Search_ProductInfo_AS_p"/>
    </where>
    ORDER BY br.id DESC
  </select>

</mapper>