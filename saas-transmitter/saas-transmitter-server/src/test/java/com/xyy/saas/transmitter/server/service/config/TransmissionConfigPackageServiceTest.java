package com.xyy.saas.transmitter.server.service.config;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.enums.transmitter.DslTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.OrganTypeEnum;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigItemPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigItemSaveReqVO;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigPackagePageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigPackageRespVO;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigPackageSaveReqVO;
import com.xyy.saas.transmitter.server.dal.dataobject.config.TransmissionConfigItemDO;
import com.xyy.saas.transmitter.server.dal.dataobject.config.TransmissionConfigPackageDO;
import com.xyy.saas.transmitter.server.dal.mysql.config.TransmissionConfigItemMapper;
import com.xyy.saas.transmitter.server.dal.mysql.config.TransmissionConfigPackageMapper;
import com.xyy.saas.transmitter.server.service.BaseIntegrationTest;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static org.junit.jupiter.api.Assertions.*;

@Import({TransmissionConfigPackageServiceImpl.class,TransmissionConfigItemServiceImpl.class})
public class TransmissionConfigPackageServiceTest extends BaseIntegrationTest {

    @Resource
    private TransmissionConfigPackageServiceImpl configPackageService;

    @Resource
    private TransmissionConfigItemServiceImpl configItemService;


    @Resource
    private TransmissionConfigPackageMapper configPackageMapper;
    
    @Resource
    private TransmissionConfigItemMapper configItemMapper;

    // ====================== 创建配置包 ======================
    @Test
    @DisplayName("测试创建配置包-成功场景")
    public void testCreateConfigPackage_Success() {
        // 准备测试数据
        TransmissionConfigPackageSaveReqVO reqVO = buildConfigPackageReqVO();

        // 执行测试
        Integer id = configPackageService.createTransmissionConfigPackage(reqVO);

        // 验证结果
        TransmissionConfigPackageDO configPackage = configPackageMapper.selectById(id);
        assertPojoEquals(reqVO, configPackage, "id");
    }

    @Test
    @DisplayName("测试创建配置包-失败场景-名称重复")
    public void testCreateConfigPackage_NameDuplicate() {
        // 准备测试数据
        TransmissionConfigPackageSaveReqVO reqVO = buildConfigPackageReqVO();
        configPackageService.createTransmissionConfigPackage(reqVO);
        
        // 创建同名配置包
        TransmissionConfigPackageSaveReqVO duplicateReqVO = buildConfigPackageReqVO();
        
        // 执行测试并验证异常
        assertThrows(ServiceException.class, () -> configPackageService.createTransmissionConfigPackage(duplicateReqVO));
    }

    // ====================== 更新配置包 ======================
    @Test
    @DisplayName("测试更新配置包-成功场景")
    public void testUpdateConfigPackage_Success() {
        // 准备测试数据
        TransmissionConfigPackageSaveReqVO reqVO = buildConfigPackageReqVO();
        Integer id = configPackageService.createTransmissionConfigPackage(reqVO);

        // 更新配置包
        reqVO.setId(id);
        reqVO.setName("更新后的配置包名称");
        configPackageService.updateTransmissionConfigPackage(reqVO);

        // 验证结果
        TransmissionConfigPackageDO configPackage = configPackageMapper.selectById(id);
        assertEquals("更新后的配置包名称", configPackage.getName());
    }

    @Test
    @DisplayName("测试更新配置包-失败场景-配置包不存在")
    public void testUpdateConfigPackage_NotExists() {
        // 准备测试数据
        TransmissionConfigPackageSaveReqVO reqVO = buildConfigPackageReqVO();
        reqVO.setId(9999); // 不存在的配置包ID

        // 执行测试并验证异常
        assertThrows(ServiceException.class, () -> configPackageService.updateTransmissionConfigPackage(reqVO));
    }

    @Test
    @DisplayName("测试更新配置包-失败场景-名称重复")
    public void testUpdateConfigPackage_NameDuplicate() {
        // 准备测试数据
        TransmissionConfigPackageSaveReqVO reqVO = buildConfigPackageReqVO();
        configPackageService.createTransmissionConfigPackage(reqVO);
        
        // 更新配置包名称
        reqVO.setName("重复的配置包名称");
        
        // 执行测试并验证异常
        assertThrows(ServiceException.class, () -> configPackageService.updateTransmissionConfigPackage(reqVO));
    }

    // ====================== 删除配置包 ======================
    @Test
    @DisplayName("测试删除配置包-成功场景")
    public void testDeleteConfigPackage_Success() {
        // 准备测试数据
        TransmissionConfigPackageSaveReqVO reqVO = buildConfigPackageReqVO();
        Integer id = configPackageService.createTransmissionConfigPackage(reqVO);

        // 执行删除操作
        configPackageService.deleteTransmissionConfigPackage(id);

        // 验证是否已删除
        assertNull(configPackageMapper.selectById(id));
    }

    @Test
    @DisplayName("测试删除配置包-失败场景-配置包不存在")
    public void testDeleteConfigPackage_NotExists() {
        // 执行测试并验证异常
        assertThrows(ServiceException.class, () -> configPackageService.deleteTransmissionConfigPackage(9999));
    }

    // ====================== 查询配置包 ======================
    @Test
    @DisplayName("测试获取配置包详情-成功场景")
    public void testGetConfigPackage_Success() {
        // 准备测试数据
        TransmissionConfigPackageSaveReqVO reqVO = buildConfigPackageReqVO();
        Integer id = configPackageService.createTransmissionConfigPackage(reqVO);

        // 执行测试
        TransmissionConfigPackageDO result = configPackageService.getTransmissionConfigPackage(id);

        // 验证结果
        assertNotNull(result);
        assertEquals(reqVO.getName(), result.getName());
        assertEquals(reqVO.getOrganType(), result.getOrganType());
        assertEquals(reqVO.getProviderName(), result.getProviderName());
    }

    @Test
    @DisplayName("测试获取配置包详情-失败场景-配置包不存在")
    public void testGetConfigPackage_NotExists() {
        // 执行测试并验证异常
        assertNull(configPackageService.getTransmissionConfigPackage(9999));
    }

    @Test
    @DisplayName("测试分页查询配置包-成功场景")
    public void testGetConfigPackagePage_Success() {
        // 准备测试数据
        TransmissionConfigPackageSaveReqVO packageVO = buildConfigPackageReqVO();
        Integer packageId = configPackageService.createTransmissionConfigPackage(packageVO);
        configPackageService.copyTransmissionConfigPackage(packageId);


        // 执行测试
        PageResult<TransmissionConfigPackageRespVO> result = configPackageService.getTransmissionConfigPackagePage(TransmissionConfigPackagePageReqVO.builder().organType(OrganTypeEnum.MEDICAL_INSURANCE.getCode()).build());

        // 验证结果
        assertNotNull(result);
        assertEquals(2L, result.getTotal());
        assertFalse(result.getList().isEmpty());
        assertEquals(2, result.getList().size());
        
        // 验证返回对象的属性
        result.getList().forEach(item -> {
            assertNotNull(item.getId());
            assertPojoEquals(packageVO,item,"id","version","createTime","updateTime");
        });
    }

    @Test
    @DisplayName("测试分页查询配置包-成功场景-条件查询")
    public void testGetConfigPackagePage_FilterSuccess() {
        // 准备测试数据
        TransmissionConfigPackageSaveReqVO packageVO = buildConfigPackageReqVO();
        Integer packageId = configPackageService.createTransmissionConfigPackage(packageVO);
        configPackageService.copyTransmissionConfigPackage(packageId);


        // 执行测试
        PageResult<TransmissionConfigPackageRespVO> result = configPackageService.getTransmissionConfigPackagePage(TransmissionConfigPackagePageReqVO.builder().organType(OrganTypeEnum.MEDICAL_INSURANCE.getCode()).build());

        // 验证结果
        assertNotNull(result);
        assertEquals(2L, result.getTotal());
        assertFalse(result.getList().isEmpty());
        assertEquals(2, result.getList().size());
        
        // 验证返回对象的属性
        result.getList().forEach(item -> {
            assertNotNull(item.getId());
            assertPojoEquals(packageVO,item,"id","version","createTime","updateTime");
        });
    }

    // ====================== 复制配置包 ======================
    @Test
    @DisplayName("测试复制配置包-成功场景")
    public void testCopyConfigPackage_Success() {
        // 准备测试数据
        TransmissionConfigPackageSaveReqVO packageVO = buildConfigPackageReqVO();
        Integer packageId = configPackageService.createTransmissionConfigPackage(packageVO);
        TransmissionConfigItemSaveReqVO itemVO1 = buildConfigItemReqVO();
        itemVO1.setConfigPackageId(packageId);
        TransmissionConfigItemSaveReqVO itemVO2 = buildConfigItemReqVO();
        itemVO2.setConfigPackageId(packageId);
        itemVO2.setNodeType(NodeTypeEnum.INTERNET_SUPERVISION_ONLINE_PRESCRIPTION_COMMENT.getCode());
        configItemService.createOrUpdateTransmissionConfigItem(itemVO1);
        configItemService.createOrUpdateTransmissionConfigItem(itemVO2);

        // 执行测试
        Integer newPackageId = configPackageService.copyTransmissionConfigPackage(packageId);

        // 验证结果
        assertNotNull(newPackageId);
        TransmissionConfigPackageDO newConfigPackage = configPackageMapper.selectById(newPackageId);
        TransmissionConfigPackageDO oldConfigPackage = configPackageMapper.selectById(packageId);
        List<TransmissionConfigItemDO> newConfigItemList = configItemService.getTransmissionConfigItemList(TransmissionConfigItemPageReqVO.builder().configPackageId(newPackageId).build());
        List<TransmissionConfigItemDO> oldConfigItemList = configItemService.getTransmissionConfigItemList(TransmissionConfigItemPageReqVO.builder().configPackageId(packageId).build());
        Map<Integer, TransmissionConfigItemDO> newConfigItemMap = newConfigItemList.stream().collect(Collectors.toMap(TransmissionConfigItemDO::getNodeType, o -> o, (o1, o2) -> o1));

        assertEquals(oldConfigItemList.size(), newConfigItemList.size());
        assertPojoEquals(newConfigPackage,oldConfigPackage,"id","version","createTime","updateTime");
        oldConfigItemList.forEach(item -> {
            TransmissionConfigItemDO newConfigItem = newConfigItemMap.get(item.getNodeType());
            assertNotNull(newConfigItem);
            assertPojoEquals(item, newConfigItem, "id","configPackageId","createTime","updateTime");
        });
    }

    @Test
    @DisplayName("测试复制配置包-失败场景-源配置包不存在")
    public void testCopyConfigPackage_SourceNotExists() {
        // 执行测试并验证异常
        assertThrows(ServiceException.class, () -> configPackageService.copyTransmissionConfigPackage(9999));
    }

    // ====================== 辅助方法 ======================
    private TransmissionConfigPackageSaveReqVO buildConfigPackageReqVO() {
        return TransmissionConfigPackageSaveReqVO.builder()
            .name("测试配置包")
            .organType(1)
            .providerName("测试服务提供商")
            .description("测试描述")
            .parentPackageId(111)
            .disable(false)
            .build();
    }

    private TransmissionConfigItemSaveReqVO buildConfigItemReqVO() {
        TransmissionConfigItemSaveReqVO reqVO = new TransmissionConfigItemSaveReqVO();
        reqVO.setParentItemId(123);
        reqVO.setDslType(DslTypeEnum.LOGIC.getCode());
        reqVO.setNodeType(NodeTypeEnum.INTERNET_SUPERVISION_UPLOAD_OUT_PATIENT_CASE.getCode());
        reqVO.setApiCode("TEST001");
        reqVO.setDescription("测试配置项");
        reqVO.setConfigValue("{\"key\":\"value\"}");
        reqVO.setDisable(false);
        return reqVO;
    }
}