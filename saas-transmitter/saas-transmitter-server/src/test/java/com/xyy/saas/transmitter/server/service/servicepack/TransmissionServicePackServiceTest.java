package com.xyy.saas.transmitter.server.service.servicepack;

import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.enums.transmitter.DslTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.ServiceEnvEnum;
import com.xyy.saas.transmitter.api.organ.dto.TransmissionOrganNetworkConfigDTO;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigItemSaveReqVO;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigPackageSaveReqVO;
import com.xyy.saas.transmitter.server.controller.admin.organ.vo.TransmissionOrganSaveReqVO;
import com.xyy.saas.transmitter.server.controller.admin.servicepack.vo.TransmissionServicePackPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.servicepack.vo.TransmissionServicePackSaveReqVO;
import com.xyy.saas.transmitter.server.dal.dataobject.servicepack.TransmissionServicePackDO;
import com.xyy.saas.transmitter.server.dal.mysql.servicepack.TransmissionServicePackMapper;
import com.xyy.saas.transmitter.server.service.BaseIntegrationTest;
import com.xyy.saas.transmitter.server.service.config.TransmissionConfigItemServiceImpl;
import com.xyy.saas.transmitter.server.service.config.TransmissionConfigPackageServiceImpl;
import com.xyy.saas.transmitter.server.service.organ.TransmissionOrganServiceImpl;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig;
import jakarta.annotation.Resource;
import java.util.Collections;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;
import org.yaml.snakeyaml.Yaml;

@Import({TransmissionServicePackServiceImpl.class, TransmissionConfigPackageServiceImpl.class,
    TransmissionOrganServiceImpl.class, TransmissionConfigItemServiceImpl.class})
public class TransmissionServicePackServiceTest extends BaseIntegrationTest {

    @Resource
    private TransmissionServicePackServiceImpl servicePackService;

    @Resource
    private TransmissionServicePackMapper servicePackMapper;

    @Resource
    private TransmissionOrganServiceImpl organService;

    @Resource
    private TransmissionConfigPackageServiceImpl configPackageService;

    @Resource
    private TransmissionConfigItemServiceImpl configItemService;

    private Integer organId;
    private Integer packageId;
    private TransmissionOrganSaveReqVO organVO;
    private TransmissionConfigPackageSaveReqVO packageVO;
    private TransmissionServicePackSaveReqVO baseReqVO;

    @BeforeEach
    public void setUp() {
        // 准备基础测试数据
        organVO = buildOrganReqVO();
        organId = organService.createTransmissionOrgan(organVO);

        packageVO = buildConfigPackageReqVO();
        packageId = configPackageService.createTransmissionConfigPackage(packageVO);

        // 创建配置项
        createConfigItems(packageId);

        // 准备基础服务包数据
        baseReqVO = buildServicePackReqVO();
        baseReqVO.setConfigPackageId(packageId);
        baseReqVO.setOrganId(organId);
    }

    // ====================== 创建服务包 ======================
    @Test
    @DisplayName("测试创建服务包-成功场景")
    public void testCreateServicePack_Success() {
        // 执行测试
        Integer id = servicePackService.createTransmissionServicePack(baseReqVO);

        // 验证结果
        assertNotNull(id);
        verifyServicePack(baseReqVO, id);
    }

    @Test
    @DisplayName("测试创建服务包-失败场景-机构不存在")
    public void testCreateServicePack_OrganNotExists() {
        // 准备测试数据
        TransmissionServicePackSaveReqVO reqVO = buildServicePackReqVO();
        reqVO.setOrganId(9999); // 不存在的机构ID

        // 执行测试并验证异常
        assertThrows(ServiceException.class, () -> servicePackService.createTransmissionServicePack(reqVO));
    }

    @Test
    @DisplayName("测试创建服务包-失败场景-配置包不存在")
    public void testCreateServicePack_ConfigPackageNotExists() {
        // 准备测试数据
        TransmissionServicePackSaveReqVO reqVO = buildServicePackReqVO();
        reqVO.setOrganId(organId);
        reqVO.setConfigPackageId(9999); // 不存在的配置包ID

        // 执行测试并验证异常
        assertThrows(ServiceException.class, () -> servicePackService.createTransmissionServicePack(reqVO));
    }

    @Test
    @DisplayName("测试创建服务包-失败场景-版本号重复")
    public void testCreateServicePack_VersionDuplicate() {
        // 准备测试数据
        TransmissionServicePackSaveReqVO reqVO = buildServicePackReqVO();
        servicePackService.createTransmissionServicePack(reqVO);

        // 执行测试并验证异常
        assertThrows(ServiceException.class, () -> servicePackService.createTransmissionServicePack(reqVO));
    }

    // ====================== 更新服务包 ======================
    @Test
    @DisplayName("测试更新服务包-成功场景")
    public void testUpdateServicePack_Success() {
        // 准备测试数据
        TransmissionServicePackSaveReqVO reqVO = buildServicePackReqVO();
        reqVO.setConfigPackageId(packageId);
        reqVO.setOrganId(organId);
        Integer id = servicePackService.createTransmissionServicePack(reqVO);
        reqVO.setId(id);
        reqVO.setName("更新后的服务包");

        // 执行测试
        servicePackService.updateTransmissionServicePack(reqVO);

        // 验证结果
        reqVO.setProvinceCode(organVO.getProvinceCode());
        reqVO.setCityCode(organVO.getCityCode());
        reqVO.setAreaCode(organVO.getAreaCode());
        reqVO.setProvince(organVO.getProvince());
        reqVO.setCity(organVO.getCity());
        reqVO.setArea(organVO.getArea());
        TransmissionServicePackDO transmissionServicePackDO = servicePackMapper.selectById(id);
        assertPojoEquals(reqVO, transmissionServicePackDO);
    }

    @Test
    @DisplayName("测试更新服务包-失败场景-服务包不存在")
    public void testUpdateServicePack_NotExists() {
        // 准备测试数据
        TransmissionServicePackSaveReqVO reqVO = buildServicePackReqVO();
        reqVO.setId(9999); // 不存在的服务包ID

        // 执行测试并验证异常
        assertThrows(ServiceException.class, () -> servicePackService.updateTransmissionServicePack(reqVO));
    }

    // ====================== 删除服务包 ======================
    @Test
    @DisplayName("测试删除服务包")
    public void testDeleteServicePack() {
        // 准备测试数据
        TransmissionServicePackSaveReqVO reqVO = buildServicePackReqVO();
        reqVO.setConfigPackageId(packageId);
        reqVO.setOrganId(organId);
        Integer id = servicePackService.createTransmissionServicePack(reqVO);

        // 执行删除操作
        servicePackService.deleteTransmissionServicePack(id);

        // 验证是否已删除
        assertNull(servicePackMapper.selectById(id));
    }

    // ====================== 查询服务包 ======================
    @Test
    @DisplayName("测试分页查询服务包")
    public void testGetServicePackPage() {
        // 准备测试数据
        TransmissionServicePackSaveReqVO reqVO = buildServicePackReqVO();
        reqVO.setConfigPackageId(packageId);
        reqVO.setOrganId(organId);
        servicePackService.createTransmissionServicePack(reqVO);
        reqVO.setName("测试服务包2");
        servicePackService.createTransmissionServicePack(reqVO);

        // 执行测试
        PageResult<?> result = servicePackService
            .getTransmissionServicePackPage(TransmissionServicePackPageReqVO.builder().organId(organId).build());

        // 验证结果
        assertNotNull(result);
        assertEquals(2L, result.getTotal());
        assertFalse(result.getList().isEmpty());
        assertEquals(2, result.getList().size());
    }

    // ====================== 辅助方法 ======================
    private TransmissionServicePackSaveReqVO buildServicePackReqVO() {
        TransmissionServicePackSaveReqVO reqVO = new TransmissionServicePackSaveReqVO();
        reqVO.setName("测试服务包");
        reqVO.setOrganId(organId);
        reqVO.setOrganType(1);
        reqVO.setConfigPackageId(packageId);
        reqVO.setVersion(202502070001L);
        reqVO.setEnv(ServiceEnvEnum.TEST.getCode());
        reqVO.setDllResource("测试DLL资源");
        reqVO.setDllVersion("1.0.0");
        reqVO.setTicketResource("测试小票资源");
        reqVO.setBillResource("测试账单资源");
        reqVO.setExtResource("测试扩展资源");
        reqVO.setApiDoc("测试接口文档");
        reqVO.setDisable(false);
        return reqVO;
    }

    private TransmissionConfigPackageSaveReqVO buildConfigPackageReqVO() {
        return TransmissionConfigPackageSaveReqVO.builder()
            .name("测试配置包")
            .organType(1)
            .providerName("测试服务提供商")
            .description("测试描述")
            .parentPackageId(111)
            .disable(false)
            .build();
    }

    private TransmissionConfigItemSaveReqVO buildConfigItemReqVO() {
        TransmissionConfigItemSaveReqVO reqVO = new TransmissionConfigItemSaveReqVO();
        reqVO.setParentItemId(123);
        reqVO.setDslType(DslTypeEnum.LOGIC.getCode());
        reqVO.setNodeType(NodeTypeEnum.INTERNET_SUPERVISION_UPLOAD_OUT_PATIENT_CASE.getCode());
        reqVO.setApiCode("TEST001");
        reqVO.setDescription("测试配置项");
        reqVO.setConfigValue("{\"key\":\"value\"}");
        reqVO.setDisable(false);
        return reqVO;
    }

    private TransmissionOrganSaveReqVO buildOrganReqVO() {
        TransmissionOrganSaveReqVO reqVO = new TransmissionOrganSaveReqVO();
        reqVO.setName("测试机构");
        reqVO.setOrganType(1);
        reqVO.setNetworkConfig(Collections.singletonList(buildNetworkConfig()));
        reqVO.setBasicConfig("{\"key\":\"value\"}");
        reqVO.setDisable(false);
        reqVO.setProvinceCode("330000");
        reqVO.setCityCode("330100");
        reqVO.setAreaCode("330106");
        reqVO.setProvince("浙江省");
        reqVO.setCity("杭州市");
        reqVO.setArea("西湖区");
        reqVO.setLogo("https://example.com/logo.png");
        reqVO.setRemark("备注信息");
        reqVO.setDisable(false);
        return reqVO;
    }

    private TransmissionOrganNetworkConfigDTO buildNetworkConfig() {
        return TransmissionOrganNetworkConfigDTO.builder()
            .code("TEST")
            .name("测试网络")
            .env(ServiceEnvEnum.TEST.getCode())
            .disable(false)
            .build();
    }

    private void createConfigItems(Integer packageId) {
        TransmissionConfigItemSaveReqVO itemVO1 = buildConfigItemReqVO();
        itemVO1.setConfigPackageId(packageId);

        TransmissionConfigItemSaveReqVO itemVO2 = buildConfigItemReqVO();
        itemVO2.setConfigPackageId(packageId);
        itemVO2.setNodeType(NodeTypeEnum.INTERNET_SUPERVISION_ONLINE_PRESCRIPTION_COMMENT.getCode());

        configItemService.createOrUpdateTransmissionConfigItem(itemVO1);
        configItemService.createOrUpdateTransmissionConfigItem(itemVO2);
    }

    private void verifyServicePack(TransmissionServicePackSaveReqVO reqVO, Integer id) {
        // 设置地理位置信息
        reqVO.setProvinceCode(organVO.getProvinceCode());
        reqVO.setCityCode(organVO.getCityCode());
        reqVO.setAreaCode(organVO.getAreaCode());
        reqVO.setProvince(organVO.getProvince());
        reqVO.setCity(organVO.getCity());
        reqVO.setArea(organVO.getArea());

        // 验证记录
        TransmissionServicePackDO servicePack = servicePackMapper.selectById(id);
        assertPojoEquals(reqVO, servicePack, "id");
    }

    public static void main(String[] args) {
        // 示例YAML格式字符串
        String yamlString = ""
            + "preParameter:\n"
            + "  nodes:\n"
            + "    - doctorHospitalPref\n"
            + "    - doctorPharmacistInfo\n"
            + "    - medicalRegistrationInfo\n"
            + "    - prescriptionDetail\n"
            + "    - clinicalCase\n"
            + "    - prescriptionCa";

        // 创建Yaml实例
        Yaml yaml = new Yaml();

        // 将YAML字符串加载为LogicConfig对象
        LogicConfig result = yaml.loadAs(yamlString, LogicConfigProcessor.LogicConfig.class);

        // 输出结果
        System.out.println(result);
    }
}