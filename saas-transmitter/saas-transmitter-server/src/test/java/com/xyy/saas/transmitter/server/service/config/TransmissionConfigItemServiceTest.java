package com.xyy.saas.transmitter.server.service.config;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import com.xyy.saas.inquiry.enums.transmitter.DslTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigItemSaveReqVO;
import com.xyy.saas.transmitter.server.dal.dataobject.config.TransmissionConfigItemDO;
import com.xyy.saas.transmitter.server.dal.mysql.config.TransmissionConfigItemMapper;
import com.xyy.saas.transmitter.server.service.BaseIntegrationTest;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static org.junit.jupiter.api.Assertions.*;
import java.util.List;
import java.util.Map;

@Import(TransmissionConfigItemServiceImpl.class)
public class TransmissionConfigItemServiceTest extends BaseIntegrationTest {

    @Resource
    private TransmissionConfigItemServiceImpl configItemService;

    @Resource
    private TransmissionConfigItemMapper configItemMapper;

    // ====================== 创建配置项 ======================
    @Test
    @DisplayName("测试创建配置项-成功场景")
    public void testCreateConfigItem_Success() {
        // 准备测试数据
        TransmissionConfigItemSaveReqVO reqVO = buildConfigItemReqVO();

        // 执行测试
        Integer id = configItemService.createOrUpdateTransmissionConfigItem(reqVO);

        // 验证结果
        TransmissionConfigItemDO transmissionConfigItemDO = configItemMapper.selectById(id);
        assertPojoEquals(reqVO,transmissionConfigItemDO,"id");
    }

    // ====================== 更新配置项 ======================
    @Test
    @DisplayName("测试更新配置项-成功场景")
    public void testUpdateConfigItem_Success() {
        // 准备测试数据
        TransmissionConfigItemSaveReqVO reqVO = buildConfigItemReqVO();

        // 执行测试
        Integer id = configItemService.createOrUpdateTransmissionConfigItem(reqVO);
        reqVO.setId(id);
        reqVO.setConfigValue("{\"newKey\":\"newValue\"}");
        configItemService.createOrUpdateTransmissionConfigItem(reqVO);

        // 验证结果
        TransmissionConfigItemDO transmissionConfigItemDO = configItemMapper.selectById(id);
        assertPojoEquals(reqVO,transmissionConfigItemDO,"id");
    }

    // ====================== 删除配置项 ======================
    @Test
    @DisplayName("测试删除配置项-成功场景")
    public void testDeleteConfigItem_Success() {
        // 准备测试数据
        TransmissionConfigItemSaveReqVO reqVO = buildConfigItemReqVO();

        // 执行测试
        Integer id = configItemService.createOrUpdateTransmissionConfigItem(reqVO);

        // 验证结果
        assertNotNull(id);

        // 执行删除
        configItemService.deleteTransmissionConfigItem(id);

        // 验证结果
        assertNull(configItemService.getTransmissionConfigItem(id));
    }

    // ====================== 查询配置项 ======================
    @Test
    @DisplayName("测试获取配置项列表")
    public void testGetConfigItemList() {
        // 准备测试数据
        TransmissionConfigItemSaveReqVO reqVO = buildConfigItemReqVO();

        // 执行测试
        Integer id = configItemService.createOrUpdateTransmissionConfigItem(reqVO);

        // 验证结果
        assertNotNull(id);
    }

    // ====================== 合并配置项 ======================
    @Test
    @DisplayName("测试获取合并后的配置值-无父配置项场景")
    public void testGetMergedConfigValue_NoParent() {
        // 准备测试数据
        TransmissionConfigItemSaveReqVO configItem = buildConfigItemReqVO();
        configItem.setParentItemId(null);
        configItem.setConfigValue("""
            request:
              method: POST
              url: /api/v1/test
              headers:
                Content-Type: application/json
                Accept: application/json
              params:
                orgCode: ${orgCode}
                timestamp: ${timestamp}
            response:
              success: true
              data:
                code: 200
                message: success
            """);
        Integer configItemId = configItemService.createOrUpdateTransmissionConfigItem(configItem);

        // 执行测试
        Map<String, Object> result = configItemService.getMergedConfigValue(configItemId);

        // 验证结果
        assertNotNull(result);
        assertFalse(result.isEmpty());
        @SuppressWarnings("unchecked")
        Map<String, Object> request = (Map<String, Object>) result.get("request");
        assertEquals("POST", request.get("method"));
        assertEquals("/api/v1/test", request.get("url"));
        @SuppressWarnings("unchecked")
        Map<String, Object> headers = (Map<String, Object>) request.get("headers");
        assertEquals("application/json", headers.get("Content-Type"));
    }

    @Test
    @DisplayName("测试获取合并后的配置值-有父配置项场景")
    public void testGetMergedConfigValue_WithParent() {
        // 准备父配置项
        TransmissionConfigItemSaveReqVO parentItem = buildConfigItemReqVO();
        parentItem.setParentItemId(null);
        parentItem.setConfigValue("""
            request:
              method: POST
              url: /api/base
              headers:
                Content-Type: application/json
                Authorization: Bearer ${token}
              timeout: 5000
            validation:
              required:
                - index: 1
                  field: orgCode
                - index: 2
                  field: timestamp
              format:
                orgCode: string
                timestamp: number
            transform:
              input:
                - source: request.body.orgName
                  target: data.organizationName
                - source: request.body.time
                  target: data.requestTime
            """);
        Integer parentId = configItemService.createOrUpdateTransmissionConfigItem(parentItem);

        // 准备子配置项
        TransmissionConfigItemSaveReqVO childItem = buildConfigItemReqVO();
        childItem.setConfigPackageId(2);
        childItem.setParentItemId(parentId);
        childItem.setConfigValue("""
            request:
              url: /api/v2/specific
              headers:
                Accept: application/json
                Custom-Header: ${customValue}
              params:
                version: 2.0
            validation:
              required:
                - index: 3
                  field: customField
              format:
                customField: string
            transform:
              output:
                - source: response.data.result
                  target: finalResult
                  transform: toString
            """);
        Integer childId = configItemService.createOrUpdateTransmissionConfigItem(childItem);

        // 执行测试
        Map<String, Object> result = configItemService.getMergedConfigValue(childId);

        // 验证结果
        assertNotNull(result);
        @SuppressWarnings("unchecked")
        Map<String, Object> request = (Map<String, Object>) result.get("request");
        assertEquals("POST", request.get("method")); // 继承自父配置
        assertEquals("/api/v2/specific", request.get("url")); // 被子配置覆盖
        assertEquals(5000, request.get("timeout")); // 继承自父配置

        @SuppressWarnings("unchecked")
        Map<String, Object> headers = (Map<String, Object>) request.get("headers");
        assertEquals("application/json", headers.get("Content-Type")); // 继承自父配置
        assertEquals("application/json", headers.get("Accept")); // 来自子配置
        assertEquals("${customValue}", headers.get("Custom-Header")); // 来自子配置

        @SuppressWarnings("unchecked")
        Map<String, Object> validation = (Map<String, Object>) result.get("validation");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> required = (List<Map<String, Object>>) validation.get("required");
        
        // 验证 required 列表合并
        assertEquals(3, required.size());
        assertTrue(required.stream()
            .anyMatch(item -> "orgCode".equals(item.get("field")))); // 继承自父配置
        assertTrue(required.stream()
            .anyMatch(item -> "timestamp".equals(item.get("field")))); // 继承自父配置
        assertTrue(required.stream()
            .anyMatch(item -> "customField".equals(item.get("field")))); // 来自子配置
    }

    @Test
    @DisplayName("测试获取合并后的配置值-失败场景-配置项不存在")
    public void testGetMergedConfigValue_NotExists() {
        // 执行测试并验证异常
        assertThrows(ServiceException.class, () -> configItemService.getMergedConfigValue(9999));
    }

    @Test
    @DisplayName("测试获取合并后的配置值-失败场景-循环依赖")
    public void testGetMergedConfigValue_CircularDependency() {
        // 准备配置项1
        TransmissionConfigItemSaveReqVO item1 = buildConfigItemReqVO();
        Integer id1 = configItemService.createOrUpdateTransmissionConfigItem(item1);

        // 准备配置项2，依赖配置项1
        TransmissionConfigItemSaveReqVO item2 = buildConfigItemReqVO();
        item2.setConfigPackageId(2);
        item2.setParentItemId(id1);
        Integer id2 = configItemService.createOrUpdateTransmissionConfigItem(item2);

        // 修改配置项1，使其依赖配置项2，形成循环依赖
        item1.setId(id1);
        item1.setParentItemId(id2);
        
        // 执行测试并验证异常
        assertThrows(ServiceException.class, () -> configItemService.createOrUpdateTransmissionConfigItem(item1));
    }

    @Test
    @DisplayName("测试获取合并后的配置值-简单覆盖场景")
    public void testGetMergedConfigValue_SimpleOverride() {
        // 准备父配置项
        TransmissionConfigItemSaveReqVO parentItem = buildConfigItemReqVO();
        parentItem.setParentItemId(null);
        parentItem.setConfigValue("""
            api:
              url: /api/v1
              method: GET
              timeout: 5000
            """);
        Integer parentId = configItemService.createOrUpdateTransmissionConfigItem(parentItem);

        // 准备子配置项
        TransmissionConfigItemSaveReqVO childItem = buildConfigItemReqVO();
        childItem.setConfigPackageId(2);
        childItem.setParentItemId(parentId);
        childItem.setConfigValue("""
            api:
              url: /api/v2
            """);
        Integer childId = configItemService.createOrUpdateTransmissionConfigItem(childItem);

        // 执行测试
        Map<String, Object> result = configItemService.getMergedConfigValue(childId);

        // 验证结果
        assertNotNull(result);
        @SuppressWarnings("unchecked")
        Map<String, Object> api = (Map<String, Object>) result.get("api");
        assertEquals("/api/v2", api.get("url")); // 应该被子配置覆盖
        assertEquals("GET", api.get("method")); // 应该继承父配置
        assertEquals(5000, api.get("timeout")); // 应该继承父配置
    }

    @Test
    @DisplayName("测试获取合并后的配置值-复杂嵌套场景")
    public void testGetMergedConfigValue_ComplexNesting() {
        // 准备父配置项
        TransmissionConfigItemSaveReqVO parentItem = buildConfigItemReqVO();
        parentItem.setParentItemId(null);
        parentItem.setConfigValue("""
            logic:
              steps:
                - index: 1
                  type: http
                  config:
                    url: /api/patient
                    method: GET
                - index: 2
                  type: transform
                  config:
                    rules:
                      - field: name
                        value: test
              validation:
                rules:
                  - field: patientId
                    type: required
                  - field: visitDate
                    type: date
            errorHandling:
              retry:
                maxAttempts: 3
                delay: 1000
              fallback:
                default: {}
            """);
        Integer parentId = configItemService.createOrUpdateTransmissionConfigItem(parentItem);

        // 准备子配置项
        TransmissionConfigItemSaveReqVO childItem = buildConfigItemReqVO();
        childItem.setConfigPackageId(2);
        childItem.setParentItemId(parentId);
        childItem.setConfigValue("""
            logic:
              steps:
                - index: 1
                  config:
                    url: /api/v2/patient
                - index: 3
                  type: validate
                  config:
                    required: true
              validation:
                rules:
                  - field: insuranceId
                    type: required
            errorHandling:
              retry:
                maxAttempts: 5
              logging:
                level: ERROR
            """);
        Integer childId = configItemService.createOrUpdateTransmissionConfigItem(childItem);

        // 执行测试
        Map<String, Object> result = configItemService.getMergedConfigValue(childId);

        // 验证结果
        assertNotNull(result);
        @SuppressWarnings("unchecked")
        Map<String, Object> logic = (Map<String, Object>) result.get("logic");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> steps = (List<Map<String, Object>>) logic.get("steps");
        
        // 验证步骤配置合并
        assertEquals(3, steps.size()); // 应该有3个步骤
        
        // 验证 step1（合并后的）
        Map<String, Object> step1 = findStepBySequence(steps, "1");
        assertNotNull(step1);
        assertEquals("http", step1.get("type")); // 继承自父配置
        @SuppressWarnings("unchecked")
        Map<String, Object> step1Config = (Map<String, Object>) step1.get("config");
        assertEquals("/api/v2/patient", step1Config.get("url")); // 被子配置覆盖
        assertEquals("GET", step1Config.get("method")); // 继承自父配置

        // 验证 step2（来自父配置）
        Map<String, Object> step2 = findStepBySequence(steps, "2");
        assertNotNull(step2);
        assertEquals("transform", step2.get("type"));
        @SuppressWarnings("unchecked")
        Map<String, Object> step2Config = (Map<String, Object>) step2.get("config");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> rules2 = (List<Map<String, Object>>) step2Config.get("rules");
        assertEquals(1, rules2.size());
        assertEquals("name", rules2.get(0).get("field"));
        assertEquals("test", rules2.get(0).get("value"));

        // 验证 step3（来自子配置）
        Map<String, Object> step3 = findStepBySequence(steps, "3");
        assertNotNull(step3);
        assertEquals("validate", step3.get("type"));
        @SuppressWarnings("unchecked")
        Map<String, Object> step3Config = (Map<String, Object>) step3.get("config");
        assertEquals(true, step3Config.get("required"));

        // 验证 validation 规则合并
        @SuppressWarnings("unchecked")
        Map<String, Object> validation = (Map<String, Object>) logic.get("validation");
        @SuppressWarnings("unchecked")
        List<Map<String, Object>> rules = (List<Map<String, Object>>) validation.get("rules");
        assertEquals(1, rules.size()); // 子配置的规则应该完全覆盖父配置的规则
        assertEquals("insuranceId", rules.get(0).get("field"));
        
        // 验证父配置中的规则不存在
        assertFalse(rules.stream().anyMatch(rule -> "patientId".equals(rule.get("field")))); // 父配置中的规则不应该存在
        assertFalse(rules.stream().anyMatch(rule -> "visitDate".equals(rule.get("field")))); // 父配置中的规则不应该存在

        // 验证 errorHandling 配置合并
        @SuppressWarnings("unchecked")
        Map<String, Object> errorHandling = (Map<String, Object>) result.get("errorHandling");
        @SuppressWarnings("unchecked")
        Map<String, Object> retry = (Map<String, Object>) errorHandling.get("retry");
        assertEquals(5, retry.get("maxAttempts")); // 被子配置覆盖
        assertEquals(1000,retry.get("delay")); // 来自父配置
        assertNotNull(errorHandling.get("logging")); // 来自子配置
        assertNotNull(errorHandling.get("fallback")); // 子配置中没有这个字段，应该不存在
    }

    // 辅助方法：根据index查找步骤
    private Map<String, Object> findStepBySequence(List<Map<String, Object>> steps, String index) {
        return steps.stream()
                .filter(step -> index.equals(String.valueOf(step.get("index"))))
                .findFirst()
                .orElse(null);
    }

    // ====================== 辅助方法 ======================
    private TransmissionConfigItemSaveReqVO buildConfigItemReqVO() {
        TransmissionConfigItemSaveReqVO reqVO = new TransmissionConfigItemSaveReqVO();
        reqVO.setParentItemId(123);
        reqVO.setDslType(DslTypeEnum.LOGIC.getCode());
        reqVO.setNodeType(NodeTypeEnum.INTERNET_SUPERVISION_UPLOAD_OUT_PATIENT_CASE.getCode());
        reqVO.setConfigPackageId(1);
        reqVO.setApiCode("TEST001");
        reqVO.setDescription("测试配置项");
        reqVO.setConfigValue("{\"key\":\"value\"}");
        reqVO.setDisable(false);
        return reqVO;
    }

}