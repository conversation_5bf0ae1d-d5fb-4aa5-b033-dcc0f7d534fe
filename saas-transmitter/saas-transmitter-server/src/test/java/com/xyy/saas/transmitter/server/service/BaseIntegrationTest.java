package com.xyy.saas.transmitter.server.service;

import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.datasource.config.YudaoDataSourceAutoConfiguration;
import cn.iocoder.yudao.framework.mybatis.config.YudaoMybatisAutoConfiguration;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.test.config.SqlInitializationTestConfiguration;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantServicePackRelationApi;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration;
import com.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration;
import com.xyy.saas.inquiry.constant.TenantConstant;
import org.junit.jupiter.api.BeforeAll;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;
import org.springframework.mock.web.MockHttpServletRequest;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@ActiveProfiles("test")
@SpringBootTest(classes = BaseIntegrationTest.Application.class, webEnvironment = SpringBootTest.WebEnvironment.NONE, properties = {
    "spring.config.location=classpath:/application-test.yml,classpath:/application.yml" // 同时加载两个配置文件
})
@Rollback
@Transactional(rollbackFor = Throwable.class)
public abstract class BaseIntegrationTest {

    protected static final Logger log = LoggerFactory.getLogger(BaseIntegrationTest.class);

    @MockBean
    protected TenantServicePackRelationApi tenantServicePackRelationApi;

    @MockBean
    protected AdminUserApi adminUserApi;

    @MockBean
    protected TenantApi tenantApi;

    @Import({
        // DB 配置类
        DynamicDataSourceAutoConfiguration.class, // MybatisPlus 动态数据源配置类
        YudaoDataSourceAutoConfiguration.class, // 自己的 DB 配置类
        DataSourceAutoConfiguration.class, // Spring DB 自动配置类
        DataSourceTransactionManagerAutoConfiguration.class, // Spring 事务自动配置类
        DruidDataSourceAutoConfigure.class, // Druid 自动配置类
        // MyBatis 配置类
        YudaoMybatisAutoConfiguration.class, // 自己的 MyBatis 配置类
        MybatisPlusAutoConfiguration.class, // MyBatis 的自动配置类
        MybatisPlusJoinAutoConfiguration.class, // MyBatis 的Join配置类

        // 其它配置类
        SpringUtil.class,
    })
    public static class Application {

    }

    @BeforeAll
    public static void beforeAll() {
        log.info("[beforeAll][准备就绪]");
        RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
        requestAttributes.setAttribute("login_user_id", 0L, RequestAttributes.SCOPE_REQUEST);
        RequestContextHolder.setRequestAttributes(requestAttributes);
        TenantContextHolder.setTenantId(TenantConstant.DEFAULT_TENANT_ID);
    }
}
