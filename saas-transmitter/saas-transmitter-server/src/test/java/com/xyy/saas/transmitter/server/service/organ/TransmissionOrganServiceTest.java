package com.xyy.saas.transmitter.server.service.organ;

import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertThrows;

import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.enums.transmitter.OrganTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.ServiceEnvEnum;
import com.xyy.saas.transmitter.api.organ.dto.TransmissionOrganNetworkConfigDTO;
import com.xyy.saas.transmitter.server.controller.admin.organ.vo.TransmissionOrganPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.organ.vo.TransmissionOrganRespVO;
import com.xyy.saas.transmitter.server.controller.admin.organ.vo.TransmissionOrganSaveReqVO;
import com.xyy.saas.transmitter.server.dal.dataobject.organ.TransmissionOrganDO;
import com.xyy.saas.transmitter.server.dal.mysql.organ.TransmissionOrganMapper;
import com.xyy.saas.transmitter.server.service.BaseIntegrationTest;
import jakarta.annotation.Resource;
import java.util.Collections;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

@Import(TransmissionOrganServiceImpl.class)
public class TransmissionOrganServiceTest extends BaseIntegrationTest {

    @Resource
    private TransmissionOrganServiceImpl organService;

    @Resource
    private TransmissionOrganMapper organMapper;


    // ====================== 创建机构 ======================
    @Test
    @DisplayName("测试创建机构-成功场景")
    public void testCreateOrgan_Success() {
        // 准备测试数据
        TransmissionOrganSaveReqVO createReqVO = buildOrganReqVO();

        // 执行测试
        Integer id = organService.createTransmissionOrgan(createReqVO);

        // 验证结果
        assertNotNull(id);
        TransmissionOrganDO transmissionOrganDO = organMapper.selectById(id);
        assertPojoEquals(createReqVO, transmissionOrganDO, "id");
    }

    @Test
    @DisplayName("测试创建机构-失败场景-名称重复")
    public void testCreateOrgan_NameDuplicate() {
        // 准备测试数据
        TransmissionOrganSaveReqVO reqVO = buildOrganReqVO();

        // 执行测试并验证异常
        organService.createTransmissionOrgan(reqVO);
        assertThrows(ServiceException.class, () -> organService.createTransmissionOrgan(reqVO));
    }

    // ====================== 更新机构 ======================
    @Test
    @DisplayName("测试更新机构-成功场景")
    public void testUpdateOrgan_Success() {
        // 准备测试数据
        TransmissionOrganSaveReqVO reqVO = buildOrganReqVO();

        Integer id = organService.createTransmissionOrgan(reqVO);
        reqVO.setId(id);
        reqVO.setProvinceCode("110000");
        reqVO.setCityCode("110100");
        reqVO.setAreaCode("110101");
        reqVO.setProvince("北京");
        reqVO.setCity("北京市");
        reqVO.setArea("东城区");
        reqVO.setDisable(true);

        // 执行测试
        organService.updateTransmissionOrgan(reqVO);
        TransmissionOrganDO transmissionOrganDO = organMapper.selectById(id);

        //验证结果
        assertPojoEquals(reqVO, transmissionOrganDO);

    }

    @Test
    @DisplayName("测试更新机构-失败场景-机构不存在")
    public void testUpdateOrgan_NotExists() {
        // 准备测试数据
        TransmissionOrganSaveReqVO reqVO = buildOrganReqVO();
        reqVO.setId(999); // 不存在的ID

        // 执行测试并验证异常
        assertThrows(ServiceException.class, () -> organService.updateTransmissionOrgan(reqVO));
    }

    // ====================== 删除机构 ======================
    @Test
    @DisplayName("测试删除机构-成功场景")
    public void testDeleteOrgan_Success() {
        // 准备测试数据
        TransmissionOrganSaveReqVO createReqVO = buildOrganReqVO();
        Integer id = organService.createTransmissionOrgan(createReqVO);

        // 执行测试
        organService.deleteTransmissionOrgan(id);

        // 验证结果
        TransmissionOrganDO transmissionOrganDO = organMapper.selectById(id);
        assertNull(transmissionOrganDO);
    }

    @Test
    @DisplayName("测试删除机构-失败场景-机构不存在")
    public void testDeleteOrgan_NotExists() {
        // 准备测试数据
        Integer id = 999; // 不存在的ID

        // 执行测试并验证异常
        assertThrows(ServiceException.class, () -> organService.deleteTransmissionOrgan(id));
    }

    // ====================== 查询机构 ======================
    @Test
    @DisplayName("测试获取机构详情")
    public void testGetOrgan() {
        // 准备测试数据
        TransmissionOrganSaveReqVO reqVO = buildOrganReqVO();

        // 执行测试
        Integer id = organService.createTransmissionOrgan(reqVO);
        reqVO.setId(id);
        TransmissionOrganDO result = organService.getTransmissionOrgan(id);

        // 验证结果
        assertNotNull(result);
        assertPojoEquals(reqVO, result);

    }

    @Test
    @DisplayName("测试分页查询机构")
    public void testGetOrganPage() {
        // 准备测试数据
        TransmissionOrganPageReqVO reqVO = TransmissionOrganPageReqVO.builder()
            .name("测试机构")
            .organType(1)
            .build();

        TransmissionOrganSaveReqVO reqVO1 = buildOrganReqVO();
        Integer id1 = organService.createTransmissionOrgan(reqVO1);
        reqVO1.setId(id1);
        TransmissionOrganSaveReqVO reqVO2 = buildOrganReqVO();
        reqVO2.setName("测试机构2");
        reqVO2.setOrganType(OrganTypeEnum.HIS.getCode());
        Integer id2 = organService.createTransmissionOrgan(reqVO2);
        TransmissionOrganSaveReqVO reqVO3 = buildOrganReqVO();
        reqVO3.setName("正式机构");
        Integer id3 = organService.createTransmissionOrgan(reqVO3);

        // 执行测试
        PageResult<TransmissionOrganRespVO> result = organService.getTransmissionOrganPage(reqVO);

        // 验证结果
        assertNotNull(result);
        assertEquals(1L, result.getTotal());
        assertEquals(1, result.getList().size());

        TransmissionOrganRespVO respVO = result.getList().get(0);

        assertPojoEquals(reqVO1, respVO);

    }

    // ====================== 辅助方法 ======================
    private TransmissionOrganSaveReqVO buildOrganReqVO() {
        TransmissionOrganSaveReqVO reqVO = new TransmissionOrganSaveReqVO();
        reqVO.setName("测试机构");
        reqVO.setOrganType(1);
        reqVO.setNetworkConfig(Collections.singletonList(buildNetworkConfig()));
        reqVO.setBasicConfig("{\"key\":\"value\"}");
        reqVO.setDisable(false);
        reqVO.setProvinceCode("330000");
        reqVO.setCityCode("330100");
        reqVO.setAreaCode("330106");
        reqVO.setProvince("浙江省");
        reqVO.setCity("杭州市");
        reqVO.setArea("西湖区");
        reqVO.setLogo("https://example.com/logo.png");
        reqVO.setRemark("备注信息");
        reqVO.setDisable(false);
        return reqVO;
    }

    private TransmissionOrganNetworkConfigDTO buildNetworkConfig() {
        return TransmissionOrganNetworkConfigDTO.builder()
            .code("TEST")
            .name("测试网络")
            .env(ServiceEnvEnum.TEST.getCode())
            .disable(false)
            .build();
    }
}