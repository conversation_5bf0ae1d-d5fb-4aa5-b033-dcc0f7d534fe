dslType: contract
enable: true
name: 电子处方医保电子签名
format: json
functions:
  - path: "'/fixmedins/rxFixmedinsSign'"
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        # 定点机构代码：定点机构唯一标识，用于识别机构对应的医保数字证书，CertId和其保持一致
        "fixmedinsCode": business.data['network']['hospitalCode']
        # 原始待签名处方文件：文件base64的字符值
        "originalRxFile": T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionPdfBase64'], '')
        # 原始待签名处方信息：JSONString序列化(对第*******中1-20字段进行JSONString)后的base64字符值
        # 这里需要将1-20字段组装成JSON字符串后进行base64编码，具体实现需要在业务逻辑中处理
        "originalValue":
          # 1. 处方追溯码：有效时间和处方有效时间保持一致，上传时每张处方只能使用一次
          "rxTraceCode": "outputAll.get(0)[0].get('rxTraceCode')"
          # 2. 医保处方编号
          "hiRxno": "outputAll.get(0)[0].get('hiRxno')"
          # 3. 医保就诊ID：参保病人信息字段(注：医保门诊挂号时返回)
          "mdtrtId": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['medicalRegistrationInfo']?.medicalVisitId, '')"
          # 4. 患者姓名
          "patnName": business.data['data']['fullName']
          # 5. 人员证件类型：参照人员证件类型(psn_cert_type)
          "psnCertType": "'01'"
          # 6. 证件号码
          "certno": business.data['aux']['inquiryDetailInfo']?.patientIdCard
          # 7. 定点医疗机构名称
          "fixmedinsName": business.data['network']['hospitalName']
          # 8. 定点医疗机构编号
          "fixmedinsCode": business.data['network']['hospitalCode']
          # 9. 开方医保医师代码：国家医保医师代码
          "drCode": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['doctorInfo']?.doctorMedicareNo, '')"
          # 10. 开方医师姓名
          "prscDrName": business.data['data']['doctorName']

          # 11. 审方药师科室名称
          "pharDeptName": "'-'"
          # 12. 审方药师科室编号：与医药机构服务的科室管理中上传的hosp_dept_codg医院科室编码字段保持一致
          "pharDeptCode": "'-'"
          # 13. 审方药师职称编码：参照审方药师职称编码(phar_pro_tech_duty)
          "pharProfttlCodg": "''"
          # 14. 审方药师职称名称
          "pharProfttlName": "''"
          # 15. 审方医保药师代码：国家医保药师代码
          "pharCode": "'HY610199001554'"
          # 16. 审方药师证件类型：参照人员证件类型(psn_cert_type)
          "pharCertType": "'01'"
          # 17. 审方药师证件号码
          "pharCertno": "'610104197605206180'"
          # 18. 审方药师姓名
          "pharName": "'李红'"
          # 19. 审方药师执业资格证号
          "pharPracCertNo": "''"
          # 20. 医疗机构药师审方时间：yyyy-MM-ddHH:mm:ss
          "pharChkTime": "T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(business.data['data']['auditPrescriptionTime'])"

    suffixedInExpression: T(com.xyy.saas.localserver.medicare.dsl.util.NationalSecretAlgorithmUtil).encryptMsg('91A9F9BF43944969AAE81414E90F5271','MkvTmPcF4KSF6tfWRkRHBICtFc/86vk6PAzC/3xFZqw=','716693E637304DF99717866FDD5AECCE',T(com.xyy.saas.localserver.medicare.dsl.util.DslBusinessUtil).changeKeyBase64Value(#root,originalValue))
    prefixedOutExpression: T(com.xyy.saas.localserver.medicare.dsl.util.NationalSecretAlgorithmUtil).sm4Decrypt('91A9F9BF43944969AAE81414E90F5271','MkvTmPcF4KSF6tfWRkRHBICtFc/86vk6PAzC/3xFZqw=',_this_)

    response:
      "code": "['code']"  # 响应状态码
      "message": "['message']"  # 响应异常信息
      "success": "['success']"  # 响应标识
      "rxFile": "['rxFile']"  # 医保电子签名后处方文件originalRxFile的base64值
      "signDigest": "['signDigest']"  # 医保电子签名后处方信息originalValue的签名结果值
      "signCertSn": "['signCertSn']"  # 签名机构证书SN
      "signCertDn": "['signCertDn']"  # 签名机构证书DN
