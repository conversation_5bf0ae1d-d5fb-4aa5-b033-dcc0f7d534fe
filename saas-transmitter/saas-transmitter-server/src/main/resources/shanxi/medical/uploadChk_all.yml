dslType: contract
enable: true
name: 电子处方上传预核验
format: json
common: false
functions:
  - path: "business.data['network']['networkItem']['epc'] + '/fixmedins/uploadChk'"
    name: 电子处方上传预核验
    bodyStrategy: jsonNode
    timeout: 120
    request:
      method: POST
      body:
        # 处方基本信息
        "mdtrtCertType": "'02'"  # 就诊凭证类型：01-电子凭证令牌、02-身份证号、03-社会保障卡号
        "mdtrtCertNo": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['inquiryDetailInfo']?.patientIdCard, '')"  # 就诊凭证编号：就诊凭证类型为"01"时填写电子凭证令牌，为"02"时填写身份证号，为"03"时填写社会保障卡卡号
        "cardSn": "''"  # 卡识别码：就诊凭证类型为"03"时必填
        "ecToken": "''"  # 电子凭证令牌：线下场景(医院就诊)使用，就诊凭证类型：01
        "authNo": "''"  # 电子凭证线上身份核验流水号：线上场景互联网医院问诊时使用，就诊凭证类型：02
        "bizTypeCode": "'01'"  # 业务类型代码：01-定点医疗机构就诊，02-互联网医院问诊
        "insuPlcNo": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['medicalRegistrationInfo']?.insuredAreaNo, '')"  # 参保地编号：默认取电子凭证返回的参保地或就诊登记时参保地信息；患者有多个参保地信息时必传
        "mdtrtareaNo": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['medicalRegistrationInfo']?.tenantAreaNo, '')"  # 就医地编号：默认取就诊登记时就医地信息；医疗机构有多个定点协议统筹区时必传
        "hospRxno": business.data['data']['pref']  # 定点医疗机构处方编号：院内内部处方号，单笔处方不可重复
        "initRxno": "''"  # 续方的原处方编号
        "rxTypeCode": "T(java.util.Objects).equals(business.data['data']['medicineType'],0) ? '1' : '2'"  # 处方类别代码：参考处方类别代码(rx_type_code)
        "prscTime": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(business.data['data'].outPrescriptionTime)  # 开方时间：yyyy-MM-ddHH:mm:ss
        "rxDrugCnt": "T(java.util.Objects).equals(business.data['data']['medicineType'],0) ? business.data['aux']['prescriptionDetail'].size() : business.data['data'].ext.tcmTotalDosage"   # 药品类目数(剂数)：非中药时为处方药品类目数量，中药时为药品总剂数
        "rxUsedWayCodg": "T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDict(business,'drug_directions',T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['data'].ext.tcmDirections, ''))"
        # 处方整剂用法编号：(注：中药汤剂处方使用字段)，参考药物使用-途径代码(drug_medc_way_code)
        "rxUsedWayName": "business.data['data'].ext.tcmDirections"  # 处方整剂用法名称：(注：中药汤剂处方使用字段)
        "rxFrquCodg": "'62'" # 必要时使用
        # 处方整剂频次编号：(注：中药汤剂处方使用字段)，参考使用频次(used_frqu)
        "rxFrquName": "'必要时使用'"  # 处方整剂频次名称：(注：中药汤剂处方使用字段)
        "rxDosunt": "'副'"  # 处方整剂剂量单位：(注：中药汤剂处方使用字段)
        "rxDoscnt": "business.data['data'].ext.tcmDailyDosage"  # 处方整剂单次剂量数：(注：中药汤剂处方使用字段)
        "rxDrordDscr": "''"  # 处方整剂医嘱说明：(注：中药汤剂处方使用字段)
        "valiDays": "'3'"  # 处方有效天数
        "valiEndTime": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(business.data['data'].outPrescriptionTime.plusDays(3))  # 有效截止时间：开方时间+处方有效天数=有效截止时间
        "reptFlag": "'0'"  # 复用(多次)使用标志：0-否、1-是
        "maxReptCnt": "''"  # 最大使用次数
        "reptdCnt": "''"  # 已使用次数
        "minInrvDays": "''"  # 使用最小间隔(天数)
        "rxCotnFlag": "'0'"  # 续方标志：0-否、1-是，默认否；预留字段
        "longRxFlag": "'0'"  # 长期处方标志：0-否、1-是，默认为否；预留字段
        "extensionFlag": "'0'"  # 外延标志：0-否、1-是

        # 处方明细信息(节点标识rxdrugdetail)
        "rxdrugdetail":
          - "medListCodg": business.data['aux']['prescriptionDetail'][_index_]['productPref']  # 医疗目录编码：即医保药品编码
            "fixmedinsHilistId": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['productPref'], '')"  # 定点医药机构目录编号：即院内药品编码
            "hospPrepFlag": "'0'"  # 医疗机构制剂标志：0-否、1-是，默认否
            "rxItemTypeCode": "T(java.util.Objects).equals(business.data['data']['medicineType'],0) ? '11' : '13'"  # 处方项目分类代码：参考处方项目分类代码(rx_item_type_code)
            "rxItemTypeName": "''"  # 处方项目分类名称
            "tcmdrugTypeName": "''"  # 中药类别名称
            "tcmdrugTypeCode": "T(java.util.Objects).equals(business.data['data']['medicineType'],0) ? '' : '9'"  # 中药类别代码：参考处方项目分类代码(tcmdrug_type_name)，中药处方必填
            "tcmherbFoote": "''"  # 草药脚注
            "mednTypeCode": "''"  # 药物类型代码：参考药物类型代码(medn_type_code)，(注：可按院内内部的药品类型分类)
            "mednTypeName": "''"  # 药物类型
            "mainMedcFlag": "'0'"  # 主要用药标志：0-否、1-是
            "urgtFlag": "'0'"  # 加急标志：0-否、1-是
            "basMednFlag": "'0'"  # 基本药物标志：0-否、1-是
            "impDrugFlag": "'0'"  # 是否进口药品：0-否、1-是
            "drugProdname": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['productName'], '')"  # 药品商品名
            "drugGenname": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['commonName'], '')"  # 药品通用名
            "drugDosform": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_].medicalCatalogDetail?.dosageForm, '-')"  # 药品剂型
            "drugSpec": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['attributeSpecification'], 'g')"  # 药品规格
            "prdrName": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['manufacturer'], '')"  # 生产厂家
            "medcWayCodg": business.data['aux']['prescriptionDetail'][_index_]['directions']  # 用药途径代码：参考药物使用-途径代码(drug_medc_way_code) (注：可使用院内内部代码)
            "medcWayDscr": business.data['aux']['prescriptionDetail'][_index_]['directions']  # 用药途径描述
            "medcBegntime": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(business.data['data'].outPrescriptionTime)  # 用药开始时间：yyyy-MM-ddHH:mm:ss
            "medcEndtime": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(business.data['data'].outPrescriptionTime.plusDays(3))  # 用药结束时间：yyyy-MM-ddHH:mm:ss
            "medcDays": "'3'"  # 用药天数
            "drugPric": "''"  # 药品单价：按drugDosunt计价
            "drugSumamt": "''"  # 药品总金额：drugCnt×药品单价(可为空)
            "drugCnt": business.data['aux']['prescriptionDetail'][_index_]['quantity']  # 药品总用药量：(取药或处方流转时药品医保结算使用的数量)，按院内"发药单位转换系数"换算
            "drugDosunt": business.data['aux']['prescriptionDetail'][_index_]['packageUnit']  # 药品总用药量单位：(即发药计价单位，取药或处方流转时药品医保结算使用的单位；如"片"或"盒")，处方项目分类代码(rx_item_type_code)值为11、12时必填
            "drugTotlcnt": business.data['aux']['prescriptionDetail'][_index_]['quantity']  # 所需药品库存数量：(按所需库存(包装)单位计算取整后的数量)，处方项目分类代码(rx_item_type_code)值为11、12时必填
            "drugTotlcntEmp": business.data['aux']['prescriptionDetail'][_index_]['packageUnit']  # 所需药品库存单位：(即库存(包装)单位，如"盒")，处方项目分类代码(rx_item_type_code)值为11、12时必填
            "sinDoscnt": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['singleDose'], '')"  # 单次用量：处方项目分类代码(rx_item_type_code)值为11、12时必填
            "sinDosunt": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['singleUnit'], '')"  # 单次剂量单位：(即开方单位或剂量单位，如"mg")，处方项目分类代码(rx_item_type_code)值为11、12时必填
            "usedFrquCodg": business.data['aux']['prescriptionDetail'][_index_]['useFrequency']  # 使用频次编码：参考使用频次(used_frqu)，处方项目分类代码(rx_item_type_code)值为11、12时必填，(注：可使用院内内部代码)
            "usedFrquName": business.data['aux']['prescriptionDetail'][_index_]['useFrequency']  # 使用频次名称：处方项目分类代码(rx_item_type_code)值为11、12时必填
            "hospApprFlag": "'1'"  # 医院审批标志：参照字典(hosp_appr_flag)，医院审批标志，配合目录的限制使用标志使用

        # 就诊信息(节点标识：mdtrtinfo)
        "mdtrtinfo":
          "fixmedinsName": "'西电集团医院'"  # 定点医疗机构名称
          "fixmedinsCode": "'H61010400856'"  # 定点医疗机构编号
          "mdtrtId": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['medicalRegistrationInfo']?.medicalVisitId, '123')"  # 医保就诊ID：参保病人信息字段(注：医保门诊挂号时返回)
          "medType": "'11'"  # 医疗类别：参考医疗类别(med_type)
          "iptOtpNo": business.data['data']['inquiryPref']  # 住院/门诊号
          "otpIptFlag": "'1'"  # 门诊住院标识：1-门诊、2-住院，值为空时默认为1门诊
          "psnNo": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['medicalRegistrationInfo']?.psnNo, '123')"  # 医保人员编号
          "patnName": business.data['data']['fullName']  # 患者姓名
          "psnCertType": "'01'"  # 人员证件类型：参照人员证件类型(psn_cert_type)
          "certno": business.data['aux']['inquiryDetailInfo']?.patientIdCard  # 证件号码
          "patnAge": "T(com.xyy.saas.inquiry.util.IdCardUtil).getAgeByIdCard(business.data['aux']['inquiryDetailInfo']?.patientIdCard)"  # 年龄
          "patnHgt": "''"  # 患者身高
          "patnWt": "''"  # 患者体重
          "gend": "T(java.util.Objects).equals(business.data['data']['patientSex'],1) ? '1' : '2'"  # 性别：参考性别(gend)
          "birctrlType": "''"  # 计划生育手术类别：生育门诊按需录入
          "birctrlMatnDate": "''"  # 计划生育手术或生育日期：生育门诊按需录入，yyyy-MM-dd
          "matnType": "''"  # 生育类别
          "gesoVal": "''"  # 妊娠(孕周)
          "nwbFlag": "'0'"  # 新生儿标志：0-否、1-是
          "nwbAge": "''"  # 新生儿日、月龄
          "suckPrdFlag": "'0'"  # 哺乳期标志：0-否、1-是
          "algsHis": "''"  # 过敏史
          "prscDeptName": T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDictLabel(business,'dept_dict',business.data['data']['deptPref'],business.data['data']['deptName'])  # 开方科室名称
          "prscDeptCode": T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDict(business,'dept_dict',business.data['data']['deptPref'])  # 开方科室编号：与医药机构服务的科室管理中上传的hosp_dept_codg医院科室编码字段保持一致
          "drCode": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['doctorInfo']?.doctorMedicareNo, '')"  # 开方医保医师代码：国家医保医师代码
          "prscDrName": business.data['data']['doctorName']  # 开方医师姓名
          "prscDrCertType": "'01'"  # 开方医师证件类型：参照人员证件类型(psn_cert_type)
          "prscDrCertno": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['doctorInfo']?.idCard, '')"  # 开方医师证件号码
          "drProfttlCodg": "T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDict(business,'doctor_title',business.data['aux']['doctorInfo']?.titleCode)"  # 医生职称编码：参照开单医生职称(drord_dr_profttl)
          "drProfttlName": "T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDictLabel(business,'doctor_title',business.data['aux']['doctorInfo']?.titleCode,business.data['aux']['doctorInfo']?.titleCode)"  # 医生职称名称
          "drDeptCode": T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDict(business,'dept_dict',business.data['data']['deptPref'])  # 医生科室编码：与医药机构服务的科室管理中上传的hosp_dept_codg医院科室编码字段保持一致
          "drDeptName": T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDictLabel(business,'dept_dict',business.data['data']['deptPref'],business.data['data']['deptName'])  # 医生科室名称：与医药机构服务的科室管理中上传的hosp_dept_codg医院科室编码字段保持一致
          "caty": T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDict(business,'dept_dict',business.data['data']['deptPref'])  # 科别：参照附录A:科室代码(dept)
          "mdtrtTime": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(business.data['data'].inquiryStartTime)  # 就诊时间：yyyy-MM-ddHH:mm:ss
          "diseCodg": "''"  # 病种编码：按照标准编码填写，医疗类别(medType)为门诊慢特病时必传
          "diseName": "''"  # 病种名称
          "spDiseFlag": "'0'"  # 特殊病种标志：0-否、1-是
          "maindiagCode": "business.data['data'].diagnosisCode[0]" #business.data['aux']['clinicalCase']?.diagnosis[0]['diagnosisCode']  # 主诊断代码：医保疾病诊断代码
          "maindiagName": "business.data['data'].diagnosisName[0]" #business.data['aux']['clinicalCase']?.diagnosis[0]['diagnosisName']  # 主诊断名称
          "diseCondDscr": T(org.apache.commons.lang3.StringUtils).joinWith(',',business.data['aux']['inquiryDetailInfo']?.mainSuit)  # 疾病病情描述
          "hiFeesetlType": "''"  # 医保费用结算类型：参考医保费用结算类型(hi_feesetl_type)
          "hiFeesetlName": "''"  # 医保费用类别名称
          "rgstFee": "''"  # 挂号费
          "medfeeSumamt": "''"  # 医疗费总额：注：本次需要结算的医疗费用总额，院外购药时不包括处方药品费用
          "fstdiagFlag": "'1'"  # 是否初诊：0-否、1-是
        # 诊断信息(节点表示：diseinfo)
        "diseinfo":
          - "diagCode": "business.data['data'].diagnosisCode[_index_]" #business.data['aux']['clinicalCase']?.diagnosis[_index_]['diagnosisCode']  # 诊断代码：医保疾病诊断代码
            "diagName": "business.data['data'].diagnosisName[_index_]" # business.data['aux']['clinicalCase']?.diagnosis[_index_]['diagnosisName']  # 诊断名称

            "diagType": "T(java.util.Objects).equals(business.data['data']['medicineType'],0) ? '1' : (_index_ == 0 ? '2' : '3')"  # 诊断类别：参考诊断类别(diag_type)
            "maindiagFlag": "(_index_ == 0 ? '1' : '0')"  # 主诊断标志：0-否、1-是
            "diagSrtNo": _index_ + 1  # 诊断排序号

            "diagDept": T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDict(business,'dept_dict',business.data['data']['deptPref'])  # 诊断科室：与医药机构服务的科室管理中上传的hosp_dept_codg医院科室编码字段保持一致
            "diagDrNo": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['doctorInfo']?.doctorMedicareNo, '')"  # 诊断医生编码：国家医保医师代码
            "diagDrName": business.data['aux']['doctorInfo']?.name  # 诊断医生姓名
            "diagTime": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(business.data['data'].outPrescriptionTime)  # 诊断时间：yyyy-MM-ddHH:mm:ss
            "tcmDiseCode": "T(java.util.Objects).equals(business.data['data']['medicineType'],0) ? '' : business.data['data'].diagnosisCode[_index_]" #"business.data['aux']['clinicalCase']?.diagnosis[_index_]['diagnosisCode']"  # 中医病名代码：diagType为2，3时上传
            "tcmDiseName": "T(java.util.Objects).equals(business.data['data']['medicineType'],0) ? '' : business.data['data'].diagnosisName[_index_]" #"business.data['aux']['clinicalCase']?.diagnosis[_index_]['diagnosisName']"  # 中医病名：diagType为2，3时上传
            "tcmsympCode": "business.data['data'].ext.tcmSyndromeCode"  # 中医证候代码：diagType为2，3时上传
            "tcmsymp": "business.data['data'].ext.tcmSyndromeName"  # 中医证候：diagType为2，3时上传

    suffixedInExpression: T(com.xyy.saas.localserver.medicare.dsl.util.NationalSecretAlgorithmUtil).encryptMsg('227F1ECD97404770820DAE9E9C0F784F','2E506F2C4E584BF78F02BE4B0BC3750D','VzdpGk/4EtA5hY4eJH0wSSuuDIhspQBsZewt/ergdhg=',#root)

    prefixedOutExpression: T(com.xyy.saas.localserver.medicare.dsl.util.NationalSecretAlgorithmUtil).decryptMsg('227F1ECD97404770820DAE9E9C0F784F','2E506F2C4E584BF78F02BE4B0BC3750D','BPX8zuOjj9AUDFqy4h0C/5W8J4fO/Yg2xO7FEABbrafhcyQXPyPAYANa+6yC2gDszMrNpbvoDVadJycUm4bCnjk=',#root)

    response:
      "code": "['code']"  # 响应状态码
      "message": "['message']"  # 响应异常信息
      "success": "['success']"  # 响应标识
      "medicareRxTraceCode": "['rxTraceCode']"  # 处方追溯码
      "hiRxno": "['hiRxno']"  # 医保处方编号
      "medicareRxNo": "['hiRxno']"  # 医保处方编号

    result:
      "success": "output['code'] == 0"  # 响应状态码
      "tips": "output['message']"  # 响应异常信息

  - path: "business.data['network']['networkItem']['signEpc'] + '/fixmedins/rxFixmedinsSign'"
    name: 电子处方医保电子签名
    bodyStrategy: jsonNode
    timeout: 120
    request:
      method: POST
      header:
        "appid": "'H61010400856_01'"
        "nonce": T(cn.hutool.core.util.IdUtil).fastSimpleUUID()
        "timestamp": T(java.lang.System).currentTimeMillis()
        "sign": T(com.xyy.saas.transmitter.server.util.transmission.internet.RequestSignUtil).signWithSHA256RSA(requestHeader['appid'],requestHeader['nonce'],requestHeader['timestamp'],business.data['network']?.thirdPartyPrivateKey)
      body:
        # 定点机构代码：定点机构唯一标识，用于识别机构对应的医保数字证书，CertId和其保持一致
        "fixmedinsCode": "'H61010400856'"
        # 原始待签名处方文件：文件base64的字符值
        "originalRxFile": T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionPdfBase64'], '')
        # 原始待签名处方信息：JSONString序列化(对第*******中1-20字段进行JSONString)后的base64字符值
        # 这里需要将1-20字段组装成JSON字符串后进行base64编码，具体实现需要在业务逻辑中处理
        "originalValue":
          # 1. 处方追溯码：有效时间和处方有效时间保持一致，上传时每张处方只能使用一次
          "rxTraceCode": "output.get('out_0').get('rxTraceCode')"
          # 2. 医保处方编号
          "hiRxno": "output.get('out_0').get('hiRxno')"
          # 3. 医保就诊ID：参保病人信息字段(注：医保门诊挂号时返回)
          "mdtrtId": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['medicalRegistrationInfo']?.medicalVisitId, '')"
          # 4. 患者姓名
          "patnName": business.data['data'].fullName
          # 5. 人员证件类型：参照人员证件类型(psn_cert_type)
          "psnCertType": "'01'"
          # 6. 证件号码
          "certno": business.data['aux']['inquiryDetailInfo']?.patientIdCard
          # 7. 定点医疗机构名称
          "fixmedinsName": "'西电集团医院'"
          # 8. 定点医疗机构编号
          "fixmedinsCode": "'H61010400856'"
          # 9. 开方医保医师代码：国家医保医师代码
          "drCode": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['doctorInfo']?.doctorMedicareNo, '')"
          # 10. 开方医师姓名
          "prscDrName": business.data['data'].doctorName

          # 11. 审方药师科室名称
          "pharDeptName": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['pharmacistInfo']?.pharmacistHospitalDeptPref, '-')"
          # 12. 审方药师科室编号：与医药机构服务的科室管理中上传的hosp_dept_codg医院科室编码字段保持一致
          "pharDeptCode": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['pharmacistInfo']?.pharmacistHospitalDeptName, '-')"
          # 13. 审方药师职称编码：参照审方药师职称编码(phar_pro_tech_duty)
          "pharProfttlCodg": "''"
          # 14. 审方药师职称名称
          "pharProfttlName": "''"
          # 15. 审方医保药师代码：国家医保药师代码
          "pharCode": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['pharmacistInfo']?.pharmacistHospitalPref, 'HY610199001554')"
          # 16. 审方药师证件类型：参照人员证件类型(psn_cert_type)
          "pharCertType": "'01'"
          # 17. 审方药师证件号码
          "pharCertno": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['pharmacistInfo']?.idCard, '')"
          # 18. 审方药师姓名
          "pharName": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['pharmacistInfo']?.name, '')"
          # 19. 审方药师执业资格证号
          "pharPracCertNo": "''"
          # 20. 医疗机构药师审方时间：yyyy-MM-ddHH:mm:ss
          "pharChkTime": "T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(business.data['data'].auditPrescriptionTime)"

    suffixedInExpression: T(com.xyy.saas.localserver.medicare.dsl.util.DslBusinessUtil).changeKeyBase64Value(#root,'originalValue')

    #    prefixedOutExpression: T(com.xyy.saas.localserver.medicare.dsl.util.NationalSecretAlgorithmUtil).decryptMsg('227F1ECD97404770820DAE9E9C0F784F','2E506F2C4E584BF78F02BE4B0BC3750D','BPX8zuOjj9AUDFqy4h0C/5W8J4fO/Yg2xO7FEABbrafhcyQXPyPAYANa+6yC2gDszMrNpbvoDVadJycUm4bCnjk=',#root)

    response:
      "code": "['code']"  # 响应状态码
      "message": "['message']"  # 响应异常信息
      "rxFile": "['data']['rxFile']"  # 医保电子签名后处方文件originalRxFile的base64值
      "signDigest": "['data']['signDigest']"  # 医保电子签名后处方信息originalValue的签名结果值
      "signCertSn": "['data']['signCertSn']"  # 签名机构证书SN
      "signCertDn": "['data']['signCertDn']"  # 签名机构证书DN

    result:
      "success": "output['code'] == 200"  # 响应状态码
      "tips": "output['message']"  # 响应异常信息

  - path: "business.data['network']['networkItem']['epc'] + '/fixmedins/rxFileUpld'"
    name: 电子处方上传
    bodyStrategy: jsonNode
    timeout: 180
    request:
      method: POST
      body:
        # 1. 处方追溯码：有效时间和处方有效时间保持一致，上传时每张处方只能使用一次
        "rxTraceCode": "output.get('out_0').get('rxTraceCode')"
        # 2. 医保处方编号
        "hiRxno": "output.get('out_0').get('hiRxno')"
        # 3. 医保就诊ID：参保病人信息字段(注：医保门诊挂号时返回)
        "mdtrtId": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['medicalRegistrationInfo']?.medicalVisitId, '')"
        # 4. 患者姓名
        "patnName": business.data['data'].fullName
        # 5. 人员证件类型：参照人员证件类型(psn_cert_type)
        "psnCertType": "'01'"
        # 6. 证件号码
        "certno": business.data['aux']['inquiryDetailInfo']?.patientIdCard
        # 7. 定点医疗机构名称
        "fixmedinsName": "'西电集团医院'"
        # 8. 定点医疗机构编号
        "fixmedinsCode": "'H61010400856'"
        # 9. 开方医保医师代码：国家医保医师代码
        "drCode": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['doctorInfo']?.doctorMedicareNo, '')"
        # 10. 开方医师姓名
        "prscDrName": business.data['data'].doctorName

        # 11. 审方药师科室名称
        "pharDeptName": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['pharmacistInfo']?.pharmacistHospitalDeptPref, '-')"
        # 12. 审方药师科室编号：与医药机构服务的科室管理中上传的hosp_dept_codg医院科室编码字段保持一致
        "pharDeptCode": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['pharmacistInfo']?.pharmacistHospitalDeptName, '-')"
        # 13. 审方药师职称编码：参照审方药师职称编码(phar_pro_tech_duty)
        "pharProfttlCodg": "''"
        # 14. 审方药师职称名称
        "pharProfttlName": "''"
        # 15. 审方医保药师代码：国家医保药师代码
        "pharCode": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['pharmacistInfo']?.pharmacistHospitalPref, 'HY610199001554')"
        # 16. 审方药师证件类型：参照人员证件类型(psn_cert_type)
        "pharCertType": "'01'"
        # 17. 审方药师证件号码
        "pharCertno": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['pharmacistInfo']?.idCard, '')"
        # 18. 审方药师姓名
        "pharName": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['pharmacistInfo']?.name, '')"
        # 19. 审方药师执业资格证号
        "pharPracCertNo": "''"
        # 20. 医疗机构药师审方时间：yyyy-MM-ddHH:mm:ss
        "pharChkTime": "T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(business.data['data'].auditPrescriptionTime)"

        # 21. 处方原件：医保电子签名后的处方文件base64字符(PDF或OFD格式)
        "rxFile": "output.get('rxFile')"
        # 22. 处方信息签名值：医保电子签名后处方信息的签名结果
        "signDigest": "output.get('signDigest')"

    suffixedInExpression: T(com.xyy.saas.localserver.medicare.dsl.util.NationalSecretAlgorithmUtil).encryptMsg('227F1ECD97404770820DAE9E9C0F784F','2E506F2C4E584BF78F02BE4B0BC3750D','VzdpGk/4EtA5hY4eJH0wSSuuDIhspQBsZewt/ergdhg=',#root)
    prefixedOutExpression: T(com.xyy.saas.localserver.medicare.dsl.util.NationalSecretAlgorithmUtil).decryptMsg('227F1ECD97404770820DAE9E9C0F784F','2E506F2C4E584BF78F02BE4B0BC3750D','BPX8zuOjj9AUDFqy4h0C/5W8J4fO/Yg2xO7FEABbrafhcyQXPyPAYANa+6yC2gDszMrNpbvoDVadJycUm4bCnjk=',#root)

    response:
      "code": "['code']"  # 响应状态码
      "message": "['message']"  # 响应异常信息
      "success": "['success']"  # 响应标识
      "rxChkStatus": "['rxStasCodg']"  # 医保处方状态编码：参考(rx_stas_codg)
      "rxStasName": "['rxStasName']"  # 医保处方状态名称
      "fixMedicalInstitutionsCode": "'H61010400856'"  # 医保处方状态名称
      "fixMedicalInstitutionsName": "'西电集团医院'"  # 医保处方状态名称

    result:
      "success": "output['code'] == 0"  # 响应状态码
      "tips": "output['message']"  # 响应异常信息