name: '人员信息查询'
enable: true
dslType: contract
#domain: "'http://*************:21001'"
#domain: business.data['network']['networkItem']['medicare']
format: json
functions:
  - path: "'/fsi/api/fsiPsnInfoService/queryPsnInfo'"
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "infno": "'1101'" # 接口号
        "insuplc_admdvs": "'610000'"
        "sign_no": "business.data['aux']['medicareSigninInfo']?.signNo"
        "input":
          "data":
            "mdtrt_cert_type": "'02'"
            "mdtrt_cert_no": "business.data['data']['idCardNo']"
            "card_sn": "''"
            "begntime": "''"
            "psn_cert_type": "''"
            "certno": "''"
            "psn_name": "''"
    response:
      # 公共字段
      "infcode": "['infcode']" # 错误代码
      "inf_refmsgid": "['inf_refmsgid']" # 接收方报文ID
      "refmsg_time": "['refmsg_time']" # 接收报文时间
      "respond_time": "['respond_time']" # 响应报文时间
      "err_msg": "['err_msg']" # 错误信息
      # 映射到medicarePersonInfo属性 - 使用最简SpEL语法
      #      "medicarePersonInfo": "['output'] != null && ['infcode'] == 0 ? {'baseInfo': {'psnNo': ['output']['baseinfo']['psn_no'], 'psnCertType': ['output']['baseinfo']['psn_cert_type'], 'certno': ['output']['baseinfo']['certno'], 'psnName': ['output']['baseinfo']['psn_name'], 'gender': ['output']['baseinfo']['gend'], 'nationality': ['output']['baseinfo']['naty'], 'birthday': ['output']['baseinfo']['brdy'], 'age': ['output']['baseinfo']['age']}, 'insuranceInfos': ['output']['insuinfo']} : null"
      "code": "['infcode']" # 错误代码
      "message": "['err_msg']" # 错误信息
      "output":
        "baseinfo":
          "psnNo": "['psn_no']"
          "psnCertType": "['psn_cert_type']"
          "certno": "['certno']"
          "psnName": "['psn_name']"
          "gender": "['gend']"
          "nationality": "['naty']"
          "birthday": "['brdy']"
          "age": "['age']"
        "insuinfo":
          - "insuranceType": "['insutype']"
            "cvlservFlag": "['cvlserv_flag']"
            "insuranceStatus": "['psn_insu_stas']"
            "psnType": "['psn_type']"
            "empName": "['emp_name']"
            "insuredAreaNo": "['insuplc_admdvs']"
            "beginDate": "['psn_insu_date']"
            "endDate": "['paus_insu_date']"

    # 结果判断配置
    result:
      #      success: "output.get('infcode') == 0" # 成功条件：infcode为0
      "success": "output['code'] == 0"  # 响应状态码
      skip: false # 失败时抛出异常
      tips: "'1101人员信息查询失败: ' + output['message']" # 错误提示