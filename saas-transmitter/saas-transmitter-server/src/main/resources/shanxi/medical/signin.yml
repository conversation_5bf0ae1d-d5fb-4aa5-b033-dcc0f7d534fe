name: '医保签到'
enable: true
dslType: contract
format: json
functions:
  - path: "'/fsi/api/signInSignOutService/signIn'"
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "infno": "'9001'" #接口号
        "insuplc_admdvs": "'610000'"
        "sign_no": "''"
        "input":
          "signIn":
            "opterNo": "'10000'"
            "mac": "'D0-27-88-65-FC-3F'"
            "ip": "'************'"
    response:
      # 公共字段 - 满足common.yml的要求
      "infcode": "['infcode']" # 错误代码
      "inf_refmsgid": "['inf_refmsgid']" # 接收方报文ID
      "refmsg_time": "['refmsg_time']" # 接收报文时间
      "respond_time": "['respond_time']" # 响应报文时间
      "err_msg": "['err_msg']" # 错误信息
      # 使用Spring EL表达式直接构建medicareSigninExt Map对象
      #      "medicareSigninExt": "{'signNo': ['output']['signinoutb']['sign_no'], 'medicareInstitutionCode': 'H61010400856', 'operatorCode': '10000', 'operatorName': '管理员', 'signinStatus': 1, 'signinTime': T(java.time.LocalDateTime).now(), 'tenantAreaNo': '610000'}"
      "signNo": "['output']['signinoutb']['sign_no']"
      "medicareInstitutionCode": "'H61010400856'"
      "operatorCode": "'10000'"
      "operatorName": "'管理员'"
      "signinStatus": "1"
      "signinTime": "T(java.time.LocalDateTime).now()"
      "tenantAreaNo": "'610000'"

    result:
      success: "output['infcode'] == 0" # 错误代码为0表示成功
      skip: false
      tips: "output['err_msg']" # 显示错误信息