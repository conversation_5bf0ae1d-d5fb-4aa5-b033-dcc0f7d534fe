dslType: contract
enable: true
name: 电子处方上传
format: json
functions:
  - path: "'/fixmedins/rxFileUpld'"
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        # 1. 处方追溯码：有效时间和处方有效时间保持一致，上传时每张处方只能使用一次
        "rxTraceCode": "outputAll.get(0)[0].get('rxTraceCode')"
        # 2. 医保处方编号
        "hiRxno": "outputAll.get(0)[0].get('hiRxno')"
        # 3. 医保就诊ID：参保病人信息字段(注：医保门诊挂号时返回)
        "mdtrtId": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['medicalRegistrationInfo']?.medicalVisitId, '')"
        # 4. 患者姓名
        "patnName": business.data['data']['fullName']
        # 5. 人员证件类型：参照人员证件类型(psn_cert_type)
        "psnCertType": "'01'"
        # 6. 证件号码
        "certno": business.data['aux']['inquiryDetailInfo']?.patientIdCard
        # 7. 定点医疗机构名称
        "fixmedinsName": business.data['network']['hospitalName']
        # 8. 定点医疗机构编号
        "fixmedinsCode": business.data['network']['hospitalCode']
        # 9. 开方医保医师代码：国家医保医师代码
        "drCode": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['doctorInfo']?.doctorMedicareNo, '')"
        # 10. 开方医师姓名
        "prscDrName": business.data['data']['doctorName']

        # 11. 审方药师科室名称
        "pharDeptName": "'-'"
        # 12. 审方药师科室编号：与医药机构服务的科室管理中上传的hosp_dept_codg医院科室编码字段保持一致
        "pharDeptCode": "'-'"
        # 13. 审方药师职称编码：参照审方药师职称编码(phar_pro_tech_duty)
        "pharProfttlCodg": "''"
        # 14. 审方药师职称名称
        "pharProfttlName": "''"
        # 15. 审方医保药师代码：国家医保药师代码
        "pharCode": "'HY610199001554'"
        # 16. 审方药师证件类型：参照人员证件类型(psn_cert_type)
        "pharCertType": "'01'"
        # 17. 审方药师证件号码
        "pharCertno": "'610104197605206180'"
        # 18. 审方药师姓名
        "pharName": "'李红'"
        # 19. 审方药师执业资格证号
        "pharPracCertNo": "''"
        # 20. 医疗机构药师审方时间：yyyy-MM-ddHH:mm:ss
        "pharChkTime": "T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(business.data['data']['auditPrescriptionTime'])"

        # 21. 处方原件：医保电子签名后的处方文件base64字符(PDF或OFD格式)
        "rxFile": "outputAll.get(1)[0].get('rxFile')"
        # 22. 处方信息签名值：医保电子签名后处方信息的签名结果
        "signDigest": "outputAll.get(1)[0].get('signDigest')"

    response:
      "code": "['code']"  # 响应状态码
      "message": "['message']"  # 响应异常信息
      "success": "['success']"  # 响应标识
      "medicareRxTraceCode": "['rxTraceCode']"  # 处方追溯码
      "medicareRxNo": "['hiRxno']"  # 医保处方编号
      "rxChkStatus": "['rxStasCodg']"  # 医保处方状态编码：参考(rx_stas_codg)
      "rxStasName": "['rxStasName']"  # 医保处方状态名称
      "ext":
        "signDigest": "['signDigest']"  # 医保电子签名后处方信息originalValue的签名结果值
        "signCertSn": "['signCertSn']"  # 签名机构证书SN
        "signCertDn": "['signCertDn']"  # 签名机构证书DN

preFilter:
  condition: true
postParameter:
  nodes:
    - doctorInfo
    - pharmacistInfo
    - medicalRegistrationInfo
    - prescriptionDetail
    - inquiryDetailInfo