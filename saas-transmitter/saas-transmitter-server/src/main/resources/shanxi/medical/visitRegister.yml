name: '就诊登记'
enable: true
dslType: contract
format: json
functions:
  # Function 2: 2201 - 门诊挂号
  - path: "'/fsi/api/outpatientDocInfoService/outpatientRregistration'"
    bodyStrategy: jsonNode
    # 执行条件：只有1101成功时才执行2201
    #    condition: "output.get('out_0') != null && output.get('out_0').get('infcode') == 0 && output.get('out_0') != null && output.get('out_0').get('output') != null && output.get('out_0').get('output').get('insuinfo') != null"
    request:
      method: POST
      body:
        "infno": "'2201'" # 接口号
        "insuplc_admdvs": "business.data['aux']['personInsuranceRecord']?.insuplcAdmdvs" # 使用1101结果中的参保地，失败时使用默认值
        "sign_no": "business.data['aux']['signIn']?.signNo"
        "input":
          "data":
            "psn_no": "business.data['aux']['personInsuranceRecord']?.psnNo"
            "insutype": "business.data['aux']['personInsuranceRecord']?.insutype"
            "begntime": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(business.data['data'].inquiryStartTime)
            "mdtrt_cert_type": "'02'"
            "mdtrt_cert_no": "business.data['aux']['inquiryDetailInfo']?.patientIdCard"
            "ipt_otp_no": "business.data['data']['businessNo']"
            "atddr_no": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['doctorInfo']?.doctorMedicareNo, '')"
            "dr_name": "business.data['aux']['doctorInfo']?.name"
            "dept_code": "business.data['data']['deptPref']"
            "dept_name": "business.data['data']['deptName']"
            "caty": "'A03'"
    response:
      # 公共字段
      "infcode": "['infcode']"
      "inf_refmsgid": "['inf_refmsgid']"
      "refmsg_time": "['refmsg_time']"
      "respond_time": "['respond_time']"
      "err_msg": "['err_msg']"
      # 构建最终返回对象 - 修正访问路径
      #      "medicalRegistrationInfo": "['output'] != null && ['infcode'] == 0 ? {'medicalVisitId': ['output']['data']['mdtrt_id'], 'medType': '11', 'insuredAreaNo': ['input']['insuplc_admdvs'], 'tenantAreaNo': '610100', 'psnNo':
      #      ['output']['data']['psn_no'], 'iptOtpNo': ['output']['data']['ipt_otp_no']} : null"
      "medicalVisitId": "['output']['data']['mdtrt_id']"
      "medType": "'11'"
      "insuredAreaNo": "['business']['data']['aux']['personInsuranceRecord']['insuplcAdmdvs']"
      "tenantAreaNo": "'610100'"
      "psnNo": "['output']['data']['psn_no']"
      "iptOtpNo": "['output']['data']['ipt_otp_no']"


    # 添加结果判断配置
    result:
      success: "output.get('infcode') == 0" # 成功条件：infcode为0
      skip: false # 2201失败时抛出异常
      tips: "'2201门诊挂号失败: ' + output.get('err_msg')" # 修正tips表达式语法

  # Function 3: 2203A - 就诊信息上传
  - path: "'/fsi/api/outpatientDocInfoService/outpatientMdtrtinfoUpA'"
    bodyStrategy: jsonNode
    # 执行条件：只有2201成功时才执行2203A
    condition: "output.get('out_0') != null && output.get('out_0').get('infcode') == 0"
    request:
      method: POST
      body:
        "infno": "'2203A'" # 接口号
        "insuplc_admdvs": "business.data['aux']['personInsuranceRecord']?.insuplcAdmdvs" # 使用1101结果中的参保地
        "sign_no": "business.data['aux']['signIn']?.signNo"
        "input":
          "mdtrtinfo":
            "psn_no": "business.data['aux']['personInsuranceRecord']?.psnNo" # 从1101结果获取
            "mdtrt_id": "output.get('medicalVisitId')" # 从2201结果获取
            "med_type": "'11'"
            "begntime": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(business.data['data'].inquiryStartTime)
          "diseinfo":
            "diag_type": " T(java.util.Objects).equals(business.data['data']['medicineType'],0) ? '1' : '2'"
            "diag_srt_no": "'1'"
            "diag_code": "business.data['data']['diagnosisCode'][0]"
            "diag_name": "business.data['data']['diagnosisName'][0]"
            "diag_dept": "T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDictLabel(business,'dept_dict',business
            .data['data']['deptPref'], T(java.util.Objects).equals(business.data['data']['medicineType'],0) ?'全科门诊':'中医科')"
            "diag_time": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(business.data['data'].inquiryStartTime)
            "vali_flag": "'1'"
    response:
      # 公共字段
      "infcode": "['infcode']"
      "inf_refmsgid": "['inf_refmsgid']"
      "refmsg_time": "['refmsg_time']"
      "respond_time": "['respond_time']"
      "err_msg": "['err_msg']"
      # 2203A只处理自己的业务逻辑，不返回medicalRegistrationInfo
    # 添加结果判断配置
    result:
      success: "output.get('infcode') == 0" # 成功条件：infcode为0
      skip: false # 2203A失败时抛出异常
      tips: "'2203A就诊信息上传失败: ' + output.get('err_msg')" # 修正tips表达式语法