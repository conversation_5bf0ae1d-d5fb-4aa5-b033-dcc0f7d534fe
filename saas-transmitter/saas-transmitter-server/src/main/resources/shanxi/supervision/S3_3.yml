dslType: contract
enable: true
common: false
name: 医疗人员信息上报
format: json
functions:
  - path: "'/incoming/supervision/complex/uploadProviderInfo'"
    domain: business.data['network']['networkItem']['internet']
    protocol: http
    bodyStrategy: jsonNode
    request:
      method: POST
      header:
        "Content-Type": "'application/json'"
        "Authorization": "'Basic ' + T(cn.hutool.core.codec.Base64).encode('xd_admin:aIokVf67KiBZqMVy')"
      body:
        # 全国统一组织机构代码, 写死
        "organizationCode": "'H61010400856'"
        # 机构名称, 写死
        "organizationName": "'西电集团医院（附设第一、二、三门诊部）'"
        # 监管平台ID，固定值:（400300045 陕西省监管平台）
        "supervisionPlatFormId": "'400300045'"
        # 医疗人员信息列表, 从业务数据中获取
        "providerList":
          - "externalProviderId": business.data['data'][_index_]['doctorInfo']['pref'] # 外部医师 ID
            #            "providerId": business.data['data'][_index_]['id'] # 互联网健康平台医师 ID
            "providerName": business.data['data'][_index_]['doctorInfo']['name'] # 医师姓名
            "providerResidentId": business.data['data'][_index_]['doctorInfo']['idCard'] # 医师身份证号码
            "providerGenderTypeId": "business.data['data'][_index_]['doctorInfo']['sex'] == 1 ? '4' : '3'" # 医师性别
            "birthDate": "T(com.xyy.saas.inquiry.util.IdCardUtil).getBirthdayByIdCard(business.data['data'][_index_]['doctorInfo']['idCard'], 'yyyy-MM-dd')" # 出生日期(YYYY-MM-dd)
            #            "national": business.data['data'][_index_]['doctorInfo']['nationCode'] # 民族
            "subjectCode": "T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDict(business, 'dept_dict', business.data['data'][_index_]['doctorDeptInfo']['deptName'])" # 医师所属的诊疗科目代码
            "subjectName": business.data['data'][_index_]['doctorDeptInfo']['deptName'] # 医师所属的诊疗科目名称
            "externalDepartmentId": business.data['data'][_index_]['doctorDeptInfo']['deptPref'] # 医师所属的外部科室ID
            #            "departmentId": business.data['data'][_index_]['doctorPracticeInfo']['departmentId'] # 医师所属的互联网健康平台科室ID
            "departmentName": business.data['data'][_index_]['doctorDeptInfo']['deptName'] # 医师所属临床科室名称
            "providerCertCode": business.data['data'][_index_]['doctorPracticeInfo']['qualificationNo'] # 医师资格证书编码
            "providerWorkCode": business.data['data'][_index_]['doctorPracticeInfo']['professionalNo'] # 医师执业证书编码
            "firstWorkOrganizationId": "'H61010400856'" # 第一执业机构ID
            "firstWorkOrganizationName": "'西电集团医院（附设第一、二、三门诊部）'" # 第一执业机构名称
            #            "firstWorkAreaName": "'陕西省西安市'" # 第一执业地点名称
            "providerOrganizationName": "'西电集团医院（附设第一、二、三门诊部）'" # 医师所在机构名称
            "providerWorkDate": business.data['data'][_index_]['doctorPracticeInfo']['professionalTime'] # 执业证书发证日期
            "providerWorkTypeId": "1" # 医师执业类别（1 医生 2 护士 3 药师）
            "providerMobileNumber": business.data['data'][_index_]['doctorInfo']['mobile'] # 手机号
            "providerLicenseTypeId": "T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDict(business, 'doctor_title', business.data['data'][_index_]['doctorPracticeInfo']['titleCode'])" # 医生职称
            "updateDateTime": "T(java.time.format.DateTimeFormatter).ofPattern(\"yyyy-MM-dd HH:mm:ss\").format(T(java.time.LocalDateTime).now())" # 最后更新时间
    response:
      # 响应返回码
      "doctorPrefList": "[business][data][data].![[doctorInfo][pref]]"
      # 相应状态码描述
      "msg": "['responseText']"
    result:
      "success": "true"  # 响应状态码
      "tips": "output[out_0]['responseText']"  # 响应异常信息
