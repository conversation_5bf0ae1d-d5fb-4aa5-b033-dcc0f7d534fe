dslType: contract
enable: true
common: false
name: 医师诊疗业务备案
format: json
functions:
  - path: "'/incoming/supervision/complex/openProviderBusiness'"
    domain: business.data['network']['networkItem']['internet']
    protocol: http
    bodyStrategy: jsonNode
    request:
      method: POST
      header:
        "Content-Type": "'application/json'"
        "Authorization": "'Basic ' + T(cn.hutool.core.codec.Base64).encode('xd_admin:aIokVf67KiBZqMVy')"
      body:
        # 全国统一组织机构代码, 写死
        "organizationCode": "'H61010400856'"
        # 机构名称, 写死
        "organizationName": "'西电集团医院（附设第一、二、三门诊部）'"
        # 监管平台ID，固定值:（400300045 陕西省监管平台）
        "supervisionPlatFormId": "'400300045'"
        # 医生职称, 从医师信息中获取，字典映射
        "providerLicenseTypeId": "T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDict(business, 'doctor_title', business.data['data']['doctorPracticeInfo']['titleCode'])"
        # 待开通业务医师的外部ID, 从医师信息中获取
        "externalProviderId": business.data['data']['doctorInfo']['pref']
        # 待开通业务医师的互联网健康平台医师ID, 从医师信息中获取
        #        "providerId": business.data['data']['doctorInfo']['id']
        # 待开通业务的医师姓名, 从医师信息中获取
        "providerName": business.data['data']['doctorInfo']['name']
        # 待开通业务的医师身份证号码, 从医师信息中获取
        "providerResidentId": business.data['data']['doctorInfo']['idCard']
        # 拟开通的业务类型集合, 从业务数据中获取
        "businessTypeIdList": [3,4,5]
        # 拟开通的咨询业务类别, 从业务数据中获取
        "consultationTypeIdList": [6,7,8]
    response:
      # 响应返回码
      "doctorPrefList": "T(java.util.List).of([business][data][data][doctorInfo][pref])"
      # 相应状态码描述
      "msg": "['responseText']"
    result:
      "success": "true"  # 响应状态码
      "tips": "output[out_0]['responseText']"  # 响应异常信息