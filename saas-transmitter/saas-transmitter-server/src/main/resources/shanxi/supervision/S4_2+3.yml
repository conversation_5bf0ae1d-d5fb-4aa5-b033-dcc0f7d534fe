dslType: contract
enable: true
common: false
domain: business.data['network']['networkItem']['internet']
name: 在线复诊信息上报 + 在线处方信息上报
format: json
functions:
  - path: "'/incoming/supervision/complex/uploadSubsequentVisitInfo'"
    protocol: http
    bodyStrategy: jsonNode
    request:
      method: POST
      header:
        "Content-Type": "'application/json'"
        "Authorization": "'Basic ' + T(cn.hutool.core.codec.Base64).encode('xd_admin:aIokVf67KiBZqMVy')"
      body:
        # 全国统一组织机构代码, 写死
        "organizationCode": "'H61010400856'"
        # 机构名称, 写死
        "organizationName": "'西电集团医院（附设第一、二、三门诊部）'"
        # 监管平台ID，固定值:（400300045 陕西省监管平台）
        "supervisionPlatFormId": "'400300045'"
        # 在线复诊业务 id, 从业务数据中获取
        "visitId": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['data']['inquiryPref'], '')"
        # 咨询医师所属专业代码（诊疗科目代码）, 从医师信息中获取，字典映射
        "subjectCode": "T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDict(business, 'dict_dept', business.data['data']?.deptPref)"
        # 咨询医师所属专业名称（诊疗科目名称）, 从医师信息中获取
        "subjectName": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['data']?.deptName, '')"
        # 外部科室 ID, 从医师信息中获取
        "externalDepartmentId": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['data']?.deptPref, '')"
        # 互联网健康平台科室 ID, 从医师信息中获取
        #        "departmentId": business.data['aux']['doctorInfo']?.id
        # 科室名称, 从医师信息中获取
        "departmentName": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['data']?.deptName, '')"
        # 外部医师 ID, 从医师信息中获取
        "externalProviderId": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['doctorInfo']?.pref, '')"
        # 互联网健康平台医师 ID, 从医师信息中获取
        #        "providerId": business.data['aux']['doctorInfo']?.pref
        # 医师姓名, 从医师信息中获取
        "providerName": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['doctorInfo']?.name, '')"
        # 医师身份证号码, 从医师信息中获取
        "providerResidentId": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['doctorInfo']?.idCard, '')"
        # 患者姓名, 从业务数据中获取
        "patientName": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['data']['fullName'], '')"
        # 患者 ID 类型（3 身份证）, 默认身份证
        "patientIdTypeId": "'3'"
        # 患者 ID, 从业务数据中获取
        "patientId": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['inquiryDetailInfo']?.patientIdCard, '')"
        # 患者年龄, 从问诊详情中获取
        "patientAge": "T(com.xyy.saas.inquiry.util.IdCardUtil).getAgeByIdCard(business.data['aux']['inquiryDetailInfo']?.patientIdCard)"
        # 监护人姓名（复诊患者<6岁必填）, 条件获取
        "guardianName": " T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['inquiryDetailInfo']?.ext?.guardianName, '') "
        # 监护人身份证号（复诊患者<6岁必填）, 条件获取
        "guardianResidentId": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['inquiryDetailInfo']?.ext?.guardianIdCard, '') "
        # 监护人手机号, 条件获取
        "guardianMobileNumber": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['inquiryDetailInfo']?.patientMobile, '')"
        # 患者性别（1 未知、2 其他、3 女、4 男）, 从问诊详情中获取并转换
        "patientGenderTypeId": "T(java.util.Objects).equals(business.data['data']['patientSex'],1) ? '4' : '3'"
        # 患者联系电话, 从问诊详情中获取
        "patientMobileNumber": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['inquiryDetailInfo']?.patientMobile, '')"
        # 咨询类型（6 视频咨询,7 电话咨询,8 图文咨询,2 其他）, 从业务数据中获取并转换
        "visitTypeId": "T(java.util.Objects).equals(business.data['data']['inquiryWayType'],1) ? '8' : '6'"
        # 患者原诊断，如有多条使用"|"进行分割, 从临床病例中获取
        "originalDiagnosis": "T(org.apache.commons.lang3.StringUtils).join(business.data['data'].diagnosisName, '|')"
        # 在线复诊申请时间, 从业务数据中获取并格式化
        "appointmentDateTime": "T(java.time.format.DateTimeFormatter).ofPattern(\"yyyy-MM-dd HH:mm:ss\").format(business.data['data']['inquiryStartTime'])"
        # 咨询开始时间, 从业务数据中获取并格式化
        "startDateTime": "business.data['data']['inquiryStartTime'] != null ? T(java.time.format.DateTimeFormatter).ofPattern(\"yyyy-MM-dd HH:mm:ss\").format(business.data['data']['inquiryStartTime']) : null"
        # 咨询结束时间, 从业务数据中获取并格式化
        "endDateTime": "business.data['data']['outPrescriptionTime'] != null ? T(java.time.format.DateTimeFormatter).ofPattern(\"yyyy-MM-dd HH:mm:ss\").format(business.data['data']['outPrescriptionTime']) : null"
        # 本次复诊诊断，如有多条使用"|"进行分割, 从临床病例中获取
        "thisDiagnosis": "T(org.apache.commons.lang3.StringUtils).join(business.data['data'].diagnosisName, '|')"
        # 本次未确诊原因, 从临床病例中获取
        "notDiagnosedReason": "''"
        # 支付方式, 从业务数据中获取并转换
        "paymentMethodId": "'1'"
        # 咨询价格, 从业务数据中获取
        "price": "0"
        # 用户咨询内容或病史摘要, 从问诊详情中获取
        "visitReason": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(T(org.apache.commons.lang3.StringUtils).join(business.data['aux']['inquiryDetailInfo']?.mainSuit, ','), '暂无')"
        # 是否已回复, 从业务数据中获取
        "isReplyed": "true"
        # 咨询拒绝/取消时间, 条件获取
        #        "cancelDateTime": "business.data['data']['cancelTime'] != null ? T(java.time.format.DateTimeFormatter).ofPattern(\"yyyy-MM-dd HH:mm:ss\").format(business.data['data']['cancelTime']) : null"
        # 咨询拒绝/取消原因, 从业务数据中获取
        #        "cancelReason": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['data']['cancelReason'], '')"
        # 咨询拒绝/取消类型（1 医生主动拒绝/取消 2 超时未回复系统自动拒绝/取消）, 从业务数据中获取并转换
        #        "cancelTypeId": "business.data['data']['cancelType'] != null ? T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDict(business, 'cancel_type', business.data['data']['cancelType']) : null"
        # 咨询过程数据查询地址，包括文字、音频、视频查询播放地址, 从业务数据中获取
    #        "processDataUrl": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['data']['processDataUrl'], '')"
    response:
      # 响应返回码
      "infcode": "['responseCode']"
      # 相应状态码描述
      "err_msg": "['responseText']"

    result:
      #      "success": "output['infcode'] == 0"  # 响应状态码
      "tips": "output['err_msg']"  # 响应异常信息

  - path: "'/incoming/supervision/complex/uploadRecipeInfo'"
    protocol: http
    bodyStrategy: jsonNode
    request:
      method: POST
      header:
        "Content-Type": "'application/json'"
        "Authorization": "'Basic ' + T(cn.hutool.core.codec.Base64).encode('xd_admin:aIokVf67KiBZqMVy')"
      body:
        # 全国统一组织机构代码, 写死
        "organizationCode": "'H61010400856'"
        # 机构名称, 写死
        "organizationName": "'西电集团医院（附设第一、二、三门诊部）'"
        # 监管平台ID，固定值:（400300045 陕西省监管平台）
        "supervisionPlatFormId": "'400300045'"
        # 在线复诊业务 id, 从业务数据中获取
        "visitId": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['data']['inquiryPref'], '')"
        # 咨询医师所属专业代码（诊疗科目代码）, 从医师信息中获取，字典映射
        "subjectCode": "T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDict(business, 'dict_dept', business.data['data']['deptName'])"
        # 咨询医师所属专业名称（诊疗科目名称）, 从医师信息中获取
        "subjectName": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['data']['deptName'], '')"
        # 外部科室 ID, 从医师信息中获取
        "externalDepartmentId": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['data']['deptPref'], '')"
        # 互联网健康平台科室 ID, 从医师信息中获取
        "departmentId": business.data['aux']['doctorInfo']?.id
        # 科室名称, 从医师信息中获取
        "departmentName": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['data']['deptName'], '')"
        # 医师外部 ID, 从医师信息中获取
        "externalProviderId": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['doctorInfo']?.pref, '')"
        # 互联网健康平台医师 ID, 从医师信息中获取
        #        "providerId": business.data['aux']['doctorInfo']?.id
        # 医师姓名, 从医师信息中获取
        "providerName": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['doctorInfo']?.name, '')"
        # 医师身份证号码, 从医师信息中获取
        "providerResidentId": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['doctorInfo']?.idCard, '')"
        # 审方医生外部 ID, 从药师信息中获取
        "externalReviewProviderId": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['pharmacistInfo']?.pref, '')"
        # 互联网健康平台审方医生 ID, 从药师信息中获取
        "reviewProviderId": business.data['aux']['pharmacistInfo']?.pref
        # 审方医师姓名, 从药师信息中获取
        "reviewProviderName": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['pharmacistInfo']?.name, '')"
        # 审方医师身份证号码, 从药师信息中获取
        "reviewProviderResidentId": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['pharmacistInfo']?.idCard, '')"
        # 审方时间, 从业务数据中获取并格式化
        "reviewDateTime": "business.data['data']['auditPrescriptionTime'] != null ? T(java.time.format.DateTimeFormatter).ofPattern(\"yyyy-MM-dd HH:mm:ss\").format(business.data['data']['auditPrescriptionTime']) : T(java.time.format.DateTimeFormatter).ofPattern(\"yyyy-MM-dd HH:mm:ss\").format(T(java.time.LocalDateTime).now())"
        # 患者姓名, 从业务数据中获取
        "patientName": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['data']['fullName'], '')"
        # 患者 ID 类型（3 身份证）, 默认身份证
        "patientIdTypeId": 3
        # 患者 ID, 从业务数据中获取
        "patientId": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['inquiryDetailInfo']?.patientIdCard, '')"
        # 患者年龄, 从问诊详情中获取
        "patientAge": "T(com.xyy.saas.inquiry.util.IdCardUtil).getAgeByIdCard(business.data['aux']['inquiryDetailInfo']?.patientIdCard)"
        # 监护人姓名（复诊患者<6岁必填）, 条件获取
        "guardianName": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['inquiryDetailInfo']['ext']?.guardianName, '')"
        # 监护人身份证号（复诊患者<6岁必填）, 条件获取
        "guardianResidentId": " T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['inquiryDetailInfo']['ext']?.guardianIdCard, '')"
        # 监护人手机号, 条件获取
        "guardianMobileNumber": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['inquiryDetailInfo']?.patientMobile, '')"
        # 患者性别（1 未知、2 其他、3 女、4 男）, 从问诊详情中获取并转换
        "patientGenderTypeId": "business.data['aux']['inquiryDetailInfo']?.patientSex == 1 ? '4' : '3'"
        # 患者联系电话, 从问诊详情中获取
        "patientMobileNumber": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['inquiryDetailInfo']?.patientMobile, '')"
        # 过敏信息, 从临床病例中获取
        "allergyInfo": "T(org.apache.commons.lang3.StringUtils).join(business.data['aux']['inquiryDetailInfo']?.allergic, ',')"
        # 患者病史摘要, 从临床病例中获取
        "problemHistory": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['inquiryDetailInfo']?.patientHisDesc, '无')"
        # 互联网医院处方唯一号, 从业务数据中获取
        "recipeUniqueId": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['data']['pref'], '')"
        # 互联网医院处方号, 从业务数据中获取
        "recipeId": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['data']['businessNo'], '')"
        # 处方医保备案号, 从医保挂号信息中获取
        "recipeRecordNo": "''"
        # 是否经过合理用药判断, 从业务数据中获取
        "isRational": "true"
        # 合理用药审核结果（isRational=true 时必填）, 条件获取
        "rationalResult": "'审核通过'"
        # 处方 CA 认证的 pdf 文件信息, 从CA信息中获取
        "CAInfo": "''"
        # 处方开始日期, 从业务数据中获取并格式化
        "startDate": "business.data['data']['outPrescriptionTime'] != null ? T(java.time.format.DateTimeFormatter).ofPattern(\"yyyy-MM-dd\").format(business.data['data']['outPrescriptionTime']) : null"
        # 咨询结束日期, 从业务数据中获取并格式化
        "endDate": "business.data['data']['outPrescriptionTime'] != null ? T(java.time.format.DateTimeFormatter).ofPattern(\"yyyy-MM-dd\").format(business.data['data']['outPrescriptionTime']) : null"
        # 诊断 icd 码，如有多条使用"|"进行分割, 从临床病例中获取
        "diagnosisCode": "business.data['data'].diagnosisCode[0]"
        # 诊断名称，如有多条使用"|"进行分割, 从临床病例中获取
        "diagnosisName": "business.data['data'].diagnosisName[0]"
        # 处方类型（2 其它、3 西药、4 中成药、5 定制中药、6 中药成分）, 从业务数据中获取并转换
        "recipeTypeId": "business.data['data']['medicineType'] == 1 ? 6 : 3"
        # 帖数，中药处方必填, 条件获取
        "packetsNum": "business.data['data']['medicineType'] == 1 ? business.data['data']['ext']['tcmTotalDosage'] : null"
        # 开方时间, 从业务数据中获取并格式化
        "createDateTime": "T(java.time.format.DateTimeFormatter).ofPattern(\"yyyy-MM-dd HH:mm:ss\").format(business.data['data']['outPrescriptionTime'])"
        # 处方有效天数, 从业务数据中获取
        "effectivePeriod": "'3'"
        # 处方总金额，单位：元, 从业务数据中获取
        "recipeTotalPrice": "'0'"  #business.data['data']['ext']['pricingPrice']
        # 支付状态 (3 等待支付 5 支付成功), 从业务数据中获取并转换
        "paymentStatusId": "'5'"
        # 是否已核销, 从业务数据中获取
        "isVerification": "''"
        # 处方订单列表, 从处方详情中获取并转换
        "recipeOrderList":
          - "medicationCode": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['productPref'], '')" # 药品编码
            "medicationName": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['productName'], '')" # 药品名称
            #            "packageSize": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['packageSize'], '')" # 药品包装
            "packageUnit": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['packageUnit'], '')" # 药品包装单位
            "routeName": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['directions'], '')" # 用药方法
            "medicationSpecification": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['attributeSpecification'], '')" # 药品规格
            "medicationManufacturer": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['manufacturer'], '')" # 药品生产厂家
            "quantity": business.data['aux']['prescriptionDetail'][_index_]['quantity'] # 药品总药量
            "quantityUnit": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['packageUnit'], '')" # 总药量单位
            "frequencyName": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['useFrequency'], '')" # 给药频率
            "doseAmount": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['singleDose'], '')" # 每次剂量
            "useDays": "''" # 用药天数
            "doseUnitName": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['singleUnit'], '')" # 剂量单位名称
            "hasOtcFlag": "T(org.apache.commons.lang3.StringUtils).contains(business.data['aux']['prescriptionDetail'][_index_]['presCategory'], 'OTC')" # 是否有 OTC 标志
    #            "remark": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['remark'], '')" # 备注
    response:
      # 响应返回码
      "infcode": "['responseCode']"
      # 相应状态码描述
      "err_msg": "['responseText']"

    result:
      #      "success": "output['infcode'] == 0"  # 响应状态码
      "tips": "output['err_msg']"  # 响应异常信息
