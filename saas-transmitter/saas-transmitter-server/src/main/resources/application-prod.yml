spring:
  cloud:
    nacos:
      server-addr: mse-5bfd0582-nacos-ans.mse.aliyuncs.com:8848
      discovery:
        namespace: e1b55694-2e80-4aec-aa56-4aae6fd307ff
        group: http
      config:
        namespace: e1b55694-2e80-4aec-aa56-4aae6fd307ff
        group: DEFAULT_GROUP
        server-addr: mse-5bfd0582-nacos-ans.mse.aliyuncs.com:8848
        prefix: ${spring.application.name}
        file-extension: yaml
  config:
    import:
      - optional:nacos:inquiry-global-${spring.profiles.active}.yaml

  datasource:
    dynamic:
      datasource:
        master:
          url: ***********************************************************************************************************************************************************************************************
          username: app_saas_system_w
          password: Gmp387stnMkeD2aDeBHx
  data:
    redis:
      host: db011-saas.prod.redis.ybm100.top # 地址
      port: 50001 # 端口
      password: Rmzhz63yYz5VwhQQmRZb # 密码，建议生产环境开启
      database: 14
rocketmq:
  name-server: mq01-inquiry-prod.rocketmq.ybm100.top:9876;mq02-inquiry-prod.rocketmq.ybm100.top:9876;mq03-inquiry-prod.rocketmq.ybm100.top:9876;mq04-inquiry-prod.rocketmq.ybm100.top:9876
  producer:
    group: saas-transmitter-server
logging:
  level:
    com.baomidou.mybatisplus: info