dslType: contract
enable: true
name: 处方药销售明细
format: json
common: false
domain: business.data['network']['networkItem']['url']
functions:
  - path: "'/openapi/retail/drugstore/drugSales'"
    bodyStrategy: jsonNode
    cycle: true
    cycleCount: business.data['aux']['prescriptionDetail'].size()
    request:
      method: POST
      header:
        "Authorization": T(com.xyy.saas.transmitter.server.util.transmission.drug.RequestSignUtil).sign4YunNanDrugSupervision('91530100MA6QFMR80Y','/openapi/retail/drugstore/drugSales',T(cn.hutool.json.JSONUtil).toJsonStr(currentInput.bodys),business.data['network']?.thirdPartyPrivateKey)
        "Content-Type": "'application/json'"
      body:
        # 唯一编号 - 格式：日期 + 社会信用代码 + 序列号（机构自定义）
        "seqNo": "T(java.time.format.DateTimeFormatter).ofPattern('yyyyMMddHHmmss').format(business.data['data']['outPrescriptionTime']) + '91530100MA6QFMR80Y' + business.data['data']['pref'] + business.data['_cycle_idx']"

        # 审方中心社会信用代码 - 营业执照上的社会信用代码 默认传固定企业社会信用代码<云南智鹿大药房连锁有限公司>
        "prcSocialCreditCode": "'91530100MA6QFMR80Y'"

        # 审方中心企业名称 - 按照营业执照上的名称准确填写
        "prcEnterpriseName": "'云南智鹿大药房连锁有限公司'"

        # 药店编号 - 药品经营许可证编号
        "storeNo": T(com.xyy.saas.transmitter.server.util.transmission.drug.YunnanAreaEnum).handleStoreNo(business.data['data']['tenantInfo']?.certificates?.?[certificateType == 2].![certificateNo])

        # 药店社会信用代码 - 营业执照上的社会信用代码 【荷叶健康-运营平台→荷叶问诊门店维护→门店维护→资质信息-营业执照号】
        "drugstoreSocialCreditCode": business.data['data']['tenantInfo']?.businessLicenseNumber

        # 药店名称 - 与药品经营许可证上的企业名称对应 - 营业执照名称
        "drugstoreName": business.data['data']['tenantInfo']?.businessLicenseName #

        "storeName": business.data['data']['tenantInfo']?.businessLicenseName #

        # 药店所在区域 - 使用行政区划代码（前六位）
        "areaNo": "T(com.xyy.saas.transmitter.server.util.transmission.drug.YunnanAreaEnum).getAreaCodeByTenantAreaCode(business.data['data']['tenantInfo']?.areaCode)" # 药店所在区域

        # 销售日期 - 开方日期
        "date": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMdd").format(business.data['data']['outPrescriptionTime'])

        # 订单编号 - 对应机构的订单编号
        "orderNo": business.data['data']['pref'] # 处方单号

        # 药品本位码 - 有该字段的企业，可上传药品本位码
        "nativeCode": "'-'"

        # 药品编号 - 国药准字编号
        "drugNo": business.data['aux']['prescriptionDetail'][business.data['_cycle_idx']]['approvalNumber'] # 批准文号

        # 药品名称 - 对应处方签中药品名称
        "drugName": business.data['aux']['prescriptionDetail'][business.data['_cycle_idx']]['commonName'] # 药品名称

        # 药品规格 - 对应处方签中该药品的规格
        "spec": business.data['aux']['prescriptionDetail'][business.data['_cycle_idx']]['attributeSpecification'] # 规格

        # 生产厂商 - 药品的生产厂商
        "manufacturer": business.data['aux']['prescriptionDetail'][business.data['_cycle_idx']]['manufacturer'] # 生产厂家

        # 药品批次号
        "batchNo": "'-'" # 批次号

        # 销售数量 - 对应处方签中该药品的数量
        "amount": business.data['aux']['prescriptionDetail'][business.data['_cycle_idx']]['quantity'] # 开方数量

        # 药品单位 - 对应处方签中该药品的单位
        "unit": business.data['aux']['prescriptionDetail'][business.data['_cycle_idx']]['packageUnit'] # 单位

        # 药店剩余库存量 - 上传销售该笔订单后，该药品剩余的药品数量
        "stock": "'0'" # 剩余库存

        # 买药人手机号 - 对应买药的人的手机号码
        "buyerPhone": business.data['data']['patientMobile'] # 患者手机号
    response:
      # 返回结果处理
      "code": "['code']" # 返回标记：成功标记=0，失败标记=1
      "msg": "['msg']" # 返回接口调用描述信息，错误时返回错误提示信息