dslType: contract
enable: true
name: 处方信息上送
format: json
common: false
domain: business.data['network']['networkItem']['url']
functions:
  - path: "'/openapi/middleman/prc/prescription'"
    bodyStrategy: jsonNode
    request:
      method: POST
      header:
        "Authorization": T(com.xyy.saas.transmitter.server.util.transmission.drug.RequestSignUtil).sign4YunNanDrugSupervision('91530100MA6QFMR80Y','/openapi/middleman/prc/prescription',T(cn.hutool.json.JSONUtil).toJsonStr(currentInput.bodys),business.data['network']?.thirdPartyPrivateKey)
        "Content-Type": "'application/json'"
      body:
        # 社会信用代码 - 营业执照上的社会信用代码
        "prcSocialCreditCode": "'91530100MA6QFMR80Y'" # 默认传固定企业社会信用代码<云南智鹿大药房连锁有限公司>
        # 审方中心企业名称 - 按照营业执照上的名称准确填写
        "prcEnterpriseName": "'云南智鹿大药房连锁有限公司'"
        # 唯一编号 - 格式：日期 + 社会信用代码 + 处方号
        "seqNo": "T(java.time.format.DateTimeFormatter).ofPattern('yyyyMMddHHmmss').format(business.data['data']['outPrescriptionTime']) + '91530100MA6QFMR80Y' + business.data['data']['pref']"
        # 处方编号 - 处方签上的编码
        "prescriptionNo": business.data['data']['pref'] # 处方号
        # 开方医院 - 填写开方医院的名称
        "hospitalName": business.data['aux']['hospitalInfo']?.name # 医院名称
        # 开方医生姓名
        "doctorName": business.data['data']['doctorName'] # 开方医生
        # 开方时间 - 格式：yyyyMMdd
        "prescribeDate": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMdd").format(business.data['data']['outPrescriptionTime']) # 开方日期
        # 药店社会信用代码 - 按照营业执照上的社会信用代码准确填写
        "drugstoreSocialCreditCode": business.data['data']['tenantInfo']?.businessLicenseNumber # 【荷叶健康-运营平台→荷叶问诊门店维护→门店维护→资质信息-营业执照号】
        # 药店编号 - 与药品经营许可证上的许可证编号对应
        "drugstoreNo": T(com.xyy.saas.transmitter.server.util.transmission.drug.YunnanAreaEnum).handleStoreNo(business.data['data']['tenantInfo']?.certificates?.?[certificateType == 2].![certificateNo]) # 药品经营许可证号
        # 药店名称 - 与药品经营许可证上的企业名称对应
        "drugstoreName": business.data['data']['tenantInfo']?.businessLicenseName
        # 药店所在区域 - 使用行政区划代码（前六位）
        "drugstoreAreaNo": "T(com.xyy.saas.transmitter.server.util.transmission.drug.YunnanAreaEnum).getAreaCodeByTenantAreaCode(business.data['data']['tenantInfo']?.areaCode)" # 药店所在区域
        # 病人姓名
        "patientName": business.data['data']['patientName'] # 患者姓名
        # 病人性别 - 0:男；1:女
        "patientGender": "T(java.util.Objects).equals(business.data['data']['patientSex'], '1') ? 0 : 1" # 患者性别转换：1(男)->0, 2(女)->1
        # 病人年龄
        "patientAge": business.data['aux']['inquiryDetailInfo']?.patientAge # 患者年龄
        # 病人联系方式
        "patientPhone": business.data['data']['patientMobile'] # 患者手机号
        # 临床诊断 - 临床诊断即诊断结果即病情描述
        "diagnosis": business.data['data']['diagnosisName'][0] # 诊断
        # 处方图片网络地址 - 审方中心提供处方图片访问的网络地址
        "imgUrl": business.data['data']['prescriptionImgUrl'] # 处方图片URL
        # 审方药师注册证号
        "registrationNo": business.data['aux']['pharmacistInfo']?.registrationNo # 【荷叶健康-运营平台→药师管理→药师列表→资质信息-注册证编号】
        # 审方时间 - 格式：yyyyMMddHHmmss
        "verificationTime": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(business.data['data']['auditPrescriptionTime']) # 审方时间
        # 药品信息说明 - 处方签上的药品信息说明
        "drugList":
          - "drugNo": business.data['aux']['prescriptionDetail'][_index_]['approvalNumber'] # 国药准字编号
            "drugName": business.data['aux']['prescriptionDetail'][_index_]['commonName'] # 对应处方签中药品名称
            "spec": business.data['aux']['prescriptionDetail'][_index_]['attributeSpecification'] # 对应处方签中该药品的规格
            "amount": business.data['aux']['prescriptionDetail'][_index_]['quantity'].intValue() # 对应处方签中该药品的数量
            "unit": business.data['aux']['prescriptionDetail'][_index_]['packageUnit'] # 对应处方签中该药品的单位
            "instructions": "T(java.lang.String).format('%s %s %s %s %s', business.data['aux']['prescriptionDetail'][_index_]['directions'] ?: '',' 每次', business.data['aux']['prescriptionDetail'][_index_]['singleDose'] ?: '',business
            .data['aux']['prescriptionDetail'][_index_]['singleUnit'] ?: '', business
            .data['aux']['prescriptionDetail'][_index_]['useFrequency'] ?: '')" # 对应处方签中该药品的用法用量
    response:
      # 根据实际API响应结构调整
      "success": "['success']"
      "message": "['message']"
      "code": "['code']"

