name: '药监药师签到'
enable: true
dslType: contract
format: json
common: false
domain: business.data['network']['networkItem']['url']
functions:
  - path: "'/openapi/middleman/prc-biz/pharmacist/login'"
    bodyStrategy: jsonNode
    request:
      method: POST
      header:
        "Authorization": T(com.xyy.saas.transmitter.server.util.transmission.drug.RequestSignUtil).sign4YunNanDrugSupervision('91530100MA6QFMR80Y','/openapi/middleman/prc-biz/pharmacist/login',T(cn.hutool.json.JSONUtil).toJsonStr(currentInput.bodys),business.data['network']?.thirdPartyPrivateKey)
        "Content-Type": "'application/json'"
      body:
        "socialCreditCode": "'91530100MA6QFMR80Y'" # 企业社会信 用代码
        "registrationNo": business.data['data']['registrationNo'] # 药师注册证号
    response:
      "code": "['code']" # 错误代码
      "msg": "['msg']" # 错误代码

    result:
      success: "output['code'] == 0" # 错误代码为0表示成功
      tips: "output['msg']" # 显示错误信息