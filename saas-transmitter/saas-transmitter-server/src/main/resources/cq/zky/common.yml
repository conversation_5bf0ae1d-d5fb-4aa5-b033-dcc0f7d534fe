header:
  "Authorization": "'Bearer ' + T(cn.hutool.extra.spring.SpringUtil).getBean('tokenService').getToken({}).accessToken"


result: # 这里就取output里面的字段做判断
  success: output['infcode'] == 0 || output['infcode'] == -2 #  0-成功 -2-token过期 也算成功，直接走skip
  skip: false
  tips: output[errmsg]
  hook:
    - globalContext.remove('accessToken') # globalContext 静态的跟着DSLContext,公有请求结束后需要手动清空
  retry:
    enable: true
    condition: output[infcode] == -2 # 判断三方返回条件,当token过期时,需要重新获取 token
    expression: "T(cn.hutool.extra.spring.SpringUtil).getBean('tokenService').getToken({'refresh':true})"