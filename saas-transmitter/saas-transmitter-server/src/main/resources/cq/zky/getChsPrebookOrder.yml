dslType: contract
enable: true
name: 获取医保预约订单
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/getChsPrebookOrder
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "itemId": "''" # 项目ID
        "idCardNo": business.data['data']['idCardNo']  #患者身份证号
        "startTime": T(cn.hutool.core.date.DateUtil).date().toString("yyyy-MM-dd HH:mm:ss") #订单查询的开始时间
        "endTime": T(java.time.format.DateTimeFormatter).ofPattern("yyyy-MM-dd HH:mm:ss").format(T(java.time.LocalDateTime).now().plusDays(1))  #结束时间 不允许超过10天
    response: # 将请求的response 解析到 output 中
      "infcode": "['errcode']"
      "msg": "['errmsg']"
      "bizVisitId": "['bizData'][0]['bookId']"
      "medicalVisitId": "['bizData'][0]['chsVisitId']"
      "medicalVisitDate": "['bizData'][0]['chsRegistTime']"
      "medType": "['bizData'][0]['medType']"
      "insuredAreaNo": "['bizData'][0]['insuPlcNo']"
      "tenantAreaNo": "['bizData'][0]['mdtrtareaNo']"
      "psnNo": "['bizData'][0]['psnNo']"
      "certno": "['bizData'][0]['certno']"
      "patientName": "['bizData'][0]['patnName']"
      "patientMobile": "['bizData'][0]['phone']"
      "bookTime": "['bizData'][0]['bookTime']"
      "planTime": "['bizData'][0]['planDate']"
      "deptName": "['bizData'][0]['prscDeptName']"
      "iptOtpNo": "['bizData'][0]['iptOtpNo']"
      "status": "['bizData'][0]['status']"
