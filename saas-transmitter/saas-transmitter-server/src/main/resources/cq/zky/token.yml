name: '获取token'
#domain: openapi.zcareze.com
domain: 127.0.0.1:8081
path: /ogi/auth/token/getAccessToken
common: false
dslType: contract
enable: true
protocol: http
format: json
functions:
  - request:
      format: form
      body:
        "appId": "''" #应用id
        "appSecret": "''" #应用密钥
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明
      "accessToken": "[accessToken]" #token
      "expiresIn": "[expiresIn]" #有效期
global:
  "token": "['accessToken']"
clearContext: false
result:
  success: output['infcode'] == 0
  skip: false
  tips: output['msg']