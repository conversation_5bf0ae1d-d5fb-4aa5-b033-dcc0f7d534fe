dslType: contract
enable: true
name: 接收预约订单状态
protocol: http
format: json
domain: 127.0.0.1:8081
functions:
  - path: /ogi/clinic/receivePrebookOrderStatus
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "chsVisitId": business.data['data']['medicalVisitId'] #医保就诊ID
        "status": "T(java.util.Objects).equals(business.data['data']['status'],3) ? 41 : 40" #包括：4-预约成功，40-执行中，41-已完成
    response:
      "infcode": "[errcode]" #交易状态码
      "msg": "[errmsg]" #交易说明