dslType: contract
enable: true
name: 门诊病例
format: json
functions:
  - path: "'/rainbow/api/hpcp/hospolmonitor/outpatientTreat'"
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "package":
          "head":
            "busseID": "'Q341'"
          "body":
            - "yljgdm": "'H50010701844'" # 医疗机构代码
              "userName": "'重庆格林医院'" # 医疗机构名称
              "credentialType": "'01'" # 证件类型 01身份证
              "credentialNum": business.data['data']['idCard'] # 证件号码
              "medicalNum": business.data['data']['inquiryPref'] # 就诊流水号
              "outpatientNumber": business.data['aux']['clinicalCase']?.iptOtpNo # 门诊号 仅实体医疗机构必传
              "doctorCode": business.data['aux']['clinicalCase']?.doctorHospitalPref # 接诊医生编号
              "doctorName": business.data['aux']['clinicalCase']?.doctorName # 接诊医生姓名
              "fzbz": "T(java.util.Objects).equals(business.data['aux']['clinicalCase']?.followUp,'0') ? '0' : '1'" # 复诊标志
              "inquirydate": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(business.data['aux']['clinicalCase']?.startTime) # 问诊时间
              "deptNum": "T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDict(business,'dept_dict',business.data['aux']['clinicalCase']?.deptPref)" # 科室编码，非空字典映射，系统的科室编码
              "deptName": "T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDictLabel(business,'dept_dict',business.data['aux']['clinicalCase']?.deptPref,business.data['aux']['clinicalCase']?.deptName)" # 科室名称，非空系统的科室名称
              "zsjl": T(org.apache.commons.lang3.StringUtils).joinWith(',',business.data['aux']['clinicalCase']?.mainSuit) # 主诉记录
              "zyzzmc": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['clinicalCase']?.mainSymptoms,'暂无')" # 主要症状
              "gmsbz": "T(cn.hutool.core.collection.CollUtil).isEmpty(business.data['aux']['clinicalCase']?.allergic) ? '1' : '2'" # 过敏史标志
              "gmsms": T(org.apache.commons.lang3.StringUtils).joinWith(',',business.data['aux']['clinicalCase']?.allergic) # 过敏史描述
              "xbsms": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['clinicalCase']?.currentIllnessDesc,'暂无')"  # 现病史描述
              "jwsms": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['clinicalCase']?.patientHisDesc,'暂无')" # 既往史描述
              "zyszms": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['clinicalCase']?.ext?.tcmFourDiagnosticDesc,'暂无')" # 中医“四诊”等描述
              "zxyzdbz": "T(java.util.Objects).equals(business.data['aux']['clinicalCase']?.medicineType,0) ? '2' : '1' " # 中医/西医病历标志
              "sfscsx": "business.data['aux']['clinicalCase']?.ext?.tcmUploadTongueImage == null ? '0' : business.data['aux']['clinicalCase']?.ext?.tcmUploadTongueImage" # 是否上传舌象
              "bzfx": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['clinicalCase']?.ext?.tcmDialecticalAnalysis,'暂无')" # 辨证分析
              "additionalDiagnosisList": # 诊断列表
                - "diagnosisCode": business.data['aux']['clinicalCase']?.diagnosis[_index_]['diagnosisCode'] # 诊断编码
                  "diagnosisName": business.data['aux']['clinicalCase']?.diagnosis[_index_]['diagnosisName'] # 诊断名称
                  "diagnosisClassify": "T(java.util.Objects).equals(business.data['aux']['clinicalCase']?.diagnosis[_index_]['diagnosisClassify'],0) ? '2' : '1'"  # 诊断分类
                  "diagnosisType": "'7'" # 诊断类型 默认其他
                  "diagSort": business.data['aux']['clinicalCase']?.diagnosis[_index_]['index'] # 诊断排序
              "zxdm": business.data['aux']['clinicalCase']?.tcmSyndromeCode # 中医辨证代码
              "zxmc": business.data['aux']['clinicalCase']?.tcmSyndromeName # 中医辨证名称
              "therapyList": # 中医治法列表
                - "zfdm": business.data['aux']['clinicalCase']?.tcmTreatmentMethodCode # 治法代码
                  "zfmc": business.data['aux']['clinicalCase']?.tcmTreatmentMethodName # 治法名称
              "jzzdsm": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['clinicalCase']?.ext?.outpatientDiagnosisDesc,'暂无')" # 门诊诊断说明
              "clcs": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['clinicalCase']?.measures,'暂无')" # 处理措施
              "sfxylygc": "business.data['aux']['clinicalCase']?.observation == null ? '0' : business.data['aux']['clinicalCase']?.observation" # 是否需要留院（入/转院）观察
              "referral": "business.data['aux']['clinicalCase']?.referral == null ? '0' : business.data['aux']['clinicalCase']?.referral " # 转诊
              "jdrq": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(T(java.time.LocalDateTime).now()) # 建档日期
              "tbrq": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(T(java.time.LocalDateTime).now()) # 填报日期
    response:
      "infcode": "['package']['additionInfo'][errorCode]"
      "err_msg": "['package']['additionInfo'][errorMsg]"
