domain: business.data['network']['networkItem']['common']
#path:
protocol: http
header:
  "x-rb-key": business.data['network']?.thirdPartyPublicKey
  "x-rb-timestamp": T(java.lang.System).currentTimeMillis()
  "x-rb-requuid": T(cn.hutool.core.util.IdUtil).fastSimpleUUID()
  "x-rb-sign": T(com.xyy.saas.transmitter.server.util.transmission.internet.RequestSignUtil).sign4CqSupervision(business.data['network']?.thirdPartyPublicKey,business.data['network']?.thirdPartyPrivateKey,commonHeader['x-rb-timestamp'],commonHeader['x-rb-requuid'])
  "Content-Type": "'application/json'"
input:
  "package":
    "head":
      #时间(14)+顺序号(4) 时间格式：yyyyMMddHHmmss
      "sendTradeNum": T(cn.hutool.core.date.DateUtil).date().toString("yyyyMMddHHmmss")+T(java.lang.String).format("%04d", T(cn.hutool.core.util.IdUtil).createSnowflake(1, 1).nextId() % 10000) #医院消息唯一码
      "senderCode": "'400080041'" #医院编码400XXX
      "senderName": "'重庆格林医院互联网医院'" #医院名称
      "CACode": "'CA014'" #医院采用的电子签名厂商编码
      "CAName": "'东方中讯数字证书认证有限公司'" #医院采用的电子签名厂商名称
      "receiverCode": "'YY0000'" #互联网医疗服务监管平台唯一码
      "receiverName": "'互联网医疗服务监管平台'" #互联网医疗服务监管平台
      "intermediaryCode": "'hywz'"
      "intermediaryName": "'荷叶问诊'"
      "hosorgNum": "'001'"
      "hosorgName": "'小荷'"
      "systemType": "'1'"
      "busenissType": "'8'"
      "standardVersionCode": "'version:1.0.0'"
      "clientmacAddress": "'7C8AE1CB0010'"
      "recordCount": "'1'"
    "additionInfo":
      "curDllAddr": "''"
      "receiverTradeNum": "''"
      "asyncAsk": "'0'"
      "errorCode": "''"
      "callback": "''"
      "correlationId": "'9909'"
      "errorMsg": "''"
#    "body":

result:
  #  success: true
  success: output[infcode] == '0'
  tips: output[err_msg]