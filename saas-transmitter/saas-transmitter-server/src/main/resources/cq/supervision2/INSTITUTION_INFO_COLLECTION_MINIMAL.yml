dslType: contract
enable: true
name: 机构信息采集接口
protocol: webservice
common: false
format: xml
domain: "'http://182.121.65.106:18081'"
functions:
  - path: "'/its/webService'"
    bodyStrategy: xmlSoap
    protocol: webservice
    timeout: 30 # 超时时间(秒)
    request:
      method: POST
      header:
        "Content-Type": "'text/xml; charset=utf-8'"
        "SOAPAction": "''"
      body:
        "namespace": "'http://putoutWebService.wsi.com/'"
        "methodName": "'COLLECTDATA_ORG'"
        "methodData":
          "xmlParm":
            xmlEscape: true  # 新增字段，标识需要XML转义
            value:
              "NOTICEMSG":
                "ORG_INFO":
                  "ORG_CODE": "'H41030300121'" # 机构代码
                  "ORG_NAME": "'洛阳恩济医院'" # 机构名称
                  "ORG_TYPE_CODE": "''" # 机构类型代码
                  "ORG_TYPE_NAME": "''" # 机构类型名称
                  "START_DATE": "''" # 开始日期
                  "END_DATE": "''" # 结束日期
                  "IH_ADDRESS": "''" # 机构地址
                  "IH_DNS": "''" # 机构DNS
                  "ADDR_STATE": "''" # 地址省份
                  "ADDR_CITY": "''" # 地址城市
                  "ADDR_COUNTY": "''" # 地址县区
                  "ADDR_TOWN": "''" # 地址乡镇
                  "ADDR_STREET": "''" # 地址街道
                  "ADDR_HOUSE_NUMBER": "''" # 地址门牌号
                  "IH_LEGALPERSON": "''" # 机构法人
                  "ECONOMY_TYPE_CODE": "''" # 经济类型代码
                  "ECONOMY_TYPE_NAME": "''" # 经济类型名称
                  "MED_INS_USCC": business.data['data']['tenantInfo']['businessLicenseNumber']
                  "MED_INS_NAME": business.data['data']['tenantInfo']['name']
                  "MED_INS_ADDRESS": business.data['data']['tenantInfo']['address']
                  "MED_INS_LP_NAME": business.data['data']['tenantInfo']['contactName']
                  "ORG_LEVEL_CODE": "''" # 机构级别代码
                  "ORG_LEVEL_NAME": "''" # 机构级别名称
                  "ENTERPRISE_USCC": "''" # 企业统一社会信用代码
                  "ENTERPRISE_NAME": "''" # 企业名称
                  "ENTERPRISE_ADDRESS": "''" # 企业地址
                  "ENTERPRISE_LP_NAME": "''" # 企业法人姓名
                  "IH_SAFE_LV": "''" # 机构安全等级
                  "DESCRIPTION": "''" # 描述
                  "IH_TELPHONE": "''" # 机构电话
                  "IH_MAIL": "''" # 机构邮箱
                  "ADDR_POSTALCODE": "''" # 地址邮政编码
                  "SPECIAL_DEPART_DESCR": "''" # 特殊科室描述
                  "DEPART_NUM": "''" # 科室数量
                  "STAFF_NUM": "''" # 员工数量
                  "INTER_MATUR": "''" # 内部成熟度
                  "IH_ELEC_LEVEL": "''" # 机构电子化水平
          "password": "'ej60232688'" # 监管平台密码(必传)
          "username": "'lyejyy'" # 监管平台用户名(必传)
    response:
      "infcode": "['COLLECTDATA_ORGResponse']['return']['RESPONSE']['ERRORCODE']" # 错误代码
      "err_msg": "['COLLECTDATA_ORGResponse']['return']['RESPONSE']['ERRORMSG']" # 错误信息