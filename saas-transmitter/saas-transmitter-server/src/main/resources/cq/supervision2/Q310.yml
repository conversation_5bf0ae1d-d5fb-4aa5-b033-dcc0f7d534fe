dslType: contract
enable: true
name: 线上处方点评
format: json
functions:
  - path: "'/rainbow/api/hpcp/hospolmonitor/prescriptionReview'"
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "package":
          "head":
            "busseID": "'Q310'"
          "body":
            - "patientNo": business.data['data']['patientPref'] # 患者编号，医院患者编号
              "medicalNum": business.data['data']['inquiryPref'] # 就诊流水号，医院门诊号，医疗机构内部门诊就诊唯一编号
              "patientName": business.data['data']['fullName'] # 患者姓名
              "patientSex": business.data['data']['patientSex'] # 患者性别，字典映射：1：男性；2：女性；9：未说明性别
              "patientIdType": "'01'" # 证件类型，字典映射
              "patientIdNo": business.data['aux']['inquiryDetailInfo']?.patientIdCard # 证件号码
              "birthday": "T(com.xyy.saas.inquiry.util.IdCardUtil).getBirthdayByIdCard(business.data['aux']['inquiryDetailInfo']?.patientIdCard)"  # 出生日期，格式YYYYMMDD
              "race": "'01'" # 民族，字典映射 - 默认汉族
              "recipeNum": business.data['data']['pref'] # 处方号
              "recipeStatus": "'0'" # 处方状态，字典映射：0.正常处方；1.退药或者其他作废处方，不传默认0
              "recipeType": "'1'" # 处方类别，字典映射：1.普通处方；2.儿科处方；3.麻醉处方；4.急诊处方；5.其他处方
              "recipeSource": "'3'" # 处方来源：1.门诊；2.急诊； 3.其他
              "epitaxy": "'1'" # 1非外延，2外延
              "recipeCategory": "T(java.util.Objects).equals(business.data['data']['medicineType'],0) ? '3' : '1' " # 处方类型，字典映射：1.草药方；2.中成药方；3.西药方
              "deptCode": business.data['data']['deptPref'] # 科室编码，字典映射
              "deptName": business.data['data']['deptName'] # 科室名称
              "hosDeptCode": business.data['data']['deptPref'] # 医院科室编码，院内科室编码
              "hosDeptName": business.data['data']['deptName'] # 医院科室名称，院内科室名称
              "recipeDocCode": business.data['aux']?.get('doctorInfo')?.doctorHospitalPref # 开方医生编码
              "recipeDocName": business.data['data']['doctorName'] # 开方医生姓名
              "recipeDocTitle": "T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDict(business,'doctor_title',business.data['aux']['doctorInfo']?.titleCode)" # 开方医生职称，字典映射
              "recipeDocSignatureValue": business.data['aux']?.get('prescriptionCa')?.get('doctorSign')?.signValue  # 开方医生数字签名值，需是符合PKCS#1格式规范的电子签名值，仅互联网诊疗必传
              "recipeDocSigncertificate": business.data['aux']['prescriptionCa']?.get('doctorSign')?.cerFile  # 开方医生数字证书，需是符合X509格式规范的BASE64编码数字证书值，需为SM2证书，仅互联网诊疗必传
              "recipeDocTimeStamp": business.data['aux']['prescriptionCa']?.get('doctorSign')?.signTimestamp  # 开方医生时间戳，需是符合SM2国密标准的BASE64编码时间戳值，仅互联网诊疗必传
              "recipeDocSignatureXML": "T(com.xyy.saas.transmitter.server.util.transmission.internet.RequestParamUtil).getCqCaXmlParam(business.data['data'],business.data['aux'],true)" # 开方医生数字签名对象数据结构XML
              "recipeDate": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(business.data['data']['outPrescriptionTime']) # 开方时间，格式YYYYMMDDHH24MISS
              "trialPharmCode": business.data['data']['pharmacistPref'] # 审方药师编码，仅互联网诊疗必传
              "trialPharmName": business.data['data']['pharmacistName'] # 审方药师名称，仅互联网诊疗必传
              "trialPharmTitle": "'244'" # 审方药师职称，字典映射，仅互联网诊疗必传  244-药师
              "trialPharmSignatureValue": business.data['aux']['prescriptionCa']?.get('pharmacistSign')?.signValue # 审方药师数字签名值，需是符合PKCS#1格式规范的电子签名值，仅互联网诊疗必传
              "trialPharmSigncertificate": business.data['aux']['prescriptionCa']?.get('pharmacistSign')?.cerFile # 审方药师数字证书，需是符合X509格式规范的BASE64编码数字证书值，需为SM2证书，仅互联网诊疗必传
              "trialPharmTimeStamp": business.data['aux']['prescriptionCa']?.get('pharmacistSign')?.signTimestamp # 审方药师时间戳，需是符合SM2国密标准的BASE64编码时间戳值，仅互联网诊疗必传
              "trialPharmSignatureXML": "T(com.xyy.saas.transmitter.server.util.transmission.internet.RequestParamUtil).getCqCaXmlParam(business.data['data'],business.data['aux'],false)"  # 审方药师数字签名对象数据结构XML
              "trialDate": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(business.data['data']['auditPrescriptionTime']) # 审方时间，格式YYYYMMDDhh24:mi:ss
              "recipeFeeTotal": business.data['data']['ext']['pricingPrice'] # 处方金额，2位小数
              "recipedistribut": "'3'" # 处方配送标识，字典映射：1-医院药房自取、2-医院派送、3-其他药店自取、4-其他药店派送，不传默认为1，仅互联网诊疗必传
              "keepUseFlag": "'1'" # 继用处方标识，1非继用，2继用，不传默认1
              "longReciptFlag": "'1'" # 是否长处方标识，1非长处方2长处方，不传默认1
              "additionalDiagnosisList": # 诊断列表
                - "diagnosisCode": business.data['aux']['clinicalCase']?.diagnosis[_index_]['diagnosisCode'] # 诊断编码，西医诊断参照ICD-10《疾病和有关健康问题的国际统计分类》
                  "diagnosisName": business.data['aux']['clinicalCase']?.diagnosis[_index_]['diagnosisName'] # 诊断名称，西医诊断参照ICD-10《疾病和有关健康问题的国际统计分类》
                  "diagnosisClassify": "T(java.util.Objects).equals(business.data['aux']['clinicalCase']?.diagnosis[_index_]['diagnosisClassify'],0) ? '2' : '1'"  # 诊断分类，字典映射：1中医诊断，2西医诊断，中医病历时必有中医诊断和西医诊断
                  "diagnosisType": "'7'" # 诊断类型，字典映射，说明：医院诊断和对应的医保诊断都需要上传，如果非医保患者默认本地职工对应的诊断
                  "diagSort": business.data['aux']['clinicalCase']?.diagnosis[_index_]['index'] # 诊断排序
              "symptomCode": business.data['aux']['clinicalCase']?.tcmSyndromeCode # 中医辨证编码，中医必填
              "symptomCodeName": business.data['aux']['clinicalCase']?.tcmSyndromeName # 中医辨证名称，中医必填
              "drugList": # 处方药品明细信息
                - "recipeSerialNum": _index_ # 处方流水号，Number类型，同一个就诊下，处方流水号在中心端能够唯一标识一条处方明细信息
                  "groupNo": "'1'" # 配伍组号，药物分组使用时的组号
                  "hospitalDrugCode": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['standardId'],business.data['aux']['prescriptionDetail'][_index_]['productPref'])" # 药品编号，医院药品编号
                  "drugCommonName": business.data['aux']['prescriptionDetail'][_index_]['commonName'] # 药品通用名
                  "drugBrandName": business.data['aux']['prescriptionDetail'][_index_]['productName'] # 药品商品名，如泰诺等
                  "drugDose": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['singleDose'],business.data['data']['ext']['tcmDailyDosage'])" # 单次给药剂量，包含饮片单次剂量
                  "drugDoseUnit": "T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDict(business,'drug_dose_unit',  T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business
                  .data['aux']['prescriptionDetail'][_index_]['singleUnit'],'g') )" # 单次给药剂量单位，字典映射
                  "medicationRoute": "T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDict(business,'drug_directions',T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business
                  .data['aux']['prescriptionDetail'][_index_]['directions'],business.data['data']['ext']['tcmDirections'] ) ) " # 给药途径，字典映射：包括饮片给药途径
                  "frequency": "T(cn.hutool.extra.spring.SpringUtil).getBean('dictService').convertOrganDict(business,'drug_use_frequency',T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business
                  .data['aux']['prescriptionDetail'][_index_]['useFrequency'],'2次/天' ) )" # 给药频率，字典映射
                  "formulation": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['prescriptionDetail'][_index_]['dosageForm'],'-')" # 剂型，包含饮片剂型
                  "spec": business.data['aux']['prescriptionDetail'][_index_]['attributeSpecification'] # 规格，药品信息、医用材料时一般不为空，其他为空
                  "deliverNum": business.data['aux']['prescriptionDetail'][_index_]['quantity'] # 发药数量，4位小数，发药数量，指多少个药品包装规格单位（包含饮片贴数）
                  "deliverNumUnit": business.data['aux']['prescriptionDetail'][_index_]['packageUnit'] # 数量单位，标准单位，发药数量单位
                  "money": business.data['aux']['prescriptionDetail'][_index_]['actualAmount'] # 金额，4位小数，该药品的单价*发药数量，计量单位为人民币元
                  "productFactory": business.data['aux']['prescriptionDetail'][_index_]['manufacturer'] # 药品厂家，如为药品，提供商品厂家名
                  "drugCategory": "T(java.util.Objects).equals(business.data['data']['medicineType'],0) ? '01' : '03' "  # 药品类别，字典映射：01- 西药；02- 中成药；03- 中药饮
    response:
      "infcode": "['package']['additionInfo'][errorCode]"
      "err_msg": "['package']['additionInfo'][errorMsg]"
      "electronicRxSn": "['package']['body'][0]['p_controlResultNo']" # 电子处方平台流水号