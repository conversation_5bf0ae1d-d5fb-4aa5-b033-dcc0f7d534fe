dslType: contract
enable: true
name: 病例数据采集接口
protocol: webservice
common: false
format: xml
domain: "'http://182.121.65.106:18081'"
functions:
  - path: "'/its/webService'"
    bodyStrategy: xmlSoap
    protocol: webservice
    timeout: 30 # 超时时间(秒)
    request:
      method: POST
      header:
        "Content-Type": "'text/xml; charset=utf-8'"
        "SOAPAction": "''"
      body:
        "namespace": "'http://putoutWebService.wsi.com/'"
        "methodName": "'COLLECTDATA_EMR'"
        "methodData":
          "xmlParm":
            xmlEscape: true  # 新增字段，标识需要XML转义
            value:
              "NOTICEMSG":
                "PATIENT_INFO":
                  "ORG_CODE": "'H41030300121'" # 机构代码
                  "ORG_NAME": "'洛阳恩济医院互联网医院'"
                  "PATIENT_ORG_NO": business.data['data']['patientPref']
                  "HEALTH_REC_NO": "'1'" # 健康档案号
                  "PATIENT_NAME": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['inquiryDetailInfo']?.patientName,'未知患者')"
                  "IDENTITY_CARD_VALUE": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['inquiryDetailInfo']?.patientIdCard,'暂无')"
                  "ID_CARD_TYPE_CODE": "'01'"
                  "INSURANCE_TYPE_NAME": "'99'"
                  "CARD_VALUE": business.data['data']['patientPref']
                  "CARD_TYPE_CODE": "'2'"
                  "GENDER_NAME": "T(java.util.Objects).equals(business.data['aux']['inquiryDetailInfo']?.patientSex,1) ? '男' : '女'"
                  "GENDER_CODE": business.data['aux']['inquiryDetailInfo']?.patientSex
                  "BIRTHDAY": "T(org.apache.commons.lang3.StringUtils).isBlank(business.data['aux']['inquiryDetailInfo']?.patientIdCard) ? '未知' : T(org.apache.commons.lang3.StringUtils).substring(business.data['aux']['inquiryDetailInfo']?.patientIdCard,6,14)"
                  "BIRTH_PLACE": "'1'" # 出生地
                  "HUKOU_ADDR_DESCR": "'1'" # 户口地址描述
                  "NOW_ADDR_DESCR": "'1'" # 现住址描述
                  "NATIONALITY_NAME": "'156'"
                  "NATIVE_PLACE": "'1'" # 籍贯
                  "NATION_NAME": "'汉族'"
                  "NATION_CODE": "'01'"
                  "POLITICAL_AFFILIATION": "'1'" # 政治面貌
                  "RELIGION": "'1'" # 宗教信仰
                  "MARITAL_NAME": "'未说明的婚姻状况'"
                  "MARITAL_CODE": "'90'"
                  "EDU_BACKGROUND_NAME": "'1'" # 文化程度名称
                  "EDU_BACKGROUND_CODE": "'1'" # 文化程度代码
                  "OCCUPATIENTION_NAME": "'1'" # 职业名称
                  "OCCUPATIENTION_CODE": "'1'" # 职业代码
                  "WORK_ORG_NAME": "'1'" # 工作单位名称
                  "ADDRESS": "'1'" # 地址
                  "TELEPHONE": business.data['aux']['inquiryDetailInfo']?.patientMobile
                  "CONTACT": "T(org.apache.commons.lang3.StringUtils).isBlank(business.data['aux']['inquiryDetailInfo']?.ext?.guardianName) ? business.data['aux']['inquiryDetailInfo']?.patientName : business.data['aux']['inquiryDetailInfo']?.ext?.guardianName"
                  "RELATIONSHIP": "T(org.apache.commons.lang3.StringUtils).isBlank(business.data['aux']['inquiryDetailInfo']?.ext?.guardianName) ? '0' : '8'"
                  "FAMDOCT_SIGN_MARK": "'1'" # 家庭医生签约标识
                  "FAMDOCT_SIGN_NO": "'1'" # 家庭医生签约号
                  "FAMDOCT_SIGN_NAME": "'1'" # 家庭医生签约姓名
                  "UPDATE_TIME": "T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(T(java.time.LocalDateTime).now())"
                  "CREATE_TIME": "T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(T(java.time.LocalDateTime).now())"
                  "UPLOAD_STATUS_MARK": "'1'"
                  "DATA_RANK": "'1'"
                "TREATMENT_RECORT":
                  "ORG_CODE": "'H41030300121'" # 机构代码
                  "PATIENT_ORG_NO": business.data['data']['patientPref']
                  "DIAG_NO": business.data['data']['inquiryPref']
                  "DIAG_TYPE_CODE": "'1'"
                  "DIAG_TYPE_NAME": "'门诊'"
                  "INSURE_TYPE": "'3'"
                  "CARD_NO": "'1'" # 卡号
                  "CARD_TYPE": "'1'" # 卡类型
                  "PAT_NO": business.data['data']['inquiryPref']
                  "DEPART_NO": business.data['data']['deptPref']
                  "DEPART_NAME": business.data['data']['deptName']
                  "PATIENT_NAME": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['inquiryDetailInfo']?.patientName,'未知患者')"
                  "GENDER_CODE": business.data['aux']['inquiryDetailInfo']?.patientSex
                  "GENDER_NAME": "T(java.util.Objects).equals(business.data['aux']['inquiryDetailInfo']?.patientSex,1) ? '男' : '女'"
                  "AGE_YEAR": business.data['aux']['inquiryDetailInfo']?.patientAge
                  "AGE_MONTH": "'1'" # 年龄(月)
                  "AGE_DAY": "'1'" # 年龄(日)
                  "ALLERGIC_HIS_MARK": "T(org.springframework.util.CollectionUtils).isEmpty(business.data['aux']['inquiryDetailInfo']?.allergic) ? '0' : '1'"
                  "ALLERGIC_HIS": "T(org.apache.commons.lang3.StringUtils).join(business.data['aux']['inquiryDetailInfo']?.allergic)"
                  "MEDICAL_START_DATE": "T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(T(java.time.LocalDateTime).now())"
                  "MEDICAL_DIAGNOSIS": "'1'" # 医疗诊断
                  "FIRST_DIAGNOSIS_FLAG": "'1'" # 初诊标识
                  "CHIEF_COMPLAINT": "T(org.apache.commons.lang3.StringUtils).join(business.data['aux']['inquiryDetailInfo']?.mainSuit)"
                  "NOW_DIS_HIS": "'1'" # 现病史
                  "PAST_HIS": "'1'" # 既往史
                  "DIAG_NAME_TYPE_CODE": "'2'"
                  "DIAG_NAME_TYPE_NAME": "'门诊诊断'"
                  "DESCRIBE_DIAG": "'1'" # 诊断描述
                  "IH_DIAGNOSIS_CODE_TYPE": "'2'"
                  "SYMPTOM": "'1'" # 症状
                  "DISCRIMINATE_ACCORD": "'1'" # 鉴别依据
                  "HEALTH_GUIDANCE": "'1'" # 健康指导
                  "DISPOSAL_PLAN": "'1'" # 处置计划
                  "DOCT_NO": business.data['data']['doctorPref']
                  "DOCT_NAME": business.data['data']['doctorName']
                  "MED_PRACTICE_NAME": "'1'" # 医疗执业证书名称
                  "MED_INS_USCC": "'H41030300121'"
                  "MED_INS_NAME": "'洛阳恩济医院互联网医院'"
                  "MEDICAL_END_DATE": "T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(T(java.time.LocalDateTime).now())"
                  "RETURN_FLAG": "'0'"
                  "APPOINTMENT_FLAG": "'0'"
                  "SATISFACTION_DEGREE": "'1'" # 满意度
                  "E_RP_ADDR": "'1'" # 电子处方地址
                  "VCR_DOC": "'1'" # 医生视频
                  "VCR_PAT": "'1'" # 患者视频
                  "AUDIO_ADDR": "'1'" # 音频地址
                  "REVOKE_FLAG": "'0'"
                  "CREATE_TIME": "T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(T(java.time.LocalDateTime).now())"
                  "UPLOAD_STATUS_MARK": "'1'"
                  "DATA_RANK": "'1'"
                  "UPDATE_TIME": "T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(T(java.time.LocalDateTime).now())"
                "TREATMENT_DIAG":
                  "ORG_CODE": "'H41030300121'"
                  "DIAG_NO": business.data['data']['inquiryPref']
                  "BUSINESS_NO": business.data['data']['inquiryPref']
                  "PATIENT_NAME": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['inquiryDetailInfo']?.patientName,'未知患者')"
                  "MEDICAL_DATE": "T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(T(java.time.LocalDateTime).now())"
                  "DIAGNOSIS_DOC_NO": business.data['data']['doctorPref']
                  "DIAGNOSIS_DOC_NAME": business.data['data']['doctorName']
                  "DIAGNOSIS_DATE": "T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(T(java.time.LocalDateTime).now())"
                  "DIAGNOSIS_LISTS":
                    "DIAGNOSIS_LIST":
                      - "DIAGNOSIS_ID": "'1'"
                        "DIAGNOSIS_CODE": "'1'" # 诊断代码
                        "DIAGNOSIS_NAME": "'1'" # 诊断名称
                        "DIAGNOSIS_TYPE_CODE": "'1'" # 诊断类型代码
                        "IH_DIAGNOSIS_CODE_TYPE": "'2'"
                        "DIAGNOSIS_REMARK": "'1'" # 诊断备注
                        "DIAGNOSIS_TYPE": "'1'"
                        "IS_TCM_DIAG": "'1'" # 是否中医诊断
                        "SYMPT_TCM_NAME": "'1'" # 中医症状名称
                        "SYMPT_TCM_CODE": "'1'" # 中医症状代码
                  "REVOKE_FLAG": "'0'"
                  "CREATE_TIME": "T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(T(java.time.LocalDateTime).now())"
                "TREATMENT_ORDER":
                  "ORG_CODE": "'H41030300121'" # 机构代码
                  "DIAG_NO": business.data['data']['inquiryPref']
                  "RECIPEL_NO": business.data['data']['pref']
                  "BUSINESS_NO": business.data['data']['pref']
                  "PRESCRIPTION_DETAIL_ID": "'1'"
                  "PRESCRIPTION_DETAIL_INS": "'1'" # 处方明细说明
                  "RECIPEL_BUILD_DATE": "T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(T(java.time.LocalDateTime).now())"
                  "RECIPEL_VALID_DAY": "'1'"
                  "ADVICE": "'1'" # 医嘱
                  "DEPART_NO": business.data['data']['deptPref']
                  "DEPART_NAME": business.data['data']['deptName']
                  "DOCT_AD_PROJ_TYPE_NAME": "T(java.util.Objects).equals(business.data['data']['medicineType'],0) ? '西药' : '中药'"
                  "DOCT_AD_PROJ_TYPE_CODE": "T(java.util.Objects).equals(business.data['data']['medicineType'],0) ? '11' : '13'"
                  "TCM_MARK": "'1'" # 中医标识
                  "THERAPEUTIC_TREAT": "'1'" # 治疗性治疗
                  "WHETHER_TCM_HERBAL": "'1'" # 是否中药饮片
                  "WHETHER_GUARAN_PATIENTS": "'1'" # 是否保证患者
                  "DRUGS_LISTS":
                    "DRUGS_LIST":
                      - "DRUGS_SUPERVISE_CODE": "'1'" # 药品监管码
                        "DRUG_COMMON_NAME_CODE": "'1'" # 药品通用名代码
                        "DRUG_COMMON_NAME_CODE_NAME": "'1'" # 药品通用名代码名称
                        "DRUG_NAME_CODE": "'1'" # 药品名称代码
                        "DRUGS_NAME": "'1'" # 药品名称
                        "DRUGS_SPEC": "'1'" # 药品规格
                        "ANTIMICROBIAL_DDD_VALUE": "'1'" # 抗菌药DDD值
                        "ANTIMICROBIAL_DDD_UNIT": "'1'" # 抗菌药DDD单位
                        "DRUG_FORM_NAME": "'1'" # 药品剂型名称
                        "DRUGS_FORM": "'1'" # 药品剂型
                        "DRUGS_DOSE": "'1'" # 药品剂量
                        "DOSE_UNIT": "'1'" # 剂量单位
                        "FREQUENCY_CODE": "'1'" # 频次代码
                        "FREQUENCY_NAME": "'1'" # 频次名称
                        "DRUGS_USE_CODE": "'1'" # 用药途径代码
                        "DRUGS_USE_NAME": "'1'" # 用药途径名称
                        "DRUGS_DOSE_TOTAL": "'1'" # 药品总剂量
                        "DOSE_GIVE": "'1'" # 给药剂量
                        "DOSE_GIVE_UNIT": "'1'" # 给药剂量单位
                        "DRUGS_PRICE": "'1'" # 药品价格
                        "WETHER_BASIC_DRUG": "'1'" # 是否基本药物
                        "TCM_TECHNOLOGY_CODE": "'1'" # 中医技术代码
                        "TCM_TECHNOLOGY_NAME": "'1'" # 中医技术名称
                        "TCM_TECHNOLOGY_DESC": "'1'" # 中医技术描述
                        "MEDI_TCM_HERBAL_NUM": "'1'" # 中药饮片数量
                        "MEDI_TCM_DECOCTION_METHOD": "'1'" # 中药煎煮方法
                        "DRUG_USE_TCM_WAY": "'1'" # 中药用药方式
                        "DRUG_TREATMENT_DAY_NUM": "'1'" # 药物治疗天数
                  "PATIENT_NAME": "T(org.apache.commons.lang3.StringUtils).defaultIfBlank(business.data['aux']['inquiryDetailInfo']?.patientName,'未知患者')"
                  "MEDICAL_DATE": "T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(T(java.time.LocalDateTime).now())"
                  "PRICE_TOTAL": "'1'" # 总价格
                  "PRESCRIPTION_DOC_NO": business.data['data']['doctorPref']
                  "PRESCRIPTION_DOC_NAME": business.data['data']['doctorName']
                  "PRESCRIPTION_PHARM_VER_NO": "'1'" # 处方药师审核号
                  "PRESCRIPTION_PHARM_VER_NAME": "'1'" # 处方药师审核姓名
                  "PRESCRIPTION_PHARM_CON_NO": "'1'" # 处方药师调配号
                  "PRESCRIPTION_PHARM_CON_NAME": "'1'" # 处方药师调配姓名
                  "PRESCRIPTION_PHARM_GAR_NO": "'1'" # 处方药师核发号
                  "PRESCRIPTION_PHARM_GAR_NAME": "'1'" # 处方药师核发姓名
                  "DO_RESULT": "'1'" # 执行结果
                  "REMARK": "'1'" # 备注
                  "GET_DRUG_ORG_CODE": "'1'" # 取药机构代码
                  "GET_DRUG_ORG_NAME": "'1'" # 取药机构名称
                  "BUYING_MEANS": "'1'" # 购买方式
                  "DELIVERY_ORG": "'1'" # 配送机构
                  "RECIPEL_REMARK_INFO": "'1'" # 处方备注信息
                  "REVOKE_FLAG": "'0'"
                  "CREATE_TIME": "T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(T(java.time.LocalDateTime).now())"
          "password": "'ej60232688'" # 监管平台密码(必传)
          "username": "'lyejyy'" # 监管平台用户名(必传)
    response:
      "ERRORCODE": "['COLLECTDATA_EMRResponse']['return']['RESPONSE']['ERRORCODE']" # 错误代码
      "ERRORMSG": "['COLLECTDATA_EMRResponse']['return']['RESPONSE']['ERRORMSG']" # 错误信息
    result:
      success: "T(java.util.Objects).equals(output['ERRORCODE'], '0')" # 错误代码为0表示成功
      tips: "output['ERRORMSG']" # 显示错误信息