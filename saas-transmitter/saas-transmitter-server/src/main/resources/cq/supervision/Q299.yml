dslType: contract
enable: true
name: 在线挂号
format: json
functions:
  - path: "'/rainbow/api/hpcp/hospolmonitor/olregister'"
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "package":
          "head":
            "busseID": "'Q299'"
          "body":
            - "medicalNum": business.data['data']['registrationPref'] # 就诊流水号，医院门诊号，医疗机构内部门诊就诊唯一编号
              "billNum": business.data['data']['registrationPref'] # 单据号，非空单据号并不是发票号，只是标识一个就诊流水号下一笔费用结算单据
              "medicalType": "'10'" # 医疗类别，字典映射 10-药店购药 11-普通门诊
              "treatDate": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(T(java.time.LocalDateTime).now()) # 就诊时间，格式YYYYMMDDHH24MISS，指的是患者实际就诊时间，不是HIS系统数据产生时间
              "deptNum": "'02'" # 科室编码，非空字典映射，系统的科室编码
              "deptName": "'全科医疗科'" # 科室名称，非空系统的科室名称
              "outpatientNumber": business.data['data']['patientPref'] # 门诊号，非空医院HIS系统中用来标识一次门诊
              "specialpatientID": "'0'" # 特殊患者标识，0:常规患者；1：高血压患者特病；2：糖尿病患者特病；3：血透析患者
              "reservationType": "'2'" # 预约来源类型，1:APP；2:网上；3:电话；4:其它预约；0：非预约
              "referral": "'0'" # 转诊，3：基层医疗医疗机构转入；4：上级医疗机构转入；5：其它医疗机构转入；0:非转诊
              "siType": "'1'" # 医保类型，1,市职工医保；2，城乡居民；3，市内非医保；4，市外医保；5，市外非医保；6，离休干部
              "doctorCode": "'D421087003352'" # 诊断医师编号
              "doctorName": "'肖治坤'" # 诊断医生姓名
              "credentialType": "'01'" # 证件类型，字典映射
              "credentialNum": business.data['data']['idCard'] # 证件号码
              "name": business.data['data']['fullName'] # 患者姓名，非空
              "gender": business.data['data']['patientSex'] # 患者性别，非空1：男性；2：女性；9：未说明性别
              "birthday": "T(com.xyy.saas.inquiry.util.IdCardUtil).getBirthdayByIdCard(business.data['data']['idCard'])" # 出生日期，格式YYYYMMDD，非空
              "race": "'01'" # 民族，非空字典映射
              "sumMoney": "0.00" # 费用总额，float(4)，非空2位小数
              "updateBy": business.data['aux']['operateUserInfo']?.nickname # 经办人，非空医疗机构操作员姓名
              "settleDate": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(T(java.time.LocalDateTime).now()) # 结算时间，格式YYYYMMDDHH24MISS，非空
              "invoiceNO": business.data['data']['registrationPref'] # 发票号，非空票据上的发票号码
              "guardianName": "''" # 患者监护人姓名，6岁以下患者就诊时必填
              "guardianIdType": "''" # 监护人证件类型，字典映射；6岁以下患者就诊时必填
              "guardianIdNo": "''" # 监护人证件号码，6岁以下患者就诊时必填
              "isKnowAgreePaper": "'1'" # 是否签署知情同意书 0否 1 是
              #            "additionalDiagnosisList": # 诊断列表
              #              - "diagnosisCode": business.data['aux']['clinicalCase']?.diagnosis[_index_]['diagnosisCode'] # 诊断编码，西医诊断参照ICD-10《疾病和有关健康问题的国际统计分类》
              #                "diagnosisName": business.data['aux']['clinicalCase']?.diagnosis[_index_]['diagnosisName'] # 诊断名称，西医诊断参照ICD-10《疾病和有关健康问题的国际统计分类》
              #                "diagnosisClassify": "T(java.util.Objects).equals(business.data['aux']['clinicalCase']?.diagnosis[_index_]['diagnosisClassify'],0) ? '2' : '1'"  # 诊断分类，字典映射：1中医诊断，2西医诊断，中医病历时必有中医诊断和西医诊断
              #                "diagnosisType": "'7'" # 诊断类型，字典映射，说明：医院诊断和对应的医保诊断都需要上传，如果非医保患者默认本地职工对应的诊断
              #                "diagSort": _index_ # 诊断排序，诊断排序：0、主要诊断，1、次要诊断1,2、次要诊断2，等，排序从0开始
              "composite": # 医保已支付费用列表 无值传 0.00
                - "selfCareAmount": "'0.00'" # 自理金额，2位小数，指乙类药品、诊疗项目、服务设施中个人按比例先行支付部分
                  "selfAmount": "'0.00'" # 自费金额，2位小数，指丙类药品、丙类诊疗项目、丙类服务设施和超限价部分
                  "inInsureMoney": "'0.00'" # 符合医保费用，2位小数，指的是符合基本医疗保险费用，在药品、诊疗项目和服务设施的甲类和乙类费用中刨除自理的费用，即总费用–自理自费
                  "medicareFundCost": "'0.00'" # 医保基金，所有医保基金支付总额，2位小数
                  "medicarePayLine": "'0.00'" # 医保起付线，本次就医的起付金，2位小数
                  "perBearMoney": "'0.00'" # 个人自付，符合医保费用中由个人支付的部分，包含起付标准，不包含转诊先自付，2位小数
                  "hosBearMoney": "'0.00'" # 医院负担，各别地方医保政策中需要医院负担的金额，2位小数
                  "priorBurdenMoney": "'0.00'" # 转诊先自付，患者从外地转入就诊，根据当地医保政策转外就诊需自付金额，2位小数
                  "sectionCoordinatePayMoney": "'0.00'" # 统筹分段自付，统筹分段计算的个人自付金额，2位小数
                  "overCappingPayMoney": "'0.00'" # 超封顶线自付，超过统筹封顶线自付金额，2位小数
                  "fundMoney": "'0.00'" # 统筹基金支付，根据人员身份进行填写（基本医疗保险基金支付、城镇居民医疗基金支付、新农合补偿金额），2位小数
                  "civilServantFundMoney": "'0.00'" # 公务员基金支付，公务员补充医疗保险支付金额，2位小数
                  "seriousFundMoney": "'0.00'" # 大病基金支付，大病基金支付金额，2位小数
                  "accountFundMoney": "'0.00'" # 账户支付，本次个人账户支付金额，2位小数
                  "civilSubsidy": "'0.00'" # 民政救助支付，民政救助金额，2位小数
                  "otherFundMoney": "'0.00'" # 其他基金支付，除过上述基金支付外的基金支付金额，2位小数
                  "cashMoney": "'0.00'" # 本次现金支付，个人现金支付金额，2位小数
              "recipeList": # 医疗项目明细信息
                - "recipeSerialNum": business.data['data']['registrationPref'] # 同一个就诊下，处方流水号在中心端能够 唯一标识一条处方明细信息 目录类别为1，则上传处方明细流水号； 其余收费明细流水号
                  "listCat": "'2'" # 目录类别，非空1:药品；2:诊疗项目；3:服务设施；4:医用材料；5：转诊
                  "medicalItemCat": "'27'" # 医疗项目类别，字典映射挂号-27
                  "recipeNum": business.data['data']['registrationPref'] # 处方号，非空目录类别为1，则上传处方号；其余上传发票号；若没有发票号，则传单据号
                  "recipeDate": T(java.time.format.DateTimeFormatter).ofPattern("yyyyMMddHHmmss").format(T(java.time.LocalDateTime).now()) # 收费日期，格式YYYYMMDDHH24MISS，非空
                  "hospitalChargeCode": "'t4y8eashpsijj3kz8dx6b4by43kjkhp5'" # 医院收费项目编码，非空
                  "hospitalChargeName": "'在线复诊服务'" # 医院收费项目名称，非空
                  "priceitemCode": "'104541'" # 物价项目编码，非空物价局统一的医疗服务项目编码，如普通门诊诊察费：AAAA0001，不是医疗服务项目时，传医院收费编码
                  "centreChargeCode": "'501110000050000-111101001.20'" # 医保收费项目编码，非空 本地就医时对应患者的医保编号，如目录类别是药品时，项目编码指的是药品编码；如果目录类别是诊疗项目时，项目编码为诊疗项目编码；如果目录类别为医用材料时，项目编码为医用材料编码。自费收费时，默认为本地城镇职工医保收费项目编码不在医保三大目录内的项目，比如伙食费、快递费等项目编码传PAXNBL0001
                  "medicareFeeitemName": "'互联网复诊费（二级医院）'" # 医保收费项目名称，社保经办机构三大目录管理规范名称，非空
                  "price": "'0.00'" # 单价，float(8)，非空4位小数
                  "quantity": "'1.00'" # 数量，float(8)，非空4位小数，按照目录库中的包装上传入，非招标按照实际情况传入
                  "money": "'0.00'" # 金额，float(8)，非空4位小数
                  "deptNum": "'50'" # 科室编码，非空字典映射，系统的科室编码
                  "deptName": "'中医科'" # 科室名称，非空系统的科室名称
                  "keepUseFlag": "'1'" # 继用处方标识 1非继用，2继用
                  "selfPayRatio": "'100%'" # 自付比例
    response:
      "infcode": "['package']['additionInfo'][errorCode]"
      "err_msg": "['package']['additionInfo'][errorMsg]"