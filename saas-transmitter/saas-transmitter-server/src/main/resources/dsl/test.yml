name: '人员信息查询'
enable: true
dslType: contract
domain: business.data['network']['networkItem']['medicare']
format: json
functions:
  - path: "'/fsi/api/outpatientDocInfoService/outpatientRegistrationCancel'"
    bodyStrategy: jsonNode
    request:
      method: POST
      body:
        "infno": "'2202'" # 接口号
        "insuplc_admdvs": "business.data['data']['insuredAreaNo']"
        "sign_no": "business.data['aux']['medicareSigninInfo']?.signNo"
        "input":
          "data":
            "psn_no": "business.data['data']['psnNo']"
            "mdtrt_id": "business.data['data']['medicalVisitId']"
            "ipt_otp_no": "business.data['data']['inquiryPref']"
    response:
      # 公共字段
      "infcode": "['infcode']" # 错误代码
      "inf_refmsgid": "['inf_refmsgid']" # 接收方报文ID
      "refmsg_time": "['refmsg_time']" # 接收报文时间
      "respond_time": "['respond_time']" # 响应报文时间
      "err_msg": "['err_msg']" # 错误信息
    # 结果判断配置
    result:
      success: "output.get('infcode') == 0" # 成功条件：infcode为0
      skip: false # 失败时抛出异常
      tips: "'2202挂号撤销失败: ' + output['message']" # 错误提示