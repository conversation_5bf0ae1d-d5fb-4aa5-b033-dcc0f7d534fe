preFilter:
  condition: T(org.apache.commons.lang3.StringUtils).equalsIgnoreCase('520000',data.tenantInfo?.provinceCode)  && T(java.util.List).of(2,3).contains(data.auditorType) && data.inquiryWayType == 2  && T(java.lang.Integer).parseInt(data.patientAge) > 6 && T(org.apache.commons.lang3.StringUtils).isNotBlank(data.idCard)
postParameter:
  nodes:
    - inquiryDetailInfo
    - prescriptionDetail
    - hospitalInfo
    - pharmacistInfo