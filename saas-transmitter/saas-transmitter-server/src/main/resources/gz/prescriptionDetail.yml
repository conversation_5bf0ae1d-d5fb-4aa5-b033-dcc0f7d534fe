dslType: contract
enable: true
name: 处方信息上送
format: json
common: false
domain: business.data['network']['networkItem']['url']
functions:
  - path: "''"
    bodyStrategy: jsonNode
    protocol: http_form_urlencoded
    "suffixedInExpression": T(com.xyy.saas.transmitter.server.util.transmission.drug.RequestSignUtil).guiZhouGenerateSignSource('hnyes','1.0.0',#root,'MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCnyHofgo1J6HcHgGwdG5hkeYSC2vf7F8uh86qRJsUhHgKtlU7r9Wo4K+ZUiPSpcBdBduzXisc7cqyW1Sgu3fnna4oWo0iBK01HEhJBsHMFPgoszlKyem/PJ1M9Me5Z9QEE6VmHXuYBrCSaoPTQ98KtXKKAL+DCXxlRHUQNU2AQI0bp2QjuNniSeEm12jWj8JyJsYNv0K/jDeN37UKeU2f2MwKwKDiX8o8sUWNSpKrbc/K9KnGVlscE6ByX+tz+TJi+VjnksYKP9CASoUyo2GfKIeFLmprzKglPLBZcxQZMMEY70ZHfXtm3SGs2KPs5glJs1nXulx4O8Ex3Du2xISyZAgMBAAECggEAYVYIoycV2H6oLw3tqqVLr15f5ZoqqtyPIhE+Lp8TVbcXTqSD+JeTW6r8IKajaXjBxRS9Kf40vuQ0A/z9JtNYG01j+uYXNfhtCIIPXVSEJdJsDLpVd31YSBFEVQgWgtGEGdlWOxZu0SGQyrP90w0pP3GyRDOCbMDG6yvTQvfPjwwywxrPiRre4aMZrQu/bjN3T3Tc6Cr/btD+neokFAUqHFPkwBYFmjpgak2Wbo93mi8p+mJ6y2Ww4AAqLpKId2vmRYpP0lyFB42ZNSEuhVMYeXT1pK1piLZ6K5I3WPMKFqnH+DksPUsnsjpzPlp8F1Y+28iKm7KMerffhWLwP8IDkQKBgQD9kqGswmByWvI6DRPNIgygp3IxQ5OqVkl5x2AAsrJkYntoK30MiTNFfN5UN7cBNoT61bzTmc229iZx/KFeoxYL79FOrtJCdQeaGZmBXD4om5TrGR5rrarMlhqzrJNZ4z2/KFRHmEiBgOA98+l1QSgoaC7z96/In3EE1TWDE6zjgwKBgQCpY58yWDSq6PI6wFviXfEyrFNwCOcUnshcxOGZTdiQE9MtCPIhM6w6b8QRsJVVjDeURIh9DvxJruvUQQmriXtaWeUB21IEUCBPjKKbxa7rXK1y/p49gTwZq0aIL1ResjvEqKooYQ4Rxd2YeXMVfB7ekIyjoR0XWJMdT8tRw7YIswKBgQC/2SDZRHcUDhsWMK66lPhY1FdhTm96gK42pNHNtWWi7Z/QwJZr1aIlAMR1k3GyST1zSiMDNWqe8r1DoI+2uk0D2v3ROhN6lHCb9qJdbRzaPFv/Q5HwhgSjpS2uovMZHwSojCq3wE+bK5Z9SP1o6VMovypHCy7D613m+ijycwBrYQKBgGXlh97BMO2fNBgjnyKNYIsbHxZGRLwiyGUGCrAuJBiH1IFC4GUYsIJ2uXvqveq4brbCg1i3qwyoLTWB6k9GZO31Jc34IV521PkFoMwUHyV2muCSSp61H3JRlhxWXGqazVmAHNwthkkqviL67EAAapF4YyILyXRB1D3NwoFtOhQBAoGAT06aGfbVfc0CbX95z+voEsAyyX0qJzjQZ8bcQGmA+uNX4Ojoy5fqw+rc/xTFGqHYq6J1W3tVbpbWmv/xzBGgT5hZv4mocyv5auo2Wl/otjJJlZ/n+vCT+Uepjl9sWRwObOfl2gg9xu4o6/mn2o2jBi3e2FUY6UXKbJVPyJY1BBM=')
    request:
      method: POST
      header:
        "Content-type": "'application/x-www-form-urlencoded; charset=utf-8'"
      body:
        "plat":
          "csdjsj": "'2022-02-14'"
          "dypz": "'2555'"
          "dz1": "'四川省'"
          "dz1_code": "'510000'"
          "dz2": "'成都市'"
          "dz2_code": "'510100'"
          "dz3": "'双流区'"
          "dz3_code": "'510116'"
          "ptmc": "'荷叶健康'"
          "ssqymc": "'成都双流宜贰叁互联网医院有限公司'"
          "tyshxydm": "'91510116MA643QRQ3U'"
        "prescriptions":
          "drugs":
            - "dcjl": business.data['aux']['prescriptionDetail'][_index_]['singleDose']
              "gg": business.data['aux']['prescriptionDetail'][_index_]['attributeSpecification']
              "jldw": business.data['aux']['prescriptionDetail'][_index_]['singleUnit']
              "sl": "business.data['aux']['prescriptionDetail'][_index_]['quantity'].intValue()"
              "sypd": business.data['aux']['prescriptionDetail'][_index_]['useFrequency']
              "yf": business.data['aux']['prescriptionDetail'][_index_]['directions']
              "ypbz": business.data['aux']['prescriptionDetail'][_index_]['packageUnit']
              "yptym": business.data['aux']['prescriptionDetail'][_index_]['commonName']
          "ent":
            "dz1": business.data['data']['tenantInfo']?.province
            "dz1_code": business.data['data']['tenantInfo']?.provinceCode
            "dz2": business.data['data']['tenantInfo']?.city
            "dz2_code": business.data['data']['tenantInfo']?.cityCode
            "dz3": business.data['data']['tenantInfo']?.area
            "dz3_code": business.data['data']['tenantInfo']?.areaCode
            "qymc": business.data['data']['tenantInfo']?.businessLicenseName
            "tyshxydm": business.data['data']['tenantInfo']?.businessLicenseNumber
          "prescription":
            "cfbh": business.data['data']['pref']
            "cfdj": 0
            "cffb": "'1'"
            "cflx": "'2'"
            "cfsj": T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(business.data['data']['outPrescriptionTime'])
            "hznl": business.data['data']['patientAge']
            "hzxb": "business.data['data']['patientSex']+''"
            "hzxm": business.data['data']['patientName']
            "id": business.data['data']['id']
            "jgbm": "'WEBH007'"
            "jhfs": "'2'"
            "ksbm":  business.data['data']['deptPref']
            "ksmc": business.data['data']['deptName']
            "scsj": T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(business.data['data']['outPrescriptionTime'])
            "spdz": business.data['aux']['inquiryInfo']['mp4Url']
            "wybs": business.data['data']['pref']
            "yiscaqmddid": business.data['data']['pref']
            "yiscaqmz": business.data['data']['doctorPref']
            "yisxm": business.data['data']['doctorName']
            "yiszyzgzh": business.data['aux']['doctorInfo']['professionalNo']
            "yljg": "'成都双流宜贰叁互联网医院有限公司'"
            "yscaqmsj": T(java.time.format.DateTimeFormatter).ofPattern('yyyy-MM-dd HH:mm:ss').format(business.data['data']['outPrescriptionTime'])
            "zd": "T(java.lang.String).format('病史摘要：%s。诊断：%s',T(org.apache.commons.lang3.StringUtils).join(business.data['data']['mainSuit'],' '),T(org.apache.commons.lang3.StringUtils).join(business.data['data']['diagnosisName']),'/')"
            "zjhm": business.data['data']['idCard']
            "zjlx": "'1'"
    response:
      # 根据实际API响应结构调整
      "success": "['success']"
      "message": "['message']"
      "code": "['code']"



