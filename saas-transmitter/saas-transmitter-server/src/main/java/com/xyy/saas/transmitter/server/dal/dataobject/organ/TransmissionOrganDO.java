package com.xyy.saas.transmitter.server.dal.dataobject.organ;


import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.transmitter.api.organ.dto.TransmissionOrganNetworkConfigDTO;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import java.util.List;

/**
 * 医药行业行政机构 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_transmission_organ", autoResultMap = true)
//@KeySequence("saas_transmission_organ_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransmissionOrganDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）
     */
    private Integer organType;
    /**
     * 名称
     */
    private String name;
    /**
     * 网络配置
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private List<TransmissionOrganNetworkConfigDTO> networkConfig;
    /**
     * 基础配置
     */
    private String basicConfig;
    /**
     * 行政机构logo
     */
    private String logo;
    /**
     * 省份
     */
    private String province;
    /**
     * 城市
     */
    private String city;
    /**
     * 区域
     */
    private String area;
    /**
     * 备注
     */
    private String remark;
    /**
     * 是否禁用
     */
    private Boolean disable;
    /**
     * 省份编码
     */
    private String provinceCode;
    /**
     * 城市编码
     */
    private String cityCode;
    /**
     * 区域编码
     */
    private String areaCode;

}
