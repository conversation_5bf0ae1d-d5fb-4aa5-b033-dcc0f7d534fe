package com.xyy.saas.transmitter.server.controller.admin.config.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 协议配置 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TransmissionConfigItemRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "15565")
    @ExcelProperty("主键ID")
    private Integer id;


    @Schema(description = "接口名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    @ExcelProperty("接口名称")
    private String description;


    @Schema(description = "协议配置包ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "31380")
    @ExcelProperty("协议配置包ID")
    private Integer configPackageId;

    @Schema(description = "父节点id", example = "16154")
    @ExcelProperty("父节点id")
    private Integer parentItemId;

    @Schema(description = "配置类型（1-视图配置、2-逻辑配置、3-协议配置）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("配置类型（1-视图配置、2-逻辑配置、3-协议配置）")
    private Integer dslType;

    @Schema(description = "节点业务类型（比如:1-药监-日结存、2-药监-商品信息...）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("节点业务类型（比如:1-药监-日结存、2-药监-商品信息...）")
    private Integer nodeType;

    @Schema(description = "接口编码", example = "3501")
    @ExcelProperty("接口编码")
    private String apiCode;


    @Schema(description = "配置值,yaml")
    @ExcelProperty("配置值,yaml")
    private String configValue;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否禁用")
    private Boolean disable;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}