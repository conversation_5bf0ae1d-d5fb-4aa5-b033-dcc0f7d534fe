package com.xyy.saas.transmitter.server.service.transmission.processor.internet;

import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionTransmitterDTO;
import com.xyy.saas.transmitter.api.servicepack.dto.TransmissionServicePackDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordSaveReqVO;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.FilterConfig;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.LogicValidationConfig;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.PostParameterConfig;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.PreParameterConfig;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.PreValidationConfig;
import org.springframework.stereotype.Component;

/**
 * 互联网监管-诊疗结算处理器
 */
@Component
public class InternetSupervisionTreatmentSettlementProcessor extends InternetSupervisionLogicConfigProcessor {

    @Override
    public NodeTypeEnum getNodeType() {
        return NodeTypeEnum.INTERNET_SUPERVISION_TREATMENT_SETTLEMENT;
    }

    @Override
    protected void serializeObj(TransmissionReqDTO transmissionReqDTO) {
        PrescriptionTransmitterDTO dataObj = transmissionReqDTO.getDataObj(PrescriptionTransmitterDTO.class);
        transmissionReqDTO.setData(dataObj).setFullName(dataObj.getFullName()).setIdCard(dataObj.getIdCard()).setBusinessNo(dataObj.getBusinessNo());
    }

    @Override
    protected void validateParameters(TransmissionReqDTO transmissionReqDTO, PreValidationConfig config) {
        super.validateParameters(transmissionReqDTO, config);
        // 添加诊疗结算特有的参数验证逻辑
    }

    @Override
    protected void fillPreProcessParameters(TransmissionReqDTO transmissionReqDTO, PreParameterConfig config) {
        super.fillPreProcessParameters(transmissionReqDTO, config);

    }

    @Override
    protected void fillPostProcessParameters(TransmissionReqDTO transmissionReqDTO, PostParameterConfig config) {
        super.fillPostProcessParameters(transmissionReqDTO, config);
        // 添加诊疗结算特有的后置参数处理逻辑
        PrescriptionTransmitterDTO data = (PrescriptionTransmitterDTO) transmissionReqDTO.getData();
        // 填充处方相关数据
        transmissionPrescriptionService.fillPrescriptionParameters(transmissionReqDTO, config, data);
        // 填充操作人信息
        transmissionPrescriptionService.fillOperateUserInfo(transmissionReqDTO.getAux(), config, data.getUserId());
    }

    @Override
    protected boolean preFilter(TransmissionReqDTO transmissionReqDTO, FilterConfig filterConfig) {
        // 添加诊疗结算特有的前置过滤逻辑
        return super.preFilter(transmissionReqDTO, filterConfig);
    }

    @Override
    protected void processLogicConfig(TransmissionReqDTO transmissionReqDTO, TransmissionTaskRecordSaveReqVO task,
        LogicValidationConfig config, TransmissionServicePackDTO servicePack) {
        super.processLogicConfig(transmissionReqDTO, task, config, servicePack);
        // 添加诊疗结算特有的业务处理逻辑
    }
} 