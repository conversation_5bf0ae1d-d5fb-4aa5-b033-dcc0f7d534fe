package com.xyy.saas.transmitter.server.service.transmission.processor.medical;

import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @DateTime: 2025/7/11 15:01
 * @Description: 医保签到处理器
 **/
@Component
public class MedicalSignConfigProcessor extends MedicalLogicConfigProcessor {

    @Override
    public NodeTypeEnum getNodeType() {
        return NodeTypeEnum.MEDICARE_SIGN;
    }
}
