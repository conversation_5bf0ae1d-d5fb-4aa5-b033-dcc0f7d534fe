package com.xyy.saas.transmitter.server.convert.dict;

import cn.iocoder.yudao.module.system.api.dict.dto.DictDataPageReqDTO;
import cn.iocoder.yudao.module.system.api.dict.dto.DictDataRespDTO;
import com.xyy.saas.inquiry.enums.dict.DictMatchEnum;
import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDto;
import com.xyy.saas.transmitter.api.dict.dto.TransmissionOrganDictDTO;
import com.xyy.saas.transmitter.enums.DictTypeConstants;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictExcelVO;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictMatchGetReqVO;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictMatchPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictMatchRespVO;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictMatchSaveReqVO;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictRespVO;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictSaveReqVO;
import com.xyy.saas.transmitter.server.dal.dataobject.dict.TransmissionOrganDictDO;
import com.xyy.saas.transmitter.server.dal.dataobject.dict.TransmissionOrganDictMatchDO;
import com.xyy.saas.transmitter.server.dal.dataobject.organ.TransmissionOrganDO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TransmitterDictConvert {


    TransmitterDictConvert INSTANCE = Mappers.getMapper(TransmitterDictConvert.class);


    default TransmissionOrganDictSaveReqVO convertExcelVO(TransmissionOrganDictExcelVO item, Integer organId, DictDataRespDTO dictData) {
        TransmissionOrganDictSaveReqVO vo = convertExcelVO(item);
        vo.setOrganId(organId);
        vo.setDictType(dictData.getValue());
        vo.setDictName(dictData.getLabel());
        return vo;
    }

    TransmissionOrganDictSaveReqVO convertExcelVO(TransmissionOrganDictExcelVO item);

    TransmissionOrganDictDO convertDo(TransmissionOrganDictSaveReqVO r);

    @Mapping(source = "noDictIds", target = "noExistsIds")
    DictDataPageReqDTO convertPageVo(TransmissionOrganDictMatchPageReqVO pageReqVO);

    default TransmissionOrganDictMatchRespVO convertMatchRespVO(TransmissionOrganDO organ, DictDataRespDTO item, TransmissionOrganDictMatchDO matchDO, TransmissionOrganDictDO organDictDO) {
        TransmissionOrganDictMatchRespVO respVO = new TransmissionOrganDictMatchRespVO();
        respVO.setDictId(item.getId());
        respVO.setDictName(item.getLabel());
        respVO.setDictValue(item.getValue());
        respVO.setDictType(item.getDictType());

        respVO.setOrganName(organ == null ? null : organ.getName());

        respVO.setOrganDictId(organDictDO == null ? null : organDictDO.getId());
        respVO.setOrganDictName(organDictDO == null ? null : organDictDO.getLabel());
        respVO.setOrganDictValue(organDictDO == null ? null : organDictDO.getValue());

        respVO.setOrganId(matchDO == null ? null : matchDO.getOrganId());
        respVO.setStatus(matchDO == null ? DictMatchEnum.UN_MATCH.getCode() : matchDO.getStatus());
        respVO.setCreateTime(matchDO == null ? null : matchDO.getCreateTime());
        respVO.setCreator(matchDO == null ? null : matchDO.getCreator());
        respVO.setUpdater(matchDO == null ? null : matchDO.getUpdater());
        return respVO;
    }

    TransmissionOrganDictMatchDO convertMatchSaveVO(TransmissionOrganDictMatchSaveReqVO createReqVO);

    TransmissionOrganDictRespVO convertVo(TransmissionOrganDictDO d);

    default DictDataRespDTO convertDictDto(InquiryDiagnosisDto diagnosis) {
        DictDataRespDTO dictDataRespDTO = new DictDataRespDTO();
        dictDataRespDTO.setId(diagnosis.getId());
        dictDataRespDTO.setLabel(diagnosis.getShowName());
        dictDataRespDTO.setValue(diagnosis.getDiagnosisCode());
        dictDataRespDTO.setDictType(DictTypeConstants.DIAGNOSIS_DICT);
        return dictDataRespDTO;
    }

    TransmissionOrganDictPageReqVO convertToPageReqVO(TransmissionOrganDictDTO transmissionOrganDictDTO);

    List<TransmissionOrganDictDTO> convertDtos(List<TransmissionOrganDictRespVO> pageResult);

    List<TransmissionOrganDictDTO> convertDo2Dtos(List<TransmissionOrganDictDO> transmissionDict);

    TransmissionOrganDictMatchPageReqVO convertMatchReqVO(TransmissionOrganDictMatchGetReqVO getReqVO);
}
