package com.xyy.saas.transmitter.server.service.transmission.processor.drug;

import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import org.springframework.stereotype.Component;

/**
 * 药店监管默认处理器
 * <p>
 * 作为 DrugLogicConfigProcessor 的默认实现，当没有找到具体的 NodeType 处理器时使用
 * <p>
 * 核心功能：
 * 1. 提供药店监管机构类型的通用处理逻辑
 * 2. 作为回退处理器，处理未定义具体 NodeType 的请求
 * 3. 继承父类的所有通用处理能力
 * <p>
 * 使用场景：
 * 1. 当 LogicConfigProcessorFactory 找不到具体的 NodeType 处理器时
 * 2. 会从 organTypeProcessorMap 中获取此默认实现
 * 3. 确保药店监管类型的请求都有对应的处理器
 */
@Component
public class DrugDefaultLogicConfigProcessor extends DrugLogicConfigProcessor {

    @Override
    public NodeTypeEnum getNodeType() {
        // 返回 null，表示这是一个通用的机构类型处理器
        // 在 LogicConfigProcessorFactory.initProcessors 中会被注册到 organTypeProcessorMap
        return null;
    }
}
