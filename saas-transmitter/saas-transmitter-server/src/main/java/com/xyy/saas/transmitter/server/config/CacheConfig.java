// package com.xyy.saas.transmitter.server.config;
//
// import com.github.benmanes.caffeine.cache.Caffeine;
// import com.github.benmanes.caffeine.cache.Expiry;
// import com.xyy.saas.transmitter.api.transmission.dto.TransmissionTokenRespDTO;
// import org.springframework.cache.CacheManager;
// import org.springframework.cache.annotation.EnableCaching;
// import org.springframework.cache.caffeine.CaffeineCacheManager;
// import org.springframework.context.annotation.Bean;
// import org.springframework.context.annotation.Configuration;
// import org.checkerframework.checker.index.qual.NonNegative;
//
// import java.util.List;
// import java.util.concurrent.TimeUnit;
//
// /**
//  * 缓存配置类
//  * 配置缓存管理器和缓存策略
//  */
// @Configuration
// @EnableCaching
// public class CacheConfig {
//
//     public static final String TOKEN_CACHE_NAME = "transmitter_token";
//     private static final long EXPIRE_TIME_BUFFER = 300000L; // 过期时间缓冲5分钟
//
//     /**
//      * 配置缓存管理器
//      * 使用Caffeine实现,支持动态过期时间
//      *
//      * @return 缓存管理器
//      */
//     @Bean
//     public CacheManager cacheManager() {
//         CaffeineCacheManager cacheManager = new CaffeineCacheManager();
//         cacheManager.setCacheNames(List.of(TOKEN_CACHE_NAME));
//         cacheManager.setCaffeine(Caffeine.newBuilder()
//                 // 初始的缓存空间大小
//                 .initialCapacity(100)
//                 // 缓存的最大条数
//                 .maximumSize(1000)
//                 // 自定义过期策略
//                 .expireAfter(new Expiry<Object, Object>() {
//                     @Override
//                     public long expireAfterCreate(Object key, Object value, long currentTime) {
//                         // 创建后过期时间
//                         if (value instanceof TransmissionTokenRespDTO tokenResp && tokenResp.getExpiresIn() != null) {
//                             long expireTime = tokenResp.getExpiresIn() - EXPIRE_TIME_BUFFER;
//                             // 判断过期时间是否小于0，若小于则返回默认的过期时间（例如10秒）
//                             return TimeUnit.MILLISECONDS.toNanos(Math.max(expireTime, 10000)); // 10秒
//                         }
//                         return TimeUnit.MINUTES.toNanos(5); // 默认5分钟
//                     }
//
//                     @Override
//                     public long expireAfterUpdate(Object key, Object value, long currentTime,
//                             @NonNegative long currentDuration) {
//                         // 更新后保持原有过期时间
//                         return currentDuration;
//                     }
//
//                     @Override
//                     public long expireAfterRead(Object key, Object value, long currentTime,
//                             @NonNegative long currentDuration) {
//                         // 读取后保持原有过期时间
//                         return currentDuration;
//                     }
//                 }));
//         return cacheManager;
//     }
// }