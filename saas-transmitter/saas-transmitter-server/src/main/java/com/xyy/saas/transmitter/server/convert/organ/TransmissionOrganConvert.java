package com.xyy.saas.transmitter.server.convert.organ;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.transmitter.server.controller.admin.organ.vo.TransmissionOrganRespVO;
import com.xyy.saas.transmitter.server.controller.admin.organ.vo.TransmissionOrganSaveReqVO;
import com.xyy.saas.transmitter.server.dal.dataobject.organ.TransmissionOrganDO;
import com.xyy.saas.transmitter.api.organ.dto.TransmissionOrganDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface TransmissionOrganConvert {

    TransmissionOrganConvert INSTANCE = Mappers.getMapper(TransmissionOrganConvert.class);

    /**
     * SaveReqVO 转换为 DO
     */
    TransmissionOrganDO convert(TransmissionOrganSaveReqVO bean);

    TransmissionOrganRespVO convert(TransmissionOrganDO bean);

    List<TransmissionOrganRespVO> convert(List<TransmissionOrganDO> transmissionOrganDOList);

    /**
     * DO 分页转换为 RespVO 分页
     */
    default PageResult<TransmissionOrganRespVO> convertPage(PageResult<TransmissionOrganDO> page) {
        if (page == null) {
            return null;
        }
        PageResult<TransmissionOrganRespVO> result = new PageResult<>();
        result.setTotal(page.getTotal());
        result.setList(convert(page.getList()));
        return result;
    }

    /**
     * DO 转 DTO
     */
    TransmissionOrganDTO convert2DTO(TransmissionOrganDO bean);

    /**
     * DO 列表转 DTO 列表
     */
    List<TransmissionOrganDTO> convert2DTOList(List<TransmissionOrganDO> list);
}
