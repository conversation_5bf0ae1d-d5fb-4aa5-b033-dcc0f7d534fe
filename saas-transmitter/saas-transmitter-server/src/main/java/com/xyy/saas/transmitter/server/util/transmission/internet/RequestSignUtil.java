package com.xyy.saas.transmitter.server.util.transmission.internet;

import cn.hutool.core.util.IdUtil;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.KeyFactory;
import java.security.NoSuchAlgorithmException;
import java.security.PrivateKey;
import java.security.Signature;
import java.security.spec.PKCS8EncodedKeySpec;
import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;

/**
 * @Author:chenxiaoyi
 * @Date:2025/02/24 19:46
 */
@Slf4j
public class RequestSignUtil {

    /**
     * 重庆互联网医院药监Sign签名
     *
     * @param key       key
     * @param secret    密钥
     * @param timestamp 时间戳
     * @param requestId 请求id
     * @return sign
     */
    public static String sign4CqSupervision(String key, String secret, String timestamp, String requuid) {
        try {
            Mac hmacSha256 = Mac.getInstance("HmacSHA256");
            byte[] keyBytes = secret.getBytes(StandardCharsets.UTF_8);
            hmacSha256.init(new SecretKeySpec(keyBytes, 0, keyBytes.length, "HmacSHA256"));
            String textToSign = timestamp + requuid;
            return Base64.encodeBase64String(hmacSha256.doFinal(textToSign.getBytes(StandardCharsets.UTF_8)));
        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            log.error("sign4CqSupervision error,key:{},secret:{},timestamp:{},requestId:{}", key, secret, timestamp, requuid, e);
        }
        return null;
    }


    /**
     * 陕西医签云Sign签名
     * <p>使用各语言对应的 SHA256WithRSA签名函数和医疗机构的医签云API私 钥对待签名字符串进行签名，并对签名值进行 Base64 编码，得到的Base64 编 码值即为 sign 参数的值</p>
     */
    public static String signWithSHA256RSA(String appId, String nonce, String timestamp, String pemPrivateKey) {
        try {
            // 1. 构造待签名字符串
            String data = String.format("appid=%s&nonce=%s&timestamp=%s", appId, nonce, timestamp);

            // 清理PEM格式的标记和空格
            String clearPem = pemPrivateKey.replace("-----BEGIN PRIVATE KEY-----", "")
                .replace("-----END PRIVATE KEY-----", "")
                .replaceAll("\\s+", "");

            // Base64解码私钥
            byte[] decodedKey = java.util.Base64.getDecoder().decode(clearPem);

            // 创建PKCS#8格式密钥规范
            PKCS8EncodedKeySpec keySpec = new PKCS8EncodedKeySpec(decodedKey);

            // 获取RSA密钥工厂
            KeyFactory keyFactory = KeyFactory.getInstance("RSA");
            PrivateKey privateKey = keyFactory.generatePrivate(keySpec);

            // 初始化签名对象
            Signature signer = Signature.getInstance("SHA256withRSA");
            signer.initSign(privateKey);
            signer.update(data.getBytes());

            // 生成签名并Base64编码
            byte[] signatureBytes = signer.sign();
            return java.util.Base64.getEncoder().encodeToString(signatureBytes);
        } catch (Exception e) {
            log.error("signWithSHA256RSA error,appId:{},nonce:{},timestamp:{}", appId, nonce, timestamp, e);
            return "";
        }
    }

    public static void main(String[] args) {

        String nonce = IdUtil.fastSimpleUUID().toLowerCase();

        long timestamp = System.currentTimeMillis();

        String appId = "appId";

        String privateKey = "privateKey";

        // 1. 构造待签名字符串
        String s = signWithSHA256RSA(appId, nonce, timestamp + "", privateKey);

        System.out.println(s);

    }

}
