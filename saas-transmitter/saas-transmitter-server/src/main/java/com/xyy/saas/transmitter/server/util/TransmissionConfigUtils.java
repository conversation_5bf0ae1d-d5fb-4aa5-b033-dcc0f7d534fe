package com.xyy.saas.transmitter.server.util;

import com.aliyun.openservices.shade.org.apache.commons.lang3.StringUtils;
import org.yaml.snakeyaml.DumperOptions;
import org.yaml.snakeyaml.Yaml;
import java.util.*;
import java.util.stream.Collectors;

public class TransmissionConfigUtils {

    private static final Yaml yaml;
    private static final String API_CODE_KEY = "apiCode";
    private static final String FUNCTION_KEY = "function";

    /**
     * 用于标识列表项的合并键 如果列表项中包含此键，则使用此键的值作为唯一标识进行合并 如果列表项中不包含此键，则直接覆盖整个列表
     */
    private static final String LIST_INDEX = "index";

    static {
        DumperOptions options = new DumperOptions();
        options.setDefaultFlowStyle(DumperOptions.FlowStyle.BLOCK);
        yaml = new Yaml(options);
    }

    public static String mapToYaml(Map<String, Object> yamlMap) {
        return yaml.dump(yamlMap);
    }

    /**
     * 递归合并所有父配置
     *
     * @param currentConfig 当前配置
     * @param parentConfigs 所有父配置的列表，按层级顺序排列（从直接父配置到最上层父配置）
     * @return 合并后的配置Map
     */
    public static Map<String, Object> mergeYamlWithParents(String currentConfig, List<String> parentConfigs) {
        if (isBlank(currentConfig) && (parentConfigs == null || parentConfigs.isEmpty())) {
            return Collections.emptyMap();
        }

        // 从最上层父配置开始合并
        Map<String, Object> result = new LinkedHashMap<>();
        if (parentConfigs != null) {
            for (int i = parentConfigs.size() - 1; i >= 0; i--) {
                String parentConfig = parentConfigs.get(i);
                if (!isBlank(parentConfig)) {
                    result = mergeMap(loadYaml(parentConfig), result);
                }
            }
        }

        // 最后合并当前配置
        if (!isBlank(currentConfig)) {
            result = mergeMap(loadYaml(currentConfig), result);
        }

        return result;
    }

    public static Map<String, Object> mergeYamlToMap(String childConfig, String parentConfig) {
        return mergeYamlWithParents(childConfig, Collections.singletonList(parentConfig));
    }

    private static Map<String, Object> loadYaml(String config) {
        return isBlank(config) ? new LinkedHashMap<>() : yaml.load(StringUtils.replaceAll(config, "\t", "  "));
    }

    /**
     * 合并两个Map
     */
    @SuppressWarnings("unchecked")
    private static Map<String, Object> mergeMap(Map<String, Object> child, Map<String, Object> parent) {
        Map<String, Object> result = new LinkedHashMap<>(parent);

        for (Map.Entry<String, Object> entry : child.entrySet()) {
            String key = entry.getKey();
            Object childValue = entry.getValue();
            Object parentValue = result.get(key);

            if (childValue instanceof Map && parentValue instanceof Map) {
                // 如果两边都是Map，递归合并
                result.put(key, mergeMap((Map<String, Object>) childValue, (Map<String, Object>) parentValue));
            } else if (childValue instanceof List && parentValue instanceof List) {
                // 如果两边都是List，使用列表合并策略
                result.put(key, mergeLists((List<Object>) childValue, (List<Object>) parentValue));
            } else {
                // 其他情况，子配置直接覆盖父配置
                result.put(key, childValue);
            }
        }

        return result;
    }

    @SuppressWarnings("unchecked")
    private static List<Object> mergeLists(List<Object> childList, List<Object> parentList) {
        // 如果子列表为空，直接返回父列表
        if (childList == null || childList.isEmpty()) {
            return new ArrayList<>(parentList);
        }

        // 如果父列表为空，直接返回子列表
        if (parentList == null || parentList.isEmpty()) {
            return new ArrayList<>(childList);
        }

        // 检查第一个元素是否包含sequence
        if (!(childList.getFirst() instanceof Map) ||
            !((Map<String, Object>) childList.getFirst()).containsKey(LIST_INDEX)) {
            return new ArrayList<>(childList); // 如果不包含sequence，直接用子列表覆盖
        }

        // 构建父列表的查找映射
        Map<String, Map<String, Object>> parentMap = parentList.stream()
            .filter(item -> item instanceof Map)
            .map(item -> (Map<String, Object>) item)
            .filter(itemMap -> itemMap.get(LIST_INDEX) != null)
            .collect(Collectors.toMap(
                itemMap -> String.valueOf(itemMap.get(LIST_INDEX)),
                itemMap -> itemMap,
                (existing, replacement) -> existing));

        // 构建结果列表，初始包含所有父列表项
        List<Object> result = new ArrayList<>(parentList);

        // 处理子列表中的每一项
        for (Object childItem : childList) {
            if (childItem instanceof Map) {
                Map<String, Object> childMap = (Map<String, Object>) childItem;
                String sequence = String.valueOf(childMap.get(LIST_INDEX));

                if (parentMap.containsKey(sequence)) {
                    // 如果父列表中存在相同sequence的项，进行合并
                    Map<String, Object> parentItem = parentMap.get(sequence);
                    Map<String, Object> mergedItem = mergeMap(childMap, parentItem);

                    // 在结果列表中替换对应项
                    int index = result.indexOf(parentItem);
                    if (index >= 0) {
                        result.set(index, mergedItem);
                    }
                } else {
                    // 如果父列表中不存在，添加到结果列表
                    result.add(childItem);
                }
            }
        }

        // 按sequence排序
        result.sort((o1, o2) -> {
            if (!(o1 instanceof Map) || !(o2 instanceof Map)) {
                return 0;
            }
            Map<String, Object> m1 = (Map<String, Object>) o1;
            Map<String, Object> m2 = (Map<String, Object>) o2;
            String s1 = String.valueOf(m1.get(LIST_INDEX));
            String s2 = String.valueOf(m2.get(LIST_INDEX));
            return s1.compareTo(s2);
        });

        return result;
    }

    private static boolean isBlank(String str) {
        return str == null || str.trim().isEmpty();
    }

    /**
     * 从YAML配置中提取所有的apiCode
     *
     * @param yamlConfig YAML配置内容
     * @return 合并后的apiCode字符串，多个code用逗号分隔
     */
    @SuppressWarnings("unchecked")
    public static String extractApiCodes(String yamlConfig) {
        if (isBlank(yamlConfig)) {
            return "";
        }

        Map<String, Object> yamlMap = loadYaml(yamlConfig);
        Set<String> apiCodes = new LinkedHashSet<>(); // 使用LinkedHashSet保持顺序并去重

        // 获取function列表中的apiCode
        Object functionObj = yamlMap.get(FUNCTION_KEY);
        if (functionObj instanceof List) {
            List<Object> functions = (List<Object>) functionObj;
            for (Object func : functions) {
                if (func instanceof Map) {
                    Map<String, Object> funcMap = (Map<String, Object>) func;
                    try {
                        // 设置当前处理的function配置
                        setCurrentFunctionConfig(funcMap);
                        if (funcMap.containsKey(API_CODE_KEY)) {
                            String apiCode = String.valueOf(funcMap.get(API_CODE_KEY));
                            // 处理EL表达式
                            if (apiCode.startsWith("${") && apiCode.endsWith("}")) {
                                apiCode = resolveElExpression(apiCode);
                            }
                            if (!isBlank(apiCode)) {
                                apiCodes.add(apiCode);
                            }
                        }
                    } finally {
                        // 清除当前function配置
                        clearCurrentFunctionConfig();
                    }
                }
            }
        }

        return String.join(",", apiCodes);
    }

    /**
     * 解析EL表达式
     *
     * @param expression EL表达式，格式如 ${infno: ''}
     * @return 解析后的值
     */
    private static String resolveElExpression(String expression) {
        // 去掉开头和结尾的 ${ 和 }
        String content = expression.substring(2, expression.length() - 1).trim();

        // 解析 key 和默认值
        String[] parts = content.split(":");
        String key = parts[0].trim();
        String defaultValue = parts.length > 1 ? parts[1].trim().replace("'", "") : "";

        // 从当前function配置中查找对应的header值
        String value = findValueInHeader(key);
        return value != null ? value : defaultValue;
    }

    /**
     * 从当前function的header中查找对应的值
     *
     * @param key 要查找的key
     * @return 找到的值，如果没找到返回null
     */
    @SuppressWarnings("unchecked")
    private static String findValueInHeader(String key) {
        // 获取当前正在处理的function配置
        Map<String, Object> currentFunction = getCurrentFunctionConfig();
        if (currentFunction != null) {
            Object input = currentFunction.get("input");
            if (input instanceof Map) {
                Object header = ((Map<String, Object>) input).get("header");
                if (header instanceof Map) {
                    Object value = ((Map<String, Object>) header).get(key);
                    if (value != null) {
                        return String.valueOf(value);
                    }
                }
            }
        }
        return null;
    }

    // 用于存储当前正在处理的function配置
    private static final ThreadLocal<Map<String, Object>> currentFunctionConfig = new ThreadLocal<>();

    /**
     * 设置当前正在处理的function配置
     */
    public static void setCurrentFunctionConfig(Map<String, Object> functionConfig) {
        currentFunctionConfig.set(functionConfig);
    }

    /**
     * 获取当前正在处理的function配置
     */
    private static Map<String, Object> getCurrentFunctionConfig() {
        return currentFunctionConfig.get();
    }

    /**
     * 清除当前function配置
     */
    public static void clearCurrentFunctionConfig() {
        currentFunctionConfig.remove();
    }

    /**
     * 从合并后的配置中提取所有的apiCode
     *
     * @param mergedConfig 合并后的配置Map
     * @return 合并后的apiCode字符串，多个code用逗号分隔
     */
    @SuppressWarnings("unchecked")
    public static String extractApiCodes(Map<String, Object> mergedConfig) {
        Set<String> apiCodes = new LinkedHashSet<>();

        // 直接获取顶层apiCode
        if (mergedConfig.containsKey(API_CODE_KEY)) {
            apiCodes.add(String.valueOf(mergedConfig.get(API_CODE_KEY)));
        }

        // 获取function列表中的apiCode
        Object functionObj = mergedConfig.get(FUNCTION_KEY);
        if (functionObj instanceof List) {
            List<Object> functions = (List<Object>) functionObj;
            for (Object func : functions) {
                if (func instanceof Map) {
                    Map<String, Object> funcMap = (Map<String, Object>) func;
                    if (funcMap.containsKey(API_CODE_KEY)) {
                        apiCodes.add(String.valueOf(funcMap.get(API_CODE_KEY)));
                    }
                }
            }
        }

        return String.join(",", apiCodes);
    }
}
