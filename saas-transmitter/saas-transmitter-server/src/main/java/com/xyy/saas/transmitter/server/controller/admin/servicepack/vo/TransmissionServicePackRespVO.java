package com.xyy.saas.transmitter.server.controller.admin.servicepack.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.pojo.BaseDto;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigPackageRespVO;
import com.xyy.saas.transmitter.server.controller.admin.organ.vo.TransmissionOrganRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 服务包 Response VO")
@Data
@ExcelIgnoreUnannotated
@Accessors(chain = true)
public class TransmissionServicePackRespVO extends BaseDto {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21563")
    @ExcelProperty("主键ID")
    private Integer id;

    @Schema(description = "服务包名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("服务包名称")
    private String name;

    @Schema(description = "医药行业行政机构ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12781")
    @ExcelProperty("医药行业行政机构ID")
    private Integer organId;

    @Schema(description = "机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）", example = "1")
    @ExcelProperty("机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）")
    private Integer organType;

    /**
     * {@link BizTypeEnum}
     */
    @Schema(description = "系统业务类型", example = "1")
    @ExcelProperty("系统业务类型")
    private Integer bizType;

    @Schema(description = "省份编码", example = "330000")
    @ExcelProperty("省份编码")
    private String provinceCode;

    @Schema(description = "省份")
    @ExcelProperty("省份")
    private String province;

    @Schema(description = "城市编码", example = "330100")
    @ExcelProperty("城市编码")
    private String cityCode;

    @Schema(description = "城市")
    @ExcelProperty("城市")
    private String city;

    @Schema(description = "区域编码", example = "330106")
    @ExcelProperty("区域编码")
    private String areaCode;

    @Schema(description = "区域")
    @ExcelProperty("区域")
    private String area;

    @Schema(description = "协议配置包id", requiredMode = Schema.RequiredMode.REQUIRED, example = "8583")
    @ExcelProperty("协议配置包id")
    private Integer configPackageId;

    @Schema(description = "动态库资源", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("动态库资源")
    private String dllResource;

    @Schema(description = "医保对账通过数量(机构数)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("医保对账通过数量(机构数)")
    private String dllVersion;

    @Schema(description = "小票模板资源", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("小票模板资源")
    private String ticketResource;

    @Schema(description = "账单模板资源", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("账单模板资源")
    private String billResource;

    @Schema(description = "拓展资源", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("拓展资源")
    private String extResource;

    @Schema(description = "接口文档", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("接口文档")
    private String apiDoc;

    @Schema(description = "版本号（实际存储：2025012209；页面展示：医药行业行政机构名称+服务提供商名称+机构类型名称+日期+小时，比如：陕西省医保局创智医保20250122；）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本号（实际存储：2025012209；页面展示：医药行业行政机构名称+服务提供商名称+机构类型名称+日期+小时，比如：陕西省医保局创智医保20250122；）")
    private Long version;

    @Schema(description = "环境：0-测试；1-灰度；2-上线；", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("环境：0-测试；1-灰度；2-上线；")
    private Integer env;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否禁用")
    private Boolean disable;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "绑定租户数", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("绑定租户数")
    private Long tenantCount;

    @Schema(description = "医保对账通过数量(机构数)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("医保对账通过数量(机构数)")
    private Long billSuccessCount;

    @Schema(description = "机构名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String organName;

    @Schema(description = "医药行业行政机构信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private TransmissionOrganRespVO organ;

    @Schema(description = "配置包名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String configPackageName;

    @Schema(description = "协议配置包信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private TransmissionConfigPackageRespVO configPackage;
}