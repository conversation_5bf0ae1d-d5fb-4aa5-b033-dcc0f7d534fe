package com.xyy.saas.transmitter.server.api.transmission;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.transmitter.api.task.TransmissionTaskApi;
import com.xyy.saas.transmitter.server.service.transmission.TransmissionRetryService;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;


@DubboService
public class TransmissionTaskApiImpl implements TransmissionTaskApi {

    @Resource
    private TransmissionRetryService transmissionRetryService;


    @Override
    public <T> CommonResult<T> retryTask(Long taskId, Class<T> clazz) {
        return transmissionRetryService.retryContractInvokeSync(taskId, clazz);
    }
}
