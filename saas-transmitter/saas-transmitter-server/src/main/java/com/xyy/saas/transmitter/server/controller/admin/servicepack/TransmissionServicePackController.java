package com.xyy.saas.transmitter.server.controller.admin.servicepack;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.transmitter.server.controller.admin.servicepack.vo.TransmissionServicePackPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.servicepack.vo.TransmissionServicePackRespVO;
import com.xyy.saas.transmitter.server.controller.admin.servicepack.vo.TransmissionServicePackSaveReqVO;
import com.xyy.saas.transmitter.server.service.servicepack.TransmissionServicePackService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 服务包")
@RestController
@RequestMapping("/transmitter/transmission-servicePack")
@Validated
public class TransmissionServicePackController {

    @Resource
    private TransmissionServicePackService transmissionServicePackService;

    @PostMapping("/create")
    @Operation(summary = "创建服务包")
    @PreAuthorize("@ss.hasPermission('saas:transmission-servicePack:create')")
    public CommonResult<Integer> createTransmissionServicePack(@Valid @RequestBody TransmissionServicePackSaveReqVO createReqVO) {
        return success(transmissionServicePackService.createTransmissionServicePack(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新服务包")
    @PreAuthorize("@ss.hasPermission('saas:transmission-servicePack:update')")
    public CommonResult<Boolean> updateTransmissionServicePack(@Valid @RequestBody TransmissionServicePackSaveReqVO updateReqVO) {
        transmissionServicePackService.updateTransmissionServicePack(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除服务包")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:transmission-servicepack:delete')")
    public CommonResult<Boolean> deleteTransmissionServicePack(@RequestParam("id") Integer id) {
        transmissionServicePackService.deleteTransmissionServicePack(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得服务包")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:transmission-servicepack:query')")
    public CommonResult<TransmissionServicePackRespVO> getTransmissionServicePack(@RequestParam("id") Integer id) {
        return success(transmissionServicePackService.getTransmissionServicePack(id));
    }

    @GetMapping("/page")
    @Operation(summary = "获得服务包分页")
    @PreAuthorize("@ss.hasPermission('saas:transmission-servicepack:query')")
    public CommonResult<PageResult<TransmissionServicePackRespVO>> getTransmissionServicePackPage(@Valid TransmissionServicePackPageReqVO pageReqVO) {
        return success(transmissionServicePackService.getTransmissionServicePackPage(pageReqVO));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出服务包 Excel")
    @PreAuthorize("@ss.hasPermission('saas:transmission-servicepack:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTransmissionServicePackExcel(@Valid TransmissionServicePackPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TransmissionServicePackRespVO> list = transmissionServicePackService.getTransmissionServicePackPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "服务包.xls", "数据", TransmissionServicePackRespVO.class,
            list);
    }

}