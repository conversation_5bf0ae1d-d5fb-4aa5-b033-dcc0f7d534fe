package com.xyy.saas.transmitter.server.service.config;

import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.transmitter.api.config.dto.TransmissionConfigPackageDTO;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigPackagePageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigPackageRespVO;
import com.xyy.saas.transmitter.server.controller.admin.config.vo.TransmissionConfigPackageSaveReqVO;
import com.xyy.saas.transmitter.server.dal.dataobject.config.TransmissionConfigPackageDO;
import jakarta.validation.*;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import java.util.List;
import java.util.Map;

/**
 * 配置包 Service 接口
 *
 * <AUTHOR>
 */
public interface TransmissionConfigPackageService {

    /**
     * 创建配置包
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createTransmissionConfigPackage(@Valid TransmissionConfigPackageSaveReqVO createReqVO);

    /**
     * 更新配置包
     *
     * @param updateReqVO 更新信息
     */
    void updateTransmissionConfigPackage(@Valid TransmissionConfigPackageSaveReqVO updateReqVO);

    /**
     * 删除配置包
     *
     * @param id 编号
     */
    void deleteTransmissionConfigPackage(Integer id);

    /**
     * 获得配置包
     *
     * @param id 编号
     * @return 协议配置包
     */
    TransmissionConfigPackageDO getTransmissionConfigPackage(Integer id);

    /**
     * 获取服务包配置列表
     *
     * @param ids
     * @return
     */
    List<TransmissionConfigPackageDO> getTransmissionConfigPackages(List<Integer> ids);

    /**
     * 获得配置包分页
     *
     * @param pageReqVO 分页查询
     * @return 协议配置包分页
     */
    PageResult<TransmissionConfigPackageRespVO> getTransmissionConfigPackagePage(TransmissionConfigPackagePageReqVO pageReqVO);

    /**
     * 复制配置包
     *
     * @param id 原配置包ID
     * @return 新配置包ID
     */
    Integer copyTransmissionConfigPackage(Integer id);

    /**
     * 获取完整的配置包Map
     *
     * @param configPackageIds       配置包ID列表
     * @param nodeType               节点类型
     * @param validateProtocolConfig 是否需要验证存在协议配置
     * @return 配置包ID到配置包信息的映射
     */
    Map<Integer, TransmissionConfigPackageDTO> getConfigPackageMap(List<Integer> configPackageIds,
        NodeTypeEnum nodeType, boolean validateProtocolConfig);

}