package com.xyy.saas.transmitter.server.service.transmission.system;

import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContext;
import com.xyy.saas.localserver.medicare.dsl.executor.DSLContextHolder;
import com.xyy.saas.localserver.medicare.dsl.executor.value.DynamicBusiness;
import com.xyy.saas.transmitter.api.servicepack.dto.TransmissionServicePackDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionTokenReqDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionTokenRespDTO;
import com.xyy.saas.transmitter.server.constant.TransmitterConstants;
import com.xyy.saas.transmitter.server.service.servicepack.TransmissionServicePackService;
import com.xyy.saas.transmitter.server.service.transmission.TransmissionService;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.aop.framework.AopContext;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

/**
 * Token服务类
 * <p>
 * 功能： 1. 获取和缓存Token 2. 支持Token刷新和自动过期 3. 基于Spring Cache实现分布式Token管理
 * <p>
 * 使用场景： 1. 在需要访问外部服务时获取Token 2. 支持Token的自动刷新和缓存 3. 多节点共享Token信息
 *
 * <AUTHOR>
 */
@Service("tokenService")
@Slf4j
@EnableAspectJAutoProxy(exposeProxy = true)
public class TokenService {

    @Resource
    private TransmissionService transmissionService;

    @Resource
    private TransmissionServicePackService servicePackService;

    /**
     * 获取Token 入口方法,处理请求参数并调用核心Token获取逻辑
     *
     * @param map 请求参数 {@link TransmissionTokenReqDTO}
     * @return Token响应对象
     */
    public TransmissionTokenRespDTO getToken(Map<String, Object> map) {
        // 1. 解析请求参数
        TransmissionTokenReqDTO tokenReqDTO = Optional.ofNullable(JsonUtils.parseObject(JsonUtils.toJsonString(map),
            TransmissionTokenReqDTO.class)).orElse(new TransmissionTokenReqDTO());

        // 2. 获取业务上下文
        DSLContext context = DSLContextHolder.getContext();
        DynamicBusiness business = context.getBusiness();

        // 构建缓存key
        String cacheKey = String.format("token:%d:%d", business.getTenantId(), business.getServicePackId());

        // 3. 从缓存获取Token,如果不存在则远程调用获取
        TokenService proxy = (TokenService) AopContext.currentProxy();
        if (tokenReqDTO.isRefresh()) {
            proxy.getTokenRefresh(cacheKey);
        }
        return proxy.getTokenWithCache(cacheKey, business);
    }

    @Cacheable(cacheNames = TransmitterConstants.CACHE_PREFIX + "#5m", key = "#cacheKey", unless = "#result == null")
    public TransmissionTokenRespDTO getTokenWithCache(String cacheKey, DynamicBusiness business) {
        // 1. 获取服务包配置
        TransmissionServicePackDTO servicePack = getServicePack(business);
        if (servicePack == null) {
            return null;
        }

        // 2. 远程调用获取新Token
        return transmissionService.executeRemoteCall(
            business.getTenantId(),
            servicePack,
            business.getData(),
            TransmissionTokenRespDTO.class);
    }

    @CacheEvict(cacheNames = TransmitterConstants.CACHE_PREFIX, key = "#cacheKey")
    public void getTokenRefresh(String cacheKey) {

    }

    /**
     * 获取服务包配置
     */
    private TransmissionServicePackDTO getServicePack(DynamicBusiness business) {
        List<TransmissionServicePackDTO> servicePacks = servicePackService.queryTenantServicePackByNode(
            business.getTenantId(),
            business.getOrganType(),
            NodeTypeEnum.GET_TOKEN,
            business.getServicePackId() == null ? null : List.of(business.getServicePackId()),
            true);

        if (CollectionUtils.isEmpty(servicePacks)) {
            log.warn("未找到服务包配置, tenantId:{}, servicePackId:{}",
                business.getTenantId(), business.getServicePackId());
            return null;
        }

        TransmissionServicePackDTO servicePack = servicePacks.getFirst();
        if (CollectionUtils.isEmpty(servicePack.getConfigPackage().getConfigItems())) {
            log.warn("服务包配置项为空, tenantId:{}, servicePackId:{}",
                business.getTenantId(), business.getServicePackId());
            return null;
        }

        return servicePack;
    }
}
