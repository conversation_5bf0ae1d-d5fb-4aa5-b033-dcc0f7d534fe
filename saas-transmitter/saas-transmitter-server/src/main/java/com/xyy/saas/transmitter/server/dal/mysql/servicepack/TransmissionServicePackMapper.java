package com.xyy.saas.transmitter.server.dal.mysql.servicepack;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xyy.saas.transmitter.server.controller.admin.servicepack.vo.TransmissionServicePackPageReqVO;
import com.xyy.saas.transmitter.server.dal.dataobject.servicepack.TransmissionServicePackDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 服务包 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TransmissionServicePackMapper extends BaseMapperX<TransmissionServicePackDO> {


    IPage<TransmissionServicePackDO> selectTransmissionServicePackPage(Page<TransmissionServicePackPageReqVO> objectPage, TransmissionServicePackPageReqVO reqVO);


    /**
     * 构建查询条件
     */
    default LambdaQueryWrapperX<TransmissionServicePackDO> buildQueryWrapper(TransmissionServicePackPageReqVO reqVO) {
        return new LambdaQueryWrapperX<TransmissionServicePackDO>()
            .likeIfPresent(TransmissionServicePackDO::getName, reqVO.getName())
            .inIfPresent(TransmissionServicePackDO::getId, reqVO.getIds())
            .eqIfPresent(TransmissionServicePackDO::getOrganId, reqVO.getOrganId())
            .eqIfPresent(TransmissionServicePackDO::getOrganType, reqVO.getOrganType())
            .eqIfPresent(TransmissionServicePackDO::getConfigPackageId, reqVO.getConfigPackageId())
            .eqIfPresent(TransmissionServicePackDO::getDllResource, reqVO.getDllResource())
            .eqIfPresent(TransmissionServicePackDO::getTicketResource, reqVO.getTicketResource())
            .eqIfPresent(TransmissionServicePackDO::getBillResource, reqVO.getBillResource())
            .eqIfPresent(TransmissionServicePackDO::getExtResource, reqVO.getExtResource())
            .eqIfPresent(TransmissionServicePackDO::getApiDoc, reqVO.getApiDoc())
            .eqIfPresent(TransmissionServicePackDO::getVersion, reqVO.getVersion())
            .eqIfPresent(TransmissionServicePackDO::getEnv, reqVO.getEnv())
            .eqIfPresent(TransmissionServicePackDO::getDisable, reqVO.getDisable())
            .betweenIfPresent(TransmissionServicePackDO::getCreateTime, reqVO.getCreateTime())
            // .eqIfPresent(TransmissionServicePackDO::getProvinceCode, reqVO.getProvinceCode())
            // .eqIfPresent(TransmissionServicePackDO::getProvince, reqVO.getProvince())
            // .eqIfPresent(TransmissionServicePackDO::getCityCode, reqVO.getCityCode())
            // .eqIfPresent(TransmissionServicePackDO::getCity, reqVO.getCity())
            // .eqIfPresent(TransmissionServicePackDO::getAreaCode, reqVO.getAreaCode())
            // .eqIfPresent(TransmissionServicePackDO::getArea, reqVO.getArea())
            .orderByDesc(TransmissionServicePackDO::getId);
    }


    /**
     * 列表查询
     */
    default List<TransmissionServicePackDO> selectList(TransmissionServicePackPageReqVO reqVO) {
        return selectList(buildQueryWrapper(reqVO));
    }

    default TransmissionServicePackDO selectOneByCondition(TransmissionServicePackPageReqVO reqVO) {
        return selectOne(buildQueryWrapper(reqVO), false);
    }

    /**
     * 根据名称和版本查询服务包
     *
     * @param name    服务包名称
     * @param version 服务包版本
     * @return 服务包对象
     */
    default TransmissionServicePackDO selectByNameAndVersion(String name, Long version) {
        return selectOne(TransmissionServicePackDO::getName, name, TransmissionServicePackDO::getVersion, version);
    }

    default Long selectCountServicePack(TransmissionServicePackPageReqVO reqVO) {
        return selectCount(buildQueryWrapper(reqVO));
    }
}