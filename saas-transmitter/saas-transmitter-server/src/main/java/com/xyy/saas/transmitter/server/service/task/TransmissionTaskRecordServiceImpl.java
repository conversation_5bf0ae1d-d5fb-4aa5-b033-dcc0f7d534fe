package com.xyy.saas.transmitter.server.service.task;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.transmitter.enums.ErrorCodeConstants.TENANT_TRANSMISSION_TASK_RECORD_NOT_EXISTS;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.enums.transmitter.RequestStatusEnum;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.transmitter.api.organ.dto.TransmissionOrganDTO;
import com.xyy.saas.transmitter.api.servicepack.dto.TransmissionServicePackDTO;
import com.xyy.saas.transmitter.server.controller.admin.organ.vo.TransmissionOrganPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.servicepack.vo.TransmissionServicePackPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordRespVO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordSaveReqVO;
import com.xyy.saas.transmitter.server.convert.organ.TransmissionOrganConvert;
import com.xyy.saas.transmitter.server.convert.servicepack.TransmissionServicePackConvert;
import com.xyy.saas.transmitter.server.convert.task.TransmissionTaskRecordConvert;
import com.xyy.saas.transmitter.server.dal.dataobject.task.TransmissionTaskRecordDO;
import com.xyy.saas.transmitter.server.dal.mysql.organ.TransmissionOrganMapper;
import com.xyy.saas.transmitter.server.dal.mysql.servicepack.TransmissionServicePackMapper;
import com.xyy.saas.transmitter.server.dal.mysql.task.TransmissionTaskRecordMapper;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 数据传输-记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TransmissionTaskRecordServiceImpl implements TransmissionTaskRecordService {

    @Resource
    private TransmissionTaskRecordMapper taskRecordMapper;

    @Resource
    private TransmissionServicePackMapper servicePackMapper;

    @Resource
    private TransmissionOrganMapper organMapper;

    @Resource
    private TenantApi tenantApi;


    /**
     * 创建或更新任务及其下游任务
     * <p>
     * 处理逻辑: 1. 主任务处理: - 不存在则创建新任务 - 存在则更新任务 2. 下游任务处理: - 存在则批量更新参数和状态 - 不存在则批量创建新任务
     *
     * @param mainTask        主任务信息，包含任务基本信息和业务参数
     * @param downstreamTasks 下游任务列表，包含需要处理的子任务信息
     */
    @Transactional(rollbackFor = Exception.class)
    public void createOrUpdateTask(TransmissionTaskRecordSaveReqVO mainTask,
        List<TransmissionTaskRecordSaveReqVO> downstreamTasks) {
        // 1. 处理主任务 - 根据ID判断是创建还是更新
        if (mainTask.getId() == null) {
            // 1.1 创建主任务 - 转换并插入数据
            TransmissionTaskRecordDO mainTaskDO = TransmissionTaskRecordConvert.INSTANCE.convert(mainTask);
            taskRecordMapper.insert(mainTaskDO);
            mainTask.setId(mainTaskDO.getId());
        } else {
            // 1.2 更新主任务 - 转换并更新数据
            taskRecordMapper.updateById(TransmissionTaskRecordConvert.INSTANCE.convert(mainTask));
        }

        // 2. 处理下游任务 - 存在则更新，不存在则创建
        if (CollectionUtils.isNotEmpty(downstreamTasks)) {
            // 2.1 查询现有下游任务
            List<TransmissionTaskRecordDO> downstreamTaskRecordDOs = taskRecordMapper.selectList(
                TransmissionTaskRecordPageReqVO.builder()
                    .upstreamTaskId(mainTask.getId())
                    .build());

            if (CollectionUtils.isNotEmpty(downstreamTaskRecordDOs)) {
                // 2.2 更新现有下游任务 - 更新参数和状态
                downstreamTaskRecordDOs.forEach(task -> {
                    task.setOriginalParams(mainTask.getOriginalParams())
                        .setRequestStatus(RequestStatusEnum.NOT_REQUESTED.getCode());
                });
                taskRecordMapper.updateBatch(downstreamTaskRecordDOs);
            } else {
                // 2.3 创建新下游任务 - 设置上游任务ID并批量创建
                downstreamTaskRecordDOs = downstreamTasks.stream()
                    .map(task -> {
                        task.setUpstreamTaskId(mainTask.getId());
                        return TransmissionTaskRecordConvert.INSTANCE.convert(task);
                    })
                    .toList();
                taskRecordMapper.insertBatch(downstreamTaskRecordDOs);
            }

            // 2.4 更新入参的下游任务列表 - 保持数据一致性
            downstreamTasks.clear();
            downstreamTasks.addAll(TransmissionTaskRecordConvert.INSTANCE.convertSaveReqVOList(downstreamTaskRecordDOs));
        }
    }

    @Override
    public Long createTransmissionTaskRecord(TransmissionTaskRecordSaveReqVO createReqVO) {
        // 转换为DO对象
        TransmissionTaskRecordDO taskRecord = TransmissionTaskRecordConvert.INSTANCE.convert(createReqVO);

        // 设置默认请求状态
        // taskRecord.setRequestStatus(RequestStatusEnum.NOT_REQUESTED.getCode());

        // 插入
        taskRecordMapper.insert(taskRecord);
        return taskRecord.getId();
    }

    @Override
    public void updateTransmissionTaskRecord(TransmissionTaskRecordSaveReqVO updateReqVO) {

        // 转换为DO对象
        TransmissionTaskRecordDO taskRecord = TransmissionTaskRecordConvert.INSTANCE.convert(updateReqVO);

        // 更新
        taskRecordMapper.updateById(taskRecord);
    }

    @Override
    public void deleteTransmissionTaskRecord(Long id) {
        // 删除
        taskRecordMapper.deleteById(id);
    }


    @Override
    public TransmissionTaskRecordRespVO getTransmissionTaskRecord(Long id) {
        // 查询记录
        TransmissionTaskRecordDO taskRecordDO = taskRecordMapper.selectById(id);
        if (taskRecordDO == null) {
            throw exception(TENANT_TRANSMISSION_TASK_RECORD_NOT_EXISTS);
        }

        // 转换为响应对象
        TransmissionTaskRecordRespVO taskRecord = TransmissionTaskRecordConvert.INSTANCE.convert(taskRecordDO);

        // 批量查询相关信息
        Map<Long, TenantDto> tenantMap = getTenantMap(Collections.singletonList(taskRecord.getTenantId()));
        Map<Integer, TransmissionServicePackDTO> servicePackMap = getServicePackMap(Collections.singletonList(taskRecord.getServicePackId()));
        Map<Integer, TransmissionOrganDTO> organMap = getOrganMap(Collections.singletonList(taskRecord.getOrganId()));

        // 填充信息
        TransmissionTaskRecordConvert.INSTANCE.fillTaskRecordDetails(taskRecord, tenantMap, servicePackMap, organMap);

        return taskRecord;
    }

    // 辅助方法：获取租户信息映射
    private Map<Long, TenantDto> getTenantMap(List<Long> tenantIds) {
        return tenantApi.getTenantList(tenantIds).stream()
            .collect(Collectors.toMap(TenantDto::getId, tenantDto -> tenantDto));
    }

    // 辅助方法：获取服务包信息映射
    private Map<Integer, TransmissionServicePackDTO> getServicePackMap(List<Integer> servicePackIds) {
        return TransmissionServicePackConvert.INSTANCE.convert2DTOList(
                servicePackMapper.selectList(TransmissionServicePackPageReqVO.builder().ids(servicePackIds).build()))
            .stream().collect(Collectors.toMap(TransmissionServicePackDTO::getId, servicePackDTO -> servicePackDTO));
    }

    // 辅助方法：获取机构信息映射
    private Map<Integer, TransmissionOrganDTO> getOrganMap(List<Integer> organIds) {
        return TransmissionOrganConvert.INSTANCE.convert2DTOList(
                organMapper.selectList(TransmissionOrganPageReqVO.builder().ids(organIds).build()))
            .stream().collect(Collectors.toMap(TransmissionOrganDTO::getId, organDTO -> organDTO));
    }

    @Override
    public PageResult<TransmissionTaskRecordRespVO> getTransmissionTaskRecordPage(TransmissionTaskRecordPageReqVO pageReqVO) {
        // 获取分页结果
        PageResult<TransmissionTaskRecordRespVO> pageResult = TransmissionTaskRecordConvert.INSTANCE.convertPage(taskRecordMapper.selectPage(pageReqVO));

        // 批量查询相关信息
        List<Long> tenantIds = pageResult.getList().stream().map(TransmissionTaskRecordRespVO::getTenantId).distinct().collect(Collectors.toList());
        List<Integer> servicePackIds = pageResult.getList().stream().map(TransmissionTaskRecordRespVO::getServicePackId).distinct().collect(Collectors.toList());
        List<Integer> organIds = pageResult.getList().stream().map(TransmissionTaskRecordRespVO::getOrganId).distinct().collect(Collectors.toList());

        Map<Long, TenantDto> tenantMap = getTenantMap(tenantIds);
        Map<Integer, TransmissionServicePackDTO> servicePackMap = getServicePackMap(servicePackIds);
        Map<Integer, TransmissionOrganDTO> organMap = getOrganMap(organIds);

        // 填充信息
        pageResult.getList().forEach(item -> TransmissionTaskRecordConvert.INSTANCE.fillTaskRecordDetails(item, tenantMap, servicePackMap, organMap));

        return pageResult;
    }

    @Override
    public PageResult<TransmissionTaskRecordRespVO> getTaskRecordPage(TransmissionTaskRecordPageReqVO pageReqVO) {
        return TransmissionTaskRecordConvert.INSTANCE.convertPage(taskRecordMapper.selectPage(pageReqVO));
    }

    @Override
    public List<TransmissionTaskRecordRespVO> getTransmissionTaskRecordList(TransmissionTaskRecordPageReqVO reqVO) {
        // 查询任务记录
        List<TransmissionTaskRecordDO> list = taskRecordMapper.selectList(reqVO);
        // 转换并返回
        return TransmissionTaskRecordConvert.INSTANCE.convertList(list);
    }

}