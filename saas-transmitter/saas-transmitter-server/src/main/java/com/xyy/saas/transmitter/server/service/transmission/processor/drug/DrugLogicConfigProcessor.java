package com.xyy.saas.transmitter.server.service.transmission.processor.drug;

import com.xyy.saas.inquiry.enums.transmitter.OrganTypeEnum;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor;
import com.xyy.saas.transmitter.server.service.transmission.processor.TransmissionPrescriptionService;
import jakarta.annotation.Resource;

/**
 * 药店监管基础处理器 提供 药店监管通用的处理逻辑
 * <p>
 * 核心功能： 1. 通用参数处理 2. 基础校验逻辑 3. 药店监管特定配置处理
 * <p>
 * 注意：此抽象类不再标注 @Component，具体的默认实现由 DrugDefaultLogicConfigProcessor 提供
 */
public abstract class DrugLogicConfigProcessor extends LogicConfigProcessor {

    @Override
    public OrganTypeEnum getOrganType() {
        return OrganTypeEnum.DRUG_SUPERVISION;
    }

    @Resource
    protected TransmissionPrescriptionService transmissionPrescriptionService;
}