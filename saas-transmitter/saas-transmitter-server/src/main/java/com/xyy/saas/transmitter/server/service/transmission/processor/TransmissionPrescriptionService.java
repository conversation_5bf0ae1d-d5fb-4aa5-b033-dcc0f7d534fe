package com.xyy.saas.transmitter.server.service.transmission.processor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.DoctorTypeEnum;
import com.xyy.saas.inquiry.enums.signature.ContractTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.user.CertificateTypeEnum;
import com.xyy.saas.inquiry.hospital.api.clinicalcase.InquiryClinicalCaseApi;
import com.xyy.saas.inquiry.hospital.api.clinicalcase.dto.InquiryClinicalCaseRespDto;
import com.xyy.saas.inquiry.hospital.api.doctor.InquiryDoctorApi;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryHospitalDeptDoctorDto;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.identification.InquiryProfessionIdentificationDto;
import com.xyy.saas.inquiry.hospital.api.doctor.indentification.InquiryProfessionIdentificationApi;
import com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalApi;
import com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalDeptDoctorRelationApi;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalReqDto;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.hospital.api.medicare.MedicalRegistrationApi;
import com.xyy.saas.inquiry.hospital.api.medicare.MedicareBaseApi;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionApi;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionDetailApi;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionDetailRespDTO;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.patient.api.medical.InquiryMedicareApi;
import com.xyy.saas.inquiry.pharmacist.api.audit.InquiryPrescriptionAuditApi;
import com.xyy.saas.inquiry.pharmacist.api.pharmacist.InquiryPharmacistApi;
import com.xyy.saas.inquiry.pharmacist.api.pharmacist.dto.InquiryPharmacistDto;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionTransmitterDTO;
import com.xyy.saas.inquiry.product.api.ProductStdlibApi;
import com.xyy.saas.inquiry.product.api.catalog.MedicalCatalogDetailApi;
import com.xyy.saas.inquiry.product.api.catalog.RegulatoryCatalogDetailApi;
import com.xyy.saas.inquiry.product.api.catalog.dto.MedicalCatalogDetailDTO;
import com.xyy.saas.inquiry.product.api.catalog.dto.RegulatoryCatalogDetailDTO;
import com.xyy.saas.inquiry.product.api.product.dto.ProductStdlibDto;
import com.xyy.saas.inquiry.product.api.product.dto.StdlibProductSearchDto;
import com.xyy.saas.inquiry.signature.api.signature.InquirySignatureContractApi;
import com.xyy.saas.inquiry.signature.dto.signature.ElectSignInfoDto;
import com.xyy.saas.inquiry.util.UrlConUtil;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import com.xyy.saas.transmitter.server.constant.InternetAuxConstants;
import com.xyy.saas.transmitter.server.constant.MedicalAuxConstants;
import com.xyy.saas.transmitter.server.convert.transmission.TransmissionInternetConvert;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.PostParameterConfig;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @Author:chenxiaoyi
 * @Date:2025/07/17 9:35
 */
@Component
@Slf4j
public class TransmissionPrescriptionService {


    @DubboReference
    private InquiryDoctorApi inquiryDoctorApi;

    @DubboReference
    private InquiryPharmacistApi inquiryPharmacistApi;

    @DubboReference
    private InquiryProfessionIdentificationApi inquiryProfessionIdentificationApi;

    @DubboReference
    private InquiryPrescriptionApi inquiryPrescriptionApi;

    @DubboReference
    private InquiryPrescriptionDetailApi inquiryPrescriptionDetailApi;

    @DubboReference
    private InquiryClinicalCaseApi inquiryClinicalCaseApi;

    @DubboReference
    private MedicalRegistrationApi medicalRegistrationApi;

    @DubboReference
    private InquirySignatureContractApi inquirySignatureContractApi;

    @DubboReference
    private InquiryApi inquiryApi;

    @Resource
    private AdminUserApi adminUserApi;

    @DubboReference
    private ProductStdlibApi productStdlibApi;

    @DubboReference
    private RegulatoryCatalogDetailApi regulatoryCatalogDetailApi;
    @DubboReference
    private MedicalCatalogDetailApi medicalCatalogDetailApi;

    @DubboReference
    private InquiryHospitalDeptDoctorRelationApi inquiryHospitalDeptDoctorRelationApi;

    @DubboReference
    private InquiryPrescriptionAuditApi inquiryPrescriptionAuditApi;

    @DubboReference
    private MedicareBaseApi medicareBaseApi;

    @DubboReference
    private InquiryMedicareApi inquiryMedicareApi;

    @DubboReference
    private TenantApi tenantApi;

    @DubboReference
    private InquiryHospitalApi hospitalApi;

    /**
     * 填充医生 药师信息+职称
     *
     * @param aux            辅助信息映射，用于存储医生或药师的信息
     * @param config         配置对象，包含预参数配置
     * @param doctorPref     医生的偏好字符串
     * @param pharmacistPref 药师的偏好字符串
     */
    public void fillDoctorPharmacistInfo(Map<String, Object> aux, LogicConfig.PostParameterConfig config, String doctorPref, String pharmacistPref, String hospitalPref) {
        if (CollUtil.contains(config.getNodes(), InternetAuxConstants.DOCTOR_INFO)) {
            if (StringUtils.isNotBlank(doctorPref)) {
                aux.put(InternetAuxConstants.DOCTOR_INFO, inquiryDoctorApi.getInquiryDoctorSupervision(doctorPref, hospitalPref));
            }
        }

        if (CollUtil.contains(config.getNodes(), InternetAuxConstants.PHARMACIST_INFO)) {
            if (StringUtils.isNotBlank(pharmacistPref)) {
                InquiryPharmacistDto pharmacist = inquiryPharmacistApi.getPharmacistByPref(pharmacistPref);

                List<InquiryHospitalDeptDoctorDto> hospitalPharmacist = inquiryHospitalDeptDoctorRelationApi.getHospitalDeptDoctorByPref(pharmacistPref, hospitalPref, DoctorTypeEnum.PHARMACIST);
                if (CollUtil.isNotEmpty(hospitalPharmacist)) {
                    pharmacist.setPharmacistHospitalPref(hospitalPharmacist.getFirst().getDoctorHospitalPref());
                    pharmacist.setPharmacistHospitalDeptPref(hospitalPharmacist.getFirst().getDeptPref());
                    pharmacist.setPharmacistHospitalDeptName(hospitalPharmacist.getFirst().getDeptName());
                }
                // 查资质证件信息
                List<InquiryProfessionIdentificationDto> identifications = inquiryProfessionIdentificationApi.getProfessionIdentifications(pharmacist.getId(), DoctorTypeEnum.PHARMACIST);
                Optional.ofNullable(identifications).orElse(List.of()).stream().filter(i -> Objects.equals(i.getCertificateType(), CertificateTypeEnum.ZCZ.getType())).findAny()
                    .ifPresent(t -> pharmacist.setRegistrationNo(t.getCertificateNo()));

                aux.put(InternetAuxConstants.PHARMACIST_INFO, pharmacist);
            }
        }

    }

    /**
     * 填充挂号信息
     *
     * @param aux         辅助数据Map，用于存储挂号信息
     * @param config      逻辑配置对象，包含需要的配置信息
     * @param bizTypeEnum 业务类型枚举，表示当前业务的类型
     * @param inquiryPref 问诊编号
     */
    public void fillMedicalRegistrationInfo(Map<String, Object> aux, LogicConfig.PostParameterConfig config, BizTypeEnum bizTypeEnum, String inquiryPref) {
        if (CollUtil.contains(config.getNodes(), InternetAuxConstants.MEDICAL_REGISTRATION_INFO)) {
            aux.put(InternetAuxConstants.MEDICAL_REGISTRATION_INFO, medicalRegistrationApi.getMedicalRegistrationInfo(bizTypeEnum, inquiryPref));
        }
    }

    /**
     * 填充处方详情信息
     *
     * @param aux              辅助数据Map，用于存储填充的处方详情信息
     * @param nodes            需要填充的信息节点配置
     * @param prescriptionPref 处方编号
     */
    public void fillPrescriptionDetailInfo(Map<String, Object> aux, List<String> nodes, String prescriptionPref) {
        if (CollUtil.contains(nodes, InternetAuxConstants.PRESCRIPTION_DETAIL)) {
            List<InquiryPrescriptionDetailRespDTO> prescriptionDetailList
                = inquiryPrescriptionDetailApi.getPrescriptionDetail(prescriptionPref);

            // 填充转换标准库商品字段 eg:剂型
            if (CollUtil.contains(nodes, InternetAuxConstants.STDLIB_PRODUCT)) {
                fillStdProductInfo(prescriptionDetailList);
            }

            // 填充监管目录 - 编码
            if (CollUtil.contains(nodes, InternetAuxConstants.REGULATORY_CATALOG)) {
                fillRegulatoryCatalog(prescriptionDetailList);
            }

            // 填充医保目录
            if (CollUtil.contains(nodes, InternetAuxConstants.MEDICAL_CATALOG)) {
                fillMedicalCatalog(prescriptionDetailList);
            }

            aux.put(InternetAuxConstants.PRESCRIPTION_DETAIL, prescriptionDetailList);
        }
    }

    /**
     * 填充标准库商品信息
     *
     * @param prescriptionDetail
     */
    private void fillStdProductInfo(List<InquiryPrescriptionDetailRespDTO> prescriptionDetail) {
        if (CollUtil.isEmpty(prescriptionDetail)) {
            return;
        }
        StdlibProductSearchDto searchDto = new StdlibProductSearchDto();
        searchDto.setMidStdlibIdList(prescriptionDetail.stream().map(s -> NumberUtil.parseLong(s.getStandardId(), 0L)).toList());
        Map<Long, ProductStdlibDto> stdlibDtoMap = productStdlibApi.searchProductStdlibList(searchDto, searchDto.getMidStdlibIdList().size()).stream()
            .collect(Collectors.toMap(ProductStdlibDto::getMidStdlibId, Function.identity(), (a, b) -> b));
        for (InquiryPrescriptionDetailRespDTO dto : prescriptionDetail) {
            if (stdlibDtoMap.get(NumberUtil.parseLong(dto.getStandardId(), 0L)) != null) {
                dto.setDosageForm(stdlibDtoMap.get(NumberUtil.parseLong(dto.getStandardId(), 0L)).getDosageForm());
                dto.setPresCategory(stdlibDtoMap.get(NumberUtil.parseLong(dto.getStandardId(), 0L)).getPresCategory());
            }
        }
    }

    /**
     * 填充监管目录 - 编码
     *
     * @param prescriptionDetail
     */
    private void fillRegulatoryCatalog(List<InquiryPrescriptionDetailRespDTO> prescriptionDetail) {
        if (CollUtil.isEmpty(prescriptionDetail)) {
            return;
        }
        Map<Long, RegulatoryCatalogDetailDTO> catalogDetailMap = regulatoryCatalogDetailApi.getCatalogDetailMapByProjectCodes(prescriptionDetail.getFirst().getTenantId(), prescriptionDetail
            .stream().map(s -> NumberUtil.parseLong(s.getStandardId(), 0L)).toList());

        for (InquiryPrescriptionDetailRespDTO dto : prescriptionDetail) {
            if (catalogDetailMap.get(NumberUtil.parseLong(dto.getStandardId(), 0L)) != null) {
                // dto.setMedicalCatalogCode(catalogDetailMap.get(NumberUtil.parseLong(dto.getStandardId(), 0L)).getMedicalCatalogCode());
            }
        }
    }

    /**
     * 填充医保目录 - 编码
     *
     * @param prescriptionDetail
     */
    private void fillMedicalCatalog(List<InquiryPrescriptionDetailRespDTO> prescriptionDetail) {
        if (CollUtil.isEmpty(prescriptionDetail)) {
            return;
        }
        Map<String, MedicalCatalogDetailDTO> catalogDetailMap = medicalCatalogDetailApi.getCatalogDetailMapByProjectCodes(prescriptionDetail.getFirst().getTenantId(), prescriptionDetail
            .stream().map(InquiryPrescriptionDetailRespDTO::getProductPref).toList());

        List<Long> stdlibIdList = new ArrayList<>();
        for (InquiryPrescriptionDetailRespDTO dto : prescriptionDetail) {
            MedicalCatalogDetailDTO catalogDetailDTO = catalogDetailMap.get(dto.getProductPref());
            dto.setMedicalCatalogDetail(catalogDetailDTO);
            // 医保目录关联的自建标准库
            if (catalogDetailDTO != null && catalogDetailDTO.getExt() != null && catalogDetailDTO.getExt().getStdlibId() != null) {
                stdlibIdList.add(catalogDetailDTO.getExt().getStdlibId());
            }
        }

        if (stdlibIdList.isEmpty()) {
            return;
        }
        // 填充剂型和处方类型（自建标准库）
        Map<Long, ProductStdlibDto> stdlibDtoMap = productStdlibApi.searchProductStdlibList(new StdlibProductSearchDto().setIdList(stdlibIdList), stdlibIdList.size()).stream()
            .collect(Collectors.toMap(ProductStdlibDto::getId, Function.identity(), (a, b) -> b));
        for (InquiryPrescriptionDetailRespDTO dto : prescriptionDetail) {
            if (dto.getMedicalCatalogDetail() != null && dto.getMedicalCatalogDetail().getExt() != null && dto.getMedicalCatalogDetail().getExt().getStdlibId() != null) {
                Optional.ofNullable(stdlibDtoMap.get(dto.getMedicalCatalogDetail().getExt().getStdlibId())).ifPresent(stdlibDto -> {
                    dto.setDosageForm(stdlibDto.getDosageForm());
                    dto.setPresCategory(stdlibDto.getPresCategory());
                });
            }
        }
    }

    /**
     * 填充临床病例信息
     *
     * @param aux         辅助信息映射，用于存储各种辅助数据
     * @param config      配置信息，包含是否需要填充临床病例信息的配置
     * @param inquiryPref 问诊编号
     */
    public void fillClinicalCaseInfo(Map<String, Object> aux, LogicConfig.PostParameterConfig config, String inquiryPref) {
        if (CollUtil.contains(config.getNodes(), InternetAuxConstants.CLINICAL_CASE)) {
            InquiryClinicalCaseRespDto clinicalCase = inquiryClinicalCaseApi.getInquiryClinicalCase(inquiryPref);
            aux.put(InternetAuxConstants.CLINICAL_CASE, TransmissionInternetConvert.INSTANCE.convert(clinicalCase));
        }
    }

    /**
     * 填充处方CA信息
     *
     * @param aux              辅助数据Map，用于存储填充的处方详情信息
     * @param config           逻辑配置，包含需要填充的信息节点配置
     * @param prescriptionPref 处方编号
     */
    public void fillPrescriptionCaInfo(Map<String, Object> aux, LogicConfig.PostParameterConfig config, String prescriptionPref) {
        if (CollUtil.contains(config.getNodes(), InternetAuxConstants.PRESCRIPTION_CA)) {
            // 获取pdf中电子签名信息
            Map<String, ElectSignInfoDto> electSignInfoDtoMap = inquirySignatureContractApi.getSignaturePlatformContractElectSignInfo(prescriptionPref, ContractTypeEnum.PRESCRIPTION);
            if (CollUtil.isEmpty(electSignInfoDtoMap) || CollUtil.size(electSignInfoDtoMap) < 2) {
                // throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), "CA电子签名获取失败");
            }
            aux.put(InternetAuxConstants.PRESCRIPTION_CA, electSignInfoDtoMap);
        }
    }

    /**
     * 填充操作人用户信息
     *
     * @param aux
     * @param config
     * @param userId
     */
    public void fillOperateUserInfo(Map<String, Object> aux, LogicConfig.PostParameterConfig config, Long userId) {
        if (CollUtil.contains(config.getNodes(), InternetAuxConstants.OPERATE_USER_INFO) && userId != null) {
            aux.put(InternetAuxConstants.OPERATE_USER_INFO, adminUserApi.getUser(userId));
        }
    }

    /**
     * 填充监护人信息
     *
     * @param aux
     * @param config
     * @param inquiryPref
     */
    protected void fillInquiryDetailInfo(Map<String, Object> aux, PostParameterConfig config, String inquiryPref) {
        if (CollUtil.contains(config.getNodes(), InternetAuxConstants.INQUIRY_DETAIL_INFO)) {

            InquiryRecordDetailDto inquiryRecordDetail = inquiryApi.getInquiryRecordDetail(inquiryPref);
            aux.put(InternetAuxConstants.INQUIRY_DETAIL_INFO, inquiryRecordDetail);
            /**
             * 填充读卡记录信息
             */
            if (CollUtil.contains(config.getNodes(), MedicalAuxConstants.PERSON_INSURANCE_RECORD) && inquiryRecordDetail != null && inquiryRecordDetail.getExt().getMedicareInsuranceId() != null) {
                aux.put(MedicalAuxConstants.PERSON_INSURANCE_RECORD, inquiryMedicareApi.getMedicarePersonInsuranceRecord(inquiryRecordDetail.getExt().getMedicareInsuranceId()));
            }

        }
    }

    public static void main(String[] args) {
        String aa = null;
        System.out.println(StringUtils.substring(aa, 6, 14));
    }

    /**
     * 填充处方相关参数
     */
    public void fillPrescriptionParameters(TransmissionReqDTO transmissionReqDTO, PostParameterConfig config, PrescriptionTransmitterDTO data) {

        // -1.填充医院药师信息
        fillPrescriptionHosPharmacist(transmissionReqDTO.getAux(), config, data);

        // 0.填充CA信息
        fillPrescriptionCaInfo(transmissionReqDTO.getAux(), config, data.getPref());

        // 1. 就诊挂号记录(就诊流水号)
        fillMedicalRegistrationInfo(transmissionReqDTO.getAux(), config, BizTypeEnum.HYWZ, data.getInquiryPref());

        // 2. 门诊病例 (过敏、证候...)
        fillClinicalCaseInfo(transmissionReqDTO.getAux(), config, data.getInquiryPref());

        // 3. 医生药师编码职称
        fillDoctorPharmacistInfo(transmissionReqDTO.getAux(), config, data.getDoctorPref(), data.getPharmacistPref(), data.getHospitalPref());

        // 4. 处方明细
        fillPrescriptionDetailInfo(transmissionReqDTO.getAux(), config.getNodes(), data.getPref());

        // 5.问诊单详情(监护人等、读卡记录)
        fillInquiryDetailInfo(transmissionReqDTO.getAux(), config, data.getInquiryPref());

        // 6.将处方pdfUrl转换成base64放入aux中
        fillPrescriptionPdfUrlInfo(transmissionReqDTO.getAux(), config, data.getPrescriptionPdfUrl());

        // -1.填充医院药师信息
        fillPrescriptionHosInfo(transmissionReqDTO.getAux(), config, data.getHospitalPref());
    }


    private void fillPrescriptionHosInfo(Map<String, Object> aux, PostParameterConfig config, String hosPref) {
        if (CollUtil.contains(config.getNodes(), InternetAuxConstants.HOSPITAL_INFO) && StringUtils.isNotBlank(hosPref)) {
            List<InquiryHospitalRespDto> hospitals = hospitalApi.getInquiryHospitals(InquiryHospitalReqDto.builder().inquiryHospitalPrefs(Collections.singletonList(hosPref)).build());
            if (CollUtil.isNotEmpty(hospitals)) {
                aux.put(InternetAuxConstants.HOSPITAL_INFO, hospitals.getFirst());
            }
        }
    }

    /**
     * 填充医院药师信息
     *
     * @param aux
     * @param config
     * @param data
     */
    private void fillPrescriptionHosPharmacist(Map<String, Object> aux, PostParameterConfig config, PrescriptionTransmitterDTO data) {
        if (CollUtil.contains(config.getNodes(), InternetAuxConstants.HOS_PHARMACIST_INFO)) {
            Optional.ofNullable(inquiryPrescriptionAuditApi.selectByAuditorType(data.getPref(), AuditorTypeEnum.HOSPITAL_PHARMACIST))
                .ifPresent(auditDto -> {
                    data.setPharmacistPref(auditDto.getAuditorPref());
                    data.setPharmacistName(auditDto.getAuditorName());
                    data.setAuditPrescriptionTime(auditDto.getAuditorApprovalTime());
                    aux.put(InternetAuxConstants.HOS_PHARMACIST_INFO, auditDto);
                });
        }
    }

    /**
     * 将处方pdfUrl转换成base64放入aux中
     *
     * @param aux
     * @param config
     * @param prescriptionPdfUrl
     */
    private void fillPrescriptionPdfUrlInfo(Map<String, Object> aux, PostParameterConfig config, String prescriptionPdfUrl) {
        if (CollUtil.contains(config.getNodes(), InternetAuxConstants.PRESCRIPTION_PDF_BASE64) && StringUtils.isNotBlank(prescriptionPdfUrl)) {
            aux.put(InternetAuxConstants.PRESCRIPTION_PDF_BASE64, UrlConUtil.downLoadFile2Base64(prescriptionPdfUrl));
        }
    }

    /**
     * 填充医保签到信息
     *
     * @param aux
     * @param config
     * @param hospitalPref
     */
    public void fillSignInInfo(Map<String, Object> aux, PostParameterConfig config, String hospitalPref) {
        if (CollUtil.contains(config.getNodes(), MedicalAuxConstants.SIGN_IN) && StringUtils.isNotBlank(hospitalPref)) {
            aux.put(MedicalAuxConstants.SIGN_IN, medicareBaseApi.getMedicareSignInfo(hospitalPref));
        }
    }

    public void fillTenantInfo(Map<String, Object> aux, List<String> nodes, Long tenantId) {
        if (CollUtil.contains(nodes, InternetAuxConstants.TENANT_INFO) && tenantId != null && !TenantConstant.isSystemTenant()) {
            TenantDto tenantDto = TenantUtils.execute(tenantId, () -> tenantApi.getTenantAndCertDto());
            aux.put(InternetAuxConstants.TENANT_INFO, tenantDto);
        }
    }
}
