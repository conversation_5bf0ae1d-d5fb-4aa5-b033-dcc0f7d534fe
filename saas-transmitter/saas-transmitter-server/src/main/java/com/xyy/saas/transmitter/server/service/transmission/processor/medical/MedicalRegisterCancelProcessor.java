package com.xyy.saas.transmitter.server.service.transmission.processor.medical;

import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.pojo.transmitter.his.RegistrationTransmitterDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.PostParameterConfig;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @DateTime: 2025/7/21 20:39
 * @Description: 医保挂号撤销
 **/
@Component
public class MedicalRegisterCancelProcessor extends MedicalLogicConfigProcessor {

    @Override
    public NodeTypeEnum getNodeType() {
        return NodeTypeEnum.MEDICARE_REGISTRATION_CANCEL;
    }

    @Override
    protected void serializeObj(TransmissionReqDTO transmissionReqDTO) {
        RegistrationTransmitterDTO dataObj = transmissionReqDTO.getDataObj(RegistrationTransmitterDTO.class);
        transmissionReqDTO.setData(dataObj).setFullName(dataObj.getFullName()).setIdCard(dataObj.getIdCard()).setBusinessNo(dataObj.getBusinessNo());
    }

    @Override
    protected void fillPostProcessParameters(TransmissionReqDTO transmissionReqDTO, PostParameterConfig config) {
        super.fillPostProcessParameters(transmissionReqDTO, config);
        // 添加挂号特有的后置参数处理逻辑
        RegistrationTransmitterDTO data = (RegistrationTransmitterDTO) transmissionReqDTO.getData();
        // 医保签到
        transmissionPrescriptionService.fillSignInInfo(transmissionReqDTO.getAux(), config, data.getHospitalPref());
    }

}
