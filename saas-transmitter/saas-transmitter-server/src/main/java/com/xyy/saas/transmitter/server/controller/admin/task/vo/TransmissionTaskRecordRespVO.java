package com.xyy.saas.transmitter.server.controller.admin.task.vo;

import com.xyy.saas.inquiry.pojo.BaseDto;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.transmitter.api.servicepack.dto.TransmissionServicePackDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 数据传输记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TransmissionTaskRecordRespVO extends BaseDto {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24095")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "上游任务ID")
    @ExcelProperty("上游任务ID")
    private Long upstreamTaskId;

    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("租户ID")
    private Long tenantId;

    @Schema(description = "租户名称", example = "智鹿大药房金融港分店")
    @ExcelProperty("租户名称")
    private String tenantName;

    @Schema(description = "业务编号", example = "BIZ202502240001")
    @ExcelProperty("业务编号")
    private String businessNo;

    @Schema(description = "服务包ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13508")
    @ExcelProperty("服务包ID")
    private Integer servicePackId;

    @Schema(description = "协议配置节点ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "26608")
    @ExcelProperty("协议配置节点ID")
    private Integer configItemId;

    @Schema(description = "医药行业行政机构ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12781")
    private Integer organId;

    @Schema(description = "机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）")
    private Integer organType;

    @Schema(description = "机构名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("机构名称")
    private String organName;

    @Schema(description = "机构区域", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("机构所属地区")
    private String organArea;

    @Schema(description = "业务类型（比如:1-药监-日结存、2-药监-商品信息...）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("业务类型（比如:1-药监-日结存、2-药监-商品信息...）")
    private Integer nodeType;

    @Schema(description = "接口编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("接口编码")
    private String apiCode;

    @Schema(description = "原始参数（json格式）")
    @ExcelProperty("原始参数（json格式）")
    private String originalParams;

    @Schema(description = "请求参数(JSON格式)")
    @ExcelProperty("请求参数(JSON格式)")
    private String requestParams;

    @Schema(description = "响应结果(JSON格式)")
    @ExcelProperty("响应结果(JSON格式)")
    private String responseResult;

    @Schema(description = "请求状态(0:未请求 1:请求中 2:请求成功 3:请求失败)", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("请求状态(0:未请求 1:请求中 2:请求成功 3:请求失败)")
    private Integer requestStatus;

    @Schema(description = "允许重试", example = "true")
    @ExcelProperty("允许重试")
    private Boolean allowRetry;

    @Schema(description = "重试次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "21066")
    @ExcelProperty("重试次数")
    private Integer retryCount;

    @Schema(description = "最大重试次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "7915")
    @ExcelProperty("最大重试次数")
    private Integer maxRetryCount;

    @Schema(description = "错误信息")
    @ExcelProperty("错误信息")
    private String errorMessage;

    @Schema(description = "预计请求时间")
    @ExcelProperty("预计请求时间")
    private LocalDateTime expectedTime;

    @Schema(description = "实际请求时间")
    @ExcelProperty("实际请求时间")
    private LocalDateTime actualTime;

    @Schema(description = "完成时间")
    @ExcelProperty("完成时间")
    private LocalDateTime completeTime;

    @Schema(description = "优先级(0-10，越大优先级越高)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("优先级(0-10，越大优先级越高)")
    private Integer priority;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "姓名", example = "张三")
    @ExcelProperty("姓名")
    private String fullName;

    @Schema(description = "身份证号", example = "330106199001011234")
    @ExcelProperty("身份证号")
    private String idCard;

    @Schema(description = "租户信息")
    private TenantDto tenantDto;

    @Schema(description = "服务包信息")
    private TransmissionServicePackDTO servicePackDTO;

}