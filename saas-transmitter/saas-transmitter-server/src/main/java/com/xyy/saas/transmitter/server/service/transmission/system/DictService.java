package com.xyy.saas.transmitter.server.service.transmission.system;

import com.xyy.saas.localserver.medicare.dsl.executor.value.DSLValue;
import com.xyy.saas.transmitter.server.constant.TransmitterConstants;
import com.xyy.saas.transmitter.server.dal.dataobject.dict.TransmissionOrganDictDO;
import com.xyy.saas.transmitter.server.service.dict.TransmissionOrganDictMatchService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;

/**
 * 字典转换服务
 * <p>
 * 功能： 1. 获取和缓存字典数据
 *
 * <AUTHOR>
 */
@Service("dictService")
@Slf4j
public class DictService {

    @Resource
    private TransmissionOrganDictMatchService transmissionOrganDictMatchService;


    /**
     * 转换三方机构自带你
     *
     * @param dslValue  协议公共参数
     * @param dictType  字典类型
     * @param dictValue 字典值
     * @return 三方机构字典value
     */
    @Cacheable(cacheNames = TransmitterConstants.CACHE_PREFIX + "#60s", key = "#dslValue.organId+'_'+#dictType + #dictValue")
    public String convertOrganDict(DSLValue dslValue, String dictType, String dictValue) {
        TransmissionOrganDictDO organDictDO = transmissionOrganDictMatchService.getOrganDictValue(dslValue, dictType, dictValue);
        return organDictDO == null ? dictValue : organDictDO.getValue();
    }

    @Cacheable(cacheNames = TransmitterConstants.CACHE_PREFIX + "#60s", key = "#dslValue.organId+'_'+#dictType + #dictValue + #dictLabel")
    public String convertOrganDictLabel(DSLValue dslValue, String dictType, String dictValue, String dictLabel) {
        TransmissionOrganDictDO organDictDO = transmissionOrganDictMatchService.getOrganDictValue(dslValue, dictType, dictValue);
        return organDictDO == null ? dictLabel : organDictDO.getLabel();
    }

}
