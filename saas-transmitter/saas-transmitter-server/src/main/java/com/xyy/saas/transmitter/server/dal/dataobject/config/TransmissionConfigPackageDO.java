package com.xyy.saas.transmitter.server.dal.dataobject.config;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 协议配置包 DO
 *
 * <AUTHOR>
 */
@TableName("saas_transmission_config_package")
//@KeySequence("saas_transmission_config_package_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransmissionConfigPackageDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）
     */
    private Integer organType;
    /**
     * 服务提供商名称
     */
    private String providerName;
    /**
     * 父节点id
     */
    private Integer parentPackageId;
    /**
     * 配置包名称
     */
    private String name;
    /**
     * 版本号（实际存储：**********；页面展示：服务提供商名称+机构类型名称+日期+小时，比如：创智医保20250122）
     */
    private Long version;
    /**
     * 描述
     */
    private String description;
    /**
     * 是否禁用
     */
    private Boolean disable;

    @TableField(exist = false)
    private Long itemCount;

}