package com.xyy.saas.transmitter.server;

import cn.iocoder.yudao.framework.web.config.YudaoWebAutoConfiguration;
import com.xyy.common.config.TomcatServerConfig;
import com.xyy.saas.transmitter.server.config.DrugstoreConfig;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.spring.context.annotation.EnableDubbo;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;

@EnableDubbo(scanBasePackages = {"com.xyy.saas.transmitter"})
@SpringBootApplication(scanBasePackages = {"com.xyy.common", "com.xyy.saas.transmitter"})
@EnableDiscoveryClient
@Slf4j
@Import({DrugstoreConfig.class, YudaoWebAutoConfiguration.class})
public class TransmitterServerApplication {

    public static void main(String[] args) {
        // 设置skywalking服务名称
        System.setProperty("skywalking.agent.service_name", "saas-transmitter-server");

        SpringApplication.run(TransmitterServerApplication.class, args);
    }


    @Bean(initMethod = "")
    public TomcatServerConfig tomcatServerConfig() {
        TomcatServerConfig tomcatServerConfig = new TomcatServerConfig();
        String configUrl = "";
        try {
            configUrl = System.getProperty("user.dir") + "/tomcat_server.conf";
            if (configUrl.startsWith("file:")) {
                configUrl = configUrl.replace("file:", "");
            }
            File file = new File(configUrl);
            if (file.exists()) {
                file.delete();
            }
            file.createNewFile();
            log.info("文件创建成功:{}", configUrl);
        } catch (FileNotFoundException var2) {
            FileNotFoundException e = var2;
            log.error("获取路径异常：", e);
        } catch (IOException var3) {
            IOException e = var3;
            log.error("创建文件异常：", e);
        }
        TomcatServerConfig.configUrl = configUrl;
        return tomcatServerConfig;
    }

}
