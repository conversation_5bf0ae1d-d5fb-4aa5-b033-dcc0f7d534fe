package com.xyy.saas.transmitter.server.controller.admin.task.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 数据传输-记录新增/修改 Request VO")
@Data
@Builder
@Accessors(chain = true)
public class TransmissionTaskRecordSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24095")
    private Long id;

    @Schema(description = "上游任务ID")
    private Long upstreamTaskId;

    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "租户ID不能为空")
    private Long tenantId;

    @Schema(description = "业务编号", example = "BIZ202502240001")
    private String businessNo;

    @Schema(description = "服务包ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "13508")
    @NotNull(message = "服务包ID不能为空")
    private Integer servicePackId;

    @Schema(description = "协议配置节点ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "26608")
    @NotNull(message = "协议配置节点ID不能为空")
    private Integer configItemId;

    @Schema(description = "医药行业行政机构ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12781")
    @NotNull(message = "医药行业行政机构ID不能为空")
    private Integer organId;

    @Schema(description = "机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）不能为空")
    private Integer organType;

    @Schema(description = "业务类型（比如:1-药监-日结存、2-药监-商品信息...）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "业务类型（比如:1-药监-日结存、2-药监-商品信息...）不能为空")
    private Integer nodeType;

    @Schema(description = "接口编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "接口编码不能为空")
    private String apiCode;

    @Schema(description = "原始参数（json格式）")
    private String originalParams;

    @Schema(description = "请求参数(JSON格式)")
    private String requestParams;

    @Schema(description = "响应结果(JSON格式)")
    private String responseResult;

    @Schema(description = "请求状态(0:未请求 1:请求中 2:请求成功 3:请求失败)", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "请求状态(0:未请求 1:请求中 2:请求成功 3:请求失败)不能为空")
    private Integer requestStatus;

    @Schema(description = "允许重试", example = "true")
    @NotNull(message = "允许重试不能为空")
    private Boolean allowRetry;

    @Schema(description = "重试次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "21066")
    @NotNull(message = "重试次数不能为空")
    private Integer retryCount;

    @Schema(description = "最大重试次数", requiredMode = Schema.RequiredMode.REQUIRED, example = "7915")
    @NotNull(message = "最大重试次数不能为空")
    private Integer maxRetryCount;

    @Schema(description = "错误信息")
    private String errorMessage;

    @Schema(description = "预计请求时间")
    private LocalDateTime expectedTime;

    @Schema(description = "实际请求时间")
    private LocalDateTime actualTime;

    @Schema(description = "完成时间")
    private LocalDateTime completeTime;

    @Schema(description = "优先级(0-10，越大优先级越高)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "优先级(0-10，越大优先级越高)不能为空")
    private Integer priority;

    @Schema(description = "姓名", example = "张三")
    private String fullName;

    @Schema(description = "身份证号", example = "330106199001011234")
    private String idCard;

    @Schema(description = "配置包id", example = "16508")
    private Integer configPackageId;

}