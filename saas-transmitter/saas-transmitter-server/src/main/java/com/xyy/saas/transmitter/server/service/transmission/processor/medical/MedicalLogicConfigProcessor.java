package com.xyy.saas.transmitter.server.service.transmission.processor.medical;

import com.xyy.saas.inquiry.enums.transmitter.OrganTypeEnum;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor;
import com.xyy.saas.transmitter.server.service.transmission.processor.TransmissionPrescriptionService;
import jakarta.annotation.Resource;

/**
 * 医保对接基础处理器 提供 医保对接通用的处理逻辑
 * <p>
 * 核心功能： 1. 通用参数处理 2. 基础校验逻辑 3. 医保对接特定配置处理
 * <p>
 * 注意：此抽象类不再标注 @Component，具体的默认实现由 MedicalDefaultLogicConfigProcessor 提供
 */
public abstract class MedicalLogicConfigProcessor extends LogicConfigProcessor {

    @Override
    public OrganTypeEnum getOrganType() {
        return OrganTypeEnum.MEDICAL_INSURANCE;
    }

    @Resource
    protected TransmissionPrescriptionService transmissionPrescriptionService;
}