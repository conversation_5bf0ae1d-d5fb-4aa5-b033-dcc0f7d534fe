package com.xyy.saas.transmitter.server.api.transmission;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.util.SpelParserUtil;
import com.xyy.saas.transmitter.api.servicepack.TransmissionServicePackApi;
import com.xyy.saas.transmitter.api.servicepack.dto.TransmissionFunConfigOptionDTO;
import com.xyy.saas.transmitter.api.transmission.TransmissionConfigApi;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionConfigReqDTO;
import com.xyy.saas.transmitter.server.service.transmission.TransmissionService;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;
import java.util.Optional;

@DubboService
public class TransmissionConfigApiImpl implements TransmissionConfigApi {

    @Resource
    private TransmissionService transmissionService;

    @Resource
    private TransmissionServicePackApi transmissionServicePackApi;

    @Override
    public boolean isProductChangeQueryCatalog(Long tenantId, Integer prescriptionType) {

        TransmissionFunConfigOptionDTO funConfigOptionDTO = getTransmissionConfigReqDTO(tenantId);

        return funConfigOptionDTO != null && SpelParserUtil.parseBoolean(funConfigOptionDTO.getProductChangeQueryCatalog(), prescriptionType);
    }


    @Override
    public Integer diagnosisChangeQueryCatalog(Long tenantId, Integer prescriptionType) {

        TransmissionFunConfigOptionDTO funConfigOptionDTO = getTransmissionConfigReqDTO(tenantId);

        if (funConfigOptionDTO != null && SpelParserUtil.parseBoolean(funConfigOptionDTO.getDiagnosisChangeQueryCatalog(), prescriptionType)) {
            return funConfigOptionDTO.getDiagnosisChangeQueryOrganId();
        }

        return null;
    }

    /**
     * 是否需要读取参保人信息
     *
     * @param tenantId
     * @param prescriptionType
     * @return
     */
    @Override
    public boolean isNeedReadInsuredInfo(Long tenantId, Integer prescriptionType) {
        TransmissionFunConfigOptionDTO funConfigOptionDTO = getTransmissionConfigReqDTO(tenantId);

        return funConfigOptionDTO != null && SpelParserUtil.parseBoolean(funConfigOptionDTO.getNeedReadInsuredInfo(), prescriptionType);
    }


    /**
     * 获取门店功能配置
     *
     * @param tenantId
     * @return
     */
    public TransmissionFunConfigOptionDTO getTransmissionConfigReqDTO(Long tenantId) {

        tenantId = Optional.ofNullable(tenantId).orElse(TenantContextHolder.getRequiredTenantId());

        TransmissionConfigReqDTO configReqDTO = TransmissionConfigReqDTO.builder().tenantId(tenantId).nodeType(NodeTypeEnum.FUN_CONFIG).build();

        CommonResult<TransmissionFunConfigOptionDTO> commonResult = transmissionServicePackApi.selectConfigItem(configReqDTO, TransmissionFunConfigOptionDTO.class);

        if (commonResult.isSuccess() && commonResult.getData() != null) {
            return commonResult.getData();
        }

        return null;
    }
}
