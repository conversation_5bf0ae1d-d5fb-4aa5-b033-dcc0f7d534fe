package com.xyy.saas.transmitter.server.dal.dataobject.config;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 协议配置 DO
 *
 * <AUTHOR>
 */
@TableName("saas_transmission_config_item")
//@KeySequence("saas_transmission_config_item_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransmissionConfigItemDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 协议配置包ID
     */
    private Integer configPackageId;
    /**
     * 父节点id
     */
    private Integer parentItemId;
    /**
     * 配置类型（1-视图配置、2-逻辑配置、3-协议配置）
     */
    private Integer dslType;
    /**
     * 节点业务类型（比如:1-药监-日结存、2-药监-商品信息...）
     */
    private Integer nodeType;
    /**
     * 接口编码
     */
    private String apiCode;
    /**
     * 描述 节点名称
     */
    private String description;
    /**
     * 配置值,yaml
     */
    private String configValue;
    /**
     * 是否禁用
     */
    private Boolean disable;

}