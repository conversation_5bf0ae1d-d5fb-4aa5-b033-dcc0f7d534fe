package com.xyy.saas.transmitter.server.dal.dataobject.servicepack;

import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 服务包 DO
 *
 * <AUTHOR>
 */
@TableName("saas_transmission_service_pack")
//@KeySequence("saas_transmission_service_pack_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TransmissionServicePackDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 服务包名称
     */
    private String name;
    /**
     * 医药行业行政机构ID
     */
    private Integer organId;

    /**
     * 系统业务类型 {@link BizTypeEnum}
     */
    private Integer bizType;

    /**
     * 机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）
     */
    private Integer organType;
    /**
     * 协议配置包id
     */
    private Integer configPackageId;
    /**
     * 动态库资源
     */
    private String dllResource;

    /**
     * 动态库版本
     */
    private String dllVersion;

    /**
     * 小票模板资源
     */
    private String ticketResource;
    /**
     * 账单模板资源
     */
    private String billResource;
    /**
     * 拓展资源
     */
    private String extResource;
    /**
     * 接口文档
     */
    private String apiDoc;
    /**
     * 版本号（实际存储：2025012209；页面展示：医药行业行政机构名称+服务提供商名称+机构类型名称+日期+小时，比如：陕西省医保局创智医保20250122；）
     */
    private Long version;
    /**
     * 环境：0-测试；1-灰度；2-上线；
     */
    private Integer env;
    /**
     * 是否禁用
     */
    private Boolean disable;

    /**
     * 省份编码
     */
    private String provinceCode;

    /**
     * 省份
     */
    private String province;

    /**
     * 城市编码
     */
    private String cityCode;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域编码
     */
    private String areaCode;

    /**
     * 区域
     */
    private String area;

}