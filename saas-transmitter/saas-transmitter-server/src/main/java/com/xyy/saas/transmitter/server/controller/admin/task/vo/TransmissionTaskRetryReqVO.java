package com.xyy.saas.transmitter.server.controller.admin.task.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 数据传输-重传 VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
public class TransmissionTaskRetryReqVO extends PageParam {

    @Schema(description = "主键IDs")
    private List<Long> ids;

    @Schema(description = "门店ids")
    private List<Long> tenantIds;

    @Schema(description = "业务编号", example = "BIZ202502240001")
    private String businessNo;

    @Schema(description = "服务包ID", example = "13508")
    private Integer servicePackId;

    @Schema(description = "机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）", example = "2")
    private Integer organType;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}