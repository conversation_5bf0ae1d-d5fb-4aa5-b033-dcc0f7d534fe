package com.xyy.saas.transmitter.server.util.transmission.drug;

import org.apache.commons.lang3.StringUtils;
import java.util.HashMap;

public enum YunnanAreaEnum {
    AREAD_87101("五华区", 87101, 530102),
    AREAD_87102("盘龙区", 87102, 530103),
    AREAD_87103("官渡区", 87103, 530111),
    AREAD_87104("西山区", 87104, 530112),
    AREAD_87105("东川区", 87105, 530113),
    AREAD_87106("呈贡区", 87106, 530114),
    AREAD_87107("晋宁县", 87107, 530115),
    AREAD_87109("富民县", 87109, 530124),
    AREAD_87110("宜良县", 87110, 530125),
    AREAD_87111("石林彝族自治县", 87111, 530126),
    AREAD_87112("嵩明县", 87112, 530127),
    AREAD_87113("禄劝彝族苗族自治县", 87113, 530128),
    AREAD_87114("寻甸回族彝族自治县", 87114, 530129),
    AREAD_87108("安宁市", 87108, 530181),
    AREAD_87401("麒麟区", 87401, 530302),
    AREAD_87403("马龙县", 87403, 530304),
    AREAD_87408("陆良县", 87408, 530322),
    AREAD_87407("师宗县", 87407, 530323),
    AREAD_87406("罗平县", 87406, 530324),
    AREAD_87405("富源县", 87405, 530325),
    AREAD_87409("会泽县", 87409, 530326),
    AREAD_87402("沾益县", 87402, 530303),
    AREAD_87404("宣威市", 87404, 530381),
    AREAD_87701("红塔区", 87701, 530402),
    AREAD_87702("江川县", 87702, 530403),
    AREAD_87703("澄江县", 87703, 530481),
    AREAD_87704("通海县", 87704, 530423),
    AREAD_87705("华宁县", 87705, 530424),
    AREAD_87706("易门县", 87706, 530425),
    AREAD_87707("峨山彝族自治县", 87707, 539426),
    AREAD_87708("新平彝族傣族自治县", 87708, 530427),
    AREAD_87709("元江哈尼族彝族傣族自治县", 87709, 530428),
    AREAD_87501("隆阳区", 87501, 530502),
    AREAD_87503("施甸县", 87503, 530521),
    AREAD_87502("腾冲县", 87502, 530581),
    AREAD_87504("龙陵县", 87504, 530523),
    AREAD_87505("昌宁县", 87505, 530524),
    AREAD_87001("昭阳区", 87001, 530602),
    AREAD_87002("鲁甸县", 87002, 530621),
    AREAD_87003("巧家县", 87003, 530622),
    AREAD_87004("盐津县", 87004, 530623),
    AREAD_87005("大关县", 87005, 530624),
    AREAD_87006("永善县", 87006, 530625),
    AREAD_87007("绥江县", 87007, 530626),
    AREAD_87008("镇雄县", 87008, 530627),
    AREAD_87009("彝良县", 87009, 530628),
    AREAD_87010("威信县", 87010, 530629),
    AREAD_87011("水富县", 87011, 530681),
    AREAD_88801("古城区", 88801, 530702),
    AREAD_88804("玉龙纳西族自治县", 88804, 530721),
    AREAD_88802("永胜县", 88802, 530722),
    AREAD_88803("华坪县", 88803, 530723),
    AREAD_88805("宁蒗彝族自治县", 88805, 530724),
    AREAD_87901("思茅区", 87901, 530802),
    AREAD_87902("宁洱哈尼族彝族自治县", 87902, 530821),
    AREAD_87903("墨江哈尼族自治县", 87903, 530822),
    AREAD_87904("景东彝族自治县", 87904, 530823),
    AREAD_87905("景谷傣族彝族自治县", 87905, 530824),
    AREAD_87906("镇沅彝族哈尼族拉祜族自治县", 87906, 530825),
    AREAD_87907("江城哈尼族彝族自治县", 87907, 530826),
    AREAD_87908("孟连傣族拉祜族佤族自治县", 87908, 530827),
    AREAD_87909("澜沧拉祜族自治县", 87909, 530828),
    AREAD_87910("西盟佤族自治县", 87910, 530829),
    AREAD_88301("临翔区", 88301, 530902),
    AREAD_88302("凤庆县", 88302, 530921),
    AREAD_88303("云县", 88303, 530922),
    AREAD_88304("永德县", 88304, 530923),
    AREAD_88305("镇康县", 88305, 530924),
    AREAD_88306("双江拉祜族佤族布朗族傣族自治县", 88306, 530925),
    AREAD_88307("耿马傣族佤族自治县", 88307, 530926),
    AREAD_88308("沧源佤族自治县", 88308, 530927),
    AREAD_87801("楚雄市", 87801, 532301),
    AREAD_87802("双柏县", 87802, 532322),
    AREAD_87803("牟定县", 87803, 532323),
    AREAD_87804("南华县", 87804, 532324),
    AREAD_87805("姚安县", 87805, 532325),
    AREAD_87806("大姚县", 87806, 532326),
    AREAD_87807("永仁县", 87807, 532327),
    AREAD_87808("元谋县", 87808, 532328),
    AREAD_87809("武定县", 87809, 532329),
    AREAD_87810("禄丰县", 87810, 532331),
    AREAD_87302("个旧市", 87302, 532501),
    AREAD_87303("开远市", 87303, 532502),
    AREAD_87301("蒙自市", 87301, 532503),
    AREAD_87307("弥勒市", 87307, 532504),
    AREAD_87304("屏边苗族自治县", 87304, 532523),
    AREAD_87305("建水县", 87305, 532524),
    AREAD_87306("石屏县", 87306, 532525),
    AREAD_87308("泸西县", 87308, 532527),
    AREAD_87309("元阳县", 87309, 532528),
    AREAD_87310("红河县", 87310, 532529),
    AREAD_87311("金平苗族瑶族傣族自治县", 87311, 532530),
    AREAD_87312("绿春县", 87312, 532531),
    AREAD_87313("河口瑶族自治县", 87313, 532532),
    AREAD_87601("文山市", 87601, 532601),
    AREAD_87602("砚山县", 87602, 532622),
    AREAD_87603("西畴县", 87603, 532623),
    AREAD_87604("麻栗坡县", 87604, 532624),
    AREAD_87605("马关县", 87605, 532625),
    AREAD_87606("丘北县", 87606, 532626),
    AREAD_87607("广南县", 87607, 532627),
    AREAD_87608("富宁县", 87608, 532628),
    AREAD_69101("景洪市", 69101, 532801),
    AREAD_69102("勐海县", 69102, 532822),
    AREAD_69103("勐腊县", 69103, 532823),
    AREAD_87201("大理市", 87201, 532901),
    AREAD_87210("漾濞彝族自治县", 87210, 532922),
    AREAD_87202("祥云县", 87202, 532923),
    AREAD_87203("宾川县", 87203, 532924),
    AREAD_87204("弥渡县", 87204, 532925),
    AREAD_87211("南涧彝族自治县", 87211, 532926),
    AREAD_87212("巍山彝族回族自治县", 87212, 532927),
    AREAD_87205("永平县", 87205, 532928),
    AREAD_87206("云龙县", 87206, 532929),
    AREAD_87207("洱源县", 87207, 532930),
    AREAD_87208("剑川县", 87208, 532931),
    AREAD_87209("鹤庆县", 87209, 532932),
    AREAD_69202("瑞丽市", 69202, 533102),
    AREAD_69201("芒市", 69201, 533103),
    AREAD_69203("梁河县", 69203, 533122),
    AREAD_69204("盈江县", 69204, 533123),
    AREAD_69205("陇川县", 69205, 533124),
    AREAD_88601("泸水县", 88601, 533301),
    AREAD_88602("福贡县", 88602, 533323),
    AREAD_88603("贡山独龙族怒族自治县", 88603, 533324),
    AREAD_88604("兰坪白族普米族自治县", 88604, 533325),
    AREAD_88701("香格里拉市", 88701, 533401),
    AREAD_88702("德钦县", 88702, 533422),
    AREAD_88703("维西傈僳族自治县", 88703, 533423);

    private static HashMap<String, Integer> map = new HashMap();

    static {
        for (YunnanAreaEnum yunnanAreaEnum : YunnanAreaEnum.values()) {
            map.put(yunnanAreaEnum.area, yunnanAreaEnum.areaCode);
        }
    }

    YunnanAreaEnum(String area, Integer areaCode, Integer tenantAreaCode) {
        this.area = area;
        this.areaCode = areaCode;
        this.tenantAreaCode = tenantAreaCode;
    }

    private String area;
    private Integer areaCode; //云南地区编码
    private Integer tenantAreaCode; //问诊定义地区编码

    public static Integer getAreaCodeByArea(String area) {
        return map.get(area);
    }


    public Integer getAreaCode() {
        return areaCode;
    }


    public Integer getTenantAreaCode() {
        return tenantAreaCode;
    }


    public static Integer getAreaCodeByTenantAreaCode(Integer tenantAreaCode) {
        for (YunnanAreaEnum ft : YunnanAreaEnum.values()) {
            if (ft.getTenantAreaCode().equals(tenantAreaCode)) {
                return ft.getAreaCode();
            }
        }
        return tenantAreaCode;
    }


    public static String handleStoreNo(String storeNo) {
        if (StringUtils.isEmpty(storeNo)) {
            return "";
        }
        return storeNo.replace("（滇）", "滇").replace("(滇)", "滇").replace("【", "").replace("】", "").replace("（", "(").replace("）", ")");
    }
}
