package com.xyy.saas.transmitter.server.constant;

import com.alibaba.fastjson.serializer.PropertyFilter;
import java.util.List;

/**
 * 传输服务常量类
 *
 * @Author:chenxiaoyi
 * @Date:2025/02/27 17:31
 */
public class TransmitterConstants {

    public static final String CACHE_PREFIX = "transmitter";

    public static PropertyFilter propertyFilter = (object, name, value) ->
        !List.of(InternetAuxConstants.PRESCRIPTION_PDF_BASE64, InternetAuxConstants.PRESCRIPTION_CA, "originalRxFile", "rxFile", "encData").contains(name);

}
