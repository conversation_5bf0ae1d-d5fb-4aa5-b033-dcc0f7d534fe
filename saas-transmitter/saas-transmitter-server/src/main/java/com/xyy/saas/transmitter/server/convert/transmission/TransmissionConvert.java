package com.xyy.saas.transmitter.server.convert.transmission;

import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.RequestStatusEnum;
import com.xyy.saas.localserver.medicare.dsl.executor.value.DynamicBusiness;
import com.xyy.saas.transmitter.api.config.dto.TransmissionConfigItemDTO;
import com.xyy.saas.transmitter.api.servicepack.dto.TransmissionServicePackDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionConfigReqDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordRespVO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRecordSaveReqVO;
import com.xyy.saas.transmitter.server.controller.admin.task.vo.TransmissionTaskRetryReqVO;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.ProcessContext;
import java.util.ArrayList;
import java.util.Map;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 传输服务转换器
 * <p>
 * 功能： 1. 对象转换 2. 参数组装 3. 数据映射
 *
 * <AUTHOR>
 */
@Mapper
public interface TransmissionConvert {

    TransmissionConvert INSTANCE = Mappers.getMapper(TransmissionConvert.class);

    int DEFAULT_PRIORITY = 5; // 默认任务优先级(1-10)

    /**
     * 从任务记录构建配置请求DTO
     *
     * @param task 任务记录
     * @return 配置请求DTO
     */
    default TransmissionConfigReqDTO convertConfig(TransmissionTaskRecordRespVO task) {
        return TransmissionConfigReqDTO.builder()
            .organType(task.getOrganType())
            .servicePackId(task.getServicePackId())
            .tenantId(task.getTenantId())
            .nodeType(NodeTypeEnum.fromCode(task.getNodeType()))
            .build();
    }

    /**
     * 构建业务上下文
     *
     * @param tenantId    租户ID
     * @param servicePack 服务包
     * @param params      业务参数
     * @return 业务上下文
     */
    default DynamicBusiness buildDynamicBusiness(Long tenantId, TransmissionServicePackDTO servicePack,
        Map<String, Object> params) {
        DynamicBusiness dynamicBusiness = new DynamicBusiness();
        dynamicBusiness.setData(params);
        dynamicBusiness.setTenantId(tenantId);
        dynamicBusiness.setOrganType(servicePack.getOrganType());
        dynamicBusiness.setServicePackId(servicePack.getId());
        dynamicBusiness.setConfigPackageId(servicePack.getConfigPackageId());
        dynamicBusiness.setOrganId(servicePack.getOrganId());
        return dynamicBusiness;
    }

    /**
     * 创建任务实体 根据配置项创建标准的任务实体，设置默认值
     * <p>
     * 设置内容： 1. 基础信息：租户、服务包、机构等 2. 业务信息：节点类型、API编码等 3. 任务属性：状态、重试配置、优先级等
     *
     * @param configItem 配置项信息
     * @param context    处理上下文
     * @return 任务实体
     */
    default TransmissionTaskRecordSaveReqVO createTask(TransmissionConfigItemDTO configItem, ProcessContext context) {
        return TransmissionTaskRecordSaveReqVO.builder()
            // 基础信息
            .id(context.getReqDTO().getTaskId())
            .tenantId(context.getTenantId())
            .servicePackId(context.getServicePack().getId())
            .configPackageId(configItem.getConfigPackageId())
            .configItemId(configItem.getId())
            .organId(context.getServicePack().getOrganId())
            .organType(context.getServicePack().getOrganType())
            .fullName(context.getReqDTO().getFullName())
            .idCard(context.getReqDTO().getIdCard())
            .businessNo(context.getReqDTO().getBusinessNo())
            // 业务信息
            .nodeType(configItem.getNodeType())
            .apiCode(configItem.getApiCode())
            .originalParams(context.getReqDTO().getOriginalParamsJson())
            // 任务属性
            .requestStatus(RequestStatusEnum.NOT_REQUESTED.getCode())
            .retryCount(0)
            .maxRetryCount(context.getLogicConfig().getRetryConfig().getMaxRetryCount())
            .allowRetry(context.getLogicConfig().getRetryConfig().isAllowRetry())
            .priority(DEFAULT_PRIORITY)
            .build();
    }

    /**
     * 构建下游请求DTO 基于下游任务创建标准的传输请求对象
     * <p>
     * 设置内容： 1. 配置信息：租户、服务包等基础配置 2. 业务数据：姓名、身份证等业务信息 3. 任务关联：关联的任务ID
     *
     * @param downstreamTask 下游任务实体
     * @param context        处理上下文
     * @return 下游请求DTO
     */
    default TransmissionReqDTO buildDownstreamReqDTO(TransmissionTaskRecordSaveReqVO downstreamTask,
        ProcessContext context) {
        return TransmissionReqDTO.builder()
            // 基础配置
            .config(buildDownstreamConfig(downstreamTask, context))
            // 业务数据
            .fullName(context.getReqDTO().getFullName())
            .idCard(context.getReqDTO().getIdCard())
            .data(context.getReqDTO().getData())
            .aux(context.getReqDTO().getAux())
            .downstreamReqList(new ArrayList<>())
            // 任务关联
            .taskId(downstreamTask.getId())
            .build();
    }

    /**
     * 构建下游配置 创建下游任务的配置信息
     * <p>
     * 配置内容： 1. 租户标识：确保多租户隔离 2. 服务包信息：关联的服务配置 3. 节点类型：业务节点标识
     *
     * @param downstreamTask 下游任务实体
     * @param context        处理上下文
     * @return 下游配置DTO
     */
    default TransmissionConfigReqDTO buildDownstreamConfig(TransmissionTaskRecordSaveReqVO downstreamTask,
        ProcessContext context) {
        return TransmissionConfigReqDTO.builder()
            .tenantId(context.getTenantId())
            .servicePackId(context.getServicePack().getId())
            .nodeType(NodeTypeEnum.fromCode(downstreamTask.getNodeType()))
            .build();
    }

    TransmissionTaskRecordPageReqVO convertRetry(TransmissionTaskRetryReqVO taskRetryReqVO);
}
