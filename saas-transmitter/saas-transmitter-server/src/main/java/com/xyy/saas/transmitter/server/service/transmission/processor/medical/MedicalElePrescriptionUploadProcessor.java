package com.xyy.saas.transmitter.server.service.transmission.processor.medical;

import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionTransmitterDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.PostParameterConfig;
import org.springframework.stereotype.Component;

/**
 * 医保-上传电子处方
 */
@Component
public class MedicalElePrescriptionUploadProcessor extends MedicalLogicConfigProcessor {

    @Override
    public NodeTypeEnum getNodeType() {
        return NodeTypeEnum.MEDICAL_ELE_PRESCRIPTION_UPLOAD;
    }

    @Override
    protected void serializeObj(TransmissionReqDTO transmissionReqDTO) {
        PrescriptionTransmitterDTO dataObj = transmissionReqDTO.getDataObj(PrescriptionTransmitterDTO.class);
        transmissionReqDTO.setData(dataObj).setFullName(dataObj.getFullName()).setIdCard(dataObj.getIdCard()).setBusinessNo(dataObj.getBusinessNo());
    }


    @Override
    protected void fillPostProcessParameters(TransmissionReqDTO transmissionReqDTO, PostParameterConfig config) {
        super.fillPostProcessParameters(transmissionReqDTO, config);
        // 添加诊疗结算特有的后置参数处理逻辑
        PrescriptionTransmitterDTO data = (PrescriptionTransmitterDTO) transmissionReqDTO.getData();
        // 填充处方相关数据
        transmissionPrescriptionService.fillPrescriptionParameters(transmissionReqDTO, config, data);
        // 填充操作人信息
        transmissionPrescriptionService.fillOperateUserInfo(transmissionReqDTO.getAux(), config, data.getUserId());

    }

}