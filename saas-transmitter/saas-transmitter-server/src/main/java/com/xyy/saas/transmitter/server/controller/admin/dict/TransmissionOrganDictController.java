package com.xyy.saas.transmitter.server.controller.admin.dict;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.idempotent.core.annotation.Idempotent;
import cn.iocoder.yudao.framework.idempotent.core.keyresolver.impl.UserIdempotentKeyResolver;
import com.xyy.saas.inquiry.pojo.excel.ImportResultDto;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictExcelVO;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictRespVO;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictSaveReqVO;
import com.xyy.saas.transmitter.server.dal.dataobject.dict.TransmissionOrganDictDO;
import com.xyy.saas.transmitter.server.service.dict.TransmissionOrganDictService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Parameters;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.Collections;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;


@Tag(name = "管理后台 - 服务商字典")
@RestController
@RequestMapping("/transmitter/transmission-organ-dict")
@Validated
public class TransmissionOrganDictController {

    @Resource
    private TransmissionOrganDictService transmissionProviderDictService;

    @PostMapping("/create")
    @Operation(summary = "创建服务商字典")
    @PreAuthorize("@ss.hasPermission('saas:transmission-provider-dict:create')")
    public CommonResult<Long> createTransmissionProviderDict(@Valid @RequestBody TransmissionOrganDictSaveReqVO createReqVO) {
        return success(transmissionProviderDictService.createTransmissionProviderDict(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新服务商字典")
    @PreAuthorize("@ss.hasPermission('saas:transmission-provider-dict:update')")
    public CommonResult<Boolean> updateTransmissionProviderDict(@Valid @RequestBody TransmissionOrganDictSaveReqVO updateReqVO) {
        transmissionProviderDictService.updateTransmissionProviderDict(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除服务商字典")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:transmission-provider-dict:delete')")
    public CommonResult<Boolean> deleteTransmissionProviderDict(@RequestParam("id") Long id) {
        transmissionProviderDictService.deleteTransmissionProviderDict(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得服务商字典")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:transmission-provider-dict:query')")
    // @TenantIgnore
    // @PermitAll
    public CommonResult<TransmissionOrganDictRespVO> getTransmissionProviderDict(@RequestParam("id") Long id) {
        TransmissionOrganDictDO transmissionProviderDict = transmissionProviderDictService.getTransmissionProviderDict(id);
        return success(BeanUtils.toBean(transmissionProviderDict, TransmissionOrganDictRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得服务商字典分页")
    @PreAuthorize("@ss.hasPermission('saas:transmission-provider-dict:query')")
    public CommonResult<PageResult<TransmissionOrganDictRespVO>> getTransmissionProviderDictPage(@Valid TransmissionOrganDictPageReqVO pageReqVO) {
        PageResult<TransmissionOrganDictRespVO> pageResult = transmissionProviderDictService.getTransmissionProviderDictPage(pageReqVO);
        return success(pageResult);
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出服务商字典 Excel")
    @PreAuthorize("@ss.hasPermission('saas:transmission-provider-dict:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTransmissionProviderDictExcel(@Valid TransmissionOrganDictPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TransmissionOrganDictRespVO> list = transmissionProviderDictService.getTransmissionProviderDictPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "服务商字典.xls", "数据", TransmissionOrganDictRespVO.class,
            BeanUtils.toBean(list, TransmissionOrganDictRespVO.class));
    }


    @GetMapping("/get-import-template")
    @Operation(summary = "获得三方字典导入模板")
    public void importTemplate(HttpServletResponse response) throws IOException {
        // 手动创建导出 demo
        List<TransmissionOrganDictExcelVO> list = Collections.singletonList(
            TransmissionOrganDictExcelVO.builder().build()
        );
        // 输出
        ExcelUtils.write(response, "三方字典导入模板.xls", "三方字典导入模板", TransmissionOrganDictExcelVO.class, list);
    }

    @PostMapping("/import")
    @Operation(summary = "三方字典导入模板")
    @Parameters({
        @Parameter(name = "file", description = "Excel 文件", required = true),
        @Parameter(name = "updateSupport", description = "是否支持更新，默认为 false", example = "true"),
        @Parameter(name = "organId", description = "是否支持更新，默认为 false", example = "true")
    })
    // @PreAuthorize("@ss.hasPermission('saas:transmission-provider-dict:import')")
    @PermitAll
    public CommonResult<ImportResultDto> importExcel(@RequestParam("file") MultipartFile file,
        @RequestParam(value = "updateSupport", required = false, defaultValue = "false") Boolean updateSupport,
        @RequestParam(value = "organId") Integer organId, @RequestParam(value = "dictValue") String dictValue) throws Exception {
        List<TransmissionOrganDictExcelVO> list = ExcelUtils.read(file, TransmissionOrganDictExcelVO.class);
        return success(transmissionProviderDictService.importDictList(list, updateSupport, organId, dictValue));
    }


    @PostMapping("/diagnosis-match")
    @Operation(summary = "诊断匹配")
    @Idempotent(timeout = 20, keyResolver = UserIdempotentKeyResolver.class, message = "请求繁忙,每次导入需间隔20秒再操作") // user维度锁住 默认30s
    public CommonResult<Boolean> diagnosisMatch(@RequestParam(value = "organId") Integer organId) {
        transmissionProviderDictService.diagnosisMatch(organId);
        return success(true);
    }


}