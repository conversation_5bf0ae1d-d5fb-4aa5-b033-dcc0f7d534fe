package com.xyy.saas.transmitter.server.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;

import java.util.concurrent.Executor;
import java.util.concurrent.ThreadPoolExecutor;

/**
 * 异步任务配置
 * 配置异步任务执行器和线程池参数
 */
@Configuration
@EnableAsync
public class AsyncConfig {

    /**
     * 核心线程数
     */
    private static final int CORE_POOL_SIZE = 10;
    
    /**
     * 最大线程数
     */
    private static final int MAX_POOL_SIZE = 20;
    
    /**
     * 队列容量
     */
    private static final int QUEUE_CAPACITY = 500;
    
    /**
     * 线程空闲时间
     */
    private static final int KEEP_ALIVE_SECONDS = 300;

    /**
     * 配置异步任务执行器
     * 用于处理 @Async 注解的方法
     *
     * @return 异步任务执行器
     */
    @Bean(name = "transmissionAsyncExecutor")
    public Executor asyncExecutor() {
        ThreadPoolTaskExecutor executor = new ThreadPoolTaskExecutor();
        
        // 核心线程数
        executor.setCorePoolSize(CORE_POOL_SIZE);
        // 最大线程数
        executor.setMaxPoolSize(MAX_POOL_SIZE);
        // 队列容量
        executor.setQueueCapacity(QUEUE_CAPACITY);
        // 线程空闲时间
        executor.setKeepAliveSeconds(KEEP_ALIVE_SECONDS);
        // 线程名前缀
        executor.setThreadNamePrefix("Transmission-Async-");
        
        // 拒绝策略：当队列已满且线程数达到最大值时，如何处理新任务
        // CALLER_RUNS：在调用者线程中执行任务，提供反馈机制
        executor.setRejectedExecutionHandler(new ThreadPoolExecutor.CallerRunsPolicy());
        
        // 等待所有任务完成后再关闭线程池
        executor.setWaitForTasksToCompleteOnShutdown(true);
        // 等待时间（默认为0，此处设置为60秒）
        executor.setAwaitTerminationSeconds(60);
        
        // 初始化线程池
        executor.initialize();
        
        return executor;
    }
} 