package com.xyy.saas.transmitter.server.controller.admin.dict.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 服务商数据字典配对分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
public class TransmissionOrganDictMatchPageReqVO extends PageParam {

    @Schema(description = "医药行业行政机构ID", example = "4327")
    private Integer organId;

    @Schema(description = "字典类型", example = "1")
    private String dictType;

    @Schema(description = "saas字典名", example = "26981")
    private String dictName;

    private Long dictId;

    @Schema(description = "服务商字典名", example = "2115")
    private String organDictName;

    private Long organDictId;

    @Schema(description = "配对状态：0未配对 1已配对", example = "1")
    private Integer status;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;


    private List<Long> dictIds;

    private List<Long> noDictIds;

    private List<Long> organDictIds;


}