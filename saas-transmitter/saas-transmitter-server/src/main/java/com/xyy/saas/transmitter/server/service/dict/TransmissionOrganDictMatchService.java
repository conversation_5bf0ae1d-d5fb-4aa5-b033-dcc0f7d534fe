package com.xyy.saas.transmitter.server.service.dict;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.localserver.medicare.dsl.executor.value.DSLValue;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictMatchGetReqVO;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictMatchPageReqVO;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictMatchRespVO;
import com.xyy.saas.transmitter.server.controller.admin.dict.vo.TransmissionOrganDictMatchSaveReqVO;
import com.xyy.saas.transmitter.server.controller.admin.organ.vo.TransmissionOrganRespVO;
import com.xyy.saas.transmitter.server.dal.dataobject.dict.TransmissionOrganDictDO;
import com.xyy.saas.transmitter.server.dal.dataobject.dict.TransmissionOrganDictMatchDO;
import jakarta.validation.Valid;
import java.util.List;


/**
 * 服务商数据字典配对 Service 接口
 *
 * <AUTHOR>
 */
public interface TransmissionOrganDictMatchService {

    /**
     * 查询三方字典匹配的机构列表
     *
     * @param dictType 字典类型
     * @return 机构列表 - 下拉选择
     */
    List<TransmissionOrganRespVO> listDictMatchOrgan(String dictType);

    /**
     * 同步数据字典
     *
     * @param syncReqVO
     * @return
     */
    // Long syncDict(TransmissionProviderDictMatchSyncReqVO syncReqVO);


    /**
     * 创建服务商数据字典配对
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    void createTransmissionOrganDictMatch(@Valid TransmissionOrganDictMatchSaveReqVO createReqVO);


    /**
     * 删除服务商数据字典配对
     *
     * @param id 编号
     */
    void deleteTransmissionOrganDictMatch(Long id);

    /**
     * 获得服务商数据字典配对
     *
     * @param getReqVO vo
     * @return 服务商数据字典配对
     */
    TransmissionOrganDictMatchRespVO getTransmissionOrganDictMatch(TransmissionOrganDictMatchGetReqVO getReqVO);

    /**
     * 获得服务商数据字典配对分页
     *
     * @param pageReqVO 分页查询
     * @return 服务商数据字典配对分页
     */
    PageResult<TransmissionOrganDictMatchRespVO> getTransmissionOrganDictMatchPage(TransmissionOrganDictMatchPageReqVO pageReqVO);

    /**
     * 获得服务商数据字典配对分页
     *
     * @param getReqVO
     * @return
     */
    List<TransmissionOrganDictMatchDO> getTransmissionDictMatch(TransmissionOrganDictMatchGetReqVO getReqVO);

    /**
     * 获取三方字典
     *
     * @param dslValue  基础参数
     * @param dictType  字典类型
     * @param dictValue 字典值
     * @return
     */
    TransmissionOrganDictDO getOrganDictValue(DSLValue dslValue, String dictType, String dictValue);
}