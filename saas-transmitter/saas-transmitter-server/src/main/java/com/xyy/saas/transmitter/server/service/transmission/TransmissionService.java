package com.xyy.saas.transmitter.server.service.transmission;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.transmitter.api.servicepack.dto.TransmissionServicePackDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionConfigReqDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionRespDTO;
import java.util.List;
import java.util.Map;

/**
 * 数据传输服务 - 领域服务接口 负责处理数据传输相关的核心业务逻辑
 * <p>
 * 核心功能： 1. 协议配置校验：验证传输协议的有效性 2. 业务逻辑校验：校验业务数据的合法性 3. 数据传输执行：处理数据传输任务
 * <p>
 * 使用场景： 1. 配置校验：验证租户的协议配置 2. 数据校验：校验传输数据的合规性 3. 数据传输：执行具体的传输任务
 *
 * <AUTHOR>
 */
public interface TransmissionService {

    /**
     * 校验协议配置有效性 验证租户是否包含有效的传输协议配置
     * <p>
     * 校验内容： 1. 租户配置：验证租户的基础配置 2. 服务包配置：检查服务包的有效性 3. 协议配置：验证协议配置的完整性
     *
     * @param transmissionConfigReqDTO 配置请求对象，包含租户ID和节点类型等信息
     * @return true-配置有效；false-配置无效
     */
    boolean validProtocolConfig(TransmissionConfigReqDTO transmissionConfigReqDTO);

    /**
     * 校验业务数据 验证待传输的数据是否符合业务规则要求
     * <p>
     * 校验内容： 1. 数据完整性：必填字段校验 2. 数据合法性：格式和业务规则校验 3. 业务约束：特定业务场景的限制条件
     *
     * @param transmissionReqDTO 传输请求对象，包含业务数据和配置信息
     * @return 校验结果，包含校验状态和错误信息
     */
    CommonResult<Boolean> validateBusinessLogic(TransmissionReqDTO transmissionReqDTO);

    /**
     * 执行数据传输(多服务包配置相同节点-药监的模式) 多协议调用，循环请求 处理数据传输任务，支持同步和异步模式
     * <p>
     * 处理流程： 1. 参数校验：验证请求参数的完整性 2. 任务创建：构建传输任务实体 3. 任务执行：处理数据传输逻辑 4. 结果处理：处理传输结果和异常
     * <p>
     * 特性支持： 1. 同步/异步：支持两种传输模式 2. 重试机制：失败任务自动重试 3. 下游处理：支持级联任务处理
     *
     * @param transmissionReqDTO 传输请求对象，包含传输数据和配置信息
     * @param clazz              响应数据类型的Class对象
     * @param <T>                响应数据类型
     * @return 传输响应列表，包含处理结果和响应数据
     */
    <T> List<TransmissionRespDTO<T>> contractsInvoke(TransmissionReqDTO transmissionReqDTO, Class<T> clazz);

    /**
     * 执行数据传输(节点唯一) 单一协议调用，如果匹配到多个服务包则取第一个服务包的节点调用 处理数据传输任务，支持同步和异步模式
     * <p>
     * 处理流程： 1. 参数校验：验证请求参数的完整性 2. 任务创建：构建传输任务实体 3. 任务执行：处理数据传输逻辑 4. 结果处理：处理传输结果和异常
     * <p>
     * 特性支持： 1. 同步/异步：支持两种传输模式 2. 重试机制：失败任务自动重试 3. 下游处理：支持级联任务处理
     *
     * @param transmissionReqDTO 传输请求对象，包含传输数据和配置信息
     * @param clazz              响应数据类型的Class对象
     * @param <T>                响应数据类型
     * @return 传输响应列表，包含处理结果和响应数据
     */
    <T> CommonResult<T> contractInvoke(TransmissionReqDTO transmissionReqDTO, Class<T> clazz);

    /**
     * 远程调用接口
     *
     * @param tenantId    租户ID
     * @param servicePack 服务包
     * @param params      请求参数
     * @param resultClass 响应数据类型
     * @return 响应数据
     */
    <T> T executeRemoteCall(Long tenantId, TransmissionServicePackDTO servicePack, Map<String, Object> params,
        Class<T> resultClass);
}