package com.xyy.saas.transmitter.server.service.transmission.processor.internet;

import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.SupervisionDoctorInfoDto;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionTransmitterDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.PostParameterConfig;
import com.xyy.saas.transmitter.server.service.transmission.processor.LogicConfigProcessor.LogicConfig.PreParameterConfig;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * 互联网监管-医师诊疗业务备案处理器
 */
@Slf4j
@Component
@AllArgsConstructor
public class InternetSupervisionPhysicianDiagnosisTreatmentRecordProcessor extends InternetSupervisionLogicConfigProcessor {

    @DubboReference
    private TenantApi tenantApi;

    @Override
    public NodeTypeEnum getNodeType() {
        return NodeTypeEnum.INTERNET_SUPERVISION_PHYSICIAN_DIAGNOSIS_TREATMENT_RECORD;
    }

    @Override
    protected void serializeObj(TransmissionReqDTO transmissionReqDTO) {
        final SupervisionDoctorInfoDto dataObj = transmissionReqDTO.getDataObj(SupervisionDoctorInfoDto.class);
        transmissionReqDTO.setData(dataObj).setFullName(dataObj.getDoctorInfo().getName()).setIdCard(dataObj.getDoctorInfo().getIdCard()).setBusinessNo(dataObj.getDoctorInfo().getPref());
    }

    @Override
    protected void fillPreProcessParameters(TransmissionReqDTO transmissionReqDTO, PreParameterConfig config) {
        super.fillPreProcessParameters(transmissionReqDTO, config);
    }

    @Override
    protected void fillPostProcessParameters(TransmissionReqDTO transmissionReqDTO, PostParameterConfig config) {
        super.fillPostProcessParameters(transmissionReqDTO, config);
        // 添加挂号特有的后置参数处理逻辑
        SupervisionDoctorInfoDto data = (SupervisionDoctorInfoDto) transmissionReqDTO.getData();


    }
}