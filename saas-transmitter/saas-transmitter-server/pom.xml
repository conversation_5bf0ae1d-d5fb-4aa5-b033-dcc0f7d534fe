<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>com.xyy.saas</groupId>
    <artifactId>saas-transmitter</artifactId>
    <version>${revision}</version>
    <relativePath>../pom.xml</relativePath>
  </parent>


  <modelVersion>4.0.0</modelVersion>
  <artifactId>saas-transmitter-server</artifactId>

  <dependencies>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>biz-soa-starter</artifactId>
    </dependency>

    <!-- 项目内部依赖 -->
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-transmitter-api</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-product-api</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-user-api</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-hospital-api</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-pharmacist-api</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-patient-api</artifactId>
      <version>${revision}</version>
    </dependency>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-signature-api</artifactId>
      <version>${revision}</version>
    </dependency>
    <!-- 暂时注释 -->
    <!--  <dependency>
       <groupId>com.xyy.saas</groupId>
       <artifactId>medicare-dsl</artifactId>
       <version>1.0-SNAPSHOT</version>
     </dependency> -->

    <dependency>
      <groupId>com.xyy.common</groupId>
      <artifactId>xyy-common-dubbo-client</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>

    <!-- SnakeYAML 依赖 -->
    <!-- <dependency> -->
    <!--   <groupId>org.yaml</groupId> -->
    <!--   <artifactId>snakeyaml</artifactId> -->
    <!--   <version>2.0</version> -->
    <!-- </dependency> -->

    <!-- 业务组件 -->
    <!-- <dependency> -->
    <!--   <groupId>cn.iocoder.boot</groupId> -->
    <!--   <artifactId>yudao-spring-boot-starter-web</artifactId> -->
    <!-- </dependency> -->
    <!-- <dependency> -->
    <!--   <groupId>cn.iocoder.boot</groupId> -->
    <!--   <artifactId>yudao-spring-boot-starter-mybatis</artifactId> -->
    <!-- </dependency> -->
    <!-- <dependency> -->
    <!--   <groupId>cn.iocoder.boot</groupId> -->
    <!--   <artifactId>yudao-spring-boot-starter-biz-tenant</artifactId> -->
    <!-- </dependency> -->

    <!-- &lt;!&ndash; DB 相关 &ndash;&gt; -->
    <!-- <dependency> -->
    <!--   <groupId>mysql</groupId> -->
    <!--   <artifactId>mysql-connector-java</artifactId> -->
    <!-- </dependency> -->
    <!-- <dependency> -->
    <!--   <groupId>com.baomidou</groupId> -->
    <!--   <artifactId>mybatis-plus-boot-starter</artifactId> -->
    <!-- </dependency> -->
    <!-- <dependency> -->
    <!--   <groupId>com.alibaba</groupId> -->
    <!--   <artifactId>druid-spring-boot-starter</artifactId> -->
    <!-- </dependency> -->

    <!-- &lt;!&ndash; Dubbo &ndash;&gt; -->
    <!-- <dependency> -->
    <!--   <groupId>org.apache.dubbo</groupId> -->
    <!--   <artifactId>dubbo-spring-boot-starter</artifactId> -->
    <!-- </dependency> -->

    <!-- &lt;!&ndash; Spring Boot &ndash;&gt; -->
    <!-- <dependency> -->
    <!--   <groupId>org.springframework.boot</groupId> -->
    <!--   <artifactId>spring-boot-starter-web</artifactId> -->
    <!-- </dependency> -->
    <!-- <dependency> -->
    <!--   <groupId>org.springframework.boot</groupId> -->
    <!--   <artifactId>spring-boot-starter-validation</artifactId> -->
    <!-- </dependency> -->
    <!-- <dependency> -->
    <!--   <groupId>org.springframework.boot</groupId> -->
    <!--   <artifactId>spring-boot-starter-test</artifactId> -->
    <!--   <scope>test</scope> -->
    <!-- </dependency> -->

    <!-- &lt;!&ndash; Common &ndash;&gt; -->
    <!-- <dependency> -->
    <!--   <groupId>org.projectlombok</groupId> -->
    <!--   <artifactId>lombok</artifactId> -->
    <!-- </dependency> -->
    <!-- <dependency> -->
    <!--   <groupId>cn.hutool</groupId> -->
    <!--   <artifactId>hutool-all</artifactId> -->
    <!-- </dependency> -->

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>biz-soa-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-excel</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-test</artifactId>
    </dependency>
    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-protection</artifactId>
    </dependency>
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-localserver-dsl</artifactId>
      <version>2.0.0-SNAPSHOT</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>${maven-springboot-plugin.version}</version>
        <configuration>
          <mainClass>com.xyy.saas.transmitter.server.TransmitterServerApplication</mainClass>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>