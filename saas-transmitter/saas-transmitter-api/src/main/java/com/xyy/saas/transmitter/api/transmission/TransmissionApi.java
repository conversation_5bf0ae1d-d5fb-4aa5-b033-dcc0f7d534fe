package com.xyy.saas.transmitter.api.transmission;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionConfigReqDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionRespDTO;
import java.util.List;

/**
 * 数据传输服务 API 接口
 * 提供数据传输相关的远程服务调用接口
 * 
 * 核心功能：
 * 1. 协议配置校验：验证传输协议的有效性
 * 2. 业务逻辑校验：校验业务数据的合法性
 * 3. 数据传输执行：执行具体的传输任务
 * 
 * 使用场景：
 * 1. 配置校验：在数据传输前进行协议配置校验
 * 2. 业务校验：执行业务规则的合法性校验
 * 3. 数据传输：执行实际的数据传输操作
 * 
 * <AUTHOR>
 */
public interface TransmissionApi {

    /**
     * 校验协议配置有效性
     * 验证租户的传输协议配置是否可用
     * 
     * 校验内容：
     * 1. 配置存在性：检查是否存在协议配置
     * 2. 配置有效性：检查配置是否处于有效状态
     * 3. 权限校验：检查租户是否有权限使用该配置
     *
     * @param transmissionConfigReqDTO 配置校验请求，包含租户ID和节点标识
     * @return true-配置有效；false-配置无效
     */
    boolean validProtocolConfig(TransmissionConfigReqDTO transmissionConfigReqDTO);

    /**
     * 执行业务逻辑校验
     * 校验业务数据的合法性
     * 
     * 校验内容：
     * 1. 数据完整性：检查必要字段是否完整
     * 2. 业务规则：验证是否符合业务规则
     * 3. 状态校验：检查相关状态是否正确
     *
     * @param transmissionReqDTO 业务校验请求，包含业务数据
     * @return 校验结果，包含校验状态和错误信息
     */
    CommonResult<Boolean> validateBusinessLogic(TransmissionReqDTO transmissionReqDTO);

    /**
     * 执行数据传输(多服务包配置相同节点-药监的模式)
     * 多协议调用，循环请求
     * 处理数据传输任务，支持同步和异步模式
     *
     * 处理流程：
     * 1. 参数校验：验证请求参数的完整性
     * 2. 任务创建：构建传输任务实体
     * 3. 任务执行：处理数据传输逻辑
     * 4. 结果处理：处理传输结果和异常
     *
     * 特性支持：
     * 1. 同步/异步：支持两种传输模式
     * 2. 重试机制：失败任务自动重试
     * 3. 下游处理：支持级联任务处理
     *
     * @param transmissionReqDTO 传输请求对象，包含传输数据和配置信息
     * @param clazz 响应数据类型的Class对象
     * @return 传输响应列表，包含处理结果和响应数据
     * @param <T> 响应数据类型
     */
    <T> List<TransmissionRespDTO<T>> contractsInvoke(TransmissionReqDTO transmissionReqDTO, Class<T> clazz);

    /**
     * 执行数据传输(节点唯一)
     * 单一协议调用，如果匹配到多个服务包则取第一个服务包的节点调用
     * 处理数据传输任务，支持同步和异步模式
     *
     * 处理流程：
     * 1. 参数校验：验证请求参数的完整性
     * 2. 任务创建：构建传输任务实体
     * 3. 任务执行：处理数据传输逻辑
     * 4. 结果处理：处理传输结果和异常
     *
     * 特性支持：
     * 1. 同步/异步：支持两种传输模式
     * 2. 重试机制：失败任务自动重试
     * 3. 下游处理：支持级联任务处理
     *
     * @param transmissionReqDTO 传输请求对象，包含传输数据和配置信息
     * @param clazz 响应数据类型的Class对象
     * @return 传输响应列表，包含处理结果和响应数据
     * @param <T> 响应数据类型
     */
    <T> CommonResult<T> contractInvoke(TransmissionReqDTO transmissionReqDTO, Class<T> clazz);
}
