package com.xyy.saas.transmitter.api.organ.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class TransmissionOrganNetworkConfigDTO implements java.io.Serializable {

    @Schema(description = "编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "ybzw")
    private String code;

    @Schema(description = "网络配置名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "医保专网")
    @NotNull(message = "网络配置名称不能为空")
    private String name;

    @Schema(description = "网络配置节点")
    private Map<String, Object> networkItem;

    @Schema(description = "平台公钥")
    private String platformPublicKey;

    @Schema(description = "平台私钥")
    private String platformPrivateKey;

    @Schema(description = "三方对接公钥")
    private String thirdPartyPublicKey;

    @Schema(description = "三方对接私钥")
    private String thirdPartyPrivateKey;

    @Schema(description = "环境类型（1-正式、2-测试）", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "环境类型")
    private Integer env;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否禁用不能为空")
    private Boolean disable;


}