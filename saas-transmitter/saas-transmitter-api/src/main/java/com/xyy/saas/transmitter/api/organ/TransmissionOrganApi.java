package com.xyy.saas.transmitter.api.organ;

import com.xyy.saas.transmitter.api.organ.dto.TransmissionOrganDTO;
import java.util.List;

/**
 * 医药行业机构 API 接口
 * <p>
 * 功能： 1. 提供机构信息的查询服务
 *
 * <AUTHOR>
 * @date 2025/02/24 15:39
 */
public interface TransmissionOrganApi {

    /**
     * 根据机构ID查询机构详细信息
     * <p>
     * 主要信息包括： 1. 基础信息：机构名称、类型、地区等 2. 网络配置：接入网络、认证信息等 3. 业务配置：业务规则、参数等
     *
     * @param id 机构ID
     * @return 机构详细信息
     */
    TransmissionOrganDTO getTransmissionOrgan(Integer id);

    /**
     * 根据机构ID列表查询机构详细信息
     *
     * @param ids
     * @return
     */
    List<TransmissionOrganDTO> getTransmissionOrgans(List<Integer> ids);
}
