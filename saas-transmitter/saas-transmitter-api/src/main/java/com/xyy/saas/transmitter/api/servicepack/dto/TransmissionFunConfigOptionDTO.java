package com.xyy.saas.transmitter.api.servicepack.dto;

import java.io.Serializable;
import lombok.Data;

/**
 * @Author:chenxiaoyi
 * @Date:2025/02/24 21:11
 */
@Data
public class TransmissionFunConfigOptionDTO implements Serializable {

    /**
     * 监管医院编码 - 用于问诊互联网医院对接监管
     */
    private String supervisionHospitalPref;

    /**
     * 监管需要划价的医院编码 - 用于问诊互联网医院对接监管
     */
    private String pricingHospitalPref;


    /**
     * 医保 - 商品切换目录查询
     */
    private String productChangeQueryCatalog;

    /**
     * 医保 - 诊断切换目录查询
     */
    private String diagnosisChangeQueryCatalog;

    /**
     * 医保 - 诊断切换目录查询-三方服务商编码
     */
    private Integer diagnosisChangeQueryOrganId;

    /**
     * 读取参保人信息
     */
    private String needReadInsuredInfo;

    /**
     * 有效参保险种
     */
    private String effectiveInsuredType;

    /**
     * 有效参保状态
     */
    private String effectiveInsuredStatus;

}
