package com.xyy.saas.transmitter.api.transmission;

import com.xyy.saas.transmitter.api.servicepack.dto.TransmissionFunConfigOptionDTO;

/**
 * 门店配置Api
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/04 15:09
 */
public interface TransmissionConfigApi {

    /**
     * 是否切换商品目录数据源
     *
     * @param tenantId         门店ID
     * @param prescriptionType 处方类型
     * @return
     */
    boolean isProductChangeQueryCatalog(Long tenantId, Integer prescriptionType);

    /**
     * 切换诊断目录数据源
     *
     * @param tenantId
     * @param prescriptionType
     * @return
     */
    Integer diagnosisChangeQueryCatalog(Long tenantId, Integer prescriptionType);

    /**
     * 是否需要读取参保人信息
     *
     * @param tenantId
     * @param prescriptionType
     * @return
     */
    boolean isNeedReadInsuredInfo(Long tenantId, Integer prescriptionType);

    /**
     * 获取门店配置
     *
     * @param tenantId
     * @return
     */
    TransmissionFunConfigOptionDTO getTransmissionConfigReqDTO(Long tenantId);
}
