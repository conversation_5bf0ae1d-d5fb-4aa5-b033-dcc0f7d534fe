package com.xyy.saas.transmitter.api.transmission.dto;

import com.xyy.saas.inquiry.enums.transmitter.DslTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.OrganTypeEnum;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class TransmissionConfigReqDTO implements Serializable {

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 机构类型 {@link OrganTypeEnum}
     */
    private Integer organType;

    /**
     * 业务节点 {@link NodeTypeEnum}
     */
    @NotBlank(message = "业务节点不能为空")
    private NodeTypeEnum nodeType;

    /**
     * 服务包ID
     */
    private Integer servicePackId;

    private List<Integer> servicePackIds;

    /**
     * 配置包ID
     */
    private Integer configPackageId;

    /**
     * 配置类型 {@link DslTypeEnum}
     */
    @Builder.Default
    private DslTypeEnum dslType = DslTypeEnum.LOGIC;
}