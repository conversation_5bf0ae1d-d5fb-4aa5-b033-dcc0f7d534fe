package com.xyy.saas.transmitter.api.transmission.dto;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import lombok.Builder;
import lombok.Data;
import java.util.List;
import java.util.Map;

@Data
@Builder
public class TransmissionRespDTO<T> {

    /**
     * 基础请求信息
     */
    private TransmissionConfigReqDTO transmissionConfigReqDTO;

    /**
     * 姓名
     */
    private String fullName;

    /**
     * 身份证号
     */
    private String idCard;


    /**
     * 任务ID
     */
    private Long taskId;

    /**
     * 传输服务 请求入参业务扩展map
     */
    private Map<String, Object> requestParams;

    /**
     * 处理结果
     */
    private CommonResult<T> result;

    /**
     * 下游节点响应列表
     */
    private List<TransmissionRespDTO<T>> downstreamRespList;

    /**
     * 从请求对象创建响应对象构建器
     *
     * @param reqDTO 请求对象
     * @param <T> 响应数据类型
     * @return 响应对象构建器
     */
    public static <T> TransmissionRespDTOBuilder<T> fromRequest(TransmissionReqDTO reqDTO) {
        return TransmissionRespDTO.<T>builder()
            .transmissionConfigReqDTO(reqDTO.getConfig())
            .fullName(reqDTO.getFullName())
            .idCard(reqDTO.getIdCard())
            .requestParams(reqDTO.getOriginalParams())
            .taskId(reqDTO.getTaskId());
    }
}