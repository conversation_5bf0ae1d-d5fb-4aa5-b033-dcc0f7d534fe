package com.xyy.saas.inquiry.supervision.server.controller.admin.prescription;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.mq.prescription.dto.PrescriptionMqCommonMessage;
import com.xyy.saas.inquiry.supervision.server.service.prescription.PrescriptionSupervisionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author:chen<PERSON><PERSON>i
 * @Date:2025/07/28 9:58
 */
@Tag(name = "管理后台 - supervision")
@RestController
@RequestMapping("/supervision/prescription")
@Validated
public class SupervisionController {

    @Resource
    private PrescriptionSupervisionService prescriptionSupervisionService;

    @GetMapping("/upload")
    @Operation(summary = "upload处方")
    public CommonResult<Boolean> updateInquirySignaturePlatform(String pref) {
        PrescriptionMqCommonMessage message = new PrescriptionMqCommonMessage();
        message.setPrescriptionPref(pref);
        message.setAuditorType(AuditorTypeEnum.PLATFORM_PHARMACIST.getCode());

        prescriptionSupervisionService.prescriptionSupervision(message);
        return success(true);
    }

}
