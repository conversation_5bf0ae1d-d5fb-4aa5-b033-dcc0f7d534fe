package com.xyy.saas.localserver.entity.enums;

import java.util.Objects;

/**
 * desc 审批业务类型：首营商品审批、商品回收站恢复审批、提报商品总部审批、质量变更申请审批、售价调整单审批、
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public enum BpmBusinessTypeEnum {

    PURCHASE_ORDER_APPROVE(1, "pd_purchase_order_apr", "采购审批"),
    STORE_ALLOCATION_APPROVE(2, "pd_store_allocation_apr", "门店要货审批"),
    STORE_REQUISITION_APPROVE(3, "pd_store_requisition_apr", "门店调剂审批"),
    ;

    public final int code;
    public final String processDefinitionKey;
    public final String desc;

    BpmBusinessTypeEnum(int code, String processDefinitionKey, String desc) {
        this.code = code;
        this.processDefinitionKey = processDefinitionKey;
        this.desc = desc;
    }

    public static BpmBusinessTypeEnum getByCode(int code) {
        for (BpmBusinessTypeEnum bpmBusinessTypeEnum : BpmBusinessTypeEnum.values()) {
            if (bpmBusinessTypeEnum.code == code) {
                return bpmBusinessTypeEnum;
            }
        }
        return null;
    }

    public static BpmBusinessTypeEnum getByProcessDefinitionKey(String processDefinitionKey) {
        for (BpmBusinessTypeEnum bpmBusinessTypeEnum : BpmBusinessTypeEnum.values()) {
            if (bpmBusinessTypeEnum.processDefinitionKey.equals(processDefinitionKey)) {
                return bpmBusinessTypeEnum;
            }
        }
        return null;
    }

}
