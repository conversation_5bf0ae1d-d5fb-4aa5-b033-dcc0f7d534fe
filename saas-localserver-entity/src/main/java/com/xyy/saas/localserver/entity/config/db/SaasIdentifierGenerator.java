package com.xyy.saas.localserver.entity.config.db;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.core.incrementer.IdentifierGenerator;
import com.xyy.saas.datasync.client.constants.DataContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jdbc.core.JdbcTemplate;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.atomic.AtomicLong;

import static com.xyy.saas.localserver.entity.config.db.DeviceIdentifierGenerator.generateDeviceId;

/**
 * SAAS本地化服务ID生成器
 * <p>
 * ID结构规则（19位数字）：
 * | 业务类型(1) | 租户ID(6) | 设备ID(3) | 自增序列(9) |
 * 示例：1000001001000000001 → 业务类型1 + 租户000001 + 设备001 + 序列000000001
 *
 * <AUTHOR>
 */
@Slf4j
public class SaasIdentifierGenerator implements IdentifierGenerator {
	// 常量定义（与图片中的编号位数对应）
	private static final int TYPE_LENGTH = 1;            // 业务类型位数
	private static final int TENANT_ID_LENGTH = 6;       // 租户ID位数
	private static final int DEVICE_ID_LENGTH = 3;        // 设备ID位数
	private static final int INCREMENT_ID_LENGTH = 9;     // 自增序列位数
	private static final long MAX_TENANT_ID = 999_999L;   // 最大租户ID（6位）
	private static final long MAX_DEVICE_ID = 999L;       // 最大设备ID（3位）
	private static final long MAX_INCREMENT_ID = 999_999_999L; // 最大自增序列（9位）

	@Value("${saas.business.type:1}")
	private int businessType;  // 业务类型（与单据前缀关联，如图片中的JHDZ/CGDZ）

	private final JdbcTemplate jdbcTemplate;
	private final Integer deviceId;  // 设备ID（根据MAC/SN生成）

	// 线程安全存储各表的序列号生成器
	private final Map<String, AtomicLong> tableSequenceMap = new ConcurrentHashMap<>();

	public SaasIdentifierGenerator(JdbcTemplate jdbcTemplate) {
		this.jdbcTemplate = jdbcTemplate;
		this.deviceId = generateDeviceId();  // 初始化时生成设备ID
		validateConfiguration();  // 启动时立即校验参数合法性
	}

	/**
	 * 生成下一个ID（实现MyBatis-Plus接口）
	 * @param entity 实体对象（用于获取表名）
	 * @return 符合规则的长整型ID
	 * @throws IllegalArgumentException 参数校验失败时抛出
	 */
	@Override
	public Number nextId(Object entity) {
		final Long tenantId = validateTenantId();
		final String tableName = resolveTableName(entity);
		return generateIncrementalId(tableName, tenantId);
	}

	// region 核心私有方法
	// ----------------------- 初始化相关 -----------------------

	/**
	 * 参数校验（启动时立即失败）
	 * @throws IllegalArgumentException 参数不合法时抛出
	 */
	private void validateConfiguration() {
		if (businessType < 1 || businessType > 9) {
			throw new IllegalArgumentException("业务类型必须为1-9之间的整数");
		}
		if (deviceId < 0 || deviceId > MAX_DEVICE_ID) {
			throw new IllegalArgumentException("设备ID超出范围 (0 <= deviceId <= 999)");
		}
	}

	// ----------------------- ID生成逻辑 -----------------------
	/**
	 * 生成递增ID（线程安全）
	 * @param tableName 表名
	 * @param tenantId 租户ID
	 */
	private Long generateIncrementalId(String tableName, Long tenantId) {
		AtomicLong sequence = tableSequenceMap.computeIfAbsent(tableName, key -> {
			Long maxId = queryMaxIdFromDB(tableName);
			long initialValue = parseSequenceFromId(maxId, tenantId);
			return new AtomicLong(initialValue);
		});

		long nextValue = sequence.incrementAndGet();
		if (nextValue > MAX_INCREMENT_ID) {
			throw new IllegalStateException("自增序列已达上限（表：" + tableName + "）");
		}
		return assembleFullId(tenantId, nextValue);
	}

	/**
	 * 从数据库查询最大ID
	 */
	private Long queryMaxIdFromDB(String tableName) {
		try {
			return jdbcTemplate.queryForObject("SELECT MAX(id) FROM " + tableName, Long.class);
		} catch (Exception e) {
			log.warn("查询表[{}]最大ID失败，初始序列将从0开始: {}", tableName, e.getMessage());
			return 0L;
		}
	}

	/**
	 * 从已有ID解析当前序列号（关键逻辑）
	 * @param maxId 数据库最大ID（可能为null）
	 * @param currentTenantId 当前租户ID（用于校验）
	 */
	private long parseSequenceFromId(Long maxId, Long currentTenantId) {
		if (maxId == null || maxId == 0) return 0L;

		String idStr = String.valueOf(maxId);
		if (idStr.length() != totalIdLength()) {
			log.warn("发现异常ID结构[{}]，将重置序列", maxId);
			return 0L;
		}

		// 分解ID各部分
		int type = Integer.parseInt(idStr.substring(0, TYPE_LENGTH));
		long tenantId = Long.parseLong(idStr.substring(TYPE_LENGTH, TYPE_LENGTH + TENANT_ID_LENGTH));
		int deviceId = Integer.parseInt(idStr.substring(TYPE_LENGTH + TENANT_ID_LENGTH,
				TYPE_LENGTH + TENANT_ID_LENGTH + DEVICE_ID_LENGTH));

		// 校验业务一致性
		if (type != this.businessType || tenantId != currentTenantId || deviceId != this.deviceId) {
			log.warn("检测到跨业务/租户/设备的ID[{}]，将重置序列", maxId);
			return 0L;
		}

		return Long.parseLong(idStr.substring(totalIdLength() - INCREMENT_ID_LENGTH));
	}

	/**
	 * 拼接完整ID（保证19位长度）
	 */
	private Long assembleFullId(Long tenantId, Long sequence) {
		String formatted = String.format(
				"%0" + TYPE_LENGTH + "d" +       // 业务类型
						"%0" + TENANT_ID_LENGTH + "d" +  // 租户ID
						"%0" + DEVICE_ID_LENGTH + "d" +  // 设备ID
						"%0" + INCREMENT_ID_LENGTH + "d",// 自增序列
				businessType,
				tenantId,
				deviceId,
				sequence
		);
		return Long.parseLong(formatted);
	}

	// ----------------------- 辅助方法 -----------------------
	/**
	 * 校验租户ID合法性
	 */
	private Long validateTenantId() {
		Long tenantId = TenantContextHolder.getTenantId() == null? Long.valueOf(DataContextHolder.getTenantId()):TenantContextHolder.getTenantId();
		if (tenantId == null || tenantId < 0 || tenantId > MAX_TENANT_ID) {
			throw new IllegalArgumentException("租户ID无效 (0 <= tenantId <= 999999)");
		}
		return tenantId;
	}

	/**
	 * 解析实体类对应的表名
	 */
	private String resolveTableName(Object entity) {
		TableName annotation = entity.getClass().getAnnotation(TableName.class);
		if (annotation == null) {
			throw new IllegalArgumentException("实体类缺少@TableName注解");
		}
		return annotation.value();
	}

	private int totalIdLength() {
		return TYPE_LENGTH + TENANT_ID_LENGTH + DEVICE_ID_LENGTH + INCREMENT_ID_LENGTH;
	}
	// endregion

}
