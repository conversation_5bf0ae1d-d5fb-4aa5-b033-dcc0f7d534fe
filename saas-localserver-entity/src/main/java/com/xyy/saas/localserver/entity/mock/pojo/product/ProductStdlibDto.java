package com.xyy.saas.localserver.entity.mock.pojo.product;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;

/**
 * 商品标准库 DTO
 */
@Schema(description = "商品标准库 DTO")
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ProductStdlibDto implements Serializable {

    @Schema(description = "主键")
    private Long id;

    @Schema(description = "中台标准库ID")
    private Long midStdlibId;
}