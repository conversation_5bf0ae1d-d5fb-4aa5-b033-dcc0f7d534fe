package com.xyy.saas.localserver.entity.config.db;
import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.security.*;
import java.math.BigInteger;
import java.util.*;
import java.lang.reflect.*;

public class DeviceIdentifierGenerator {

    // 主入口：生成3位设备ID
    public static int generateDeviceId() {
        String rawId = getPlatformIdentifier();
        try {
            MessageDigest digest = MessageDigest.getInstance("SHA-256");
            byte[] hash = digest.digest(rawId.getBytes(StandardCharsets.UTF_8));
            return new BigInteger(1, hash)
                    .mod(BigInteger.valueOf(1000))
                    .intValue();
        } catch (NoSuchAlgorithmException e) {
            return 999; // 终极回退
        }
    }

    // 获取平台原始标识符
    private static String getPlatformIdentifier() {
        if (isAndroid()) {
            return getAndroidIdentifier();
        } else if (isIOS()) {
            return getIOSIdentifier();
        } else {
            return getDesktopIdentifier();
        }
    }

    /* Android实现 */
    private static boolean isAndroid() {
        return System.getProperty("java.runtime.name", "").contains("Android");
    }

    private static String getAndroidIdentifier() {
//        try {
//            // 获取ANDROID_ID
//            Class<?> settings = Class.forName("android.provider.Settings$Secure");
//            Method getString = settings.getMethod("getString",
//                    ContentResolver.class, String.class);
//            String androidId = (String) getString.invoke(null,
//                    getAndroidContentResolver(), "android_id");
//
//            // 回退到序列号
//            if (androidId == null || androidId.equals("9774d56d682e549c")) {
//                return getAndroidSerial();
//            }
//            return androidId;
//        } catch (Exception e) {
//            return getAndroidFallback();
//        }
        return "UNKNOWN";
    }
//
//    private static String getAndroidSerial() {
//        try {
//            Class<?> build = Class.forName("android.os.Build");
//            return (String) build.getField("SERIAL").get(null);
//        } catch (Exception e) {
//            return "UNKNOWN";
//        }
//    }
//
//    private static Object getAndroidContentResolver() {
//        try {
//            Class<?> context = Class.forName("android.app.ActivityThread");
//            Method current = context.getMethod("currentApplication");
//            Object app = current.invoke(null);
//            return app.getClass().getMethod("getContentResolver").invoke(app);
//        } catch (Exception e) {
//            return null;
//        }
//    }
//
//    private static String getAndroidFallback() {
//        try {
//            // 尝试获取MAC地址
//            Class<?> wifiManager = Class.forName("android.net.wifi.WifiManager");
//            Object wm = getAndroidSystemService();
//            Method getInfo = wifiManager.getMethod("getConnectionInfo");
//            Object info = getInfo.invoke(wm);
//            String mac = (String) info.getClass().getMethod("getMacAddress").invoke(info);
//            return mac != null ? mac : generateHostHash();
//        } catch (Exception e) {
//            return generateHostHash();
//        }
//    }

    /* iOS实现（需JNI桥接） */
    private static boolean isIOS() {
        return System.getProperty("os.name", "").toLowerCase().contains("ios");
    }

    private static String getIOSIdentifier() {
        try {
            // 调用预编译的iOS原生桥接类
            Class<?> bridge = Class.forName("com.apple.DeviceBridge");
            Method getVendorId = bridge.getMethod("identifierForVendor");
            return (String) getVendorId.invoke(null);
        } catch (Exception e) {
            return "IOS_" + generateHostHash();
        }
    }

    /* 桌面平台实现 */
    private static String getDesktopIdentifier() {
        return sanitizeHex(getMacAddress()) + sanitizeHex(getSerialNumber());
    }

    private static String getMacAddress() {
        try {
            Enumeration<NetworkInterface> interfaces = NetworkInterface.getNetworkInterfaces();
            while (interfaces.hasMoreElements()) {
                NetworkInterface ni = interfaces.nextElement();
                if (isPhysicalNic(ni)) {
                    byte[] mac = ni.getHardwareAddress();
                    if (mac != null && mac.length == 6) {
                        return bytesToHex(mac);
                    }
                }
            }
        } catch (SocketException e) {
            // Ignored
        }
        return "00:00:00:00:00:00";
    }

    private static boolean isPhysicalNic(NetworkInterface ni) throws SocketException {
        if (ni.isLoopback() || !ni.isUp() || ni.isVirtual()) return false;
        String os = System.getProperty("os.name").toLowerCase();
        String name = ni.getDisplayName().toLowerCase();

        // Windows过滤虚拟适配器
        if (os.contains("win") && name.matches(".*(virtual|hyper-v|wan miniport).*"))
            return false;

        // Linux过滤容器接口
        return !os.contains("nux") || !name.matches(".*(docker|veth|br-).*");
    }

    private static String getSerialNumber() {
        try {
            if (System.getProperty("os.name").toLowerCase().contains("win")) {
                return getWindowsSerial();
            } else {
                return getUnixSerial();
            }
        } catch (Exception e) {
            return "SN" + generateHostHash();
        }
    }

    private static String getWindowsSerial() {
        try {
            Process proc = Runtime.getRuntime().exec(
                    new String[]{"wmic", "bios", "get", "serialnumber"});
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(proc.getInputStream()))) {
                String line;
                while ((line = reader.readLine()) != null) {
                    if (line.contains("SerialNumber")) {
                        return line.split("\\s+")[1];
                    }
                }
            }
        } catch (IOException e) {
            // Ignored
        }
        return "";
    }

    private static String getUnixSerial() {
        try {
            // 尝试读取DMI文件
            String sn = new BufferedReader(new FileReader("/sys/class/dmi/id/product_serial")).readLine().trim();
            if (!sn.isEmpty()) return sn;

            // 执行dmidecode命令
            Process proc = Runtime.getRuntime().exec(
                    new String[]{"dmidecode", "-s", "system-serial-number"});
            try (BufferedReader reader = new BufferedReader(
                    new InputStreamReader(proc.getInputStream()))) {
                return reader.readLine().trim();
            }
        } catch (IOException e) {
            return "";
        }
    }

    /* 通用工具方法 */
    private static String sanitizeHex(String input) {
        return input.replaceAll("[^A-Fa-f0-9]", "").toUpperCase();
    }

    private static String bytesToHex(byte[] bytes) {
        StringBuilder sb = new StringBuilder();
        for (byte b : bytes) {
            sb.append(String.format("%02X", b));
        }
        return sb.toString();
    }

    private static String generateHostHash() {
        String host = System.getenv().getOrDefault("COMPUTERNAME",
                System.getenv().getOrDefault("HOSTNAME", "unknown"));
        return Integer.toHexString(host.hashCode()).toUpperCase();
    }

    private static Object getAndroidSystemService() {
        try {
//            Class<?> context = Class.forName("android.content.Context");
            Class<?> activityThread = Class.forName("android.app.ActivityThread");
            Object app = activityThread.getMethod("currentApplication").invoke(null);
            Method getService = app.getClass().getMethod("getSystemService", String.class);
            return getService.invoke(app, "wifi");
        } catch (Exception e) {
            return null;
        }
    }

    // 测试用例
    public static void main(String[] args) {
        System.out.println("Device ID: " + generateDeviceId());
        System.out.println("Platform ID: " + getPlatformIdentifier());
    }
}