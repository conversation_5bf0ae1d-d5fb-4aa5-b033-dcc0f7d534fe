package com.xyy.saas.localserver.entity.enums.tenant;

import cn.iocoder.yudao.framework.common.core.IntArrayValuable;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Arrays;

/**
 * @Desc 门店类型（1-单店 2连锁门店 3连锁总部）
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum TenantTypeEnum implements IntArrayValuable {

    SINGLE_STORE(1, "单体门店"),
    CHAIN_STORE(2, "连锁门店"),
    CHAIN_HEADQUARTERS(3, "连锁总部"),

    ;

    private final int code;
    private final String desc;

    public static final int[] ARRAYS = Arrays.stream(values()).mapToInt(TenantTypeEnum::getCode).toArray();

    @Override
    public int[] array() {
        return ARRAYS;
    }

    public static TenantTypeEnum fromCode(Integer code) {
        return Arrays.stream(values())
            .filter(value -> value.getCode() == code)
            .findFirst()
            .orElse(null);
    }

    /**
     * 连锁门店必须关联总部
     * @return
     */
    public boolean requiredHeadquarters() {
        return this == CHAIN_STORE;
    }
}