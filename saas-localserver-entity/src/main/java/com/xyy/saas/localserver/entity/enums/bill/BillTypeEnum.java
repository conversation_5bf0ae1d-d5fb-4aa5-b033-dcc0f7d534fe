package com.xyy.saas.localserver.entity.enums.bill;

public enum BillTypeEnum {

    POSITION_MOVE(100, "HWYD", "货位移动"),

    INVENTORY_PLAN(101, "PD", "盘点"),

    PROFIT_LOSS(102, "SY", "报损报溢"),

    INVENTORY_UNBUNDLED(103, "CLD", "库存拆零"),

    QUALITY_REVIEW(104, "ZLFC", "质量复查"),

    PURCHASE_PLAN(200,"JHD", "采购计划单"),

    PURCHASE_ORDER(201,"CGD", "采购单"),

    STORE_REQUISITION(202,"YHD", "要货申请单"),

    STORE_ALLOCATION(203,"YHTJD", "要货调剂单"),

    HEADQUARTERS_DISTRIBUTION(204,"PHD", "总部铺货单"),

    PURCHASE_ORDER_RECEIVE(205,"SHD", "采购收货单"),

    REJECT_RECEIVE(206,"SHD", "拒收收货单"),

    RETURN_RECEIVE(207,"SHD", "退货收货单"),

    ALLOCATION_RECEIVE(208,"SHD", "调剂收货单"),

    REQUISITION_RECEIVE(209,"SHD", "要货收货单"),

    DISTRIBUTION_RECEIVE(210,"SHD", "要货收货单"),

    RETURN(211,"THD", "退货单"),

    STOCKOUT(212,"QHD", "缺货单"),

    OUTBOUND_ORDER(213,"CKD", "总部出库单"),
    ;

    public final int billType;
    public final String billNoPrefix;
    public final String billName;

    BillTypeEnum(int billType, String billNoPrefix, String billName) {
        this.billType = billType;
        this.billNoPrefix = billNoPrefix;
        this.billName = billName;
    }

    public static BillTypeEnum getByBillType(int billType) {
        for (BillTypeEnum billTypeEnum : BillTypeEnum.values()) {
            if (billTypeEnum.billType == billType) {
                return billTypeEnum;
            }
        }
        return null;
    }

}
