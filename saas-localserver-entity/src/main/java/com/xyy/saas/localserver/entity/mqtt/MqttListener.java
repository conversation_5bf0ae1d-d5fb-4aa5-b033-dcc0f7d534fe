package com.xyy.saas.localserver.entity.mqtt;

import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.IMqttDeliveryToken;
import org.eclipse.paho.client.mqttv3.MqttCallback;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Component
@ConditionalOnProperty(prefix = "mqtt", name = "enabled", havingValue = "true")
public class MqttListener implements MqttCallback {

    private final Map<String, MqttMessageHandler<?>> handlerMap = new ConcurrentHashMap<>();

    @Resource
    private MqttSubscriber mqttSubscriber;

    @Resource
    public void registerHandlers(List<MqttMessageHandler<?>> handlers) {
        handlers.forEach(handler -> {
            String messageType = handler.getMessageType();
            if (handlerMap.containsKey(messageType)) {
                log.error("[MQTT] Duplicate message type registration: {}", messageType);
                throw new IllegalStateException("Duplicate message type: " + messageType);
            }
            handlerMap.put(messageType, handler);
            log.info("[MQTT] Registered handler for type: {}", messageType);
        });
    }

    @Override
    public void connectionLost(Throwable cause) {
        log.error("[MQTT] 连接丢失", cause);
        // 调用正确的重试方法
        mqttSubscriber.immediateRetry();
    }

    @Override
    public void messageArrived(String topic, MqttMessage message) {
        try {
            MqttPayload mqttPayload = JsonUtils.parseObject(message.getPayload(), MqttPayload.class);
            String messageType = mqttPayload.getMessageType();

            MqttMessageHandler<?> handler = handlerMap.get(messageType);
            if (handler != null) {
                handleMessage(handler, topic, mqttPayload.getData());
            } else {
                log.warn("[MQTT] Unhandled message type: {} from topic: {}", messageType, topic);
            }
        } catch (Exception e) {
            log.error("[MQTT] Message processing failed. Topic: {}", topic, e);
        }
    }

    @Override
    public void deliveryComplete(IMqttDeliveryToken token) {
        log.debug("[MQTT] Message delivery complete");
    }

    private <T> void handleMessage(MqttMessageHandler<T> handler, String topic, Object rawData) {
        T data = JsonUtils.parseObject(JsonUtils.toJsonString(rawData), handler.getDataClass());
        try {
            handler.handle(topic, data);
            log.debug("[MQTT] Handled message: {} | Topic: {}", handler.getMessageType(), topic);
        } catch (Exception e) {
            log.error("[MQTT] Handler execution failed. Type: {} | Topic: {}", 
                     handler.getMessageType(), topic, e);
        }
    }
}