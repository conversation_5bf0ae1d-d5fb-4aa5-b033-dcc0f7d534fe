package com.xyy.saas.localserver.entity.mock;

import com.xyy.saas.localserver.entity.enums.tenant.TenantTypeEnum;
import com.xyy.saas.localserver.entity.pojo.TenantDto;

public class MockTenantUtil {

    // 模拟单体门店
    public static TenantDto mockSingleStore() {
        return TenantDto.builder()
                .id(10001L)
                .pref("STORE")
                .name("阳光大药房（单体店）")
                .type(TenantTypeEnum.SINGLE_STORE)
                .contactName("张经理")
                .contactMobile("13800000001")
                .province("浙江省")
                .city("杭州市")
                .area("西湖区")
                .address("文一西路100号")
                .status(0)
                .wzStatus(0)
                .zhlStatus(0)
                .build();
    }

    // 模拟连锁总部
    public static TenantDto mockHeadquarters() {
        return TenantDto.builder()
                .id(20000L)
                .pref("HQ")
                .name("康泰医药连锁总部")
                .type(TenantTypeEnum.CHAIN_HEADQUARTERS)
                .contactName("王总")
                .contactMobile("13900000002")
                .province("上海市")
                .city("上海市")
                .area("浦东新区")
                .address("陆家嘴金融中心1号")
                .status(0)
                .wzStatus(0)
                .zhlStatus(0)
                .build();
    }

    // 模拟连锁门店1（关联总部）
    public static TenantDto mockChainStore1() {
        return TenantDto.builder()
                .id(20001L)
                .pref("CS")
                .name("康泰医药连锁-西湖分店")
                .type(TenantTypeEnum.CHAIN_STORE)
                .headTenantId(20000L) // 关联总部ID
                .contactName("李店长")
                .contactMobile("13700000003")
                .province("浙江省")
                .city("杭州市")
                .area("西湖区")
                .address("学院路58号")
                .status(0)
                .wzStatus(0)
                .zhlStatus(0)
                .build();
    }

    // 模拟连锁门店2（关联总部）
    public static TenantDto mockChainStore2() {
        return TenantDto.builder()
                .id(20002L)
                .pref("CS")
                .name("康泰医药连锁-滨江分店")
                .type(TenantTypeEnum.CHAIN_STORE)
                .headTenantId(20000L) // 关联总部ID
                .contactName("陈经理")
                .contactMobile("13600000004")
                .province("浙江省")
                .city("杭州市")
                .area("滨江区")
                .address("江南大道888号")
                .status(0)
                .wzStatus(0)
                .zhlStatus(0)
                .build();
    }
}
