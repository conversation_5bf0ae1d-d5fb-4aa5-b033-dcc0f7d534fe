package com.xyy.saas.localserver.entity.mqtt;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.binary.Base64;
import org.eclipse.paho.client.mqttv3.*;
import org.eclipse.paho.client.mqttv3.persist.MemoryPersistence;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

@Data
@Configuration
@ConfigurationProperties(prefix = "mqtt")
@Slf4j
public class MqttConfig {
    private String serverUrl;
    private String accessKey;
    private String secretKey;
    private String parentTopic;
    /**
     * 保活时长
     */
    private Integer keepAliveInterval;

    /**
     * 超时时间
     */
    private Integer connectionTimeout;

    /**
     * 是否需要重连
     */
    private Boolean automaticReconnect;
    private String instanceId;
    private String groupId;
    private String clientId;

    /**
     * MQTT功能开关
     */
    private Boolean enabled = false;

    @Bean
    @ConditionalOnProperty(prefix = "mqtt", name = "enabled", havingValue = "true")
    public MqttClient mqttClient(MqttConfig config) {
        try {
            MqttClient client = new MqttClient(config.getServerUrl(), config.getClientId(), new MemoryPersistence());
            MqttConnectOptions options = buildConnectOptions(config);

            // 异步连接，避免阻塞启动
            try {
                client.connect(options);
                log.info("[MQTT] 客户端连接成功");
            } catch (Exception e) {
                log.warn("[MQTT] 初始连接失败，将在后续重试: {}", e.getMessage());
                // 不抛出异常，允许应用继续启动
            }
            return client;
        } catch (Exception e) {
            log.error("[MQTT] 客户端创建失败", e);
            throw new RuntimeException("MQTT客户端创建失败", e);
        }
    }

//    public static MqttClient createClient(MqttConfig config) throws MqttException {
////        String clientId = String.format("%s-%s-%s",
////                config.getClientId(),
////                TenantContextHolder.getTenantId(),
////                DeviceIdentifierGenerator.generateDeviceId());
////        return new MqttClient(config.getServerUrl(), clientId, new MemoryPersistence());
//        return new MqttClient(config.getServerUrl(), config.getClientId(), new MemoryPersistence());
//    }

    public static MqttConnectOptions buildConnectOptions(MqttConfig config) throws NoSuchAlgorithmException, InvalidKeyException {
        MqttConnectOptions options = new MqttConnectOptions();
        options.setUserName(getMqttUserName(config.getAccessKey(), config.getInstanceId()));
        options.setPassword(macSignature(config.getClientId(), config.getSecretKey()).toCharArray());
        options.setAutomaticReconnect(true);
        options.setCleanSession(true);
        options.setConnectionTimeout(config.getConnectionTimeout());
        options.setKeepAliveInterval(config.getKeepAliveInterval());
        return options;
    }

    private static String getMqttUserName(String accessKey, String instanceId){
        return "Signature|" + accessKey + "|" + instanceId;
    }

    /**
     * 根据数据加密连接mqtt服务密码
     * @param text 要签名的文本
     * @param secretKey 阿里云MQ secretKey
     * @return 加密后的字符串
     */
    private static String macSignature(String text, String secretKey) throws NoSuchAlgorithmException, InvalidKeyException {
        Charset charset = StandardCharsets.UTF_8;
        byte[] bytes = new byte[0];
        try {
            String algorithm = "HmacSHA1";
            Mac mac = Mac.getInstance(algorithm);
            mac.init(new SecretKeySpec(secretKey.getBytes(charset), algorithm));
            bytes = mac.doFinal(text.getBytes(charset));
        }catch (InvalidKeyException | NoSuchAlgorithmException e){
            log.error("获取加密签名异常：{}",e.getMessage());
            throw e;
        }
        return new String(Base64.encodeBase64(bytes), charset);
    }
}