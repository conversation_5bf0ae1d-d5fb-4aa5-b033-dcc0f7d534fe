package com.xyy.saas.localserver.entity.mqtt;

import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttMessage;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Service
@ConditionalOnProperty(prefix = "mqtt", name = "enabled", havingValue = "true")
public class MqttServer {
    @Resource private MqttConfig config;
    @Resource private MqttClient client;
    
    private final BlockingQueue<RetryMessage> retryQueue = new LinkedBlockingQueue<>();

    // 新增指数退避重试逻辑
    private ScheduledExecutorService retryScheduler;
    private final AtomicInteger retryCount = new AtomicInteger(0);

//    @PostConstruct
//    public void init() {
//        if (!config.getServerEnabled()) {
//            log.info("[MQTT] 服务端未启用");
//            return;
//        }
//
//        try {
//            client.connect(MqttConfig.buildConnectOptions(config));
//            log.info("[MQTT] 服务端初始化成功");
//        } catch (Exception e) {
//            log.error("[MQTT] 服务端初始化失败", e);
//            startRetrySchedule(); // 修正方法调用
//        }
//    }

    // 重命名并重构重试逻辑
    private void startRetrySchedule() {
        if (retryScheduler != null && !retryScheduler.isShutdown()) {
            return;
        }

        retryScheduler = java.util.concurrent.Executors.newSingleThreadScheduledExecutor();
        retryCount.set(0);

        Runnable retryTask = () -> {
            try {
                if (retryCount.get() > 5) {
                    log.error("[MQTT] 超过最大重试次数");
                    retryScheduler.shutdown();
                    return;
                }

                if (!client.isConnected()) {
                    client.connect(MqttConfig.buildConnectOptions(config));
                    log.info("[MQTT] 第{}次重连成功", retryCount.get());
                    retryScheduler.shutdown();
                }
            } catch (Exception e) {
                long delay = (long) Math.pow(2, retryCount.getAndIncrement()) * 1000;
                log.warn("[MQTT] 第{}次重连失败，{}秒后重试", retryCount.get(), delay/1000);
                retryScheduler.schedule(this::startRetrySchedule, delay, TimeUnit.MILLISECONDS);
            }
        };

        retryScheduler.schedule(retryTask, 1, TimeUnit.SECONDS);
    }

    // 保持原有消息发送方法
    public void sendToDevice(long tenantId, int deviceId, Object data, String messageType) {
        String topic = String.format("%s/%d/%d", config.getParentTopic(), tenantId, deviceId);
        publish(topic, buildPayload(tenantId, deviceId, data, messageType), 1);
    }

    public void sendToTenant(long tenantId, Object data, String messageType) {
        String topic = String.format("%s/%d", config.getParentTopic(), tenantId);
        publish(topic, buildPayload(tenantId, null, data, messageType), 1);
    }

    // 保持原有广播方法
    public void broadcast(Object data, String messageType) {
        publish(config.getParentTopic() + "/broadcast", buildPayload(null, null, data, messageType), 0);
    }

    private void publish(String topic, MqttPayload payload, int qos) {
        try {
            MqttMessage message = new MqttMessage();
            message.setPayload(JsonUtils.toJsonByte(payload));
            message.setQos(qos);
            client.publish(topic, message);
            log.debug("[MQTT] 消息发送成功: {}", topic);
        } catch (Exception e) {
            retryQueue.offer(new RetryMessage(topic, payload, qos));
            log.warn("[MQTT] 消息进入重试队列: {}", topic);
            startRetrySchedule(); // 发送失败时触发重连
        }
    }

    // 保持定时重试逻辑
    @Scheduled(fixedRate = 10_000)
    private void processRetries() {
        while (!retryQueue.isEmpty()) {
            RetryMessage msg = retryQueue.poll();
            try {
                client.publish(msg.topic, createMessage(msg.payload, msg.qos));
                log.info("[MQTT] 重试成功: {}", msg.topic);
            } catch (Exception e) {
                log.error("[MQTT] 重试失败: {}", msg.topic, e);
                retryQueue.offer(msg); // 重新加入队列
            }
        }
    }

    // 保持辅助方法
    private MqttMessage createMessage(MqttPayload payload, int qos) {
        MqttMessage message = new MqttMessage();
        message.setPayload(JsonUtils.toJsonByte(payload));
        message.setQos(qos);
        return message;
    }

    private MqttPayload buildPayload(Long tenantId, Integer deviceId, Object data, String messageType) {
        return MqttPayload.builder()
                .messageType(messageType)
                .data(data)
                .tenantId(tenantId)
                .deviceId(deviceId)
                .timestamp(System.currentTimeMillis())
                .build();
    }

    // 保持内部记录类
    private record RetryMessage(String topic, MqttPayload payload, int qos) {}
}