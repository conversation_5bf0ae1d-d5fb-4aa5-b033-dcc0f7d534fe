//package com.xyy.saas.localserver.entity.mock.pojo.product;
//
//import io.swagger.v3.oas.annotations.media.Schema;
//import io.swagger.v3.oas.annotations.media.Schema.RequiredMode;
//import lombok.Builder;
//import lombok.Data;
//import java.io.Serializable;
//import java.math.BigDecimal;
//import java.time.LocalDate;
//import java.time.LocalDateTime;
//
//@Data
//@Builder
//public class ProductInfoDto implements Serializable {
//
//
//    @Schema(description = "商品编号", requiredMode = RequiredMode.REQUIRED, example = "21893")
//    private Long id;
//
//    @Schema(description = "多租户编号", requiredMode = RequiredMode.REQUIRED, example = "21893")
//    private Long tenantId;
//
//    @Schema(description = "多租户编号", requiredMode = RequiredMode.REQUIRED, example = "21893")
//    private Long headTenantId;
//
//    @Schema(description = "商品编码", requiredMode = RequiredMode.REQUIRED)
//    private String pref;
//
//    @Schema(description = "商品外码（自动生成或填写，机构唯一）", requiredMode = RequiredMode.REQUIRED)
//    private String showPref;
//
//    @Schema(description = "源商品编码", requiredMode = RequiredMode.REQUIRED)
//    private String sourceProductPref;
//
//    @Schema(description = "源商品外码", requiredMode = RequiredMode.REQUIRED)
//    private String sourceProductShowPref;
//
//    @Schema(description = "中台标准库ID", requiredMode = RequiredMode.REQUIRED, example = "21163")
//    private Long midStdlibId;
//
//    @Schema(description = "标准库ID", requiredMode = RequiredMode.REQUIRED, example = "21163")
//    private Long stdlibId;
//
//    @Schema(description = "助记码(商品外码、品牌名、通用名、生产厂家、规格、条码、标准库id、批准文号 这些字段用“|”分隔用于查询使用)", requiredMode = RequiredMode.REQUIRED)
//    private String mnemonicCode;
//
//    @Schema(description = "通用名", requiredMode = RequiredMode.REQUIRED, example = "李四")
//    private String commonName;
//
//    @Schema(description = "品牌名", requiredMode = RequiredMode.REQUIRED, example = "王五")
//    private String brandName;
//
//    @Schema(description = "规格/型号", requiredMode = RequiredMode.REQUIRED)
//    private String spec;
//
//    @Schema(description = "条形码（基本单位条形码）", requiredMode = RequiredMode.REQUIRED)
//    private String barcode;
//
//    @Schema(description = "生产厂家", requiredMode = RequiredMode.REQUIRED)
//    private String manufacturer;
//
//    @Schema(description = "批准文号", requiredMode = RequiredMode.REQUIRED)
//    private String approvalNumber;
//
//    @Schema(description = "商品图片链接")
//    private String images;
//
//    @Schema(description = "最小包装数量")
//    private BigDecimal minPackageNum;
//
//    @Schema(description = "单位", requiredMode = RequiredMode.REQUIRED, example = "23148")
//    private Long unitId;
//
//    @Schema(description = "剂型", requiredMode = RequiredMode.REQUIRED, example = "21146")
//    private Long dosageFormId;
//
//    @Schema(description = "所属范围", requiredMode = RequiredMode.REQUIRED, example = "6843")
//    private Long businessScopeId;
//
//    @Schema(description = "商品分类", requiredMode = RequiredMode.REQUIRED, example = "12764")
//    private Long productCategoryId;
//
//    /**
//     * 商品六级分类
//     */
//    private String firstCategory;
//    private String secondCategory;
//    private String thirdCategory;
//    private String fourthCategory;
//    private String fiveCategory;
//    private String sixCategory;
//
//    @Schema(description = "处方分类", requiredMode = RequiredMode.REQUIRED, example = "8689")
//    private Long presCategoryId;
//
//    @Schema(description = "储存条件", requiredMode = RequiredMode.REQUIRED, example = "1588")
//    private Long storageWayId;
//
//    @Schema(description = "产地", requiredMode = RequiredMode.REQUIRED)
//    private String origin;
//
//    @Schema(description = "生产企业社会信用代码", requiredMode = RequiredMode.REQUIRED)
//    private String manufacturerUscc;
//
//    @Schema(description = "有效期,例12个月,36个月", requiredMode = RequiredMode.REQUIRED)
//    private String productValidity;
//
//    @Schema(description = "批准文号有效期")
//    private LocalDate approvalValidityPeriod;
//
//    @Schema(description = "上市许可持有人", requiredMode = RequiredMode.REQUIRED)
//    private String marketingAuthorityHolder;
//
//    @Schema(description = "用法用量")
//    private String usageDosage;
//
//    @Schema(description = "药品标识码", requiredMode = RequiredMode.REQUIRED)
//    private String drugIdentCode;
//
//    @Schema(description = "进项税率")
//    private BigDecimal inputTaxRate;
//
//    @Schema(description = "销项税率")
//    private BigDecimal outputTaxRate;
//
//    @Schema(description = "备注", requiredMode = RequiredMode.REQUIRED, example = "随便")
//    private String remark;
//
//    @Schema(description = "拆零数量", requiredMode = RequiredMode.REQUIRED, example = "100")
//    private Integer unbundledQuantity;
//    @Schema(description = "状态", requiredMode = RequiredMode.REQUIRED, example = "1")
//    private Integer status;
//    @Schema(description = "是否禁用", requiredMode = RequiredMode.REQUIRED, example = "2")
//    private Boolean disable;
//    @Schema(description = "删除时间", requiredMode = RequiredMode.REQUIRED, example = "2025-01-01 00:00:00")
//    private LocalDateTime deletedAt;
//    @Schema(description = "删除类型", requiredMode = RequiredMode.REQUIRED, example = "1")
//    private Integer deleteType;
//
//    @Schema(description = "创建时间", requiredMode = RequiredMode.REQUIRED, example = "2025-01-01 00:00:00")
//    private LocalDateTime createTime;
//    @Schema(description = "更新时间", requiredMode = RequiredMode.REQUIRED, example = "2025-01-01 00:00:00")
//    private LocalDateTime updateTime;
//    @Schema(description = "创建人ID", requiredMode = RequiredMode.REQUIRED, example = "1")
//    private Long creator;
//    @Schema(description = "创建人名称", requiredMode = RequiredMode.REQUIRED, example = "1")
//    private String creatorName;
//    @Schema(description = "更新人ID", requiredMode = RequiredMode.REQUIRED, example = "1")
//    private Long updater;
//    @Schema(description = "更新人名称", requiredMode = RequiredMode.REQUIRED, example = "1")
//    private String updaterName;
//
//
//    @Schema(description = "多属性标志")
//    private Long multiFlag;
//
//    // 标准库信息
//     @Schema(description = "标准库信息", requiredMode = RequiredMode.REQUIRED, example = "{}")
//     private ProductStdlibDto stdlib;
//
//    // 使用信息（价格，医保匹配信息）
//    @Schema(description = "使用信息（价格，医保匹配信息）", requiredMode = RequiredMode.REQUIRED, example = "{}")
//    private ProductUseInfoDto useInfo;
//
//
//
//}