//package com.xyy.saas.localserver.entity.pojo.product;
//
//import io.swagger.v3.oas.annotations.media.Schema;
//import lombok.Builder;
//import lombok.Data;
//
//@Schema(description = "单据详情 - 扩展信息 - 商品快照 DTO")
//@Data
//@Builder
//public class ProductInfoDTO {
//
//    /**
//     * 商品图片地址（支持多个URL，用逗号分隔）
//     */
//    private String imgUrl;
//
//    /**
//     * 处方分类
//     * 值集：OTC(非处方)、RX(处方)、HERBAL(中药) 等
//     */
//    private String presCategory;
//
//    /**
//     * 品牌名称（如：白云山、同仁堂）
//     */
//    private String brandName;
//
//    /**
//     * 通用名（药典标准名称，如：阿莫西林胶囊）
//     */
//    private String commonName;
//
//    /**
//     * 规格（如：0.5g*12片/盒）
//     */
//    private String spec;
//
//    /**
//     * 单位（如：盒、瓶、袋）
//     */
//    private String unit;
//
//    /**
//     * 剂型（如：片剂、胶囊剂、口服液）
//     */
//    private String dosageForm = "";
//
//    /**
//     * 商品条码（国际条码，如：6928804010012）
//     */
//    private String barcode;
//
//    /**
//     * 批准文号（药监批准文号，如：国药准字*********）
//     */
//    private String approvalNumber;
//
//    /**
//     * 生产厂家全称（如：华润三九医药股份有限公司）
//     */
//    private String manufacturer;
//
//    /**
//     * 标准库ID（关联标准药品库）
//     */
//    private String stdlibId;
//
//    /**
//     * 商品外部编码（对接第三方系统编码）
//     */
//    private String showPref;
//
//}