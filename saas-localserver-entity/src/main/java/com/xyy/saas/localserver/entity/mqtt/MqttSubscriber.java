package com.xyy.saas.localserver.entity.mqtt;

import com.xyy.saas.datasync.client.constants.DataContextHolder;
import com.xyy.saas.localserver.entity.config.db.DeviceIdentifierGenerator;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.eclipse.paho.client.mqttv3.MqttClient;
import org.eclipse.paho.client.mqttv3.MqttException;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

@Slf4j
@Component
@ConditionalOnProperty(prefix = "mqtt", name = "enabled", havingValue = "true")
public class MqttSubscriber {
    @Resource private MqttConfig config;
    @Resource private MqttClient client;
    
    private Long tenantId;
    private Integer deviceId;
    private String[] subscribedTopics;
    private int[] qosLevels;

    @PostConstruct
    public void init() {
        if (!config.getEnabled()) {
            log.info("[MQTT] Subscriber功能已禁用");
            return;
        }

        try {
            this.tenantId = Long.valueOf(DataContextHolder.getTenantId());
            this.deviceId = DeviceIdentifierGenerator.generateDeviceId();
            initSubscriptionSettings();

            // 异步订阅，避免阻塞启动
            asyncSubscribe();
        } catch (Exception e) {
            log.warn("[MQTT] 初始化失败，将在后续重试: {}", e.getMessage());
            scheduleDelayedRetry();
        }
    }

    private void asyncSubscribe() {
        try {
            if (client.isConnected()) {
                client.subscribe(subscribedTopics, qosLevels);
                log.info("[MQTT] 订阅成功: {}", Arrays.toString(subscribedTopics));
            } else {
                log.warn("[MQTT] 客户端未连接，延迟订阅");
                scheduleDelayedRetry();
            }
        } catch (MqttException e) {
            log.error("[MQTT] 订阅失败", e);
            scheduleDelayedRetry();
        }
    }

    private void initSubscriptionSettings() {
        subscribedTopics = new String[]{
            String.format("%s/%d/+", config.getParentTopic(), tenantId),
            String.format("%s/%d/%d", config.getParentTopic(), tenantId, deviceId),
            String.format("%s/broadcast", config.getParentTopic())
        };
        qosLevels = new int[]{0, 2, 1};
    }

    public void scheduleDelayedRetry() {
        ScheduledExecutorService scheduler = Executors.newSingleThreadScheduledExecutor();
        scheduler.schedule(() -> {
            try {
                if (!client.isConnected()) {
                    client.connect(MqttConfig.buildConnectOptions(config));
                    client.subscribe(subscribedTopics, qosLevels);
                    log.info("[MQTT] 延迟重连成功");
                }
            } catch (Exception e) {
                log.error("[MQTT] 延迟重连失败", e);
            }
        }, 30, TimeUnit.SECONDS);
    }

    // 新增立即重试方法
    public void immediateRetry() {
        try {
            if (!client.isConnected()) {
                client.connect(MqttConfig.buildConnectOptions(config));
                client.subscribe(subscribedTopics, qosLevels);
                log.info("[MQTT] 立即重连成功");
            }
        } catch (Exception e) {
            log.error("[MQTT] 立即重连失败", e);
            scheduleDelayedRetry();
        }
    }
}