package cn.iocoder.yudao.module.system.dal.mysql.appversion;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.controller.admin.appversion.vo.AppVersionDetailPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.appversion.AppVersionDetailDO;
import org.apache.ibatis.annotations.Mapper;


/**
 * app版本用户详情 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface AppVersionDetailMapper extends BaseMapperX<AppVersionDetailDO> {

    default PageResult<AppVersionDetailDO> selectPage(AppVersionDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<AppVersionDetailDO>()
            .eqIfPresent(AppVersionDetailDO::getAppVersionId, reqVO.getAppVersionId())
            .eqIfPresent(AppVersionDetailDO::getBussnissType, reqVO.getBussnissType())
            .eqIfPresent(AppVersionDetailDO::getUserId, reqVO.getUserId())
            .betweenIfPresent(AppVersionDetailDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(AppVersionDetailDO::getId));
    }

    void deleteGrayTenantByVersionId(Integer versionId);

}