package cn.iocoder.yudao.module.system.framework.operatelog.core;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.module.system.dal.dataobject.dept.DeptDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.service.dept.DeptService;
import cn.iocoder.yudao.module.system.service.tenant.TenantService;
import com.mzt.logapi.service.IParseFunction;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 门店名字的 {@link IParseFunction} 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class TenantParseFunction implements IParseFunction {

    public static final String NAME = "getTenant";

    @Resource
    private TenantService tenantService;

    @Override
    public String functionName() {
        return NAME;
    }

    @Override
    public String apply(Object value) {
        if (StrUtil.isEmptyIfStr(value) || Convert.toLong(value) == null) {
            return "";
        }

        // 获取门店信息
        TenantDO tenant = tenantService.getTenant(Convert.toLong(value));
        if (tenant == null) {
            log.warn("[apply][获取部门{{}}为空", value);
            return "";
        }
        return tenant.getName();
    }

}
