package cn.iocoder.yudao.module.system.job.pharmacist;

import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import com.xyy.saas.inquiry.pharmacist.api.pharmacist.InquiryPharmacistApi;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class SaasMigrationJob implements JobHandler {

    @DubboReference
    private InquiryPharmacistApi inquiryPharmacistApi;

    @Override
    public String execute(String param) {
        log.info("定时任务处理老系统迁移 - 开始");
        inquiryPharmacistApi.jobHandSaasMigration();
        log.info("定时任务处理老系统迁移 - 结束");
        return "";
    }

}
