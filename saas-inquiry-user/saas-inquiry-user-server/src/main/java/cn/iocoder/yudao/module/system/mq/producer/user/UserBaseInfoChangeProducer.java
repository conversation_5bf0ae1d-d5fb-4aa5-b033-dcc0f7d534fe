package cn.iocoder.yudao.module.system.mq.producer.user;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.mq.user.UserBaseInfoChangeEvent;
import org.springframework.stereotype.Component;

/**
 * @Desc 用户基础信息修改producer
 * <AUTHOR>
 */
@Component
@EventBusProducer(
    topic = UserBaseInfoChangeEvent.TOPIC
)
public class UserBaseInfoChangeProducer extends EventBusRocketMQTemplate {

}
