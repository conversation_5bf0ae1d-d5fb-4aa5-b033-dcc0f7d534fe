package cn.iocoder.yudao.module.system.service.tenant.servicepack;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantServicePackRelationReqDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationBatchChangeVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationDetailRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantRespVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantTransmissionServicePackRelationDO;
import com.xyy.saas.inquiry.pojo.catalog.CatalogRelationTenantDto;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;

/**
 * 门店-开通服务包关系 Service 接口
 *
 * <AUTHOR>
 */
public interface TenantTransmissionServicePackRelationService {

    /**
     * 查询绑定机构的门店数
     *
     * @param organIds 机构列表
     * @param status   状态 {@link com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum}
     * @return 机构-门店数
     */
    Map<Integer, Long> selectCountByOrgans(List<Integer> organIds, Integer status);

    /**
     * 查询开通服务包的门店数
     *
     * @param servicePackIds 服务包列表
     * @param status         状态 {@link com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum}
     * @return 服务包-门店数
     */
    Map<Integer, Long> selectCountByServicePacks(List<Integer> servicePackIds, Integer status);


    /**
     * 查询开通服务包的门店数
     *
     * @param catalogIds 目录版本列表
     * @param status     状态 {@link com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum}
     * @return 服务包-门店数
     */
    Map<Long, Long> selectCountByCatalogIds(List<Long> catalogIds, Integer status);

    /**
     * 根据条件查询服务包列表
     *
     * @param reqVO 入参条件
     * @return 服务包列表
     */
    List<TenantTransmissionServicePackRelationDO> selectByCondition(TenantServicePackRelationPageReqVO reqVO);

    /**
     * 根据条件查询一条门店开通关系 selectTenantServicePack
     *
     * @param reqVO
     * @return
     */
    TenantTransmissionServicePackRelationDO selectTenantServicePack(TenantServicePackRelationPageReqVO reqVO);

    /**
     * 获得门店-开通服务包关系
     *
     * @param tenantId 编号
     * @return 门店-开通服务包关系
     */
    TenantServicePackRelationDetailRespVO getTenantTransmissionServicePackRelation(Long tenantId);

    /**
     * 获得门店-开通服务包关系分页
     *
     * @param pageReqVO 分页查询
     * @return 门店-开通服务包关系分页
     */
    PageResult<TenantServicePackRelationRespVO> getTenantTransmissionServicePackRelationPage(TenantServicePackRelationPageReqVO pageReqVO);


    /**
     * 创建门店-开通服务包关系
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createTenantTransmissionServicePackRelation(@Valid TenantServicePackRelationSaveReqVO createReqVO);

    /**
     * 批量切换服务包
     *
     * @param changeVO
     * @return
     */
    Integer batchChangeServicePack(TenantServicePackRelationBatchChangeVO changeVO);

    /**
     * 批量切换服务包目录
     *
     * @param changeVO
     * @return
     */
    Integer batchChangeServiceCatalog(TenantServicePackRelationBatchChangeVO changeVO);

    /**
     * 分页查询租户信息
     *
     * @param reqDto
     * @return
     */
    PageResult<CatalogRelationTenantDto> getCatalogRelationTenantPage(TenantServicePackRelationReqDto reqDto);


    PageResult<TenantRespVO> getTransmissionServicePackRelationTenantPage(TenantServicePackRelationPageReqVO pageReqVO);
}