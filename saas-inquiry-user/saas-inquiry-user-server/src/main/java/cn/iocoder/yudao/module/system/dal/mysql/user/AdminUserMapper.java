package cn.iocoder.yudao.module.system.dal.mysql.user;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserQueryReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserRespVO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xyy.saas.inquiry.enums.user.UserAccountStatusEnum;
import java.util.Collection;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface AdminUserMapper extends BaseMapperX<AdminUserDO> {

    default AdminUserDO selectByUsername(String username) {
        //return selectOne(AdminUserDO::getUsername, username);
        return selectOne(new LambdaQueryWrapperX<AdminUserDO>().eq(AdminUserDO::getUsername, username).ne(AdminUserDO::getStatus, UserAccountStatusEnum.QUIT.getCode()));
    }

    default AdminUserDO selectByEmail(String email) {
        return selectOne(AdminUserDO::getEmail, email);
    }

    default AdminUserDO selectByMobile(String mobile) {
        return selectOne(new LambdaQueryWrapperX<AdminUserDO>().eq(AdminUserDO::getMobile, mobile).ne(AdminUserDO::getStatus, UserAccountStatusEnum.QUIT.getCode()));
        // return getUserByMobileStore(mobile, null);
    }

    default List<AdminUserDO> selectListByNickname(String nickname) {
        return selectList(new LambdaQueryWrapperX<AdminUserDO>().like(AdminUserDO::getNickname, nickname));
    }

    default List<AdminUserDO> selectListByDeptIds(Collection<Long> deptIds) {
        return selectList(AdminUserDO::getDeptId, deptIds);
    }


    IPage<UserRespVO> selectUserPageStore(Page<UserRespVO> objectPage, UserPageReqVO pageReqVO);

    List<AdminUserDO> selectUserListStore(UserQueryReqVO queryReqVO);

    List<UserRespVO> selectUserTenantInfo(@Param("userIds") List<Long> userIds);

    IPage<UserRespVO> selectUserPageSystem(Page<Object> objectPage, UserPageReqVO pageReqVO);

    AdminUserDO getUserByMobileStore(String mobile, Long tenantId);

    /**
     * 获取门店可用的用户列表
     *
     * @param userIds
     * @return
     */
    List<UserRespVO> getTenantAvailableUserList(@Param("userIds") List<Long> userIds);
}
