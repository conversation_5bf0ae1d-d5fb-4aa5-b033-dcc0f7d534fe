package cn.iocoder.yudao.module.system.dal.dataobject.user;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 门店员工指纹 DO
 *
 * <AUTHOR>
 */
@TableName(value = "system_tenant_user_finger_print", autoResultMap = true)
@KeySequence("system_tenant_user_finger_print_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantUserFingerPrintDO extends BaseDO {

    /**
     * 指纹ID
     */
    @TableId
    private Long id;

    /**
     * 门店id
     */
    private Long tenantId;

    /**
     * 用户ID
     */
    private Long userId;
    /**
     * 指纹信息
     */
    private String fingerPrintInfo;
    /**
     * 生产厂商
     */
    private String manufacturer;
    /**
     * 设备id
     */
    private String deviceId;

}