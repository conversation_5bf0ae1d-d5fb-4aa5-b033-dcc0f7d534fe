package cn.iocoder.yudao.module.system.service.tenant.ybm;

import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

import java.util.Map;

/**
 * @ClassName：YbmClient
 * @Author: dongjiajun
 * @Date: 2024/11/28 10:09
 * @Description: 调Ybm接口
 */
@HttpExchange(accept = "application/json", contentType = "application/json")
public interface YbmWebClient {

    @PostExchange("/{apiPath}")
    String callApi(@PathVariable("apiPath") String apiPath, @RequestBody Map<String, Object> params);
}
