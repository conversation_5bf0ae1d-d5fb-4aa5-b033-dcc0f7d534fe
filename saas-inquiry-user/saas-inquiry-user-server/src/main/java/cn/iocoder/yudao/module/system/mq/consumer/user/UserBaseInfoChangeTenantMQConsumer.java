package cn.iocoder.yudao.module.system.mq.consumer.user;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantMapper;
import cn.iocoder.yudao.module.system.dal.mysql.user.AdminUserMapper;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.mq.user.UserBaseInfoChangeEvent;
import com.xyy.saas.inquiry.mq.user.UserBaseInfoDto;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @Desc 用户信息修改, 暂时不关联门店信息, 注释掉
 * <AUTHOR>
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "cn_iocoder_yudao_module_system_mq_consumer_user_UserBaseInfoChangeTenantMQConsumer",
    topic = UserBaseInfoChangeEvent.TOPIC)
public class UserBaseInfoChangeTenantMQConsumer {

    @Resource
    private TenantMapper tenantMapper;

    @Resource
    private AdminUserMapper userMapper;

    @EventBusListener
    public void tenantUserBaseInfoUpdateMQConsumer(UserBaseInfoChangeEvent userBaseInfoChangeEvent) {
        // UserBaseInfoDto userBaseInfoDto = userBaseInfoChangeEvent.getMsg();
        // if (userBaseInfoDto == null || userBaseInfoDto.getUserId() == null) {
        //     return;
        // }
        // List<TenantDO> tenantList = tenantMapper.selectList(TenantDO::getContactUserId, userBaseInfoDto.getUserId());
        // if (CollUtil.isEmpty(tenantList)) {
        //     return;
        // }
        // AdminUserDO userBaseInfo = userMapper.selectById(userBaseInfoDto.getUserId());
        //
        // List<TenantDO> updateList = tenantList.stream().filter(t -> !StringUtils.equals(userBaseInfo.getNickname(), t.getContactName())
        //         || !StringUtils.equals(userBaseInfo.getMobile(), t.getContactMobile()))
        //     .map(t -> TenantDO.builder().id(t.getId()).contactName(userBaseInfo.getNickname()).contactMobile(userBaseInfo.getMobile()).build()).toList();
        // if (CollUtil.isNotEmpty(updateList)) {
        //     log.info("用户基础信息修改-门店管理员,userId:{},nickName:{}", userBaseInfo.getId(), userBaseInfoDto.getNickname());
        //     tenantMapper.updateBatch(updateList);
        // }
    }

}
