package cn.iocoder.yudao.module.member.controller.admin.auth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.List;

@Schema(description = "用户 PC - 智慧脸删除对应用户 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ZhlDeleteAccountReqVO implements Serializable {

    @Schema(description = "问诊用户openid", requiredMode = Schema.RequiredMode.REQUIRED, example = "123")
    @NotEmpty(message = "问诊用户openid不能为空")
    private List<String> openidList;

}
