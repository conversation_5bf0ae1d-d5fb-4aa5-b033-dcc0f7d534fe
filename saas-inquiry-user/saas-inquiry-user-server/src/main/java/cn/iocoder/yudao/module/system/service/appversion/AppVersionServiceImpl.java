package cn.iocoder.yudao.module.system.service.appversion;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.APP_VERSION_GRAY_TENANT_NOT_EXISTS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.APP_VERSION_INNER_VERSION_ERROR;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.APP_VERSION_NOT_EXISTS;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.system.controller.admin.appversion.vo.AppVersionPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.appversion.vo.AppVersionRespVO;
import cn.iocoder.yudao.module.system.controller.admin.appversion.vo.AppVersionSaveReqVO;
import cn.iocoder.yudao.module.system.controller.app.appversion.vo.CheckAppVersionReqVO;
import cn.iocoder.yudao.module.system.controller.app.appversion.vo.IgnoreUpgradeReqVO;
import cn.iocoder.yudao.module.system.convert.appversion.AppVersionConvert;
import cn.iocoder.yudao.module.system.convert.appversion.AppVersionDetailConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.appversion.AppVersionDO;
import cn.iocoder.yudao.module.system.dal.dataobject.appversion.AppVersionDetailDO;
import cn.iocoder.yudao.module.system.dal.mysql.appversion.AppVersionDetailMapper;
import cn.iocoder.yudao.module.system.dal.mysql.appversion.AppVersionMapper;
import cn.iocoder.yudao.module.system.service.appversion.strategy.AppVersionMatchStrategy;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xyy.saas.inquiry.enums.system.AppVersionDetailBussnissTypeEnum;
import com.xyy.saas.inquiry.enums.system.AppVersionUpgradeScopeEnum;
import com.xyy.saas.inquiry.pojo.TenantDto;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;


/**
 * App版本 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class AppVersionServiceImpl implements AppVersionService {

    @Resource
    private AppVersionMapper appVersionMapper;

    @Resource
    private AppVersionDetailMapper appVersionDetailMapper;

    @Resource
    private TenantApi tenantApi;

    /**
     * 自动开方判定策略
     */
    private static final Map<Integer, AppVersionMatchStrategy> matchStrategyMap = new HashMap<>();

    @Autowired
    public void initHandler(List<AppVersionMatchStrategy> strategies) {
        strategies.forEach(strategy -> matchStrategyMap.put(strategy.getUpgradeScope().getCode(), strategy));
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Integer createAppVersion(AppVersionSaveReqVO createReqVO) {
        // 检查版本号
        checkAppVersion(createReqVO);
        // 插入
        AppVersionDO appVersion = AppVersionConvert.INSTANCE.convertVO2DO(createReqVO);
        appVersionMapper.insert(appVersion);
        //非指定用户灰度场景直接返回
        if(ObjectUtil.notEqual(AppVersionUpgradeScopeEnum.SPECIFIC.getCode(), createReqVO.getUpgradeScope())){
            //
            return appVersion.getId();
        }
        // 指定用户灰度场景需判断是否有灰度租户
        if(CollectionUtils.isEmpty(createReqVO.getGrayTenant())){
            throw exception(APP_VERSION_GRAY_TENANT_NOT_EXISTS);
        }
        appVersionDetailMapper.insertBatch(AppVersionDetailConvert.INSTANCE.convertVO2DO(appVersion,createReqVO.getGrayTenant()));
        return appVersion.getId();
    }

    Boolean checkAppVersion(AppVersionSaveReqVO createReqVO) {
        // 根据业务线+系统类型查询，并根据appVersionCode降序排列取第一条
        AppVersionDO appVersionDO = appVersionMapper.selectOne(Wrappers.<AppVersionDO>lambdaQuery()
            .eq(AppVersionDO::getAppBiz, createReqVO.getAppBiz())
            .eq(AppVersionDO::getOsType, createReqVO.getOsType())
            .orderByDesc(AppVersionDO::getAppVersionCode).last("LIMIT 1"));
        if(ObjectUtil.isNotNull(appVersionDO) && appVersionDO.getAppVersionCode() >= createReqVO.getAppVersionCode()){
            throw exception(APP_VERSION_INNER_VERSION_ERROR);
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateAppVersion(AppVersionSaveReqVO updateReqVO) {
        // 校验存在
        AppVersionDO versionDO = appVersionMapper.selectById(updateReqVO.getId());
        if(ObjectUtil.isNull(versionDO)){
            throw exception(APP_VERSION_NOT_EXISTS);
        }
        // 如果历史为指定用户灰度场景，先删除灰度租户
        if(ObjectUtil.equals(AppVersionUpgradeScopeEnum.SPECIFIC.getCode(), versionDO.getUpgradeScope())){
            appVersionDetailMapper.deleteGrayTenantByVersionId(updateReqVO.getId());
        }
        // 更新
        AppVersionDO updateObj = BeanUtils.toBean(updateReqVO, AppVersionDO.class);
        appVersionMapper.updateById(updateObj);

        // 如果为指定用户灰度场景，插入灰度租户
        if(ObjectUtil.equals(AppVersionUpgradeScopeEnum.SPECIFIC.getCode(), updateReqVO.getUpgradeScope()) && !CollectionUtils.isEmpty(updateReqVO.getGrayTenant())){
            appVersionDetailMapper.insertBatch(AppVersionDetailConvert.INSTANCE.convertVO2DO(updateObj,updateReqVO.getGrayTenant()));
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteAppVersion(Integer id) {
        // 校验存在
        validateAppVersionExists(id);
        // 删除
        appVersionMapper.deleteById(id);
        appVersionDetailMapper.deleteGrayTenantByVersionId(id);
    }

    private void validateAppVersionExists(Integer id) {
        if (appVersionMapper.selectById(id) == null) {
            throw exception(APP_VERSION_NOT_EXISTS);
        }
    }

    @Override
    public AppVersionRespVO getAppVersion(Integer id) {
        AppVersionDO versionDO = appVersionMapper.selectById(id);
        if(ObjectUtil.notEqual(versionDO.getUpgradeScope(), AppVersionUpgradeScopeEnum.SPECIFIC.getCode())){
            return AppVersionConvert.INSTANCE.convert(versionDO);
        }
        List<AppVersionDetailDO> detailList = appVersionDetailMapper.selectList(AppVersionDetailDO::getAppVersionId,versionDO.getId(), AppVersionDetailDO::getBussnissType, AppVersionDetailBussnissTypeEnum.GRAY_TENANT.getCode());
        if(CollectionUtils.isEmpty(detailList)){
            return AppVersionConvert.INSTANCE.convert(versionDO);
        }
        List<TenantDto> dtoList = tenantApi.getTenantList(detailList.stream().map(AppVersionDetailDO::getTenantId).distinct().toList());
        return AppVersionConvert.INSTANCE.convertWithGaryTenant(versionDO,dtoList);
    }

    @Override
    public PageResult<AppVersionDO> getAppVersionPage(AppVersionPageReqVO pageReqVO) {
        return appVersionMapper.selectPage(pageReqVO);
    }

    /**
     * 检查App版本升级
     *
     * @param reqVO 入参
     * @return App版本
     */
    @Override
    public AppVersionRespVO checkAppVersionUpgrade(CheckAppVersionReqVO reqVO) {
        //根据传入业务类型 + 系统类型  查询当前最新的版本
        List<AppVersionDO> versionDOList = appVersionMapper.selectList(AppVersionPageReqVO.builder().osType(reqVO.getOsType()).disable(Boolean.FALSE).appBiz(reqVO.getAppBiz()).build());
        if(CollectionUtils.isEmpty(versionDOList)){
            return null;
        }
        //判断是否已是最新版本
        if(reqVO.getVersionCode().equals(versionDOList.getFirst().getAppVersionCode())){
            return null;
        }
        // 定义符合的版本
        AppVersionDO versionDO = null;
        // 遍历所有比当前版本大的版本，判断是否满足升级条件
        for (AppVersionDO appVersionDO : versionDOList) {
            // 版本池版本小于当前版本时，直接跳出
            if(appVersionDO.getAppVersionCode() <= reqVO.getVersionCode()){
                break;
            }
            // 获取匹配策略
            AppVersionMatchStrategy matchStrategy = matchStrategyMap.get(appVersionDO.getUpgradeScope());
            if(ObjectUtils.isEmpty(matchStrategy)){
                continue;
            }
            versionDO =  matchStrategy.match(appVersionDO, reqVO);
            //获取到符合版本时，直接返回
            if(!ObjectUtils.isEmpty(versionDO)){
                break;
            }
        }
        return ObjectUtils.isEmpty(versionDO)? null : AppVersionConvert.INSTANCE.convertDO2RespVO(versionDO);
    }

    /**
     * 忽略App版本升级
     *
     * @param reqVO 入参
     * @return App版本
     */
    @Override
    public Boolean ignoreUpgrade(IgnoreUpgradeReqVO reqVO) {
        return appVersionDetailMapper.insert(AppVersionDetailDO.builder()
            .appVersionId(reqVO.getVersionId())
            .bussnissType(AppVersionDetailBussnissTypeEnum.USER_IGNORE_VERSION.getCode())
            .userId(reqVO.getUserId()).build()) > 0;
    }

}