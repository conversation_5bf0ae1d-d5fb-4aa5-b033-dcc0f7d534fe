package cn.iocoder.yudao.module.system.api.permission;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.module.system.api.permission.dto.RoleRespDTO;
import cn.iocoder.yudao.module.system.convert.premission.RoleConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO;
import cn.iocoder.yudao.module.system.service.permission.RoleService;
import jakarta.annotation.Resource;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * 角色 API 实现类
 *
 * <AUTHOR>
 */
@Service
public class RoleApiImpl implements RoleApi {

    @Resource
    private RoleService roleService;

    @Override
    public void validRoleList(Collection<Long> ids) {
        roleService.validateRoleList(ids);
    }

    @Override
    public List<RoleRespDTO> selectRoleListByIds(List<Long> roleIdList) {
        final List<RoleDO> roleList = roleService.getRoleList(roleIdList);
        return RoleConvert.INSTANCE.convertUseRole(roleList);
    }

    @Override
    public List<RoleRespDTO> getSystemRoleIdByCodes(List<String> codes) {
        if (CollUtil.isEmpty(codes)) {
            return null;
        }
        List<RoleDO> roleDOS = roleService.selectByCodes(new HashSet<>(codes));
        return RoleConvert.INSTANCE.convertUseRole(roleDOS);
    }
}
