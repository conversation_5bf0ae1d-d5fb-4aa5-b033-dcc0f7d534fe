package cn.iocoder.yudao.module.system.dal.mysql.tenant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.share.TenantPackageShareRelationPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageShareRelationDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 总部门店套餐共享 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantPackageShareRelationMapper extends BaseMapperX<TenantPackageShareRelationDO> {

    default PageResult<TenantPackageShareRelationDO> selectPage(TenantPackageShareRelationPageReqVO reqVO) {
        return selectPage(reqVO, getQueryWrapper(reqVO));
    }

    default List<TenantPackageShareRelationDO> selectList(TenantPackageShareRelationPageReqVO reqVO) {
        return selectList(getQueryWrapper(reqVO));
    }

    default Long selectCount(TenantPackageShareRelationPageReqVO reqVO) {
        return selectCount(getQueryWrapper(reqVO));
    }


    private static LambdaQueryWrapperX<TenantPackageShareRelationDO> getQueryWrapper(TenantPackageShareRelationPageReqVO reqVO) {
        return new LambdaQueryWrapperX<TenantPackageShareRelationDO>()
            .eqIfPresent(TenantPackageShareRelationDO::getHeadTenantId, reqVO.getHeadTenantId())
            .eqIfPresent(TenantPackageShareRelationDO::getBizType, reqVO.getBizType())
            .eqIfPresent(TenantPackageShareRelationDO::getTenantId, reqVO.getTenantId())
            .inIfPresent(TenantPackageShareRelationDO::getTenantId, reqVO.getTenantIds())
            .eqIfPresent(TenantPackageShareRelationDO::getTenantPackageId, reqVO.getTenantPackageId())
            .betweenIfPresent(TenantPackageShareRelationDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(TenantPackageShareRelationDO::getId);
    }


}