package cn.iocoder.yudao.module.system.service.permission;

import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthPermissionInfoRespVo;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.MenuDO;
import com.xyy.saas.inquiry.enums.system.ClientTypeEnum;
import java.util.List;

/**
 * 权限菜单 Service 接口
 * <p>
 * 提供用户-角色、角色-菜单、关联权限处理
 *
 * <AUTHOR>
 */
public interface MenuPermissionService {

    /**
     * 获取用户某一个端的菜单角色权限
     *
     * @param client 端
     * @return
     */
    AuthPermissionInfoRespVo getMenuPermissionInfo(ClientTypeEnum client);

    /**
     * 获取门店管理用户的菜单角色权限
     *
     * @return
     */
    List<MenuDO> getAdminMenuPermissionInfo();


}
