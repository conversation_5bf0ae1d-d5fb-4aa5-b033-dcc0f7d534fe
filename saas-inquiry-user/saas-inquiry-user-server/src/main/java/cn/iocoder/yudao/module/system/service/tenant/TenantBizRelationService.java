package cn.iocoder.yudao.module.system.service.tenant;

import java.util.*;
import jakarta.validation.*;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.biz.*;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantBizRelationDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;

/**
 * 门店业务关系 Service 接口
 *
 * <AUTHOR>
 */
public interface TenantBizRelationService {

    /**
     * 创建门店业务关系
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTenantBizRelation(@Valid TenantBizRelationSaveReqVO createReqVO);

    /**
     * 更新门店业务关系
     *
     * @param updateReqVO 更新信息
     */
    void updateTenantBizRelation(@Valid TenantBizRelationSaveReqVO updateReqVO);

    /**
     * 批量创建或更新门店业务关系
     *
     * @param bizRelations
     */
    List<TenantBizRelationDO> createOrUpdateTenantBizRelations(List<TenantBizRelationSaveReqVO> bizRelations);

    /**
     * 批量创建或更新门店业务关系
     *
     * @param bizRelations
     */
    List<TenantBizRelationDO> batchCreateOrUpdateTenantBizRelations(List<TenantBizRelationSaveReqVO> bizRelations);

    /**
     * 删除门店业务关系
     *
     * @param id 编号
     */
    void deleteTenantBizRelation(Long id);

    /**
     * 获得门店业务关系
     *
     * @param id 编号
     * @return 门店业务关系
     */
    TenantBizRelationDO getTenantBizRelation(Long id);

    /**
     * 获得门店业务关系列表
     *
     * @param tenantId 租户
     * @return 门店业务关系分页
     */
    List<TenantBizRelationRespVO> getTenantBizRelationList(Long tenantId);


    /**
     * 获取问诊业务线门店类型
     *
     * @param tenantId
     * @return
     */
    TenantBizRelationDO getWzTenantType(Long tenantId);

    /**
     * 获取问诊业务线门店类型
     *
     * @param tenantIds
     * @return
     */
    List<TenantBizRelationDO> getWzTenantType(List<Long> tenantIds);
}