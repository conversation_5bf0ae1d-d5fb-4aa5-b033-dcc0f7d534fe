package cn.iocoder.yudao.module.system.service.tenant.ybm.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.Data;

/**
 * @Description: 商户基本信息
 * @Auther: WanKp
 * @Date: 2018/8/18 11:49
 **/
@Data
public class YbmAccountDto implements Serializable {

    /** 主键ID */
    private Long Id;

    /** 手机号 */
    private String mobile;

    /** 密码 */
    private String password;

    /** 账号状态：0-冻结，1-有效，2-注销 */
    private Integer status;

    /** 账号类型：1-普通账户，2-测试账号 */
    private Integer type;

    /** 注册来源：2-ios，1-android，3-H5，4-pc，5-saas pc，6-saas ios，7-雨诺注册，8-saas 安卓 */
    private Integer registerSource;

    /** 联系人姓名 */
    private String contactName;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 实名认证信息 */
    private String identityInfo;

    /** 异地登录检测开关：0-关闭，1-开启 */
    private Integer checkSwitch;

    /** 账号常登录省份列表 */
    private List<String> oftenLoginProvinces;

    /** 微信UnionID */
    private String unionid;

    /** 微信OpenID */
    private String openid;

    /** 苹果账号ID */
    private String appleId;

    /** 是否强制修改密码 */
    private Integer updatePassword;
}
