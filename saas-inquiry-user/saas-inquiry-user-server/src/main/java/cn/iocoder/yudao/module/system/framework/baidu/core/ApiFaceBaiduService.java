package cn.iocoder.yudao.module.system.framework.baidu.core;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception0;

import cn.iocoder.yudao.module.system.framework.baidu.dto.FaceResultDto;
import cn.iocoder.yudao.module.system.framework.baidu.dto.FaceResultMatchDto;
import cn.iocoder.yudao.module.system.framework.baidu.dto.FaceSearchUserDto;
import com.alibaba.fastjson.JSONObject;
import com.baidu.aip.face.AipFace;
import java.util.HashMap;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author:chenxiaoyi
 * @Date:2025/08/06 15:35
 */
@Component
@Slf4j
public class ApiFaceBaiduService {

    private static final String IMAGE_TYPE = "BASE64";

    // @Value("${baidu.api.face.group-id}")
    private String faceGroupId;


    // @Resource
    private AipFace aipFace;

    /**
     * 搜索用户
     *
     * @param faceBase64
     */
    public void search(String faceBase64) {
        org.json.JSONObject res = aipFace.search(faceBase64, IMAGE_TYPE, faceGroupId, new HashMap<>(0));
        FaceSearchUserDto faceResult = getFaceResultDto(res, FaceSearchUserDto.class);
    }

    /**
     * 添加用户
     *
     * @param userId
     * @param faceBase64
     * @return
     */
    public String addUser(String userId, String faceBase64) {
        org.json.JSONObject res = aipFace.addUser(faceBase64, IMAGE_TYPE, faceGroupId, userId, new HashMap<>(0));
        FaceResultDto faceResult = getFaceResultDto(res, FaceResultDto.class);
        return faceResult.getFace_token();
    }

    /**
     * 删除 用户
     *
     * @param userId
     */
    public void deleteUser(String userId) {
        org.json.JSONObject res = aipFace.deleteUser(faceGroupId, userId, new HashMap<>(0));
        getFaceResultDto(res, FaceResultDto.class);
    }

    /**
     * 人脸匹配
     *
     * @param faceBase64
     * @param registerBase64
     * @return
     */
    public double faceMingJingMatch(String faceBase64, String registerBase64) {
        org.json.JSONObject res = aipFace.faceMingJingMatch(faceBase64, IMAGE_TYPE, registerBase64, IMAGE_TYPE, new HashMap<>(0));
        FaceResultMatchDto faceResult = getFaceResultDto(res, FaceResultMatchDto.class);
        if (faceResult.getResult() == null) {
            return 0.0;
        }
        return faceResult.getResult().getScore();
    }

    /**
     * 处理sdk结果
     *
     * @param res   sdk结果
     * @param clazz 结果类
     * @param <T>   结果类
     * @return
     */
    private static <T extends FaceResultDto> T getFaceResultDto(org.json.JSONObject res, Class<T> clazz) {
        T faceResult = JSONObject.parseObject(res.toString(), clazz);
        if (faceResult.getError_code() != 0) {
            throw exception0(faceResult.getError_code().intValue(), faceResult.getError_msg());
        }
        return faceResult;
    }

}
