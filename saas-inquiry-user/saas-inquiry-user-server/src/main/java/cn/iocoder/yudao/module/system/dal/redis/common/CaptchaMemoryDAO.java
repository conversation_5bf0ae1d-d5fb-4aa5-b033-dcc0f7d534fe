//package cn.iocoder.yudao.module.system.dal.redis.common;
//
//import cn.hutool.cache.Cache;
//import cn.hutool.cache.CacheUtil;
//import cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
//import org.springframework.data.redis.core.StringRedisTemplate;
//import org.springframework.stereotype.Repository;
//
//import java.time.Duration;
//import java.util.HashMap;
//import java.util.Map;
//
//import static cn.iocoder.yudao.module.system.dal.redis.RedisKeyConstants.CAPTCHA_CODE;
//
///**
// * @Desc 验证码的 Memory DAO @<NAME_EMAIL> @Date Created in 2023/07/14 11:10
// */
//@Repository
//public class CaptchaMemoryDAO extends CaptchaRedisDAO {
//
//    private Cache<String, String> captchaMap = CacheUtil.newLRUCache(10);
//
//    public String get(String uuid) {
//        String redisKey = formatKey(uuid);
//        return captchaMap.get(redisKey);
//    }
//
//    public void set(String uuid, String code, Duration timeout) {
//        String redisKey = formatKey(uuid);
//        captchaMap.put(redisKey, code, timeout.toMillis());
//    }
//
//    public void delete(String uuid) {
//        String redisKey = formatKey(uuid);
//        captchaMap.remove(redisKey);
//    }
//
//    private static String formatKey(String uuid) {
//        return String.format(CAPTCHA_CODE.getKeyTemplate(), uuid);
//    }
//
//}
//
