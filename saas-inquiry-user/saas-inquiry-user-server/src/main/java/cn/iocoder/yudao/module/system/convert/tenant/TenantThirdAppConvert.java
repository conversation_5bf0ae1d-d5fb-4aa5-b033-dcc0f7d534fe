package cn.iocoder.yudao.module.system.convert.tenant;

import cn.iocoder.yudao.module.system.api.tenant.dto.TenantThirdAppRespDto;
import com.xyy.saas.inquiry.user.server.dal.dataobject.tenant.TenantThirdAppDO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface TenantThirdAppConvert {

    TenantThirdAppConvert INSTANCE = Mappers.getMapper(TenantThirdAppConvert.class);

    List<TenantThirdAppRespDto> convertDoList2RespDtoList(List<TenantThirdAppDO> tenantThirdAppDOList);

}
