package cn.iocoder.yudao.module.system.convert.auth;

import cn.iocoder.yudao.module.system.api.social.dto.SocialUserReqDTO;
import cn.iocoder.yudao.module.system.dal.dataobject.social.SocialUserDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface SocialUserConvert {

    SocialUserConvert INSTANCE = Mappers.getMapper(SocialUserConvert.class);

    SocialUserDO convertReqDTO2DO(SocialUserReqDTO reqDTO);

}
