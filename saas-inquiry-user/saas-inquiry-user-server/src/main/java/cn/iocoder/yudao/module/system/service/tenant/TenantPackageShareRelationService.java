package cn.iocoder.yudao.module.system.service.tenant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageReqDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.share.TenantPackageShareRelationPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.share.TenantPackageShareRelationRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.share.TenantPackageShareRelationSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageShareRelationDO;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 总部门店套餐共享 Service 接口
 *
 * <AUTHOR>
 */
public interface TenantPackageShareRelationService {

    /**
     * 创建总部门店套餐共享
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTenantPackageShareRelation(@Valid TenantPackageShareRelationSaveReqVO createReqVO);

    /**
     * 更新总部门店套餐共享
     *
     * @param updateReqVO 更新信息
     */
    void updateTenantPackageShareRelation(@Valid TenantPackageShareRelationSaveReqVO updateReqVO);

    /**
     * 删除总部门店套餐共享
     *
     * @param id 编号
     */
    void deleteTenantPackageShareRelation(List<Long> ids);

    /**
     * 获得总部门店套餐共享
     *
     * @param id 编号
     * @return 总部门店套餐共享
     */
    TenantPackageShareRelationDO getTenantPackageShareRelation(Long id);

    /**
     * 获得总部门店套餐共享分页
     *
     * @param pageReqVO 分页查询
     * @return 总部门店套餐共享分页
     */
    PageResult<TenantPackageShareRelationRespVO> getTenantPackageShareRelationPage(TenantPackageShareRelationPageReqVO pageReqVO);

    /**
     * 获取门店被总部共享的套餐
     *
     * @param tenantId
     * @return
     */
    List<TenantPackageShareRelationDO> getTenantPackageShareRelationList(Long tenantId, BizTypeEnum bizTypeEnum);

    /**
     * 获取总部门店分享套餐列表
     *
     * @param reqDto
     * @return
     */
    List<TenantPackageRelationRespVO> getTenantSharePackageRelationList(TenantPackageReqDto reqDto);

    /**
     * 解绑总部-门店套餐关系
     *
     * @param id           门店id
     * @param headTenantId 总部id
     */
    void unBindTenantPackageShareRelation(Long id, Long headTenantId);
}