package cn.iocoder.yudao.module.system.service.biz;

import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.service.impl.DiffParseFunction;
import com.mzt.logapi.starter.annotation.LogRecord;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;

import cn.iocoder.yudao.module.system.controller.admin.biz.vo.*;
import cn.iocoder.yudao.module.system.dal.dataobject.biz.BizDO;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;

import cn.iocoder.yudao.module.system.dal.mysql.biz.BizMapper;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.BIZ_NOT_EXISTS;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_BIZ_UPDATE_SUB_TYPE;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_BIZ_UPDATE_SUCCESS;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_BIZ;

/**
 * 业务 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class BizServiceImpl implements BizService {

    @Resource
    private BizMapper bizMapper;

    @Override
    public Long createBiz(BizSaveReqVO createReqVO) {
        // 插入
        BizDO biz = BeanUtils.toBean(createReqVO, BizDO.class);
        bizMapper.insert(biz);
        // 返回
        return biz.getId();
    }

    @LogRecord(type = SYSTEM_BIZ, subType = SYSTEM_BIZ_UPDATE_SUB_TYPE, bizNo = "{{#updateReqVO.id}}",
        success = SYSTEM_BIZ_UPDATE_SUCCESS)
    @Override
    public void updateBiz(BizSaveReqVO updateReqVO) {
        // 校验存在
        BizDO biz = validateBizExists(updateReqVO.getId());
        // 更新
        BizDO updateObj = BeanUtils.toBean(updateReqVO, BizDO.class);
        bizMapper.updateById(updateObj);

        LogRecordContext.putVariable(DiffParseFunction.OLD_OBJECT, BeanUtils.toBean(biz, BizSaveReqVO.class));
        LogRecordContext.putVariable("biz", biz);
    }

    @Override
    public void deleteBiz(Long id) {
        // 校验存在
        validateBizExists(id);
        // 删除
        bizMapper.deleteById(id);
    }

    private BizDO validateBizExists(Long id) {
        BizDO biz = bizMapper.selectById(id);
        if (biz == null) {
            throw exception(BIZ_NOT_EXISTS);
        }
        return biz;
    }

    @Override
    public BizDO getBiz(Long id) {
        return bizMapper.selectById(id);
    }

    @Override
    public List<BizDO> getBizList(BizListReqVO reqVO) {
        return bizMapper.selectList(reqVO);
    }

}