package cn.iocoder.yudao.module.system.service.tenant;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_BIZ_RELATION_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.biz.TenantBizRelationRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.biz.TenantBizRelationSaveReqVO;
import cn.iocoder.yudao.module.system.convert.tenant.TenantBizRelationConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantBizRelationDO;
import cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantBizRelationMapper;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantUserRelationDO;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 门店业务关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TenantBizRelationServiceImpl implements TenantBizRelationService {

    @Resource
    private TenantBizRelationMapper tenantBizRelationMapper;

    @Override
    public Long createTenantBizRelation(TenantBizRelationSaveReqVO createReqVO) {
        // 插入
        TenantBizRelationDO tenantBizRelation = BeanUtils.toBean(createReqVO, TenantBizRelationDO.class);
        tenantBizRelationMapper.insert(tenantBizRelation);
        // 返回
        return tenantBizRelation.getId();
    }

    @Override
    public void updateTenantBizRelation(TenantBizRelationSaveReqVO updateReqVO) {
        // 校验存在
        validateTenantBizRelationExists(updateReqVO.getId());
        // 更新
        TenantBizRelationDO updateObj = BeanUtils.toBean(updateReqVO, TenantBizRelationDO.class);
        tenantBizRelationMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TenantBizRelationDO> createOrUpdateTenantBizRelations(List<TenantBizRelationSaveReqVO> bizRelations) {
        if (CollUtil.isEmpty(bizRelations)) {
            return List.of();
        }
        List<TenantBizRelationDO> tenantBizRelationDOS = TenantBizRelationConvert.INSTANCE.convertVo2Do(bizRelations);

        Map<Integer, TenantBizRelationDO> relationDOMap = tenantBizRelationMapper.getTenantBizRelationList(bizRelations.getFirst().getTenantId()).stream()
            .collect(Collectors.toMap(TenantBizRelationDO::getBizType, Function.identity(), (a, b) -> b));
        // 判断存在先删除再新增
        for (TenantBizRelationDO relationDO : tenantBizRelationDOS) {
            if (relationDOMap.containsKey(relationDO.getBizType())) {
                tenantBizRelationMapper.deleteId(relationDOMap.get(relationDO.getBizType()).getId());
            }
        }

        tenantBizRelationMapper.insert(tenantBizRelationDOS);
        // tenantBizRelationMapper.insertOrUpdate(tenantBizRelationDOS);
        // bizRelations.forEach(bizRelation -> {
        //     tenantBizRelationMapper.update(new UpdateWrapper<TenantBizRelationDO>().set("head_tenant_id", bizRelation.getHeadTenantId()).eq("id", bizRelation.getId()));
        // });
        return tenantBizRelationDOS;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public List<TenantBizRelationDO> batchCreateOrUpdateTenantBizRelations(List<TenantBizRelationSaveReqVO> bizRelations) {
        if (CollUtil.isEmpty(bizRelations)) {
            return List.of();
        }
        List<TenantBizRelationDO> tenantBizRelationDOS = TenantBizRelationConvert.INSTANCE.convertVo2Do(bizRelations);
        tenantBizRelationDOS.forEach(relation ->{
            if(relation.getId()== null){
                TenantBizRelationDO relationDO = tenantBizRelationMapper.getBizRelation(relation.getBizType(), relation.getTenantId());
                if (relationDO != null) {
                    relation.setId(relationDO.getId());
                }
            }
        });
        tenantBizRelationMapper.insertOrUpdate(tenantBizRelationDOS);
        bizRelations.forEach(bizRelation -> {
            tenantBizRelationMapper.update(new UpdateWrapper<TenantBizRelationDO>().set("head_tenant_id", bizRelation.getHeadTenantId()).eq("id", bizRelation.getId()));
        });
        return tenantBizRelationDOS;
    }

    @Override
    public void deleteTenantBizRelation(Long id) {
        // 校验存在
        validateTenantBizRelationExists(id);
        // 删除
        tenantBizRelationMapper.deleteById(id);
    }

    private void validateTenantBizRelationExists(Long id) {
        if (tenantBizRelationMapper.selectById(id) == null) {
            throw exception(TENANT_BIZ_RELATION_NOT_EXISTS);
        }
    }

    @Override
    public TenantBizRelationDO getTenantBizRelation(Long id) {
        return tenantBizRelationMapper.selectById(id);
    }

    @Override
    public List<TenantBizRelationRespVO> getTenantBizRelationList(Long tenantId) {
        List<TenantBizRelationDO> list = tenantBizRelationMapper.getTenantBizRelationList(tenantId);
        return TenantBizRelationConvert.INSTANCE.convertDo2Vo(list);
    }

    @Override
    public TenantBizRelationDO getWzTenantType(Long tenantId) {
        List<TenantBizRelationDO> list = tenantBizRelationMapper.getTenantBizRelationList(tenantId);
        TenantBizRelationDO tenantBizRelationDO = list.stream().filter(item -> item.getBizType().equals(BizTypeEnum.HYWZ.getCode())).findFirst().orElse(new TenantBizRelationDO());
        return tenantBizRelationDO;
    }

    @Override
    public List<TenantBizRelationDO> getWzTenantType(List<Long> tenantIds) {
        return tenantBizRelationMapper.getBizRelationList(BizTypeEnum.HYWZ.getCode(), tenantIds);

    }

}