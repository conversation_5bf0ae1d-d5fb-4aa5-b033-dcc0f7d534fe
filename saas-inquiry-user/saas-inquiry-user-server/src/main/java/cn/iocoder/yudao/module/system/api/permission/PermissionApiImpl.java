package cn.iocoder.yudao.module.system.api.permission;

import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.system.api.permission.dto.DeptDataPermissionRespDTO;
import cn.iocoder.yudao.module.system.api.permission.dto.RoleRespDTO;
import cn.iocoder.yudao.module.system.convert.premission.RoleConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO;
import cn.iocoder.yudao.module.system.service.permission.PermissionService;
import cn.iocoder.yudao.module.system.service.permission.RoleService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Set;

/**
 * 权限 API 实现类
 *
 * <AUTHOR>
 */
@Service
public class PermissionApiImpl implements PermissionApi {

    @Resource
    private PermissionService permissionService;

    @Resource
    private RoleService roleService;

    @Override
    public Set<Long> getUserRoleIdListByRoleIds(Collection<Long> roleIds) {
        return permissionService.getUserRoleIdListByRoleId(roleIds);
    }

    @Override
    public boolean hasAnyPermissions(Long userId, String... permissions) {
        return permissionService.hasAnyPermissions(userId, permissions);
    }

    @Override
    public boolean hasAnyRoles(Long userId, String... roles) {
        return permissionService.hasAnyRoles(userId, roles);
    }

    @Override
    public DeptDataPermissionRespDTO getDeptDataPermission(Long userId) {
        return permissionService.getDeptDataPermission(userId);
    }

    @Override
    public void assignUserRoleWithRoleRanges(Long userId, Set<Long> roleIds, Set<Long> roleRangeIds) {
        permissionService.assignUserRoleWithRoleRanges(userId, roleIds, roleRangeIds);
    }

    @Override
    public void assignUserRoleWithSystemRoleCode(Long userId, String roleCode) {
        permissionService.assignUserRoleWithSystemRoleCodes(userId, Collections.singletonList(roleCode));
    }

    @Override
    @TenantIgnore
    public List<RoleRespDTO> selectUserRoleByUserIdRoleCodes(Long userId, Set<String> roleCodes) {
        List<RoleDO> roleDOS = permissionService.selectUserRoleByUserIdRoleCodes(userId, roleCodes);
        return RoleConvert.INSTANCE.convertUseRole(roleDOS);
    }

    @Override
    public List<RoleRespDTO> selectWzCadRole() {
        return RoleConvert.INSTANCE.convertUseRole(roleService.selectWzCadRole());
    }

    @Override
    public void deleteUserSystemRole(Long userId, Long tenantId, String roleCode) {
        permissionService.deleteUserSystemRole(userId, tenantId, roleCode);
    }
}
