package cn.iocoder.yudao.module.system.dal.dataobject.biz;

import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import lombok.*;
import java.util.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import lombok.experimental.Accessors;

/**
 * 业务 DO
 *
 * <AUTHOR>
 */
@TableName(value = "system_biz", autoResultMap = true)
@KeySequence("system_biz_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@Accessors(chain = true)
public class BizDO extends BaseDO {

    /**
     * 业务ID
     */
    @TableId
    private Long id;
    /**
     * 类型
     */
    private Integer type;
    /**
     * 共享类型
     */
    private Integer shareType;
    /**
     * 名称
     */
    private String name;
    /**
     * 角色id
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private Set<Long> headRoleIds;
    /**
     * 角色id
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private Set<Long> storeRoleIds;
    /**
     * 菜单id
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private Set<Long> menuIds;
    /**
     * 备注
     */
    private String remark;

}