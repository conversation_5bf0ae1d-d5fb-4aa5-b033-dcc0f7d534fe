package cn.iocoder.yudao.module.system.service.member;

import cn.hutool.core.util.ClassUtil;
import cn.hutool.core.util.ReflectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.module.member.api.user.MemberUserApi;
import cn.iocoder.yudao.module.member.api.user.dto.MemberUserRespDTO;
import cn.iocoder.yudao.module.member.dal.dataobject.user.MemberUserDO;
import cn.iocoder.yudao.module.member.dal.mysql.user.MemberUserMapper;
import cn.iocoder.yudao.module.member.service.user.MemberUserService;
import jakarta.annotation.Resource;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * Member Service 实现类
 *
 * <AUTHOR>
 */
@Service
public class MemberServiceImpl implements MemberService {

    @Resource
    private MemberUserMapper memberUserMapper;

    @Override
    public String getMemberUserMobile(Long id) {
        MemberUserDO user = getMemberUser(id);
        if (user == null) {
            return null;
        }
        return user.getMobile();
    }

    @Override
    public String getMemberUserEmail(Long id) {
        return null;
    }

    private MemberUserDO getMemberUser(Long id) {
        if (id == null) {
            return null;
        }
        return memberUserMapper.selectById(id);
    }


}
