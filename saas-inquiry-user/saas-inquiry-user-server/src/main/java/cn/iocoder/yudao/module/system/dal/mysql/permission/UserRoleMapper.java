package cn.iocoder.yudao.module.system.dal.mysql.permission;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.controller.admin.permission.vo.permission.UserRoleVO;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.UserRoleDO;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Mapper
public interface UserRoleMapper extends BaseMapperX<UserRoleDO> {

    default List<UserRoleDO> selectListByUserId(Long userId) {
        return selectList(UserRoleDO::getUserId, userId);
    }

    default void deleteListByUserIdAndRoleIdIds(Long userId, Collection<Long> roleIds) {
        delete(new LambdaQueryWrapper<UserRoleDO>()
            .eq(UserRoleDO::getUserId, userId)
            .in(UserRoleDO::getRoleId, roleIds));
    }

    default void deleteListByUserId(Long userId) {
        delete(new LambdaQueryWrapper<UserRoleDO>().eq(UserRoleDO::getUserId, userId));
    }

    default void deleteListByRoleId(Long roleId) {
        delete(new LambdaQueryWrapper<UserRoleDO>().eq(UserRoleDO::getRoleId, roleId));
    }

    default List<UserRoleDO> selectListByRoleIds(Collection<Long> roleIds) {
        return selectList(UserRoleDO::getRoleId, roleIds);
    }

    default List<UserRoleDO> selectListByUserIdRoleIds(Long userId, Set<Long> roleIds) {
        return selectList(new LambdaQueryWrapperX<UserRoleDO>().eq(UserRoleDO::getUserId, userId).in(UserRoleDO::getRoleId, roleIds));
    }


    List<RoleDO> selectUserRoleByUserIdRoleCodes(@Param("userId") Long userId, @Param("roleCodes") Set<String> roleCodes);


    void deleteListByUserIdAndRoleIdTenantIds(@Param("userId") Long userId, @Param("tenantIds") List<Long> tenantIds, @Param("roleIds") List<Long> roleIds);

    List<UserRoleVO> getUserRoleByUserIds(@Param("tenantId") Long tenantId, @Param("userIds") Collection<Long> userIds);

    default List<UserRoleDO> getUserListByRoleIds(Set<Long> roleIds) {
        return selectList(UserRoleDO::getRoleId, roleIds);
    }

}
