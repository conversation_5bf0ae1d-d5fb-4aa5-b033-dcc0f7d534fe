package cn.iocoder.yudao.module.system.service.tenant;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_PACKAGE_SHARE_RELATION_NOT_EXISTS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_PACKAGE_SHARE_RELATION_TENANT_TYPE_FAIL;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageReqDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.share.TenantPackageShareRelationPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.share.TenantPackageShareRelationRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.share.TenantPackageShareRelationSaveReqVO;
import cn.iocoder.yudao.module.system.convert.tenant.TenantPackageShareConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageShareRelationDO;
import cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantPackageShareRelationMapper;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 总部门店套餐共享 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TenantPackageShareRelationServiceImpl implements TenantPackageShareRelationService {

    @Resource
    private TenantPackageShareRelationMapper tenantPackageShareRelationMapper;

    @Resource
    private TenantService tenantService;

    @Resource
    private TenantPackageRelationService tenantPackageRelationService;


    @Override
    public Long createTenantPackageShareRelation(TenantPackageShareRelationSaveReqVO createReqVO) {
        // 校验总部类型
        createReqVO.setHeadTenantId(Optional.ofNullable(createReqVO.getHeadTenantId()).orElse(TenantContextHolder.getRequiredTenantId()));

        TenantDO headTenant = tenantService.validTenant(createReqVO.getHeadTenantId());

        if (!Objects.equals(headTenant.getType(), TenantTypeEnum.CHAIN_HEADQUARTERS.getCode())) {
            throw exception(TENANT_PACKAGE_SHARE_RELATION_TENANT_TYPE_FAIL, headTenant.getName());
        }

        List<TenantDO> tenantList = tenantService.getTenantList(createReqVO.getTenantIds());

        // 校验门店类型+总部id
        tenantList.stream().filter(tenant -> !Objects.equals(tenant.getWzTenantType(), TenantTypeEnum.CHAIN_STORE.getCode())
            || !Objects.equals(headTenant.getId(), tenant.getHeadTenantId())).findAny().ifPresent(tenant -> {
            throw exception(TENANT_PACKAGE_SHARE_RELATION_TENANT_TYPE_FAIL, tenant.getName());
        });

        //校验门店套餐订单
        tenantPackageRelationService.validateTenantPackageRelationExists(createReqVO.getTenantPackageId());

        // 判断关系是否存在
        Map<Long, TenantPackageShareRelationDO> relationDOMap = tenantPackageShareRelationMapper.selectList(TenantPackageShareConvert.INSTANCE.convert(createReqVO))
            .stream().collect(Collectors.toMap(TenantPackageShareRelationDO::getTenantId, Function.identity(), (a, b) -> b));

        createReqVO.setTenantIds(createReqVO.getTenantIds().stream().filter(a -> !relationDOMap.containsKey(a)).toList());

        // 不存在则新增
        List<TenantPackageShareRelationDO> shareRelationDOS = TenantPackageShareConvert.INSTANCE.convertDos(createReqVO);
        tenantPackageShareRelationMapper.insert(shareRelationDOS);
        return 0L;
    }

    @Override
    public void updateTenantPackageShareRelation(TenantPackageShareRelationSaveReqVO updateReqVO) {
        // 校验存在
        validateTenantPackageShareRelationExists(updateReqVO.getId());
        // 更新
        TenantPackageShareRelationDO updateObj = BeanUtils.toBean(updateReqVO, TenantPackageShareRelationDO.class);
        tenantPackageShareRelationMapper.updateById(updateObj);
    }

    @Override
    public void deleteTenantPackageShareRelation(List<Long> ids) {
        // 删除
        tenantPackageShareRelationMapper.deleteByIds(ids);
    }

    private void validateTenantPackageShareRelationExists(Long id) {
        if (tenantPackageShareRelationMapper.selectById(id) == null) {
            throw exception(TENANT_PACKAGE_SHARE_RELATION_NOT_EXISTS);
        }
    }

    @Override
    public TenantPackageShareRelationDO getTenantPackageShareRelation(Long id) {
        return tenantPackageShareRelationMapper.selectById(id);
    }

    @Override
    public PageResult<TenantPackageShareRelationRespVO> getTenantPackageShareRelationPage(TenantPackageShareRelationPageReqVO pageReqVO) {

        pageReqVO.setHeadTenantId(Optional.ofNullable(pageReqVO.getHeadTenantId()).orElse(TenantContextHolder.getRequiredTenantId()));

        TenantDO tenantDO = tenantService.validTenant(pageReqVO.getHeadTenantId());

        if (!Objects.equals(tenantDO.getType(), TenantTypeEnum.CHAIN_HEADQUARTERS.getCode())) {
            return new PageResult<>();
        }

        PageResult<TenantPackageShareRelationDO> pageResult = tenantPackageShareRelationMapper.selectPage(pageReqVO);

        Map<Long, TenantDO> tenantDOMap = tenantService.getTenantListMap(pageResult.getList().stream().map(TenantPackageShareRelationDO::getTenantId).distinct().toList());

        List<TenantPackageShareRelationRespVO> list = pageResult.getList().stream().map(s -> {
            TenantPackageShareRelationRespVO respVO = TenantPackageShareConvert.INSTANCE.convert(s);
            TenantPackageShareConvert.INSTANCE.convertFill(respVO, tenantDOMap.get(s.getTenantId()));
            return respVO;
        }).toList();

        return new PageResult<>(list, pageResult.getTotal());
    }


    @Override
    public List<TenantPackageShareRelationDO> getTenantPackageShareRelationList(Long tenantId, BizTypeEnum bizTypeEnum) {
        // 校验门店类型+总部id
        TenantDO tenant = tenantService.validTenant(tenantId);

        if (!Objects.equals(tenant.getWzTenantType(), TenantTypeEnum.CHAIN_STORE.getCode()) || tenant.getHeadTenantId() == null) {
            return new ArrayList<>();
        }
        // 查总部 分享给 当前门店的套餐
        return tenantPackageShareRelationMapper.selectList(new TenantPackageShareRelationPageReqVO()
            .setTenantId(tenant.getId())
            .setHeadTenantId(tenant.getHeadTenantId())
            .setBizType(bizTypeEnum.getCode()));
    }

    @Override
    public List<TenantPackageRelationRespVO> getTenantSharePackageRelationList(TenantPackageReqDto reqDto) {
        // 查总部 分享给 当前门店的套餐
        Long tenantId = Optional.ofNullable(reqDto.getTenantId()).orElse(TenantContextHolder.getRequiredTenantId());

        List<TenantPackageShareRelationDO> shareRelationDOS = getTenantPackageShareRelationList(tenantId, BizTypeEnum.fromCode(reqDto.getBizType()));

        if (CollUtil.isEmpty(shareRelationDOS)) {
            return new ArrayList<>();
        }

        TenantDO tenant = tenantService.validTenant(tenantId);

        // 查总部门店套餐订单
        reqDto.setTenantId(tenant.getHeadTenantId());
        reqDto.setTenantPackageIds(shareRelationDOS.stream().map(TenantPackageShareRelationDO::getTenantPackageId).distinct().toList());

        List<TenantPackageRelationRespVO> relationList = tenantPackageRelationService.getTenantPackageRelationList(reqDto);
        relationList.forEach(s -> s.setPackageName("【总部共享】" + s.getPackageName()));

        return relationList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unBindTenantPackageShareRelation(Long id, Long headTenantId) {

        List<TenantPackageShareRelationDO> shareRelationDOS = tenantPackageShareRelationMapper.selectList(new TenantPackageShareRelationPageReqVO()
            .setTenantId(id)
            .setHeadTenantId(headTenantId));

        if (CollUtil.isEmpty(shareRelationDOS)) {
            return;
        }
        tenantPackageShareRelationMapper.deleteByIds(shareRelationDOS.stream().map(TenantPackageShareRelationDO::getId).distinct().toList());

    }
}