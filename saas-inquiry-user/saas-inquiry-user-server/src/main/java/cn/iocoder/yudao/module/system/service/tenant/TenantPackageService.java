package cn.iocoder.yudao.module.system.service.tenant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageReqDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.TenantPackageListReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.TenantPackagePageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.TenantPackageRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.TenantPackageSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageDO;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;

/**
 * 门店套餐 Service 接口
 *
 * <AUTHOR>
 */
public interface TenantPackageService {

    /**
     * 创建门店套餐
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTenantPackage(@Valid TenantPackageSaveReqVO createReqVO);

    /**
     * 更新门店套餐
     *
     * @param updateReqVO 更新信息
     */
    void updateTenantPackage(@Valid TenantPackageSaveReqVO updateReqVO);

    /**
     * 删除门店套餐
     *
     * @param id 编号
     */
    void deleteTenantPackage(Long id);

    /**
     * 获得门店套餐
     *
     * @param id 编号
     * @return 门店套餐
     */
    TenantPackageRespVO getTenantPackage(Long id);

    /**
     * 获得门店套餐分页
     *
     * @param pageReqVO 分页查询
     * @return 门店套餐分页
     */
    PageResult<TenantPackageRespVO> getTenantPackagePage(TenantPackagePageReqVO pageReqVO);

    /**
     * 根据套餐包ids获取套餐VO信息 - 填充了VO中相关字段
     *
     * @param packageDto 套餐请求入参
     * @return 套餐Map
     */
    Map<Long, TenantPackageRespVO> getTenantPackageVoByCondition(TenantPackageReqDto packageDto);

    /**
     * 根据套餐包ids获取套餐VO信息
     *
     * @param packageDto 套餐请求入参
     * @return list
     */
    List<TenantPackageDO> getTenantPackageDoByCondition(TenantPackageReqDto packageDto);

    /**
     * 获得门店套餐列表
     *
     * @param reqVO 套餐请求入参
     * @return list
     */
    List<TenantPackageDO> getTenantPackageList(TenantPackageListReqVO reqVO);


    /**
     * 校验门店套餐
     *
     * @param id 编号
     * @return 门店套餐
     */
    TenantPackageDO validTenantPackage(Long id);

    /**
     * 校验套餐重复
     */
    void validPackageItemsDuplicate(List<InquiryPackageItem> packageItems, Integer inquiryBizType);

    /**
     * 获得指定状态的门店套餐列表
     *
     * @param status 状态
     * @return 门店套餐
     */
    List<TenantPackageDO> getTenantPackageListByStatus(Integer status);


}
