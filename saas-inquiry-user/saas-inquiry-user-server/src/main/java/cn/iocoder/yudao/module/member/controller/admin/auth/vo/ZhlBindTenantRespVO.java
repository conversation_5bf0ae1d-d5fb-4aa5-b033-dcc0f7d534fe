package cn.iocoder.yudao.module.member.controller.admin.auth.vo;

import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantSimpleRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Schema(description = "用户 APP - 微信小程序手机登录 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ZhlBindTenantRespVO implements Serializable {

    @Schema(description = "用户登录的门店id,当仅有一个门店时返回 并且返回一下token信息")
    private Long tenantId;

    @Schema(description = "用户所属门店列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<TenantSimpleRespVO> tenantList;

}
