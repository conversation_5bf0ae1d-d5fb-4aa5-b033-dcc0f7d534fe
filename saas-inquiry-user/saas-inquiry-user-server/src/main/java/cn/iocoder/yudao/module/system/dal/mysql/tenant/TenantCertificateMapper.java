package cn.iocoder.yudao.module.system.dal.mysql.tenant;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantCertificateDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 门店资质证件信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantCertificateMapper extends BaseMapperX<TenantCertificateDO> {

    default List<TenantCertificateDO> selectCertificatesByTenantId(Long tenantId) {
        return selectList(new LambdaQueryWrapperX<TenantCertificateDO>().eq(TenantCertificateDO::getTenantId, tenantId));
    }

    default List<TenantCertificateDO> selectCertificatesByTenantIds(List<Long> tenantIds) {
        return selectList(new LambdaQueryWrapperX<TenantCertificateDO>().in(TenantCertificateDO::getTenantId, tenantIds));
    }
}