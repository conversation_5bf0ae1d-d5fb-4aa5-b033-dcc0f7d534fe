package cn.iocoder.yudao.module.system.service.logger;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.logger.dto.OperateLogCreateReqDTO;
import cn.iocoder.yudao.module.system.controller.admin.logger.vo.operatelog.OperateLogPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.logger.OperateLogDO;
import java.util.List;

/**
 * 操作日志 Service 接口
 *
 * <AUTHOR>
 */
public interface IOperateLogService {

    /**
     * 批量创建操作日志
     *
     * @param createReqDTOs
     */
    void batchCreateOperateLog(List<OperateLogCreateReqDTO> createReqDTOs);

    /**
     * 获得操作日志分页列表
     *
     * @param pageReqVO 分页条件
     * @return 操作日志分页列表
     */
    PageResult<OperateLogDO> getOperateLogPage(OperateLogPageReqVO pageReqVO);

}
