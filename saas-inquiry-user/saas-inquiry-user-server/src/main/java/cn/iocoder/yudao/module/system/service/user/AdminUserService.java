package cn.iocoder.yudao.module.system.service.user;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.system.api.user.dto.SSOUser;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.profile.UserProfileUpdatePasswordReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.profile.UserProfileUpdateReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserImportExcelVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserImportRespVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserInfoVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserQueryReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserRespVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import jakarta.validation.Valid;
import java.io.InputStream;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 后台用户 Service 接口
 *
 * <AUTHOR>
 */
public interface AdminUserService {

    /**
     * 创建用户
     *
     * @param createReqVO 用户信息
     * @return 用户编号
     */
    Long createUserStore(UserSaveReqVO createReqVO);

    /**
     * 初始化门店创建用户
     *
     * @param createReqVO 用户信息
     * @return
     */
    Long createTenantUser(@Valid UserSaveReqVO createReqVO);

    /**
     * 修改用户
     *
     * @param updateReqVO 用户信息
     */
    void updateUserStore(@Valid UserSaveReqVO updateReqVO);

    /**
     * 更新用户的最后登陆信息
     *
     * @param id      用户编号
     * @param loginIp 登陆 IP
     */
    void updateUserLogin(Long id, String loginIp);

    /**
     * 修改用户个人信息
     *
     * @param id    用户编号
     * @param reqVO 用户个人信息
     */
    void updateUserProfile(Long id, @Valid UserProfileUpdateReqVO reqVO);

    /**
     * 修改用户个人密码
     *
     * @param id    用户编号
     * @param reqVO 更新用户个人密码
     */
    void updateUserPassword(Long id, @Valid UserProfileUpdatePasswordReqVO reqVO);

    /**
     * 更新用户头像
     *
     * @param id         用户 id
     * @param avatarFile 头像文件
     */
    String updateUserAvatar(Long id, InputStream avatarFile) throws Exception;

    /**
     * 修改密码
     *
     * @param id       用户编号
     * @param password 密码
     */
    void updateUserPassword(Long id, String password);

    /**
     * 修改状态
     *
     * @param id     用户编号
     * @param status 状态
     */
    void updateUserStatusSystem(Long id, Integer status);

    /**
     * 修改员工状态
     *
     * @param id     用户编号
     * @param status 状态
     */
    void updateEmployeeStatus(Long id, Integer status);

    /**
     * 删除用户 - 系统
     *
     * @param id 用户编号
     */
    void deleteUserSystem(Long id);


    /**
     * 门店删除用户 - 门店
     *
     * @param id 用户编号
     */
    void deleteUserStore(Long id);

    /**
     * 通过用户名查询用户
     *
     * @param username 用户名
     * @return 用户对象信息
     */
    AdminUserDO getUserByUsername(String username);

    /**
     * 获取用户基础信息by id
     *
     * @param id 用户id
     * @return
     */
    AdminUserDO getUserById(Long id);

    /**
     * 通过手机号获取用户
     *
     * @param mobile 手机号
     * @return 用户对象信息
     */
    AdminUserDO getUserByMobileSystem(String mobile);

    /**
     * 获得用户分页列表
     *
     * @param reqVO 分页条件
     * @return 分页列表
     */
    PageResult<UserRespVO> getUserPageStore(UserPageReqVO reqVO);

    /**
     * 通过用户 ID 查询用户
     *
     * @param id 用户ID
     * @return 用户对象信息
     */
    AdminUserDO getUserStore(Long id);

    /**
     * 获得指定部门的用户数组
     *
     * @param deptIds 部门数组
     * @return 用户数组
     */
    List<AdminUserDO> getUserListByDeptIds(Collection<Long> deptIds);

    /**
     * 获得指定岗位的用户数组
     *
     * @param postIds 岗位数组
     * @return 用户数组
     */
    List<AdminUserDO> getUserListByPostIds(Collection<Long> postIds);

    /**
     * 获得用户列表
     *
     * @param ids 用户编号数组
     * @return 用户列表
     */
    List<AdminUserDO> getUserList(Collection<Long> ids);

    /**
     * 校验用户们是否有效。如下情况，视为无效： 1. 用户编号不存在 2. 用户被禁用
     *
     * @param ids 用户编号数组
     */
    void validateUserList(Collection<Long> ids);

    /**
     * 获得用户 Map
     *
     * @param ids 用户编号数组
     * @return 用户 Map
     */
    default Map<Long, String> getUserMap(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new HashMap<>();
        }
        return getUserList(ids).stream().collect(Collectors.toMap(AdminUserDO::getId, AdminUserDO::getNickname, (a, b) -> b));
    }

    /**
     * 获得用户列表，基于昵称模糊匹配
     *
     * @param nickname 昵称
     * @return 用户列表
     */
    List<AdminUserDO> getUserListByNickname(String nickname);

    /**
     * 批量导入用户
     *
     * @param importUsers     导入用户列表
     * @param isUpdateSupport 是否支持更新
     * @return 导入结果
     */
    UserImportRespVO importUserList(List<UserImportExcelVO> importUsers, boolean isUpdateSupport);

    /**
     * 获得指定状态的用户们
     *
     * @param status 状态
     * @return 用户们
     */
    List<AdminUserDO> getUserListStore(UserQueryReqVO queryReqVO);

    /**
     * 判断密码是否匹配
     *
     * @param rawPassword     未加密的密码
     * @param encodedPassword 加密后的密码
     * @return 是否匹配
     */
    boolean isPasswordMatch(String rawPassword, String encodedPassword);

    /**
     * 处理OA账号登录的用户信息
     *
     * @param ssoUser oa用户信息
     * @return userId
     */
    Long handleOALoginUser(SSOUser ssoUser);


    /**
     * 获取用户信息
     *
     * @param pageReqVO 用户信息入参
     * @return 用户page
     */
    PageResult<UserRespVO> getUserPageSystem(UserPageReqVO pageReqVO);

    /**
     * 获取门店用户
     *
     * @param mobile 手机号
     * @return user
     */
    AdminUserDO getUserByMobileStore(String mobile);

    /**
     * 获取问诊用户签名管理员工列表 (核对/调配/发药)
     *
     * @param pageReqVO 分页VO
     * @return 用户签名管理员工列表
     */
    PageResult<UserRespVO> pageInquiryUserSignature(UserPageReqVO pageReqVO);


    /**
     * 填充用户信息
     */
    <T extends BaseDO> List<T> fillUserInfo(List<T> list);

    /**
     * 重置用户guid
     *
     * @param userId
     */
    void resetUserGuid(Long userId);

    /**
     * 根据角色Codes获取当前门店用户
     *
     * @param roleCodes 角色codes
     * @return
     */
    List<UserRespVO> getUserListByRoleCodes(List<String> roleCodes);

    /**
     * 获取当前租户下的user
     *
     * @return
     */
    AdminUserRespDTO getTenantUser();

    /**
     * 强制退出用户
     *
     * @param userId 用户id
     * @param status 状态
     */
    void logout(Long userId);

    /**
     * 获取当前登录用户信息
     *
     * @return
     */
    UserInfoVO getUserInfo();

    /**
     * 验证用户是否存在
     *
     * @param id
     * @return
     */
    AdminUserDO validateUserExists(Long id);

    /**
     * 验证手机号是否唯一
     *
     * @param id
     * @param mobile
     */
    void validateMobileUnique(Long id, String mobile);

}
