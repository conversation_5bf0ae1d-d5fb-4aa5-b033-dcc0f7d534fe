package cn.iocoder.yudao.module.system.dal.mysql.user;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserClockInLogPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.TenantUserClockInLogDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 门店员工打卡记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantUserClockInLogMapper extends BaseMapperX<TenantUserClockInLogDO> {

    default PageResult<TenantUserClockInLogDO> selectPage(TenantUserClockInLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TenantUserClockInLogDO>()
            .eqIfPresent(TenantUserClockInLogDO::getUserId, reqVO.getUserId())
            .eqIfPresent(TenantUserClockInLogDO::getUserIp, reqVO.getUserIp())
            .eqIfPresent(TenantUserClockInLogDO::getUserAgent, reqVO.getUserAgent())
            .betweenIfPresent(TenantUserClockInLogDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(TenantUserClockInLogDO::getId));
    }

}