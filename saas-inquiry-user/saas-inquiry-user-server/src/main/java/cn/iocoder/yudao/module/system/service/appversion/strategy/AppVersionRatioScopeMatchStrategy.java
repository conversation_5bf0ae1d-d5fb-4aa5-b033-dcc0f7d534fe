package cn.iocoder.yudao.module.system.service.appversion.strategy;

import cn.iocoder.yudao.module.system.controller.app.appversion.vo.CheckAppVersionReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.appversion.AppVersionDO;
import com.xyy.saas.inquiry.enums.system.AppVersionUpgradeScopeEnum;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

/**
 * @Author: xucao
 * @Date: 2025/01/20 19:30
 * @Description: APP 版本匹配策略-比例匹配
 */
@Component
public class AppVersionRatioScopeMatchStrategy extends AppVersionMatchStrategy{

    /**
     * 检查版本是否匹配
     *
     * @param appVersionDO 版本池版本信息
     * @param reqVO        客户端请求信息
     * @return 匹配时返回当前版本，不匹配返回null
     */
    @Override
    public AppVersionDO match(AppVersionDO appVersionDO, CheckAppVersionReqVO reqVO) {
        // 如果当前版本没有灰度比例，或者当前未传入租户id 则直接返回null
        if(ObjectUtils.isEmpty(appVersionDO.getGrayRatio()) || ObjectUtils.isEmpty(reqVO.getTenantId())){
            return null;
        }
        // 用租户id取模100，如果大于当前版本灰度比例 ，说明当前租户不在当前版本灰度范围，返回null
        if(reqVO.getTenantId() % 100 > appVersionDO.getGrayRatio()){
            return null;
        }
        return super.baseMatch(appVersionDO,reqVO);
    }

    /**
     * 获取当前策略对应的更新范围
     *
     * @return 当前策略对应的更新范围
     */
    @Override
    public AppVersionUpgradeScopeEnum getUpgradeScope() {
        return AppVersionUpgradeScopeEnum.RATIO;
    }
}
