package cn.iocoder.yudao.module.system.framework.tencent.dto;

import cn.hutool.core.annotation.Alias;
import lombok.Data;

/**
 * @Author:chenxiaoyi
 * @Date:2025/08/06 20:04
 */
@Data
public class TencentDetectFaceDto extends TencentFaceResultDto {

    @Alias(value = "ImageWidth")
    private String imageWidth;

    @Alias(value = "ImageHeight")
    private String imageHeight;

    @Alias(value = "FaceModelVersion")
    private String faceModelVersion;

}
