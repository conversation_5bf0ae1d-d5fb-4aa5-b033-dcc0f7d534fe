package cn.iocoder.yudao.module.system.service.permission;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertMap;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.ROLE_ADMIN_CODE_ERROR;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.ROLE_CODE_DUPLICATE;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.ROLE_IS_DISABLE;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.ROLE_NAME_DUPLICATE;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.ROLE_NOT_EXISTS;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_ROLE_CREATE_SUB_TYPE;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_ROLE_CREATE_SUCCESS;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_ROLE_DELETE_SUB_TYPE;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_ROLE_DELETE_SUCCESS;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_ROLE_TYPE;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_ROLE_UPDATE_SUB_TYPE;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_ROLE_UPDATE_SUCCESS;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.system.controller.admin.permission.vo.role.RolePageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.permission.vo.role.RoleSaveReqVO;
import cn.iocoder.yudao.module.system.convert.premission.RoleConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.mysql.permission.RoleMapper;
import cn.iocoder.yudao.module.system.dal.redis.RedisKeyConstants;
import cn.iocoder.yudao.module.system.enums.permission.DataScopeEnum;
import cn.iocoder.yudao.module.system.enums.permission.RoleTypeEnum;
import cn.iocoder.yudao.module.system.service.tenant.TenantService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.annotations.VisibleForTesting;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.service.impl.DiffParseFunction;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

/**
 * 角色 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class RoleServiceImpl implements RoleService {

    @Resource
    @Lazy // 延迟，避免循环依赖报错
    private PermissionService permissionService;

    @Resource
    private RoleMapper roleMapper;

    @Resource
    @Lazy
    private TenantService tenantService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    @LogRecord(type = SYSTEM_ROLE_TYPE, subType = SYSTEM_ROLE_CREATE_SUB_TYPE, bizNo = "{{#role.id}}",
        success = SYSTEM_ROLE_CREATE_SUCCESS)
    public Long createRole(RoleSaveReqVO createReqVO, Integer type) {
        // 1. 校验角色
        validateRoleDuplicate(createReqVO.getName(), createReqVO.getCode(), null);
        RoleDO role = RoleConvert.INSTANCE.convertCreate(createReqVO, type);
        roleMapper.insert(role);
        // 3. 记录操作日志上下文
        LogRecordContext.putVariable("role", role);
        return role.getId();
    }

    /**
     * 批量创建角色信息
     *
     * @param createReqVOs 建角色信息s
     */
    @Override
    public void initTenantSystemRoles(List<RoleSaveReqVO> createReqVOs) {
        List<RoleDO> roleDOList = RoleConvert.INSTANCE.convert(createReqVOs);
        if (CollUtil.isEmpty(roleDOList)) {
            return;
        }
        // 存在不处理
        List<RoleDO> roleDOS = roleMapper.selectByCodes(CollectionUtils.convertSet(createReqVOs, RoleSaveReqVO::getCode));
        if (CollUtil.isNotEmpty(roleDOS)) {
            List<String> existsCodes = CollectionUtils.convertList(roleDOS, RoleDO::getCode);
            roleDOList = roleDOList.stream().filter(r -> !existsCodes.contains(r.getCode())).toList();
        }
        if (CollUtil.isEmpty(roleDOList)) {
            return;
        }
        roleMapper.insertBatch(roleDOList.stream().peek(r -> r.setType(RoleTypeEnum.SYSTEM.getType())
            .setSort(1).setStatus(CommonStatusEnum.ENABLE.getStatus())
            .setDataScope(DataScopeEnum.ALL.getScope()).setRemark("系统角色不可操作")).collect(Collectors.toSet()));
    }

    @Override
    @CacheEvict(value = RedisKeyConstants.ROLE, key = "#updateReqVO.id")
    @LogRecord(type = SYSTEM_ROLE_TYPE, subType = SYSTEM_ROLE_UPDATE_SUB_TYPE, bizNo = "{{#updateReqVO.id}}",
        success = SYSTEM_ROLE_UPDATE_SUCCESS)
    public void updateRole(RoleSaveReqVO updateReqVO) {
        // 1.1 校验是否可以更新
        RoleDO role = validateRoleForUpdate(updateReqVO.getId());
        // 1.2 校验角色的唯一字段是否重复
        validateRoleDuplicate(updateReqVO.getName(), updateReqVO.getCode(), updateReqVO.getId());

        // 2. 更新到数据库
        RoleDO updateObj = BeanUtils.toBean(updateReqVO, RoleDO.class);
        roleMapper.updateById(updateObj);

        // 3. 记录操作日志上下文
        LogRecordContext.putVariable("role", role);
    }

    @Override
    @CacheEvict(value = RedisKeyConstants.ROLE, key = "#id")
    public void updateRoleDataScope(Long id, Integer dataScope, Set<Long> dataScopeDeptIds) {
        // 校验是否可以更新
        validateRoleForUpdate(id);

        // 更新数据范围
        RoleDO updateObject = new RoleDO();
        updateObject.setId(id);
        updateObject.setDataScope(dataScope);
        updateObject.setDataScopeDeptIds(dataScopeDeptIds);
        roleMapper.updateById(updateObject);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    @CacheEvict(value = RedisKeyConstants.ROLE, key = "#id")
    @LogRecord(type = SYSTEM_ROLE_TYPE, subType = SYSTEM_ROLE_DELETE_SUB_TYPE, bizNo = "{{#id}}",
        success = SYSTEM_ROLE_DELETE_SUCCESS)
    public void deleteRole(Long id) {
        // 1. 校验是否可以更新
        RoleDO role = validateRoleForUpdate(id);

        // 2.1 标记删除
        roleMapper.deleteById(id);
        // 2.2 删除相关数据
        permissionService.processRoleDeleted(id);

        // 3. 记录操作日志上下文
        LogRecordContext.putVariable(DiffParseFunction.OLD_OBJECT, BeanUtils.toBean(role, RoleSaveReqVO.class));
        LogRecordContext.putVariable("role", role);
    }

    /**
     * 校验角色的唯一字段是否重复
     * <p>
     * 1. 是否存在相同名字的角色 2. 是否存在相同编码的角色
     *
     * @param name 角色名字
     * @param code 角色额编码
     * @param id   角色编号  开发
     */
    @VisibleForTesting
    void validateRoleDuplicate(String name, String code, Long id) {
        // 0. 超级管理员，不允许创建
        if (RoleCodeEnum.isSuperAdmin(code) || RoleCodeEnum.isStoreAdmin(code)) {
            throw exception(ROLE_ADMIN_CODE_ERROR, code);
        }
        // 1. 该 name 名字被其它角色所使用
        RoleDO role = roleMapper.selectByName(name);
        if (role != null && !role.getId().equals(id)) {
            throw exception(ROLE_NAME_DUPLICATE, name);
        }
        // 2. 是否存在相同编码的角色
        if (!StringUtils.hasText(code)) {
            return;
        }
        // 该 code 编码被其它角色所使用
        role = roleMapper.selectByCode(code);
        if (role != null && !role.getId().equals(id)) {
            throw exception(ROLE_CODE_DUPLICATE, code);
        }
    }

    /**
     * 校验角色是否可以被更新
     *
     * @param id 角色编号
     */
    @VisibleForTesting
    RoleDO validateRoleForUpdate(Long id) {
        RoleDO role = roleMapper.selectById(id);
        if (role == null) {
            throw exception(ROLE_NOT_EXISTS);
        }
        // 内置角色，不允许删除
        if (!RoleTypeEnum.CUSTOM.getType().equals(role.getType())) {
            throw exception(ROLE_CAN_NOT_UPDATE_SYSTEM_TYPE_ROLE);
        }
        return role;
    }

    @Override
    public RoleDO getRole(Long id) {
        return roleMapper.selectById(id);
    }

    @Override
    @Cacheable(value = RedisKeyConstants.ROLE, key = "#id",
        unless = "#result == null")
    public RoleDO getRoleFromCache(Long id) {
        return TenantUtils.executeIgnore(() -> roleMapper.selectById(id));
    }


    @Override
    public List<RoleDO> getRoleListByStatus(Integer status) {
        RolePageReqVO rolePageReqVO = new RolePageReqVO().setStatus(status);
        rolePageReqVO.setPageSize(Integer.MAX_VALUE);
        // 系统门店 不查门店角色
        if (TenantConstant.isSystemTenant()) {
            rolePageReqVO.setNoRoleCodes(Stream.of(RoleCodeEnum.STORE_ADMIN.getCode(), RoleCodeEnum.STORE_EMPLOYEE.getCode()).toList());
        }
        PageResult<RoleDO> rolePage = getRolePage(rolePageReqVO);
        if (rolePage == null) {
            return null;
        }
        return rolePage.getList();
    }

    @Override
    public List<RoleDO> getRoleList() {
        return roleMapper.selectList();
    }

    @Override
    public List<RoleDO> getRoleList(Collection<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        return roleMapper.selectBatchIds(ids);
    }

    @Override
    public List<RoleDO> getRoleListFromCache(Collection<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return Collections.emptyList();
        }
        // 这里采用 for 循环从缓存中获取，主要考虑 Spring CacheManager 无法批量操作的问题
        RoleServiceImpl self = getSelf();
        return CollectionUtils.convertList(ids, self::getRoleFromCache);
    }

    @Override
    public PageResult<RoleDO> getRolePage(RolePageReqVO reqVO) {
        // 超级管理员获取自己角色
        if (TenantConstant.isSystemTenant()) {
            return roleMapper.selectPage(reqVO);
        }
        // 门店获取共享角色
        TenantDO tenantDO = tenantService.getRequiredTenant(TenantContextHolder.getRequiredTenantId());
        Set<Long> roleIds = new HashSet<>();
        if (CommonStatusEnum.isEnable(tenantDO.getWzBizTypeStatus())) {
            tenantService.getBizRoles(tenantDO.getType(), BizTypeEnum.HYWZ.getCode(), null, roleIds);
        }
        if (CommonStatusEnum.isEnable(tenantDO.getZhlBizTypeStatus())) {
            tenantService.getBizRoles(tenantDO.getType(), BizTypeEnum.ZHL.getCode(), null, roleIds);
        }
        // List<Integer> roleTypes = Stream.of(CommonStatusEnum.isEnable(tenantDO.getWzBizTypeStatus()) ? RoleTypeEnum.SYSTEM_WZ : null,
        //         CommonStatusEnum.isEnable(tenantDO.getZhlBizTypeStatus()) ? RoleTypeEnum.SYSTEM_ZHL : null)
        //     .filter(Objects::nonNull).map(RoleTypeEnum::getType).toList();
        // reqVO.setTypes(roleTypes).setTenantId(tenantDO.getId());
        reqVO.setRoleIds(roleIds).setTenantId(tenantDO.getId());
        IPage<RoleDO> pageResult = TenantUtils.executeIgnore(() -> roleMapper.selectPageStore(MyBatisUtils.buildPage(reqVO), reqVO));
        if (pageResult == null || pageResult.getTotal() == 0) {
            return PageResult.empty();
        }
        return new PageResult(pageResult.getRecords(), pageResult.getTotal());


    }

    @Override
    public boolean hasAnySuperAdmin(Collection<Long> ids) {
        if (CollectionUtil.isEmpty(ids)) {
            return false;
        }
        RoleServiceImpl self = getSelf();
        return ids.stream().anyMatch(id -> {
            RoleDO role = self.getRoleFromCache(id);
            return role != null && RoleCodeEnum.isSuperAdmin(role.getCode());
        });
    }

    @Override
    public void validateRoleList(Collection<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }
        // 获得角色信息
        List<RoleDO> roles = roleMapper.selectBatchIds(ids);
        Map<Long, RoleDO> roleMap = convertMap(roles, RoleDO::getId);
        // 校验
        ids.forEach(id -> {
            RoleDO role = roleMap.get(id);
            if (role == null) {
                throw exception(ROLE_NOT_EXISTS);
            }
            if (!CommonStatusEnum.ENABLE.getStatus().equals(role.getStatus())) {
                throw exception(ROLE_IS_DISABLE, role.getName());
            }
        });
    }

    @Override
    public RoleDO selectByCode(String code) {
        if (RoleCodeEnum.isSystemRole(RoleCodeEnum.forCode(code))) {
            return TenantUtils.execute(TenantConstant.DEFAULT_TENANT_ID, () -> roleMapper.selectByCode(code));
        }
        return roleMapper.selectByCode(code);
    }


    public List<RoleDO> selectByCodes(Set<String> codes) {
        List<RoleDO> list = new ArrayList<>();
        Set<String> systemCodes = codes.stream().filter(c -> RoleCodeEnum.isSystemRole(RoleCodeEnum.forCode(c))).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(systemCodes)) {
            list.addAll(TenantUtils.execute(TenantConstant.DEFAULT_TENANT_ID, () -> roleMapper.selectByCodes(systemCodes)));
        }
        Set<String> roleCodes = codes.stream().filter(c -> !RoleCodeEnum.isSystemRole(RoleCodeEnum.forCode(c))).collect(Collectors.toSet());
        if (CollectionUtil.isNotEmpty(roleCodes)) {
            list.addAll(roleMapper.selectByCodes(roleCodes));
        }
        return list;
    }

    @Override
    public List<RoleDO> selectWzCadRole() {
        return selectByCodes(RoleCodeEnum.listWzCadRole().stream().map(RoleCodeEnum::getCode).collect(Collectors.toSet()));
    }

    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private RoleServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }

}
