package cn.iocoder.yudao.module.member.controller.admin.auth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Schema(description = "用户 APP - 微信小程序手机登录 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ZhlTicketLoginReqVO {

    @Schema(description = "智慧脸登录票据", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "智慧脸登录票据不能为空")
    private String ticket;

    @Schema(description = "微信 openid", requiredMode = Schema.RequiredMode.REQUIRED)
    private String openid;

    @Schema(description = "租户id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;

}
