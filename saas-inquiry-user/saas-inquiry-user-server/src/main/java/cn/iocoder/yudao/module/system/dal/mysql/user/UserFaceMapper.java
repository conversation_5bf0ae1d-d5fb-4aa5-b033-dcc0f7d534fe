package cn.iocoder.yudao.module.system.dal.mysql.user;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.dal.dataobject.user.UserFaceDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;

/**
 * 用户人脸信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface UserFaceMapper extends BaseMapperX<UserFaceDO> {

    default UserFaceDO selectOneByUserId(Long userId) {
        return selectOne(new LambdaQueryWrapperX<UserFaceDO>()
            .eq(UserFaceDO::getUserId, userId), false);
    }

    @Delete("delete from system_users_face where id = #{id}")
    void deleteId(Long id);
}