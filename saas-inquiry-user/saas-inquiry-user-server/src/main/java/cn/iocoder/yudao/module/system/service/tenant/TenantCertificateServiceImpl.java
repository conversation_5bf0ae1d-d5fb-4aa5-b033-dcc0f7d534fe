package cn.iocoder.yudao.module.system.service.tenant;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_CERTIFICATE_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.certificate.TenantCertificateRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.certificate.TenantCertificateSaveReqVO;
import cn.iocoder.yudao.module.system.convert.tenant.TenantCertificateConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantCertificateDO;
import cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantCertificateMapper;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 门店资质证件信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TenantCertificateServiceImpl implements TenantCertificateService {

    @Resource
    private TenantCertificateMapper tenantCertificateMapper;

    @Override
    public Long createTenantCertificate(TenantCertificateSaveReqVO createReqVO) {
        // 插入
        TenantCertificateDO tenantCertificate = BeanUtils.toBean(createReqVO, TenantCertificateDO.class);
        tenantCertificateMapper.insert(tenantCertificate);
        // 返回
        return tenantCertificate.getId();
    }

    @Override
    public void updateTenantCertificate(TenantCertificateSaveReqVO updateReqVO) {
        // 校验存在
        validateTenantCertificateExists(updateReqVO.getId());
        // 更新
        TenantCertificateDO updateObj = BeanUtils.toBean(updateReqVO, TenantCertificateDO.class);
        tenantCertificateMapper.updateById(updateObj);
    }

    @Override
    public void deleteTenantCertificateByTenantId(Long tenantId) {
        // 校验存在
        // validateTenantCertificateExists(id);
        List<TenantCertificateDO> certificateDOS = tenantCertificateMapper.selectCertificatesByTenantId(tenantId);
        if (CollUtil.isNotEmpty(certificateDOS)) {
            tenantCertificateMapper.deleteByIds(certificateDOS.stream().map(TenantCertificateDO::getId).toList());
        }

    }

    private void validateTenantCertificateExists(Long id) {
        if (tenantCertificateMapper.selectById(id) == null) {
            throw exception(TENANT_CERTIFICATE_NOT_EXISTS);
        }
    }

    @Override
    public TenantCertificateDO getTenantCertificate(Long id) {
        return tenantCertificateMapper.selectById(id);
    }


    @Override
    public void createOrUpdateTenantCertificates(List<TenantCertificateSaveReqVO> certificates) {
        if (CollUtil.isEmpty(certificates)) {
            return;
        }
        tenantCertificateMapper.insertOrUpdate(TenantCertificateConvert.INSTANCE.convertVo2Do(certificates));
    }

    @Override
    public List<TenantCertificateRespVO> getTenantCertificates(Long tenantId) {
        List<TenantCertificateDO> certificateDOS = tenantCertificateMapper.selectCertificatesByTenantId(tenantId);
        return TenantCertificateConvert.INSTANCE.convertDo2Vo(certificateDOS);
    }

    @Override
    public List<TenantCertificateRespVO> getTenantCertificates(List<Long> tenantIds) {
        if (CollUtil.isEmpty(tenantIds)) {
            return List.of();
        }
        List<TenantCertificateDO> certificateDOS = tenantCertificateMapper.selectCertificatesByTenantIds(tenantIds);
        return TenantCertificateConvert.INSTANCE.convertDo2Vo(certificateDOS);
    }
}