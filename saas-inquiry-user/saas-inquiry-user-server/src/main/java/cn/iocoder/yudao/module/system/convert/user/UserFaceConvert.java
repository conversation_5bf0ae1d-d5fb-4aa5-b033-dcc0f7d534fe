package cn.iocoder.yudao.module.system.convert.user;

import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserFingerPrintCheckReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserFingerPrintPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserFingerPrintRespVO;
import cn.iocoder.yudao.module.system.controller.app.user.vo.UserFaceSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.TenantUserFingerPrintDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.UserFaceDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface UserFaceConvert {

    UserFaceConvert INSTANCE = Mappers.getMapper(UserFaceConvert.class);

    UserFaceDO convertDo(UserFaceSaveReqVO createReqVO);
}
