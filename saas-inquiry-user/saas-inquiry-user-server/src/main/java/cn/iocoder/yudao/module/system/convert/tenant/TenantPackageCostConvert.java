package cn.iocoder.yudao.module.system.convert.tenant;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageRelationDO;
import com.xyy.saas.inquiry.mq.tenant.TenantPackageCostMessageDto;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 门店 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantPackageCostConvert {

    TenantPackageCostConvert INSTANCE = Mappers.getMapper(TenantPackageCostConvert.class);

    @Mapping(target = "tenantPackageId", source = "id")
    @Mapping(target = "hospitalPrefs", expression = "java(extractHospitalPrefsForMq(packageRelationDO))")
    TenantPackageCostMessageDto tenantPackageRelationDO2CostMessage(TenantPackageRelationDO packageRelationDO);

    default List<TenantPackageCostMessageDto> convertChangeTcbCosts(List<TenantPackageRelationDO> newTcbList) {
        if (CollUtil.isEmpty(newTcbList)) {
            return null;
        }
        return newTcbList.stream().map(n -> {
            TenantPackageCostMessageDto packageCostMessage = tenantPackageRelationDO2CostMessage(n);
            packageCostMessage.setTenantPackageId(n.getId());
            packageCostMessage.setOldTenantPackageId(n.getOldId());
            return packageCostMessage;
        }).collect(Collectors.toList());

    }

    @Mapping(target = "tenantPackageId", source = "id")
    @Mapping(target = "hospitalPrefs", expression = "java(extractHospitalPrefsForMq(relationDO))")
    TenantPackageCostMessageDto convertStatus2CostMsg(TenantPackageRelationDO relationDO);

    /**
     * 为 MQ 消息提取 hospitalPrefs，处理兼容性
     * 优先从 inquiryPackageItems 中提取，如果提取不到则使用原有的 hospitalPrefs 字段
     *
     * @param relationDO 套餐关系DO
     * @return 医院编码列表
     */
    default List<String> extractHospitalPrefsForMq(TenantPackageRelationDO relationDO) {
        if (relationDO == null) {
            return null;
        }

        // 优先从 inquiryPackageItems 中提取
        List<String> extracted = InquiryPackageItem.extractHospitalPrefs(relationDO.getInquiryPackageItems());
        if (CollUtil.isNotEmpty(extracted)) {
            return extracted;
        }

        // 如果提取不到，使用原有的 hospitalPrefs 字段
        return relationDO.getHospitalPrefs();
    }
}
