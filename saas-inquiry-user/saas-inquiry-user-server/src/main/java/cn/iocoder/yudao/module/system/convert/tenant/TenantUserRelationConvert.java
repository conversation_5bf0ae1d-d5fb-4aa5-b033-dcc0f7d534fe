package cn.iocoder.yudao.module.system.convert.tenant;

import cn.hutool.core.bean.BeanUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserRelationBindDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserRelationDto;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantUserRelationUpdateVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.profile.UserProfileUpdateReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.user.UserSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantUserRelationDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import java.util.List;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * 门店 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantUserRelationConvert {

    TenantUserRelationConvert INSTANCE = Mappers.getMapper(TenantUserRelationConvert.class);

    TenantUserRelationDO dto2Do(TenantUserRelationDto relationDto);

    @Mapping(target = "id", ignore = true)
    TenantUserRelationDto userSaveReqVO2Dto(UserSaveReqVO createReqVO, Long userId);
    // {
    //     TenantUserRelationDto tenantUserRelationDto = new TenantUserRelationDto();
    //     tenantUserRelationDto.setUserId(id);
    //     tenantUserRelationDto.setNeedClockIn(createReqVO.getNeedClockIn());
    //     tenantUserRelationDto.setDeptId(createReqVO.getDeptId());
    //     tenantUserRelationDto.setPostIds(createReqVO.getPostIds());
    //     return tenantUserRelationDto;
    // }

    default List<TenantUserRelationDO> convertUpdateDo(List<TenantUserRelationDO> userRelationDOS, TenantUserRelationDto tenantUserRelationDto) {
        return userRelationDOS.stream().peek(u -> {
            fillUserRelationDo(u, tenantUserRelationDto);
        }).collect(Collectors.toList());
    }

    @Mapping(target = "id", ignore = true)
    void fillUserRelationDo(@MappingTarget TenantUserRelationDO u, TenantUserRelationDto tenantUserRelationDto);

    TenantUserRelationDto convertDto(TenantUserRelationDO tenantUserRelation);

    default List<TenantUserRelationDO> convertBindDo(TenantUserRelationDO tenantUserRelationDO, TenantUserRelationUpdateVO bindVO) {
        return bindVO.getTenantIds().stream()
            .map(t -> {
                TenantUserRelationDO userRelationDO = BeanUtil.copyProperties(tenantUserRelationDO, TenantUserRelationDO.class, "id");
                userRelationDO.setTenantId(t);
                userRelationDO.setStatus(CommonStatusEnum.ENABLE.getStatus());
                userRelationDO.setNeedClockIn(bindVO.getNeedClockIn());
                return userRelationDO;
            })
            .collect(Collectors.toList());
    }

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "userId", source = "id")
    @Mapping(target = "joinTime", expression = "java(java.time.LocalDateTime.now())")
    TenantUserRelationDO convertBindRelationDo(AdminUserDO adminUserDO);

    List<TenantUserRelationDto> convertDo2Dto(List<TenantUserRelationDO> tenantUserRelationDOS);

    TenantUserRelationUpdateVO convert(TenantUserRelationBindDto bindDto);

    default TenantUserRelationDto convertDto(TenantUserRelationDO tenantUserRelation, TenantDO tenantDO) {
        TenantUserRelationDto t = convertDto(tenantUserRelation);
        t.setTenantType(tenantDO.getType());
        t.setHeadTenantId(tenantDO.getHeadTenantId());
        return t;
    }

    default void fillUserRelationDto(TenantUserRelationDO userRelationDO, AdminUserRespDTO userRespDTO) {
        if (userRelationDO != null) {
            userRespDTO.setNickname(userRelationDO.getNickname());
            userRespDTO.setSex(userRelationDO.getSex());
            userRespDTO.setAvatar(userRelationDO.getAvatar());
            userRespDTO.setIdCard(userRelationDO.getIdCard());

            userRespDTO.setTenantUserRelationId(userRelationDO.getId());
            userRespDTO.setRelationStatus(userRelationDO.getStatus());
            userRespDTO.setNeedClockIn(userRelationDO.getNeedClockIn());
            userRespDTO.setClockInStatus(userRelationDO.getClockInStatus());
        }

    }

    @Mapping(target = "userId", source = "id")
    @Mapping(target = "id", ignore = true)
    TenantUserRelationDto userSaveReqVO2Dto(UserProfileUpdateReqVO reqVO, Long id);

    default void fillUserRelationDo(AdminUserDO adminUserDO, TenantUserRelationDO userRelationDO) {
        if (userRelationDO != null) {
            adminUserDO.setNickname(userRelationDO.getNickname());
            adminUserDO.setIdCard(userRelationDO.getIdCard());
            adminUserDO.setAvatar(userRelationDO.getAvatar());
            adminUserDO.setSex(userRelationDO.getSex());
            adminUserDO.setDeptId(userRelationDO.getDeptId());
            adminUserDO.setPostIds(userRelationDO.getPostIds());
        }

    }
}
