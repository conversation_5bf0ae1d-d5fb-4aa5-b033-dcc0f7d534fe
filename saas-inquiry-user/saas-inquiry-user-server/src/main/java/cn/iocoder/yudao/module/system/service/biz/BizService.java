package cn.iocoder.yudao.module.system.service.biz;

import jakarta.validation.*;
import cn.iocoder.yudao.module.system.controller.admin.biz.vo.*;
import cn.iocoder.yudao.module.system.dal.dataobject.biz.BizDO;
import java.util.List;

/**
 * 业务 Service 接口
 *
 * <AUTHOR>
 */
public interface BizService {

    /**
     * 创建业务
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createBiz(@Valid BizSaveReqVO createReqVO);

    /**
     * 更新业务
     *
     * @param updateReqVO 更新信息
     */
    void updateBiz(@Valid BizSaveReqVO updateReqVO);

    /**
     * 删除业务
     *
     * @param id 编号
     */
    void deleteBiz(Long id);

    /**
     * 获得业务
     *
     * @param id 编号
     * @return 业务
     */
    BizDO getBiz(Long id);

    /**
     * 获得业务列表
     *
     * @param reqVO 列表查询
     * @return 业务列表
     */
    List<BizDO> getBizList(BizListReqVO reqVO);

}