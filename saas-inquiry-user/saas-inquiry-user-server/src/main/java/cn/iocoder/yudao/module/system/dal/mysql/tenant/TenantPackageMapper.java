package cn.iocoder.yudao.module.system.dal.mysql.tenant;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageReqDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.TenantPackageListReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.TenantPackagePageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageDO;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 门店套餐 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantPackageMapper extends BaseMapperX<TenantPackageDO> {

    default PageResult<TenantPackageDO> selectPage(TenantPackagePageReqVO reqVO) {
        final LambdaQueryWrapperX<TenantPackageDO> queryWrapper = new LambdaQueryWrapperX<TenantPackageDO>()
            .eqIfPresent(TenantPackageDO::getPref, reqVO.getPref())
            .likeIfPresent(TenantPackageDO::getName, reqVO.getName())
            .eqIfPresent(TenantPackageDO::getBizType, reqVO.getBizType())
            .eqIfPresent(TenantPackageDO::getPackageType, reqVO.getPackageType())
            .eqIfPresent(TenantPackageDO::getInquiryBizType, reqVO.getInquiryBizType())
            .eqIfPresent(TenantPackageDO::getStatus, reqVO.getStatus())
            .betweenIfPresent(TenantPackageDO::getCreateTime, reqVO.getCreateTime())
            .likeIfPresent(TenantPackageDO::getHospitalPrefs, reqVO.getHospitalPref())
            .orderByDesc(TenantPackageDO::getSorted)
            .orderByDesc(TenantPackageDO::getId);

        if (CollUtil.isNotEmpty(reqVO.getInquiryWayTypes())) {
            queryWrapper.and(orWrapper -> {
                reqVO.getInquiryWayTypes().forEach(i -> {
                    orWrapper.like(TenantPackageDO::getInquiryWayTypes, i.toString());
                });
            });
        }
        if (CollUtil.isNotEmpty(reqVO.getPrescriptionTypes())) {
            queryWrapper.and(orWrapper -> {
                reqVO.getPrescriptionTypes().forEach(i -> {
                    orWrapper.like(TenantPackageDO::getPrescriptionTypes, i.toString());
                });
            });
        }
        if (CollUtil.isNotEmpty(reqVO.getRegionArr())) {
            queryWrapper.and(orWrapper -> {
                reqVO.getRegionArr().forEach(r -> {
                    orWrapper.like(TenantPackageDO::getRegionArr, r);
                });
            });
        }
        return selectPage(reqVO, queryWrapper);
    }

    default List<TenantPackageDO> selectListByStatus(Integer status) {
        return selectList(TenantPackageDO::getStatus, status);
    }

    default List<TenantPackageDO> selectListByPrefs(List<String> prefs, Integer status) {
        return selectList(new LambdaQueryWrapperX<TenantPackageDO>().in(TenantPackageDO::getPref, prefs).eq(TenantPackageDO::getStatus, status));
    }

    default void updateByIdSelective(TenantPackageDO updateObj) {
        UpdateWrapper<TenantPackageDO> wrapper = Wrappers.update();
        wrapper.lambda().set(TenantPackageDO::getStatus, updateObj.getStatus())
            .set(TenantPackageDO::getRemark, updateObj.getRemark())
            .set(TenantPackageDO::getHospitalPrefs, updateObj.getHospitalPrefs())
            .set(TenantPackageDO::getSorted, updateObj.getSorted())
            .set(TenantPackageDO::getRegionArr, updateObj.getRegionArr())
            .eq(TenantPackageDO::getId, updateObj.getId());
        update(wrapper);
    }

    default List<TenantPackageDO> selectListByCondition(TenantPackageReqDto packageDto) {
        return selectList(new LambdaQueryWrapperX<TenantPackageDO>().in(TenantPackageDO::getId, packageDto.getPackageIds()));
    }

    default List<TenantPackageDO> getTenantPackageList(TenantPackageListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<TenantPackageDO>()
            .eqIfPresent(TenantPackageDO::getPref, reqVO.getPref())
            .likeIfPresent(TenantPackageDO::getName, reqVO.getName())
            .eqIfPresent(TenantPackageDO::getBizType, reqVO.getBizType())
            .eqIfPresent(TenantPackageDO::getPackageType, reqVO.getPackageType())
            .eqIfPresent(TenantPackageDO::getInquiryBizType, reqVO.getInquiryBizType())
            .eqIfPresent(TenantPackageDO::getStatus, reqVO.getStatus()));
    }

}
