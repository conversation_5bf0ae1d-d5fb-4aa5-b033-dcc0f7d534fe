package cn.iocoder.yudao.module.infra.dal.dataobject.file;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 文件信息 DO
 *
 * <AUTHOR>
 */
@TableName("infra_file")
@KeySequence("infra_file_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileDO extends BaseDO {

    /**
     * 文件编号
     */
    @TableId
    private Long id;
    
    /**
     * 配置编号
     * 
     * 关联 FileConfigDO 的 id 字段
     */
    private Long configId;
    
    /**
     * 文件名
     */
    private String name;
    
    /**
     * 文件路径
     */
    private String path;
    
    /**
     * 文件 URL
     */
    private String url;
    
    /**
     * 文件类型
     * 
     * 例如说 jpg、png、gif 等等
     */
    private String type;
    
    /**
     * 文件大小
     * 
     * 单位：字节
     */
    private Integer size;

}
