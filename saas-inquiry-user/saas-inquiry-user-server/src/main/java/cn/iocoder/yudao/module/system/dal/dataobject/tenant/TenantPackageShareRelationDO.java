package cn.iocoder.yudao.module.system.dal.dataobject.tenant;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 总部门店套餐共享 DO
 *
 * <AUTHOR>
 */
@TableName("saas_tenant_package_share_relation")
@KeySequence("saas_tenant_package_share_relation_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantPackageShareRelationDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 总部id
     */
    private Long headTenantId;

    /**
     * 门店Id
     */
    private Long tenantId;
    /**
     * 业务类型 0-问诊,1-智慧脸...
     */
    private Integer bizType;
    /**
     * 共享套餐开通关系表id
     */
    private Long tenantPackageId;
    /**
     * 扩展信息
     */
    private String ext;

}