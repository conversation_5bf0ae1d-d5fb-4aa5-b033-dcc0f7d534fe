package cn.iocoder.yudao.module.system.convert.logger;

import cn.iocoder.yudao.module.system.api.logger.dto.LoginLogCreateReqDTO;
import cn.iocoder.yudao.module.system.api.logger.dto.OperateLogCreateReqDTO;
import cn.iocoder.yudao.module.system.dal.dataobject.logger.LoginLogDO;
import cn.iocoder.yudao.module.system.dal.dataobject.logger.OperateLogDO;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 门店证件 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface LoggerConvert {

    LoggerConvert INSTANCE = Mappers.getMapper(LoggerConvert.class);

    List<OperateLogDO> convert(List<OperateLogCreateReqDTO> createReqDTOs);

    List<LoginLogDO> convertLoginLogCreateReqDTOList2LoginLogDOList(List<LoginLogCreateReqDTO> createReqDTOs);
}
