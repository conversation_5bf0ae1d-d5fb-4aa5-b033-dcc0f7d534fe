### 获取token
GET https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=wxd1425c818778593e&secret=32ab10e980c95f7eeb6d7163d35c2649
Content-Type: application/json
tag: Yunai.local

> {%
  client.global.set("accessToken", response.body.access_token);
%}






### 获取手机号
POST https://api.weixin.qq.com/wxa/business/getuserphonenumber?access_token={{accessToken}}
Content-Type: application/json
tag: Yunai.local

{
  "code": "3beea5967cc4d77e14dbfc1d020fba1a9788708842def6bd72e6058495034f68"
}

> {%
  if (response.body.errcode !== 0) {
    throw new Error(response.body.errmsg);
  }
  client.global.set("phoneInfo", JSON.stringify(response.body.phone_info));
%}



### 请求 /login 接口 => 成功
POST {{baseAppSystemUrl}}/system/auth/weixin-mini-app-login
Content-Type: application/json
tag: Yunai.local

{"loginCode":"0b1BsU1w39YuV436ay1w31Eojk2BsU1S","state": "0","tenantId": 1902541900682842113}

> {%
  client.global.set("tenantId", response.body.data.tenantId == null ? response.body.data.tenantList[0].id : response.body.data.tenantId);
  client.global.set("token", response.body.data.accessToken);
  console.log(response.body.data.accessToken);
  client.global.set("loginUserId", response.body.data.userId);
%}

### 2.获取权限
GET {{baseAppKernelUrl}}/kernel/drugstore/base-info/inquiry-permission
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.errmsg);
  }
%}

### 3.获取权限
GET {{baseAppKernelUrl}}/kernel/drugstore/base-info/can-inquiry?tenantId={{tenantId}}
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.errmsg);
  }
%}

### 3.获取IM信息
GET {{baseAppKernelUrl}}/kernel/im/inquiry-im-user/get?clientChannelType=2
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.errmsg);
  }
%}