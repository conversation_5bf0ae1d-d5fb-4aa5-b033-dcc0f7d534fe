package cn.iocoder.yudao.module.system.service.dict;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.DICT_DATA_NOT_ENABLE;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.DICT_DATA_NOT_EXISTS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.DICT_DATA_VALUE_DUPLICATE;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.DICT_TYPE_NOT_ENABLE;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.DICT_TYPE_NOT_EXISTS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.OA_LOGIN_PARAM_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.api.user.dto.OASsoConfigDto;
import cn.iocoder.yudao.module.system.controller.admin.dict.vo.data.DictDataPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.dict.vo.data.DictDataSaveBatchReqVO;
import cn.iocoder.yudao.module.system.controller.admin.dict.vo.data.DictDataSaveReqVO;
import cn.iocoder.yudao.module.system.controller.app.dict.vo.AppDictDataRespVO;
import cn.iocoder.yudao.module.system.dal.dataobject.dict.DictDataDO;
import cn.iocoder.yudao.module.system.dal.dataobject.dict.DictTypeDO;
import cn.iocoder.yudao.module.system.dal.mysql.dict.DictDataMapper;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.google.common.annotations.VisibleForTesting;
import com.xyy.saas.inquiry.constant.SystemConstant;
import com.xyy.saas.inquiry.constant.TenantConstant;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 字典数据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
@Validated
public class DictDataServiceImpl implements DictDataService {

    /**
     * 排序 dictType > sort
     */
    private static final Comparator<DictDataDO> COMPARATOR_TYPE_AND_SORT = Comparator
        .comparing(DictDataDO::getDictType)
        .thenComparingInt(DictDataDO::getSort);

    @Resource
    @Lazy // 延迟，避免循环依赖报错
    private DictTypeService dictTypeService;

    @Resource
    private DictDataMapper dictDataMapper;

    @Override
    public List<DictDataDO> getDictDataList(Integer status, Long tenantId, String dictType) {
        List<DictDataDO> list = dictDataMapper.selectListByStatusAndDictType(status, tenantId, dictType, CommonStatusEnum.ENABLE.getStatus());
        list.sort(COMPARATOR_TYPE_AND_SORT);
        return list;
    }

    @Override
    public Map<String, List<AppDictDataRespVO>> getDictDataLists(Integer status, Long tenantId, List<String> types) {
        return dictDataMapper.selectListByStatusAndDictType(CommonStatusEnum.ENABLE.getStatus(), tenantId, types, CommonStatusEnum.ENABLE.getStatus())
            .stream().sorted(Comparator.comparing(DictDataDO::getDictType)
                .thenComparingInt(DictDataDO::getSort)).collect(Collectors.groupingBy(DictDataDO::getDictType,
                Collectors.mapping(dictData -> new AppDictDataRespVO(dictData.getId(), dictData.getLabel(),
                    dictData.getValue(), dictData.getDictType()), Collectors.toList())));
    }

    @Override
    public PageResult<DictDataDO> getDictDataPage(DictDataPageReqVO pageReqVO) {
        return dictDataMapper.selectPage(pageReqVO);
    }

    @Override
    public DictDataDO getDictData(Long id) {
        return dictDataMapper.selectById(id);
    }

    @Override
    public int insertOrUpdateBatch(@Valid DictDataSaveBatchReqVO reqVO) {
        // 校验字典类型有效
        validateDictTypeExists(reqVO.getDictType());
        // 先查询，判断是否有变更再写数据库
        Map<String, DictDataDO> dictDataMap = dictDataMapper.selectByTenantIdDictTypeAndValues(reqVO.getTenantId(), reqVO.getDictType(), reqVO.getValueLabelMap().keySet())
            .stream().collect(Collectors.toMap(DictDataDO::getValue, Function.identity(), (a, b) -> b));

        Map<String, String> valueLabelMap = new HashMap<>();
        reqVO.getValueLabelMap().forEach((value, label) -> {
            DictDataDO dictDataDO = dictDataMap.get(value);
            // 字典无变化
            if (dictDataDO != null && Objects.equals(dictDataDO.getLabel(), label)) {
                return;
            }
            valueLabelMap.put(value, label);
        });

        if (CollUtil.isEmpty(valueLabelMap)) {
            return 0;
        }

        // 只写有变更的数据
        return dictDataMapper.insertOrUpdateBatch(BeanUtils.toBean(reqVO, DictDataSaveBatchReqVO.class).setValueLabelMap(valueLabelMap));
    }

    @Override
    public Long createDictData(DictDataSaveReqVO createReqVO) {
        // 校验字典类型有效
        validateDictTypeExists(createReqVO.getDictType());
        // 校验字典数据的值的唯一性
        validateDictDataValueUnique(null, TenantContextHolder.getRequiredTenantId(), createReqVO.getDictType(), createReqVO.getValue());

        // 插入字典类型
        DictDataDO dictData = BeanUtils.toBean(createReqVO, DictDataDO.class);
        // 使用 insertOrUpdate 兼容逻辑删除场景
        dictDataMapper.insertOrUpdate(dictData);
        return dictData.getId();
    }

    @Override
    public void updateDictData(DictDataSaveReqVO updateReqVO) {
        // 校验自己存在
        validateDictDataExists(updateReqVO.getId());
        // 校验字典类型有效
        validateDictTypeExists(updateReqVO.getDictType());
        // 校验字典数据的值的唯一性
        validateDictDataValueUnique(updateReqVO.getId(), TenantContextHolder.getRequiredTenantId(), updateReqVO.getDictType(), updateReqVO.getValue());

        // 更新字典类型
        DictDataDO updateObj = BeanUtils.toBean(updateReqVO, DictDataDO.class);
        dictDataMapper.updateById(updateObj);
    }

    @Override
    public void deleteDictData(Long id) {
        // 校验是否存在
        validateDictDataExists(id);

        // 删除字典数据
        dictDataMapper.deleteById(id);
    }

    @Override
    public long getDictDataCountByDictType(String dictType) {
        return dictDataMapper.selectCountByDictType(dictType);
    }

    @VisibleForTesting
    public void validateDictDataValueUnique(Long id, Long tenantId, String dictType, String value) {
        DictDataDO dictData = dictDataMapper.selectByDictTypeAndValue(tenantId, dictType, value);
        if (dictData == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的字典数据
        if (id == null) {
            throw exception(DICT_DATA_VALUE_DUPLICATE);
        }
        if (!dictData.getId().equals(id)) {
            throw exception(DICT_DATA_VALUE_DUPLICATE);
        }
    }

    @VisibleForTesting
    public void validateDictDataExists(Long id) {
        if (id == null) {
            return;
        }
        DictDataDO dictData = dictDataMapper.selectById(id);
        if (dictData == null) {
            throw exception(DICT_DATA_NOT_EXISTS);
        }
    }

    @VisibleForTesting
    public void validateDictTypeExists(String type) {
        DictTypeDO dictType = dictTypeService.getDictType(type);
        if (dictType == null) {
            throw exception(DICT_TYPE_NOT_EXISTS);
        }
        if (!CommonStatusEnum.ENABLE.getStatus().equals(dictType.getStatus())) {
            throw exception(DICT_TYPE_NOT_ENABLE);
        }
    }

    @Override
    public void validateDictDataList(Long tenantId, String dictType, Collection<String> values) {
        if (CollUtil.isEmpty(values)) {
            return;
        }
        Map<String, DictDataDO> dictDataMap = CollectionUtils.convertMap(
            dictDataMapper.selectByDictTypeAndValues(tenantId, dictType, values), DictDataDO::getValue);
        // 校验
        values.forEach(value -> {
            DictDataDO dictData = dictDataMap.get(value);
            if (dictData == null) {
                throw exception(DICT_DATA_NOT_EXISTS);
            }
            if (!CommonStatusEnum.ENABLE.getStatus().equals(dictData.getStatus())) {
                throw exception(DICT_DATA_NOT_ENABLE, dictData.getLabel());
            }
        });
    }

    @Override
    public DictDataDO getDictData(Long tenantId, String dictType, String value) {
        return dictDataMapper.selectByDictTypeAndValue(tenantId, dictType, value);
    }

    @Override
    public DictDataDO parseDictData(Long tenantId, String dictType, String label) {
        return dictDataMapper.selectByDictTypeAndLabel(tenantId, dictType, label);
    }

    @Override
    public List<DictDataDO> getDictDataListByDictType(Long tenantId, String dictType) {
        List<DictDataDO> list = dictDataMapper.selectList(new LambdaQueryWrapper<DictDataDO>()
            .eq(DictDataDO::getDictType, dictType)
            .in(DictDataDO::getTenantId, TenantConstant.getTenantIds(tenantId)));
        list.sort(Comparator.comparing(DictDataDO::getSort));
        return list;
    }

    @Override
    public OASsoConfigDto getOAConfigDictDto() {
        List<DictDataDO> dataListByDictType = getDictDataListByDictType(TenantConstant.DEFAULT_TENANT_ID, SystemConstant.OA_CONFIG_DICT);
        if (CollUtil.isEmpty(dataListByDictType)) {
            throw exception(OA_LOGIN_PARAM_NOT_EXISTS);
        }
        Map<String, String> oaDictMap = dataListByDictType.stream().collect(Collectors.toMap(DictDataDO::getLabel, DictDataDO::getValue, (a, b) -> b));
        return JSON.parseObject(JSON.toJSONString(oaDictMap), OASsoConfigDto.class);
    }

    @Override
    public List<DictDataDO> getDictDatas(Long tenantId, String dictType, String dictName) {
        return dictDataMapper.getDictDatas(tenantId, dictType, dictName);
    }

    @Override
    public List<DictDataDO> getDictDataList(Long tenantId, List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return new ArrayList<>();
        }
        return dictDataMapper.selectList(new LambdaQueryWrapperX<DictDataDO>().in(DictDataDO::getTenantId, TenantConstant.getTenantIds(tenantId)).inIfPresent(DictDataDO::getId, ids));
    }
}
