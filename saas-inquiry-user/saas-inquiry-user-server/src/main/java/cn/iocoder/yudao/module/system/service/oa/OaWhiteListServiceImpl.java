package cn.iocoder.yudao.module.system.service.oa;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.controller.admin.oa.vo.OaWhiteListPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.oa.vo.OaWhiteListSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.oa.OaWhiteListDO;
import cn.iocoder.yudao.module.system.dal.mysql.oa.OaWhiteListMapper;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.OA_WHITE_LIST_EXISTS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.OA_WHITE_LIST_NOT_EXISTS;

/**
 * OA用户白名单 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class OaWhiteListServiceImpl implements OaWhiteListService {

    @Resource
    private OaWhiteListMapper oaWhiteListMapper;

    @Override
    public Long createOaWhiteList(OaWhiteListSaveReqVO createReqVO) {
        validUserNameDuplicate(createReqVO.getUsername(), null);
        // 插入
        OaWhiteListDO oaWhiteList = BeanUtils.toBean(createReqVO, OaWhiteListDO.class);
        oaWhiteListMapper.insert(oaWhiteList);
        // 返回
        return oaWhiteList.getId();
    }

    @Override
    public void updateOaWhiteList(OaWhiteListSaveReqVO updateReqVO) {
        // 校验存在
        validateOaWhiteListExists(updateReqVO.getId());

        validUserNameDuplicate(updateReqVO.getUsername(), updateReqVO.getId());
        // 更新
        OaWhiteListDO updateObj = BeanUtils.toBean(updateReqVO, OaWhiteListDO.class);
        oaWhiteListMapper.updateById(updateObj);
    }


    private void validUserNameDuplicate(String userName, Long id) {
        OaWhiteListDO oaWhiteListDO = oaWhiteListMapper.selectByName(userName);
        if (oaWhiteListDO == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同名字的门店
        if (id == null) {
            throw exception(OA_WHITE_LIST_EXISTS, userName);
        }
        if (!oaWhiteListDO.getId().equals(id)) {
            throw exception(OA_WHITE_LIST_EXISTS, userName);
        }
    }


    @Override
    public void deleteOaWhiteList(Long id) {
        // 校验存在
        validateOaWhiteListExists(id);
        // 删除
        oaWhiteListMapper.deleteById(id);
    }

    private void validateOaWhiteListExists(Long id) {
        if (oaWhiteListMapper.selectById(id) == null) {
            throw exception(OA_WHITE_LIST_NOT_EXISTS);
        }
    }


    @Override
    public OaWhiteListDO getOaWhiteRequiredByUserName(String username, BizTypeEnum bizTypeEnum) {
        OaWhiteListDO oaWhiteListDO = oaWhiteListMapper.selectByName(username);
        if (oaWhiteListDO == null || !oaWhiteListDO.getBizTypes().contains(bizTypeEnum.getCode())) {
            throw exception(OA_WHITE_LIST_NOT_EXISTS);
        }
        return oaWhiteListDO;
    }

    @Override
    public OaWhiteListDO getOaWhiteList(Long id) {
        return oaWhiteListMapper.selectById(id);
    }

    @Override
    public PageResult<OaWhiteListDO> getOaWhiteListPage(OaWhiteListPageReqVO pageReqVO) {
        return oaWhiteListMapper.selectPage(pageReqVO);
    }

}