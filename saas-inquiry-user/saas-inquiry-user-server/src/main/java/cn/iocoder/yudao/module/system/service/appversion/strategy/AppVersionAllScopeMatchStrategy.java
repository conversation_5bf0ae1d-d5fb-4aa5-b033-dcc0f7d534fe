package cn.iocoder.yudao.module.system.service.appversion.strategy;

import cn.iocoder.yudao.module.system.controller.app.appversion.vo.CheckAppVersionReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.appversion.AppVersionDO;
import com.xyy.saas.inquiry.enums.system.AppVersionUpgradeScopeEnum;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2025/01/20 19:03
 * @Description: 全量版本匹配策略
 */
@Component
public class AppVersionAllScopeMatchStrategy extends AppVersionMatchStrategy{

    /**
     * 检查版本是否匹配
     *
     * @param appVersionDO 版本池版本信息
     * @param reqVO        客户端请求信息
     * @return 匹配时返回当前版本，不匹配返回null
     */
    @Override
    public AppVersionDO match(AppVersionDO appVersionDO, CheckAppVersionReqVO reqVO) {
       return super.baseMatch(appVersionDO,reqVO);
    }

    /**
     * 获取当前策略对应的更新范围
     *
     * @return
     */
    @Override
    public AppVersionUpgradeScopeEnum getUpgradeScope() {
        return AppVersionUpgradeScopeEnum.ALL;
    }
}
