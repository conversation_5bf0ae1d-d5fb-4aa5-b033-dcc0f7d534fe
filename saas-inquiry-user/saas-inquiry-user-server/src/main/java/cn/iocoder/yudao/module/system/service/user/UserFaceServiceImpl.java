package cn.iocoder.yudao.module.system.service.user;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_FACE_NOT_EXISTS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_FACE_VERIFY_FAIL;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_SAVE_FACE_LIMIT_LOCKED;

import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.system.controller.app.user.vo.UserFacePageReqVO;
import cn.iocoder.yudao.module.system.controller.app.user.vo.UserFaceSaveReqVO;
import cn.iocoder.yudao.module.system.convert.user.UserFaceConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.user.AdminUserDO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.UserFaceDO;
import cn.iocoder.yudao.module.system.dal.mysql.user.UserFaceMapper;
import cn.iocoder.yudao.module.system.dal.redis.RedisKeyConstants;
import cn.iocoder.yudao.module.system.dal.redis.common.UserLockRedisDAO;
import cn.iocoder.yudao.module.system.framework.tencent.core.ApiFaceTencentService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 用户人脸信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class UserFaceServiceImpl implements UserFaceService {

    @Resource
    private UserFaceMapper userFaceMapper;

    @Resource
    private ApiFaceTencentService apiFaceTencentService;

    @Resource
    private AdminUserService adminUserService;

    @Resource
    private UserLockRedisDAO userLockRedisDAO;

    @Resource
    private ConfigApi configApi;


    @Override
    public UserFaceDO getUserFace(Long userId) {
        return userFaceMapper.selectOneByUserId(userId);
    }


    @Override
    public UserFaceDO validateFace(Long userId) {
        UserFaceDO userFace = getUserFace(userId);
        if (userFace == null) {
            throw exception(USER_FACE_NOT_EXISTS);
        }
        return userFace;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long saveUserFace(UserFaceSaveReqVO createReqVO) {

        AdminUserDO adminUserDO = adminUserService.validateUserExists(createReqVO.getUserId());

        // 校验用户id锁 一天只能操作5次
        userLockRedisDAO.checkAndLockUserIfExceeded(RedisKeyConstants.USER_SAVE_FACE_LIMIT_COUNT, adminUserDO.getId(), getFaceLimitCount(), USER_SAVE_FACE_LIMIT_LOCKED);
        try {
            // 校验人脸是否认证通过
            apiFaceTencentService.detectFace(createReqVO.getFaceImage());

            UserFaceDO userFace = getUserFace(createReqVO.getUserId());
            UserFaceDO userFaceDo = UserFaceConvert.INSTANCE.convertDo(createReqVO);
            // 先删再增
            if (userFace != null) {
                userFaceMapper.deleteId(userFace.getId());
            }
            userFaceMapper.insert(userFaceDo);
            return userFaceDo.getId();
        } finally {
            // 记录用户锁
            userLockRedisDAO.incrementUserOperationCount(RedisKeyConstants.USER_SAVE_FACE_LIMIT_COUNT, adminUserDO.getId());
        }
    }

    private Integer getFaceLimitCount() {
        return NumberUtil.parseInt(configApi.getConfigValueByKey(RedisKeyConstants.USER_SAVE_FACE_LIMIT_COUNT), 10);
    }

    @Override
    public Boolean matchUserFace(UserFaceSaveReqVO createReqVO) {
        UserFaceDO userFace = getUserFace(createReqVO.getUserId());
        if (userFace == null) {
            throw exception(USER_FACE_NOT_EXISTS);
        }
        // 匹配人脸信息，官方建议相似度>80分
        double matchScore = apiFaceTencentService.compareFace(createReqVO.getFaceImage(), userFace.getFaceImage());
        if (matchScore < 80) {
            throw exception(USER_FACE_VERIFY_FAIL);
        }
        return true;
    }

    @Override
    public void deleteUserFace(Long id) {
        // 校验存在
        validateUserFaceExists(id);
        // 删除
        userFaceMapper.deleteById(id);
    }

    private void validateUserFaceExists(Long id) {
        if (userFaceMapper.selectById(id) == null) {
            throw exception(USER_FACE_NOT_EXISTS);
        }
    }


    @Override
    public PageResult<UserFaceDO> getUserFacePage(UserFacePageReqVO pageReqVO) {
        return null;
    }

}