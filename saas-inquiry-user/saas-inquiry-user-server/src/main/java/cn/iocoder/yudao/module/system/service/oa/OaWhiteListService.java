package cn.iocoder.yudao.module.system.service.oa;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.oa.vo.OaWhiteListPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.oa.vo.OaWhiteListSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.oa.OaWhiteListDO;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import jakarta.validation.Valid;

/**
 * OA用户白名单 Service 接口
 *
 * <AUTHOR>
 */
public interface OaWhiteListService {

    /**
     * 创建OA用户白名单
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createOaWhiteList(@Valid OaWhiteListSaveReqVO createReqVO);

    /**
     * 更新OA用户白名单
     *
     * @param updateReqVO 更新信息
     */
    void updateOaWhiteList(@Valid OaWhiteListSaveReqVO updateReqVO);

    /**
     * 删除OA用户白名单
     *
     * @param id 编号
     */
    void deleteOaWhiteList(Long id);

    /**
     * 获得OA用户白名单
     *
     * @param id 编号
     * @return OA用户白名单
     */
    OaWhiteListDO getOaWhiteList(Long id);

    /**
     * 获得OA用户白名单 - 不存在报错
     *
     * @param username    OA用户名
     * @param bizTypeEnum 系统类型
     * @return OA用户
     */
    OaWhiteListDO getOaWhiteRequiredByUserName(String username, BizTypeEnum bizTypeEnum);

    /**
     * 获得OA用户白名单分页
     *
     * @param pageReqVO 分页查询
     * @return OA用户白名单分页
     */
    PageResult<OaWhiteListDO> getOaWhiteListPage(OaWhiteListPageReqVO pageReqVO);

}