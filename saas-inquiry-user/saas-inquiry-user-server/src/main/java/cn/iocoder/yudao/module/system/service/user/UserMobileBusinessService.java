package cn.iocoder.yudao.module.system.service.user;

import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantTransfersVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.profile.UserProfileChangeMobileReqVO;

/**
 * 后台用户 Service 接口
 *
 * <AUTHOR>
 */
public interface UserMobileBusinessService {

    /**
     * 用户修改手机号
     *
     * @param loginUserId
     * @param reqVO
     */
    void changeMobile(Long loginUserId, UserProfileChangeMobileReqVO reqVO);


    /**
     * 门店转让
     *
     * @param transfersVO
     */
    void transfers(TenantTransfersVO transfersVO);


    /**
     * 释放锁
     *
     * @param lockKey   key
     * @param lockValue value
     */
    void releaseLock(String lockKey, String lockValue);
}
