package cn.iocoder.yudao.module.system.service.user;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserFingerPrintCheckReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserFingerPrintPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserFingerPrintRespVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserFingerPrintSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.TenantUserFingerPrintDO;
import jakarta.validation.Valid;

/**
 * 门店员工指纹 Service 接口
 *
 * <AUTHOR>
 */
public interface TenantUserFingerPrintService {

    /**
     * 创建门店员工指纹
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTenantUserFingerPrint(@Valid TenantUserFingerPrintSaveReqVO createReqVO);

    /**
     * 更新门店员工指纹
     *
     * @param updateReqVO 更新信息
     */
    Long updateTenantUserFingerPrint(@Valid TenantUserFingerPrintSaveReqVO updateReqVO);

    /**
     * 删除门店员工指纹
     *
     * @param id 编号
     */
    void deleteTenantUserFingerPrint(Long id);

    /**
     * 获得门店员工指纹
     *
     * @param id 编号
     * @return 门店员工指纹
     */
    TenantUserFingerPrintDO getTenantUserFingerPrint(Long id);

    /**
     * 获得门店员工指纹分页
     *
     * @param pageReqVO 分页查询
     * @return 门店员工指纹分页
     */
    PageResult<TenantUserFingerPrintDO> getTenantUserFingerPrintPage(TenantUserFingerPrintPageReqVO pageReqVO);

    /**
     * 获得默认的指纹信息
     *
     * @return
     */
    TenantUserFingerPrintRespVO getTenantUserDefaultFingerPrint();

    /**
     * 校验指纹信息是否正确
     *
     * @param checkReqVO
     */
    void checkFingerPrint(@Valid TenantUserFingerPrintCheckReqVO checkReqVO);

    /**
     * 判断用户是否已经录入指纹
     *
     * @param userId
     * @param tenantId
     * @return
     */
    boolean hasFingerPrint(Long userId);

}