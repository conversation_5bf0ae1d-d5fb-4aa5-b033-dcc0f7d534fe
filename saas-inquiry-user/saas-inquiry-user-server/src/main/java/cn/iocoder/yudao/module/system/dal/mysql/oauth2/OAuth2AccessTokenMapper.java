package cn.iocoder.yudao.module.system.dal.mysql.oauth2;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import cn.iocoder.yudao.module.system.controller.admin.oauth2.vo.token.OAuth2AccessTokenPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import cn.iocoder.yudao.module.system.util.JwtUtil;
import org.apache.ibatis.annotations.Mapper;

import java.time.LocalDateTime;
import java.util.List;

@Mapper
public interface OAuth2AccessTokenMapper extends BaseMapperX<OAuth2AccessTokenDO> {

    @TenantIgnore // 获取 token 的时候，需要忽略门店编号。原因是：一些场景下，可能不会传递 tenant-id 请求头，例如说文件上传、积木报表等等
    default OAuth2AccessTokenDO selectByAccessToken(String accessToken) {
        // accessToken  jwt 转 原token
        return selectOne(OAuth2AccessTokenDO::getAccessToken, JwtUtil.originToken(accessToken));
    }

    default List<OAuth2AccessTokenDO> selectListByRefreshToken(String refreshToken) {
        // refreshToken  jwt 转 原token
        return selectList(OAuth2AccessTokenDO::getRefreshToken, JwtUtil.originToken(refreshToken));
    }

    default PageResult<OAuth2AccessTokenDO> selectPage(OAuth2AccessTokenPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<OAuth2AccessTokenDO>()
            .eqIfPresent(OAuth2AccessTokenDO::getUserId, reqVO.getUserId())
            .eqIfPresent(OAuth2AccessTokenDO::getUserType, reqVO.getUserType())
            .likeIfPresent(OAuth2AccessTokenDO::getClientId, reqVO.getClientId())
            .gt(OAuth2AccessTokenDO::getExpiresTime, LocalDateTime.now())
            .orderByDesc(OAuth2AccessTokenDO::getId));
    }

    default List<OAuth2AccessTokenDO> selectAvailableByUserId(Long userId) {
        return selectList(new LambdaQueryWrapperX<OAuth2AccessTokenDO>()
            .eq(OAuth2AccessTokenDO::getUserId, userId)
            .gt(OAuth2AccessTokenDO::getExpiresTime, LocalDateTime.now()));
    }

    default List<OAuth2AccessTokenDO> selectAvailableByUserIdList(List<Long> userIdList) {
        return selectList(new LambdaQueryWrapperX<OAuth2AccessTokenDO>()
            .in(OAuth2AccessTokenDO::getUserId, userIdList)
            .gt(OAuth2AccessTokenDO::getExpiresTime, LocalDateTime.now()));
    }

    @TenantIgnore
    default List<OAuth2AccessTokenDO> selectAvailableByUserIdTenantIds(Long userId, List<Long> tenantIds) {
        return selectList(new LambdaQueryWrapperX<OAuth2AccessTokenDO>()
            .eq(OAuth2AccessTokenDO::getUserId, userId)
            .in(TenantBaseDO::getTenantId, tenantIds)
            .gt(OAuth2AccessTokenDO::getExpiresTime, LocalDateTime.now()));
    }
}
