package cn.iocoder.yudao.module.system.dal.dataobject.tenant;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.type.StringListTypeHandler;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 门店资质证件信息 DO
 *
 * <AUTHOR>
 */
@TableName(value = "system_tenant_certificate", autoResultMap = true)
// @KeySequence("system_tenant_certificate_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantCertificateDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;

    /**
     * 门店id
     */
    private Long tenantId;

    /**
     * 证件类型 1营业执照 2药品经营许可证 3药品经营质量管理规范认证号
     */
    private Integer certificateType;
    /**
     * 证件名称
     */
    private String certificateName;
    /**
     * 证件号
     */
    private String certificateNo;
    /**
     * 证件url地址
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> certificateImgUrls;
    /**
     * 注册时间
     */
    private LocalDateTime registerTime;
    /**
     * 有效期至
     */
    private LocalDateTime validTime;

}