package cn.iocoder.yudao.module.system.service.tenant;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_ADMIN_NOT_OPERATE;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_RELATION_BIND_NEED_ONE;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_USER_RELATION_NOT_EXISTS;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.USER_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserClockInDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserRelationDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantUserRelationPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantUserRelationRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantUserRelationUpdateVO;
import cn.iocoder.yudao.module.system.convert.tenant.TenantUserRelationConvert;
import cn.iocoder.yudao.module.system.convert.user.UserClockInLogConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantUserRelationDO;
import cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantUserRelationMapper;
import cn.iocoder.yudao.module.system.dal.mysql.user.AdminUserMapper;
import cn.iocoder.yudao.module.system.enums.logger.LoginLogTypeEnum;
import cn.iocoder.yudao.module.system.service.auth.AdminAuthService;
import cn.iocoder.yudao.module.system.service.permission.PermissionService;
import cn.iocoder.yudao.module.system.service.permission.RoleService;
import cn.iocoder.yudao.module.system.service.user.TenantUserClockInLogService;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.enums.user.ClockInTypeEnum;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 门店员工关系 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TenantUserRelationServiceImpl implements TenantUserRelationService {

    @Resource
    private TenantUserRelationMapper tenantUserRelationMapper;


    @Resource
    private AdminUserMapper adminUserMapper;

    @Resource
    @Lazy
    private TenantService tenantService;

    @Resource
    @Lazy
    private PermissionService permissionService;

    @Resource
    @Lazy
    private RoleService roleService;

    @Resource
    @Lazy
    private AdminAuthService adminAuthService;

    @Resource
    private TenantUserClockInLogService tenantUserClockInLogService;

    @Override
    public Long createTenantUserRelation(TenantUserRelationDto relationDto) {
        TenantUserRelationDO tenantUserRelationDO = TenantUserRelationConvert.INSTANCE.dto2Do(relationDto);
        tenantUserRelationDO.setTenantId(TenantContextHolder.getTenantId());

        TenantUserRelationDO relationDO = tenantUserRelationMapper.selectByTenantUserId(relationDto.getUserId(), tenantUserRelationDO.getTenantId());
        if (relationDO != null) {
            tenantUserRelationDO.setId(relationDO.getId());
            tenantUserRelationMapper.updateById(tenantUserRelationDO);
            return relationDO.getId();
        }
        tenantUserRelationMapper.insert(tenantUserRelationDO);
        return tenantUserRelationDO.getId();
    }

    @Override
    public void updateTenantUserRelation(TenantUserRelationDto tenantUserRelationDto) {
        List<TenantUserRelationDO> userRelationDOS = tenantUserRelationMapper.selectByTenantUserId(tenantUserRelationDto.getUserId());
        // 修改
        if (CollUtil.isNotEmpty(userRelationDOS)) {
            List<TenantUserRelationDO> updateUserRelationDOS = TenantUserRelationConvert.INSTANCE.convertUpdateDo(userRelationDOS, tenantUserRelationDto);
            tenantUserRelationMapper.updateBatch(updateUserRelationDOS);
        }
    }

    @Override
    public void deleteTenantUserRelation(Long userId) {
        // 校验存在
        List<TenantUserRelationDO> userRelationDOS = tenantUserRelationMapper.selectByTenantUserId(userId);
        // 删除
        if (CollUtil.isNotEmpty(userRelationDOS)) {
            tenantUserRelationMapper.deleteByIds(userRelationDOS.stream().map(TenantUserRelationDO::getId).collect(Collectors.toList()));
        }
    }


    @Override
    public TenantUserRelationDO getTenantUserRelation(Long id) {
        return tenantUserRelationMapper.selectById(id);
    }

    @Override
    public TenantUserRelationDO getTenantUserRelation(Long userId, Long tenantId) {
        return TenantUtils.executeIgnore(() -> tenantUserRelationMapper.selectByTenantUserId(userId, tenantId));
    }

    @Override
    public List<TenantUserRelationDO> getTenantListByUserId(Long userId) {
        return tenantUserRelationMapper.selectListByUserId(userId);
    }

    @Override
    public List<TenantUserRelationDO> getAvailableTenantListByUserId(Long userId) {
        return tenantUserRelationMapper.selectAvailableListByUserId(userId);
    }

    @Override
    public TenantUserRelationDO getTenantUserRelationByMobile(String mobile) {
        return tenantUserRelationMapper.getTenantUserRelationByMobile(mobile);
    }

    @Override
    public long selectCount() {
        return tenantUserRelationMapper.selectCount();
    }

    @Override
    public PageResult<TenantUserRelationRespVO> pageTenantUserRelation(TenantUserRelationPageReqVO reqVO) {
        // 传userId查某个用户关联门店  并在当前登录userId的可见门店列表内
        if (reqVO.getUserId() != null && !TenantConstant.isSystemTenant()) {
            List<Long> tenantIds = TenantUtils.executeIgnore(() -> CollectionUtils.convertList(getAvailableTenantListByUserId(SecurityFrameworkUtils.getLoginUserId()), TenantUserRelationDO::getTenantId));
            reqVO.setTenantIds(tenantIds);
        }
        if (reqVO.getUserId() == null) {
            reqVO.setUserId(SecurityFrameworkUtils.getLoginUserId());
        }
        if (StringUtils.isNotBlank(reqVO.getRoleCode())) {
            Optional.ofNullable(roleService.selectByCode(reqVO.getRoleCode())).ifPresent(roleDO -> reqVO.setRoleIds(Collections.singletonList(roleDO.getId())));
        }

        IPage<TenantUserRelationRespVO> pageResult = tenantUserRelationMapper.pageTenantUserRelation(MyBatisUtils.buildPage(reqVO), reqVO);
        if (pageResult == null || pageResult.getTotal() == 0) {
            return PageResult.empty();
        }
        return new PageResult<>(pageResult.getRecords(), pageResult.getTotal());
    }


    @Override
    public List<TenantUserRelationDto> getTenantUserRelationsByRoleCode(RoleCodeEnum roleCodeEnum) {
        RoleDO roleDO = roleService.selectByCode(roleCodeEnum.getCode());
        return tenantUserRelationMapper.getTenantUserRelationsByRoleCode(TenantContextHolder.getTenantId(), null, Optional.ofNullable(roleDO).map(RoleDO::getId).orElse(null));
    }

    @Override
    @TenantIgnore
    public List<TenantUserRelationDto> getUserTenantRelationsByRoleCode(RoleCodeEnum roleCodeEnum) {
        RoleDO roleDO = roleService.selectByCode(roleCodeEnum.getCode());
        return tenantUserRelationMapper.getAvailableUserTenantRelationsByRoleCode(SecurityFrameworkUtils.getLoginUserId(), Optional.ofNullable(roleDO).map(RoleDO::getId).orElse(null));
    }

    @Override
    public void updateTenantUserRelationStatus(Long id, Integer status) {
        tenantUserRelationMapper.updateTenantUserRelationStatus(id, status);
    }

    @Override
    public void updateTenantUserRelationStatus(List<Long> ids, Integer status) {
        if (CollUtil.isNotEmpty(ids)) {
            tenantUserRelationMapper.updateTenantUserRelationStatus(ids, status);
        }
    }

    @Override
    public TenantUserRelationDO validateUserRelationExists(Long id) {
        TenantUserRelationDO tenantUserRelationDO = tenantUserRelationMapper.selectById(id);
        if (tenantUserRelationDO == null) {
            throw exception(TENANT_USER_RELATION_NOT_EXISTS);
        }
        return tenantUserRelationDO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void bindTenant(TenantUserRelationUpdateVO bindVO) {

        TenantUserRelationDO tenantUserRelationDO = getUserBindTenantRelationDo(bindVO.getUserId());

        // 1.删除绑定关系
        List<TenantUserRelationDO> userRelationDOS = TenantUtils.executeIgnore(() -> tenantUserRelationMapper.selectByTenantsUserId(bindVO.getUserId(), bindVO.getTenantIds()));
        if (CollUtil.isNotEmpty(userRelationDOS)) {
            TenantUtils.executeIgnore(() -> tenantUserRelationMapper.deleteByIds(userRelationDOS.stream().map(TenantUserRelationDO::getId).collect(Collectors.toList())));
        }
        // 2. 绑定 + 分配角色
        bindAndAssignRole(bindVO, tenantUserRelationDO);
    }

    public TenantUserRelationDO getUserBindTenantRelationDo(Long userId) {

        TenantUserRelationDO tenantUserRelationDO = tenantUserRelationMapper.selectByTenantUserId(userId, TenantContextHolder.getRequiredTenantId());

        if (tenantUserRelationDO == null) {
            tenantUserRelationDO = TenantUserRelationConvert.INSTANCE.convertBindRelationDo(adminUserMapper.selectById(userId));
        }

        if (tenantUserRelationDO == null) {
            throw exception(USER_NOT_EXISTS, userId);
        }

        return tenantUserRelationDO;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void unBindTenant(TenantUserRelationUpdateVO bindVO) {
        // 查询当前用户绑定门店数量,如果是最后一个,不可解除
        List<TenantUserRelationDO> allRelations = TenantUtils.executeIgnore(() -> tenantUserRelationMapper.selectByTenantsUserId(bindVO.getUserId(), null));
        if (CollUtil.size(allRelations) == bindVO.getTenantIds().size()) {
            throw exception(TENANT_RELATION_BIND_NEED_ONE);
        }
        // 1.删除绑定关系
        List<TenantUserRelationDO> userRelationDOS = TenantUtils.executeIgnore(() -> tenantUserRelationMapper.selectByTenantsUserId(bindVO.getUserId(), bindVO.getTenantIds()));
        // 判断解绑 管理员
        List<TenantDO> tenantDOList = tenantService.getTenantByAdminUserId(bindVO.getUserId());
        String adminTenantName = tenantDOList.stream().filter(t -> bindVO.getTenantIds().contains(t.getId())).map(TenantDO::getName).collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(adminTenantName)) {
            throw exception(TENANT_ADMIN_NOT_OPERATE, adminTenantName);
        }
        if (CollUtil.isNotEmpty(userRelationDOS)) {
            TenantUtils.executeIgnore(() -> tenantUserRelationMapper.deleteByIds(userRelationDOS.stream().map(TenantUserRelationDO::getId).collect(Collectors.toList())));
        }
        // 2.解除角色
        permissionService.deleteUserRoleBatchTenant(bindVO.getUserId(), bindVO.getTenantIds(), null);

        //3.退出登录
        adminAuthService.logout(bindVO.getUserId(), bindVO.getTenantIds(), LoginLogTypeEnum.LOGOUT_DELETE);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void reBindNewTenantUserRelation(TenantUserRelationUpdateVO bindVO) {
        if (bindVO.getUserId() == null) {
            return;
        }
        // 当前用户所有绑定关系
        List<TenantUserRelationDO> allRelations = TenantUtils.executeIgnore(() -> tenantUserRelationMapper.selectByTenantsUserId(bindVO.getUserId(), null));
        List<Long> relationIds = allRelations.stream().map(TenantUserRelationDO::getId).collect(Collectors.toList());
        List<Long> tenantIds = allRelations.stream().map(TenantUserRelationDO::getTenantId).collect(Collectors.toList());

        // 如果是管理员，不可操作变更重新绑定
        List<TenantDO> tenantDOList = tenantService.getTenantByAdminUserId(bindVO.getUserId());
        if (CollUtil.isNotEmpty(tenantDOList)) {
            throw exception(TENANT_ADMIN_NOT_OPERATE, "类型");
        }
        // 获取当前用户信息
        TenantUserRelationDO tenantUserRelationDO = getUserBindTenantRelationDo(bindVO.getUserId());

        // 删除所有绑定关系
        if (CollUtil.isNotEmpty(relationIds)) {
            TenantUtils.executeIgnore(() -> tenantUserRelationMapper.deleteByIds(relationIds));
            // 解除角色
            permissionService.deleteUserRoleBatchTenant(bindVO.getUserId(), tenantIds, null);

            // 强制退出登录
            adminAuthService.logout(bindVO.getUserId(), tenantIds, LoginLogTypeEnum.LOGOUT_DELETE);
        }
        // 绑定门店 + 角色
        bindAndAssignRole(bindVO, tenantUserRelationDO);
    }

    /**
     * 绑定门店 + 角色
     *
     * @param bindVO
     */
    private void bindAndAssignRole(TenantUserRelationUpdateVO bindVO, TenantUserRelationDO tenantUserRelationDO) {
        if (CollUtil.isNotEmpty(bindVO.getTenantIds())) {
            // 新增绑定
            List<TenantUserRelationDO> tenantUserRelationDOS = TenantUserRelationConvert.INSTANCE.convertBindDo(tenantUserRelationDO, bindVO);
            tenantUserRelationMapper.insertBatch(tenantUserRelationDOS);
            // 分配角色
            RoleDO roleDO = roleService.selectByCode(bindVO.getRoleCode());
            if (roleDO != null) {
                permissionService.assignUserRoleBatchTenant(bindVO.getUserId(), bindVO.getTenantIds(), Collections.singletonList(roleDO.getId()));
            }
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    @TenantIgnore
    public void updateNeedClockIn(TenantUserRelationUpdateVO relationUpdateVO) {
        List<TenantUserRelationDO> tenantUserRelationDOS = tenantUserRelationMapper.selectByTenantsUserId(relationUpdateVO.getUserId(), relationUpdateVO.getTenantIds());
        if (CollUtil.isEmpty(tenantUserRelationDOS)) {
            throw exception(TENANT_USER_RELATION_NOT_EXISTS);
        }
        List<TenantUserRelationDO> list = tenantUserRelationDOS.stream().peek(t -> t.setNeedClockIn(relationUpdateVO.getNeedClockIn())).toList();
        tenantUserRelationMapper.updateBatch(list);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Long tenantUserClockIn(TenantUserClockInDto clockInDto) {
        clockInDto.setUserId(Optional.ofNullable(clockInDto.getUserId()).orElse(SecurityFrameworkUtils.getLoginUserId()));
        clockInDto.setTenantId(Optional.ofNullable(clockInDto.getTenantId()).orElseGet(TenantContextHolder::getRequiredTenantId));

        TenantUserRelationDO tenantUserRelationDO = tenantUserRelationMapper.selectByTenantUserId(clockInDto.getUserId(), clockInDto.getTenantId());
        if (tenantUserRelationDO == null) {
            throw exception(TENANT_USER_RELATION_NOT_EXISTS);
        }
        // 更新员工上下班状态
        tenantUserRelationDO.setClockInStatus(ClockInTypeEnum.convertClockInType2Status(clockInDto.getClockInType()));
        tenantUserRelationMapper.updateById(tenantUserRelationDO);

        // 记录打卡日志
        return tenantUserClockInLogService.createTenantUserClockInLog(UserClockInLogConvert.INSTANCE.convert(clockInDto));

    }

}