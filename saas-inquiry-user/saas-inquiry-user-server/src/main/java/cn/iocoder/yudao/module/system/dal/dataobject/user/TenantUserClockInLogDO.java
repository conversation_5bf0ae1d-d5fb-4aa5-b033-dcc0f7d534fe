package cn.iocoder.yudao.module.system.dal.dataobject.user;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xyy.saas.inquiry.enums.user.ClockInTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 门店员工打卡记录 DO
 *
 * <AUTHOR>
 */
@TableName("system_tenant_user_clock_in_log")
@KeySequence("system_tenant_user_clock_in_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantUserClockInLogDO extends BaseDO {

    /**
     * 指纹ID
     */
    @TableId
    private Long id;

    /**
     * 门店id
     */
    private Long tenantId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 打卡类型 {@link ClockInTypeEnum}
     */
    private Integer clockInType;

    /**
     * 用户 IP
     */
    private String userIp;
    /**
     * 浏览器 UA
     */
    private String userAgent;

}