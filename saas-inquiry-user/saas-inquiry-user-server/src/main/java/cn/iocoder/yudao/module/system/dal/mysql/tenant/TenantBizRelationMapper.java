package cn.iocoder.yudao.module.system.dal.mysql.tenant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantBizRelationDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantCertificateDO;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.biz.*;
import java.util.List;

/**
 * 门店业务关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantBizRelationMapper extends BaseMapperX<TenantBizRelationDO> {

    default List<TenantBizRelationDO> getTenantBizRelationList(Long tenantId) {
        return selectList(new LambdaQueryWrapperX<TenantBizRelationDO>().eq(TenantBizRelationDO::getTenantId, tenantId));
    }

    default List<TenantBizRelationDO> getBizRelationList(Integer bizType, List<Long> tenantIds) {
        return selectList(new LambdaQueryWrapperX<TenantBizRelationDO>()
            .eq(TenantBizRelationDO::getBizType, bizType).in(TenantBizRelationDO::getTenantId, tenantIds));
    }

    @Delete("delete from system_tenant_biz_relation where id = #{id}")
    void deleteId(Long id);

    default TenantBizRelationDO getBizRelation(Integer bizType, Long tenantId) {
        return selectOne(new LambdaQueryWrapperX<TenantBizRelationDO>()
            .eq(TenantBizRelationDO::getBizType, bizType).eq(TenantBizRelationDO::getTenantId, tenantId));
    }

}