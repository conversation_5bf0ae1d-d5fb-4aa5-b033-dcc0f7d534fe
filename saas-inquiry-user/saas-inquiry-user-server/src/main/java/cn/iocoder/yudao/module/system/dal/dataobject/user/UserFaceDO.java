package cn.iocoder.yudao.module.system.dal.dataobject.user;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 用户人脸信息 DO
 *
 * <AUTHOR>
 */
@TableName("system_users_face")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserFaceDO extends BaseDO {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 用户账号
     */
    private Long userId;
    /**
     * 三方人脸id
     */
    private String faceId;
    /**
     * 人脸图片
     */
    private String faceImage;
    /**
     * 图片类型（0-BASE64 1-URL）
     */
    private Integer faceType;
    /**
     * 帐号状态（0正常 1停用）
     */
    private Integer status;

}