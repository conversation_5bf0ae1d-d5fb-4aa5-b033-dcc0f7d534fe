package cn.iocoder.yudao.module.system.service.user;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_USER_CLOCK_IN_LOG_NOT_EXISTS;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserClockInLogPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserClockInLogSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.TenantUserClockInLogDO;
import cn.iocoder.yudao.module.system.dal.mysql.user.TenantUserClockInLogMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 门店员工打卡记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TenantUserClockInLogServiceImpl implements TenantUserClockInLogService {

    @Resource
    private TenantUserClockInLogMapper tenantUserClockInLogMapper;

    @Override
    public Long createTenantUserClockInLog(TenantUserClockInLogSaveReqVO createReqVO) {
        // 插入
        TenantUserClockInLogDO tenantUserClockInLog = BeanUtils.toBean(createReqVO, TenantUserClockInLogDO.class);
        tenantUserClockInLogMapper.insert(tenantUserClockInLog);
        // 返回
        return tenantUserClockInLog.getId();
    }

    @Override
    public void updateTenantUserClockInLog(TenantUserClockInLogSaveReqVO updateReqVO) {
        // 校验存在
        validateTenantUserClockInLogExists(updateReqVO.getId());
        // 更新
        TenantUserClockInLogDO updateObj = BeanUtils.toBean(updateReqVO, TenantUserClockInLogDO.class);
        tenantUserClockInLogMapper.updateById(updateObj);
    }

    @Override
    public void deleteTenantUserClockInLog(Long id) {
        // 校验存在
        validateTenantUserClockInLogExists(id);
        // 删除
        tenantUserClockInLogMapper.deleteById(id);
    }

    private void validateTenantUserClockInLogExists(Long id) {
        if (tenantUserClockInLogMapper.selectById(id) == null) {
            throw exception(TENANT_USER_CLOCK_IN_LOG_NOT_EXISTS);
        }
    }

    @Override
    public TenantUserClockInLogDO getTenantUserClockInLog(Long id) {
        return tenantUserClockInLogMapper.selectById(id);
    }

    @Override
    public PageResult<TenantUserClockInLogDO> getTenantUserClockInLogPage(TenantUserClockInLogPageReqVO pageReqVO) {
        return tenantUserClockInLogMapper.selectPage(pageReqVO);
    }

}