package cn.iocoder.yudao.module.system.controller.app.dict;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.controller.admin.dict.vo.data.DictDataPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.dict.vo.data.DictDataRespVO;
import cn.iocoder.yudao.module.system.controller.app.dict.vo.AppDictDataRespVO;
import cn.iocoder.yudao.module.system.dal.dataobject.dict.DictDataDO;
import cn.iocoder.yudao.module.system.service.dict.DictDataService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "用户 App - 字典数据")
@RestController
@RequestMapping(value = {"/admin-api/system/dict-data", "/app-api/system/dict-data"})
@Validated
public class AppDictDataController {

    @Resource
    private DictDataService dictDataService;

    @GetMapping("/type")
    @Operation(summary = "根据字典类型查询字典数据信息")
    @Parameter(name = "type", description = "字典类型", required = true, example = "common_status")
    public CommonResult<List<AppDictDataRespVO>> getDictDataListByType(@RequestParam("type") String type) {
        List<DictDataDO> list = dictDataService.getDictDataList(
            CommonStatusEnum.ENABLE.getStatus(), TenantContextHolder.getRequiredTenantId(), type);
        return success(BeanUtils.toBean(list, AppDictDataRespVO.class));
    }


    @GetMapping("/pages")
    @Operation(summary = "/获得字典类型的分页列表")
    public CommonResult<PageResult<DictDataRespVO>> getDictTypePage(@Valid DictDataPageReqVO pageReqVO) {
        pageReqVO.setStatus(CommonStatusEnum.ENABLE.getStatus());
        PageResult<DictDataDO> pageResult = dictDataService.getDictDataPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, DictDataRespVO.class));
    }

    @GetMapping("/types")
    @Operation(summary = "根据字典类型列表查询字典数据信息")
    @Parameter(name = "types", description = "字典类型", required = true, example = "['common_status','xxx']")
    public CommonResult<Map<String, List<AppDictDataRespVO>>> getDictDataListByTypes(@RequestParam("types") List<String> types) {
        Map<String, List<AppDictDataRespVO>> data = dictDataService.getDictDataLists(CommonStatusEnum.ENABLE.getStatus(), TenantContextHolder.getRequiredTenantId(), types);
        return success(data);
    }

}
