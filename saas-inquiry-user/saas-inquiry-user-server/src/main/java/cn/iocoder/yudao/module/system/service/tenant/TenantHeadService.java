package cn.iocoder.yudao.module.system.service.tenant;

import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantRespVO;
import java.util.List;

/**
 * 总部门店 Service 接口
 *
 * <AUTHOR>
 */
public interface TenantHeadService {

    /**
     * 获得总部门店列表
     *
     * @return 总部门店列表
     */
    List<TenantRespVO> getTenantsByHeadId();

    /**
     * 根据总部获取连锁门店id
     *
     * @return
     */
    default List<Long> getTenantIdsByHeadId() {
        return getTenantsByHeadId().stream().map(TenantRespVO::getId).toList();
    }
}
