package cn.iocoder.yudao.module.system.convert.tenant;

import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.certificate.TenantCertificateRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.certificate.TenantCertificateSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantCertificateDO;
import com.xyy.saas.inquiry.pojo.tenant.TenantCertificateRespDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 门店证件 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantCertificateConvert {

    TenantCertificateConvert INSTANCE = Mappers.getMapper(TenantCertificateConvert.class);

    List<TenantCertificateRespVO> convertDo2Vo(List<TenantCertificateDO> certificateDOS);

    List<TenantCertificateDO> convertVo2Do(List<TenantCertificateSaveReqVO> certificates);

    List<TenantCertificateSaveReqVO> convert2Do(List<TenantCertificateRespVO> certs);

    List<TenantCertificateRespDto> convertDtos(List<TenantCertificateRespVO> certificates);
}
