package cn.iocoder.yudao.module.member.controller.app.auth;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.ZHL_TICKET_LOGIN_ERROR;
import static com.xyy.saas.inquiry.util.AESUtil.AESUtil_KEY;
import static cn.iocoder.yudao.framework.common.util.servlet.ServletUtils.getClientIP;

import cn.hutool.core.lang.Assert;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.member.controller.app.auth.vo.AppAuthLoginRespVO;
import cn.iocoder.yudao.module.member.controller.app.auth.vo.AppAuthWeixinMiniAppLoginReqVO;
import cn.iocoder.yudao.module.member.controller.app.auth.vo.AppAuthWeixinMiniAppUserMobileReqVO;
import cn.iocoder.yudao.module.member.controller.app.auth.vo.MobileLoginReqVO;
import cn.iocoder.yudao.module.member.controller.app.auth.vo.WeixinAppAssignTenantLoginReqVO;
import cn.iocoder.yudao.module.member.controller.app.auth.vo.WeixinMiniAppAuthUserMobileSwitchReqVO;
import cn.iocoder.yudao.module.member.service.auth.MemberAuthService;
import cn.iocoder.yudao.module.system.api.sms.SmsCodeApi;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthLoginRespVO;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthLoginTenantReqVO;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.AuthSmsSendReqVO;
import cn.iocoder.yudao.module.system.controller.admin.auth.vo.BindMobileReqVO;
import cn.iocoder.yudao.module.system.convert.auth.AuthConvert;
import cn.iocoder.yudao.module.system.service.auth.AdminAuthService;
import com.alibaba.fastjson.JSONObject;
import cn.iocoder.yudao.module.system.service.tenant.TenantService;
import com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.annotation.security.PermitAll;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "用户 APP - 认证")
@RestController
@RequestMapping(value = {"/admin-api/system/auth", "/app-api/system/auth"})
@Validated
@Slf4j
public class AppAuthController {

    @Resource
    private MemberAuthService authService;

    @Resource
    private AdminAuthService adminAuthService;

    @Resource
    private SmsCodeApi smsCodeApi;

    @PostMapping("/weixin-mini-app-login")
    @PermitAll
    @Operation(summary = "微信小程序的一键登录")
    public CommonResult<AuthLoginRespVO> weixinMiniAppLogin(@RequestBody @Valid AppAuthWeixinMiniAppLoginReqVO reqVO) {
        Assert.notNull(reqVO.getTenantId(), "租户编号不能为空");
        AppAuthLoginRespVO loginRespVO = authService.weixinMiniAppLogin(reqVO);
        return success(adminAuthService.loginWithTenantId(AuthLoginTenantReqVO.builder()
            .token(loginRespVO.getAccessToken())
            .userId(loginRespVO.getUserId())
            .tenantId(reqVO.getTenantId())
            .clientChannelType(ClientChannelTypeEnum.MINI_PROGRAM.getCode())
            .build()));
    }

    @PostMapping("/weixin-mini-app-auth")
    @PermitAll
    @Operation(summary = "小程序认证（微信小程序登录）")
    public CommonResult<AppAuthLoginRespVO> weixinMiniAppAuth(@RequestBody @Valid AppAuthWeixinMiniAppLoginReqVO reqVO) {
        return success(authService.weixinMiniAppLogin(reqVO));
    }

    @PostMapping("/binding-mobile")
    @PermitAll
    @Operation(summary = "绑定手机号")
    public CommonResult<String> bindMobile(@RequestBody @Valid BindMobileReqVO reqVO) {
        return success(authService.bindingMobile(reqVO));
    }

    @PostMapping("/binding-weixin-app-auth-mobile")
    @PermitAll
    @Operation(summary = "绑定小程序授权手机号")
    public CommonResult<String> weixinMiniAppAuthUserMobile(@RequestBody @Valid AppAuthWeixinMiniAppUserMobileReqVO reqVO) {
        return success(authService.bindingWXUserMobile(reqVO));
    }

    @PostMapping("/app-login-with-tenant")
    @PermitAll
    @Operation(summary = "微信登录")
    public CommonResult<AuthLoginRespVO> loginWithTenant(@RequestBody @Valid WeixinAppAssignTenantLoginReqVO reqVO) {

        return success(adminAuthService.loginWithTenantId(AuthLoginTenantReqVO.builder()
            .token(reqVO.getAccessToken())
            .userId(reqVO.getUserId())
            .tenantId(reqVO.getTenantId())
            .clientChannelType(ClientChannelTypeEnum.MINI_PROGRAM.getCode())
            .build()));
    }

    @PostMapping("/login-by-mobile")
    @PermitAll
    @Operation(summary = "手机登录")
    public CommonResult<AppAuthLoginRespVO> loginByPhone(@RequestBody @Valid MobileLoginReqVO reqVO) {

        return success(authService.mobileLogin(reqVO));
    }

    @PostMapping("/send-sms-code-v2")
    @PermitAll
    @Operation(summary = "发送手机验证码")
    public CommonResult<Boolean> senSmsCode(@RequestBody @Valid AuthSmsSendReqVO reqVO) {
        // 发送验证码 不校验手机号
        smsCodeApi.sendSmsCode(AuthConvert.INSTANCE.convert(reqVO).setCreateIp(getClientIP()));
        return success(true);
    }

    @PostMapping("/get-weixin-app-auth-mobile-switch")
    @PermitAll
    @Operation(summary = "获取微信授权手机号开关")
    public CommonResult<Boolean> getWeixinAuthUserMobileSwitch(@RequestBody @Valid WeixinMiniAppAuthUserMobileSwitchReqVO reqVO) {
        return success(authService.getWeixinAuthUserMobileSwitch(reqVO));
    }
}
