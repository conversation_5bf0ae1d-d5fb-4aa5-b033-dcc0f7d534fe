package cn.iocoder.yudao.module.system.mq.producer.sms;

import cn.iocoder.yudao.module.system.mq.message.sms.SmsSendMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationContext;
import org.springframework.stereotype.Component;

/**
 * Sms 短信相关消息的 Producer
 *
 * <AUTHOR>
 * @since 2021/3/9 16:35
 */
@Slf4j
@Component
public class SmsProducer {

    @Resource
    private ApplicationContext applicationContext;

    /**
     * 发送 {@link SmsSendMessage} 消息
     *
     * @param smsSendMessage 短信message消息
     */
    public void sendSmsSendMessage(SmsSendMessage smsSendMessage) {
        applicationContext.publishEvent(smsSendMessage);
    }

}
