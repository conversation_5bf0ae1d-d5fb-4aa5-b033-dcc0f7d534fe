package cn.iocoder.yudao.module.system.service.tenant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageChainStoreReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageReqDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.*;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageRelationDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 门店套餐包信息 Service 接口
 *
 * <AUTHOR>
 */
public interface TenantPackageRelationService {

    /**
     * 创建门店套餐包信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTenantPackageRelation(@Valid TenantPackageRelationSaveReqVO createReqVO);

    List<Long> migrationTenantPackageRelation(@Valid List<TenantPackageRelationSaveReqVO> createReqVOs);

    /**
     * 更新门店套餐包信息
     *
     * @param updateReqVO 更新信息
     */
    void updateTenantPackageRelation(@Valid TenantPackageRelationSaveReqVO updateReqVO);


    /**
     * 批量切换门店套餐包列表
     *
     * @param changeVO 切换VO
     */
    void batchUpdateTenantPackageRelation(TenantPackageRelationChangeVO changeVO);


    /**
     * 更新门店套餐包状态
     *
     * @param invalidReqVO 失效信息VO
     */
    void updateTenantPackageRelationStatus(TenantPackageRelationStatusChangeReqVO invalidReqVO);


    /**
     * 批量更新套餐包状态
     *
     * @param invalidReqVO
     */
    void batchUpdateTenantPackageRelationStatus(TenantPackageRelationStatusChangeReqVO invalidReqVO);


    /**
     * 删除门店套餐包信息
     *
     * @param id 编号
     */
    void deleteTenantPackageRelation(Long id);

    /**
     * 校验门店套餐订单
     *
     * @param id
     * @return
     */
    TenantPackageRelationDO validateTenantPackageRelationExists(Long id);

    /**
     * 获得门店套餐包信息
     *
     * @param id 编号
     * @return 门店套餐包信息
     */
    TenantPackageRelationDO getTenantPackageRelation(Long id);

    /**
     * 获得门店套餐包信息分页
     *
     * @param pageReqVO 分页查询
     * @return 门店套餐包信息分页
     */
    PageResult<TenantPackageRelationRespVO> getTenantPackageRelationPage(TenantPackageRelationPageReqVO pageReqVO);

    /**
     * 获取门店套餐列表
     *
     * @param tenantId 门店id
     * @return
     */
    List<TenantPackageRelationDO> getTenantPackageRelationList(Long tenantId);


    /**
     * 获取门店套餐列表 + vo字段组装
     *
     * @param reqDto 请求dto
     * @return 门店套餐包list
     */
    List<TenantPackageRelationRespVO> getTenantPackageRelationList(TenantPackageReqDto reqDto);

    /**
     * 获取门店套餐列表 + vo字段组装
     *
     * @param reqDto 请求dto
     * @return 门店套餐包list
     */
    List<TenantPackageRelationRespVO> getChainStoreTenantPackageRelationList(TenantPackageChainStoreReqDto reqDto);

    /**
     * 获取门店套餐列表
     *
     * @param reqDto 请求dto
     * @return 门店套餐包list
     */
    List<TenantPackageRelationRespVO> getTenantPackageRelations(TenantPackageReqDto reqDto);

    /**
     * 查询关联得门店套餐包数量
     *
     * @param id 套餐包id
     */
    Long getTenantCountByPackageId(Long id);


}