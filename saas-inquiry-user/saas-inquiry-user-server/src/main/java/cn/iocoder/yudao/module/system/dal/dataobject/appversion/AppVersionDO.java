package cn.iocoder.yudao.module.system.dal.dataobject.appversion;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.type.StringListTypeHandler;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * App版本 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_app_version", autoResultMap = true)
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppVersionDO extends BaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * app类型 0-荷叶问诊,1-智慧脸...
     */
    private Integer appBiz;
    /**
     * app版本，如:v1.0.0
     */
    private String appVersion;
    /**
     * 当前版本编码,如100
     */
    private Integer appVersionCode;
    /**
     * app版本升级内容描述
     */
    private String appVersionDesc;
    /**
     * 系统类型: android 、 ios
     */
    private String osType;
    /**
     * 当前版本支持的最低操作系统版本
     */
    private String minOsType;
    /**
     * 下载地址
     */
    private String downloadUrl;
    /**
     * 升级更新类型：0-强制更新  1-提示可选更新  2-不提示可选更新 {@link com.xyy.saas.inquiry.enums.system.AppVersionUpgradeTypeEnum }
     */
    private Integer upgradeType;
    /**
     * 升级范围：0-全量升级  1-比例用户灰度   2-指定用户灰度 {@link com.xyy.saas.inquiry.enums.system.AppVersionUpgradeScopeEnum }
     */
    private Integer upgradeScope;
    /**
     * 灰度比例0-100
     */
    private Integer grayRatio;

    /**
     * 已上架应用商店
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> releasedChannels;

    /**
     * 是否禁用
     */
    private Boolean disable;

}