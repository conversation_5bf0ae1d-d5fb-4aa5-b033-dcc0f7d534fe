package cn.iocoder.yudao.module.system.convert.user;

import cn.iocoder.yudao.module.system.api.tenant.dto.TenantUserClockInDto;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserClockInLogSaveReqVO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

@Mapper
public interface UserClockInLogConvert {

    UserClockInLogConvert INSTANCE = Mappers.getMapper(UserClockInLogConvert.class);

    @Mapping(target = "clockInType", expression = "java(clockInDto.getClockInType().getCode())")
    TenantUserClockInLogSaveReqVO convert(TenantUserClockInDto clockInDto);
}
