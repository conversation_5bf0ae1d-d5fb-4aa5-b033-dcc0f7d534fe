package cn.iocoder.yudao.module.system.framework.sms.core.client.impl;

import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.hutool.core.util.XmlUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.iocoder.yudao.framework.common.core.KeyValue;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import cn.iocoder.yudao.module.system.framework.sms.core.client.dto.SmsReceiveRespDTO;
import cn.iocoder.yudao.module.system.framework.sms.core.client.dto.SmsSendMessageDTO;
import cn.iocoder.yudao.module.system.framework.sms.core.client.dto.SmsSendRespDTO;
import cn.iocoder.yudao.module.system.framework.sms.core.client.dto.SmsTemplateRespDTO;
import cn.iocoder.yudao.module.system.framework.sms.core.enums.SmsTemplateAuditStatusEnum;
import cn.iocoder.yudao.module.system.framework.sms.core.property.SmsChannelProperties;
import com.alibaba.fastjson.JSON;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.google.common.annotations.VisibleForTesting;
import lombok.Data;
import lombok.experimental.Accessors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;

import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.TIME_ZONE_DEFAULT;

/**
 * 海岩短信功能实现
 * <p>
 * 参见 <a href="https://cloud.tencent.com/document/product/382/52077">文档</a>
 *
 * <AUTHOR>
 */
public class HaiyanSmsClient extends AbstractSmsClient {

    private static final Logger log = LoggerFactory.getLogger(HaiyanSmsClient.class);
    /**
     * 发送短信URL
     */
    private final String SEND_URL = "http://www.duanxin10086.com/v2sms.aspx";

    /**
     * 回复查询URL
     */
    private final String REPLY_URL = "http://www.duanxin10086.com/v2callApi.aspx";

    /**
     * 余额查询URL
     */
    private final String OVERAGE_URL = "http://www.duanxin10086.com/v2sms.aspx";


    /**
     * 调用成功 code
     */
    public static final String API_CODE_SUCCESS = "Success";


    /**
     * 是否国际/港澳台短信：
     * <p>
     * 0：表示国内短信。 1：表示国际/港澳台短信。
     */
    private static final long INTERNATIONAL_CHINA = 0L;


    public HaiyanSmsClient(SmsChannelProperties properties) {
        super(properties);
        Assert.notEmpty(properties.getApiSecret(), "apiSecret 不能为空");
        validateSdkAppId(properties);
    }


    /**
     * 海岩参数 原因是：海岩发放短信的时候，需要额外的参数 userid  也就是海岩给企业分配得companyId 解决方案：考虑到不破坏原有的 apiKey + apiSecret 的结构，所以将 secretId 拼接到 apiKey 字段中，格式为 "secretId companyId"。
     *
     * @param properties 配置
     */
    private static void validateSdkAppId(SmsChannelProperties properties) {
        String combineKey = properties.getApiKey();
        Assert.notEmpty(combineKey, "apiKey 不能为空");
        String[] keys = combineKey.trim().split(" ");
        Assert.isTrue(keys.length == 2, "海岩短信 apiKey 配置格式错误，请配置 为[secretId companyId]");
    }

    private String getCompanyId() {
        return StrUtil.subAfter(properties.getApiKey(), " ", true);
    }

    private String getApiKey() {
        return StrUtil.subBefore(properties.getApiKey(), " ", true);
    }


    @Override
    public SmsSendRespDTO sendSms(Long logId, String mobile, String apiTemplateId, List<KeyValue<String, Object>> templateParams) throws Throwable {
        return null;
    }

    @Override
    public SmsSendRespDTO sendSms(SmsSendMessageDTO sendMessageDTO) throws Throwable {
        // 组装参数
        Map<String, String> map = new LinkedHashMap<>();
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMddHHmmss");
        String timestamp = dateFormat.format(new Date());
        map.put("action", "send");
        map.put("userid", getCompanyId());
        map.put("timestamp", timestamp);
        map.put("sign", MD5.create().digestHex(String.format("%s%s%s", getApiKey(), properties.getApiSecret(), timestamp)));
        map.put("mobile", sendMessageDTO.getMobile());
        map.put("content", sendMessageDTO.getContent());

        String content = map.entrySet().stream()
            .map(entry -> entry.getKey() + "=" + entry.getValue())
            .collect(Collectors.joining("&"));

//        // 发送请求
        HttpResponse response = HttpRequest.post(SEND_URL)
            .header(HttpHeaders.CONTENT_TYPE, MediaType.APPLICATION_FORM_URLENCODED_VALUE)
            .body(content).execute();
        HySmsSendResultDto smsSendResultDto = XmlUtil.xmlToBean(XmlUtil.parseXml(response.body()).getDocumentElement(), HySmsSendResultDto.class);
        log.info("haiYanSmsClient sendSms response: {}", JSON.toJSONString(smsSendResultDto));
        return new SmsSendRespDTO().setSuccess(Objects.equals(smsSendResultDto.getReturnstatus(), API_CODE_SUCCESS)).setSerialNo(smsSendResultDto.getTaskID())
            .setApiRequestId(null).setApiCode(smsSendResultDto.getReturnstatus()).setApiMsg(smsSendResultDto.getMessage());
    }

    @Override
    public List<SmsReceiveRespDTO> parseSmsReceiveStatus(String text) {
        List<SmsReceiveStatus> callback = JsonUtils.parseArray(text, SmsReceiveStatus.class);
        return convertList(callback, status -> new SmsReceiveRespDTO()
            .setSuccess(SmsReceiveStatus.SUCCESS_CODE.equalsIgnoreCase(status.getStatus()))
            .setErrorCode(status.getErrCode()).setErrorMsg(status.getDescription())
            .setMobile(status.getMobile()).setReceiveTime(status.getReceiveTime())
            .setSerialNo(status.getSerialNo()).setLogId(status.getSessionContext().getLogId()));
    }

    @Override
    public SmsTemplateRespDTO getSmsTemplate(String apiTemplateId) throws Throwable {
        // 构建请求
//        DescribeSmsTemplateListRequest request = new DescribeSmsTemplateListRequest();
//        request.setTemplateIdSet(new Long[]{Long.parseLong(apiTemplateId)});
//        request.setInternational(INTERNATIONAL_CHINA);
//        // 执行请求
//        DescribeSmsTemplateListResponse response = null; // client.DescribeSmsTemplateList(request);
//        DescribeTemplateListStatus status = response.getDescribeTemplateStatusSet()[0];
//        if (status == null || status.getStatusCode() == null) {
//            return null;
//        }
        return new SmsTemplateRespDTO().setAuditStatus(SmsTemplateAuditStatusEnum.SUCCESS.getStatus());
    }

    @VisibleForTesting
    Integer convertSmsTemplateAuditStatus(int templateStatus) {
        switch (templateStatus) {
            case 1:
                return SmsTemplateAuditStatusEnum.CHECKING.getStatus();
            case 0:
                return SmsTemplateAuditStatusEnum.SUCCESS.getStatus();
            case -1:
                return SmsTemplateAuditStatusEnum.FAIL.getStatus();
            default:
                throw new IllegalArgumentException(String.format("未知审核状态(%d)", templateStatus));
        }
    }


    @Data
//    @XmlRootElement(name = "returnsms")
    private static class HySmsSendResultDto {

        /**
         * 返回状态值：成功返回Success 失败返回：Faild
         */
        private String returnstatus;

        /**
         * 返回信息
         */
        private String message;

        /**
         * 返回余额
         */
        private Long remainpoint;

        /**
         * 返回本次任务的序列ID
         */
        private String taskID;

        /**
         * 成功短信数：当成功后返回提交成功短信数
         */
        private Long successCounts;


    }

    @Data
    private static class SmsReceiveStatus {

        /**
         * 短信接受成功 code
         */
        public static final String SUCCESS_CODE = "SUCCESS";

        /**
         * 用户实际接收到短信的时间
         */
        @JsonProperty("user_receive_time")
        @JsonFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND, timezone = TIME_ZONE_DEFAULT)
        private LocalDateTime receiveTime;

        /**
         * 国家（或地区）码
         */
        @JsonProperty("nationcode")
        private String nationCode;

        /**
         * 手机号码
         */
        private String mobile;

        /**
         * 实际是否收到短信接收状态，SUCCESS（成功）、FAIL（失败）
         */
        @JsonProperty("report_status")
        private String status;

        /**
         * 用户接收短信状态码错误信息
         */
        @JsonProperty("errmsg")
        private String errCode;

        /**
         * 用户接收短信状态描述
         */
        @JsonProperty("description")
        private String description;

        /**
         * 本次发送标识 ID（与发送接口返回的SerialNo对应）
         */
        @JsonProperty("sid")
        private String serialNo;

        /**
         * 用户的 session 内容（与发送接口的请求参数 SessionContext 一致）
         */
        @JsonProperty("ext")
        private SessionContext sessionContext;

    }

    @VisibleForTesting
    @Data
    @Accessors(chain = true)
    static class SessionContext {

        /**
         * 发送短信记录id
         */
        private Long logId;

    }

}
