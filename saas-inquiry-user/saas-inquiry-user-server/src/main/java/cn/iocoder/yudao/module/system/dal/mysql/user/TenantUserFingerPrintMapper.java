package cn.iocoder.yudao.module.system.dal.mysql.user;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.controller.admin.user.vo.fingerPrint.TenantUserFingerPrintPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.TenantUserFingerPrintDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 门店员工指纹 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantUserFingerPrintMapper extends BaseMapperX<TenantUserFingerPrintDO> {

    default PageResult<TenantUserFingerPrintDO> selectPage(TenantUserFingerPrintPageReqVO reqVO) {
        return selectPage(reqVO, getQueryWrapperX(reqVO));
    }

    default List<TenantUserFingerPrintDO> selectList(TenantUserFingerPrintPageReqVO reqVO) {
        return selectList(getQueryWrapperX(reqVO));
    }

    default TenantUserFingerPrintDO selectOne(TenantUserFingerPrintPageReqVO reqVO) {
        return selectOne(getQueryWrapperX(reqVO), false);
    }

    default Long selectUserFingerCount(Long userId) {
        TenantUserFingerPrintPageReqVO pageReqVO = new TenantUserFingerPrintPageReqVO();
        pageReqVO.setUserId(userId);
        return selectCount(getQueryWrapperX(pageReqVO));
    }

    default List<TenantUserFingerPrintDO> selectUserFingerList(Long userId) {
        TenantUserFingerPrintPageReqVO pageReqVO = new TenantUserFingerPrintPageReqVO();
        pageReqVO.setUserId(userId);
        return selectList(getQueryWrapperX(pageReqVO));
    }


    private static LambdaQueryWrapperX<TenantUserFingerPrintDO> getQueryWrapperX(TenantUserFingerPrintPageReqVO reqVO) {
        return new LambdaQueryWrapperX<TenantUserFingerPrintDO>()
            .eqIfPresent(TenantUserFingerPrintDO::getUserId, reqVO.getUserId())
            .eqIfPresent(TenantUserFingerPrintDO::getFingerPrintInfo, reqVO.getFingerPrintInfo())
            .eqIfPresent(TenantUserFingerPrintDO::getManufacturer, reqVO.getManufacturer())
            .eqIfPresent(TenantUserFingerPrintDO::getDeviceId, reqVO.getDeviceId())
            .betweenIfPresent(TenantUserFingerPrintDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(TenantUserFingerPrintDO::getId);
    }


}