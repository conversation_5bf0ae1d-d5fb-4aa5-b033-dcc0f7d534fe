package cn.iocoder.yudao.module.system.framework.tencent.dto;

import java.io.Serializable;
import cn.hutool.core.annotation.Alias;
import lombok.Data;

/**
 * @Author:chenxiaoyi
 * @Date:2025/08/06 20:04
 */
@Data
public class TencentFaceResultDto implements Serializable {

    @Alias(value = "RequestId")
    private String requestId;

    @Alias(value = "Error")
    private TencentErrorResultDto error;


    @Data
    public static class TencentErrorResultDto implements Serializable {

        @Alias(value = "Code")
        private String code;

        @Alias(value = "Message")
        private String message;

    }
}
