package cn.iocoder.yudao.module.member.controller.admin.auth.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Schema(description = "用户 APP - 微信小程序手机登录 Request VO")
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ZhlBindAccountReqVO implements Serializable {

    @Schema(description = "租户id", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "租户id不能为空")
    private Long tenantId;

}
