package cn.iocoder.yudao.module.system.service.tenant;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantRespVO;
import cn.iocoder.yudao.module.system.convert.tenant.TenantConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantMapper;
import com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * 总部门店 Service 接口
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class TenantHeadServiceImpl implements TenantHeadService {

    @Resource
    private TenantMapper tenantMapper;

    @Resource
    private TenantService tenantService;

    @Override
    public List<TenantRespVO> getTenantsByHeadId() {

        TenantDO tenant = tenantService.validTenant(TenantContextHolder.getRequiredTenantId());
        if (!Objects.equals(tenant.getType(), TenantTypeEnum.CHAIN_HEADQUARTERS.getCode())) {
            return List.of();
        }

        List<TenantDO> tenantDOS = tenantMapper.selectList(TenantDO::getHeadTenantId, tenant.getId());
        return TenantConvert.INSTANCE.convertDo2Vos(tenantDOS);
    }
}
