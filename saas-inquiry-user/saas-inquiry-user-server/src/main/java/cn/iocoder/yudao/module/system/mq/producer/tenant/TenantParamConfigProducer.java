package cn.iocoder.yudao.module.system.mq.producer.tenant;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.mq.tenant.TenantParamConfigEvent;
import org.springframework.stereotype.Component;

/**
 * @Desc 门店配置
 * <AUTHOR>
 */
@Component
@EventBusProducer(
    topic = TenantParamConfigEvent.TOPIC
)
public class TenantParamConfigProducer extends EventBusRocketMQTemplate {


}
