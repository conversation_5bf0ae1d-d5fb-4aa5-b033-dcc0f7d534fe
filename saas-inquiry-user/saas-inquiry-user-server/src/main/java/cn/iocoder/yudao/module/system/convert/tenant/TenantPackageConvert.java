package cn.iocoder.yudao.module.system.convert.tenant;

import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.TenantPackageRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.TenantPackageSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageDO;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem;
import com.xyy.saas.inquiry.util.PrefUtil;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.mapstruct.IterableMapping;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;
import org.mapstruct.factory.Mappers;

/**
 * 门店 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantPackageConvert {

    TenantPackageConvert INSTANCE = Mappers.getMapper(TenantPackageConvert.class);

    // ------------ 套餐包 --------------
    // @Mapping(target = "inquiryWayTypes", expression = "java(createReqVO.getInquiryPackageItems().stream().map(InquiryPackageItem::getInquiryWayType).distinct().collect(java.util.stream.Collectors.toList()))")
    @Mapping(target = "inquiryPackageItems", expression = "java(com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem.convertToNewFormat(createReqVO.getInquiryBizType(),createReqVO.getInquiryPackageItems(),createReqVO"
        + ".getHospitalPrefs()))")
    TenantPackageDO packageSaveReqVO2DO(TenantPackageSaveReqVO createReqVO);

    default TenantPackageDO packageInitReqVO2DO(TenantPackageSaveReqVO createReqVO) {
        TenantPackageDO packageDO = packageSaveReqVO2DO(createReqVO);
        packageDO.setId(null);
        packageDO.setPref(PrefUtil.getTcPref());
        packageDO.setInquiryWayTypes(Optional.ofNullable(packageDO.getInquiryPackageItems()).orElse(List.of()).stream()
            .flatMap(i -> i.effectiveItems().stream())
            .map(InquiryPackageItem::getInquiryWayType).distinct().collect(Collectors.toList()));

        packageDO.setHospitalPrefs(Optional.ofNullable(packageDO.getInquiryPackageItems()).orElse(List.of()).stream()
            .flatMap(i -> i.effectiveItems().stream())
            .flatMap(i -> Optional.ofNullable(i.getHospitalPref()).orElse(List.of()).stream()).distinct().collect(Collectors.toList()));

        packageDO.setPrescriptionTypes(Optional.ofNullable(packageDO.getInquiryPackageItems()).orElse(List.of()).stream()
            .flatMap(i -> Optional.ofNullable(i.getPrescriptionValue()).orElse(List.of()).stream()).distinct().collect(Collectors.toList()));

        return packageDO;
    }

    @Named("defaultVoMapping")
    @Mapping(target = "inquiryPackageItems", expression = "java(com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem.convertToNewFormat(pk.getInquiryBizType(),pk.getInquiryPackageItems(),pk.getHospitalPrefs()))")
    TenantPackageRespVO convertVo(TenantPackageDO pk);

}
