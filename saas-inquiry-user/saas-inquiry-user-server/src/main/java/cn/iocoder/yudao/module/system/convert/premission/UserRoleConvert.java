package cn.iocoder.yudao.module.system.convert.premission;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.module.system.controller.admin.permission.vo.permission.UserPermissionVO;
import cn.iocoder.yudao.module.system.controller.admin.permission.vo.permission.UserRoleVO;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.UserRoleDO;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.Set;

/**
 * 用户角色 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface UserRoleConvert {

    UserRoleConvert INSTANCE = Mappers.getMapper(UserRoleConvert.class);

    List<UserRoleVO> convertVo(List<UserRoleDO> userListByRoleIds);

    default UserPermissionVO convertPermission(List<RoleDO> roles , List<Long> tenantIdList){
        Set<String> roleCodes = CollectionUtils.convertSet(roles, RoleDO::getCode);
        return UserPermissionVO.builder()
            .tenantIdList(tenantIdList)
            .drugStore(tenantIdList.stream().anyMatch(tenantId -> ObjectUtil.notEqual(tenantId, TenantConstant.DEFAULT_TENANT_ID)))
            .pharmacist(roleCodes.contains(RoleCodeEnum.PHARMACIST.getCode()))
            .physician(roleCodes.contains(RoleCodeEnum.DOCTOR.getCode()))
            .build();
    }
}
