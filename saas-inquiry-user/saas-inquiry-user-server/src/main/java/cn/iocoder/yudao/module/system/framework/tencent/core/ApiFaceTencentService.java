package cn.iocoder.yudao.module.system.framework.tencent.core;

import static cn.hutool.crypto.digest.DigestUtil.sha256Hex;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception0;

import cn.hutool.core.date.format.FastDateFormat;
import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.digest.DigestUtil;
import cn.hutool.crypto.digest.HmacAlgorithm;
import cn.hutool.json.JSONUtil;
import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.framework.common.util.http.HttpUtils;
import cn.iocoder.yudao.module.system.framework.tencent.dto.TencentDetectFaceDto;
import cn.iocoder.yudao.module.system.framework.tencent.dto.TencentFaceAddUserDto;
import cn.iocoder.yudao.module.system.framework.tencent.dto.TencentFaceResultDto;
import cn.iocoder.yudao.module.system.framework.tencent.dto.TencentVerifyFaceDto;

import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.TimeZone;
import java.util.TreeMap;

import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @Author:chenxiaoyi
 * @Date:2025/08/06 15:35
 */
@Component
@Slf4j
public class ApiFaceTencentService {

    @Value("${tencent.api.face.group-id:}")
    private String faceGroupId;

    @Value("${tencent.api.face.secret-id:}")
    private String faceSecretId;

    @Value("${tencent.api.face.secret-key:}")
    private String faceSecretKey;

    private static final String HOST = "iai.tencentcloudapi.com";
    private static final String VERSION = "2020-03-03";
    private static final String REGION = "ap-guangzhou";
    private static final String SERVICE = "iai";

    // public static void main(String[] args) {
    //     String s1 = FileUtil.readString(new File("F://1.txt"), StandardCharsets.UTF_8);
    //     String s2 = FileUtil.readString(new File("F://2.txt"), StandardCharsets.UTF_8);
    //     final double v = new ApiFaceTencentService().compareFace(s1, s2);
    //     System.out.println(v);
    // }

    /**
     * 人脸检测与分析 - 判断是否人脸检测通过
     */
    public void detectFace(String faceBase64) {
        TreeMap<String, Object> body = new TreeMap<>();
        body.put("Image", faceBase64);
        request("DetectFace", body, TencentDetectFaceDto.class);
    }

    /**
     * 添加用户
     */
    public String addUser(String userName, String userId, String faceBase64) {

        TreeMap<String, Object> body = new TreeMap<>();
        body.put("GroupId", faceGroupId);
        body.put("PersonName", userName);
        body.put("PersonId", userId);
        body.put("Image", faceBase64);
        TencentFaceAddUserDto addUserDto = request("CreatePerson", body, TencentFaceAddUserDto.class);
        return addUserDto.getFaceId();
    }

    /**
     * 删除用户
     */
    public void deleteUser(String userId) {
        TreeMap<String, Object> body = new TreeMap<>();
        body.put("PersonId", userId);
        try {
            request("DeletePerson", body, TencentFaceAddUserDto.class);
        } catch (Exception ignore) {
        }
    }

    /**
     * 人脸对比
     */
    public double compareFace(String faceBase64, String registerBase64) {
        TreeMap<String, Object> body = new TreeMap<>();
        body.put("ImageA", faceBase64);
        body.put("ImageB", registerBase64);
        TencentVerifyFaceDto verifyFace = request("CompareFace", body, TencentVerifyFaceDto.class);
        return verifyFace.getScore();
    }

    /**
     * 人脸验证
     */
    public Boolean verifyFace(String userId, String faceBase64) {
        TreeMap<String, Object> body = new TreeMap<>();
        body.put("PersonId", userId);
        body.put("Image", faceBase64);
        TencentVerifyFaceDto verifyFace = request("VerifyFace", body, TencentVerifyFaceDto.class);
        return BooleanUtil.isTrue(verifyFace.getIsMatch());
    }

    /**
     * 请求腾讯云人脸识别
     *
     * @param action 请求的 API 名称
     * @param body   请求参数
     * @return 请求结果
     * @see <a href="https://cloud.tencent.com/document/product/867/44978#Java">签名方法 v3</a>
     */
    private <T extends TencentFaceResultDto> T request(String action, TreeMap<String, Object> body, Class<T> clazz) {
        // 1.1 请求 Header
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json; charset=utf-8");
        headers.put("Host", HOST);
        headers.put("X-TC-Action", action);
        Date now = new Date();
        String nowStr = FastDateFormat.getInstance("yyyy-MM-dd", TimeZone.getTimeZone("UTC")).format(now);
        headers.put("X-TC-Timestamp", String.valueOf(now.getTime() / 1000));
        headers.put("X-TC-Version", VERSION);
        headers.put("X-TC-Region", REGION);

        // 1.2 构建签名 Header
        String canonicalQueryString = "";
        String canonicalHeaders = "content-type:application/json; charset=utf-8\n"
            + "host:" + HOST + "\n" + "x-tc-action:" + action.toLowerCase() + "\n";
        String signedHeaders = "content-type;host;x-tc-action";
        String canonicalRequest = "POST" + "\n" + "/" + "\n" + canonicalQueryString + "\n" + canonicalHeaders + "\n"
            + signedHeaders + "\n" + sha256Hex(JSONUtil.toJsonStr(body));
        String credentialScope = nowStr + "/" + SERVICE + "/" + "tc3_request";
        String stringToSign = "TC3-HMAC-SHA256" + "\n" + now.getTime() / 1000 + "\n" + credentialScope + "\n" +
            sha256Hex(canonicalRequest);
        byte[] secretService = hmac256(hmac256(("TC3" + faceSecretKey).getBytes(StandardCharsets.UTF_8), nowStr), SERVICE);
        String signature = HexUtil.encodeHexStr(hmac256(hmac256(secretService, "tc3_request"), stringToSign));
        headers.put("Authorization", "TC3-HMAC-SHA256" + " " + "Credential=" + faceSecretId + "/" + credentialScope + ", "
            + "SignedHeaders=" + signedHeaders + ", " + "Signature=" + signature);

        // 2. 发起请求
        String responseBody = HttpUtils.post("https://" + HOST, headers, JSONUtil.toJsonStr(body));

        cn.hutool.json.JSONObject responseResult = JSONUtil.parseObj(responseBody).getJSONObject("Response");
        T result = com.alibaba.fastjson.JSONObject.parseObject(responseResult.toString(), clazz);
        log.info("请求腾讯云人脸识别结果：action:{},result:{},request:{}", action, responseBody, StringUtils.substring(JSONUtil.toJsonStr(body), 0, 200));
        if (result.getError() != null) {
            throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), result.getError().getMessage());
        }
        return result;
    }

    private static byte[] hmac256(byte[] key, String msg) {
        return DigestUtil.hmac(HmacAlgorithm.HmacSHA256, key).digest(msg);
    }
}
