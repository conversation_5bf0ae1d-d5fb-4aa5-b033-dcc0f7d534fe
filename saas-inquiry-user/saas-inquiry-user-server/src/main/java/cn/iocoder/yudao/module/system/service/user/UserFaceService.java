package cn.iocoder.yudao.module.system.service.user;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.app.user.vo.UserFacePageReqVO;
import cn.iocoder.yudao.module.system.controller.app.user.vo.UserFaceSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.user.UserFaceDO;
import jakarta.validation.Valid;

/**
 * 用户人脸信息 Service 接口
 *
 * <AUTHOR>
 */
public interface UserFaceService {


    /**
     * 校验是否有人脸信息
     *
     * @param userId
     * @return
     */
    UserFaceDO validateFace(Long userId);

    /**
     * 创建 或者 修改 用户人脸信息
     *
     * @param createReqVO
     * @return
     */
    Long saveUserFace(UserFaceSaveReqVO createReqVO);

    /**
     * 匹配用户人脸信息
     *
     * @param createReqVO
     * @return
     */
    Boolean matchUserFace(UserFaceSaveReqVO createReqVO);


    /**
     * 删除用户人脸信息
     *
     * @param id 编号
     */
    void deleteUserFace(Long id);

    /**
     * 获得用户人脸信息
     *
     * @param id 编号
     * @return 用户人脸信息
     */
    UserFaceDO getUserFace(Long userId);

    /**
     * 获得用户人脸信息分页
     *
     * @param pageReqVO 分页查询
     * @return 用户人脸信息分页
     */
    PageResult<UserFaceDO> getUserFacePage(UserFacePageReqVO pageReqVO);
}