package cn.iocoder.yudao.module.system.util;

import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.validation.annotation.Validated;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@ConfigurationProperties(prefix = "jose4j.token")
@Validated
@Data
public class Jose4jTokenProperties {

    // 令牌公私钥对
    @NotNull(message = "keypair 不能为空")
    private String keypair;

    // 令牌有效期（默认30分钟），默认30分钟
    @Min(value = 60, message = "expireSeconds 不能小于60")
    private int expireSeconds = 1800;

    // 令牌签发者 默认inquiry-user
    @NotNull(message = "issuer 不能为空")
    private String issuer = "inquiry-user";

    // 令牌有效期起始时间（默认1分钟之前）
    private int notBeforeMinutesInThePast = 1;
}
