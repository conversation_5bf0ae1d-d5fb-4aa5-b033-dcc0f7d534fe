package cn.iocoder.yudao.module.system.dal.mysql.biz;

import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.module.system.dal.dataobject.biz.BizDO;
import org.apache.ibatis.annotations.Mapper;
import cn.iocoder.yudao.module.system.controller.admin.biz.vo.*;
import java.util.List;

/**
 * 业务 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface BizMapper extends BaseMapperX<BizDO> {

    default List<BizDO> selectList(BizListReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<BizDO>()
            .likeIfPresent(BizDO::getName, reqVO.getName())
            .inIfPresent(BizDO::getType, reqVO.getTypeList())
            .eqIfPresent(BizDO::getType, reqVO.getType()));
    }

}