package cn.iocoder.yudao.module.infra.service.file;


import cn.hutool.core.lang.Assert;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.io.FileUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.infra.controller.admin.file.vo.file.FileCreateReqVO;
import cn.iocoder.yudao.module.infra.controller.admin.file.vo.file.FilePageReqVO;
import cn.iocoder.yudao.module.infra.controller.admin.file.vo.file.FilePresignedUrlRespVO;
import cn.iocoder.yudao.module.infra.dal.dataobject.file.FileDO;
import cn.iocoder.yudao.module.infra.dal.mysql.file.FileMapper;
import cn.iocoder.yudao.module.infra.framework.file.config.FileUploadProperties;
import cn.iocoder.yudao.module.infra.framework.file.core.client.FileClient;
import cn.iocoder.yudao.module.infra.framework.file.core.client.s3.FilePresignedUrlRespDTO;
import cn.iocoder.yudao.module.infra.framework.file.core.utils.FileTypeUtils;
import jakarta.annotation.Resource;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.module.infra.enums.ErrorCodeConstants.FILE_NOT_EXISTS;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Service
public class FileServiceImpl implements FileService {

    @Resource
    private FileConfigService fileConfigService;

    @Resource
    private FileMapper fileMapper;

    @Resource
    private FileUploadProperties fileUploadProperties;

    @Override
    public PageResult<FileDO> getFilePage(FilePageReqVO pageReqVO) {
        return fileMapper.selectPage(pageReqVO);
    }

    @Override
    public String createFile(String name, String path, byte[] content, boolean... pub) {
        // 参数校验
        if (content == null || content.length == 0) {
            throw new IllegalArgumentException("文件内容不能为空");
        }

        // 文件大小限制检查
        if (fileUploadProperties.isEnableSizeCheck() && content.length > fileUploadProperties.getMaxFileSize()) {
            throw new IllegalArgumentException("文件大小超过限制: " + content.length + " bytes, 最大允许: " + fileUploadProperties.getMaxFileSize() + " bytes");
        }

        // 大文件警告
        if (fileUploadProperties.isEnableLargeFileWarning() && content.length > fileUploadProperties.getLargeFileThreshold()) {
            log.warn("上传大文件: name={}, size={}MB", name, content.length / 1024 / 1024);
        }

        log.info("开始创建文件: name={}, originalPath={}, size={}", name, path, content.length);

        try {
            // 计算默认的 path 名
            String type = FileTypeUtils.getMineType(content, name);
            if (StrUtil.isEmpty(path)) {
                path = FileUtils.generatePath(content, name);
            }
            // 如果 name 为空，则使用 path 填充
            if (StrUtil.isEmpty(name)) {
                name = path;
            }

            // 上传到文件存储器
            FileClient client = fileConfigService.getMasterFileClient();
            Assert.notNull(client, "客户端(master) 不能为空");

            String url;
            try {
                url = client.upload(content, path, type, pub);
                log.info("文件上传成功: name={}, path={}, url={}", name, path, url);
            } catch (Exception e) {
                log.error("文件上传失败: name={}, path={}, size={}", name, path, content.length, e);
                throw new RuntimeException("文件上传失败: " + e.getMessage(), e);
            }

            // 保存到数据库 - 如果失败需要删除已上传的文件
            FileDO file = new FileDO();
            file.setConfigId(client.getId());
            file.setName(name);
            file.setPath(path);
            file.setUrl(url);
            file.setType(type);
            file.setSize(content.length);

            try {
                fileMapper.insert(file);
                log.info("文件记录保存成功: id={}, name={}, path={}", file.getId(), name, path);
            } catch (Exception e) {
                log.error("文件记录保存失败，尝试删除已上传的文件: path={}", path, e);
                // 尝试删除已上传的文件以保持数据一致性
                try {
                    client.delete(path);
                    log.info("已删除上传的文件: path={}", path);
                } catch (Exception deleteException) {
                    log.error("删除上传文件失败，可能存在孤立文件: path={}", path, deleteException);
                }
                throw new RuntimeException("保存文件记录失败: " + e.getMessage(), e);
            }

            return url;

        } catch (Exception e) {
            log.error("创建文件失败: name={}, path={}, size={}", name, path, content != null ? content.length : 0, e);
            // 重新抛出运行时异常，保持原有的异常处理逻辑
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            }
            throw new RuntimeException("创建文件失败: " + e.getMessage(), e);
        }
    }

    @Override
    public Long createFile(FileCreateReqVO createReqVO) {
        FileDO file = BeanUtils.toBean(createReqVO, FileDO.class);
        fileMapper.insert(file);
        return file.getId();
    }

    @Override
    public void deleteFile(Long id) throws Exception {
        // 校验存在
        FileDO file = validateFileExists(id);

        // 从文件存储器中删除
        FileClient client = fileConfigService.getFileClient(file.getConfigId());
        Assert.notNull(client, "客户端({}) 不能为空", file.getConfigId());
        client.delete(file.getPath());

        // 删除记录
        fileMapper.deleteById(id);
    }

    private FileDO validateFileExists(Long id) {
        FileDO fileDO = fileMapper.selectById(id);
        if (fileDO == null) {
            throw exception(FILE_NOT_EXISTS);
        }
        return fileDO;
    }

    @Override
    public byte[] getFileContent(Long configId, String path) throws Exception {
        // 参数校验
        if (configId == null) {
            throw new IllegalArgumentException("配置ID不能为空");
        }
        if (StrUtil.isEmpty(path)) {
            throw new IllegalArgumentException("文件路径不能为空");
        }

        log.debug("获取文件内容: configId={}, path={}", configId, path);

        try {
            FileClient client = fileConfigService.getFileClient(configId);
            Assert.notNull(client, "客户端({}) 不能为空", configId);

            byte[] content = client.getContent(path);
            log.debug("文件内容获取成功: configId={}, path={}, size={}", configId, path, content != null ? content.length : 0);
            return content;

        } catch (Exception e) {
            log.error("获取文件内容失败: configId={}, path={}", configId, path, e);
            throw e; // 保持原有异常抛出逻辑
        }
    }

    @Override
    public FilePresignedUrlRespVO getFilePresignedUrl(String path) throws Exception {
        FileClient fileClient = fileConfigService.getMasterFileClient();
        FilePresignedUrlRespDTO presignedObjectUrl = fileClient.getPresignedObjectUrl(path);
        return BeanUtils.toBean(presignedObjectUrl, FilePresignedUrlRespVO.class,
            object -> object.setConfigId(fileClient.getId()));
    }

    @Override
    public String getPrivateFilePreSignedUrl(String url)  throws Exception{
        FileClient fileClient = fileConfigService.getMasterFileClient();
        return fileClient.getPrivateFilePreSignedUrl(url);
    }
}
