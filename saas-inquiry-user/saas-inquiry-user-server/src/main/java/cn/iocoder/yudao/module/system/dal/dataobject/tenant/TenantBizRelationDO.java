package cn.iocoder.yudao.module.system.dal.dataobject.tenant;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import lombok.experimental.Accessors;

/**
 * 门店业务关系 DO
 *
 * <AUTHOR>
 */
@TableName("system_tenant_biz_relation")
@KeySequence("system_tenant_biz_relation_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class TenantBizRelationDO extends BaseDO {

    /**
     * 自增编号
     */
    @TableId
    private Long id;
    /**
     * 门店id
     */
    private Long tenantId;
    /**
     * 业务类型
     */
    private Integer bizType;
    /**
     * 租户类型（1-单店 2连锁门店 3连锁总部）
     */
    private Integer tenantType;
    /**
     * 租户编号(总部)
     */
    private Long headTenantId;
    /**
     * 账号数量
     */
    private Integer accountCount;

}