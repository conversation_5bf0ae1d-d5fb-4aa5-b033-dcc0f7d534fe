package cn.iocoder.yudao.module.system.dal.dataobject.appversion;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * app版本用户详情 DO
 *
 * <AUTHOR>
 */
@TableName("saas_app_version_detail")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AppVersionDetailDO extends BaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * app版本id
     */
    private Integer appVersionId;
    /**
     * 业务类型：0-灰度租户  1-用户忽略版本
     */
    private Integer bussnissType;
    /**
     * 忽略用户id
     */
    private Long userId;

    /**
     * 灰度租户Id
     */
    private Long tenantId;
}