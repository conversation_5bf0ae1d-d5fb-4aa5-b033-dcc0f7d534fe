package cn.iocoder.yudao.module.system.dal.mysql.permission;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.controller.admin.permission.vo.role.RolePageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.lang.Nullable;

import java.util.Collection;
import java.util.List;
import java.util.Set;

@Mapper
public interface RoleMapper extends BaseMapperX<RoleDO> {

    default PageResult<RoleDO> selectPage(RolePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<RoleDO>()
            .likeIfPresent(RoleDO::getName, reqVO.getName())
            .likeIfPresent(RoleDO::getCode, reqVO.getCode())
            .eqIfPresent(RoleDO::getStatus, reqVO.getStatus())
            .inIfPresent(RoleDO::getType, reqVO.getTypes())
            .betweenIfPresent(BaseDO::getCreateTime, reqVO.getCreateTime())
            .notIn(CollUtil.isNotEmpty(reqVO.getNoRoleCodes()), RoleDO::getCode, reqVO.getNoRoleCodes())
            .orderByAsc(RoleDO::getSort));
    }

    default RoleDO selectByName(String name) {
        return selectOne(RoleDO::getName, name);
    }

    default RoleDO selectByCode(String code) {
        return selectOne(RoleDO::getCode, code, RoleDO::getStatus, CommonStatusEnum.ENABLE.getStatus());
    }

    default List<RoleDO> selectListByStatus(@Nullable Collection<Integer> statuses) {
        return selectList(RoleDO::getStatus, statuses);
    }

    default List<RoleDO> selectByCodes(Set<String> codes) {
        return selectList(new LambdaQueryWrapperX<RoleDO>().in(RoleDO::getCode, codes).eq(RoleDO::getStatus, CommonStatusEnum.ENABLE.getStatus()));
    }


    IPage<RoleDO> selectPageStore(Page<RoleDO> objectPage, RolePageReqVO reqVO);


}
