package cn.iocoder.yudao.module.system.service.appversion.strategy;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.module.system.controller.app.appversion.vo.CheckAppVersionReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.appversion.AppVersionDO;
import cn.iocoder.yudao.module.system.dal.dataobject.appversion.AppVersionDetailDO;
import cn.iocoder.yudao.module.system.dal.mysql.appversion.AppVersionDetailMapper;
import com.xyy.saas.inquiry.enums.system.AppVersionDetailBussnissTypeEnum;
import com.xyy.saas.inquiry.enums.system.AppVersionUpgradeScopeEnum;
import com.xyy.saas.inquiry.enums.system.AppVersionUpgradeTypeEnum;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

/**
 * @Author: xucao
 * @Date: 2025/01/20 18:59
 * @Description: app 版本匹配策略
 */
@Component
public abstract class AppVersionMatchStrategy {

    @Resource
    private AppVersionDetailMapper appVersionDetailMapper;

    /**
     * 检查版本是否匹配
     * @param appVersionDO 版本池版本信息
     * @param reqVO 客户端请求信息
     * @return 匹配时返回当前版本，不匹配返回null
     */
    public abstract AppVersionDO match(AppVersionDO appVersionDO, CheckAppVersionReqVO reqVO);

    /**
     * 获取当前策略对应的更新范围
     * @return 当前策略对应的更新范围
     */
    public abstract AppVersionUpgradeScopeEnum getUpgradeScope();

    /**
     * 检查版本是否匹配
     *
     * @param appVersionDO 版本池版本信息
     * @param reqVO        客户端请求信息
     * @return 匹配时返回当前版本，不匹配返回null
     */
    public AppVersionDO baseMatch(AppVersionDO appVersionDO, CheckAppVersionReqVO reqVO) {
        // 用户主动检查更新直接返回当前版本
        if(ObjectUtil.equals(reqVO.getVoluntaryCheck(),Boolean.TRUE)){
            return appVersionDO;
        }
        //当前版本升级类型为 强制升级 ，直接返回当前版本
        if(AppVersionUpgradeTypeEnum.FORCE_UPDATE.getCode().equals(appVersionDO.getUpgradeType())){
            return appVersionDO;
        }
        //当前版本为提示可选升级时，如果userId为空，直接返回当前版本
        if(AppVersionUpgradeTypeEnum.OPTIONAL_UPDATE.getCode().equals(appVersionDO.getUpgradeType()) && ObjectUtils.isEmpty(reqVO.getUserId())){
            return appVersionDO;
        }
        //当前版本为提示可选升级时，如果userId不为空，需要判断当前用户是否已忽略此版本
        boolean isIgnore = isIgnore(appVersionDO,reqVO);
        if(!isIgnore){
            //未忽略情况下，返回当前版本
            return appVersionDO;
        }
        //其他情况，不返回当前版本
        return null;
    }

    /**
     * 检查用户是否忽略当前版本
     * @param appVersionDO 版本池版本信息
     * @param reqVO 客户端入参
     * @return true-已忽略，false-未忽略
     */
    public boolean isIgnore(AppVersionDO appVersionDO, CheckAppVersionReqVO reqVO) {
        AppVersionDetailDO appVersionDetailDO = appVersionDetailMapper.selectOne(AppVersionDetailDO::getAppVersionId, appVersionDO.getId() , AppVersionDetailDO::getBussnissType, AppVersionDetailBussnissTypeEnum.USER_IGNORE_VERSION.getCode(),
            AppVersionDetailDO::getUserId,
            reqVO.getUserId());
        return !ObjectUtils.isEmpty(appVersionDetailDO);
    }

    /**
     * 检查租户是否灰度
     * @param appVersionDO 版本池版本信息
     * @param reqVO 客户端入参
     * @return true-灰度租户，false-非灰度租户
     */
    public boolean isGrayTenant(AppVersionDO appVersionDO, CheckAppVersionReqVO reqVO) {
        AppVersionDetailDO appVersionDetailDO = appVersionDetailMapper.selectOne(AppVersionDetailDO::getAppVersionId, appVersionDO.getId(), AppVersionDetailDO::getBussnissType, AppVersionDetailBussnissTypeEnum.GRAY_TENANT.getCode(),
            AppVersionDetailDO::getTenantId , reqVO.getTenantId());
        return !ObjectUtils.isEmpty(appVersionDetailDO);
    }
}
