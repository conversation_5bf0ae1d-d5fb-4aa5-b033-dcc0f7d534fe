package cn.iocoder.yudao.module.system.mq.consumer.tenant;

import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.service.tenant.TenantPackageShareRelationService;
import cn.iocoder.yudao.module.system.service.tenant.TenantService;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.mq.tenant.TenantInfoUpdateEvent;
import com.xyy.saas.inquiry.mq.tenant.TenantInfoUpdateMessageDto;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.util.Objects;

/**
 * @Author: chenxiaoyi
 * @DateTime: 2025/4/24 14:21
 * @Description: 租户信息变更，套餐共享业务监听消费者
 **/
@Component
@Slf4j
@EventBusConsumer(groupId = "cn_iocoder_yudao_module_system_mq_consumer_tenant_TenantInfoUpdateSharePackageConsumer", topic = TenantInfoUpdateEvent.TOPIC)
public class TenantInfoUpdateSharePackageConsumer {

    @Resource
    private TenantPackageShareRelationService tenantPackageShareRelationService;

    @Resource
    private TenantService tenantService;

    @EventBusListener
    public void tenantUpdSharePackageOnMessage(TenantInfoUpdateEvent event) {
        // 旧门店信息
        TenantInfoUpdateMessageDto msg = event.getMsg();
        // 查询当前门店信息
        TenantDO tenant = tenantService.getTenant(msg.getId());

        if (msg.getHeadTenantId() != null && !Objects.equals(msg.getHeadTenantId(), tenant.getHeadTenantId())) {
            log.info("解绑总部门店套餐关系, id = {}, headTenantId = {}", tenant.getId(), msg.getHeadTenantId());
            tenantPackageShareRelationService.unBindTenantPackageShareRelation(tenant.getId(), msg.getHeadTenantId());
        }
    }
}
