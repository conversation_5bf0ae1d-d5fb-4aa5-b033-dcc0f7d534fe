package cn.iocoder.yudao.module.infra.controller.admin.file.vo.file;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

@Schema(description = "管理后台 - 文件创建 Request VO")
@Data
public class FileCreateReqVO {

    @Schema(description = "配置编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "配置编号不能为空")
    private Long configId;

    @Schema(description = "文件名", requiredMode = Schema.RequiredMode.REQUIRED, example = "test.jpg")
    @NotBlank(message = "文件名不能为空")
    private String name;

    @Schema(description = "文件路径", requiredMode = Schema.RequiredMode.REQUIRED, example = "2024/01/01/test.jpg")
    @NotBlank(message = "文件路径不能为空")
    private String path;

    @Schema(description = "文件 URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://example.com/test.jpg")
    @NotBlank(message = "文件 URL 不能为空")
    private String url;

    @Schema(description = "文件类型", example = "jpg")
    private String type;

    @Schema(description = "文件大小", example = "1024")
    private Integer size;

}
