package cn.iocoder.yudao.module.system.api.tenant;

import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageShareRelationDto;
import cn.iocoder.yudao.module.system.convert.tenant.TenantPackageShareConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageShareRelationDO;
import cn.iocoder.yudao.module.system.service.tenant.TenantPackageShareRelationService;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * 门店套餐共享Api
 *
 * <AUTHOR>
 */
@Service
public class TenantPackageShareApiImpl implements TenantPackageShareApi {

    @Resource
    private TenantPackageShareRelationService tenantPackageShareRelationService;

    @Override
    public List<TenantPackageShareRelationDto> getTenantPackageShareRelationList(Long tenantId, BizTypeEnum bizTypeEnum) {

        List<TenantPackageShareRelationDO> shareRelationList = tenantPackageShareRelationService.getTenantPackageShareRelationList(tenantId, bizTypeEnum);

        return TenantPackageShareConvert.INSTANCE.convertDo2Dto(shareRelationList);
    }
}
