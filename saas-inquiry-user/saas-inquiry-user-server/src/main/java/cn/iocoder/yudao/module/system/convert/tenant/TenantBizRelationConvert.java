package cn.iocoder.yudao.module.system.convert.tenant;

import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.biz.TenantBizRelationRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.biz.TenantBizRelationSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.biz.BizDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantBizRelationDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
 * 业务线 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantBizRelationConvert {

    TenantBizRelationConvert INSTANCE = Mappers.getMapper(TenantBizRelationConvert.class);

    List<TenantBizRelationDO> convertVo2Do(List<TenantBizRelationSaveReqVO> certificates);

    List<TenantBizRelationRespVO> convertDo2Vo(List<TenantBizRelationDO> certificates);

    List<TenantBizRelationSaveReqVO> convertDo2SaveVo(List<TenantBizRelationDO> certificates);

    default TenantBizRelationSaveReqVO convert2Vo(TenantDO tenantDO, BizTypeEnum bizType) {
        return TenantBizRelationSaveReqVO.builder()
                .tenantId(tenantDO.getId())
                .bizType(bizType.getCode())
                .tenantId(tenantDO.getId())
                .headTenantId(tenantDO.getHeadTenantId())
                .tenantType(tenantDO.getType()).build();
    }

}
