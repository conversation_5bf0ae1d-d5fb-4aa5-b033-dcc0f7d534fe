package cn.iocoder.yudao.module.system.controller.app.user.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 用户人脸信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class UserFaceRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21292")
    @ExcelProperty("ID")
    private Long id;

    @Schema(description = "用户账号", requiredMode = Schema.RequiredMode.REQUIRED, example = "25295")
    @ExcelProperty("用户账号")
    private Long userId;

    @Schema(description = "三方人脸id", example = "24607")
    @ExcelProperty("三方人脸id")
    private String faceId;

    @Schema(description = "人脸图片")
    @ExcelProperty("人脸图片")
    private String faceImage;

    @Schema(description = "图片类型（0-BASE64 1-URL）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("图片类型（0-BASE64 1-URL）")
    private Integer faceType;

    @Schema(description = "帐号状态（0正常 1停用）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("帐号状态（0正常 1停用）")
    private Integer status;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}