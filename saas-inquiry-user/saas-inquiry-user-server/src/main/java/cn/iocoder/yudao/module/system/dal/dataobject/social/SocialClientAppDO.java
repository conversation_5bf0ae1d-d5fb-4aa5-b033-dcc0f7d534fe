package cn.iocoder.yudao.module.system.dal.dataobject.social;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import cn.iocoder.yudao.module.system.enums.social.AppTypeEnum;
import cn.iocoder.yudao.module.system.enums.social.SocialTypeEnum;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 社交客户端应用配置 DO
 *
 * <AUTHOR>
 */
@TableName(value = "system_social_client_app", autoResultMap = true)
@KeySequence("system_social_client_app_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class SocialClientAppDO extends TenantBaseDO {

    /**
     * 编号
     */
    @TableId
    private Long id;
    
    /**
     * 应用名
     */
    private String name;
    
    /**
     * 社交平台的类型
     * 
     * 枚举 {@link SocialTypeEnum}
     */
    private Integer socialType;
    
    /**
     * 应用类型
     * 
     * 枚举 {@link AppTypeEnum}
     */
    private Integer appType;
    
    /**
     * 用户类型
     * 
     * 枚举 {@link cn.iocoder.yudao.framework.common.enums.UserTypeEnum}
     */
    private Integer userType;
    
    /**
     * 客户端编号
     */
    private String clientId;
    
    /**
     * 客户端密钥
     */
    private String clientSecret;
    
    /**
     * 代理编号
     */
    private String agentId;
    
    /**
     * 状态
     * 
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;
    
    /**
     * 手机号授权开关
     * 
     * true-启用，false-禁用
     */
    private Boolean enableWxMobileAuth;

}