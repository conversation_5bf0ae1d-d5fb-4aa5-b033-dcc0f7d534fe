package cn.iocoder.yudao.module.system.dal.mysql.oa;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.controller.admin.oa.vo.OaWhiteListPageReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.oa.OaWhiteListDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * OA用户白名单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface OaWhiteListMapper extends BaseMapperX<OaWhiteListDO> {

    default PageResult<OaWhiteListDO> selectPage(OaWhiteListPageReqVO reqVO) {
        LambdaQueryWrapperX<OaWhiteListDO> queryWrapper = new LambdaQueryWrapperX<OaWhiteListDO>()
            .likeIfPresent(OaWhiteListDO::getUsername, reqVO.getUsername())
            .likeIfPresent(OaWhiteListDO::getFlowerName, reqVO.getFlowerName())
            .eqIfPresent(OaWhiteListDO::getRemark, reqVO.getRemark())
            .betweenIfPresent(OaWhiteListDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(OaWhiteListDO::getId);
        if (CollUtil.isNotEmpty(reqVO.getBizTypes())) {
            queryWrapper.and(orWrapper -> {
                reqVO.getBizTypes().forEach(i -> {
                    orWrapper.like(OaWhiteListDO::getBizTypes, i.toString());
                });
            });
        }
        return selectPage(reqVO, queryWrapper);
    }


    default OaWhiteListDO selectByName(String userName) {
        return selectOne(OaWhiteListDO::getUsername, userName);
    }
}