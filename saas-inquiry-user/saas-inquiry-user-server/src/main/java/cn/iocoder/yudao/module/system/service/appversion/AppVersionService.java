package cn.iocoder.yudao.module.system.service.appversion;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.controller.admin.appversion.vo.AppVersionPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.appversion.vo.AppVersionRespVO;
import cn.iocoder.yudao.module.system.controller.admin.appversion.vo.AppVersionSaveReqVO;
import cn.iocoder.yudao.module.system.controller.app.appversion.vo.CheckAppVersionReqVO;
import cn.iocoder.yudao.module.system.controller.app.appversion.vo.IgnoreUpgradeReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.appversion.AppVersionDO;
import jakarta.validation.Valid;


/**
 * App版本 Service 接口
 *
 * <AUTHOR>
 */
public interface AppVersionService {

    /**
     * 创建App版本
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createAppVersion(@Valid AppVersionSaveReqVO createReqVO);

    /**
     * 更新App版本
     *
     * @param updateReqVO 更新信息
     */
    void updateAppVersion(@Valid AppVersionSaveReqVO updateReqVO);

    /**
     * 删除App版本
     *
     * @param id 编号
     */
    void deleteAppVersion(Integer id);

    /**
     * 获得App版本
     *
     * @param id 编号
     * @return App版本
     */
    AppVersionRespVO getAppVersion(Integer id);

    /**
     * 获得App版本分页
     *
     * @param pageReqVO 分页查询
     * @return App版本分页
     */
    PageResult<AppVersionDO> getAppVersionPage(AppVersionPageReqVO pageReqVO);


    /**
     * 检查App版本升级
     * @param reqVO 入参
     * @return App版本
     */
    AppVersionRespVO checkAppVersionUpgrade(CheckAppVersionReqVO reqVO);

    /**
     * 忽略App版本升级
     * @param reqVO 入参
     * @return App版本
     */
    Boolean ignoreUpgrade(IgnoreUpgradeReqVO reqVO);
}