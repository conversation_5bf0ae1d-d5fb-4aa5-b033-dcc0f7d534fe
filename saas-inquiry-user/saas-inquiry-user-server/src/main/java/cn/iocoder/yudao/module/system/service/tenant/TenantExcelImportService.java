package cn.iocoder.yudao.module.system.service.tenant;

import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_BIZ_RELATION_EXISTS;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_TENANT_PACKAGE_RELATION;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_TENANT_PACKAGE_RELATION_CREATE_SUB_TYPE;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_TENANT_PACKAGE_RELATION_CREATE_SUCCESS;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_TENANT_PACKAGE_RELATION_UPDATE_STATUS_SUCCESS;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_TENANT_PACKAGE_RELATION_UPDATE_SUB_TYPE;
import static cn.iocoder.yudao.module.system.enums.LogRecordConstants.SYSTEM_TENANT_PACKAGE_RELATION_UPDATE_SUCCESS;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageReqDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.biz.TenantBizRelationSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.certificate.TenantCertificateRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.excel.TenantAndPackageExcelVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.excel.TenantPackageRelationOpenExcelVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.excel.TenantPackageRelationUpdateExcelVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationSaveReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantSaveReqVO;
import cn.iocoder.yudao.module.system.convert.tenant.TenantBizRelationConvert;
import cn.iocoder.yudao.module.system.convert.tenant.TenantConvert;
import cn.iocoder.yudao.module.system.convert.tenant.TenantPackageRelationConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantBizRelationDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageDO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageRelationDO;
import cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantBizRelationMapper;
import cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantMapper;
import cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantPackageMapper;
import cn.iocoder.yudao.module.system.dal.mysql.tenant.TenantPackageRelationMapper;
import com.mzt.logapi.context.LogRecordContext;
import com.mzt.logapi.service.impl.DiffParseFunction;
import com.mzt.logapi.starter.annotation.LogRecord;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageEffectiveStatusEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum;
import com.xyy.saas.inquiry.pojo.excel.ImportReqDto;
import com.xyy.saas.inquiry.pojo.excel.ImportResultDto;
import com.xyy.saas.inquiry.util.excel.EasyExcelUtil;
import jakarta.annotation.Resource;
import jakarta.validation.groups.Default;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author:chenxiaoyi
 * @Date:2025/04/28 10:26
 */
@Service
@Slf4j
public class TenantExcelImportService {

    @Autowired
    private EasyExcelUtil easyExcelUtil;

    @Resource
    private ConfigApi configApi;

    @Resource
    private TenantPackageRelationMapper tenantPackageRelationMapper;

    @Resource
    private TenantBizRelationMapper tenantBizRelationMapper;

    @Resource
    private TenantMapper tenantMapper;

    @Resource
    private TenantService tenantService;

    @Resource
    private TenantPackageMapper tenantPackageMapper;

    @Resource
    private TenantCertificateService tenantCertificateService;

    @Autowired
    private TenantPackageRelationServiceImpl tenantPackageRelationService;

    public TenantExcelImportService getSelf() {
        return SpringUtil.getBean(getClass());
    }


    private static final String BATCH_UPDATE_PACKAGE_RELATION_EXCEL_NAME = "批量修改订单信息";

    private static final String BATCH_OPEN_PACKAGE_RELATION_EXCEL_NAME = "批量续费套餐";

    private static final String BATCH_TENANT_OPEN_PACKAGE_RELATION_EXCEL_NAME = "批量开通编辑门店";

    private static final String TENANT_EXCEL_MAX_IMPORT_SIZE = "system.tenant.excel.max.import.size";

    private int getMaxImportSize() {
        return NumberUtil.parseInt(configApi.getConfigValueByKey(TENANT_EXCEL_MAX_IMPORT_SIZE), 500);
    }

    /**
     * 批量开通、编辑门店+开套餐
     *
     * @param importReqDto
     * @return
     */
    // @Transactional(rollbackFor = Exception.class)
    public ImportResultDto batchTenantAndPackageRelation(ImportReqDto importReqDto) {

        importReqDto.setExcelName(BATCH_TENANT_OPEN_PACKAGE_RELATION_EXCEL_NAME).setLimitCount(getMaxImportSize());

        return easyExcelUtil.importData(importReqDto, TenantAndPackageExcelVO.class, this::processBatchTenantPackageRelation, Default.class);
    }

    /**
     * 批量修改订单信息
     *
     * @param importReqDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ImportResultDto batchUpdatePackageRelation(ImportReqDto importReqDto) {

        importReqDto.setExcelName(BATCH_UPDATE_PACKAGE_RELATION_EXCEL_NAME).setLimitCount(getMaxImportSize());

        return easyExcelUtil.importData(importReqDto, TenantPackageRelationUpdateExcelVO.class, this::processBatchUpdatePackageRelation);
    }

    /**
     * 批量续费套餐
     *
     * @param importReqDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public ImportResultDto batchOpenPackageRelation(ImportReqDto importReqDto) {

        importReqDto.setExcelName(BATCH_OPEN_PACKAGE_RELATION_EXCEL_NAME).setLimitCount(getMaxImportSize());

        return easyExcelUtil.importData(importReqDto, TenantPackageRelationOpenExcelVO.class, this::processBatchOpenPackageRelation);
    }


    /**
     * 批量开通、编辑门
     */
    private void processBatchTenantPackageRelation(ImportReqDto reqDto, List<TenantAndPackageExcelVO> readyImportDataList) {
        // 1.校验唯一键重复
        EasyExcelUtil.validateDuplicates(readyImportDataList.stream().filter(r -> StringUtils.isNotBlank(r.getName())).toList(), TenantAndPackageExcelVO::getName, "门店名重复:");
        EasyExcelUtil.validateDuplicates(readyImportDataList.stream().filter(r -> StringUtils.isNotBlank(r.getBusinessLicenseName())).toList(), TenantAndPackageExcelVO::getBusinessLicenseName, "营业执照名称重复:");
        EasyExcelUtil.validateDuplicates(readyImportDataList.stream().filter(r -> StringUtils.isNotBlank(r.getBusinessLicenseNumber())).toList(), TenantAndPackageExcelVO::getBusinessLicenseNumber, "营业执照编号重复:");

        // 门店编码、套餐包编码是否存在
        List<String> tenantPref = readyImportDataList.stream().map(TenantAndPackageExcelVO::getPref).filter(StringUtils::isNotBlank).distinct().collect(Collectors.toList());
        List<String> headTenantPref = readyImportDataList.stream().map(TenantAndPackageExcelVO::getHeadTenantPref).filter(StringUtils::isNotBlank).distinct().toList();
        tenantPref.addAll(headTenantPref);
        Map<String, TenantDO> tenantDOMap = CollUtil.isEmpty(tenantPref) ? Map.of() : tenantMapper.getTenantByPrefs(tenantPref).stream().collect(Collectors.toMap(TenantDO::getPref, Function.identity(), (a, b) -> b));
        Map<Long, TenantBizRelationSaveReqVO> wzBizRelationMap = CollUtil.isEmpty(tenantPref) ? Map.of() : getWzBizRelationMap(tenantDOMap);

        Map<String, TenantPackageDO> packageDOMap = getPackageMap(readyImportDataList, TenantAndPackageExcelVO::getPackagePref);
        Map<Long, List<TenantCertificateRespVO>> certMap = getTenantCertificateMap(tenantDOMap.values().stream().map(TenantDO::getId).toList());

        // 校验名称和营业执照编号重复
        Map<String, TenantDO> tenantNameMap = getTenantMapByNames(readyImportDataList, TenantAndPackageExcelVO::getName, TenantDO::getName);
        Map<String, TenantDO> tenantBusinessLicenseNumberMap = getTenantMapByBusinessLicenseNumbers(readyImportDataList, TenantAndPackageExcelVO::getBusinessLicenseNumber, TenantDO::getBusinessLicenseNumber);
        Map<String, TenantDO> tenantBusinessLicenseNameMap = getTenantMapByBusinessLicenseNames(readyImportDataList, TenantAndPackageExcelVO::getBusinessLicenseName, TenantDO::getBusinessLicenseName);

        // 批量续费套餐
        List<TenantPackageRelationSaveReqVO> openList = new ArrayList<>();

        // 2.批量开门店
        for (TenantAndPackageExcelVO excelVO : readyImportDataList) {
            // 校验门店编码、套餐包编码
            validateTenantPackage(excelVO, tenantDOMap, tenantNameMap, tenantBusinessLicenseNameMap, tenantBusinessLicenseNumberMap, packageDOMap, wzBizRelationMap);

            if (StringUtils.isNotBlank(excelVO.getErrMsg())) {
                continue;
            }

            try {
                // 处理门店 + 套餐
                Long tenantId = StringUtils.isBlank(excelVO.getPref()) ? null : tenantDOMap.get(excelVO.getPref()).getId();

                TenantSaveReqVO tenantSaveReqVO = TenantConvert.INSTANCE.convertImportVo(excelVO
                    , tenantDOMap, tenantId == null ? null : certMap.get(tenantId), wzBizRelationMap);

                tenantId = tenantId == null
                    ? tenantService.createTenant(tenantSaveReqVO)
                    : tenantService.updateTenant(tenantSaveReqVO).getId();

                if (StringUtils.isNotBlank(excelVO.getPackagePref())) {
                    openList.add(TenantPackageRelationConvert.INSTANCE.convertImportVo(excelVO, tenantId, packageDOMap.get(excelVO.getPackagePref())));
                }
            } catch (Exception e) {
                log.error("{}失败,excelVO:{}", BATCH_TENANT_OPEN_PACKAGE_RELATION_EXCEL_NAME, excelVO, e);
                excelVO.setErrMsg(excelVO.getErrMsg() + e.getMessage());
            }
        }

        // 3.批量开套餐
        batchOpenPackages(packageDOMap, openList);
    }


    /**
     * 验证门店+套餐信息
     */
    private void validateTenantPackage(TenantAndPackageExcelVO excelVO,
        Map<String, TenantDO> tenantMap,
        Map<String, TenantDO> nameMap,
        Map<String, TenantDO> licenseNameMap,
        Map<String, TenantDO> licenseMap,
        Map<String, TenantPackageDO> packageMap,
        Map<Long, TenantBizRelationSaveReqVO> wzBizRelationMap) {

        // 创建场景验证
        if (StringUtils.isBlank(excelVO.getPref())) {
            if (nameMap.containsKey(excelVO.getName())) {
                excelVO.setErrMsg(excelVO.getErrMsg() + "门店名重复,");
            }
            if (licenseMap.containsKey(excelVO.getBusinessLicenseNumber())) {
                excelVO.setErrMsg(excelVO.getErrMsg() + "营业执照编号重复,");
            }
            if (licenseNameMap.containsKey(excelVO.getBusinessLicenseName())) {
                excelVO.setErrMsg(excelVO.getErrMsg() + "营业执照名称重复,");
            }
        }
        if (StringUtils.isNotBlank(excelVO.getPref())) {
            // 更新场景验证
            if (!tenantMap.containsKey(excelVO.getPref())) {
                excelVO.setErrMsg(excelVO.getErrMsg() + "门店编码不存在,");
            }

            TenantDO existingTenant = tenantMap.get(excelVO.getPref());

            // 验证名称冲突
            if (StringUtils.isNotBlank(excelVO.getName())
                && nameMap.containsKey(excelVO.getName())
                && !existingTenant.getPref().equals(nameMap.get(excelVO.getName()).getPref())) {
                excelVO.setErrMsg(excelVO.getErrMsg() + "门店名已被其他门店使用,");
            }

            // 验证营业执照冲突
            if (StringUtils.isNotBlank(excelVO.getBusinessLicenseName())
                && licenseNameMap.containsKey(excelVO.getBusinessLicenseName())
                && !existingTenant.getPref().equals(licenseNameMap.get(excelVO.getBusinessLicenseName()).getPref())) {
                excelVO.setErrMsg(excelVO.getErrMsg() + "营业执照名称已被其他门店使用,");
            }

            // 验证营业执照冲突
            if (StringUtils.isNotBlank(excelVO.getBusinessLicenseNumber())
                && licenseMap.containsKey(excelVO.getBusinessLicenseNumber())
                && !existingTenant.getPref().equals(licenseMap.get(excelVO.getBusinessLicenseNumber()).getPref())) {
                excelVO.setErrMsg(excelVO.getErrMsg() + "营业执照编号已被其他门店使用,");
            }
        }

        if (StringUtils.isNotBlank(excelVO.getHeadTenantPref())) {
            if(!tenantMap.containsKey(excelVO.getHeadTenantPref())) {
                excelVO.setErrMsg(excelVO.getErrMsg() + "连锁总部门店编码不存在,");
            } else {
                TenantBizRelationSaveReqVO tenantBizRelationSaveReqVO = wzBizRelationMap.get(tenantMap.get(excelVO.getHeadTenantPref()).getId());
                if(tenantBizRelationSaveReqVO == null || tenantBizRelationSaveReqVO.getTenantType() != TenantTypeEnum.CHAIN_HEADQUARTERS.getCode()) {
                    excelVO.setErrMsg(excelVO.getErrMsg() + "当前仅支持关联荷叶问诊业务线连锁总部,");
                }
            }
        }

        // 验证套餐包冲突
        if (StringUtils.isNotBlank(excelVO.getPackagePref())) {
            if(!packageMap.containsKey(excelVO.getPackagePref())) {
                excelVO.setErrMsg(excelVO.getErrMsg() + "套餐包编码不存在或套餐已停用,");
            } else {
                if(!packageMap.get(excelVO.getPackagePref()).getBizType().equals(BizTypeEnum.HYWZ.getCode())) {
                    excelVO.setErrMsg(excelVO.getErrMsg() + "当前仅支持导入荷叶套餐,");
                }
                Long tenantId = StringUtils.isBlank(excelVO.getPref()) ? null : tenantMap.get(excelVO.getPref()).getId();
                if (StringUtils.isBlank(excelVO.getBizType())) {
                    if (tenantId == null || !wzBizRelationMap.containsKey(tenantId)) {
                        excelVO.setErrMsg(excelVO.getErrMsg() + "该门店未开通荷叶问诊业务线,");
                    }
                }
            }
        }
    }


    /**
     * 批量续费套餐
     */
    private void processBatchOpenPackageRelation(ImportReqDto reqDto, List<TenantPackageRelationOpenExcelVO> readyImportDataList) {
        // 门店编码、套餐包编码是否存在
        Map<String, TenantDO> tenantDOMap = getTenantMapByPrefs(readyImportDataList, TenantPackageRelationOpenExcelVO::getTenantPref, TenantDO::getPref);
        Map<String, TenantPackageDO> packageDOMap = getPackageMap(readyImportDataList, TenantPackageRelationOpenExcelVO::getPackagePref);

        List<TenantPackageRelationSaveReqVO> openList = new ArrayList<>();

        for (TenantPackageRelationOpenExcelVO excelVO : readyImportDataList) {
            TenantDO tenantDO = tenantDOMap.get(excelVO.getTenantPref());
            if (tenantDO == null) {
                excelVO.setErrMsg("门店编码不存在");
                continue;
            }
            if(!CommonStatusEnum.isEnable(tenantDO.getWzBizTypeStatus())) {
                excelVO.setErrMsg("当前机构未开通【荷叶问诊】业务线");
                continue;
            }
            TenantPackageDO packageDO = packageDOMap.get(excelVO.getPackagePref());
            if (packageDO == null) {
                excelVO.setErrMsg("套餐包编码不存在或套餐已停用");
                continue;
            }
            if (packageDO.getBizType() != BizTypeEnum.HYWZ.getCode()) {
                excelVO.setErrMsg("当前不支持批量开通智慧脸套餐");
                continue;
            }
            TenantPackageRelationSaveReqVO saveReqVO = TenantPackageRelationConvert.INSTANCE.convertImportVo(excelVO, tenantDO, packageDO);
            openList.add(saveReqVO);
        }
        // 执行批量开通
        batchOpenPackages(packageDOMap, openList);
    }

    // 批量开通套餐
    private void batchOpenPackages(Map<String, TenantPackageDO> packageDOMap, List<TenantPackageRelationSaveReqVO> openList) {
        if (CollUtil.isNotEmpty(openList)) {
            List<TenantPackageRelationDO> list = openList.stream().map(t -> {
                TenantPackageRelationDO tenantPackageRelation = TenantPackageRelationConvert.INSTANCE.tenantPackageRelationInitReqVO2DO(t);
                TenantPackageRelationConvert.INSTANCE.fillPackageRelationInfo(packageDOMap.get(t.getPackagePref()), tenantPackageRelation);
                getSelf().createTenantPackageRelationLog(t);
                return tenantPackageRelation;
            }).toList();

            tenantPackageRelationMapper.insertBatch(list);
            // 发送mq
            list.forEach(t -> tenantPackageRelationService.recordPackageCostMq(t));
        }
    }

    /**
     * 处理批量变更套餐
     */
    private void processBatchUpdatePackageRelation(ImportReqDto reqDto, List<TenantPackageRelationUpdateExcelVO> readyImportDataList) {
        // 校验唯一键重复
        EasyExcelUtil.validateDuplicates(readyImportDataList, TenantPackageRelationUpdateExcelVO::getPref, "订单编号重复:");

        // 获取数据库订单
        Map<String, TenantPackageRelationDO> relationDOMap = tenantPackageRelationMapper.getListByPrefs(CollectionUtils.convertList(readyImportDataList, TenantPackageRelationUpdateExcelVO::getPref)).stream()
            .collect(Collectors.toMap(TenantPackageRelationDO::getPref, Function.identity(), (a, b) -> b));

        List<TenantPackageRelationSaveReqVO> updateList = new ArrayList<>();
        List<TenantPackageRelationDO> updateStatusList = new ArrayList<>();

        for (TenantPackageRelationUpdateExcelVO excelVO : readyImportDataList) {
            TenantPackageRelationDO oldRelationDO = relationDOMap.get(excelVO.getPref());
            if (!validateRecord(excelVO, oldRelationDO)) {
                continue;
            }
            updateList.add(TenantPackageRelationConvert.INSTANCE.convertImportVo(excelVO, oldRelationDO));
            if (excelVO.getStatus() != null && !Objects.equals(excelVO.status(), oldRelationDO.getStatus())) {
                updateStatusList.add(new TenantPackageRelationDO().setId(oldRelationDO.getId()).setPref(oldRelationDO.getPref()).setTenantId(oldRelationDO.getTenantId()).setStatus(excelVO.status()));
            }
        }
        // 执行批量更新
        if (CollUtil.isNotEmpty(updateList)) {
            List<TenantPackageRelationDO> doList = updateList.stream().map(TenantPackageRelationConvert.INSTANCE::tenantPackageRelationUpdateReqVO2DO).toList();
            tenantPackageRelationMapper.updateBatch(doList);
            updateList.forEach(vo -> {
                TenantPackageRelationDO oldRelationDO = relationDOMap.get(vo.getPref());
                vo.setTenantId(oldRelationDO.getTenantId());
                vo.setStartTime(oldRelationDO.getStartTime());
                vo.setInquiryPackageItems(oldRelationDO.getInquiryPackageItems());
                getSelf().updateTenantPackageRelationLog(vo, oldRelationDO);
                // 发送mq
                tenantPackageRelationService.recordPackageCostMq(TenantPackageRelationConvert.INSTANCE.tenantPackageRelationUpdateReqVO2DO(vo));
            });
        }
        // 处理状态变更
        updateStatusList.forEach(relationDO -> {
            getSelf().updateTenantPackageRelationStatusLog(relationDO, relationDOMap.get(relationDO.getPref()));
            // 发送mq
            tenantPackageRelationService.recordPackageCostStatusMq(relationDO);
        });
    }

    /**
     * 验证单条记录
     */
    private boolean validateRecord(TenantPackageRelationUpdateExcelVO excelVO,
        TenantPackageRelationDO oldRelationDO) {
        if (oldRelationDO == null) {
            excelVO.setErrMsg("订单编号不存在");
            return false;
        }
        if (excelVO.getEndTime() != null && excelVO.localEndTime().isBefore(oldRelationDO.getStartTime())) {
            excelVO.setErrMsg("服务失效时间早于服务生效时间");
            return false;
        }
        if (TenantPackageRelationStatusEnum.finalStatus(oldRelationDO.getStatus())) {
            if (BeanUtil.beanToMap(excelVO).entrySet().stream()
                .filter(e -> !"pref".equals(e.getKey()))
                .anyMatch(e -> ObjectUtil.isNotEmpty(e.getValue()))) {
                excelVO.setErrMsg("订单状态为最终状态，不可再修改订单信息");
                return false;
            }
        }
        if(oldRelationDO.getBizType() == BizTypeEnum.ZHL.getCode()) {
            TenantPackageReqDto reqDto = TenantPackageReqDto.builder().tenantId(oldRelationDO.getTenantId()).bizType(BizTypeEnum.ZHL.getCode())
                .statusList(Arrays.asList(TenantPackageRelationStatusEnum.NORMAL.getCode(), TenantPackageRelationStatusEnum.STOP.getCode())).build();
            List<TenantPackageRelationDO> tenantPackageRelationList = tenantPackageRelationMapper.getTenantPackageRelationList(reqDto);
            if(!CollUtil.isEmpty(tenantPackageRelationList)) {
                for(TenantPackageRelationDO c : tenantPackageRelationList) {
                    Integer status = TenantPackageEffectiveStatusEnum.getEffectiveStatus(BizTypeEnum.ZHL.getCode(), c.getStartTime(), c.getEndTime(),
                        TenantPackageRelationStatusEnum.fromStatusCode(c.getStatus()), c.getInquiryPackageItems());
                    if(status == TenantPackageEffectiveStatusEnum.EFFECT.getCode() || status == TenantPackageEffectiveStatusEnum.UN_EFFECT.getCode()) {
                        if(oldRelationDO.getId() != null && !oldRelationDO.getId().equals(c.getId())) {
                            excelVO.setErrMsg(TENANT_BIZ_RELATION_EXISTS.getMsg());
                            return false;
                        }
                    }
                }
            }
        }
        return true;
    }

    /**
     * 获取门店Map
     */
    public <T, K> Map<String, TenantDO> getTenantMapByPrefs(List<T> data, Function<T, K> keyMapper, Function<TenantDO, String> mapKey) {
        List<String> ks = data.stream().map(keyMapper).filter(Objects::nonNull).map(Objects::toString).distinct().toList();
        if (CollUtil.isEmpty(ks)) {
            return Map.of();
        }
        return tenantMapper.getTenantByPrefs(ks).stream().collect(Collectors.toMap(mapKey, Function.identity(), (a, b) -> b));
    }

    public <T, K> Map<String, TenantDO> getTenantMapByNames(List<T> data, Function<T, K> keyMapper, Function<TenantDO, String> mapKey) {
        List<String> ks = data.stream().map(keyMapper).filter(Objects::nonNull).map(Objects::toString).distinct().toList();
        if (CollUtil.isEmpty(ks)) {
            return Map.of();
        }
        return tenantMapper.getTenantByNames(ks).stream().collect(Collectors.toMap(mapKey, Function.identity(), (a, b) -> b));
    }

    public <T, K> Map<String, TenantDO> getTenantMapByBusinessLicenseNumbers(List<T> data, Function<T, K> keyMapper, Function<TenantDO, String> mapKey) {
        List<String> ks = data.stream().map(keyMapper).filter(Objects::nonNull).map(Objects::toString).distinct().toList();
        if (CollUtil.isEmpty(ks)) {
            return Map.of();
        }
        return tenantMapper.getTenantBusinessLicenseNumbers(ks).stream().collect(Collectors.toMap(mapKey, Function.identity(), (a, b) -> b));
    }

    public <T, K> Map<String, TenantDO> getTenantMapByBusinessLicenseNames(List<T> data, Function<T, K> keyMapper, Function<TenantDO, String> mapKey) {
        List<String> ks = data.stream().map(keyMapper).filter(Objects::nonNull).map(Objects::toString).distinct().toList();
        if (CollUtil.isEmpty(ks)) {
            return Map.of();
        }
        return tenantMapper.getTenantBusinessLicenseNames(ks).stream().collect(Collectors.toMap(mapKey, Function.identity(), (a, b) -> b));
    }

    /**
     * 获取套餐Map
     */
    public <T, K> Map<String, TenantPackageDO> getPackageMap(List<T> data, Function<T, K> keyMapper) {
        List<String> ks = data.stream().map(keyMapper).filter(Objects::nonNull).map(Objects::toString).distinct().toList();
        if (CollUtil.isEmpty(ks)) {
            return Map.of();
        }
        return tenantPackageMapper.selectListByPrefs(ks, CommonStatusEnum.ENABLE.getStatus()).stream().collect(Collectors.toMap(TenantPackageDO::getPref, Function.identity(), (a, b) -> b));
    }

    /**
     * 获取门店资质信息
     */
    public Map<Long, List<TenantCertificateRespVO>> getTenantCertificateMap(List<Long> tenantIds) {
        List<TenantCertificateRespVO> certificateDOS = tenantCertificateService.getTenantCertificates(tenantIds);
        return certificateDOS.stream().collect(Collectors.groupingBy(TenantCertificateRespVO::getTenantId));
    }


    //  -------------------- 记录操作日志上下文 -----------------
    @LogRecord(type = SYSTEM_TENANT_PACKAGE_RELATION, subType = SYSTEM_TENANT_PACKAGE_RELATION_UPDATE_SUB_TYPE, bizNo = "{{#updateReqVO.tenantId}}",
        success = SYSTEM_TENANT_PACKAGE_RELATION_UPDATE_SUCCESS)
    public void updateTenantPackageRelationLog(TenantPackageRelationSaveReqVO updateReqVO, TenantPackageRelationDO oldRelationDO) {
        LogRecordContext.putVariable(DiffParseFunction.OLD_OBJECT, BeanUtils.toBean(oldRelationDO, TenantPackageRelationSaveReqVO.class));
    }

    @LogRecord(type = SYSTEM_TENANT_PACKAGE_RELATION, subType = SYSTEM_TENANT_PACKAGE_RELATION_UPDATE_SUB_TYPE, bizNo = "{{#_oldObj.tenantId}}",
        success = SYSTEM_TENANT_PACKAGE_RELATION_UPDATE_STATUS_SUCCESS)
    public void updateTenantPackageRelationStatusLog(TenantPackageRelationDO newRelationDO, TenantPackageRelationDO oldRelationDO) {

        LogRecordContext.putVariable("_oldObj", oldRelationDO);
        LogRecordContext.putVariable("status", TenantPackageRelationStatusEnum.fromStatusCode(newRelationDO.getStatus()).getDesc());
        LogRecordContext.putVariable("oriStatus", TenantPackageRelationStatusEnum.fromStatusCode(oldRelationDO.getStatus()).getDesc());
    }


    @LogRecord(type = SYSTEM_TENANT_PACKAGE_RELATION, subType = SYSTEM_TENANT_PACKAGE_RELATION_CREATE_SUB_TYPE, bizNo = "{{#createReqVO.tenantId}}",
        success = SYSTEM_TENANT_PACKAGE_RELATION_CREATE_SUCCESS)
    public void createTenantPackageRelationLog(TenantPackageRelationSaveReqVO createReqVO) {
        LogRecordContext.putVariable("tenantPackageRelation", createReqVO);
    }

    private Map<Long, TenantBizRelationSaveReqVO> getWzBizRelationMap(Map<String, TenantDO> tenantDOMap) {
        List<Long> tenantIdList = tenantDOMap.entrySet().stream().map(entry -> entry.getValue().getId()).collect(Collectors.toList());
        List<TenantBizRelationDO> bizRelationList = tenantBizRelationMapper.getBizRelationList(BizTypeEnum.HYWZ.getCode(), tenantIdList);
        List<TenantBizRelationSaveReqVO> list = TenantBizRelationConvert.INSTANCE.convertDo2SaveVo(bizRelationList);
        return list.stream().collect(Collectors.toMap(TenantBizRelationSaveReqVO::getTenantId, Function.identity(), (a, b) -> b));
    }

}
