package cn.iocoder.yudao.module.system.dal.dataobject.tenant;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.type.IntegerListTypeHandler;
import cn.iocoder.yudao.framework.mybatis.core.type.StringListTypeHandler;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.enums.inquiry.InquiryAuditTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.DateTermTypeEnum;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem;
import java.math.BigDecimal;
import java.util.List;
import java.util.Set;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 门店套餐 DO
 *
 * <AUTHOR>
 */
@TableName(value = "system_tenant_package", autoResultMap = true)
@KeySequence("system_tenant_package_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class TenantPackageDO extends BaseDO {

    /**
     * 套餐编号，自增
     */
    private Long id;
    /**
     * 编码
     */
    private String pref;
    /**
     * 套餐名，唯一
     */
    private String name;
    /**
     * 系统业务类型 {@link BizTypeEnum}
     */
    private Integer bizType;
    /**
     * 租户类型（1-单店 2连锁门店 3连锁总部）{@link TenantTypeEnum}
     */
    private Integer packageType;
    /**
     * 门店套餐状态 枚举 {@link CommonStatusEnum}
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 关联的菜单编号
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private Set<Long> menuIds;
    /**
     * 总部菜单id
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private Set<Long> headMenuIds;
    /**
     * 门店菜单id
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private Set<Long> storeMenuIds;

    /**
     * 问诊医院pref
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> hospitalPrefs;

    /**
     * 问诊业务类型 {@link com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum}
     */
    private Integer inquiryBizType;

    /**
     * 问诊审方类型 {@link InquiryAuditTypeEnum}
     */
    private Integer inquiryAuditType;

    /**
     * 套餐定价
     */
    private BigDecimal price;

    /**
     * 套餐时限
     */
    private Integer term;

    /**
     * 时限类型 {@link DateTermTypeEnum}
     */
    private Integer termType;

    /**
     * 问诊方式类型 {@link InquiryWayTypeEnum}
     */
    @TableField(typeHandler = IntegerListTypeHandler.class)
    private List<Integer> inquiryWayTypes;

    /**
     * 问诊处方类型 {@link com.xyy.saas.inquiry.enums.prescription.PrescriptionTypeEnum}
     */
    @TableField(typeHandler = IntegerListTypeHandler.class)
    private List<Integer> prescriptionTypes;

    /**
     * 问诊包信息
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private List<InquiryPackageItem> inquiryPackageItems;
    /**
     * 套餐排序
     */
    private Integer sorted;
    /**
     * 套餐可见地区编码数组,对应区域编码
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> regionArr;

    /**
     * 套餐底部文案
     */
    private String bottomTxt;

    /**
     * 套餐推荐文案
     */
    private String recommendTxt;
}
