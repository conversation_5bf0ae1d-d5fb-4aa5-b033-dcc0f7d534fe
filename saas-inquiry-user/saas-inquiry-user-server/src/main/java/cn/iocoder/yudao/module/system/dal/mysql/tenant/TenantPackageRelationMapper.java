package cn.iocoder.yudao.module.system.dal.mysql.tenant;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageChainStoreReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageReqDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationRespVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantPackageRelationDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.apache.ibatis.annotations.Mapper;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;

/**
 * 门店套餐订单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantPackageRelationMapper extends BaseMapperX<TenantPackageRelationDO> {

    default List<TenantPackageRelationDO> getListByTenantId(Long tenantId) {
        return selectList(new LambdaQueryWrapperX<TenantPackageRelationDO>()
            .eq(TenantPackageRelationDO::getTenantId, Optional.ofNullable(tenantId).orElse(TenantContextHolder.getTenantId())));
    }

    default List<TenantPackageRelationDO> getListByPrefs(List<String> prefs) {
        return selectList(new LambdaQueryWrapperX<TenantPackageRelationDO>().in(TenantPackageRelationDO::getPref, prefs));
    }


    default List<TenantPackageRelationDO> getTenantPackageRelationList(TenantPackageReqDto reqDto) {
        final LambdaQueryWrapperX<TenantPackageRelationDO> queryWrapper = new LambdaQueryWrapperX<TenantPackageRelationDO>()
            .inIfPresent(TenantPackageRelationDO::getId, reqDto.getTenantPackageIds())
            .eq(TenantPackageRelationDO::getTenantId, Optional.ofNullable(reqDto.getTenantId()).orElse(TenantContextHolder.getRequiredTenantId()))
            .eqIfPresent(TenantPackageRelationDO::getBizType, reqDto.getBizType())
            .eqIfPresent(TenantPackageRelationDO::getStatus, reqDto.getStatus())
            .inIfPresent(TenantPackageRelationDO::getStatus, reqDto.getStatusList())
            .inIfPresent(TenantPackageRelationDO::getPackageId, reqDto.getPackageIds());

//        if(Objects.equals(reqDto.getEffective(), TenantPackageEffectiveStatusEnum.EFFECT.getCode())){
//            queryWrapper.le(TenantPackageRelationDO::getStartTime, LocalDateTime.now()).ge(TenantPackageRelationDO::getEndTime,LocalDateTime.now()).eq(TenantPackageRelationDO::getStatus, TenantPackageRelationStatusEnum.NORMAL.getCode());
//        }
//        //  AND ( ( a.start_time <![CDATA[>=]]> now() and  a.status = 0 )  or
//        //                      (a.start_time <![CDATA[<=]]>  now() AND a.end_time <![CDATA[>=]]> now() and a.status = 1 ) )
//        if(Objects.equals(reqDto.getEffective(), TenantPackageEffectiveStatusEnum.UN_EFFECT.getCode())){
//            queryWrapper.and(orWrapper -> {
//                orWrapper.or(i ->i.ge(TenantPackageRelationDO::getStartTime, LocalDateTime.now()).eq(TenantPackageRelationDO::getStatus, TenantPackageRelationStatusEnum.NORMAL.getCode()))
//                .or(aw -> aw.le(TenantPackageRelationDO::getStartTime, LocalDateTime.now()).ge(TenantPackageRelationDO::getEndTime,LocalDateTime.now()).eq(TenantPackageRelationDO::getStatus, TenantPackageRelationStatusEnum.STOP.getCode()));
//            });
//        }
//        // AND  (a.status in (2,3) or  a.end_time <![CDATA[<]]> now())
//        if(Objects.equals(reqDto.getEffective(), TenantPackageEffectiveStatusEnum.INVALID.getCode())){
//            queryWrapper.and(orWrapper -> {
//                orWrapper.or(i ->i.in(TenantPackageRelationDO::getStatus, TenantPackageRelationStatusEnum.REFUND.getCode(),TenantPackageRelationStatusEnum.ABANDONED.getCode()))
//                .or(aw -> aw.le(TenantPackageRelationDO::getEndTime,LocalDateTime.now()));
//            });
//        }
        return selectList(queryWrapper);
    }

    default List<TenantPackageRelationDO> getChainStoreTenantPackageRelationList(TenantPackageChainStoreReqDto reqDto) {
        final LambdaQueryWrapperX<TenantPackageRelationDO> queryWrapper = new LambdaQueryWrapperX<TenantPackageRelationDO>()
            .eqIfPresent(TenantPackageRelationDO::getBizType, reqDto.getBizType())
            .eqIfPresent(TenantPackageRelationDO::getStatus, reqDto.getStatus())
            .inIfPresent(TenantPackageRelationDO::getStatus, reqDto.getStatusList())
            .inIfPresent(TenantPackageRelationDO::getTenantId, reqDto.getHeadTenantId() != null ? Arrays.asList(reqDto.getTenantId(), reqDto.getHeadTenantId()) : Arrays.asList(reqDto.getTenantId()));
        return selectList(queryWrapper);
    }


    IPage<TenantPackageRelationDO> getTenantPackageRelationPage(Page<TenantPackageRelationRespVO> objectPage, TenantPackageRelationPageReqVO pageReqVO);

}