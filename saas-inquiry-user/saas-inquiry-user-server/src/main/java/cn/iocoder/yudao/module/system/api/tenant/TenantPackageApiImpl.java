package cn.iocoder.yudao.module.system.api.tenant;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageRelationPageReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageRelationRespDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageReqDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.packages.relation.TenantPackageRelationRespVO;
import cn.iocoder.yudao.module.system.convert.tenant.TenantPackageRelationConvert;
import cn.iocoder.yudao.module.system.service.tenant.TenantPackageRelationService;
import cn.iocoder.yudao.module.system.service.tenant.TenantPackageShareRelationService;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * @Author:chenxiaoyi
 * @Date:2024/10/25 10:18
 */

@Service
public class TenantPackageApiImpl implements TenantPackageApi {

    @Resource
    private TenantPackageRelationService tenantPackageRelationService;

    @Resource
    private TenantPackageShareRelationService tenantPackageShareRelationService;


    @Override
    public List<TenantPackageRelationRespDto> selectTenantPackages(TenantPackageReqDto reqDto) {
        // 查询门店套餐开通包
        List<TenantPackageRelationRespVO> tenantPackageRelationList = tenantPackageRelationService.getTenantPackageRelationList(reqDto);
        if (CollUtil.isEmpty(tenantPackageRelationList)) {
            return new ArrayList<>();
        }
        return TenantPackageRelationConvert.INSTANCE.convertVo2Dto(tenantPackageRelationList);
    }

    @Override
    public List<TenantPackageRelationRespDto> selectTenantSharePackages(TenantPackageReqDto reqDto) {
        // 查询门店被分享的总部套餐包
        List<TenantPackageRelationRespVO> tenantPackageRelationList = tenantPackageShareRelationService.getTenantSharePackageRelationList(reqDto);
        if (CollUtil.isEmpty(tenantPackageRelationList)) {
            return new ArrayList<>();
        }
        return TenantPackageRelationConvert.INSTANCE.convertVo2Dto(tenantPackageRelationList);
    }

    @Override
    public PageResult<TenantPackageRelationRespDto> pageTenantPackageRelation(TenantPackageRelationPageReqDto pageReqDto) {

        PageResult<TenantPackageRelationRespVO> packageRelationPage = tenantPackageRelationService.getTenantPackageRelationPage(TenantPackageRelationConvert.INSTANCE.convertDto2Vo(pageReqDto));

        return new PageResult<>(TenantPackageRelationConvert.INSTANCE.convertVo2Dto(packageRelationPage.getList()), packageRelationPage.getTotal());
    }
}
