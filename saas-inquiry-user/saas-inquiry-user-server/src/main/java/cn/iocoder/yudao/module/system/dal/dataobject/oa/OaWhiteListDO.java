package cn.iocoder.yudao.module.system.dal.dataobject.oa;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.type.IntegerListTypeHandler;
import cn.iocoder.yudao.framework.mybatis.core.type.LongListTypeHandler;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.util.List;

/**
 * OA用户白名单 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_oa_white_list", autoResultMap = true)
@KeySequence("saas_oa_white_list_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OaWhiteListDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 业务类型 0-问诊,1-saas...
     */
    @TableField(typeHandler = LongListTypeHandler.class)
    private List<Integer> bizTypes;
    /**
     * 用户名
     */
    private String username;
    /**
     * 花名
     */
    private String flowerName;
    /**
     * 备注
     */
    private String remark;

}