package cn.iocoder.yudao.module.system.api.user;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.enums.UserTypeEnum;
import cn.iocoder.yudao.framework.security.config.SecurityProperties;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.member.api.user.MemberUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.system.convert.user.UserConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2AccessTokenDO;
import cn.iocoder.yudao.module.system.service.oauth2.OAuth2TokenService;
import cn.iocoder.yudao.module.system.service.user.UserCompatService;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;


/**
 * @Author: xucao
 * @DateTime: 2025/5/8 13:30
 * @Description: 用户api  兼容社交用户登录
 **/
@Service
public class UserCompatApiImpl implements UserCompatApi {

    @Resource
    private UserCompatService userCompatService;

    /**
     * 获取登录用户的信息
     *
     * @return 用户对象信息
     */
    @Override
    public AdminUserRespDTO getUser(String token) {
        return userCompatService.getUser(token);
    }

}
