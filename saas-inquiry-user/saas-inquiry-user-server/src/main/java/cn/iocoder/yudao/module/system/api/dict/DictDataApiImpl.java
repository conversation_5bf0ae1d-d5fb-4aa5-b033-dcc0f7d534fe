package cn.iocoder.yudao.module.system.api.dict;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.system.api.dict.dto.DictDataPageReqDTO;
import cn.iocoder.yudao.module.system.api.dict.dto.DictDataRespDTO;
import cn.iocoder.yudao.module.system.api.dict.dto.DictDataSaveBatchDto;
import cn.iocoder.yudao.module.system.controller.admin.dict.vo.data.DictDataSaveBatchReqVO;
import cn.iocoder.yudao.module.system.controller.app.dict.vo.AppDictDataRespVO;
import cn.iocoder.yudao.module.system.convert.dict.DictConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.dict.DictDataDO;
import cn.iocoder.yudao.module.system.service.dict.DictDataService;
import com.google.common.collect.Maps;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 字典数据 API 实现类
 *
 * <AUTHOR>
 */
@Service
public class DictDataApiImpl implements DictDataApi {

    @Resource
    private DictDataService dictDataService;

    @Override
    public void saveOrUpdateDictData(DictDataSaveBatchDto dto) {
        dictDataService.insertOrUpdateBatch(BeanUtils.toBean(dto, DictDataSaveBatchReqVO.class));
    }

    @Override
    public void validateDictDataList(Long tenantId, String dictType, Collection<String> values) {
        dictDataService.validateDictDataList(tenantId, dictType, values);
    }

    @Override
    public DictDataRespDTO getDictData(Long tenantId, String dictType, String value) {
        DictDataDO dictData = dictDataService.getDictData(tenantId, dictType, value);
        return BeanUtils.toBean(dictData, DictDataRespDTO.class);
    }

    @Override
    public DictDataRespDTO parseDictData(Long tenantId, String dictType, String label) {
        DictDataDO dictData = dictDataService.parseDictData(tenantId, dictType, label);
        return BeanUtils.toBean(dictData, DictDataRespDTO.class);
    }

    @Override
    public List<DictDataRespDTO> getDictDataList(Long tenantId, String dictType) {
        List<DictDataDO> list = dictDataService.getDictDataListByDictType(tenantId, dictType);
        return BeanUtils.toBean(list, DictDataRespDTO.class);
    }

    @Override
    public PageResult<DictDataRespDTO> getDictDataPage(DictDataPageReqDTO pageReqDTO) {
        PageResult<DictDataDO> dictDataPage = dictDataService.getDictDataPage(DictConvert.INSTANCE.convertPageDto(pageReqDTO));
        return new PageResult(DictConvert.INSTANCE.convertDto(dictDataPage.getList()), dictDataPage.getTotal());
    }

    @Override
    public List<DictDataRespDTO> getDictDatas(Long tenantId, String dictType, String dictName) {
        List<DictDataDO> list = dictDataService.getDictDatas(tenantId, dictType, dictName);
        return DictConvert.INSTANCE.convertDto(list);
    }

    @Override
    public List<DictDataRespDTO> getDictDataList(Long tenantId, List<Long> ids) {
        List<DictDataDO> list = dictDataService.getDictDataList(tenantId, ids);
        return DictConvert.INSTANCE.convertDto(list);
    }

    @Override
    public Map<String, List<DictDataRespDTO>> getDictDataListByTypes(Long tenantId, List<String> dictTypeList) {
        Map<String, List<AppDictDataRespVO>> data = dictDataService.getDictDataLists(CommonStatusEnum.ENABLE.getStatus(), tenantId, dictTypeList);

        if (CollUtil.isEmpty(data)) {
            return Maps.newHashMap();
        }

        Map<String, List<DictDataRespDTO>> dictDataRespDTOMap = new HashMap<>();
        for (Map.Entry<String, List<AppDictDataRespVO>> entry : data.entrySet()) {
            dictDataRespDTOMap.put(entry.getKey(),  DictConvert.INSTANCE.convertVOList2DtoList(entry.getValue()));
        }

        return dictDataRespDTOMap;
    }
}
