package cn.iocoder.yudao.module.system.convert.tenant;

import cn.iocoder.yudao.module.system.dal.dataobject.sms.SmsTemplateDO;
import cn.iocoder.yudao.module.system.framework.sms.core.client.dto.SmsSendMessageDTO;
import cn.iocoder.yudao.module.system.framework.sms.core.client.dto.SmsTemplateRespDTO;
import cn.iocoder.yudao.module.system.mq.message.sms.SmsSendMessage;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * sms Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface SmsConvert {

    SmsConvert INSTANCE = Mappers.getMapper(SmsConvert.class);

    SmsTemplateRespDTO convertTemplate(SmsTemplateDO template);

    SmsSendMessageDTO convertMessage(SmsSendMessage message);
}
