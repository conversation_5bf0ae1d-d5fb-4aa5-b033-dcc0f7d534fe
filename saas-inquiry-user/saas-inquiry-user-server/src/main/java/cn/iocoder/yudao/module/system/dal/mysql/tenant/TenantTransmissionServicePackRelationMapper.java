package cn.iocoder.yudao.module.system.dal.mysql.tenant;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantServicePackRelationReqDto;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationPageReqVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.servicepack.TenantServicePackRelationRespVO;
import cn.iocoder.yudao.module.system.controller.admin.tenant.vo.tenant.TenantRespVO;
import cn.iocoder.yudao.module.system.dal.dataobject.tenant.TenantTransmissionServicePackRelationDO;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xyy.saas.inquiry.pojo.CommonGroupStatisticsDto;
import com.xyy.saas.inquiry.pojo.catalog.CatalogRelationTenantDto;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 门店-开通服务包关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantTransmissionServicePackRelationMapper extends BaseMapperX<TenantTransmissionServicePackRelationDO> {

    IPage<TenantRespVO> getTenantServicePackRelationPage(Page<TenantServicePackRelationRespVO> objectPage, TenantServicePackRelationPageReqVO reqVO);


    default List<TenantTransmissionServicePackRelationDO> selectByCondition(TenantServicePackRelationPageReqVO reqVO) {
        return selectList(getQueryWrapper(reqVO));
    }


    default TenantTransmissionServicePackRelationDO selectOneByCondition(TenantServicePackRelationPageReqVO reqVO) {
        LambdaQueryWrapperX<TenantTransmissionServicePackRelationDO> queryWrapper = getQueryWrapper(reqVO);
        return selectOne(queryWrapper, false);
    }

    private static LambdaQueryWrapperX<TenantTransmissionServicePackRelationDO> getQueryWrapper(TenantServicePackRelationPageReqVO reqVO) {
        return new LambdaQueryWrapperX<TenantTransmissionServicePackRelationDO>()
            .eqIfPresent(TenantTransmissionServicePackRelationDO::getTenantId, reqVO.getTenantId())
            .inIfPresent(TenantTransmissionServicePackRelationDO::getTenantId, reqVO.getTenantIds())
            .eqIfPresent(TenantTransmissionServicePackRelationDO::getServicePackId, reqVO.getServicePackId())
            .inIfPresent(TenantTransmissionServicePackRelationDO::getServicePackId, reqVO.getServicePackIds())
            .eqIfPresent(TenantTransmissionServicePackRelationDO::getOrganId, reqVO.getOrganId())
            .eqIfPresent(TenantTransmissionServicePackRelationDO::getOrganType, reqVO.getOrganType())
            .eqIfPresent(TenantTransmissionServicePackRelationDO::getServicePackVersion, reqVO.getServicePackVersion())
            .eqIfPresent(TenantTransmissionServicePackRelationDO::getStatus, reqVO.getStatus())
            .betweenIfPresent(TenantTransmissionServicePackRelationDO::getCreateTime, reqVO.getCreateTime());
    }


    /**
     * 统计开通的当前服务商包的 租户数
     *
     * @param organIds 三方机构（服务商）id列表
     * @param status   状态 {@link com.xyy.saas.inquiry.enums.tenant.TenantServicePackRelationStatusEnum}
     * @return
     */
    List<CommonGroupStatisticsDto> selectCountByOrgans(@Param("organIds") List<Integer> organIds, @Param("status") Integer status);

    /**
     * 统计开通的当前服务包的 租户数
     *
     * @param servicePackIds 服务包ID列表
     * @param status         {@link com.xyy.saas.inquiry.enums.tenant.TenantServicePackRelationStatusEnum}
     * @return
     */
    List<CommonGroupStatisticsDto> selectCountByServicePacks(@Param("servicePackIds") List<Integer> servicePackIds, @Param("status") Integer status);

    /**
     * 统计开通的目录版本的 租户数
     *
     * @param catalogIds 目录版本列表
     * @param status     {@link com.xyy.saas.inquiry.enums.tenant.TenantServicePackRelationStatusEnum}
     * @return
     */
    List<CommonGroupStatisticsDto> selectCountByCatalogIds(@Param("catalogIds") List<Long> catalogIds, @Param("status") Integer status);

    void deleteByTenantOrganType(@Param("tenantIds") List<Long> tenantIds, @Param("organType") Integer organType);

    void deleteIds(@Param("ids") List<Integer> ids);

    IPage<CatalogRelationTenantDto> getCatalogRelationTenantPage(Page<TenantServicePackRelationReqDto> objectPage, TenantServicePackRelationReqDto reqDto);

}