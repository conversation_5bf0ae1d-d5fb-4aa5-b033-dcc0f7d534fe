package cn.iocoder.yudao.module.infra.api.file;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.infra.api.file.dto.FileConfigDto;
import cn.iocoder.yudao.module.infra.api.file.dto.FileCreateReqDTO;
import cn.iocoder.yudao.module.infra.controller.admin.file.vo.file.FileCreateReqVO;
import cn.iocoder.yudao.module.infra.dal.mysql.file.FileConfigMapper;
import cn.iocoder.yudao.module.infra.service.file.FileConfigService;
import cn.iocoder.yudao.module.infra.service.file.FileService;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 文件 API 实现类
 *
 * <AUTHOR>
 */
@Service
@DubboService
@Validated
@Slf4j
public class FileApiImpl implements FileApi {

    @Resource
    private FileService fileService;
    @Resource
    private FileConfigMapper fileConfigMapper;



    @Override
    public String createFile(String name, String path, byte[] content,boolean... pub) {
        return fileService.createFile(name, path, content,pub);
    }


    /**
     * 创建文件记录
     *
     * @param createReqDTO
     * @return
     */
    @Override
    public Long createFileRecord(FileCreateReqDTO createReqDTO) {
        FileCreateReqVO reqVO = BeanUtils.toBean(createReqDTO, FileCreateReqVO.class);
        return fileService.createFile(reqVO);
    }

    @Override
    public String getPrivateFilePreSignedUrl(String url) {
        try {
            return fileService.getPrivateFilePreSignedUrl(url);
        } catch (Exception e) {
            log.error("获取文件预签名url失败,url:{},msg:{}",url,e.getMessage(),e);
        }
        return url;
    }

    @Override
    public Map<String, List<String>> batchGetPrivateFilePreSignedUrl(Map<String, List<String>> urlMap) {
        if(CollUtil.isEmpty(urlMap)){
            return Map.of();
        }
        urlMap.replaceAll((k, value) -> value.stream().map(v -> {
            try {
                return fileService.getPrivateFilePreSignedUrl(v);
            } catch (Exception e) {
                return v;
            }
        }).collect(Collectors.toList()));
        return urlMap;
    }







    /**
     * 获取文件配置
     *
     * @param id
     * @return
     */
    @Override
    public FileConfigDto getFileConfig(Long id) {
        return BeanUtils.toBean(fileConfigMapper.selectById(id), FileConfigDto.class);
    }

    /**
     * 获取主文件配置
     *
     * @return
     */
    @Override
    public FileConfigDto getMasterFileConfig() {
        return BeanUtils.toBean(fileConfigMapper.selectByMaster(), FileConfigDto.class);
    }
}
