package cn.iocoder.yudao.module.system.convert.appversion;

import cn.iocoder.yudao.module.system.controller.admin.appversion.vo.AppVersionRespVO;
import cn.iocoder.yudao.module.system.controller.admin.appversion.vo.AppVersionSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.appversion.AppVersionDO;
import com.xyy.saas.inquiry.pojo.TenantDto;
import java.util.List;
import java.util.Optional;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author: xucao
 * @Date: 2025/01/20 20:25
 * @Description: app版本升级相关转换器
 */
@Mapper
public interface AppVersionConvert {

    AppVersionConvert INSTANCE = Mappers.getMapper(AppVersionConvert.class);

    AppVersionRespVO convertDO2RespVO(AppVersionDO versionDO);

    AppVersionRespVO convert(AppVersionDO versionDO);

    default AppVersionRespVO convertWithGaryTenant(AppVersionDO versionDO, List<TenantDto> dtoList){
        AppVersionRespVO respVO =  convert(versionDO);
        respVO.setGrayTenant(dtoList);
        Optional.ofNullable(versionDO.getReleasedChannels()).ifPresent(list -> {
            respVO.setReleasedChannels(list.stream().map(Integer::valueOf).toList());
        });
        return respVO;
    }

    AppVersionDO convertVO2DO(AppVersionSaveReqVO respVO);
}
