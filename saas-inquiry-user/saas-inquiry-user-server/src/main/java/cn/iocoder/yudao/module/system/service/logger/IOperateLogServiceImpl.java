package cn.iocoder.yudao.module.system.service.logger;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.logger.dto.OperateLogCreateReqDTO;
import cn.iocoder.yudao.module.system.controller.admin.logger.vo.operatelog.OperateLogPageReqVO;
import cn.iocoder.yudao.module.system.convert.logger.LoggerConvert;
import cn.iocoder.yudao.module.system.dal.dataobject.logger.OperateLogDO;
import cn.iocoder.yudao.module.system.dal.mysql.logger.OperateLogMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import java.util.List;

/**
 * 操作日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class IOperateLogServiceImpl implements IOperateLogService {

    @Resource
    private OperateLogMapper operateLogMapper;

    @Override
    public void batchCreateOperateLog(List<OperateLogCreateReqDTO> createReqDTOs) {
        if (CollUtil.isEmpty(createReqDTOs)) {
            return;
        }
        operateLogMapper.insertBatch(LoggerConvert.INSTANCE.convert(createReqDTOs));
    }

    @Override
    public PageResult<OperateLogDO> getOperateLogPage(OperateLogPageReqVO pageReqVO) {
        return operateLogMapper.selectPage(pageReqVO);
    }

}
