package cn.iocoder.yudao.module.system.dal.dataobject.tenant;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.type.IntegerListTypeHandler;
import cn.iocoder.yudao.framework.mybatis.core.type.StringListTypeHandler;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.enums.inquiry.InquiryAuditTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.PackageNatureEnum;
import com.xyy.saas.inquiry.enums.tenant.PackagePaymentTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem;
import com.xyy.saas.inquiry.pojo.tenant.TenantPackageRelationExtDto;
import com.xyy.saas.inquiry.pojo.tenant.TenantPackageRelationStatusChangeDto;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 门店套餐订单 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_tenant_package_relation", autoResultMap = true)
@KeySequence("saas_tenant_package_relation_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class TenantPackageRelationDO extends BaseDO {

    /**
     * 门店套餐关系id
     */
    @TableId
    private Long id;
    /**
     * 编码
     */
    private String pref;
    /**
     * 门店id
     */
    private Long tenantId;
    /**
     * 系统业务类型 {@link BizTypeEnum}
     */
    private Integer bizType;
    /**
     * 租户类型（1-单店 2连锁门店 3连锁总部）{@link TenantTypeEnum}
     */
    private Integer packageType;
    /**
     * 门店套餐编号
     */
    private Long packageId;
    /**
     * 套餐名
     */
    private String packageName;

    /**
     * 问诊医院id
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> hospitalPrefs;

    /**
     * 问诊业务类型 {@link com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum}
     */
    private Integer inquiryBizType;

    /**
     * 问诊审方类型 {@link InquiryAuditTypeEnum}
     */
    private Integer inquiryAuditType;

    /**
     * 套餐定价
     */
    private BigDecimal price;

    /**
     * 问诊方式类型 {@link InquiryWayTypeEnum}
     */
    @TableField(typeHandler = IntegerListTypeHandler.class)
    private List<Integer> inquiryWayTypes;

    /**
     * 问诊处方类型 {@link com.xyy.saas.inquiry.enums.prescription.PrescriptionTypeEnum}
     */
    @TableField(typeHandler = IntegerListTypeHandler.class)
    private List<Integer> prescriptionTypes;

    /**
     * 问诊包信息
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private List<InquiryPackageItem> inquiryPackageItems;


    /**
     * {@link TenantPackageRelationStatusEnum}
     */
    private Integer status;
    /**
     * 套餐包性质 {@link PackageNatureEnum}
     */
    private Integer packageNature;
    /**
     * 开始时间
     */
    private LocalDateTime startTime;
    /**
     * 结束时间
     */
    private LocalDateTime endTime;
    /**
     * 收款方式：0线上，1线下 {@link PackagePaymentTypeEnum}
     */
    private Integer paymentType;
    /**
     * 签约日期
     */
    private LocalDateTime signTime;
    /**
     * 签约人
     */
    private String signUser;

    /**
     * 签约人工号 - 数据库已加
     */
    private String signUserNo;
    /**
     * 代理人/中间人
     */
    private String proxyUser;
    /**
     * 签约渠道(eg:3智鹿)
     */
    private Integer signChannel;

    /**
     * 签约(支付)来源：0运营线下提单 1App,2-PC,3-小程序   - 数据库已加
     */
    private Integer signSource;

    /**
     * 实收金额
     */
    private BigDecimal actualAmount;
    /**
     * 收款账户 eg:(微信对公-成都)
     */
    private Integer collectAccount;
    /**
     * 付款流水号
     */
    private String payNo;
    /**
     * 付款凭证url
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> payVoucherUrls;


    /**
     * 备注
     */
    private String remark;

    /**
     * 失效信息
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private TenantPackageRelationStatusChangeDto statusChangeInfo;

    /**
     * 扩展信息
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private TenantPackageRelationExtDto ext;


    @JsonIgnore
    public TenantPackageRelationExtDto extGet() {
        if (ext == null) {
            ext = new TenantPackageRelationExtDto();
        }
        return ext;
    }

    /**
     * 切套餐包冗余旧id
     */
    @TableField(exist = false)
    private Long oldId;

}