package cn.iocoder.yudao.module.system.convert.premission;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.module.system.api.permission.dto.RoleRespDTO;
import cn.iocoder.yudao.module.system.controller.admin.permission.vo.role.RoleSaveReqVO;
import cn.iocoder.yudao.module.system.dal.dataobject.permission.RoleDO;
import cn.iocoder.yudao.module.system.enums.permission.DataScopeEnum;
import cn.iocoder.yudao.module.system.enums.permission.RoleTypeEnum;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.util.PrefUtil;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 门店证件 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface RoleConvert {

    RoleConvert INSTANCE = Mappers.getMapper(RoleConvert.class);

    List<RoleDO> convert(List<RoleSaveReqVO> createReqVOs);

    default List<RoleSaveReqVO> convertRoleEnum(List<RoleCodeEnum> roleCodeEnums) {
        if (CollUtil.isEmpty(roleCodeEnums)) {
            return null;
        }
        return roleCodeEnums.stream().map(r -> new RoleSaveReqVO().setCode(r.getCode()).setName(r.getName())).collect(Collectors.toList());
    }


    List<RoleRespDTO> convertUseRole(List<RoleDO> roleDOS);

    default RoleDO convertCreate(RoleSaveReqVO createReqVO, Integer type) {
        return new RoleDO().setName(createReqVO.getName())
            .setCode(StringUtils.defaultIfBlank(createReqVO.getCode(), PrefUtil.getRolePref()))
            .setSort(createReqVO.getSort())
            .setRemark(createReqVO.getRemark())
            .setStatus(createReqVO.getStatus())
            .setDataScope(DataScopeEnum.ALL.getScope())
            .setType(ObjectUtil.defaultIfNull(type, RoleTypeEnum.CUSTOM.getType()));
    }
}
