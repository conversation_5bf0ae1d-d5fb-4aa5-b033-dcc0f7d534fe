package cn.iocoder.yudao.module.system.service.tenant.ybm.dto;

import lombok.Data;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;

/**
 * @Description: 商户基本信息
 * @Auther: WanKp
 * @Date: 2018/8/18 11:49
 **/
@Data
public class YbmMerchantBusinessDto implements Serializable {

    /** 主键 */
    private Long id;

    /** 同步编号 */
    private String syncNo;

    /** 商户真实名称 */
    private String realName= "";

    /** 用于精确查询 */
    private String findRealName;

    /** 手机号 */
    private String mobile= "";

    /** 密码 */
    private String password;

    /** 邮箱地址 */
    private String email = "";

    /** 昵称 */
    private String nickname= "";

    /** 状态 */
    private Integer status;

    /** 商户类型：1-普通账户，2-测试账户 */
    private Integer type;

    /** 创建时间 */
    private Date createTime;

    /** 更新时间 */
    private Date updateTime;

    /** 邀请码 */
    private String authcode= "";

    /** 商户资质状态 */
    private Integer licenseStatus;
    /** 商户资质状态文本 */
    private String licenseText;

    /** 时空表中的资质状态 */
    private Integer skLicenseStatus;

    /** 系统负责人编号 */
    private Long sysUserId;
    /** 系统负责人姓名 */
    private String sysUserName= "";
    /** 工号 */
    private String jobNumber= "";

    /** 是否可以点击邀请码 */
    private Integer checkcode;

    /** 新老客户标识 1：新客户，2：老客户 */
    private Integer merchantType;

    /** 注册来源 2-ios，1-android，3-H5，4-pc */
    private Integer registerSource;

    /** 激活来源 2-ios，1-android，3-H5，4-pc */
    private Integer	activeSource;

    /** 激活时间 */
    private Date activeTime;

    /** 商户类型（业务类型）：1-个体药店，2-连锁药店（加盟），3-连锁药店（直营），4-诊所 */
    private Integer businessType;

    /** 商户类型名称 */
    private String businessTypeName;

    /** 省 */
    private String province;

    /** 市 */
    private String city;

    /** 区 */
    private String district;

    /** 合成地址 */
    private String address;

    /** 注册地址编码 */
    private String registerCode;
    /** 注册地址名称：页面显示上显示的域名称 */
    private String registerBranch;

    /** 默认联系人 */
    private String defaultContactor;

    /** 默认地址 */
    private String defaultAddress;

    /** 商户id数组 */
    private Long[] ids;

    /** 商户状态名称（暂时是导出功能使用） */
    private String statusName = "";

    /** 最后登录日期 */
    private Date lastLoginDate;

    /** 开始时间 */
    private Date createStartTime;
    /** 结束时间 */
    private Date createEndTime;
    /** 业务最后登录时间 */
    private Long bizLastLoginTime;

    @SuppressWarnings("unused")
    /** 当前对象标识 */
    private String identity;

    @SuppressWarnings("unused")
    /** 登录名 */
    private String	loginName;

    /** 药店名称或者电话号码 */
    private String searchConditon;

    /** 系统真实姓名 */
    private String sysRealName;
    /** 系统工号 */
    private String sysJobNumber;

    /** 针对后台操作人员的多域查询：不能去掉 */
    private String registerCodes;

    /** 是否新用户id：不要去掉 */
    private Long merchantId;

    /** 发票邮寄地址 */
    private String invoiceMailAddress;

    /** 联系方式备用 */
    private String mobileBackUp;

    /** 是否认领 1.是 0 否 */
    private Integer isClaim;

    /** 省份编码 */
    private String provinceCode;

    /** 市编码 */
    private String cityCode;

    /**  区编码 */
    private String areaCode;

    /**
     * 发票类型0：普通 ；  1：增值税
     */
    private Integer invoiceType;

    /**税号*/
    private String taxNum;

    /** 客服电话 */
    private String kfPhone;

    /**  街道编码 */
    private String streetCode;

    private String street;

    /**
     * 登录次数
     */
    private Integer loginCount;

    /**
     * 店铺审核状态，1-审核中，2-审核通过，3-审核不通过
     */
    private Integer auditStatus;
    /***********************扩展字段**********************/
    private Long[] groupIdArray;			//所在分组数组
    private String keyword;					//关键字查询
    //    private List<Branch> branchList;		//区域列表
    private String syncAddress;
    private BigDecimal lon;					//经度
    private BigDecimal lat;					//纬度
    private Integer[] statuses;				//多状态数组
    private Integer registerStatus;         //激活状态

    /** 多用户类型数组 */
    private Integer[] businessTypes;

    //用户资质id
    private Integer findClientId;
    //资质状态
    private String findClientText;
    //注册开始时间
    private Date startCreateTime;
    //注册结束时间
    private Date endCreateTime;
    /** 等级名称 */
    private Integer currentLevel;
    /** 等级名称 */
    private String currentLevelName;
    /** 是否标记黑名单 */
    private Integer isBlackRemark;
    /** 黑名单标记名称 */
    private String isBlackRemarkName;

    //会员渠道关系
    private List<String> channelList;

    private List<String> channelNameList;

    private String channelNames;

    /** 价格是否展示*/
    private boolean priceDisplayFlag;

    private Double fob;

    /** 是否是KA用户 true表示是KA用户 false表示不是KA用户**/
    private boolean isKa=false;

    /** 是否在融合灰度区域 true表示在 false表示不在**/
    private boolean isGrayBranch=false;

    /** 流水号 **/
    private String uuid;

    /** app设备id **/
    private String imei;

    /**用户状态操作原因*/
    private String applReason;

    /**用户状态操作时间*/
    private String auditTime;

    /** 冻结原因 **/
    private String freezeReasonCode;

    /** 新用户类型**/
    private Integer customerType;

    /**
     * 冻结日志 id
     */
    private Long freezeLogId;

    /**
     * 营业执照编码或医疗机构执业许可证编码
     * @return
     */
    private String code;

    private String operator;
    //账号ID
    private Long accountId;

    /**
     * 账号角色，1店长，2店员
     */
    private Integer accountRole;

    /**
     * 营业执照编码或医疗机构执业许可证编码
     */
    private String licenseNo;
    /**
     * 营业执照电子版url/医疗机构执业许可证电子版url
     */
    private String licenseImageUrl;

    /**
     * 是否saas用户: 1是 2否
     */
    private Integer saasUser;

    /**
     * 是否走自动过审判断标志，第一版现在app走，pc不走
     */
    private Boolean autoProcessSwitch = false;

    /**
     * 注册时查询的天眼查的企业基础数据信息透传回来
     */
    private String tycBasicDataDto;

    /**
     * ocr识别结果
     */
    private String ocrParseDTO;

}
