package cn.iocoder.yudao.module.system.util;

import cn.hutool.core.util.IdUtil;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import lombok.extern.slf4j.Slf4j;
import org.jose4j.json.JsonUtil;
import org.jose4j.jwk.JsonWebKey;
import org.jose4j.jwk.RsaJsonWebKey;
import org.jose4j.jwk.RsaJwkGenerator;
import org.jose4j.jws.JsonWebSignature;
import org.jose4j.jwt.JwtClaims;
import org.jose4j.jwt.NumericDate;
import org.jose4j.jwt.consumer.ErrorCodes;
import org.jose4j.jwt.consumer.InvalidJwtException;
import org.jose4j.jwt.consumer.JwtConsumer;
import org.jose4j.jwt.consumer.JwtConsumerBuilder;
import org.jose4j.lang.JoseException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.security.PrivateKey;
import java.security.PublicKey;
import java.util.Optional;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
@Component
public class JwtUtil {

    public static final String ORIGIN_TOKEN_KEY = "token";
    public static final String THIRD_APP_ID = "thirdAppId";

    private static String KEY_ID;
    private static String ALG;
    private static String ISS;
    private static int ExpireSeconds;
    private static int NotBeforeMinutesInThePast;

    private static PrivateKey privateKey;
    private static PublicKey publicKey;

    private final Jose4jTokenProperties jose4jTokenProperties;

    @Autowired
    public JwtUtil(Jose4jTokenProperties jose4jTokenProperties) {
        Assert.notNull(jose4jTokenProperties, "jose4jTokenProperties must not be null");
        this.jose4jTokenProperties = jose4jTokenProperties;
        try {
            String keypair = this.jose4jTokenProperties.getKeypair();
            RsaJsonWebKey rsaJsonWebKey = new RsaJsonWebKey(JsonUtil.parseJson(keypair));
            // 初始化公私钥对
            JwtUtil.privateKey = rsaJsonWebKey.getPrivateKey();
            JwtUtil.publicKey = rsaJsonWebKey.getRsaPublicKey();
            JwtUtil.KEY_ID = rsaJsonWebKey.getKeyId();
            JwtUtil.ALG = rsaJsonWebKey.getAlgorithm();

            JwtUtil.ISS = this.jose4jTokenProperties.getIssuer();
            JwtUtil.ExpireSeconds = this.jose4jTokenProperties.getExpireSeconds();
            JwtUtil.NotBeforeMinutesInThePast = this.jose4jTokenProperties.getNotBeforeMinutesInThePast();
            log.info("================== JwtUtil init finished ==================");
        } catch (Throwable e) {
            log.error("================== JwtUtil init error: {} ==================", e.getMessage(), e);
        }
    }

    /**
     * 原token  短uuid
     *
     * @return
     */
    public static String originToken() {
        return IdUtil.fastSimpleUUID();
    }

    /**
     * 从jwt中获取原token
     *
     * @param jwt
     * @return
     */
    public static String originToken(String jwt) {
        // 兼容原token
        if (jwt == null || jwt.length() < 64) {
            return jwt;
        }
        // return (String) JWTUtil.parseToken(jwt).getPayload().getClaim(ORIGIN_TOKEN_KEY);
        JwtClaims jwtClaims = verify(jwt);
        if (jwtClaims == null || !jwtClaims.hasClaim(ORIGIN_TOKEN_KEY)) {
            return jwt;
        }
        return jwtClaims.getClaimValueAsString(ORIGIN_TOKEN_KEY);
    }


    /**
     * jwt
     *
     * @param sub
     * @param originToken
     * @return
     */
    public static String builder(String sub, String originToken, Integer tokenValiditySeconds) {
        // 第一步：载荷payload
        JwtClaims claims = new JwtClaims();
        // 注册的声明 1.iss: jwt签发者
        claims.setIssuer(ISS);
        // 注册的声明 2.sub: jwt所面向的用户
        claims.setSubject(sub);
        // 注册的声明 3.aud：接收jwt的一方
        // claims.setAudience("Audience");
        // 注册的声明 4.exp：jwt的过期时间，这个过期时间必须要大于签发时间
        // 【从现在开始10分钟】
        // claims.setExpirationTimeMinutesInTheFuture(10000);
        NumericDate date = NumericDate.now();
        date.addSeconds(Optional.ofNullable(tokenValiditySeconds).orElse(60 * 60 * 24 * 30));
        // exp：Expiration time，令牌的过期时间戳。超过此时间的 token 会作废， 该声明是一个整数，是 1970 年 1 月 1 日以来的秒数
        claims.setExpirationTime(date);
        // 注册的声明 5.定义在什么时间之前，该jwt都是不可用的【2分钟前】
        if (NotBeforeMinutesInThePast > 0) {
            claims.setNotBeforeMinutesInThePast(NotBeforeMinutesInThePast);
        }
        // 注册的声明 6.iat: jwt的签发时间
        claims.setIssuedAtToNow();
        // 注册的声明 7.jti: jwt的唯一身份标识，主要用来作为一次性token,从而回避重放攻击。
        claims.setGeneratedJwtId();
        // 公共的声明：可可以添加任何的信息，一般这里我们会存放一下用户的基本信息
        // claims.setClaim("userId", userId);
        // claims.setClaim("username", username);
        claims.setClaim(ORIGIN_TOKEN_KEY, originToken);
        claims.setClaim(THIRD_APP_ID, TenantContextHolder.getThirdAppId());

        // 第二步：签证signature：其值是对头部header和载荷payload进行base64UrlEncode后使用指定算法签名生成
        JsonWebSignature jws = new JsonWebSignature();
        // 1.放入头部
        jws.setKeyIdHeaderValue(KEY_ID);
        // 2.放入载荷
        jws.setPayload(claims.toJson());
        // 3.使用私钥签名
        jws.setKey(privateKey);
        // 4.设置签名算法
        jws.setAlgorithmHeaderValue(ALG);
        try {
            //第三步：算出签名以后，把 Header、Payload、Signature 三个部分拼成一个字符串，每个部分之间用"点"（.）分隔，就可以返回给用户
            return jws.getCompactSerialization();
        } catch (JoseException e) {
            log.error("JwtUtil.builder error: {}", e.getMessage(), e);
            return null;
        }
    }

    public static JwtClaims verify(String jwt) {
        //1.引入公钥，使用公钥对私钥的签名解密
        //2.使用JwtConsumer解密
        JwtConsumer jwtConsumer = new JwtConsumerBuilder().setRequireExpirationTime()
            // 允许在验证基于时间的令牌时留有一定的余地，以计算时钟偏差,单位/秒
            .setAllowedClockSkewInSeconds(30)
            // 主题声明
            .setRequireSubject()
            // 验证 jwt签发者
            .setExpectedIssuer(ISS)
            // 验证 接收jwt的一方
            // .setExpectedAudience("Audience")
            // 用公钥验证签名 ,验证私钥
            .setVerificationKey(publicKey)
            // 使用生成jwt的签名算法解密 （没有加密， 只有签名）
            .setJwsAlgorithmConstraints(
                org.jose4j.jwa.AlgorithmConstraints.ConstraintType.WHITELIST,
                ALG)
            .build();
        JwtClaims jwtClaims = null;
        try {
            // 验证JWT并将其处理为jwtClaims
            jwtClaims = jwtConsumer.processToClaims(jwt);
            //如果JWT失败的处理或验证，将会抛出InvalidJwtException，希望能有一些有意义的解释关于哪里出了问题
            if (log.isDebugEnabled()) {
                log.debug("JWT validation succeeded: {}", jwtClaims);
            }
        } catch (InvalidJwtException e) {
            log.error("Invalid JWT: {}", e.getMessage(), e);
            // 对JWT无效的（某些）特定原因的编程访问也是可能的
            // 在某些情况下，您是否需要不同的错误处理行为。
            // JWT是否已经过期是无效的一个常见原因
            if (e.hasExpired()) {
                // log.error("JWT expired at {}", e.getJwtContext().getJwtClaims().getExpirationTime());
            }
            // 或者观众是无效的
            if (e.hasErrorCode(ErrorCodes.AUDIENCE_INVALID)) {
                // log.error("JWT had wrong audience: {}", e.getJwtContext().getJwtClaims().getAudience());
            }
        }

        return jwtClaims;
    }

    public static void main(String[] args) throws JoseException {
        RsaJsonWebKey rsaJsonWebKey = RsaJwkGenerator.generateJwk(2048);
        rsaJsonWebKey.setAlgorithm(ALG);
        rsaJsonWebKey.setKeyId(KEY_ID);

        final String publicKeyString = rsaJsonWebKey.toJson(JsonWebKey.OutputControlLevel.PUBLIC_ONLY);
        final String privateKeyString = rsaJsonWebKey.toJson(JsonWebKey.OutputControlLevel.INCLUDE_PRIVATE);
        log.info("公钥: {}", publicKeyString);
        log.info("私钥: {}", privateKeyString);
    }
}
