package cn.iocoder.yudao.module.system.dal.mysql.oauth2;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.system.dal.dataobject.oauth2.OAuth2RefreshTokenDO;
import cn.iocoder.yudao.module.system.util.JwtUtil;
import java.time.LocalDateTime;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

@Mapper
public interface OAuth2RefreshTokenMapper extends BaseMapperX<OAuth2RefreshTokenDO> {

    default int deleteByRefreshToken(String refreshToken) {
        // refreshToken  jwt 转 原token
        return delete(new LambdaQueryWrapperX<OAuth2RefreshTokenDO>()
            .eq(OAuth2RefreshTokenDO::getRefreshToken, JwtUtil.originToken(refreshToken)));
    }

    default OAuth2RefreshTokenDO selectByRefreshToken(String refreshToken) {
        // refreshToken  jwt 转 原token
        return selectOne(OAuth2RefreshTokenDO::getRefreshToken, JwtUtil.originToken(refreshToken));
    }

    default List<OAuth2RefreshTokenDO> selectAvailableByUserId(Long userId) {
        return selectList(new LambdaQueryWrapperX<OAuth2RefreshTokenDO>()
            .eq(OAuth2RefreshTokenDO::getUserId, userId).gt(OAuth2RefreshTokenDO::getExpiresTime, LocalDateTime.now()));
    }

    default List<OAuth2RefreshTokenDO> selectAvailableByUserIdList(List<Long> userIdList) {
        return selectList(new LambdaQueryWrapperX<OAuth2RefreshTokenDO>()
            .in(OAuth2RefreshTokenDO::getUserId, userIdList).gt(OAuth2RefreshTokenDO::getExpiresTime, LocalDateTime.now()));
    }

    @TenantIgnore
    default List<OAuth2RefreshTokenDO> selectAvailableByUserIdTenants(Long userId, List<Long> tenantIds) {
        return selectList(new LambdaQueryWrapperX<OAuth2RefreshTokenDO>()
            .eq(OAuth2RefreshTokenDO::getUserId, userId)
            .in(OAuth2RefreshTokenDO::getTenantId, tenantIds)
            .gt(OAuth2RefreshTokenDO::getExpiresTime, LocalDateTime.now()));
    }
}
