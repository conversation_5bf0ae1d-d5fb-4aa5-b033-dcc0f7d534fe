package cn.iocoder.yudao.module.system.util;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.iocoder.yudao.module.system.api.user.dto.SSOLoginResult;
import cn.iocoder.yudao.module.system.api.user.dto.SSORespDto;
import cn.iocoder.yudao.module.system.api.user.dto.SSOUser;
import com.alibaba.fastjson.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StreamUtils;

import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * 对接公司OA - SSO Util
 *
 * <AUTHOR>
 */
public class SSOUtil {

    private static final Logger logger = LoggerFactory.getLogger(SSOUtil.class);

    /**
     * 根据用户名密码换取tgc 凭证
     *
     * @param username 用户名
     * @param password 密码
     * @param service  当前系统服务域名
     * @param ssoUrl   sso业务地址
     * @return 登录tgc
     */
    public static SSOLoginResult getTicket(String username, String password, String service, String ssoUrl) {
        String param = String.join("&", "username=" + username, "password=" + URLEncoder.encode(password, StandardCharsets.UTF_8), "service=" + ssoUrl);
        HttpURLConnection connection;
        OutputStream os;
        try {
            URL url = new URL(ssoUrl);
            connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setConnectTimeout(15000);
            connection.setReadTimeout(6000);
            connection.setDoOutput(true);
            connection.setDoInput(true);
            connection.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            os = connection.getOutputStream();
            os.write(param.getBytes());
            Map<String, List<String>> a = connection.getHeaderFields();
            int code = connection.getResponseCode();
            //认证成功！
            if (Objects.equals(code, 201)) {
                String tgt = connection.getHeaderFields().get("Location").get(0).substring(connection.getHeaderFields().get("Location").get(0).lastIndexOf("/") + 1);
                return SSOLoginResult.success(tgt);
            }
            //认证失败
            return SSOLoginResult.error((StreamUtils.copyToString(connection.getErrorStream(), StandardCharsets.UTF_8)).split(":")[1].replaceAll("\"", ""));
        } catch (Exception e) {
            logger.error("SSOUtil#getTGT login failed", e);
            return SSOLoginResult.error(e.getMessage());
        }
    }

    /**
     * 根据tgc查询用户基本信息
     *
     * @param account 账号
     * @param tgc     上一个接口换取的凭证
     * @param baseUrl 查询业务地址
     * @return 用户基本信息
     */
    public static SSOUser querySSOUser(String account, String tgc, String baseUrl) {
        String url = baseUrl + "?account=" + account;
        try {
            HttpResponse response = HttpUtil.createGet(url).header("TGC", tgc).execute();
            SSORespDto queryUserResponse = JSON.parseObject(response.body(), SSORespDto.class);
            logger.info("查询sso用户信息.path:{},tgc:{},resp:{}", url, tgc, JSON.toJSONString(queryUserResponse));
            if (!Objects.equals(queryUserResponse.getCode(), 200)) {
                return null;
            }
            return JSON.parseObject(JSON.toJSONString(queryUserResponse.getResult()), SSOUser.class);
        } catch (Exception e) {
            logger.error("查询中台用户信息异常", e);
            return null;
        }
    }

}
