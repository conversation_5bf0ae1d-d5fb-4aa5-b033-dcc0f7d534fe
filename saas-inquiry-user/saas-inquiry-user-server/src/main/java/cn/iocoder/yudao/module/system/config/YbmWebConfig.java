package cn.iocoder.yudao.module.system.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @ClassName：TencentConfig
 * @Author: xucao
 * @Date: 2024/11/27 13:49
 * @Description: 腾讯IM配置
 */
@Data
@ConfigurationProperties(prefix = "ybm.web")
public class YbmWebConfig {
    /**
     * ybm服务调用域名
     */
    private String domain;

    /**
     * API配置
     */
    private Api api;

    // Getter and Setter methods

    // Nested class for API configuration
    @Data
    public static class Api {
        /**
         * 获取店铺
         */
        private String getMerchant;

        /**
         * 获取账号
         */
        private String getAccount;

    }
}
