package cn.iocoder.yudao.module.system.framework.baidu.dto;

import java.io.Serializable;
import java.util.List;
import lombok.Data;

/**
 * @Author:chenxiaoyi
 * @Date:2025/08/06 17:10
 */
@Data
public class FaceResultMatchDto extends FaceResultDto {

    private FaceMatchList result;

    private String dec_image;

    private String risk_level;

    private String risk_tag;

    private String risk_warn_code;

    @Data
    public static class FaceMatchList implements Serializable {

        private double score;

        private List<FaceListDto> face_list;

    }
}
