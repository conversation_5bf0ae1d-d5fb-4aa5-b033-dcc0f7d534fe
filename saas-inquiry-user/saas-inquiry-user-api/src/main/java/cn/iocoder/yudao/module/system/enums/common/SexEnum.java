package cn.iocoder.yudao.module.system.enums.common;

import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.Arrays;
import java.util.Objects;

/**
 * 性别的枚举值
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum SexEnum {

    /**
     * 男
     */
    MALE(1, "男"),
    /**
     * 女
     */
    FEMALE(2, "女"),
    /* 未知 */
    UNKNOWN(0, "");

    /**
     * 性别
     */
    private final Integer sex;

    private final String desc;

    public static SexEnum forValue(Integer value) {
        return Arrays.stream(values()).filter(s -> Objects.equals(s.getSex(), value)).findFirst().orElse(UNKNOWN);
    }

    public static String getDesc(Integer value) {
        return forValue(value).getDesc();
    }

}
