package cn.iocoder.yudao.module.system.api.tenant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantServicePackRelationDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantServicePackRelationReqDto;
import com.xyy.saas.inquiry.enums.transmitter.OrganTypeEnum;
import com.xyy.saas.inquiry.pojo.catalog.CatalogRelationTenantDto;

import java.util.List;
import java.util.Map;

/**
 * 门店服务包关系api
 *
 * <AUTHOR>
 */
public interface TenantServicePackRelationApi {

    /**
     * 查询绑定机构的门店数
     *
     * @param organIds 机构列表
     * @param status   状态 {@link com.xyy.saas.inquiry.enums.tenant.TenantServicePackRelationStatusEnum}
     * @return 机构-门店数
     */
    Map<Integer, Long> selectCountByOrgans(List<Integer> organIds, Integer status);

    /**
     * 查询绑定机构的门店数
     *
     * @param servicePackIds 服务包列表
     * @param status         状态 {@link com.xyy.saas.inquiry.enums.tenant.TenantServicePackRelationStatusEnum}
     * @return 机构-门店数
     */
    Map<Integer, Long> selectCountByServicePacks(List<Integer> servicePackIds, Integer status);

    /**
     * 查询绑定目录版本的门店数
     *
     * @param catalogIds 服务包列表
     * @param status     状态 {@link com.xyy.saas.inquiry.enums.tenant.TenantServicePackRelationStatusEnum}
     * @return 机构-门店数
     */
    Map<Long, Long> selectCountByCatalogIds(List<Long> catalogIds, Integer status);

    /**
     * 根据条件查询服务包列表
     *
     * @param reqDto 入参条件
     * @return 服务包列表
     */
    List<TenantServicePackRelationDto> selectByCondition(TenantServicePackRelationReqDto reqDto);

    /**
     * 获取医疗类别在用得目录id
     *
     * @param organTypeEnum 机构医疗类型
     * @return
     */
    Long getTenantOrganCatalogId(OrganTypeEnum organTypeEnum);

    /**
     * 分页查询租户
     *
     * @param reqDto
     * @return
     */
    PageResult<CatalogRelationTenantDto> getCatalogRelationTenantPage(TenantServicePackRelationReqDto reqDto);


    /**
     * 根据租户id和服务包id查询租户服务包关系
     *
     * @param tenantId
     * @param servicePackId
     * @return
     */
    TenantServicePackRelationDto selectByTenantIdServicePackId(Long tenantId, Integer servicePackId);

    /**
     * 获取门店服务包网络
     *
     * @param tenantId
     * @param servicePackId
     * @return
     */
    default String selectNetworkCodeByTenantIdServicePackId(Long tenantId, Integer servicePackId) {
        if (tenantId == null || servicePackId == null) {
            return null;
        }
        TenantServicePackRelationDto relationDto = selectByTenantIdServicePackId(tenantId, servicePackId);
        if (relationDto != null) {
            return relationDto.extGet().getNetworkCode();
        }
        return null;
    }


}
