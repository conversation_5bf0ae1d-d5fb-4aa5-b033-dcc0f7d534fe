package cn.iocoder.yudao.module.system.api.tenant.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.util.List;

@Data
@Accessors(chain = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TenantPackageChainStoreReqDto implements Serializable {

    private Long tenantId;

    private Long headTenantId;

    @Schema(description = "业务线", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer bizType;

    private Integer tenantType;

    @Schema(description = "开通套餐状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    private List<Integer> statusList;

}
