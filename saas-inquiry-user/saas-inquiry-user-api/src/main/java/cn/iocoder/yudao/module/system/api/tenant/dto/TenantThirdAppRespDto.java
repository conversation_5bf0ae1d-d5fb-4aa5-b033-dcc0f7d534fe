package cn.iocoder.yudao.module.system.api.tenant.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import java.io.Serializable;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 门店三方应用配置 Response DTO")
@Data
@ToString(callSuper = true)
public class TenantThirdAppRespDto implements Serializable {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "租户编号", example = "1")
    private Long tenantId;

    @Schema(description = "应用名称", example = "ERP")
    private String appName;

    @Schema(description = "应用标识", example = "erp_key")
    private String appKey;

    @Schema(description = "应用密钥", example = "erp_key")
    private String appSecret;

    @Schema(description = "ERP对接机构id", example = "2048")
    private Integer transmissionOrganId;

    @Schema(description = "状态", example = "1")
    private Integer status;

    @Schema(description = "备注")
    private String remark;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;

    @Schema(description = "更新时间")
    private LocalDateTime updateTime;

}
