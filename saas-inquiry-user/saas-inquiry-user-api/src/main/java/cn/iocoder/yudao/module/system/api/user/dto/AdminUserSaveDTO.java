package cn.iocoder.yudao.module.system.api.user.dto;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;
import java.io.Serializable;
import lombok.Data;

/**
 * Admin 用户 Response DTO
 *
 * <AUTHOR>
 */
@Data
public class AdminUserSaveDTO implements Serializable {

    /**
     * 用户ID
     */
    private Long id;

    //    @Pattern(regexp = "^[a-zA-Z0-9]{4,30}$", message = "用户账号由 数字、字母 组成")
    // @Size(min = 4, max = 30, message = "用户账号长度为 4-30 个字符")
    private String username;

    /**
     * 密码
     */
    private String password;

    /**
     * 姓名
     */
    private String nickname;
    /**
     * 帐号状态
     * <p>
     * 枚举 {@link CommonStatusEnum}
     */
    private Integer status;

    @NotBlank(message = "手机号不能为空")
    @Size(max = 11, message = "手机号长度为 11 个字符")
    private String mobile;
    /**
     * 用户头像
     */
    private String avatar;

    /**
     * 用户性别，参见 SexEnum 枚举类
     */
    private Integer sex;

    /**
     * 身份证号
     */
    private String idCard;

    /**
     * 角色code 存在则分配角色
     */
    private String roleCode;

    /**
     * 租户编号
     */
    private Long tenantId;

    /**
     * 是否绑定当前门店关系 默认是 超管创建门店药师时 不绑定
     */
    private boolean bindTenant = true;

    /**
     * 检查员工绑定关系，药师创建除外-
     */
    private boolean checkRelation = true;

    /**
     * 是否需要打卡 (0是 1否) {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer needClockIn;

    /**
     * 是否签到
     */
    private Integer clockInStatus;

}
