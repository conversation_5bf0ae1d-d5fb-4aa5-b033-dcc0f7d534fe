package cn.iocoder.yudao.module.system.enums.logger;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 登录日志的类型枚举
 */
@Getter
@AllArgsConstructor
public enum LoginLogTypeEnum {

    LOGIN_USERNAME(100), // 使用账号登录
    LOGIN_SOCIAL(101), // 使用社交登录
    LOGIN_MOBILE(103), // 使用手机登陆
    LOGIN_SMS(104), // 使用短信登陆
    LOGIN_OA(105), // 使用OA账号登陆
    LOGIN_MINI_PROGRAM(108), // 使用小程序授权登陆
    LOGIN_API(109), // 使用三方应用授权密钥登陆

    LOGOUT_SELF(200),  // 自己主动登出
    LOGOUT_DELETE(202), // 强制退出
    LOGOUT_OFF(203),  // 注销登录退出
    LOGOUT_KICK(204), // 同账号登陆踢下线
    ;

    /**
     * 日志类型
     */
    private final Integer type;

}
