package cn.iocoder.yudao.module.system.api.tenant.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TenantUserRelationBindDto implements Serializable {


    @Schema(description = "处理的门店列表", example = "111")
    private List<Long> tenantIds;


    @Schema(description = "用户id", example = "111")
    @NotNull
    private Long userId;

    /**
     * {@link com.xyy.saas.inquiry.enums.system.RoleCodeEnum}
     */
    @Schema(description = "赋予的角色code", example = "wz_admin")
    private String roleCode;

    /**
     * 是否需要打卡 (0是 1否) {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "是否需要打卡 (0是 1否)")
    private Integer needClockIn;

}
