package cn.iocoder.yudao.module.system.api.tenant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageRelationPageReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageRelationRespDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageReqDto;

import java.util.List;

/**
 * 门店套餐Api
 *
 * <AUTHOR>
 */
public interface TenantPackageApi {

    /**
     * 获取当前门店的套餐包列表
     *
     * @param reqDto dto
     * @return 门店套餐列表
     */
    List<TenantPackageRelationRespDto> selectTenantPackages(TenantPackageReqDto reqDto);

    /**
     * 获取连锁门店 被总部分享的套餐
     *
     * @param reqDto
     * @return
     */
    List<TenantPackageRelationRespDto> selectTenantSharePackages(TenantPackageReqDto reqDto);


    /**
     * 获得门店套餐包信息分页
     *
     * @param pageReqDto 分页查询
     * @return 门店套餐包信息分页
     */
    PageResult<TenantPackageRelationRespDto> pageTenantPackageRelation(TenantPackageRelationPageReqDto pageReqDto);

}
