package cn.iocoder.yudao.module.system.api.dict;

import static cn.iocoder.yudao.framework.common.util.collection.CollectionUtils.convertList;

import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.dict.dto.DictDataPageReqDTO;
import cn.iocoder.yudao.module.system.api.dict.dto.DictDataRespDTO;
import cn.iocoder.yudao.module.system.api.dict.dto.DictDataSaveBatchDto;
import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * 字典数据 API 接口
 *
 * <AUTHOR>
 */
public interface DictDataApi {

    /**
     * 批量保存或更新字典数据
     * @param dto
     */
    void saveOrUpdateDictData(DictDataSaveBatchDto dto);

    /**
     * 校验字典数据们是否有效。如下情况，视为无效： 1. 字典数据不存在 2. 字典数据被禁用
     *
     * @param dictType 字典类型
     * @param values   字典数据值的数组
     */
    void validateDictDataList(Long tenantId, String dictType, Collection<String> values);

    /**
     * 获得指定的字典数据，从缓存中
     *
     * @param type  字典类型
     * @param value 字典数据值
     * @return 字典数据
     */
    DictDataRespDTO getDictData(Long tenantId, String type, String value);

    default DictDataRespDTO getDictData(String type, String value) {
        return getDictData(null, type, value);
    }


    /**
     * 获得指定的字典标签，从缓存中
     *
     * @param type  字典类型
     * @param value 字典数据值
     * @return 字典标签
     */
    default String getDictDataLabel(Long tenantId, String type, Integer value) {
        DictDataRespDTO dictData = getDictData(tenantId, type, String.valueOf(value));
        if (ObjUtil.isNull(dictData)) {
            return StrUtil.EMPTY;
        }
        return dictData.getLabel();
    }

    default String getDictDataLabel(String type, Integer value) {
        return getDictDataLabel(null, type, value);
    }

    /**
     * 解析获得指定的字典数据，从缓存中
     *
     * @param type  字典类型
     * @param label 字典数据标签
     * @return 字典数据
     */
    DictDataRespDTO parseDictData(Long tenantId, String type, String label);

    default DictDataRespDTO parseDictData(String type, String label) {
        return parseDictData(null, type, label);
    }

    /**
     * 获得指定字典类型的字典数据列表
     *
     * @param dictType 字典类型
     * @return 字典数据列表
     */
    List<DictDataRespDTO> getDictDataList(Long tenantId, String dictType);

    default List<DictDataRespDTO> getDictDataList(String dictType) {
        return getDictDataList(null, dictType);
    }

    /**
     * 获得字典数据标签列表
     *
     * @param dictType 字典类型
     * @return 字典数据标签列表
     */
    default List<String> getDictDataLabelList(Long tenantId, String dictType) {
        List<DictDataRespDTO> list = getDictDataList(tenantId, dictType);
        return convertList(list, DictDataRespDTO::getLabel);
    }

    default List<String> getDictDataLabelList(String dictType) {
        List<DictDataRespDTO> list = getDictDataList(null, dictType);
        return convertList(list, DictDataRespDTO::getLabel);
    }

    /**
     * 分页查询字典数据
     *
     * @param pageReqDTO
     * @return
     */
    PageResult<DictDataRespDTO> getDictDataPage(DictDataPageReqDTO pageReqDTO);

    /**
     * 模糊查询字典数据
     *
     * @param tenantId
     * @param dictType
     * @param dictName
     * @return
     */
    List<DictDataRespDTO> getDictDatas(Long tenantId, String dictType, String dictName);

    List<DictDataRespDTO> getDictDataList(Long tenantId, List<Long> ids);

    /**
     * 获得指定字典类型的字典数据列表
     *
     * @param dictTypeList 字典类型
     * @return 字典数据列表
     */
    Map<String, List<DictDataRespDTO>> getDictDataListByTypes(Long tenantId, List<String> dictTypeList);
}
