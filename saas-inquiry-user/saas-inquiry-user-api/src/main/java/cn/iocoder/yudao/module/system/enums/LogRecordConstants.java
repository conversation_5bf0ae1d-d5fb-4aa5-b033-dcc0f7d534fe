package cn.iocoder.yudao.module.system.enums;

/**
 * System 操作日志枚举 目的：统一管理，也减少 Service 里各种“复杂”字符串
 *
 * <AUTHOR>
 */
public interface LogRecordConstants {

    // ======================= SYSTEM_USER 用户 =======================

    String SYSTEM_USER_TYPE = "SYSTEM 用户";
    String SYSTEM_USER_CREATE_SUB_TYPE = "创建用户";
    String SYSTEM_USER_CREATE_SUCCESS = "创建了用户【{{#user.nickname}}】";
    String SYSTEM_USER_UPDATE_SUB_TYPE = "更新用户";
    String SYSTEM_USER_UPDATE_SUCCESS = "{_DIFF{#updateReqVO}}";
    String SYSTEM_USER_DELETE_SUB_TYPE = "删除用户";
    String SYSTEM_USER_DELETE_SUCCESS = "删除了用户【{{#user.nickname}}】";
    String SYSTEM_USER_UPDATE_PASSWORD_SUB_TYPE = "重置用户密码";
    String SYSTEM_USER_UPDATE_PASSWORD_SUCCESS = "将用户【{{#user.nickname}}】的密码从【{{#user.password}}】重置为【{{#newPassword}}】";

    // ======================= SYSTEM_ROLE 角色 =======================

    String SYSTEM_ROLE_TYPE = "SYSTEM 角色";
    String SYSTEM_ROLE_CREATE_SUB_TYPE = "创建角色";
    String SYSTEM_ROLE_CREATE_SUCCESS = "创建了角色【{{#role.name}}】";
    String SYSTEM_ROLE_UPDATE_SUB_TYPE = "更新角色";
    String SYSTEM_ROLE_UPDATE_SUCCESS = "{_DIFF{#updateReqVO}}";
    String SYSTEM_ROLE_DELETE_SUB_TYPE = "删除角色";
    String SYSTEM_ROLE_DELETE_SUCCESS = "删除了角色【{{#role.name}}】";


    // ======================= TENANT 门店 =======================
    String SYSTEM_TENANT_TYPE = "TENANT";

    String SYSTEM_TENANT_CREATE_SUB_TYPE = "创建门店";
    String SYSTEM_TENANT_CREATE_SUCCESS = "创建门店【{{#tenant.name}}】";

    String SYSTEM_TENANT_UPDATE_SUB_TYPE = "更新门店";
    String SYSTEM_TENANT_UPDATE_SUCCESS = "{_DIFF{#updateReqVO}}";

    String SYSTEM_TENANT_DELETE_SUB_TYPE = "删除门店";
    String SYSTEM_TENANT_DELETE_SUCCESS = "删除门店【{{#tenant.name}}】";


    String SYSTEM_TENANT_PARAM_CONFIG_CREATE_SUB_TYPE = "创建配置";
    String SYSTEM_TENANT_PARAM_CONFIG_CREATE_SUCCESS = "创建配置【{{#paramConfig.paramName}}】: {{#paramConfig.paramValue}}";

    String SYSTEM_TENANT_PARAM_CONFIG_UPDATE_SUB_TYPE = "更新配置";
    String SYSTEM_TENANT_PARAM_CONFIG_UPDATE_SUCCESS = "更新配置【{{#paramConfig.paramName}}】: 从【{{#paramConfig.paramValue}}】变更为 【{{#updateReqVO.paramValue}}】";


    // ======================= 门店套餐 =======================
    String SYSTEM_TENANT_PACKAGE = "TENANT_PACKAGE";

    String SYSTEM_TENANT_PACKAGE_CREATE_SUB_TYPE = "创建套餐";
    String SYSTEM_TENANTT_PACKAGE_CREATE_SUCCESS = "创建套餐【{{#tenantPackage.name}}】";

    String SYSTEM_TENANTT_PACKAGE_UPDATE_SUB_TYPE = "更新套餐";
    String SYSTEM_TENANTT_PACKAGE_UPDATE_SUCCESS = "{_DIFF{#updateReqVO}}";

    String SYSTEM_TENANTT_PACKAGE_DELETE_SUB_TYPE = "删除套餐";
    String SYSTEM_TENANTT_PACKAGE_DELETE_SUCCESS = "删除套餐【{{#tenantPackage.name}}】";


    // ======================= 门店套餐 关系 =======================
    String SYSTEM_TENANT_PACKAGE_RELATION = "TENANT_PACKAGE_RELATION";

    String SYSTEM_TENANT_PACKAGE_RELATION_CREATE_SUB_TYPE = "开通门店套餐包";
    String SYSTEM_TENANT_PACKAGE_RELATION_CREATE_SUCCESS = "开通套餐包【{{#tenantPackageRelation.packageName}}】";

    String SYSTEM_TENANT_PACKAGE_RELATION_UPDATE_SUB_TYPE = "更新门店套餐包";
    String SYSTEM_TENANT_PACKAGE_RELATION_UPDATE_SUCCESS = "更新套餐包【{{#_oldObj.packageName}}】:{_DIFF{#updateReqVO}}";
    String SYSTEM_TENANT_PACKAGE_RELATION_UPDATE_STATUS_SUCCESS = "更新套餐包【{{#_oldObj.packageName}}】:状态从【{{#oriStatus}}】变更为【{{#status}}】";

    // ======================= 门店服务包 关系 =======================

    String SYSTEM_TENANT_SERVICE_PACK_RELATION = "TENANT_SERVICE_PACK_RELATION";

    String SYSTEM_TENANT_SERVICE_PACK_RELATION_OPERATE_SUB_TYPE = "编辑服务包";

    String SYSTEM_TENANT_SERVICE_PACK_RELATION_CHANGE_SUB_TYPE = "切换服务包";

    String SYSTEM_TENANT_SERVICE_PACK_CATALOG_RELATION_CHANGE_SUB_TYPE = "切换目录";

    // ======================= 业务线 =======================
    String SYSTEM_BIZ = "SYSTEM_BIZ";

    String SYSTEM_BIZ_UPDATE_SUB_TYPE = "更新业务线";
    String SYSTEM_BIZ_UPDATE_SUCCESS = "{_DIFF{#updateReqVO}}";

}
