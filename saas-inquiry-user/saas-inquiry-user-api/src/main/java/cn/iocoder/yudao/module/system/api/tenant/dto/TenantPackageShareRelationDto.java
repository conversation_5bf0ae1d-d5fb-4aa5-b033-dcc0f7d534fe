package cn.iocoder.yudao.module.system.api.tenant.dto;

import com.xyy.saas.inquiry.pojo.BaseDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 总部门店套餐共享 DO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantPackageShareRelationDto extends BaseDto {

    /**
     * 主键
     */
    private Long id;
    /**
     * 总部id
     */
    private Long headTenantId;

    /**
     * 门店Id
     */
    private Long tenantId;
    /**
     * 业务类型 0-问诊,1-智慧脸...
     */
    private Integer bizType;
    /**
     * 共享套餐开通关系表id
     */
    private Long tenantPackageId;
    /**
     * 扩展信息
     */
    private String ext;

}