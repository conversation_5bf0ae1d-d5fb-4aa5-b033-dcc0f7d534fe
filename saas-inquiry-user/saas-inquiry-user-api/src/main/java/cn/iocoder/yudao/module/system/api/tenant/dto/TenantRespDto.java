package cn.iocoder.yudao.module.system.api.tenant.dto;

import com.xyy.saas.inquiry.pojo.tenant.TenantCertificateRespDto;
import com.xyy.saas.inquiry.pojo.tenant.TenantExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Schema(description = "管理后台 - 门店 Response VO")
@Data
public class TenantRespDto implements Serializable {

    @Schema(description = "门店编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "14411")
    private Long id;

    @Schema(description = "门店编码", example = "MD100001")
    private String pref;

    @Schema(description = "门店名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    private String name;

    @Schema(description = "联系人的用户id", example = "11333")
    private Long contactUserId;

    @Schema(description = "联系人", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String contactName;

    @Schema(description = "联系手机")
    private String contactMobile;

    @Schema(description = "门店状态（0正常 1停用）", example = "1")
    private Integer status;

    /**
     * {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "问诊系统业务类型开通", example = "0")
    private Integer wzBizTypeStatus;

    @Schema(description = "账号数量", example = "26469")
    private Integer wzAccountCount;


    @Schema(description = "智慧脸业务线类型开通", example = "2")
    private Integer zhlBizTypeStatus;

    @Schema(description = "智慧脸账号数量", example = "17503")
    private Integer zhlAccountCount;


    @Schema(description = "营业执照名称", example = "张三")
    private String businessLicenseName;

    @Schema(description = "营业执照号")
    private String businessLicenseNumber;

    @Schema(description = "省", requiredMode = Schema.RequiredMode.REQUIRED)
    private String province;

    @Schema(description = "省编码")
    private String provinceCode;

    @Schema(description = "市", requiredMode = Schema.RequiredMode.REQUIRED)
    private String city;

    @Schema(description = "市编码")
    private String cityCode;

    @Schema(description = "区", requiredMode = Schema.RequiredMode.REQUIRED)
    private String area;

    @Schema(description = "区编码")
    private String areaCode;

    @Schema(description = "药店地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String address;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "环境标志：prod-真实数据；test-测试数据；show-线上演示数据")
    private String envTag;

    @Schema(description = "租户拓展字段")
    private TenantExtDto ext;

    @Schema(description = "门店是否存在当前选择")
    private boolean existsOptionType;

    List<TenantCertificateRespDto> bizTypeList;

}
