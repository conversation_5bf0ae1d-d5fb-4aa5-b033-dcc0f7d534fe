package cn.iocoder.yudao.module.system.api.tenant.dto;

import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryPackageItem;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Set;
import lombok.Data;

@Schema(description = "管理后台 - 门店套餐 Response Dto")
@Data
public class TenantPackageRespDto implements Serializable {

    @Schema(description = "套餐编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "1024")
    private Long id;

    @Schema(description = "套餐名", requiredMode = Schema.RequiredMode.REQUIRED, example = "VIP")
    private String name;

    @Schema(description = "状态，参见 CommonStatusEnum 枚举", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    @Schema(description = "备注", example = "好")
    private String remark;

    @Schema(description = "关联的菜单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private Set<Long> menuIds;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "业务类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer bizType;

    // ------------------------- 问诊包信息 -------------------------

    @Schema(description = "问诊套餐包关联医院id", example = "1,2")
    private List<String> hospitalPrefs;

    @Schema(description = "问诊套餐包关联医院名称", example = "xx医院,xx医院")
    private String hospitalName;


    @Schema(description = "问诊业务类型", example = "1")
    private Integer inquiryBizType;

    @Schema(description = "问诊审方类型", example = "1")
    private Integer inquiryAuditType;


    @Schema(description = "套餐定价", example = "999")
    private BigDecimal price;

    @Schema(description = "套餐时限", example = "1")
    private Integer term;

    @Schema(description = "时限类型", example = "year")
    private Integer termType;

    /**
     * {@link InquiryWayTypeEnum}
     */
    @Schema(description = "问诊方式类型", example = "1,2")
    private List<Integer> inquiryWayTypes;

    @Schema(description = "问诊包信息")
    private List<InquiryPackageItem> inquiryPackageItems;

    @Schema(description = "套餐排序", example = "99")
    private Integer sorted;

    @Schema(description = "套餐可见地区编码,对应区域编码", example = "420000,420100")
    private List<String> regionArr;

    @Schema(description = "套餐底部文案", example = "套餐底部文案")
    @Max(value = 255)
    private String bottomTxt;

    @Schema(description = "套餐推荐文案", example = "套餐推荐文案")
    @Max(value = 255)
    private String recommendTxt;

    @Schema(description = "问诊包信息文案")
    private String inquiryPackageItemStr;

    public String getInquiryPackageItemStr() {
        return InquiryPackageItem.getInquiryPackageItemStr(getInquiryBizType(), getInquiryPackageItems());
    }
}
