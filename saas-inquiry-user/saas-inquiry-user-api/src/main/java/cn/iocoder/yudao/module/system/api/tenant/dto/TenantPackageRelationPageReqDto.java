package cn.iocoder.yudao.module.system.api.tenant.dto;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 门店套餐订单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantPackageRelationPageReqDto extends PageParam {

    @Schema(description = "门店套餐编号 0自定义套餐", example = "14967")
    private Long packageId;

    @Schema(description = "编码", example = "xx100001")
    private String pref;


    /**
     * 套餐包ids
     */
    private List<Long> packageIds;

    @Schema(description = "门店id", example = "14967")
    private Long tenantId;

    @Schema(description = "门店id", example = "14967")
    private Long zhlTenantId;

    @Schema(description = "门店名称", example = "14967")
    private String tenantName;

    @Schema(description = "省编码")
    private String provinceCode;

    @Schema(description = "市编码")
    private String cityCode;

    @Schema(description = "区编码")
    private String areaCode;

    @Schema(description = "套餐名", example = "张三")
    private String packageName;

    /**
     * {@link TenantPackageRelationStatusEnum}
     */
    @Schema(description = "订单状态", example = "1")
    private Integer status;

    @Schema(description = "开始时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "收款方式：0线上，1线下", example = "1")
    private Integer paymentType;

    @Schema(description = "服务包性质", example = "1")
    private Integer packageNature;

    @Schema(description = "签约日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] signTime;

    @Schema(description = "签约人")
    private String signUser;

    @Schema(description = "代理人/中间人")
    private String proxyUser;

    @Schema(description = "签约渠道(eg:3智鹿)")
    private Integer signChannel;

    @Schema(description = "实收金额 ")
    private BigDecimal actualAmount;

    @Schema(description = "收款账户 eg:(微信对公-成都)", example = "31497")
    private Integer collectAccount;

    @Schema(description = "付款流水号")
    private String payNo;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    /**
     * 问诊方式类型 {@link InquiryWayTypeEnum}
     */
    private List<Integer> inquiryWayTypes;
    /**
     * 问诊医院id
     */
    private String hospitalPref;

    @Schema(description = "系统类型", example = "1")
    private Integer bizType;

    @Schema(description = "套餐类型", example = "1")
    private Integer packageType;

    @Schema(description = "问诊业务类型", example = "1")
    private Integer inquiryBizType;

    @Schema(description = "问诊审方类型", example = "1")
    private Integer inquiryAuditType;

    /**
     * 问诊处方类型 {@link com.xyy.saas.inquiry.enums.prescription.PrescriptionTypeEnum}
     */
    @Schema(description = "问诊处方类型", example = "0,1")
    private List<Integer> prescriptionTypes;

    /**
     * {@link CommonStatusEnum}
     */
    @Schema(description = "是否生效", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer effective;

    /**
     * {@link CommonStatusEnum}
     */
    @Schema(description = "是否生效冲额套餐", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer recharge;


}