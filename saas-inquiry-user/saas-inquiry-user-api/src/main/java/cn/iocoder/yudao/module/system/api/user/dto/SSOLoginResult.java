package cn.iocoder.yudao.module.system.api.user.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
public class SSOLoginResult implements Serializable {

    private boolean success;

    private String msg;

    private String tgc;

    public static SSOLoginResult success(String tgc) {
        SSOLoginResult ssoLoginResult = new SSOLoginResult();
        ssoLoginResult.setAllParam(true, tgc, "");
        return ssoLoginResult;
    }

    public static SSOLoginResult error(String msg) {
        SSOLoginResult ssoLoginResult = new SSOLoginResult();
        ssoLoginResult.setAllParam(false, "", msg);
        return ssoLoginResult;
    }

    public SSOLoginResult setAllParam(boolean success, String tgc, String msg) {
        this.success = success;
        this.msg = msg;
        this.tgc = tgc;
        return this;
    }
}
