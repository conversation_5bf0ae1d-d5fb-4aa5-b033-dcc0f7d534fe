package cn.iocoder.yudao.module.system.enums.permission;

import com.xyy.saas.inquiry.enums.system.ClientTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 菜单类型枚举类
 *
 * <AUTHOR>
 */
@Getter
@AllArgsConstructor
public enum MenuTypeEnum {

    DIR(1), // 目录
    MENU(2), // 菜单
    BUTTON(3), // 按钮
    APP_MENU(4) // APP菜单
    ;

    /**
     * 类型
     */
    private final Integer type;


    public static List<Integer> clientMenuTypes(ClientTypeEnum clientTypeEnum) {
        if (clientTypeEnum == ClientTypeEnum.APP) {
            return Stream.of(APP_MENU.type).collect(Collectors.toList());
        }
        return Stream.of(DIR.type, MENU.type, BUTTON.type).collect(Collectors.toList());
    }

}
