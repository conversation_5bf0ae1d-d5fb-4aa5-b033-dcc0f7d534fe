package cn.iocoder.yudao.module.system.api.tenant.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import java.io.Serializable;
import java.util.List;

@Schema(description = "管理后台 - 门店三方应用配置 Response DTO")
@Data
@ToString(callSuper = true)
public class TenantThirdAppReqDto implements Serializable {

    @Schema(description = "id集合")
    private List<Long> idList;

}
