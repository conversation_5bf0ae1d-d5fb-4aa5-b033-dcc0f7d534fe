package cn.iocoder.yudao.module.system.api.dict.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.Map;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class DictDataSaveBatchDto implements Serializable {

    /**
     * 租户ID
     */
    @Schema(description = "", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;

    /**
     * 字典类型
     */
    private String dictType;

    /**
     * 字典键值
     */
    private Map<String, String> valueLabelMap;

}
