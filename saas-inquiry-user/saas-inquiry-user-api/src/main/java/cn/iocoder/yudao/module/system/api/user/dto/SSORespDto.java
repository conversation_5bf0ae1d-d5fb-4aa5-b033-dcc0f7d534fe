package cn.iocoder.yudao.module.system.api.user.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * OA sso 单点登录响应
 *
 * @Author:chenxiaoyi
 * @Date:2024/09/14 16:53
 */
@Data
public class SSORespDto<T> implements Serializable {

    /**
     * oa接口响应code
     */
    private int code;

    /**
     * 登录凭证
     */
    private String msg;

    /**
     * OA ID
     */
    private T result;


}
