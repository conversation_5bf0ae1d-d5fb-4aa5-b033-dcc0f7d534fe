package cn.iocoder.yudao.module.system.api.user.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class SSOUser implements Serializable {

    /**
     * oa账号
     */
    private String account;
    /**
     * oaId
     */
    private long oaId;
    /**
     * 用户真实姓名
     */
    private String realname;
    /**
     * 昵称
     */
    private String nickname;
    /**
     * 工号 E0001
     */
    private String staffNum;
    /**
     * 性别 1男 2女
     */
    private int sex;
    /**
     * 用户状态 未知
     */
    private int userStatus;
    /**
     * 手机号
     */
    private String mobile;
    /**
     * 公司邮箱 <EMAIL>
     */
    private String email;
    /**
     * 联系电话 通mobile
     */
    private String tel;
    /**
     * 头像url
     */
    private String imgUrl;

    /**
     * 创建时间
     */
    private String createTime;
    /**
     * 更新时间
     */
    private String updateTime;
    /**
     * 创建人
     */
    private String createUser;
    /**
     * 更新人
     */
    private String updateUser;
    /**
     * oa是否有效 未知
     */
    private int yn;
    /**
     * oa状态 未知
     */
    private int state;

}
