package cn.iocoder.yudao.module.system.api.tenant.dto;

import com.xyy.saas.inquiry.enums.user.ClockInTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2024/09/13 20:27
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class TenantUserClockInDto implements Serializable {

    @Schema(description = "用户id", example = "111")
    private Long userId;


    @Schema(description = "门店id", example = "111")
    private Long tenantId;

    /**
     * 打卡类型 {@link ClockInTypeEnum}
     */
    @NotNull(message = "打卡类型不能为空")
    private ClockInTypeEnum clockInType;

    /**
     * 用户 IP
     */
    private String userIp;
    /**
     * 浏览器 UA
     */
    private String userAgent;

}
