package cn.iocoder.yudao.module.system.api.tenant;

import cn.iocoder.yudao.module.system.api.tenant.dto.TenantThirdAppReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantThirdAppRespDto;
import java.util.List;

/**
 * 门店关联三方服务配置
 */
public interface TenantThirdAppApi {

    /**
     * 获取门店关联三方服务配置
     *
     * @return 门店DTO
     */
    List<TenantThirdAppRespDto> getByTenantId(Long tenantId);

    /**
     * 获取门店关联三方服务配置
     *
     * @return 门店DTO
     */
    List<TenantThirdAppRespDto> getByTenantIdList(List<Long> tenantIdList);

    /**
     * 获取门店关联三方服务配置
     *
     * @return 门店DTO
     */
    List<TenantThirdAppRespDto> getListByCondition(TenantThirdAppReqDto tenantThirdAppReqDto);

}
