package cn.iocoder.yudao.module.system.api.user.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * OA sso 单点登录dto
 *
 * @Author:chenxiaoyi
 * @Date:2024/09/14 16:53
 */
@Data
public class OASsoConfigDto implements Serializable {

    /**
     * sso单点登录url
     */
    private String ssoUrl;
    /**
     * oa中系统id
     */
    private String oaSystemId;
    /**
     * oa系统业务地址
     */
    private String oaBusinessUrl;
    /**
     * oa系统登录接口path
     */
    private String loginPath;
    /**
     * oa系统用户详情接口path
     */
    private String userDetailPath;
    /**
     * oa系统查询用户账号接口path
     */
    private String queryAccountPath;
    /**
     * oa系统登出接口path
     */
    private String loginOutPath;
    /**
     * 自己业务地址域名
     */
    private String serviceUrl;

}
