package cn.iocoder.yudao.module.system.api.tenant.migration;

import cn.iocoder.yudao.module.system.api.tenant.migration.dto.MigrationTrailPackageDto;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserSaveDTO;
import com.xyy.saas.inquiry.pojo.migration.MigrationDrugStoreRespDto;
import com.xyy.saas.inquiry.pojo.migration.MigrationPackageRespDto;
import java.util.List;

/**
 * 门店迁移 API 接口
 *
 * <AUTHOR>
 */
public interface TenantMigrationApi {

    /**
     * 迁移门店 有tenantId 修改,判断营业执照号重复?
     *
     * @param migrationDrugStoreRespDto
     * @return
     */
    Long migrationTenant(MigrationDrugStoreRespDto migrationDrugStoreRespDto);

    /**
     * 迁移员工 手机号存在 则绑定到当前门店
     *
     * @param tenantId
     * @param employees
     * @return
     */
    Long migrationEmployee(AdminUserSaveDTO userSaveDTO);

    /**
     * 绑定员工到门店
     *
     * @param userId
     */
    void bindTenant(Long userId);

    /**
     * 解绑所有员工
     *
     * @param tenantId
     */
    void unBindEmployee(Long tenantId);

    /**
     * 创建迁移体验套餐
     *
     * @param tenantId
     */
    Long createTrailPackage(MigrationTrailPackageDto trailPackageDto);

    /**
     * 迁移套餐
     */
    List<Long> migrationPackage(List<MigrationPackageRespDto> saveDtos);

    /**
     * 迁移套餐失败-删除套餐
     *
     * @param packageIds
     */
    void deleteMigrationPackage(List<Long> packageIds);


}
