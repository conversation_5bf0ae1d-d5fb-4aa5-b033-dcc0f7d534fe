package com.xyy.saas.inquiry.util.excel;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.constant.ErrorCodeConstants.UPLOAD_EXCEL_MORE_THAN_LIMIT;
import static com.xyy.saas.inquiry.constant.ErrorCodeConstants.UPLOAD_EXCEL_TEMP_UN_MATCH;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import com.alibaba.excel.annotation.ExcelProperty;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;

/**
 * easyexcel自动检查监听者
 *
 * @param <T>
 */
@Slf4j
@Getter
public class AutoCheckExcelListener<T> extends AnalysisEventListener<T> {

    /**
     * 存储数据集合
     */
    private final List<T> dataList = new ArrayList<>();

    /**
     * 限制条数
     */
    private final int limitCount;

    /**
     * 构造函数
     */
    private final Class<T> clazz;

    /**
     * 忽略比对的excel列名
     */
    private final List<String> ignoreExcelColumnNameList = new ArrayList<>() {{
        add("失败原因");
        add("错误信息");
    }};

    public AutoCheckExcelListener(int limitCount, Class<T> clazz, String... ignoreExcelColumnName) {
        this.limitCount = limitCount;
        this.clazz = clazz;
        if (ignoreExcelColumnName.length > 0) {
            this.ignoreExcelColumnNameList.addAll(List.of(ignoreExcelColumnName));
        }
    }

    /**
     * 校验excel表头信息是否匹配
     *
     * @param headMap
     * @param context
     */
    @Override
    public void invokeHeadMap(Map<Integer, String> headMap, AnalysisContext context) {

        if (clazz == null) {
            return;
        }

        try {

            // 获取excel对象上index和名称映射
            Map<Integer, String> indexNameMap = this.getClassIndexNameMap(clazz);

            if (MapUtils.isEmpty(indexNameMap)) {
                throw exception(UPLOAD_EXCEL_TEMP_UN_MATCH, "解析字段为空");
            }

            for (Integer key : indexNameMap.keySet()) {
                // 考虑到导入和导出模版用一个对象 , 并在导出模版中定义了错误信息字段导致导入模版的字段比对失败问题
                // 统一处理忽略比对的excel列名
                if (CollUtil.isNotEmpty(ignoreExcelColumnNameList) && ignoreExcelColumnNameList.contains(indexNameMap.get(key))) {
                    continue;
                }
                if (StringUtils.isBlank(headMap.get(key))) {
                    throw exception(UPLOAD_EXCEL_TEMP_UN_MATCH, indexNameMap.get(key));
                }
                if (!StringUtils.equals(headMap.get(key), indexNameMap.get(key))) {
                    throw exception(UPLOAD_EXCEL_TEMP_UN_MATCH, headMap.get(key));
                }
            }

        } catch (ServiceException e) {
            log.error("AutoCheckExcelListener#invokeHeadMap#解析Excel失败", e);
            throw e;
        } catch (NoSuchFieldException e) {
            log.error("AutoCheckExcelListener#invokeHeadMap#解析Excel失败", e);
            throw exception(UPLOAD_EXCEL_TEMP_UN_MATCH, "系统异常");
        }
    }

    /**
     * 获取excel对象上index和名称映射
     *
     * @param clazz
     * @return
     * @throws NoSuchFieldException
     */
    public Map<Integer, String> getClassIndexNameMap(Class<T> clazz) throws NoSuchFieldException {

        Field field;
        Field[] fields = clazz.getDeclaredFields();
        Map<Integer, String> result = new HashMap<>();

        for (Field item : fields) {

            field = clazz.getDeclaredField(item.getName());
            field.setAccessible(true);

            ExcelProperty excelProperty = field.getAnnotation(ExcelProperty.class);

            if (excelProperty != null) {

                int index = excelProperty.index();
                String[] values = excelProperty.value();
                StringBuilder value = new StringBuilder();

                for (String v : values) {
                    value.append(v);
                }

                result.put(index, value.toString());
            }
        }
        return result;
    }

    /**
     * 填充解析数据
     *
     * @param data
     * @param context
     */
    @Override
    public void invoke(T data, AnalysisContext context) {

        dataList.add(data);

        if (dataList.size() > limitCount) {
            throw exception(UPLOAD_EXCEL_MORE_THAN_LIMIT, limitCount);
        }
    }

    /**
     * 解析完数据后掉用的方法
     *
     * @param context
     */
    @Override
    public void doAfterAllAnalysed(AnalysisContext context) {
        log.info("AutoCheckExcelListener#dataList#size:{}", dataList.size());
    }

}
