package com.xyy.saas.inquiry.transmitter.api.config.dto;

import com.xyy.saas.inquiry.pojo.BaseDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Builder;
import lombok.Data;

@Schema(description = "管理后台 - 协议配置包 DTO")
@Data
@Builder
public class TransmissionConfigPackageDTO extends BaseDto {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "29798")
    private Integer id;

    @Schema(description = "机构类型（1-医保、2-药监、3-互联网监管、4-ERP对接、5-HIS对接、99-其他）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer organType;

    @Schema(description = "机构类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "医保")
    private Integer organTypeDesc;

    @Schema(description = "服务提供商名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "王五")
    private String providerName;

    @Schema(description = "父节点id", example = "28667")
    private Integer parentPackageId;

    @Schema(description = "配置包名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String name;

    @Schema(description = "版本号（实际存储：**********；页面展示：服务提供商名称+机构类型名称+日期+小时，比如：创智医保20250122）", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long version;

    @Schema(description = "描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "更新动态库xxx")
    private String description;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    private Boolean disable;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime createTime;

    @Schema(description = "功能数", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long itemCount;

    @Schema(description = "业务节点信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<TransmissionConfigItemDTO> configItems;

    @Schema(description = "业务节点信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<TransmissionConfigItemDTO> commonConfigItems;

}