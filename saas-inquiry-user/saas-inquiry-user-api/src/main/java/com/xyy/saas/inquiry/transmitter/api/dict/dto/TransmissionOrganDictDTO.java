package com.xyy.saas.inquiry.transmitter.api.dict.dto;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 服务商字典 DO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class TransmissionOrganDictDTO extends PageParam {

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 服务提供方id
     */
    private Integer organId;
    /**
     * 字典类型
     */
    private String dictType;
    /**
     * 字典名称
     */
    private String dictName;
    /**
     * 父值 空无父值
     */
    private String parentValue;

    /**
     * 是否末级节点
     */
    private Integer endNode;
    /**
     * 字典标签
     */
    private String label;
    /**
     * 字典值
     */
    private String value;
    /**
     * 字典外码
     */
    private String outerValue;
    /**
     * 状态（0正常 1停用）
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 排序
     */
    private Integer sort;

    private List<String> dictNames;

    private List<String> values;

    /**
     * saas字典ID
     */
    private List<Long> dictIds;

    /**
     * 页码，从 1 开始
     */
    private Integer pageNo = 1;

    /**
     * 每页条数，最大值为 100
     */
    private Integer pageSize = 10;

}