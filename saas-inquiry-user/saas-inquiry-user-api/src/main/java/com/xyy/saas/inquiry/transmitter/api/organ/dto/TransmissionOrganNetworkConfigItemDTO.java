package com.xyy.saas.inquiry.transmitter.api.organ.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 医药行业行政机构 - 网络配置节点 VO")
@Data
public class TransmissionOrganNetworkConfigItemDTO {

    @Schema(description = "网络配置类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "ecToken")
    @NotNull(message = "网络配置类型不能为空")
    private String code;

    @Schema(description = "网络配置链接", requiredMode = Schema.RequiredMode.REQUIRED, example = "医保专网")
    @NotNull(message = "网络配置链接不能为空")
    private String url;
}