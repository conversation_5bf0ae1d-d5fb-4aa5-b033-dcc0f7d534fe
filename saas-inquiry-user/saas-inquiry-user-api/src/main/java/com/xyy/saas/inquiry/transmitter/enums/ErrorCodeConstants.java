package com.xyy.saas.inquiry.transmitter.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * System 错误码枚举类
 * <p>
 * system 系统，使用 1-003-000-000 段
 */
public interface ErrorCodeConstants {

    ErrorCode SAAS_DICT_NOT_EXISTS = new ErrorCode(1_003_000_000, "基础字典数据不存在");

    ErrorCode TRANSMISSION_PROVIDER_DICT_NOT_EXISTS = new ErrorCode(1_003_000_001, "服务商字典不存在");

    ErrorCode TRANSMISSION_PROVIDER_DICT_DATA_NOT_EXISTS = new ErrorCode(1_003_000_002, "服务商字典数据不存在,不可操作");

    ErrorCode TRANSMISSION_PROVIDER_DICT_MATCH_NOT_EXISTS = new ErrorCode(1_003_000_003, "服务商数据字典配对不存在");

    ErrorCode TRANSMISSION_ORGAN_NOT_EXISTS = new ErrorCode(1_003_000_004, "医药行业行政机构不存在");

    ErrorCode TRANSMISSION_ORGAN_TYPE_CHANGE_NOT_ALLOW = new ErrorCode(1_003_000_005, "医药行业行政机构已绑定服务包，不允许修改类型");

    ErrorCode TRANSMISSION_ORGAN_NETWORK_CONFIG_EXISTS = new ErrorCode(1_003_000_006, "医药行业行政机构网络配置重复");

    ErrorCode TRANSMISSION_ORGAN_NETWORK_CONFIG_ITEM_EXISTS = new ErrorCode(1_003_000_007, "医药行业行政机构网络配置节点重复");


    ErrorCode TRANSMISSION_ORGAN_EXISTS = new ErrorCode(1_003_000_008, "已存在该服务商，请检查后重新保存");

    ErrorCode TRANSMISSION_CONFIG_PACKAGE_NOT_EXISTS = new ErrorCode(1_003_000_009, "配置包不存在");

    ErrorCode TRANSMISSION_CONFIG_PACKAGE_EXISTS = new ErrorCode(1_003_000_010, "配置包[{}],版本[{}]已存在,同一类型配置包,一小时内只能存在一个版本");

    ErrorCode TRANSMISSION_CONFIG_PACKAGE_ONLINE_NOT_ALLOW_MODIFY =
        new ErrorCode(1_003_000_011, "配置包已被线上环境的服务包引用，不允许修改配置");

    ErrorCode TRANSMISSION_CONFIG_ITEM_NOT_EXISTS = new ErrorCode(1_003_000_012, "配置不存在");

    ErrorCode TRANSMISSION_CONFIG_ITEM_DSL_TYPE_NOT_NULL = new ErrorCode(1_003_000_013, "配置类型不能为空");

    ErrorCode TRANSMISSION_CONFIG_ITEM_DUPLICATE = new ErrorCode(1_003_000_013, "已存在相同的配置项");

    ErrorCode TRANSMISSION_CONFIG_ITEM_CIRCULAR_DEPENDENCY = new ErrorCode(1_003_000_014, "配置项存在循环依赖");

    ErrorCode TRANSMISSION_SERVICE_PACK_NOT_EXISTS = new ErrorCode(1_003_000_015, "服务包不存在");

    ErrorCode TRANSMISSION_SERVICE_PACK_EXISTS = new ErrorCode(1_003_000_016, "服务包已存在");

    ErrorCode TRANSMISSION_SERVICE_PACK_TRANSMISSION_ORGAN_TYPE_NOT_MATCH =
        new ErrorCode(1_003_000_017, "机构类型与配置包类型不匹配");

    ErrorCode TRANSMISSION_SERVICE_PACK_DISABLE_FAIL = new ErrorCode(1_003_000_018, "该配置包已被{}个门店使用，无法禁用！");

    ErrorCode TENANT_TRANSMISSION_SERVICE_PACK_NOT_EXISTS = new ErrorCode(1_003_000_019, "租户-开通服务包不存在");

    ErrorCode TENANT_TRANSMISSION_TASK_RECORD_NOT_EXISTS = new ErrorCode(1_003_000_020, "数据传输-记录不存在");

    ErrorCode TRANSMISSION_REQ_VALIDATION_FAILED = new ErrorCode(1_003_000_021, "请求数据传输-入参校验失败:");

    ErrorCode TRANSMISSION_TASK_CREATE_FAILED = new ErrorCode(1_003_000_022, "请求数据传输-任务创建失败:");

    ErrorCode TRANSMISSION_POST_PROCESS_FAILED = new ErrorCode(1_003_000_023, "请求数据传输-任务后置处理失败:");

    ErrorCode TRANSMISSION_REQUEST_FAILED = new ErrorCode(1_003_000_024, "请求数据传输-请求失败:");

    ErrorCode TRANSMISSION_RESPONSE_NULL = new ErrorCode(1_003_000_025, "请求数据传输-响应结果为空");

    ErrorCode TRANSMISSION_TASK_NOT_EXISTS = new ErrorCode(1_003_000_026, "任务不存在");

    ErrorCode TRANSMISSION_PROTOCOL_CONFIG_NOT_EXISTS = new ErrorCode(1_003_000_027, "协议配置不存在");

    ErrorCode TRANSMISSION_PROTOCOL_CONFIG_PARSE_FAILED = new ErrorCode(1_003_000_028, "协议配置解析失败");

    ErrorCode TRANSMISSION_CONFIG_ITEM_NODE_NOT_EXISTS = new ErrorCode(1_003_000_029, "配置节点不存在:");

    ErrorCode TRANSMISSION_NOT_SUPPORT_NODE_TYPE = new ErrorCode(1_003_000_030, "不支持该节点类型");

    ErrorCode TRANSMISSION_NOT_SUPPORT_ORGAN_TYPE = new ErrorCode(1_003_000_031, "不支持该机构类型");


    ErrorCode TRANSMISSION_CONFIG_RELATION_SERVICE_PACKAGE_USED = new ErrorCode(1_003_000_032, "该配置包已被{}个服务包使用，无法禁用！");

    ErrorCode TRANSMISSION_CONFIG_RELATION_SERVICE_PACKAGE_PROD = new ErrorCode(1_003_000_033, "该配置包关联的服务包已上线,不可操作！");

    ErrorCode TRANSMISSION_SERVICE_PACKAGE_PROD = new ErrorCode(1_003_000_034, "该服务包已上线,不可操作！");

    ErrorCode TRANSMISSION_CONFIG_RELATION_PARENT = new ErrorCode(1_003_000_035, "该配置包已成为其他父类配置包,无法禁用！");

    ErrorCode TRANSMISSION_CONFIG_ITEM_RELATION_PARENT = new ErrorCode(1_003_000_036, "该配置已成为其他父类配置,无法禁用！");

    /**
     * 格式化错误信息 统一错误信息的格式
     * <p>
     * 格式规则： 1. 包含错误码描述 2. 包含具体异常信息
     *
     * @param errorCode 错误码
     * @param e         异常对象
     * @return 格式化后的错误信息
     */
    static String formatErrorMsg(ErrorCode errorCode, Exception e) {
        if (e == null) {
            return errorCode.getMsg();
        }
        return String.format("%s: %s", errorCode.getMsg(), e.getMessage());
    }

    /**
     * 格式化错误信息 统一错误信息的格式
     * <p>
     * 格式规则： 1. 包含错误码描述 2. 包含具体异常信息
     *
     * @param errorCode 错误码
     * @param e         异常对象
     * @return 格式化后的错误信息
     */
    static ErrorCode formatError(ErrorCode errorCode, Exception e) {
        return new ErrorCode(errorCode.getCode(), formatErrorMsg(errorCode, e));
    }
}
