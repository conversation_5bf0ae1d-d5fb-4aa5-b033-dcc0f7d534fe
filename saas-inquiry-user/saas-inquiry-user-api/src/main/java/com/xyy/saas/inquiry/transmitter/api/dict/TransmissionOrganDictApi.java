package com.xyy.saas.inquiry.transmitter.api.dict;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.transmitter.api.dict.dto.TransmissionOrganDictDTO;
import java.util.List;

/**
 * 服务商字典 Api 接口
 */
public interface TransmissionOrganDictApi {

    /**
     * 查询字典列表
     *
     * @param transmissionOrganDictDTO 查询条件
     * @return 字典列表
     */
    List<TransmissionOrganDictDTO> queryDictList(TransmissionOrganDictDTO transmissionOrganDictDTO);

    /**
     * 分页查询字典数据
     *
     * @param pageReqDTO 分页查询条件
     * @return 分页结果
     */
    PageResult<TransmissionOrganDictDTO> queryDictPage(TransmissionOrganDictDTO pageReqDTO);


    /**
     * 查询匹配的saas字典列表
     *
     * @param transmissionOrganDictDTO
     * @return
     */
    List<Long> queryMatchSaasDictByValues(TransmissionOrganDictDTO transmissionOrganDictDTO);

    /**
     * 查询匹配的saas字典列表
     *
     * @param transmissionOrganDictDTO
     * @return
     */
    List<TransmissionOrganDictDTO> queryMatchSaasDictByDictIds(TransmissionOrganDictDTO transmissionOrganDictDTO);
}