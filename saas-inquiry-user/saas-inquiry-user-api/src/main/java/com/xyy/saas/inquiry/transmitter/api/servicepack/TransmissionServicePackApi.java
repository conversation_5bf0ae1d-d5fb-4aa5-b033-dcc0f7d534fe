package com.xyy.saas.inquiry.transmitter.api.servicepack;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.transmitter.api.servicepack.dto.TenantTransmissionServicePackRespDTO;
import com.xyy.saas.inquiry.transmitter.api.servicepack.dto.TransmissionServicePackDTO;
import com.xyy.saas.inquiry.transmitter.api.config.dto.TransmissionConfigReqDTO;
import java.util.List;

/**
 * @Author:chenxiaoyi
 * @Date:2025/02/07 9:47
 */

/**
 * 传输服务包 API 接口
 * <p>
 * 功能： 1. 根据服务包ID里列表，批量查询服务包信息 2. 根据业务节点、租户ID等，批量获取配置信息
 *
 * <AUTHOR>
 */
public interface TransmissionServicePackApi {

    /**
     * 批量查询服务包信息
     * <p>
     * 查询内容： 1. 服务包基本信息 2. 关联的配置信息 3. 资源信息（动态库、模板等）
     *
     * @param servicePackIds 服务包ID列表
     * @return 服务包信息列表
     */
    List<TenantTransmissionServicePackRespDTO> selectServicePacksByIds(List<Integer> servicePackIds);

    List<TenantTransmissionServicePackRespDTO> selectServicePacks(TransmissionServicePackDTO packDTO);

    /**
     * 查询租户服务包的配置项 - 互联网监管
     * <p>
     * 查询规则： 1. 根据业务节点类型匹配配置 2. 按配置类型过滤 3. 支持泛型返回结果
     *
     * @param reqDTO 查询条件（租户ID、业务节点、配置类型等）
     * @return 配置项内容
     */
    <T> CommonResult<T> selectConfigItem(TransmissionConfigReqDTO reqDTO, Class<T> clazz);

}
