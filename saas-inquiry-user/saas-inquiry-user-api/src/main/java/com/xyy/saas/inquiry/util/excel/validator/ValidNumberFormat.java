package com.xyy.saas.inquiry.util.excel.validator;

import jakarta.validation.Constraint;
import jakarta.validation.Payload;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

@Target({ElementType.FIELD, ElementType.PARAMETER})
@Retention(RetentionPolicy.RUNTIME)
@Constraint(validatedBy = ValidNumberValidator.class)
public @interface ValidNumberFormat {

    String message() default "格式错误";

    Class<?>[] groups() default {};

    int scale() default 2;

    double min() default 0.00;

    double max() default 9999999.99;

    Class<? extends Payload>[] payload() default {};
}