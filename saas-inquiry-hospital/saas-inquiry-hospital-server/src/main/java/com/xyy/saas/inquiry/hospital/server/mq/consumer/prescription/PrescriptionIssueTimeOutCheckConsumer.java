package com.xyy.saas.inquiry.hospital.server.mq.consumer.prescription;

import cn.hutool.core.util.ObjectUtil;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.enums.inquiry.InquiryStatusEnum;
import com.xyy.saas.inquiry.hospital.server.mq.message.prescription.PrescriptionIssueTimeOutCheckEvent;
import com.xyy.saas.inquiry.hospital.server.mq.message.prescription.dto.PrescriptionIssueTimeOutCheckMessage;
import com.xyy.saas.inquiry.hospital.server.mq.producer.prescription.PrescriptionIssueTimeOutCheckProducer;
import com.xyy.saas.inquiry.hospital.server.service.message.DoctorImService;
import com.xyy.saas.inquiry.hospital.server.service.prescription.PrescriptionService;
import com.xyy.saas.inquiry.im.enums.PushContentEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * 处方开具超时检查mq
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_hospital_server_mq_consumer_reception_PrescriptionIssueTimeOutCheckConsumer",
    topic = PrescriptionIssueTimeOutCheckEvent.TOPIC)
public class PrescriptionIssueTimeOutCheckConsumer {

    @Resource
    private PrescriptionService prescriptionService;

    @Resource
    private PrescriptionIssueTimeOutCheckProducer prescriptionIssueTimeOutCheckProducer;

    @Resource
    private DoctorImService doctorImService;

    @DubboReference
    private InquiryApi inquiryApi;


    public static final String GROUP_ID = PrescriptionIssueTimeOutCheckConsumer.class.getName();

    @EventBusListener
    public void prescriptionIssueTimeOutCheck(PrescriptionIssueTimeOutCheckEvent prescriptionIssueTimeOutCheckEvent) {
        // 校验问诊状态是否还在链接中
        InquiryRecordDto inquiryRecordDto = inquiryApi.getInquiryRecord(getInquiryPref(prescriptionIssueTimeOutCheckEvent));
        if (!Objects.equals(InquiryStatusEnum.INQUIRING.getStatusCode(), inquiryRecordDto.getInquiryStatus())) {
            return;
        }
        // msg 不为空时处理--直接调用接口
        if(StringUtils.isNotBlank(prescriptionIssueTimeOutCheckEvent.getMsg())){
            timeOutCheck(inquiryRecordDto);
            return;
        }
        PrescriptionIssueTimeOutCheckMessage config = prescriptionIssueTimeOutCheckEvent.getNotifyConfigMessage();
        if(ObjectUtil.isEmpty(config)){
            return;
        }
        List<Integer> timeOutConfig = config.getTimeOutConfig();
        Integer currIndex = config.getIndex();
        // 新逻辑
        if(currIndex <= timeOutConfig.size()-1){
            // 推送自动抢单成功通知消息
            doctorImService.batchPushNotifyMessage(Collections.singletonList(config.getDoctorPref()), PushContentEnum.ORDER_TIMEOUT , InquiryRecordDto.builder().pref(config.getInquiryPref()).build());
            int invterval = currIndex == timeOutConfig.size()-1 ?
                timeOutConfig.getLast() : timeOutConfig.get(currIndex) - timeOutConfig.get(currIndex+1);
            config.setIndex(currIndex+1);
            prescriptionIssueTimeOutCheckProducer.sendMessage(
                PrescriptionIssueTimeOutCheckEvent.builder().notifyConfigMessage(config).build(),
                LocalDateTime.now().plusSeconds(invterval * 60L));
        }
        if(currIndex >= timeOutConfig.size()){
            timeOutCheck(inquiryRecordDto);
        }
    }

    private void timeOutCheck(InquiryRecordDto inquiryRecordDto) {
        prescriptionService.prescriptionIssueTimeOutCheck(inquiryRecordDto);
    }

    /**
     * @Description: 获取问诊单pref
     * @param prescriptionIssueTimeOutCheckEvent
     * @return
     */
    private String getInquiryPref(PrescriptionIssueTimeOutCheckEvent prescriptionIssueTimeOutCheckEvent)  {
        if(StringUtils.isNotBlank(prescriptionIssueTimeOutCheckEvent.getMsg())){
            return prescriptionIssueTimeOutCheckEvent.getMsg();
        }
        PrescriptionIssueTimeOutCheckMessage notifyConfigMessage = prescriptionIssueTimeOutCheckEvent.getNotifyConfigMessage();
        if(ObjectUtil.isNotEmpty(notifyConfigMessage)){
            return notifyConfigMessage.getInquiryPref();
        }
        return null;
    }
}
