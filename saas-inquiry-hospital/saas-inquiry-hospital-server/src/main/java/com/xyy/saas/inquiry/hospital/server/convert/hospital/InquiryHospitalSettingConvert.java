package com.xyy.saas.inquiry.hospital.server.convert.hospital;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalSettingDto;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalSettingExtDto;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalSpecificPrescriptionCaDto;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalSpecificPrescriptionTemplateDto;
import com.xyy.saas.inquiry.hospital.enums.HospitalSettingTypeEnum;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalSettingVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalSpecificPrescriptionCaVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalSpecificPrescriptionTemplateVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalSettingDO;
import com.xyy.saas.inquiry.pojo.HospitalDeptDto.Dept;
import jakarta.annotation.Nonnull;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author:chenxiaoyi
 * @Date:2024/09/30 10:53
 */
@Mapper
public interface InquiryHospitalSettingConvert {

    InquiryHospitalSettingConvert INSTANCE = Mappers.getMapper(InquiryHospitalSettingConvert.class);

    default List<InquiryHospitalSettingDO> saveReqVO2SettingDOs(InquiryHospitalSaveReqVO saveReqVO, Map<Integer, InquiryHospitalSettingDO> hospitalSettingMap) {
        InquiryHospitalSettingVO settingVO = Optional.ofNullable(saveReqVO.getSetting()).orElseGet(InquiryHospitalSettingVO::new);
        return Stream.concat(
            // 枚举不存在的配置类型
            hospitalSettingMap.keySet().stream().filter(type -> HospitalSettingTypeEnum.fromType(type) == null)
                .map(type -> {
                    InquiryHospitalSettingDO delSettingDo = hospitalSettingMap.get(type);
                    delSettingDo.setDeleted(true);
                    return delSettingDo;
                }),
            // 枚举对应的配置类型
            Arrays.stream(HospitalSettingTypeEnum.values()).map(hsEnum -> {
                InquiryHospitalSettingDO setting = Optional.ofNullable(hospitalSettingMap.get(hsEnum.getType())).orElseGet(InquiryHospitalSettingDO::new);
                setting.setDeleted(false);
                setting.setHospitalId(saveReqVO.getId())
                    .setParamType(hsEnum.getType())
                    .setParamName(hsEnum.getField())
                    .setDescription(hsEnum.getDescription());
                return switch (hsEnum) {
                    case DEFAULT_INQUIRY_WESTERN_MEDICINE_DEPT -> setting.setParamValue(getDefaultDeptPrefList(settingVO.getDefaultInquiryWesternMedicineDept()).stream().map(String::valueOf).collect(Collectors.joining(",")))
                        .setterExt(settingVO::getDefaultInquiryWesternMedicineDept, true);
                    case DEFAULT_INQUIRY_CHINESE_MEDICINE_DEPT -> setting.setParamValue(getDefaultDeptPrefList(settingVO.getDefaultInquiryChineseMedicineDept()).stream().map(String::valueOf).collect(Collectors.joining(",")))
                        .setterExt(settingVO::getDefaultInquiryChineseMedicineDept, true);
                    case DEFAULT_WESTERN_PRESCRIPTION_TEMPLATE -> setting.setterParamValue(settingVO::getDefaultWesternPrescriptionTemplate, true);
                    case DEFAULT_CHINESE_PRESCRIPTION_TEMPLATE -> setting.setterParamValue(settingVO::getDefaultChinesePrescriptionTemplate, true);
                    case SPECIAL_PRESCRIPTION_TEMPLATES -> setting.setParamValue(getSpecialPrescriptionTemplateIdList(settingVO).stream().map(String::valueOf).collect(Collectors.joining(",")))
                        .setterExt(() -> settingVO.getExtend().getSpecificPrescriptionTemplates(), true);
                    case SPECIAL_PRESCRIPTION_CA -> setting.setParamValue(getSpecialPrescriptionCaList(settingVO).stream().map(String::valueOf).collect(Collectors.joining(",")))
                        .setterExt(() -> settingVO.getExtend().getSpecificPrescriptionCas(), true);
                };
            })
        ).filter(s -> StringUtils.isNotBlank(s.getParamValue())).toList();
    }

    private List<Long> getSpecialPrescriptionTemplateIdList(@Nonnull InquiryHospitalSettingVO settingVO) {
        if (settingVO.getExtend() == null || CollUtil.isEmpty(settingVO.getExtend().getSpecificPrescriptionTemplates())) {
            return List.of();
        }
        return settingVO.getExtend().getSpecificPrescriptionTemplates()
            .stream().map(InquiryHospitalSpecificPrescriptionTemplateVO::getPrescriptionTemplateId)
            .filter(Objects::nonNull).toList();
    }

    private List<String> getDefaultDeptPrefList(List<Dept> deptList) {
        if (CollUtil.isEmpty(deptList)) {
            return List.of();
        }
        return deptList.stream().map(Dept::getDeptPref).filter(Objects::nonNull).toList();
    }


    private List<Integer> getSpecialPrescriptionCaList(@Nonnull InquiryHospitalSettingVO settingVO) {
        if (settingVO.getExtend() == null || CollUtil.isEmpty(settingVO.getExtend().getSpecificPrescriptionCas())) {
            return List.of();
        }
        return settingVO.getExtend().getSpecificPrescriptionCas()
            .stream().map(InquiryHospitalSpecificPrescriptionCaVO::getSignaturePlatform)
            .filter(Objects::nonNull).toList();
    }

    default InquiryHospitalRespDto convertDO2DtoWithSetting(InquiryHospitalDO hospitalDO, Map<Integer, InquiryHospitalSettingDO> hospitalSettingMap) {
        InquiryHospitalRespDto respDto = InquiryHospitalConvert.INSTANCE.convertDo2Dto(hospitalDO);
        InquiryHospitalSettingDto settingDto = new InquiryHospitalSettingDto();
        settingDto.setExtend(new InquiryHospitalSettingExtDto());

        respDto.setSetting(settingDto);
        // 枚举对应的配置类型
        Arrays.stream(HospitalSettingTypeEnum.values()).forEach(hsEnum -> {
            InquiryHospitalSettingDO setting = Optional.ofNullable(hospitalSettingMap.get(hsEnum.getType())).orElseGet(InquiryHospitalSettingDO::new);
            switch (hsEnum) {
                case DEFAULT_INQUIRY_WESTERN_MEDICINE_DEPT -> settingDto.setDefaultInquiryWesternMedicineDept(JsonUtils.parseArray(setting.getExt(), Dept.class));
                case DEFAULT_INQUIRY_CHINESE_MEDICINE_DEPT -> settingDto.setDefaultInquiryChineseMedicineDept(JsonUtils.parseArray(setting.getExt(), Dept.class));
                case DEFAULT_WESTERN_PRESCRIPTION_TEMPLATE -> settingDto.setDefaultWesternPrescriptionTemplate(NumberUtil.parseLong(setting.getParamValue(), null));
                case DEFAULT_CHINESE_PRESCRIPTION_TEMPLATE -> settingDto.setDefaultChinesePrescriptionTemplate(NumberUtil.parseLong(setting.getParamValue(), null));
                case SPECIAL_PRESCRIPTION_TEMPLATES -> settingDto.getExtend().setSpecificPrescriptionTemplates(JsonUtils.parseArray(setting.getExt(), InquiryHospitalSpecificPrescriptionTemplateDto.class));
                case SPECIAL_PRESCRIPTION_CA -> settingDto.getExtend().setSpecificPrescriptionCas(JsonUtils.parseArray(setting.getExt(), InquiryHospitalSpecificPrescriptionCaDto.class));
            }
        });
        return respDto;
    }
}
