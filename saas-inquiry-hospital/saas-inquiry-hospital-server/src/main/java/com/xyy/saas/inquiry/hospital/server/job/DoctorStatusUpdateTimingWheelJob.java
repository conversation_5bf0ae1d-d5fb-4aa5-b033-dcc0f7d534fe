package com.xyy.saas.inquiry.hospital.server.job;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.quartz.core.handler.JobHandler;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.hospital.server.dal.redis.doctor.DoctorRedisDao;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorService;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalService;
import io.netty.util.HashedWheelTimer;
import java.time.LocalTime;
import java.time.ZonedDateTime;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import io.netty.util.Timeout;
import io.netty.util.TimerTask;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;

/**
 * @Desc 医生接诊停诊任务
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2025/1/22 下午5:23
 */
@Slf4j
public class DoctorStatusUpdateTimingWheelJob implements JobHandler {

    private static final int WHEEL_SIZE = 24 * 60; // 分钟槽的数量

    // private TimerWheel timerWheel = new TimerWheel(1000, WHEEL_SIZE);

    //key=online-status-slot:slotId    value(List)= doctorId1,doctorId2,doctorId3


    private final HashedWheelTimer timer = new HashedWheelTimer(1, TimeUnit.MINUTES);

    // @Autowired
    // public DoctorStatusUpdateTimingWheelJob(RedisDistributedLock redisDistributedLock) {
    //     this.redisDistributedLock = redisDistributedLock;
    // }

    @Resource //医院服务
    private InquiryHospitalService inquiryHospitalService;

    @Resource //医生服务
    private InquiryDoctorService inquiryDoctorService;

    @Resource
    private DoctorRedisDao doctorRedisDao;


    //在启动服务的时候，初始化医生的排班信息，并定时更新医生状态
    public void initWheelTimerData() {
        List<InquiryHospitalRespDto> allHospitals = inquiryHospitalService.getInquiryHospitalAllList();
        if (CollUtil.isEmpty(allHospitals)) {
            return;
        }
        for (InquiryHospitalRespDto hospital : allHospitals) {
            // 医院停用，不需要初始化医生状态
            if (hospital.getDisable() == 1) {
                continue;
            }
            // List<String> hospitailDoctorList = doctorRedisDao.getHospitalDoctorAutoInquiryKeyList(hospital.getPref());
            //医生出停诊时间段,先写个假数据,eg:doctorMap.put("doctor1", Arrays.asList("08:00", "12:00", "14:00", "18:00"));
            //TODO 从数据库中查询医生的出诊时间段
            Map<String, List<String>> doctorMap = new HashMap<>();
            doctorMap.put("doctor1", Arrays.asList("08:00", "12:00", "14:00", "18:00"));

            if (doctorMap.isEmpty()) {
                continue;
            }
            for (Map.Entry<String, List<String>> entry : doctorMap.entrySet()) {
                buildWheelTimerRedisData(entry.getKey(), entry.getValue());
            }
        }
    }


    /*
     * 构建医生的时间轮数据
     */
    private void buildWheelTimerRedisData(String doctorId, List<String> inquiryTimeRanges) {
        //inquiryTimeRanges是医生的出诊时间段，一定是偶数个，下标为奇数的元素是开始时间，偶数的元素是结束时间，
        // eg:Arrays.asList("08:00", "12:00", "14:00", "18:00"),可能有多个时间段
        //先检查时间段是否合法
        if (inquiryTimeRanges.size() % 2 != 0) {
            log.error("医生出诊时间段配置错误，时间段数量必须为偶数，doctorId={}, inquiryTimeRanges={}", doctorId, inquiryTimeRanges);
            return;
        }
        for (int i = 0; i < inquiryTimeRanges.size(); i++) {
            LocalTime start = LocalTime.parse(inquiryTimeRanges.get(i));
            LocalTime end = LocalTime.parse(inquiryTimeRanges.get(++i));
            //start和end都需要转换为分钟数
            int startMinute = start.getHour() * 60 + start.getMinute();
            int endMinute = end.getHour() * 60 + end.getMinute();
            //在线状态的key格式为online-startMinute:[doctorId1,doctorId2,doctorId3]
            // doctorRedisDao.addDoctorOnline(doctorId, startMinute);
            //离线状态的key格式为offline-startMinute:[doctorId1,doctorId2,doctorId3]
            // doctorRedisDao.addDoctorOffline(doctorId, endMinute);
        }

        // 1.构建时间轮数据
        // 2.存储到redis中
    }

    private void updateDoctorStatus(String doctorId, List<String> newInquiryTimeRanges, List<String> oldInquiryTimeRanges) {
        // 更新医生的状态
        //找到之前的时间段，删除之前的时间段，循环医生List移除
        //找到新的时间段，添加新的时间段
        // ...
    }


    // private class DoctorStatusUpdateTask implements TimerTask {
    //     private final String doctorName;
    //     private final boolean isAvailable;
    //
    //     public DoctorStatusUpdateTask(String doctorName, boolean isAvailable) {
    //         this.doctorName = doctorName;
    //         this.isAvailable = isAvailable;
    //     }
    //
    //     @Override
    //     public void run(Timeout timeout) throws Exception {
    //         updateDoctorStatus(doctorName, isAvailable);
    //         // 重新调度下一个周期的任务
    //         if (isAvailable) {
    //             int hour = ZonedDateTime.now().withHour(12).withMinute(0).getHour();
    //             scheduleDoctorStatusUpdate(doctorName, hour, false);
    //         } else {
    //             scheduleDoctorStatusUpdate(doctorName, ZonedDateTime.now().withHour(14).withMinute(0).getHour(), true);
    //         }
    //     }
    // }


    @Override
    public String execute(String param) throws Exception {
        timer.newTimeout(timeout -> {
            //获取当前的分钟数
            int minute = ZonedDateTime.now().getMinute();
            //Key的格式为online-minute:slotId
            //获取医生的出诊时间段
            // 业务逻辑
        }, 1000, java.util.concurrent.TimeUnit.MILLISECONDS);
        timer.start();

        return "";
    }
}
