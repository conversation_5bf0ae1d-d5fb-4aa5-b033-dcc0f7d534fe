package com.xyy.saas.inquiry.hospital.server.service.medical.impl;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.MEDICAL_INSURANCE_ORDER_NOT_EXISTS;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.hospital.server.controller.admin.medical.vo.MedicalInsuranceOrderPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.medical.vo.MedicalInsuranceOrderSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.medical.MedicalInsuranceOrderDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.medical.MedicalInsuranceOrderMapper;
import com.xyy.saas.inquiry.hospital.server.service.medical.MedicalInsuranceOrderService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 医保订单信息 Service 实现类
 * <p>
 * # medfee_sumamt 医疗费总额 = psn_part_amt个人负担总金额 + fund_pay_sumamt 基金支付总额 + oth_pay其他支出 + hosp_part_amt医院负担金额 # # psn_part_amt个人负担总金额 = acct_pay个人账户支出 + psn_cash_pay个人现金支出 + ？acct_mulaid_pay个人账户共济支付金额？ # # fund_pay_sumamt 基金支付总额 =
 * hifp_pay基本医疗保险统筹基金支出 # +hifmi_pay居民大病保险资金支出、hifob_pay职工大额医疗费用补助基金支出、hifes_pay 企业补充医疗保险基金支出、cvlserv_pay 公务员医疗补助资金支出 # +maf_pay医疗救助基金支出 # +hifdm_pay 伤残人员医疗保障基金支出
 *
 * <AUTHOR>
 */
@Service
@Validated
public class MedicalInsuranceOrderServiceImpl implements MedicalInsuranceOrderService {

    @Resource
    private MedicalInsuranceOrderMapper medicalInsuranceOrderMapper;

    @Override
    public Long createMedicalInsuranceOrder(MedicalInsuranceOrderSaveReqVO createReqVO) {
        // 插入
        MedicalInsuranceOrderDO medicalInsuranceOrder = BeanUtils.toBean(createReqVO, MedicalInsuranceOrderDO.class);
        medicalInsuranceOrderMapper.insert(medicalInsuranceOrder);
        // 返回
        return medicalInsuranceOrder.getId();
    }

    @Override
    public void updateMedicalInsuranceOrder(MedicalInsuranceOrderSaveReqVO updateReqVO) {
        // 校验存在
        validateMedicalInsuranceOrderExists(updateReqVO.getId());
        // 更新
        MedicalInsuranceOrderDO updateObj = BeanUtils.toBean(updateReqVO, MedicalInsuranceOrderDO.class);
        medicalInsuranceOrderMapper.updateById(updateObj);
    }

    @Override
    public void deleteMedicalInsuranceOrder(Long id) {
        // 校验存在
        validateMedicalInsuranceOrderExists(id);
        // 删除
        medicalInsuranceOrderMapper.deleteById(id);
    }

    private void validateMedicalInsuranceOrderExists(Long id) {
        if (medicalInsuranceOrderMapper.selectById(id) == null) {
            throw exception(MEDICAL_INSURANCE_ORDER_NOT_EXISTS);
        }
    }

    @Override
    public MedicalInsuranceOrderDO getMedicalInsuranceOrder(Long id) {
        return medicalInsuranceOrderMapper.selectById(id);
    }

    @Override
    public PageResult<MedicalInsuranceOrderDO> getMedicalInsuranceOrderPage(MedicalInsuranceOrderPageReqVO pageReqVO) {
        return medicalInsuranceOrderMapper.selectPage(pageReqVO);
    }

}