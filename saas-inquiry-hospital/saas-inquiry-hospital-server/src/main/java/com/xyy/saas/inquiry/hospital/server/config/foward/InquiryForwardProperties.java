package com.xyy.saas.inquiry.hospital.server.config.foward;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

/**
 * @Author:chen<PERSON><PERSON>i
 * @Date:2024/11/26 9:32
 */
@ConfigurationProperties(prefix = "inquiry.forward")
@Data
public class InquiryForwardProperties {

    /**
     * 转发服务sign 统一key
     */
    private String sign;
    /**
     * 转发服务domain
     */
    private String domain;

    /**
     * 超时时间 单位 s
     */
    private Integer httpTimeOut;

}
