package com.xyy.saas.inquiry.hospital.server.mq.producer.prescription;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.hospital.server.mq.message.prescription.PrescriptionAutoIssueEvent;
import org.springframework.stereotype.Component;

/**
 * 处方自动开具Producer
 *
 * @Author:ch<PERSON><PERSON><PERSON><PERSON>
 * @Date:2024/12/18 11:25
 */
@Component
@EventBusProducer(
    topic = PrescriptionAutoIssueEvent.TOPIC
)
public class PrescriptionAutoIssueProducer extends EventBusRocketMQTemplate {

}
