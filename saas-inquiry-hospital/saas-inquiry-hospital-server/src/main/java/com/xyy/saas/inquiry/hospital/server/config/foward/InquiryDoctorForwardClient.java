package com.xyy.saas.inquiry.hospital.server.config.foward;

import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.forward.InquiryDoctorForwardRespVO;
import com.xyy.saas.inquiry.hospital.server.service.doctor.dto.DoctorForwardDetailRespDto;
import com.xyy.saas.inquiry.hospital.server.service.doctor.dto.DoctorSyncQueryDto;
import com.xyy.saas.inquiry.pojo.ForwardResult;
import com.xyy.saas.inquiry.pojo.forward.ForwardPageResultDto;
import com.xyy.saas.inquiry.pojo.forward.ForwardPersonInfo;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

/**
 * @Author:chenxiaoyi
 * @Date:2024/11/27 20:41
 */


@HttpExchange(accept = "application/json", contentType = "application/json")
public interface InquiryDoctorForwardClient {

    /**
     * 转发获取医生列表
     *
     * @return ForwardResult
     */
    @PostExchange("/doctor/forward/queryUserDoctorPhysicianLists")
    ForwardResult<ForwardPageResultDto<InquiryDoctorForwardRespVO>> queryUserDoctorPhysicianLists(@RequestBody DoctorSyncQueryDto dto);


    /**
     * 转发获取医生同步详情 + CA
     *
     * @return ForwardResult
     */
    @GetExchange("/doctor/forward/syncUserDoctorPhysician")
    ForwardResult<DoctorForwardDetailRespDto> syncUserDoctorPhysician(@RequestParam String guid);

    /**
     * 查询旧系统用户CA信息
     *
     * @return ForwardResult
     */
    @GetExchange("/doctor/forward/queryUserCaInfo")
    ForwardResult<ForwardPersonInfo> queryUserCaInfo(@RequestParam String mobile);

}
