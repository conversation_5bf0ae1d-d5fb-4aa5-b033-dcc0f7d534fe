package com.xyy.saas.inquiry.hospital.server.mq.message.prescription.dto;

import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Builder.Default;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.util.List;

/**
 * @Author: xucao
 * @DateTime: 2025/5/26 19:44
 * @Description: 医生接诊超时检查消息
 **/
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class PrescriptionIssueTimeOutCheckMessage implements Serializable {
    private String inquiryPref;

    private String doctorPref;

    private List<Integer> timeOutConfig;

    @Builder.Default
    private Integer index = 0;
}
