package com.xyy.saas.inquiry.hospital.server.service.doctor.chain;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import com.alibaba.fastjson2.JSON;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.constant.InquiryConstant;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.hospital.server.dal.redis.doctor.DoctorRedisDao;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.util.MathUtil;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/12/25 14:02
 * @Description: 问诊派单医生过滤
 * @see GrabFirstFilterChain previous（抢单优先模式过滤--正常不会走）
 * @see AutoGrabCheckChain next（自动抢单调度检查）
 */
@Component
@Slf4j
public class InquirySendFilterChain extends DoctorFilterChain {

    @Resource
    private ConfigApi configApi;

    @Resource
    private DoctorRedisDao doctorRedisDao;

    @Override
    @TraceNode(node = TraceNodeEnum.DOCTOR_FILTER_SEND_NUM , prefLocation = "inquiryDto.pref")
    public void filter(InquiryRecordDto inquiryDto, List<String> doctorList) {
        log.info("问诊单号：{},问诊派单医生数量过滤，医生列表：{}", inquiryDto.getPref(),JSON.toJSONString(doctorList));
        // 自动开方默认只选取一个医生派单，非自动开方则选取配置中指定数量的医生进行派单
        int sendNum = ObjectUtil.equals(AutoInquiryEnum.YES.getCode(), inquiryDto.getAutoInquiry()) ? 1 : MathUtil.formatNumberWithDefault(configApi.getConfigValueByKey(InquiryConstant.INQUIRY_DOCTOR_SEND_NUM), 3);
        if (doctorList.size() <= sendNum) {
            return;
        }
        // 存放符合条件的医生
        List<String> result = new ArrayList<>();
        // 执行过滤
        for (int i = 0; i < sendNum; i++) {
            // 获取当前队列发号坐标
            Integer currIdxSeed = doctorRedisDao.getNextAtomicIntNumber(inquiryDto, 100000000L);
            int idx = currIdxSeed % doctorList.size();
            result.add(doctorList.get(idx));
        }
        // 移出所有不在派单医生列表中的医生
        doctorList.removeIf(doctor -> !result.contains(doctor));
    }
}
