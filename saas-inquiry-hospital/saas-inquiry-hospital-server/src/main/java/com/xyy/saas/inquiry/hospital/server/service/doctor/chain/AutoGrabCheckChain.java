package com.xyy.saas.inquiry.hospital.server.service.doctor.chain;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.constant.InquiryConstant;
import com.xyy.saas.inquiry.enums.inquiry.DistributeModelEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.hospital.server.dal.redis.doctor.DoctorRedisDao;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.util.MathUtil;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @Author: xucao
 * @Date: 2025/01/07 14:05
 * @Description: 在派单医生过滤之后，进行自动派单检查
 * @see InquirySendFilterChain previous（派单医生过滤）
 * @see AutoVideoChooseChain next（视频自动问诊医生录屏选取）
 */
@Component
@Slf4j
public class AutoGrabCheckChain extends DoctorFilterChain {

    @Resource
    private DoctorRedisDao doctorRedisDao;

    @Resource
    private ConfigApi configApi;

    @Override
    @TraceNode(node = TraceNodeEnum.DOCTOR_FILTER_AUTOGRABCHECK , prefLocation = "inquiryDto.pref")
    public void filter(InquiryRecordDto inquiryDto, List<String> doctorList) {
        log.info("问诊单pref:{},doctor:{}，自动派单检查", inquiryDto.getPref(), JSON.toJSONString(doctorList));
        // 自动开方  或者  非图文问诊 直接返回
        if (inquiryDto.isAutoInquiry() || ObjectUtil.notEqual(InquiryWayTypeEnum.TEXT.getCode(), inquiryDto.getInquiryWayType())) {
            return;
        }
        log.info("判定是否自动抢单，当前医生列表：{}", JSON.toJSONString(doctorList));
        // 查询开启自动抢单医生列表
        List<String> grabDoctorList = doctorRedisDao.getGrabDoctorList(inquiryDto.getEnv());
        log.info("开启自动抢单的医生列表：{}", JSON.toJSONString(grabDoctorList));
        // 自动抢单医生列表为空，或者待调度的医生列表中没有任何一个医生是自动抢单的，则直接返回。
        if (CollectionUtils.isEmpty(grabDoctorList) || doctorList.stream().noneMatch(grabDoctorList::contains)) {
            log.info("自动抢单医生列表为空，或者待调度的医生列表中没有任何一个医生是自动抢单的，直接返回。");
            return;
        }
        //取交集
        grabDoctorList.retainAll(doctorList);
        log.info("当前取交集获取到的可自动抢单的医生：{}", JSON.toJSONString(grabDoctorList));
        // 过滤掉当前医生列表中，在自动抢单列表中的医生数量大于等于自动抢单数量限制的医生
        grabDoctorList.removeIf(doctor -> doctorRedisDao.getDoctorCurrentAutoGrabList(doctor, inquiryDto.getEnv()).size() >= MathUtil.formatNumberWithDefault(configApi.getConfigValueByKey(InquiryConstant.INQUIRY_DOCTOR_AUTOGRAB_NUM),2));
        if (CollectionUtils.isEmpty(grabDoctorList)) {
            log.info("过滤掉当前医生列表中，在自动抢单列表中的医生数量大于等于自动抢单数量限制的医生后，没有自动抢单的医生了");
            return;
        }
        // doctorList中有医生在自动抢单列表中,取交集，doctorList仅保留自动抢单的医生
        doctorList.retainAll(grabDoctorList);
        // 自动抢单只取一个医生
        doctorList = Collections.singletonList(doctorList.getFirst());
        // 指定问诊单为自动抢单
        inquiryDto.setDistributeModelEnum(DistributeModelEnum.AUTO_GRAB_DISTRIBUTE);
        log.info("问诊单pref:{},doctor:{}，指定为自动抢单", inquiryDto.getPref(), doctorList.getFirst());
    }

}
