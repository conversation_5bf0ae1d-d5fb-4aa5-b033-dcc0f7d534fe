package com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorAuditedRecordPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorAuditedRecordDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 医生审核记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DoctorAuditedRecordMapper extends BaseMapperX<DoctorAuditedRecordDO> {

    default PageResult<DoctorAuditedRecordDO> selectPage(DoctorAuditedRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DoctorAuditedRecordDO>()
            .eqIfPresent(DoctorAuditedRecordDO::getDoctorId, reqVO.getDoctorId())
            .likeIfPresent(DoctorAuditedRecordDO::getAuditorName, reqVO.getAuditorName())
            .eqIfPresent(DoctorAuditedRecordDO::getAuditorId, reqVO.getAuditorId())
            .eqIfPresent(DoctorAuditedRecordDO::getAuditResult, reqVO.getAuditStatus())
            .betweenIfPresent(DoctorAuditedRecordDO::getAuditTime, reqVO.getAuditTime())
            .betweenIfPresent(DoctorAuditedRecordDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(DoctorAuditedRecordDO::getId));
    }

}