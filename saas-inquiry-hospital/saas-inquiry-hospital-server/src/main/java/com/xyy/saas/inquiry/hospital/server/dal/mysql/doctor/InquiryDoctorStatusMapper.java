package com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorStatusPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorStatusDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 医生出诊状态关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryDoctorStatusMapper extends BaseMapperX<InquiryDoctorStatusDO> {

    default PageResult<InquiryDoctorStatusDO> selectPage(InquiryDoctorStatusPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InquiryDoctorStatusDO>()
            .eqIfPresent(InquiryDoctorStatusDO::getDoctorPref, reqVO.getDoctorPref())
            .eqIfPresent(InquiryDoctorStatusDO::getInquiryType, reqVO.getInquiryType())
            .eqIfPresent(InquiryDoctorStatusDO::getInquiryWayType, reqVO.getInquiryWayType())
            .eqIfPresent(InquiryDoctorStatusDO::getInquiryBizType, reqVO.getInquiryBizType())
            .eqIfPresent(InquiryDoctorStatusDO::getStatus, reqVO.getStatus())
            .betweenIfPresent(InquiryDoctorStatusDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(InquiryDoctorStatusDO::getId));
    }

    default List<InquiryDoctorStatusDO> selectList(InquiryDoctorStatusPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<InquiryDoctorStatusDO>()
            .eqIfPresent(InquiryDoctorStatusDO::getDoctorPref, reqVO.getDoctorPref())
            .eqIfPresent(InquiryDoctorStatusDO::getInquiryWayType, reqVO.getInquiryWayType())
            .eqIfPresent(InquiryDoctorStatusDO::getInquiryType, reqVO.getInquiryType())
            .eqIfPresent(InquiryDoctorStatusDO::getInquiryBizType, reqVO.getInquiryBizType())
            .eqIfPresent(InquiryDoctorStatusDO::getStatus, reqVO.getStatus())
            .betweenIfPresent(InquiryDoctorStatusDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(InquiryDoctorStatusDO::getId));
    }

    /**
     * 根据医生+问诊类型删除
     *
     * @param doctorPref  医生编码
     * @param inquiryType 问诊类型
     */
    void deleteByDoctorPrefType(@Param("doctorPref") String doctorPref, @Param("inquiryType") Integer inquiryType);

}