package com.xyy.saas.inquiry.hospital.server.api.prescription;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionApi;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionFlushQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionUpdateDTO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.convert.prescription.InquiryPrescriptionConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDO;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalService;
import com.xyy.saas.inquiry.hospital.server.service.prescription.InquiryPrescriptionService;
import com.xyy.saas.inquiry.pojo.patient.PatientSimpleDTO;
import jakarta.annotation.Resource;
import java.util.Optional;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

/**
 * @Author:chenxiaoyi
 * @Date:2024/12/03 19:38
 */
@DubboService
@Validated
public class InquiryPrescriptionApiImpl implements InquiryPrescriptionApi {

    @Resource
    private InquiryPrescriptionService inquiryPrescriptionService;

    @Resource
    private InquiryHospitalService inquiryHospitalService;

    @Override
    public InquiryPrescriptionRespDTO getInquiryPrescription(InquiryPrescriptionQueryDTO queryDTO) {
        InquiryPrescriptionRespVO prescriptionRespVO = inquiryPrescriptionService.queryByCondition(queryDTO);
        return getInquiryPrescriptionRespDTO(prescriptionRespVO);
    }

    @Override
    public InquiryPrescriptionRespDTO getInquiryPrescriptionWithPdf(InquiryPrescriptionQueryDTO queryDTO) {
        InquiryPrescriptionRespVO prescriptionRespVO = inquiryPrescriptionService.getInquiryPrescriptionWithPdf(queryDTO);
        return getInquiryPrescriptionRespDTO(prescriptionRespVO);
    }

    private InquiryPrescriptionRespDTO getInquiryPrescriptionRespDTO(InquiryPrescriptionRespVO prescriptionRespVO) {
        if (prescriptionRespVO == null) {
            return null;
        }
        // 填充医院信息
        Optional.ofNullable(inquiryHospitalService.getInquiryHospitalInfo(prescriptionRespVO.getHospitalPref())).ifPresent(inquiryHospitalDO -> {
            prescriptionRespVO.setHospitalName(inquiryHospitalDO.getName());
            prescriptionRespVO.setInstitutionCode(inquiryHospitalDO.getInstitutionCode());
        });

        return InquiryPrescriptionConvert.INSTANCE.convertVO2DTO(prescriptionRespVO);
    }

    @Override
    public IPage<PatientSimpleDTO> getPrescriptionPatientList(InquiryPrescriptionQueryDTO queryDTO) {
        return inquiryPrescriptionService.getPrescriptionPatientList(queryDTO);
    }

    @Override
    public void updateInquiryPrescription(InquiryPrescriptionUpdateDTO prescriptionUpdateDTO) {
        inquiryPrescriptionService.updateInquiryPrescription(InquiryPrescriptionConvert.INSTANCE.convertDTO2VO(prescriptionUpdateDTO));
    }

    @Override
    public Long getPrescriptionCount(InquiryPrescriptionQueryDTO queryDTO) {
        return inquiryPrescriptionService.getPrescriptionCount(InquiryPrescriptionConvert.INSTANCE.convertQueryVO(queryDTO));
    }


    @Override
    public boolean distributePharmacist(Long id, Long userId) {
        return inquiryPrescriptionService.distributePharmacist(id, userId);
    }

    @Override
    public boolean releasePharmacist(Long id, Long userId) {
        return inquiryPrescriptionService.releasePharmacist(id, userId);
    }

    @Override
    public void postProcessIssuesPrescription(String pref) {
        inquiryPrescriptionService.postProcessIssuesPrescription(pref);
    }

    @Override
    public PageResult<InquiryPrescriptionRespDTO> getInquiryPrescriptionPage(InquiryPrescriptionQueryDTO inquiryPrescriptionQueryDTO) {

        InquiryPrescriptionPageReqVO inquiryPrescriptionPageReqVO = new InquiryPrescriptionPageReqVO();
        inquiryPrescriptionPageReqVO.setQueryScene(inquiryPrescriptionQueryDTO.getQueryScene());
        inquiryPrescriptionPageReqVO.setPageNo(inquiryPrescriptionQueryDTO.getPageNum());
        inquiryPrescriptionPageReqVO.setPageSize(inquiryPrescriptionQueryDTO.getPageSize());
        inquiryPrescriptionPageReqVO.setTenantId(inquiryPrescriptionQueryDTO.getTenantId());
        inquiryPrescriptionPageReqVO.setOutPrescriptionTime(inquiryPrescriptionQueryDTO.getOutPrescriptionTime());
        PageResult<InquiryPrescriptionRespVO> voPageResult = inquiryPrescriptionService.getInquiryPrescriptionPage(inquiryPrescriptionPageReqVO);

        return InquiryPrescriptionConvert.INSTANCE.convertVO2DTOPageResult(voPageResult);
    }


    @Override
    public PageResult<InquiryPrescriptionRespDTO> getFlushOfflinePrescriptionPage(InquiryPrescriptionFlushQueryDTO flushQueryDTO) {

        PageResult<InquiryPrescriptionDO> pageResult = inquiryPrescriptionService.getFlushOfflinePrescriptionPage(flushQueryDTO);

        return InquiryPrescriptionConvert.INSTANCE.convertDO2DTOPageResult(pageResult);
    }


}
