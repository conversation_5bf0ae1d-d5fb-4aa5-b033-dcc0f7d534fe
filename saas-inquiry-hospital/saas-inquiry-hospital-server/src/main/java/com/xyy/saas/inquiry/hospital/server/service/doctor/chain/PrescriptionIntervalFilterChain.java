package com.xyy.saas.inquiry.hospital.server.service.doctor.chain;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson2.JSON;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.constant.RedisKeyConstants;
import com.xyy.saas.inquiry.drugstore.api.option.InquiryOptionConfigApi;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigQueryDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionGlobalConfigRespDto;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.hospital.server.dal.redis.doctor.DoctorRedisDao;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import java.util.List;

/**
 * @Author: xucao
 * @Date: 2024/12/24 18:54
 * @Description: 开方时间间隔过滤器
 * @see ProvinceFilterChain next （同省医生过滤器）
 */
@Component
@Slf4j
public class PrescriptionIntervalFilterChain extends DoctorFilterChain {

    @Resource
    private DoctorRedisDao doctorRedisDao;


    @Resource
    private InquiryOptionConfigApi inquiryOptionConfigApi;

    @Override
    @TraceNode(node = TraceNodeEnum.DOCTOR_FILTER_INTERVAL , prefLocation = "inquiryDto.pref")
    public void filter(InquiryRecordDto inquiryDto, List<String> doctorList) {
        log.info("问诊单号：{},自动开方时间间隔过滤器，医生列表：{}", inquiryDto.getPref(),JSON.toJSONString(doctorList));
        // 真人问诊不进行开方间隔过滤
        if (ObjectUtil.equals(inquiryDto.getAutoInquiry(), AutoInquiryEnum.NO.getCode())) {
            return;
        }
        // 获取缓存配置是否开启了自动开方间隔配置
        InquiryOptionGlobalConfigRespDto globalConfig = inquiryOptionConfigApi.getInquiryOptionGlobalConfig(new InquiryOptionConfigQueryDto());
        // 未配置开方间隔直接返回
        if (ObjectUtils.isEmpty(globalConfig.getPresSignatureUseInterval())) {
            return;
        }
        // 生成开方时间间隔key
        List<String> intervalDoctorKeys = doctorList.stream().map(RedisKeyConstants::getDoctorIntervalKey).toList();
        // 获取处于间隔期内的医生
        List<String> intervalDoctorList = doctorRedisDao.getIntervalDoctorList(intervalDoctorKeys);
        // 从全量医生中过滤出处于间隔期内的医生
        doctorList.removeAll(intervalDoctorList);
    }
}
