package com.xyy.saas.inquiry.hospital.server.service.hospital;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.convert.hospital.InquiryHospitalDepartmentRelationConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDepartmentDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDepartmentRelationDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital.InquiryHospitalDepartmentMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital.InquiryHospitalDepartmentRelationMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital.InquiryHospitalMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;

import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.*;


/**
 * 医院科室信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InquiryHospitalDepartmentRelationServiceImpl implements InquiryHospitalDepartmentRelationService {

    @Resource
    private InquiryHospitalDepartmentRelationMapper inquiryHospitalDepartmentRelationMapper;

    @Resource
    private InquiryHospitalDepartmentMapper inquiryHospitalDepartmentMapper;

    @Resource
    private InquiryHospitalMapper inquiryHospitalMapper;

    @Override
    public Long createInquiryHospitalDepartmentRelation(InquiryHospitalDepartmentRelationSaveReqVO createReqVO) {
        //查询医院信息
        InquiryHospitalDO hospitalDO = inquiryHospitalMapper.selectOne(InquiryHospitalDO::getPref, createReqVO.getHospitalPref());
        if (ObjectUtils.isEmpty(hospitalDO)) {
            throw exception(INQUIRY_HOSPITAL_NOT_EXISTS);
        }
        //检验存在
        validateInquiryHospitalDepartmentRelationExists(createReqVO.getDeptId(), hospitalDO.getId());
        //查询科室信息
        InquiryHospitalDepartmentDO departmentDO = inquiryHospitalDepartmentMapper.selectById(createReqVO.getDeptId());
        if (ObjectUtils.isEmpty(departmentDO)) {
            throw exception(INQUIRY_HOSPITAL_DEPARTMENT_NOT_EXISTS);
        }
        // 插入
        InquiryHospitalDepartmentRelationDO inquiryHospitalDepartmentRelation = InquiryHospitalDepartmentRelationDO.builder()
            .hospitalId(hospitalDO.getId())
            .hospitalPref(hospitalDO.getPref())
            .deptPref(departmentDO.getPref())
            .deptName(departmentDO.getDeptName())
            .deptParentId(departmentDO.getDeptParentId())
            .deptId(createReqVO.getDeptId())
            .build();
        inquiryHospitalDepartmentRelationMapper.insert(inquiryHospitalDepartmentRelation);
        // 返回
        return inquiryHospitalDepartmentRelation.getId();
    }


    @Override
    public void deleteInquiryHospitalDepartmentRelation(Long id) {
        // 校验存在
        validateInquiryHospitalDepartmentRelationExists(id);
        // 删除
        inquiryHospitalDepartmentRelationMapper.deleteById(id);
    }

    private void validateInquiryHospitalDepartmentRelationExists(Long id) {
        if (inquiryHospitalDepartmentRelationMapper.selectById(id) == null) {
            throw exception(INQUIRY_HOSPITAL_DEPARTMENT_RELATION_NOT_EXISTS);
        }
    }

    private void validateInquiryHospitalDepartmentRelationExists(Long deptId, Long hospitalId) {
        PageResult<InquiryHospitalDepartmentRelationDO> pageResult = inquiryHospitalDepartmentRelationMapper.selectPage(InquiryHospitalDepartmentRelationPageReqVO.builder().deptId(deptId).hospitalId(hospitalId).build());
        if (pageResult.getTotal() > 0) {
            throw exception(INQUIRY_HOSPITAL_DEPARTMENT_RELATION_EXISTS);
        }
    }

    @Override
    public InquiryHospitalDepartmentRelationDO getInquiryHospitalDepartmentRelation(Long id) {
        return inquiryHospitalDepartmentRelationMapper.selectById(id);
    }

    @Override
    public PageResult<InquiryHospitalDepartmentRelationDO> getInquiryHospitalDepartmentRelationPage(InquiryHospitalDepartmentRelationPageReqVO pageReqVO) {
        return inquiryHospitalDepartmentRelationMapper.selectPage(pageReqVO);
    }


    /**
     * 医院科室维护
     *
     * @param createReqVO 创建请求
     * @return Boolean
     */
    @Override
    @Transactional
    public Boolean editHospitalDept(InquiryHospitalDepartmentRelationSaveReqVO createReqVO) {
        //查询医院信息
        InquiryHospitalDO hospitalDO = inquiryHospitalMapper.selectOne(InquiryHospitalDO::getPref, createReqVO.getHospitalPref());
        if (ObjectUtils.isEmpty(hospitalDO)) {
            throw exception(INQUIRY_HOSPITAL_NOT_EXISTS);
        }
        //查询当前医院科室
        List<InquiryHospitalDepartmentRelationDO> deptList = inquiryHospitalDepartmentRelationMapper.selectList(InquiryHospitalDepartmentRelationDO::getHospitalPref, hospitalDO.getPref());
        //过滤掉当前医院已维护的科室
        List<Long> createDeptList = createReqVO.getDeptIds().stream().filter(deptId -> deptList.stream().noneMatch(dept -> dept.getDeptId().equals(deptId))).toList();
        //查询字典
        List<InquiryHospitalDepartmentDO> list = inquiryHospitalDepartmentMapper.selectList(InquiryHospitalDepartmentDO::getId, createDeptList);
        List<InquiryHospitalDepartmentRelationDO> relationList = list.stream()
                .map(departmentDO -> InquiryHospitalDepartmentRelationDO.builder()
                        .hospitalId(hospitalDO.getId())
                        .hospitalPref(hospitalDO.getPref())
                        .hospitalName(hospitalDO.getName())
                        .deptPref(departmentDO.getPref())
                        .deptName(departmentDO.getDeptName())
                        .deptParentId(departmentDO.getDeptParentId())
                        .deptId(departmentDO.getId())
                        .build())
                .toList();
        return inquiryHospitalDepartmentRelationMapper.insertBatch(relationList);
    }

    /**
     * 根据互联网医院guid查询医院科室信id
     *
     * @param hospitalId 医院id
     * @return 医院科室id集合
     */
    @Override
    public List<Long> getDeptIdByHospitalId(Long hospitalId) {
        return inquiryHospitalDepartmentRelationMapper.selectObjs(new LambdaQueryWrapperX<InquiryHospitalDepartmentRelationDO>()
            .select(InquiryHospitalDepartmentRelationDO::getDeptId)
            .eq(InquiryHospitalDepartmentRelationDO::getHospitalId, hospitalId));
    }

    @Override
    public List<InquiryHospitalDepartmentRelationRespVO> getDeptByHospitalPref(String hospitalPref) {
        return InquiryHospitalDepartmentRelationConvert.INSTANCE.convertDOsTOVOs(inquiryHospitalDepartmentRelationMapper.selectList(InquiryHospitalDepartmentRelationDO::getHospitalPref, hospitalPref));
    }

}