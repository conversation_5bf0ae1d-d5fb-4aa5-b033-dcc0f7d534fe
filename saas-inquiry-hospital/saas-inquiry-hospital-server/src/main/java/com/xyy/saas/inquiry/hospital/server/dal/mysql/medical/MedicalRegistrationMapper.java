package com.xyy.saas.inquiry.hospital.server.dal.mysql.medical;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import java.util.List;
import com.xyy.saas.inquiry.hospital.server.controller.admin.medical.vo.MedicalRegistrationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.medical.MedicalRegistrationDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 医疗就诊登记(挂号) Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MedicalRegistrationMapper extends BaseMapperX<MedicalRegistrationDO> {


    default MedicalRegistrationDO selectOneByCondition(MedicalRegistrationPageReqVO reqVO) {
        return selectOne(getQueryWrapper(reqVO), false);
    }

    default PageResult<MedicalRegistrationDO> selectListByCondition(MedicalRegistrationPageReqVO reqVO) {
        return selectPage(reqVO, getQueryWrapper(reqVO));
    }

    private static LambdaQueryWrapperX<MedicalRegistrationDO> getQueryWrapper(MedicalRegistrationPageReqVO reqVO) {
        return new LambdaQueryWrapperX<MedicalRegistrationDO>()
            .eqIfPresent(MedicalRegistrationDO::getPref, reqVO.getPref())
            .eqIfPresent(MedicalRegistrationDO::getBizId, reqVO.getBizId())
            .eqIfPresent(MedicalRegistrationDO::getBizType, reqVO.getBizType())
            .eqIfPresent(MedicalRegistrationDO::getPatientPref, reqVO.getPatientPref())
            .likeIfPresent(MedicalRegistrationDO::getPatientName, reqVO.getPatientName())
            .eqIfPresent(MedicalRegistrationDO::getPatientMobile, reqVO.getPatientMobile())
            .eqIfPresent(MedicalRegistrationDO::getPatientIdCard, reqVO.getPatientIdCard())
            .eqIfPresent(MedicalRegistrationDO::getBizVisitId, reqVO.getBizVisitId())
            .eqIfPresent(MedicalRegistrationDO::getMedicalVisitId, reqVO.getMedicalVisitId())
            .betweenIfPresent(MedicalRegistrationDO::getMedicalVisitDate, reqVO.getMedicalVisitDate())
            .eqIfPresent(MedicalRegistrationDO::getMedType, reqVO.getMedType())
            .eqIfPresent(MedicalRegistrationDO::getInsuredAreaNo, reqVO.getInsuredAreaNo())
            .eqIfPresent(MedicalRegistrationDO::getTenantAreaNo, reqVO.getTenantAreaNo())
            .eqIfPresent(MedicalRegistrationDO::getPsnNo, reqVO.getPsnNo())
            .eqIfPresent(MedicalRegistrationDO::getIptOtpNo, reqVO.getIptOtpNo())
            .eqIfPresent(MedicalRegistrationDO::getStatus, reqVO.getStatus())
            .eqIfPresent(MedicalRegistrationDO::getDeptPref, reqVO.getDeptPref())
            .likeIfPresent(MedicalRegistrationDO::getDeptName, reqVO.getDeptName())
            .eqIfPresent(MedicalRegistrationDO::getHospitalPref, reqVO.getHospitalPref())
            .likeIfPresent(MedicalRegistrationDO::getHospitalName, reqVO.getHospitalName())
            .eqIfPresent(MedicalRegistrationDO::getAcctUsedFlag, reqVO.getAcctUsedFlag())
            .betweenIfPresent(MedicalRegistrationDO::getBookTime, reqVO.getBookTime())
            .betweenIfPresent(MedicalRegistrationDO::getPlanTime, reqVO.getPlanTime())
            .betweenIfPresent(MedicalRegistrationDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(MedicalRegistrationDO::getId);
    }

}