package com.xyy.saas.inquiry.hospital.server.service.doctor;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorFilingPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorFilingSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorFilingDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.DoctorFilingMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.DOCTOR_FILING_NOT_EXISTS;


/**
 * 医生备案信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DoctorFilingServiceImpl implements DoctorFilingService {

    @Resource
    private DoctorFilingMapper doctorFilingMapper;

    @Override
    public Long createDoctorFiling(DoctorFilingSaveReqVO createReqVO) {
        // 插入
        DoctorFilingDO doctorFiling = BeanUtils.toBean(createReqVO, DoctorFilingDO.class);
        doctorFilingMapper.insert(doctorFiling);
        // 返回
        return doctorFiling.getId();
    }

    @Override
    public void updateDoctorFiling(DoctorFilingSaveReqVO updateReqVO) {
        // 校验存在
        validateDoctorFilingExists(updateReqVO.getId());
        // 更新
        DoctorFilingDO updateObj = BeanUtils.toBean(updateReqVO, DoctorFilingDO.class);
        doctorFilingMapper.updateById(updateObj);
    }

    @Override
    public void deleteDoctorFiling(Long id) {
        // 校验存在
        validateDoctorFilingExists(id);
        // 删除
        doctorFilingMapper.deleteById(id);
    }

    private void validateDoctorFilingExists(Long id) {
        if (doctorFilingMapper.selectById(id) == null) {
            throw exception(DOCTOR_FILING_NOT_EXISTS);
        }
    }

    @Override
    public DoctorFilingDO getDoctorFiling(Long id) {
        return doctorFilingMapper.selectById(id);
    }

    @Override
    public PageResult<DoctorFilingDO> getDoctorFilingPage(DoctorFilingPageReqVO pageReqVO) {
        return doctorFilingMapper.selectPage(pageReqVO);
    }

}