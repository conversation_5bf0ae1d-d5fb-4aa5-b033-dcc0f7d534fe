package com.xyy.saas.inquiry.hospital.server.dal.mysql.clinicalcase;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.hospital.api.clinicalcase.dto.InquiryClinicalCaseQueryDto;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.clinicalcase.InquiryClinicalCaseDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 门诊病例 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryClinicalCaseMapper extends BaseMapperX<InquiryClinicalCaseDO> {

    default List<InquiryClinicalCaseDO> selectList(InquiryClinicalCaseQueryDto reqDto) {
        return selectList(getQueryWrapper(reqDto));
    }


    default InquiryClinicalCaseDO selectOne(InquiryClinicalCaseQueryDto reqDto) {
        return selectOne(getQueryWrapper(reqDto), false);
    }

    private static LambdaQueryWrapperX<InquiryClinicalCaseDO> getQueryWrapper(InquiryClinicalCaseQueryDto reqDto) {
        return new LambdaQueryWrapperX<InquiryClinicalCaseDO>()
            .eqIfPresent(InquiryClinicalCaseDO::getPref, reqDto.getPref())
            .eqIfPresent(InquiryClinicalCaseDO::getInquiryPref, reqDto.getInquiryPref())
            .eqIfPresent(InquiryClinicalCaseDO::getHospitalPref, reqDto.getHospitalPref())
            .eqIfPresent(InquiryClinicalCaseDO::getDoctorPref, reqDto.getDoctorPref())
            .eqIfPresent(InquiryClinicalCaseDO::getDeptPref, reqDto.getDeptPref())
            .eqIfPresent(InquiryClinicalCaseDO::getPatientPref, reqDto.getPatientPref())
            .likeIfPresent(InquiryClinicalCaseDO::getPatientName, reqDto.getPatientName())
            .eqIfPresent(InquiryClinicalCaseDO::getPatientIdCard, reqDto.getPatientIdCard())
            .eqIfPresent(InquiryClinicalCaseDO::getFollowUp, reqDto.getFollowUp())
            .eqIfPresent(InquiryClinicalCaseDO::getMainSymptoms, reqDto.getMainSymptoms())
            .eqIfPresent(InquiryClinicalCaseDO::getMeasures, reqDto.getMeasures())
            .eqIfPresent(InquiryClinicalCaseDO::getObservation, reqDto.getObservation())
            .eqIfPresent(InquiryClinicalCaseDO::getReferral, reqDto.getReferral())
            .eqIfPresent(InquiryClinicalCaseDO::getDiagnosisCode, reqDto.getDiagnosisCode())
            .eqIfPresent(InquiryClinicalCaseDO::getTcmDiagnosisCode, reqDto.getTcmDiagnosisCode())
            .eqIfPresent(InquiryClinicalCaseDO::getTcmSyndromeCode, reqDto.getTcmSyndromeCode())
            .eqIfPresent(InquiryClinicalCaseDO::getTcmTreatmentMethodCode, reqDto.getTcmTreatmentMethodCode())
            .betweenIfPresent(InquiryClinicalCaseDO::getCreateTime, reqDto.getCreateTime())
            .orderByDesc(InquiryClinicalCaseDO::getId);
    }


}