package com.xyy.saas.inquiry.hospital.server.service.medical.impl;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.xyy.saas.inquiry.annotation.InquiryDateType;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.enums.registration.RegistrationStatusEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.hospital.server.controller.admin.medical.vo.MedicalRegistrationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.medical.vo.MedicalRegistrationRespVO;
import com.xyy.saas.inquiry.hospital.server.convert.medical.RegistrationConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.medical.MedicalRegistrationDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.medical.MedicalRegistrationMapper;
import com.xyy.saas.inquiry.hospital.server.service.medical.MedicalRegistrationService;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.hospital.api.medicare.dto.MedicalRegistrationInquiryUpdateDto;
import com.xyy.saas.inquiry.hospital.api.medicare.transmission.MedicalRegistrationTransmissionRespDto;
import com.xyy.saas.inquiry.pojo.medicare.MedicareBaseResp;
import com.xyy.saas.inquiry.pojo.transmitter.his.RegistrationTransmitterDTO;
import com.xyy.saas.transmitter.api.servicepack.TransmissionServicePackApi;
import com.xyy.saas.transmitter.api.transmission.TransmissionApi;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionConfigReqDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import com.xyy.saas.inquiry.util.PrefUtil;
import com.xyy.saas.inquiry.hospital.api.medicare.dto.MedicareRegistrationDto;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;
import java.util.Objects;

/**
 * 医疗就诊登记(挂号) Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class MedicalRegistrationServiceImpl implements MedicalRegistrationService {

    @Resource
    private MedicalRegistrationMapper medicalRegistrationMapper;

    @DubboReference(retries = 0)
    private TransmissionApi transmissionApi;

    @DubboReference
    private TransmissionServicePackApi transmissionServicePackApi;


    @Override
    public Long saveInquiryMedicalRegistration(InquiryRecordDto inquiryDto) {
        return null;
        // // 判断挂号节点 获取需要挂号的医院pref
        // TransmissionConfigReqDTO configReqDTO = TransmissionConfigReqDTO.builder().nodeType(NodeTypeEnum.INTERNET_SUPERVISION_REGISTRATION_FEEDBACK).build();
        // CommonResult<InternetConfigOptionDTO> configItem = transmissionServicePackApi.selectConfigItem(configReqDTO, InternetConfigOptionDTO.class);
        // if (configItem.isError() || configItem.getData() == null || !CollUtil.contains(inquiryDto.getChoiceHospitalList(), configItem.getData().getSupervisionHospitalPref())) {
        //     return null;
        // }
        // // 指定选择的互联网医院
        // inquiryDto.setChoiceHospitalList(CollUtil.newArrayList(configItem.getData().getSupervisionHospitalPref()));
        // log.info("问诊单号：{},医院:{},开始执行问诊登记预约挂号单", inquiryDto.getPref(), configItem.getData().getSupervisionHospitalPref());
        // // 生成系统就诊流水号
        // String registrationPref = PrefUtil.getMedicalRegistrationPref();
        // // 调用三方 获取挂号就诊登记信息数据
        // RegistrationTransmitterDTO transmitterDTO = RegistrationConvert.INSTANCE.convertTransmission(inquiryDto).setRegistrationPref(registrationPref);
        //
        // // 构建保存挂号信息 新增 or 修改
        // MedicalRegistrationDO saveDo = RegistrationConvert.INSTANCE.convertInquirySaveDo(inquiryDto, new MedicalRegistrationTransmissionRespDto().setBizVisitId(registrationPref));
        // MedicalRegistrationDO registrationDO = medicalRegistrationMapper.selectOneByCondition(
        //     MedicalRegistrationPageReqVO.builder().tenantId(inquiryDto.getTenantId()).bizType(BizTypeEnum.HYWZ.getCode()).bizId(saveDo.getBizId()).build());
        // if (registrationDO == null) {
        //     saveDo.setPref(registrationPref);
        //     medicalRegistrationMapper.insert(saveDo);
        // } else {
        //     saveDo.setId(registrationDO.getId());
        //     medicalRegistrationMapper.updateById(saveDo);
        // }
        // // 异步调用
        // CommonResult<MedicalRegistrationTransmissionRespDto> respDTOS = transmissionApi.contractInvoke(TransmissionReqDTO.buildReq(configReqDTO, transmitterDTO).setAsync(true),
        //     MedicalRegistrationTransmissionRespDto.class);
        // log.info("问诊单号：{},医院:{},结束执行问诊登记预约挂号单", inquiryDto.getPref(), configItem.getData().getSupervisionHospitalPref());
        // // if (respDTOS.isError() || respDTOS.getData() == null) {
        // //     throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), respDTOS.getMsg());
        // // }
        // return saveDo.getId();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInquiryMedicalRegistrationStatus(MedicalRegistrationInquiryUpdateDto updateDto) {
        MedicalRegistrationDO registrationDO = medicalRegistrationMapper.selectOneByCondition(MedicalRegistrationPageReqVO.builder().bizType(BizTypeEnum.HYWZ.getCode()).bizId(updateDto.getInquiryPref()).build());
        if (registrationDO == null) {
            return;
        }
        medicalRegistrationMapper.updateById(MedicalRegistrationDO.builder().id(registrationDO.getId()).status(updateDto.getStatus()).build());
        // 判断节点 调用三方 获取挂号就诊登记信息数据
        TransmissionConfigReqDTO configReqDTO = TransmissionConfigReqDTO.builder().tenantId(registrationDO.getTenantId()).nodeType(NodeTypeEnum.HIS_PRESCRIPTION_ORDER_STATUS).build();
        CommonResult<Boolean> businessLogic = transmissionApi.validateBusinessLogic(TransmissionReqDTO.buildReq(configReqDTO, registrationDO));
        if (businessLogic.isSuccess() && businessLogic.getData()) {
            RegistrationTransmitterDTO transmitterDTO = RegistrationConvert.INSTANCE.convertTransmission(updateDto, registrationDO);
            transmissionApi.contractInvoke(TransmissionReqDTO.buildReq(configReqDTO, transmitterDTO), MedicalRegistrationTransmissionRespDto.class);
        }
    }

    @Override
    public MedicalRegistrationRespVO getMedicalRegistrationInfo(BizTypeEnum bizTypeEnum, String bizId) {
        MedicalRegistrationDO registrationDO = medicalRegistrationMapper.selectOneByCondition(MedicalRegistrationPageReqVO.builder().bizType(bizTypeEnum.getCode()).bizId(bizId).build());
        if (registrationDO == null) {
            return null;
        }
        return RegistrationConvert.INSTANCE.convert(registrationDO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveMedicareRegistrationInfo(MedicareRegistrationDto medicareRegistrationDto) {
        try {
            log.info("【医保挂号登记】保存挂号信息，处方编号：{}, 患者身份证：{}", medicareRegistrationDto.getBizId(), medicareRegistrationDto.getPatientIdCard());

            // 转换为DO对象
            MedicalRegistrationDO registrationDO = convertToMedicalRegistrationDO(medicareRegistrationDto);

            // 查询是否已存在记录
            MedicalRegistrationDO existingRecord = medicalRegistrationMapper.selectOneByCondition(
                MedicalRegistrationPageReqVO.builder().tenantId(medicareRegistrationDto.getTenantId()).bizType(BizTypeEnum.HYWZ.getCode()).bizId(medicareRegistrationDto.getBizId()).build());

            if (existingRecord == null) {
                // 新增记录
                registrationDO.setPref(PrefUtil.getMedicalRegistrationPref());
                medicalRegistrationMapper.insert(registrationDO);
                log.info("【医保挂号登记】新增挂号记录成功，处方编号：{}, 挂号编号：{}", medicareRegistrationDto.getBizId(), registrationDO.getPref());
            } else {
                // 更新记录
                registrationDO.setId(existingRecord.getId());
                medicalRegistrationMapper.updateById(registrationDO);
                log.info("【医保挂号登记】更新挂号记录成功，处方编号：{}, 挂号编号：{}", medicareRegistrationDto.getBizId(), existingRecord.getPref());
            }

        } catch (Exception e) {
            log.error("【医保挂号登记】保存挂号信息异常：{}", e.getMessage(), e);
            throw new RuntimeException("保存医保挂号登记信息失败", e);
        }
    }

    /**
     * 转换DTO为DO对象
     *
     * @param medicareRegistrationDto DTO对象
     * @return DO对象
     */
    private MedicalRegistrationDO convertToMedicalRegistrationDO(MedicareRegistrationDto medicareRegistrationDto) {
        return MedicalRegistrationDO.builder().tenantId(medicareRegistrationDto.getTenantId()).bizId(medicareRegistrationDto.getBizId()).bizType(medicareRegistrationDto.getBizType()).patientPref(medicareRegistrationDto.getPatientPref())
            .patientName(medicareRegistrationDto.getPatientName()).patientMobile(medicareRegistrationDto.getPatientMobile()).patientIdCard(medicareRegistrationDto.getPatientIdCard()).bizVisitId(medicareRegistrationDto.getBizVisitId())
            .medicalVisitId(medicareRegistrationDto.getMedicalVisitId()).medicalVisitDate(medicareRegistrationDto.getMedicalVisitDate()).medType(medicareRegistrationDto.getMedType()).insuredAreaNo(medicareRegistrationDto.getInsuredAreaNo())
            .tenantAreaNo(medicareRegistrationDto.getTenantAreaNo()).psnNo(medicareRegistrationDto.getPsnNo()).iptOtpNo(medicareRegistrationDto.getIptOtpNo()).status(medicareRegistrationDto.getStatus())
            .deptPref(medicareRegistrationDto.getDeptPref()).deptName(medicareRegistrationDto.getDeptName()).hospitalPref(medicareRegistrationDto.getHospitalPref()).hospitalName(medicareRegistrationDto.getHospitalName())
            .acctUsedFlag(medicareRegistrationDto.getAcctUsedFlag()).bookTime(medicareRegistrationDto.getBookTime()).planTime(medicareRegistrationDto.getPlanTime()).build();
    }

    @Override
    @InquiryDateType
    public CommonResult<PageResult<MedicalRegistrationRespVO>> queryRegistPage(MedicalRegistrationPageReqVO pageReqVO) {
        //租户id为空且当前租户非系统租户
        if(ObjectUtil.isEmpty(pageReqVO.getTenantId()) && ObjectUtil.notEqual(TenantContextHolder.getTenantId(), TenantConstant.DEFAULT_TENANT_ID)){
            pageReqVO.setTenantId(TenantContextHolder.getTenantId());
        }
        // 使用MyBatis-Plus分页查询
        PageResult<MedicalRegistrationDO> pageResult = medicalRegistrationMapper.selectListByCondition(pageReqVO);
        // 转换为响应VO
        PageResult<MedicalRegistrationRespVO> respPageResult = new PageResult<>(RegistrationConvert.INSTANCE.convertList(pageResult.getList()), pageResult.getTotal());
        return CommonResult.success(respPageResult);
    }

    @Override
    public CommonResult<Boolean> cancelMedicalRegistration(String registrationPref) {
        try {
            log.info("【医保挂号撤销】开始撤销挂号，挂号流水号：{}", registrationPref);
            
            // 1. 根据挂号流水号查询当前挂号信息
            MedicalRegistrationDO registrationDO = medicalRegistrationMapper.selectOneByCondition(
                MedicalRegistrationPageReqVO.builder().pref(registrationPref).build());

            CommonResult<Boolean> cancelResult = medicareVisitRegistrationCancel(registrationDO);

            if(cancelResult.isError()){
                return cancelResult;
            }
            // 3. 医保撤销成功后，将当前挂号状态改为已撤销
            medicalRegistrationMapper.updateById(
                MedicalRegistrationDO.builder()
                    .id(registrationDO.getId())
                    .status(RegistrationStatusEnum.CANCELED.getCode())
                    .build());
            
            log.info("【医保挂号撤销】撤销成功，挂号流水号：{}", registrationPref);
            return CommonResult.success(true);
            
        } catch (Exception e) {
            log.error("【医保挂号撤销】撤销异常，挂号流水号：{}，异常信息：{}", registrationPref, e.getMessage(), e);
            return CommonResult.error(500, "撤销医保挂号失败：" + e.getMessage());
        }
    }

    /**
     * 医保挂号登记 委托给医保服务处理
     *
     * @param registrationDO 传输数据
     */
    public CommonResult<Boolean> medicareVisitRegistrationCancel(MedicalRegistrationDO registrationDO) {
        try {
            // 构建传输配置请求
            TransmissionConfigReqDTO configReqDTO = TransmissionConfigReqDTO.builder()
                .tenantId(registrationDO.getTenantId())
                .nodeType(NodeTypeEnum.MEDICARE_REGISTRATION_CANCEL)
                .build();

            // 构建transmitterDTO 入参
            RegistrationTransmitterDTO transmitterDTO = RegistrationConvert.INSTANCE.convertTras(registrationDO);

            // 3. 构建传输请求
            TransmissionReqDTO transmissionReqDTO = TransmissionReqDTO.buildReq(configReqDTO, transmitterDTO);

            // 5. 业务逻辑校验
            CommonResult<Boolean> businessLogic = transmissionApi.validateBusinessLogic(transmissionReqDTO);
            if (businessLogic.isSuccess() && BooleanUtil.isTrue(businessLogic.getData())) {
                return transmissionApi.contractInvoke(transmissionReqDTO, Boolean.class);
            }
        } catch (Exception e) {
            log.error("医保挂号撤销异常：{}", e.getMessage(), e);
            return CommonResult.error(500, "医保挂号撤销失败：" + e.getMessage());
        }
        return CommonResult.error("医保挂号撤销失败");
    }

}
