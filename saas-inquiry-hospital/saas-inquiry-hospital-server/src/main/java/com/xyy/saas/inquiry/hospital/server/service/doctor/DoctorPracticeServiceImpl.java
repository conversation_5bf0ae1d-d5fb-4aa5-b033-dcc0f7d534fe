package com.xyy.saas.inquiry.hospital.server.service.doctor;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.DOCTOR_PRACTICE_NOT_EXISTS;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorPracticePageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorPracticeSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorPracticeDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.DoctorPracticeMapper;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;


/**
 * 医生执业信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DoctorPracticeServiceImpl implements DoctorPracticeService {

    @Resource
    private DoctorPracticeMapper doctorPracticeMapper;

    @Resource
    @Lazy
    private InquiryDoctorService inquiryDoctorService;

    @Override
    public Long createDoctorPractice(DoctorPracticeSaveReqVO createReqVO) {
        // 插入
        DoctorPracticeDO doctorPractice = BeanUtils.toBean(createReqVO, DoctorPracticeDO.class);
        doctorPracticeMapper.insert(doctorPractice);
        // 返回
        return doctorPractice.getId();
    }

    @Override
    public void updateDoctorPractice(DoctorPracticeSaveReqVO updateReqVO) {
        // 校验存在
        validateDoctorPracticeExists(updateReqVO.getId());
        // 更新
        DoctorPracticeDO updateObj = BeanUtils.toBean(updateReqVO, DoctorPracticeDO.class);
        doctorPracticeMapper.updateById(updateObj);
    }

    @Override
    public void deleteDoctorPractice(Long id) {
        // 校验存在
        validateDoctorPracticeExists(id);
        // 删除
        doctorPracticeMapper.deleteById(id);
    }

    private void validateDoctorPracticeExists(Long id) {
        if (doctorPracticeMapper.selectById(id) == null) {
            throw exception(DOCTOR_PRACTICE_NOT_EXISTS);
        }
    }

    @Override
    public DoctorPracticeDO getDoctorPractice(Long id) {
        return doctorPracticeMapper.selectById(id);
    }

    @Override
    public PageResult<DoctorPracticeDO> getDoctorPracticePage(DoctorPracticePageReqVO pageReqVO) {
        return doctorPracticeMapper.selectPage(pageReqVO);
    }

    @Override
    public DoctorPracticeDO getDoctorPracticeByDoctorId(Long id) {
        return doctorPracticeMapper.selectOne(DoctorPracticeDO::getDoctorId, id);
    }

    @Override
    public DoctorPracticeDO getDoctorPracticeByDoctorPref(String pref) {
        InquiryDoctorDO inquiryDoctorDO = inquiryDoctorService.getInquiryDoctorByDoctorPref(pref);
        if (inquiryDoctorDO != null) {
            return getDoctorPracticeByDoctorId(inquiryDoctorDO.getId());
        }
        return null;
    }
}