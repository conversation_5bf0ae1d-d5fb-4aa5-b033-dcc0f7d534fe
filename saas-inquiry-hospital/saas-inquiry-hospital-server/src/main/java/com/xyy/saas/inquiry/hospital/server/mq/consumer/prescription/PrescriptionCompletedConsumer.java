package com.xyy.saas.inquiry.hospital.server.mq.consumer.prescription;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.mq.prescription.PrescriptionCompletedEvent;
import com.xyy.saas.inquiry.hospital.server.service.prescription.PrescriptionCompletedService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 处方流程完成事件消费者
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_hospital_server_mq_consumer_prescription_PrescriptionCompletedConsumer",
    topic = PrescriptionCompletedEvent.TOPIC)
public class PrescriptionCompletedConsumer {

    @Resource
    private PrescriptionCompletedService prescriptionCompletedService;

    public static final String GROUP_ID = PrescriptionCompletedConsumer.class.getName();

    @EventBusListener
    public void prescriptionCompletedConsumer(PrescriptionCompletedEvent event) {
        log.info("【PrescriptionCompleted】收到处方流程完成事件, pref:{}", event.getMsg());
        try {
            prescriptionCompletedService.handlePrescriptionCompleted(event.getMsg());
        } catch (Exception e) {
            log.error("【PrescriptionCompleted】处理处方流程完成事件失败, pref:{}, error:{}",
                event.getMsg(), e.getMessage(), e);
        }
    }
} 