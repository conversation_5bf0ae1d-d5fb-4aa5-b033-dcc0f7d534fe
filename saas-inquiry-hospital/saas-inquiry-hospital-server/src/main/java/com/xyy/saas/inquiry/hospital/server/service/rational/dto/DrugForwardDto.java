package com.xyy.saas.inquiry.hospital.server.service.rational.dto;

import java.io.Serializable;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 药品Dto
 *
 * <AUTHOR>
 * @Date 4/29/24 2:03 PM
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DrugForwardDto implements Serializable {

    private static final long serialVersionUID = 144045266673859060L;

    /**
     * 标准库id
     */
    private String pref;

    /**
     * 通用名
     */
    private String commonName;

    /**
     * 数量
     */
    private BigDecimal quantity;

    /**
     * 单次剂量
     */
    private String singleDose;

    /**
     * 用药频次
     */
    private String useFrequency;

    /**
     * 给药单位
     */
    private String singleUnit;

    /**
     * 给药途径
     */
    private String directions;

}
