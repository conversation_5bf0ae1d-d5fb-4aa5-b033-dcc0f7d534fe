package com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDepartmentDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 科室字典 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryHospitalDepartmentMapper extends BaseMapperX<InquiryHospitalDepartmentDO> {

    default PageResult<InquiryHospitalDepartmentDO> selectPage(InquiryHospitalDepartmentPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InquiryHospitalDepartmentDO>()
            .eqIfPresent(InquiryHospitalDepartmentDO::getPref, reqVO.getPref())
            .likeIfPresent(InquiryHospitalDepartmentDO::getDeptName, reqVO.getDeptName())
            .eqIfPresent(InquiryHospitalDepartmentDO::getDeptParentId, reqVO.getDeptParentId())
            .eqIfPresent(InquiryHospitalDepartmentDO::getDeptOrder, reqVO.getDeptOrder())
            .eqIfPresent(InquiryHospitalDepartmentDO::getStatus, reqVO.getStatus())
            .betweenIfPresent(InquiryHospitalDepartmentDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(InquiryHospitalDepartmentDO::getId));
    }

    default List<InquiryHospitalDepartmentDO> selectCurrrDeptAndChildDeptList(Long parentId) {
        return selectList(new LambdaQueryWrapperX<InquiryHospitalDepartmentDO>()
            .eq(InquiryHospitalDepartmentDO::getId, parentId)
            .or()
            .eq(InquiryHospitalDepartmentDO::getDeptParentId, parentId));
    }

    default List<InquiryHospitalDepartmentDO> selectDeptmentList(InquiryHospitalDepartmentPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<InquiryHospitalDepartmentDO>()
            .eqIfPresent(InquiryHospitalDepartmentDO::getPref, reqVO.getPref())
            .likeIfPresent(InquiryHospitalDepartmentDO::getDeptName, reqVO.getDeptName())
            .eqIfPresent(InquiryHospitalDepartmentDO::getStatus, reqVO.getStatus()));
    }

}