package com.xyy.saas.inquiry.hospital.server.dal.redis.inquiry;

import cn.hutool.core.util.ObjectUtil;
import com.xyy.saas.inquiry.constant.RedisKeyConstants;
import com.xyy.saas.inquiry.enums.doctor.AutoGrabStatusEnum;
import com.xyy.saas.inquiry.enums.doctor.DoctorInquiryTypeEnum;
import com.xyy.saas.inquiry.hospital.server.dal.redis.HospitalBaseRedisDao;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.util.RedisUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import org.springframework.stereotype.Repository;

/**
 * @Author: xucao
 * @Date: 2024/12/26 14:38
 * @Description: 问诊redis操作类
 */
@Repository
public class HospitalInquiryRedisDao extends HospitalBaseRedisDao {

    /**
     * 根据问诊单号获取当前问诊的派单医生
     * @param inquiryPref 问诊单号
     * @return 已派单医生
     */
    public List<String> getInquirySendList(String inquiryPref) {
        return new ArrayList<>(RedisUtils.lGetAll(RedisKeyConstants.getSendInquiryKey(inquiryPref)).stream().map(Object::toString).toList());
    }

    public String getInquiryCurrentDoctor(String inquiryPref) {
        return Optional.ofNullable(RedisUtils.get(RedisKeyConstants.getInquiryCurrentDoctorKey(inquiryPref))).orElse("").toString();
    }

    /**
     * 获取当前问诊单调度过的科室
     * @param inquiryPref 问诊单号
     * @return
     */
    public List<String> getInquiryDispatchDeptList(String inquiryPref) {
        return new ArrayList<>(RedisUtils.lGetAll(RedisKeyConstants.getInquiryDispatchDeptKey(inquiryPref)).stream().map(Object::toString).toList());
    }

    /**
     * 将当前问诊单推入医生接诊大厅
     * @param inquiryDto 问诊单信息
     * @param deptPref 部门编码
     */
    public void pushInquiryToReactionArea(InquiryRecordDto inquiryDto, String deptPref) {
        String key = RedisKeyConstants.getRecetionAreaKey(inquiryDto.getHospitalPref(), deptPref, inquiryDto.getAutoInquiry(), inquiryDto.getInquiryWayType());
        String saveKey = RedisKeyConstants.getInquiryDispatchDeptKey(inquiryDto.getPref());
        executeRedisTransaction(operations -> {
            // 将问诊单号写入当前调度的接诊大厅
            // 先删除
            operations.opsForList().remove(key, 0, inquiryDto.getPref());
            // 再写入
            operations.opsForList().leftPush(key, inquiryDto.getPref());

            // 同步记录当前问诊单调度了那个科室
            // 先删除
            operations.opsForList().remove(saveKey, 0, deptPref);
            // 再写入
            operations.opsForList().leftPush(saveKey, deptPref);
            // 给saveKey添加过期时间
            operations.expire(saveKey, 2, TimeUnit.HOURS);
        });
    }
}
