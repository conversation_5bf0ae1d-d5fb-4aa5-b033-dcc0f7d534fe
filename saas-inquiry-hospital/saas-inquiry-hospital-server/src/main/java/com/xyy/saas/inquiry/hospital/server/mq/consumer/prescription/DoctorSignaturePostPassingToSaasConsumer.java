package com.xyy.saas.inquiry.hospital.server.mq.consumer.prescription;


import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.hospital.server.service.prescription.SynInquiryPrescriptionToSaaS;
import com.xyy.saas.inquiry.mq.prescription.DoctorSignaturePostPassingEvent;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_hospital_server_mq_consumer_prescription_DoctorSignaturePostPassingToSaasConsumer",
    topic = DoctorSignaturePostPassingEvent.TOPIC)
public class DoctorSignaturePostPassingToSaasConsumer {


    public static final String GROUP_ID = DoctorSignaturePostPassingToSaasConsumer.class.getName();


    @Resource
    private SynInquiryPrescriptionToSaaS  synInquiryPrescriptionToSaaS;

    @EventBusListener
    public void doctorSignaturePostPassingConsumer(DoctorSignaturePostPassingEvent event) {
        log.info("医生签章后回传Saas消费者,event: {}", JsonUtils.toJsonString(event));
        synInquiryPrescriptionToSaaS.toSaas(event.getMsg());
    }
}
