package com.xyy.saas.inquiry.hospital.server.service.hospital;

import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryHospitalDeptDoctorDO;
import java.util.List;

/**
 * @Author: xucao
 * @DateTime: 2025/4/11 18:20
 * @Description: 医院 科室  医生 关系服务 接口
 **/
public interface InquiryHospitalDeptDoctorService {

    /**
     * 条件查询医院科室医生关系列表
     * @param reqVO
     * @return
     */
    List<InquiryHospitalDeptDoctorDO> selectList(InquiryHospitalDepartmentRelationPageReqVO reqVO);
}
