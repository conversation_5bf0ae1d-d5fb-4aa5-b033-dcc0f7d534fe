package com.xyy.saas.inquiry.hospital.server.mq.consumer.doctor;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.BooleanUtil;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.ding.DingService;
import com.xyy.saas.inquiry.ding.DingService.Markdown;
import com.xyy.saas.inquiry.enums.doctor.AuditStatusEnum;
import com.xyy.saas.inquiry.enums.doctor.CooperationStatusEnum;
import com.xyy.saas.inquiry.enums.doctor.DoctorInquiryTypeEnum;
import com.xyy.saas.inquiry.enums.signature.FddCaConstantEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorFilingDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.DoctorFilingMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryDoctorStatusMapper;
import com.xyy.saas.inquiry.hospital.server.dal.redis.doctor.DoctorRedisDao;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorService;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorStatusService;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalDetpDoctorRelationService;
import com.xyy.saas.inquiry.mq.doctor.DoctorAutoInquiryTimerWheelEvent;
import com.xyy.saas.inquiry.mq.doctor.dto.DoctorAutoInquiryTimerWheelDto;
import com.xyy.saas.inquiry.mq.doctor.dto.DoctorAutoInquiryTimerWheelMessage;
import com.xyy.saas.inquiry.mq.doctor.dto.DoctorAutoInquiryTimerWheelMessage.DoctorAutoInquiryTimerWheelEnum;
import com.xyy.saas.inquiry.signature.api.ca.InquirySignatureCaAuthApi;
import com.xyy.saas.inquiry.signature.api.ca.dto.InquirySignatureCaAuthRespDto;
import com.xyy.saas.inquiry.util.RedisUtils;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 医师自动开方出停诊Consumer
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_hospital_server_mq_consumer_doctor_DoctorAutoInquiryTimerWheelEvent",
    topic = DoctorAutoInquiryTimerWheelEvent.TOPIC)
public class DoctorAutoInquiryTimerWheelConsumer {

    public static final String GROUP_ID = DoctorAutoInquiryTimerWheelConsumer.class.getName();

    @Resource
    private InquiryHospitalDetpDoctorRelationService doctorInquiryHospitalDetpDoctorRelationService;

    @Resource
    private InquiryDoctorStatusService inquiryDoctorStatusService;

    @Resource
    private InquiryDoctorStatusMapper inquiryDoctorStatusMapper;

    @Resource
    private InquiryDoctorService inquiryDoctorService;

    @Resource
    private DoctorFilingMapper doctorFilingMapper;

    @Resource
    private DoctorRedisDao doctorRedisDao;

    @Resource
    private InquirySignatureCaAuthApi inquirySignatureCaAuthApi;

    @Resource
    private DingService dingService;


    @EventBusListener
    @Transactional(rollbackFor = Exception.class)
    public void doctorAutoInquiryTimerWheelEvent(DoctorAutoInquiryTimerWheelEvent doctorAutoInquiryTimerWheelEvent) {
        DoctorAutoInquiryTimerWheelMessage msg = doctorAutoInquiryTimerWheelEvent.getMsg();

        if (Objects.equals(msg.getType(), DoctorAutoInquiryTimerWheelEnum.PERSONNEL_CHECK_CA.getCode())) {
            // 处理自动开方医生Ca校验
            TenantUtils.execute(TenantConstant.DEFAULT_TENANT_ID, () -> autoInquiryCaCheck(msg));
            return;
        }

        // 基础编辑 个人 获取医生信息 非审核通过 || 未合作的
        InquiryDoctorDO doctor = inquiryDoctorService.getInquiryDoctorByDoctorPref(msg.getDoctorPref());
        // 先移除医生省份缓存
        String orgProvinceCode = doctor == null ? null : Optional.ofNullable(doctorFilingMapper.selectOne(DoctorFilingDO::getDoctorId, doctor.getId())).map(DoctorFilingDO::getOrgProvinceCode).orElse(null);
        // 异常状态下
        if (doctor == null || BooleanUtil.isTrue(doctor.getDisable()) || !Objects.equals(doctor.getAuditStatus(), AuditStatusEnum.APPROVED.getCode()) || !Objects.equals(doctor.getCooperation(),
            CooperationStatusEnum.IN_COOPERATION.getCode())) {
            // 停诊 - 移除时间轮
            doctorRedisDao.handleAutoInquiryDoctorTimerWheel(msg.getOriginWheels(), null, orgProvinceCode);
            inquiryDoctorStatusMapper.deleteByDoctorPrefType(msg.getDoctorPref(), DoctorInquiryTypeEnum.AUTO_INQUIRY.getCode());
            return;
        }
        // 获取并处理医生对当时间新轮
        List<DoctorAutoInquiryTimerWheelDto> newWheels = doctorInquiryHospitalDetpDoctorRelationService.queryDoctorAutoInquiryTimeWheelDto(Collections.singletonList(msg.getDoctorPref()));
        doctorRedisDao.handleAutoInquiryDoctorTimerWheel(msg.getOriginWheels(), newWheels, orgProvinceCode);
        // 处理自动开方医生状态
        inquiryDoctorStatusService.handleAutoInquiryDoctorStatus(doctor);
    }

    /**
     * 自动开方免签时间校验
     *
     * @param msg
     */
    private void autoInquiryCaCheck(DoctorAutoInquiryTimerWheelMessage msg) {
        InquiryDoctorDO doctor = inquiryDoctorService.getInquiryDoctorByDoctorPref(msg.getDoctorPref());

        InquirySignatureCaAuthRespDto authInfo = doctor == null ? null : inquirySignatureCaAuthApi.getCaAuthInfo(doctor.getUserId(), SignaturePlatformEnum.FDD);

        if (isFreeSignValid(authInfo, 24 * 60 * 60)) {  // 免签1天过期 移除
            List<DoctorAutoInquiryTimerWheelDto> oldWheels = doctorInquiryHospitalDetpDoctorRelationService.queryDoctorAutoInquiryTimeWheelDto(Collections.singletonList(msg.getDoctorPref()));
            doctorRedisDao.handleAutoInquiryDoctorTimerWheel(oldWheels, null, null);
            log.info("医生自动开方时间轮-移除,CA未免签或到期,doctorPref:{}", msg.getDoctorPref());

            if (RedisUtils.tryLock("doctor_auto_inquiry_timer_wheel_ding_key" + msg.getDoctorPref(), "1", 30 * 60 * 1000)) { // 锁30分钟
                dingService.send(Markdown
                    .title("医生自动开方池-移除")
                    .add("医生", msg.getDoctorPref() + "(" + (doctor == null ? "" : doctor.getName()) + ")")
                    .add("开方池", msg.getRealKey())
                    .add("说明", "CA未免签或即将过期,请CA免签后再[编辑保存]当前医生信息,以重放开方池")
                );
            }

            return;
        }

        for (Integer i : List.of(2, 3, 5, 7, 10, 15)) {
            if (isFreeSignValid(authInfo, i * 24 * 60 * 60)) { // 免签i天过期 发送提醒的Ding
                log.info("医生自动开方时间轮-临期提醒,CA未免签或还有{}天到期,doctorPref:{}", i, msg.getDoctorPref());
                if (RedisUtils.tryLock("doctor_auto_inquiry_timer_wheel_ca_ding_key" + msg.getDoctorPref(), "1", 23 * 60 * 60 * 1000)) { // 锁23小时
                    dingService.send(Markdown
                        .title("医生自动开方池-CA临期")
                        .add("医生", msg.getDoctorPref() + "(" + doctor.getName() + ")")
                        .add("说明", "CA免签剩余" + i + "天到期,请及时联系医生重做CA免签")
                    );
                }

                break;
            }
        }
    }

    private static boolean isFreeSignValid(InquirySignatureCaAuthRespDto authInfo, int time) {
        return authInfo == null || !FddCaConstantEnum.isFreeSignAndValid(authInfo.getAuthorizeFreeSignStatus(), authInfo.getAuthorizeFreeSignDdl(), time)
            || (CollUtil.isNotEmpty(authInfo.getExt()) && authInfo.getExt().stream().anyMatch(c -> !FddCaConstantEnum.isFreeSignAndValid(c.getAuthorizeFreeSignStatus(), c.getAuthorizeFreeSignDdl(), time)));
    }

}
