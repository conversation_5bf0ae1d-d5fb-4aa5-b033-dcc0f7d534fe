package com.xyy.saas.inquiry.hospital.server.util;

import cn.hutool.core.util.StrUtil;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

/**
 * @Author:chenxiaoyi
 * @Date:2025/04/16 10:46
 */
public class FileUtils {

    /**
     * 构建提取文件名称
     *
     * @param fileName 文件名
     * @param pdfUrl   图片地址
     * @return
     */
    public static String buildFileName(String fileName, String pdfUrl) {
        // 基础名称处理（过滤非法字符）
        String baseName = cn.hutool.core.io.FileUtil.cleanInvalid(fileName);
        // 扩展名提取（处理含参数的URL）
        String pureUrl = StrUtil.subBefore(pdfUrl, '?', false);
        String ext = cn.hutool.core.io.FileUtil.extName(pureUrl);
        return StrUtil.isBlank(ext) ? baseName : baseName + "." + ext;
    }


    // 处理非法字符和中文编码问题
    public static String sanitizeFileName(String originalName) {
        // 方案1：URL编码文件名（推荐）
        String encoded = URLEncoder.encode(originalName, StandardCharsets.UTF_8);
        // 替换URL编码后的空格和保留字符（可选）
        return encoded.replace("%", "_");

        // 方案2：直接过滤非法字符（备选）
        // return originalName.replaceAll("[^a-zA-Z0-9._-]", "_");
    }

}
