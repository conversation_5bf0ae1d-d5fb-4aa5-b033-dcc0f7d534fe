package com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 科室字典 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_hospital_department")
@KeySequence("saas_inquiry_hospital_department_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryHospitalDepartmentDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 科室编码,eg:101
     */
    private String pref;
    /**
     * 科室名称,eg:内科
     */
    private String deptName;
    /**
     * 父级科室id
     */
    private Long deptParentId;
    /**
     * 科室序号
     */
    private Integer deptOrder;
    /**
     * 当前科室状态 0 启用 1 禁用
     */
    private Integer status;

}