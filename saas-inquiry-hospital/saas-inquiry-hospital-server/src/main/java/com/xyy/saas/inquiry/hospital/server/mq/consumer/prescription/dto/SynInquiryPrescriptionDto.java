package com.xyy.saas.inquiry.hospital.server.mq.consumer.prescription.dto;

import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.text.MessageFormat;
import java.util.Date;
import java.util.List;
import java.util.Objects;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SynInquiryPrescriptionDto implements Serializable {


    /**
     *问诊id，加前缀区分
     */
    private String apply_id;

    /**
     * 患者姓名
     */
    private String patient_name = "";

    /**
     * 性别  1-男 2-女
     */
    private Integer sex;

    /**
     * 年龄
     */
    private Integer age;

    /**
     * 手机号
     */
    private String telephone;

    /**
     * 生日
     */
    private Date birthday;

    /**
     * 婚否 0-未婚 1-已婚
     */
    private Integer married;

    /**
     * 过敏药品，没有写无
     */
    private String allergy;

    /**
     * 症状
     */
    private String patient_condition;

    /**
     * 医生姓名
     */
    private String doctor_name = "";

    /**
     * 科室
     */
    private String departments  = "";

    /**
     * 医院
     */
    private String hospital = "";

    /**
     * 医生头像
     */
    private String doctor_head = "";

    /**
     * 诊断内容
     */
    private String diagnostic_content = "";

    /**
     * 处方链接
     */
    private String prescription_pdf;

    /**
     * 机构号
     */
    private String organSign;

    /**
     * 问诊时间
     */
    private Date create_time;

    /**
     * 处方列表
     */
    private List<DrugInfoDto> drug_info;

    /**
     * 处方审核状态 0-未审核 1-审核通过 2-审核驳回',
     */
    private Integer approval_status;

    // 转化新问诊处方状态 对应 Saas远程问诊记录的审核状态
    public void setApproval_status(Integer approval_status) {
        this.approval_status = 0;
        if (Objects.equals(PrescriptionStatusEnum.APPROVAL.getStatusCode(), approval_status)) {
            this.approval_status = 1;
        }
        if (Objects.equals(PrescriptionStatusEnum.APPROVAL_REJECTED.getStatusCode(), approval_status)) {
            this.approval_status = 2;
        }

    }

    /**
     * 审核药师姓名
     */
    private String approval_user ;

    /**
     * 身份证
     */
    private String id_card ;
    /**
     * 医师出方时间
     */
    private Date out_prescription_time;

    /**
     * 处方状态
     */
    private Integer status;

    /**
     * 审方类型 0 正常审方 1 带方审方   99废弃处方
     */
    private Integer auditType;

    public void setAuditType(Integer inquiryBizType) {
        this.auditType = 0;
        if(Objects.equals(InquiryBizTypeEnum.REMOTE_INQUIRY.getCode(),inquiryBizType)){
            this.auditType = 1;
        }
        if(Objects.equals(99,inquiryBizType)){
            this.auditType = inquiryBizType;
        }

    }

    /**
     * 新问诊标识 1 代表新问诊
     */
    private String newInquiry;



    @Override
    public String toString() {
        return MessageFormat.format(
            "SynInquiryPrescriptionDto'{'apply_id=''{0}'', patient_name=''{1}'', sex={2}, telephone=''{3}'', birthday={4}, married={5}, allergy=''{6}'', patient_condition=''{7}'', doctor_name=''{8}'', departments=''{9}'', hospital=''{10}'', doctor_head=''{11}'', diagnostic_content=''{12}'', prescription_pdf=''{13}'', organSign=''{14}'', create_time={15}, drug_info={16}, approval_status={17}, approval_user=''{18}'', id_card=''{19}'''}'",
            apply_id, patient_name, sex, telephone, birthday, married, allergy, patient_condition, doctor_name, departments, hospital, doctor_head, diagnostic_content, prescription_pdf, organSign, create_time, drug_info, approval_status,
            approval_user, id_card);
    }


    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class DrugInfoDto implements Serializable{


        /**
         * 药品名称
         */
        private String drug_name = "";

        /**
         * 友德医药品code，可忽略
         */
        private String drug_code = "";

        /**
         * 数量
         */
        private String quantity = "";

        /**
         * 单位
         */
        private String unit = "";

        /**
         * 规格
         */
        private String specification = "";

        /**
         * 单价
         */
        private String unit_price = "";

        /**
         * 用法
         */
        private String usage = "";

        /**
         * 用量
         */
        private String dosage_info = "";

        /**
         * 频次
         */
        private String frequency = "";

        @Override
        public String toString() {
            return MessageFormat.format("DrugInfoDto'{'drug_name=''{0}'', drug_code=''{1}'', quantity=''{2}'', unit=''{3}'', specification=''{4}'', unit_price=''{5}'', usage=''{6}'', dosage_info=''{7}'', frequency=''{8}'''}'", drug_name,
                drug_code, quantity, unit, specification, unit_price, usage, dosage_info, frequency);
        }

    }
}