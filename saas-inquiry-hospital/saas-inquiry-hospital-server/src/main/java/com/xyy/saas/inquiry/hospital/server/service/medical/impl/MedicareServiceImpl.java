package com.xyy.saas.inquiry.hospital.server.service.medical.impl;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.hospital.api.medicare.dto.MedicareRegistrationDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.convert.medical.MedicareConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.medical.MedicareSigninRecordDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.medicare.MedicareSigninRecordMapper;
import com.xyy.saas.inquiry.hospital.server.service.medical.MedicalRegistrationService;
import com.xyy.saas.inquiry.hospital.server.service.medical.MedicareService;
import com.xyy.saas.inquiry.pojo.medicare.MedicareSignInDto;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionTransmitterDTO;
import com.xyy.saas.transmitter.api.transmission.TransmissionApi;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionConfigReqDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * 医保服务实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MedicareServiceImpl implements MedicareService {

    @Resource
    private MedicareSigninRecordMapper medicareSigninRecordMapper;

    @Resource
    private MedicalRegistrationService medicalRegistrationService;

    @DubboReference(retries = 0)
    private TransmissionApi transmissionApi;

    @Override
    public MedicareSigninRecordDO ensureSigninRecord(InquiryPrescriptionRespVO prescriptionRespVO) {
        try {
            // 查询是否已有签到记录
            MedicareSigninRecordDO existingRecord = medicareSigninRecordMapper.selectSignedInRecord(prescriptionRespVO.getHospitalPref());
            if (existingRecord != null) {
                return existingRecord;
            }
            // 调用9001医保签到接口并保存签到记录
            return performMedicareSignin(prescriptionRespVO);

        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 执行医保签到并保存记录
     *
     * @param prescriptionRespVO 处方信息
     * @return 签到记录，失败返回null
     */
    private MedicareSigninRecordDO performMedicareSignin(InquiryPrescriptionRespVO prescriptionRespVO) {
        try {
            // 构建签到传输配置
            TransmissionConfigReqDTO signinConfigReqDTO = TransmissionConfigReqDTO.builder()
                .tenantId(prescriptionRespVO.getTenantId())
                .nodeType(NodeTypeEnum.MEDICARE_SIGN)
                .build();

            // 构建签到传输请求（这里使用现有的transmitterDTO结构，实际应根据9001接口需求调整）
            TransmissionReqDTO signinReqDTO = TransmissionReqDTO.buildReq(signinConfigReqDTO,
                MedicareConvert.INSTANCE.buildSigninTransmitterData(prescriptionRespVO));

            // 业务逻辑校验
            CommonResult<Boolean> businessLogic = transmissionApi.validateBusinessLogic(signinReqDTO);
            if (!businessLogic.isSuccess()) {
                return null;
            }

            // 调用DSL进行签到
            CommonResult<MedicareSignInDto> signinResult =
                transmissionApi.contractInvoke(signinReqDTO, MedicareSignInDto.class);

            if (signinResult.isSuccess()) {
                // 保存签到记录并返回
                MedicareSigninRecordDO signinRecord = MedicareConvert.INSTANCE.buildMedicareSigninRecord(
                    prescriptionRespVO, signinResult.getData());
                medicareSigninRecordMapper.insert(signinRecord);
                return signinRecord;
            }
        } catch (Exception e) {
        }
        return null;
    }


    /**
     * 保存医保挂号登记信息
     *
     * @param prescriptionRespVO 处方信息
     * @param transmitterDTO     传输数据
     * @param responseData       响应数据
     */
    public void saveMedicareRegistrationInfo(InquiryPrescriptionRespVO prescriptionRespVO,
        PrescriptionTransmitterDTO transmitterDTO,
        MedicareRegistrationDto responseData) {
        try {
            log.info("【医保挂号登记】开始保存挂号信息，处方编号：{}", prescriptionRespVO.getPref());

            // 构建医保挂号登记DTO
            MedicareRegistrationDto medicareRegistrationDto = MedicareConvert.INSTANCE.buildMedicareRegistrationDto(
                prescriptionRespVO, transmitterDTO, responseData);

            // 调用MedicalRegistrationService保存医保挂号登记信息
            medicalRegistrationService.saveMedicareRegistrationInfo(medicareRegistrationDto);

            log.info("【医保挂号登记】挂号信息保存成功，处方编号：{}", prescriptionRespVO.getPref());

        } catch (Exception e) {
            log.error("【医保挂号登记】保存挂号信息异常，处方编号：{}, error:{}",
                prescriptionRespVO.getPref(), e.getMessage(), e);
        }
    }

} 