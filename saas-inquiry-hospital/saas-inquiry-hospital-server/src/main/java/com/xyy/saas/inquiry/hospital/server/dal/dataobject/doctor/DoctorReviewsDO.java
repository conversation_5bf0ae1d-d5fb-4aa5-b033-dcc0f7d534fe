package com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.type.StringListTypeHandler;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.math.BigDecimal;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 医生问诊评价 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_doctor_reviews", autoResultMap = true)
@KeySequence("saas_doctor_reviews_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DoctorReviewsDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 问诊单pref
     */
    private String inquiryPref;
    /**
     * 处方编号
     */
    private String prescriptionPref;
    /**
     * 医师编码
     */
    private String doctorPref;
    /**
     * 满意度评分
     */
    private BigDecimal satisfactionScore;

    /**
     * 满意的点
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> satisfactionItem;

    /**
     * 评论内容
     */
    private String reviewsContent;

}