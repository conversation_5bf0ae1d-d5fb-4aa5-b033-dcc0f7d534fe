package com.xyy.saas.inquiry.hospital.server.service.doctor.chain;

import cn.hutool.core.util.BooleanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.drugstore.api.option.InquiryOptionConfigApi;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigRespDto;
import com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.hospital.server.dal.redis.doctor.DoctorRedisDao;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pojo.TenantDto;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import java.util.List;

/**
 * @Author: xucao
 * @DateTime: 2025/7/10 13:08
 * @Description: 医保编码医生过滤
 **/
@Component
@Slf4j
public class MedicareDoctorFilterChain extends DoctorFilterChain {

    @DubboReference
    private InquiryOptionConfigApi inquiryOptionConfigApi;

    @DubboReference
    private TenantApi tenantApi;

    @Resource
    private DoctorRedisDao doctorRedisDao;

    @Override
    @TraceNode(node = TraceNodeEnum.DOCTOR_FILTER_MEDICARE, prefLocation = "inquiryDto.pref")
    public void filter(InquiryRecordDto inquiryDto, List<String> doctorList) {
        TenantDto tenantDto = tenantApi.getTenant();
        // 查询区域门店配置
        InquiryOptionConfigRespDto optionConfigRespDto = inquiryOptionConfigApi.getInquiryOptionConfig(tenantDto, InquiryOptionTypeEnum.PRES_MEDICARE_CODE_DISPLAY);
        if (ObjectUtil.isEmpty(optionConfigRespDto) || !BooleanUtil.isTrue(optionConfigRespDto.getPresMedicareCodeDisplay())) {
            // 不显示医保编码，则直接返回
            return;
        }
        // 需要过滤有医保编码的医生、首先获取医保编码医生有哪些
        List<String> medicareDoctor = doctorRedisDao.getMedicareDoctorQueue(tenantDto.getEnvTag());
        // 取交集
        doctorList.retainAll(medicareDoctor);
    }
}
