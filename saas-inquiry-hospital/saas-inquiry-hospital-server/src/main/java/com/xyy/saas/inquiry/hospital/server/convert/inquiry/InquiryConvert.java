package com.xyy.saas.inquiry.hospital.server.convert.inquiry;

import com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo.DoctorOperatFloorRespVO;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pojo.TenantDto;
import java.util.ArrayList;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.util.ObjectUtils;

/**
 * @Author: xucao
 * @Date: 2024/12/25 14:47
 * @Description: 问诊参数转换器
 */
@Mapper
public interface InquiryConvert {

    InquiryConvert INSTANCE = Mappers.getMapper(InquiryConvert.class);

    default void convertTenantParam(InquiryRecordDto inquiryDto, TenantDto tenantDto) {
        inquiryDto.setEnv(tenantDto.getEnvTag());
    }

    default List<DoctorOperatFloorRespVO> convertDTO2VO(List<InquiryRecordDto> recordDtos) {
        List<DoctorOperatFloorRespVO> respVOList = new ArrayList<>();
        recordDtos.stream().filter(recordDto -> !ObjectUtils.isEmpty(recordDto.getInquiryRecordDetailDto())).forEach(recordDto -> {
            DoctorOperatFloorRespVO respVO = convertDetailDTO2VO(recordDto.getInquiryRecordDetailDto());
            respVO.setIsAutoGrabInquiry(recordDto.getAutoGrabStatus());
            respVO.setInquiryWayType(recordDto.getInquiryWayType());
            respVOList.add(respVO);
        });
        return respVOList;
    }

    DoctorOperatFloorRespVO convertDetailDTO2VO(InquiryRecordDetailDto detailDto);
}
