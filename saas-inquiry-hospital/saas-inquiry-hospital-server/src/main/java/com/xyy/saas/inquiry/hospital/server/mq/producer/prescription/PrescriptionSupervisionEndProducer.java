package com.xyy.saas.inquiry.hospital.server.mq.producer.prescription;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.mq.prescription.PrescriptionSupervisionEndEvent;
import org.springframework.stereotype.Component;

/**
 * 处方对接监管完成 producer
 *
 * @Author:ch<PERSON><PERSON><PERSON>i
 * @Date:2024/12/18 11:25
 */
@Component
@EventBusProducer(
    topic = PrescriptionSupervisionEndEvent.TOPIC
)
public class PrescriptionSupervisionEndProducer extends EventBusRocketMQTemplate {

}
