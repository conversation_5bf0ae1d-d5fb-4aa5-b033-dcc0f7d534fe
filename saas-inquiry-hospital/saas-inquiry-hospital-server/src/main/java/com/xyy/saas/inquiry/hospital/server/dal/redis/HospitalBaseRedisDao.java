package com.xyy.saas.inquiry.hospital.server.dal.redis;

import com.xyy.saas.inquiry.constant.RedisKeyConstants;
import com.xyy.saas.inquiry.drugstore.api.option.InquiryOptionConfigApi;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigQueryDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionGlobalConfigRespDto;
import com.xyy.saas.inquiry.enums.doctor.OnlineStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorStatusPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorStatusDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryHospitalDeptDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryDoctorMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryDoctorStatusMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryHospitalDeptDoctorMapper;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.function.Consumer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.core.RedisOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SessionCallback;
import org.springframework.stereotype.Repository;

/**
 * @Author: xucao
 * @Date: 2024/12/19 10:43
 * @Description: redis dao基类
 */
@Slf4j
@Repository
public class HospitalBaseRedisDao {

    @Resource
    private RedisTemplate redisTemplate;

    @Resource
    private InquiryHospitalDeptDoctorMapper inquiryHospitalDeptDoctorMapper;

    @Resource
    private InquiryDoctorStatusMapper inquiryDoctorStatusMapper;

    @Resource
    private InquiryDoctorMapper inquiryDoctorMapper;

    @Resource
    private InquiryOptionConfigApi inquiryOptionConfigApi;

    /**
     * 医生最大同时接诊数
     */
    @Value("${doctor.max.inquiry:5}")
    private Integer defaultDoctorMaxInquiry;

    /**
     * 执行Redis事务，将非公共逻辑传入
     *
     * @param action 非公共逻辑处理
     */
    public void executeRedisTransaction(Consumer<RedisOperations> action) {
        redisTemplate.execute(new SessionCallback<Boolean>() {
            @Override
            public Boolean execute(RedisOperations operations) throws DataAccessException {
                // 开启事务
                operations.multi();
                try {
                    // 执行传入的操作
                    action.accept(operations);
                    // 提交事务
                    operations.exec();
                    return true;
                } catch (Exception e) {
                    log.error("Error executing redis transaction, e:{}", e);
                    operations.discard();
                    throw e;
                }
            }
        });
    }

    /**
     * 获取当前医生等待队列
     *
     * @param doctorPref             医生编码
     * @param inquiryType            接诊类型
     * @param hospitalDeptRelationId 医院科室id
     * @return
     */
    public List<String> getDoctorWaitKeyList(String doctorPref, Integer inquiryType, Long hospitalDeptRelationId) {
        InquiryDoctorDO inquiryDoctor = inquiryDoctorMapper.selectOne(InquiryDoctorDO::getPref, doctorPref);
        // 查询当前医生（真人  or  ai）出诊的接诊权限 （图文  or  视频）
        List<Integer> inquiryWayTypes = inquiryDoctorStatusMapper.selectList(
                InquiryDoctorStatusPageReqVO.builder().doctorPref(doctorPref).inquiryType(inquiryType).status(OnlineStatusEnum.ONLINE.getCode()).build()).stream()
            .map(InquiryDoctorStatusDO::getInquiryWayType).distinct().toList();
        // 查询当前医生可接诊问诊列表
        List<InquiryHospitalDeptDoctorDO> promistList = inquiryHospitalDeptDoctorMapper.selectList(InquiryHospitalDepartmentRelationPageReqVO.builder()
            .doctorPref(doctorPref).inquiryType(inquiryType).inquiryWayTypes(inquiryWayTypes).hospitalDeptRelationId(hospitalDeptRelationId).build());
        return promistList.stream().map(obj -> {
            return RedisKeyConstants.getDoctorWaitPoolKey(obj.getHospitalPref(), obj.getDeptPref(), obj.getInquiryType(), obj.getInquiryWayType(), inquiryDoctor.getEnvTag());
        }).toList();
    }

    /**
     * 获取当前医生对应接诊大厅的队列key集合
     *
     * @param doctorPref  医生编码
     * @param inquiryType 接诊类型
     * @return
     */
    public List<String> getDoctorReceptionAreaKeyList(String doctorPref, Integer inquiryType) {
        // 查询当前医生可接诊图文问诊列表
        List<InquiryHospitalDeptDoctorDO> promistList = inquiryHospitalDeptDoctorMapper.selectList(InquiryHospitalDepartmentRelationPageReqVO.builder()
            .doctorPref(doctorPref).inquiryType(inquiryType).inquiryWayTypes(Collections.singletonList(InquiryWayTypeEnum.TEXT.getCode())).build());
        return promistList.stream().map(obj -> {
            return RedisKeyConstants.getRecetionAreaKey(obj.getHospitalPref(), obj.getDeptPref(), obj.getInquiryType(), obj.getInquiryWayType());
        }).toList();
    }

    /**
     * 获取医生可同时接诊的最大数量
     * @return
     */
    public Integer getDoctorMaxReceptionNum(){
        InquiryOptionGlobalConfigRespDto globalConfig = inquiryOptionConfigApi.getInquiryOptionGlobalConfig(new InquiryOptionConfigQueryDto());
        if(globalConfig == null || globalConfig.getPresTeletextInquiryDoctorOrderMax() == null){
            return defaultDoctorMaxInquiry;
        }
        return globalConfig.getPresTeletextInquiryDoctorOrderMax();
    }

    /**
     * 获取医生处方签名间隔时间
     */
    public Integer getDoctorPrescriptionIntervalTime(){
        InquiryOptionGlobalConfigRespDto globalConfig = inquiryOptionConfigApi.getInquiryOptionGlobalConfig(new InquiryOptionConfigQueryDto());
        if(globalConfig == null || globalConfig.getPresSignatureUseInterval() == null){
            return null;
        }
        return globalConfig.getPresSignatureUseInterval();
    }

}
