package com.xyy.saas.inquiry.hospital.server.api.clinicalcase;

import com.xyy.saas.inquiry.hospital.api.clinicalcase.InquiryClinicalCaseApi;
import com.xyy.saas.inquiry.hospital.api.clinicalcase.dto.InquiryClinicalCaseRespDto;
import com.xyy.saas.inquiry.hospital.server.controller.app.clinicalcase.vo.InquiryClinicalCaseRespVO;
import com.xyy.saas.inquiry.hospital.server.convert.clinicalcase.InquiryClinicalCaseConvert;
import com.xyy.saas.inquiry.hospital.server.service.clinicalcase.InquiryClinicalCaseService;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * 门诊病例 Service 接口
 *
 * <AUTHOR>
 */
@DubboService
public class InquiryClinicalCaseApiImpl implements InquiryClinicalCaseApi {

    @Resource
    private InquiryClinicalCaseService inquiryClinicalCaseService;

    @Override
    public InquiryClinicalCaseRespDto getInquiryClinicalCase(String inquiryPref) {
        InquiryClinicalCaseRespVO clinicalCaseRespVO = inquiryClinicalCaseService.getInquiryClinicalCase(inquiryPref);
        return InquiryClinicalCaseConvert.INSTANCE.convertDto(clinicalCaseRespVO);
    }
}