package com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 医生快捷回复语 DO
 *
 * <AUTHOR>
 */
@TableName("saas_doctor_quick_reply_msg")
@KeySequence("saas_doctor_quick_reply_msg_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class DoctorQuickReplyMsgDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 父级id
     */
    private Long parentId;
    /**
     * 医生id
     */
    private Long doctorId;
    /**
     * 分类标题
     */
    private String title;
    /**
     * 常用语内容
     */
    private String content;
    /**
     * 排序
     */
    private Integer sorted;

}