package com.xyy.saas.inquiry.hospital.server.mq.consumer.inquiry;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.constant.InquiryConstant;
import com.xyy.saas.inquiry.enums.inquiry.InquiryStatusEnum;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.redis.doctor.DoctorRedisDao;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorService;
import com.xyy.saas.inquiry.hospital.server.service.reception.HospitalReceptionAreaService;
import com.xyy.saas.inquiry.mq.inquiry.InquiryEndEvent;
import com.xyy.saas.inquiry.mq.inquiry.dto.InquiryEndMessage;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.util.MathUtil;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @Author: xucao
 * @Date: 2025/01/07 17:14
 * @Description: 问诊结束医生消费者
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_hospital_server_mq_consumer_inquiry_InquiryEndDoctorConsumer", topic = InquiryEndEvent.TOPIC)
public class InquiryEndDoctorConsumer {

    @Resource
    private InquiryApi inquiryApi;

    @Resource
    private InquiryDoctorService inquiryDoctorService;

    @Resource
    private HospitalReceptionAreaService hospitalReceptionAreaService;

    @Resource
    private DoctorRedisDao doctorRedisDao;

    @Resource
    private ConfigApi configApi;


    @EventBusListener
    public void onEvent(InquiryEndEvent event) {
        InquiryEndMessage msg = event.getMsg();
        // 查询问诊单信息
        InquiryRecordDto inquiryDto = inquiryApi.getInquiryRecord(msg.getInquiryPref());
        // 自动开方时直接return
        if (inquiryDto.isAutoInquiry()) {
            return;
        }
        // 查询医生信息
        InquiryDoctorDO doctor = inquiryDoctorService.getRequireInquiryDoctorByDoctorPref(inquiryDto.getDoctorPref());
        // 获取自动抢单医生列表
        List<String> grabDoctorList = doctorRedisDao.getGrabDoctorList(doctor.getEnvTag());
        // 自动抢单医生列表为空或者当前医生不在列表中，则不处理
        if (CollectionUtils.isEmpty(grabDoctorList) || !grabDoctorList.contains(doctor.getPref())) {
            return;
        }
        // 判断问诊单状态，如果是超时取消则关闭当前医生的自动抢派单功能
        if(ObjectUtil.equals(inquiryDto.getInquiryStatus(), InquiryStatusEnum.TIMEOUT_CANCELED.getStatusCode())){
            // 关闭当前医生自动抢派单功能，不再继续抢单
            inquiryDoctorService.closeDoctorAutoGrab(doctor);
            return;
        }
        // 判断当前医生正在接诊中的自动抢单问诊是否大于等于阈值，则不处理
        if (doctorRedisDao.getDoctorCurrentAutoGrabList(doctor.getPref(), doctor.getEnvTag()).size() >= MathUtil.formatNumberWithDefault(configApi.getConfigValueByKey(InquiryConstant.INQUIRY_DOCTOR_AUTOGRAB_NUM), 1)) {
            return;
        }
        // 从接诊大厅为当前医生拉取问诊，并自动抢单
        hospitalReceptionAreaService.doctorPullInquiryFromReceptionArea(doctor);
    }

}
