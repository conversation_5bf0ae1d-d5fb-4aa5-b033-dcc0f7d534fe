package com.xyy.saas.inquiry.hospital.server.service.doctor;

import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo.DoctorReviewsSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorReviewsDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.DoctorReviewsMapper;
import com.xyy.saas.inquiry.hospital.server.service.message.DoctorImService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DOCTOR_REVIEW_EXISTS;

/**
 * 医生问诊评价 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DoctorReviewsServiceImpl implements DoctorReviewsService {

    @Resource
    private DoctorReviewsMapper doctorReviewsMapper;

    @Resource
    private DoctorImService doctorImService;

    @Override
    public Boolean createDoctorReviews(DoctorReviewsSaveReqVO createReqVO) {
        DoctorReviewsDO reviewsDO = doctorReviewsMapper.selectOne(DoctorReviewsDO::getInquiryPref, createReqVO.getInquiryPref());
        if (reviewsDO != null) {
            throw exception(INQUIRY_DOCTOR_REVIEW_EXISTS);
        }
        // 插入
        DoctorReviewsDO doctorReviews = BeanUtils.toBean(createReqVO, DoctorReviewsDO.class);
        // 返回
        Boolean result = doctorReviewsMapper.insert(doctorReviews) > 0;
        // 通知医生端评价完成
        if (result) {
            doctorImService.sendDoctorReviewsImMessage(createReqVO.getInquiryPref());
        }
        return result;
    }

    @Override
    public DoctorReviewsDO getDoctorReviewsByInquiryPref(String inquiryPref) {
        return doctorReviewsMapper.selectOne(DoctorReviewsDO::getInquiryPref, inquiryPref);
    }

}