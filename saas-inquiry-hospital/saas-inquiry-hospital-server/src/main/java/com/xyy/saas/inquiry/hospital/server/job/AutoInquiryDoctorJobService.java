package com.xyy.saas.inquiry.hospital.server.job;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import com.alibaba.fastjson.JSON;
import com.baomidou.lock.annotation.Lock4j;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorRespVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorFilingDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.DoctorFilingMapper;
import com.xyy.saas.inquiry.hospital.server.dal.redis.RedisKeyConstants;
import com.xyy.saas.inquiry.hospital.server.dal.redis.doctor.DoctorRedisDao;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorService;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorStatusService;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalDetpDoctorRelationService;
import com.xyy.saas.inquiry.mq.doctor.DoctorAutoInquirySwitchProducer;
import com.xyy.saas.inquiry.mq.doctor.DoctorAutoInquiryTimerWheelEvent;
import com.xyy.saas.inquiry.mq.doctor.dto.DoctorAutoInquiryTimerWheelDto;
import com.xyy.saas.inquiry.mq.doctor.dto.DoctorAutoInquiryTimerWheelMessage;
import com.xyy.saas.inquiry.mq.doctor.dto.DoctorAutoInquiryTimerWheelMessage.DoctorAutoInquiryTimerWheelEnum;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class AutoInquiryDoctorJobService {

    @Resource
    private InquiryHospitalDetpDoctorRelationService inquiryHospitalDetpDoctorRelationService;

    @Resource
    private InquiryDoctorService inquiryDoctorService;

    @Resource
    private DoctorFilingMapper doctorFilingMapper;

    @Resource
    private DoctorRedisDao doctorRedisDao;

    @Resource
    private InquiryDoctorStatusService inquiryDoctorStatusService;

    @Resource
    private DoctorAutoInquirySwitchProducer doctorAutoInquirySwitchProducer;

    /**
     * 定时任务处理自动开方医生 出停诊 - 时间轮
     */
    @Lock4j(keys = "'" + RedisKeyConstants.DOCTOR_AUTO_INQUIRY_TIMER_WHEEL_LOCK_KEY + "'")
    public void jobHandAutoInquiryDoctor() {
        LocalTime time = LocalTime.now();
        List<String> keys = doctorRedisDao.getCurrentAutoInquiryDoctorTimerWheel(time);
        log.info("定时任务处理自动开方医生,keys:{}", JSON.toJSONString(keys));
        if (CollUtil.isEmpty(keys)) {
            return;
        }

        for (String key : keys) {
            String doctorPref = RedisKeyConstants.getDoctorAutoInquiryTimerWheelPref(key);
            String realKey = RedisKeyConstants.getDoctorAutoInquiryTimerWheelRealKey(key);
            // 获取医生信息  + 医生省份
            log.info("--start--定时任务处理自动开方医生 出诊,doctorPref:{},key:{},是否出诊:{}", doctorPref, realKey, StringUtils.contains(key, RedisKeyConstants.DOCTOR_AUTO_INQUIRY_START));
            InquiryDoctorDO doctor = inquiryDoctorService.getInquiryDoctorByDoctorPref(doctorPref);
            if (StringUtils.contains(key, RedisKeyConstants.DOCTOR_AUTO_INQUIRY_START)) { // 操作出诊
                String orgProvinceCode = doctor == null ? null : Optional.ofNullable(doctorFilingMapper.selectOne(DoctorFilingDO::getDoctorId, doctor.getId())).map(DoctorFilingDO::getOrgProvinceCode).orElse(null);
                doctorRedisDao.doctorStartReceptionByAuto(doctorPref, realKey, orgProvinceCode);
            } else { // 操作停诊
                doctorRedisDao.doctorStopReceptionByAuto(doctorPref, realKey);
            }
            // 处理自动开方医生状态
            inquiryDoctorStatusService.handleAutoInquiryDoctorStatus(doctor);
            // 自动开方免签时间轮校验
            doctorAutoInquirySwitchProducer.sendMessage(
                DoctorAutoInquiryTimerWheelEvent.builder().msg(DoctorAutoInquiryTimerWheelMessage.builder().doctorPref(doctorPref).realKey(realKey).type(DoctorAutoInquiryTimerWheelEnum.PERSONNEL_CHECK_CA.getCode()).build()).build(),
                LocalDateTime.now().plusSeconds(10));
        }
    }

    /**
     * 初始化自动开方医生时间轮 为空初始化全部
     */
    @Lock4j(keys = "'" + RedisKeyConstants.DOCTOR_AUTO_INQUIRY_TIMER_WHEEL_LOCK_KEY + "_init'")
    public void initAutoInquiryDoctorTimerWheel() {
        int pageNo = 1;
        List<DoctorAutoInquiryTimerWheelDto> wheels = new ArrayList<>();
        Map<String, String> doctorOrgProvinceCodeMap = new HashMap<>();

        InquiryDoctorPageReqVO reqVO = InquiryDoctorPageReqVO.builder().build();
        reqVO.setPageSize(100);
        reqVO.setPageNo(pageNo);
        while (pageNo < 10000) {
            // 1.分页获取医生信息
            PageResult<InquiryDoctorRespVO> doctorPage = inquiryDoctorService.getInquiryDoctorPage(reqVO);
            if (doctorPage == null || CollUtil.isEmpty(doctorPage.getList())) {
                break;
            }
            reqVO.setPageNo(++pageNo);
            List<InquiryDoctorRespVO> doctorList = doctorPage.getList();
            // 获取医生自动开方时间轮key
            wheels.addAll(inquiryHospitalDetpDoctorRelationService.queryDoctorAutoInquiryTimeWheelDto(CollectionUtils.convertList(doctorList, InquiryDoctorRespVO::getPref)));
            // 获取医生备案省份
            Map<Long, String> filingMap = doctorFilingMapper.getByDoctorIds(CollectionUtils.convertList(doctorList, InquiryDoctorRespVO::getId)).stream()
                .collect(Collectors.toMap(DoctorFilingDO::getDoctorId, DoctorFilingDO::getOrgProvinceCode, (a, b) -> b));
            for (InquiryDoctorRespVO doctorDO : doctorList) {
                doctorOrgProvinceCodeMap.put(doctorDO.getPref(), filingMap.get(doctorDO.getId()));
            }
        }

        // 全部处理完后,重置轮子,加入新数据
        doctorRedisDao.initAutoInquiryDoctorTimerWheel(wheels, doctorOrgProvinceCodeMap);

    }

}
