package com.xyy.saas.inquiry.hospital.server.convert.doctor;

import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorVideoSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorVideoDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @ClassName：InquiryDoctorVideoConvert
 * @Author: xucao
 * @Date: 2025/3/10 20:03
 * @Description: 医生录屏对象转换类
 */
@Mapper
public interface InquiryDoctorVideoConvert {

    InquiryDoctorVideoConvert INSTANCE = Mappers.getMapper(InquiryDoctorVideoConvert.class);


    @Mapping(target = "pref", expression = "java(com.xyy.saas.inquiry.util.PrefUtil.getDoctorVideoPref())")
    InquiryDoctorVideoDO initConvertVO2DO(InquiryDoctorVideoSaveReqVO reqVO);
}