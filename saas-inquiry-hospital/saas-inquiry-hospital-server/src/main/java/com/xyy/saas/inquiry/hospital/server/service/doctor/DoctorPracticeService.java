package com.xyy.saas.inquiry.hospital.server.service.doctor;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorPracticePageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorPracticeSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorPracticeDO;
import jakarta.validation.Valid;


/**
 * 医生执业信息 Service 接口
 *
 * <AUTHOR>
 */
public interface DoctorPracticeService {

    /**
     * 创建医生执业信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDoctorPractice(@Valid DoctorPracticeSaveReqVO createReqVO);

    /**
     * 更新医生执业信息
     *
     * @param updateReqVO 更新信息
     */
    void updateDoctorPractice(@Valid DoctorPracticeSaveReqVO updateReqVO);

    /**
     * 删除医生执业信息
     *
     * @param id 编号
     */
    void deleteDoctorPractice(Long id);

    /**
     * 获得医生执业信息
     *
     * @param id 编号
     * @return 医生执业信息
     */
    DoctorPracticeDO getDoctorPractice(Long id);

    /**
     * 获得医生执业信息分页
     *
     * @param pageReqVO 分页查询
     * @return 医生执业信息分页
     */
    PageResult<DoctorPracticeDO> getDoctorPracticePage(DoctorPracticePageReqVO pageReqVO);

    /**
     * 获取医生执业信息，根据医生id
     *
     * @param id 医生id
     * @return
     */
    DoctorPracticeDO getDoctorPracticeByDoctorId(Long id);

    DoctorPracticeDO getDoctorPracticeByDoctorPref(String pref);
}