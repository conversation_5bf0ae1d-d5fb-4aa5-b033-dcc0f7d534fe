package com.xyy.saas.inquiry.hospital.server.api.hospital;

import com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalEmployeeApi;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalBindRespVO;
import com.xyy.saas.inquiry.hospital.server.service.hospital.HospitalEmployeeService;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;
import java.util.List;

/**
 * @Author: xucao
 * @DateTime: 2025/7/24 17:41
 * @Description: 医院员工api实现类
 **/
@DubboService
public class InquiryHospitalEmployeeApiImpl implements InquiryHospitalEmployeeApi {

    @Resource
    private HospitalEmployeeService hospitalEmployeeService;

    @Override
    public List<String> getHospitalByUserId(Long userId) {
        List<HospitalBindRespVO> bindHospitalsByUserId = hospitalEmployeeService.getBindHospitalsByUserId(userId);
        return bindHospitalsByUserId.stream().map(HospitalBindRespVO::getHospitalPref).toList();
    }
}
