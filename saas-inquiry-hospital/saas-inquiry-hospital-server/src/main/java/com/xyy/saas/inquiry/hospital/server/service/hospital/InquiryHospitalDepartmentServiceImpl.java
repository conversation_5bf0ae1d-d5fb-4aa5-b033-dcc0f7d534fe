package com.xyy.saas.inquiry.hospital.server.service.hospital;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_HOSPITAL_DEPARTMENT_EXISTS;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_HOSPITAL_DEPARTMENT_NOT_EXISTS;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.convert.hospital.InquiryHospitalDepartmentConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDepartmentDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital.InquiryHospitalDepartmentMapper;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;


/**
 * 科室字典 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InquiryHospitalDepartmentServiceImpl implements InquiryHospitalDepartmentService {

    @Resource
    private InquiryHospitalDepartmentMapper inquiryHospitalDepartmentMapper;

    @Override
    public Long createInquiryHospitalDepartment(InquiryHospitalDepartmentSaveReqVO createReqVO) {
        validateDeptName(null, createReqVO.getDeptName());
        // 插入
        InquiryHospitalDepartmentDO inquiryHospitalDepartment = InquiryHospitalDepartmentConvert.INSTANCE.initConvetVO2DO(createReqVO);
        inquiryHospitalDepartmentMapper.insert(inquiryHospitalDepartment);
        // 返回
        return inquiryHospitalDepartment.getId();
    }

    @Override
    public void updateInquiryHospitalDepartment(InquiryHospitalDepartmentSaveReqVO updateReqVO) {
        // 校验存在
        validateInquiryHospitalDepartmentExists(updateReqVO.getId());
        validateDeptName(updateReqVO.getId(), updateReqVO.getDeptName());
        // 更新
        InquiryHospitalDepartmentDO updateObj = BeanUtils.toBean(updateReqVO, InquiryHospitalDepartmentDO.class);
        inquiryHospitalDepartmentMapper.updateById(updateObj);
    }

    @Override
    public void deleteInquiryHospitalDepartment(Long id) {
        // 校验存在
        validateInquiryHospitalDepartmentExists(id);
        // 删除
        inquiryHospitalDepartmentMapper.deleteById(id);
    }

    private void validateInquiryHospitalDepartmentExists(Long id) {
        if (inquiryHospitalDepartmentMapper.selectById(id) == null) {
            throw exception(INQUIRY_HOSPITAL_DEPARTMENT_NOT_EXISTS);
        }
    }


    private void validateDeptName(Long id, String deptName) {
        if (StringUtils.isBlank(deptName)) {
            return;
        }
        InquiryHospitalDepartmentDO departmentDO = inquiryHospitalDepartmentMapper.selectOne(InquiryHospitalDepartmentDO::getDeptName, deptName);
        if (departmentDO == null) {
            return;
        }
        // 如果 id 为空，说明不用比较是否为相同 id 的数据
        if (id == null) {
            throw exception(INQUIRY_HOSPITAL_DEPARTMENT_EXISTS, deptName);
        }
        if (Objects.equals(departmentDO.getId(), id)) {
            throw exception(INQUIRY_HOSPITAL_DEPARTMENT_EXISTS, deptName);
        }
    }

    @Override
    public InquiryHospitalDepartmentDO getInquiryHospitalDepartment(Long id) {
        return inquiryHospitalDepartmentMapper.selectById(id);
    }

    @Override
    public PageResult<InquiryHospitalDepartmentDO> getInquiryHospitalDepartmentPage(InquiryHospitalDepartmentPageReqVO pageReqVO) {
        return inquiryHospitalDepartmentMapper.selectPage(pageReqVO);
    }

    /**
     * 查询全部科室信息
     *
     * @return 科室字典
     */
    @Override
    public List<InquiryHospitalDepartmentDO> getInquiryHospitalDepartmentAllList() {
        return inquiryHospitalDepartmentMapper.selectList();
    }

    /**
     * 查询一级科室信息
     *
     * @return 一级科室
     */
    @Override
    public List<InquiryHospitalDepartmentDO> queryFirstDeptList() {
        return inquiryHospitalDepartmentMapper.selectList(InquiryHospitalDepartmentDO::getDeptParentId, 0);
    }

    /**
     * 获取当前科室以及子科室列表（非树结构）
     *
     * @param currDeptId 当前科室id
     * @return 当前科室以及子科室列表
     */
    @Override
    public List<InquiryHospitalDepartmentDO> getCurrDeptAndChildDeptList(Long currDeptId) {
        if (ObjectUtils.isEmpty(currDeptId)) {
            return List.of();
        }
        return inquiryHospitalDepartmentMapper.selectCurrrDeptAndChildDeptList(currDeptId);
    }

    /**
     * 根据科室pref集合查询科室信息
     *
     * @param deptPrefList
     * @return
     */
    @Override
    public Map<String, InquiryHospitalDepartmentDO> getDeptPrefMap(List<String> deptPrefList) {
        if (ObjectUtils.isEmpty(deptPrefList)) {
            return Map.of();
        }
        return inquiryHospitalDepartmentMapper.selectList(InquiryHospitalDepartmentDO::getPref, deptPrefList)
            .stream().collect(Collectors.toMap(InquiryHospitalDepartmentDO::getPref, Function.identity(), (a, b) -> b));
    }

    /**
     * 条件查询科室列表-不分页
     *
     * @param pageReqVO 查询条件
     * @return 科室列表
     */
    @Override
    public List<InquiryHospitalDepartmentDO> selectDeptmentList(InquiryHospitalDepartmentPageReqVO pageReqVO) {
        return inquiryHospitalDepartmentMapper.selectDeptmentList(pageReqVO);
    }

    @Override
    public InquiryHospitalDepartmentDO getByPref(String deptPref) {
        return inquiryHospitalDepartmentMapper.selectOne(InquiryHospitalDepartmentDO::getPref, deptPref);
    }
}