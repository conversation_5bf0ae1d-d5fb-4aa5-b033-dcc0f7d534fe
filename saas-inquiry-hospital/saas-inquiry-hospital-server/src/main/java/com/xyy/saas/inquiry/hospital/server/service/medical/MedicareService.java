package com.xyy.saas.inquiry.hospital.server.service.medical;

import com.xyy.saas.inquiry.hospital.api.medicare.dto.MedicareRegistrationDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.medical.MedicareSigninRecordDO;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionTransmitterDTO;

/**
 * 医保服务接口
 *
 * <AUTHOR>
 */
public interface MedicareService {

    /**
     * 确保医保签到记录存在
     *
     * @param prescriptionRespVO 处方信息
     * @return 签到记录，失败返回null
     */
    MedicareSigninRecordDO ensureSigninRecord(InquiryPrescriptionRespVO prescriptionRespVO);

    /**
     * 医保挂号登记
     *
     * @param prescriptionRespVO 处方信息
     * @param transmitterDTO     传输数据
     * @return 是否成功
     */
    void saveMedicareRegistrationInfo(InquiryPrescriptionRespVO prescriptionRespVO,
        PrescriptionTransmitterDTO transmitterDTO, MedicareRegistrationDto registrationDto);
} 