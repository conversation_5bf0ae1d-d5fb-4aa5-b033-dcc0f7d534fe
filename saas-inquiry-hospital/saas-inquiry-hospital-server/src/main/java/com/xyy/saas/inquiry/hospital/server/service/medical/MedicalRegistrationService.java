package com.xyy.saas.inquiry.hospital.server.service.medical;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.hospital.server.controller.admin.medical.vo.MedicalRegistrationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.medical.vo.MedicalRegistrationRespVO;
import com.xyy.saas.inquiry.hospital.api.medicare.dto.MedicareRegistrationDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.hospital.api.medicare.dto.MedicalRegistrationInquiryUpdateDto;
import jakarta.validation.Valid;

/**
 * 医疗就诊登记(挂号) Service 接口
 *
 * <AUTHOR>
 */
public interface MedicalRegistrationService {

    /**
     * 去问诊时保存就诊登记记录,此时问诊记录还未落库
     *
     * @param inquiryDto 创建信息
     * @return 编号
     */
    Long saveInquiryMedicalRegistration(InquiryRecordDto inquiryDto);

    /**
     * 医生接诊时、医生开方后 调用此接口回更三方接口状态
     *
     * @param updateDto
     */
    void updateInquiryMedicalRegistrationStatus(@Valid MedicalRegistrationInquiryUpdateDto updateDto);

    /**
     * 获得医疗就诊登记(挂号)
     *
     * @param bizTypeEnum 业务线类型
     * @param bizId       业务id
     * @return
     */
    MedicalRegistrationRespVO getMedicalRegistrationInfo(BizTypeEnum bizTypeEnum, String bizId);

    /**
     * 保存医保挂号登记信息
     *
     * @param medicareRegistrationDto 医保挂号登记信息
     */
    void saveMedicareRegistrationInfo(MedicareRegistrationDto medicareRegistrationDto);

    /**
     * 获得医疗就诊登记(挂号)分页
     *
     * @param pageReqVO 分页查询
     * @return 医疗就诊登记(挂号)分页
     */
    CommonResult<PageResult<MedicalRegistrationRespVO>> queryRegistPage(MedicalRegistrationPageReqVO pageReqVO);

    /**
     * 撤销医保挂号
     *
     * @param registrationPref 挂号流水号
     * @return 撤销结果
     */
    CommonResult<Boolean> cancelMedicalRegistration(String registrationPref);

}
