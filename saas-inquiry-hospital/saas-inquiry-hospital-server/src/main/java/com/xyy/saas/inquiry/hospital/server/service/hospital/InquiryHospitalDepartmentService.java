package com.xyy.saas.inquiry.hospital.server.service.hospital;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDepartmentDO;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;


/**
 * 科室字典 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryHospitalDepartmentService {

    /**
     * 创建科室字典
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInquiryHospitalDepartment(@Valid InquiryHospitalDepartmentSaveReqVO createReqVO);

    /**
     * 更新科室字典
     *
     * @param updateReqVO 更新信息
     */
    void updateInquiryHospitalDepartment(@Valid InquiryHospitalDepartmentSaveReqVO updateReqVO);

    /**
     * 删除科室字典
     *
     * @param id 编号
     */
    void deleteInquiryHospitalDepartment(Long id);

    /**
     * 获得科室字典
     *
     * @param id 编号
     * @return 科室字典
     */
    InquiryHospitalDepartmentDO getInquiryHospitalDepartment(Long id);

    /**
     * 获得科室字典分页
     *
     * @param pageReqVO 分页查询
     * @return 科室字典分页
     */
    PageResult<InquiryHospitalDepartmentDO> getInquiryHospitalDepartmentPage(InquiryHospitalDepartmentPageReqVO pageReqVO);

    /**
     * 查询全部科室信息
     *
     * @return
     */
    List<InquiryHospitalDepartmentDO> getInquiryHospitalDepartmentAllList();

    /**
     * 查询一级科室信息
     *
     * @return
     */
    List<InquiryHospitalDepartmentDO> queryFirstDeptList();

    /**
     * 获取当前科室以及子科室列表（非树结构）
     *
     * @param currDeptId
     * @return
     */
    List<InquiryHospitalDepartmentDO> getCurrDeptAndChildDeptList(Long currDeptId);

    /**
     * 获取当前科室列表（非树结构）
     *
     * @param deptPrefList
     * @return
     */
    Map<String, InquiryHospitalDepartmentDO> getDeptPrefMap(List<String> deptPrefList);


    /**
     * 条件查询科室列表-不分页
     *
     * @return
     */
    List<InquiryHospitalDepartmentDO> selectDeptmentList(InquiryHospitalDepartmentPageReqVO createReqVO);

    /**
     * 根据科室编码查询科室信息
     *
     * @param deptPref 科室编码
     * @return
     */
    InquiryHospitalDepartmentDO getByPref(String deptPref);
}