package com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 医生录屏记录 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_doctor_video")
@KeySequence("saas_inquiry_doctor_video_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryDoctorVideoDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 录屏编号
     */
    private String pref;
    /**
     * 医生pref
     */
    private String doctorPref;
    /**
     * 医生端问诊视频url地址
     */
    private String videoUrl;
    /**
     * 视频md5
     */
    private String md5;
    /**
     * 启用：0是，1否
     */
    private Integer status;

}