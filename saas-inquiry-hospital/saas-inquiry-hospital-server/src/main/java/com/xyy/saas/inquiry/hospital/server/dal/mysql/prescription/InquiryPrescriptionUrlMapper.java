package com.xyy.saas.inquiry.hospital.server.dal.mysql.prescription;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionUrlDO;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.ibatis.annotations.Mapper;

/**
 * 处方图片扩展 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryPrescriptionUrlMapper extends BaseMapperX<InquiryPrescriptionUrlDO> {


    default InquiryPrescriptionUrlDO selectByPrefType(String pref, Integer dateType) {
        return selectOne(new LambdaQueryWrapper<InquiryPrescriptionUrlDO>().eq(InquiryPrescriptionUrlDO::getPref, pref)
            .eq(InquiryPrescriptionUrlDO::getDateType, dateType), false);
    }

    default List<InquiryPrescriptionUrlDO> selectByPrefsType(List<String> prefs, Integer dateType) {
        return selectList(new LambdaQueryWrapper<InquiryPrescriptionUrlDO>().in(InquiryPrescriptionUrlDO::getPref, prefs)
            .eq(InquiryPrescriptionUrlDO::getDateType, dateType));
    }

    default Map<String, InquiryPrescriptionUrlDO> selectByPrefMap(List<String> prefs, Integer dateType) {
        return selectByPrefsType(prefs, dateType).stream().collect(Collectors.toMap(InquiryPrescriptionUrlDO::getPref, Function.identity(), (k1, k2) -> k2));
    }

}