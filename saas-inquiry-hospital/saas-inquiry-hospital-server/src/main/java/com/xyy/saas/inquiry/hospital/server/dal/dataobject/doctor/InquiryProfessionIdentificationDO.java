package com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xyy.saas.inquiry.enums.user.CertificateTypeEnum;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 问诊职业(医生药师)证件信息 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_profession_identification")
@KeySequence("saas_inquiry_profession_identification_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryProfessionIdentificationDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 医生|药师id
     */
    private Long personId;
    /**
     * 医生类型 {@link DoctorTypeEnum}
     */
    private Integer doctorType;

    /**
     * 证件类型 {@link CertificateTypeEnum}
     */
    private Integer certificateType;
    /**
     * 证件名称
     */
    private String certificateName;
    /**
     * 证件号
     */
    private String certificateNo;
    /**
     * 证件地址
     */
    private String certificateImgUrl;
    /**
     * 注册发证日期
     */
    private LocalDateTime registerTime;
    /**
     * 有效期
     */
    private LocalDateTime validTime;

}