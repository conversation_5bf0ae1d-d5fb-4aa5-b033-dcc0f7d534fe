package com.xyy.saas.inquiry.hospital.server.convert.indentification;

import com.xyy.saas.inquiry.hospital.api.doctor.dto.identification.InquiryProfessionIdentificationDto;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryProfessionIdentificationDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.Collection;
import java.util.List;

/**
 * 资质证件信息
 *
 * @Author:chenxia<PERSON>i
 * @Date:2024/10/12 17:17
 */
@Mapper
public interface InquiryProfessionIdentificationConvert {

    InquiryProfessionIdentificationConvert INSTANCE = Mappers.getMapper(InquiryProfessionIdentificationConvert.class);

    Collection<InquiryProfessionIdentificationDO> convert(List<InquiryProfessionIdentificationDto> professionIdentifications);

    @Mapping(target = "id", ignore = true)
    InquiryProfessionIdentificationDO convert(InquiryProfessionIdentificationDto professionIdentification);


    InquiryProfessionIdentificationDto convert(InquiryProfessionIdentificationDO professionIdentifications);

    List<InquiryProfessionIdentificationDto> converts(List<InquiryProfessionIdentificationDO> identificationDOS);
}
