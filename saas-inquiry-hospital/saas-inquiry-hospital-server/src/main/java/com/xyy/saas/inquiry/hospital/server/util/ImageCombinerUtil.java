package com.xyy.saas.inquiry.hospital.server.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HttpUtil;
import java.awt.Color;
import java.awt.Graphics2D;
import java.awt.RenderingHints;
import java.awt.image.BufferedImage;
import java.io.File;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * 图片合并处理器
 */
public class ImageCombinerUtil {

    // public static void main(String[] args) {
    //     final List<P> pList = List.of(P.builder().pdfUrl("http://files.test.ybm100.com/INVT/Lzinq/20250225/c1f2c2af48ec6b78adaab07ca31672af3a4045f0fa125762b6a7959560ebbd4b.jpg").build()
    //         , P.builder().pdfUrl("http://files.test.ybm100.com/INVT/Lzinq/20250109/f40b596fc9dd8b2d2f3e5dd2eb4ceff8771b59e99a256996d7e9bd1ef978f0bc.jpg").build()
    //         , P.builder().pdfUrl("http://files.test.ybm100.com/INVT/Lzinq/20250109/47552c02cb59b3a038877ee827f90f13cc808851858018a170335aff877e0b62.jpg").build()
    //         , P.builder().pdfUrl("http://files.test.ybm100.com/INVT/Lzinq/20250109/6fe6bc55c17b4b258f77e87fb26b4ac9bda832670167228c87bd45ac8a1ae7f4.jpg").build()
    //         , P.builder().pdfUrl("http://files.test.ybm100.com/INVT/Lzinq/20250109/45e3429847bfe10c238b295ba9e52fa8e2d86b257f99ff00bb560e0841b90c7b.jpg").build()
    //         , P.builder().pdfUrl("http://files.test.ybm100.com/INVT/Lzinq/20250110/64781da8b57ae8594a94491711c4b90f872f318d4ad89f6bf2bc5550ad168ad2.jpg").build());
    //
    //
    //
    // }

    // A4纸张像素大小
    static float DPI = 300;
    static final int PAPER_WIDTH = (int) (210 * DPI / 25.4);  // ≈2480像素
    static final int PAPER_HEIGHT = (int) (297 * DPI / 25.4); // ≈3508像素

    /**
     * 合并图片返回文件
     *
     * @param urls      图片pdfUrl链接
     * @param groupSize 一张A4纸张放多少图
     * @return
     */
    public static List<File> combineImages(List<String> urls, int groupSize) {
        if (CollUtil.isEmpty(urls)) {
            return List.of();
        }

        // 根据 groupSize 决定纸张方向
        int paperWidth, paperHeight;
        if (groupSize == 2) {
            paperWidth = PAPER_HEIGHT;  // 横向：高度变宽度
            paperHeight = PAPER_WIDTH;  // 横向：宽度变高度
        } else {
            paperWidth = PAPER_WIDTH;
            paperHeight = PAPER_HEIGHT;
        }

        List<BufferedImage> bufferedImages = combineImages(urls, groupSize, paperWidth, paperHeight);

        return bufferedImages.stream().map(i -> {
            File targetFile = new File("/tmp/" + IdUtil.simpleUUID() + ".png");
            ImgUtil.write(i, targetFile);
            return targetFile;
        }).toList();
    }


    /**
     * 将图片列表拼接成田字格排列的大图
     *
     * @param plist       包含网络图片URL的对象列表
     * @param paperWidth  输出纸张宽度（像素）
     * @param paperHeight 输出纸张高度（像素）
     * @return 拼接后的图片列表（每张对应一个田字格）
     */
    public static List<BufferedImage> combineImages(List<String> plist, int groupSize, int paperWidth, int paperHeight) {
        List<BufferedImage> result = new ArrayList<>();
        // 计算子图尺寸（根据 groupSize 动态计算）
        int subWidth, subHeight;
        if (groupSize == 2) {
            subWidth = paperWidth / 2;  // 横向分半
            subHeight = paperHeight;    // 全高
        } else {
            subWidth = paperWidth / 2;
            subHeight = paperHeight / 2;
        }

        // 将原始列表按4个一组分组
        List<List<String>> groups = splitIntoGroups(plist, groupSize);

        // 使用 CompletableFuture 并行处理每个分组
        List<CompletableFuture<BufferedImage>> futures = new ArrayList<>();

        for (List<String> group : groups) {

            CompletableFuture<BufferedImage> future = CompletableFuture.supplyAsync(() -> {
                // 创建新画布
                BufferedImage paperImage = createPaperImage(paperWidth, paperHeight);
                Graphics2D graphics = paperImage.createGraphics();

                // 新增高质量绘图参数
                graphics.setRenderingHint(RenderingHints.KEY_ANTIALIASING,
                    RenderingHints.VALUE_ANTIALIAS_ON);
                graphics.setRenderingHint(RenderingHints.KEY_RENDERING,
                    RenderingHints.VALUE_RENDER_QUALITY);
                graphics.setRenderingHint(RenderingHints.KEY_COLOR_RENDERING,
                    RenderingHints.VALUE_COLOR_RENDER_QUALITY);
                // 设置渲染参数
                graphics.setRenderingHint(RenderingHints.KEY_INTERPOLATION,
                    RenderingHints.VALUE_INTERPOLATION_BILINEAR);

                // 处理每个子图片
                for (int i = 0; i < group.size(); i++) {
                    String p = group.get(i);
                    try {
                        BufferedImage subImage = processSingleImage(p, subWidth, subHeight);
                        // 动态计算坐标
                        int x, y;
                        if (groupSize == 2) {
                            x = i * subWidth;  // 横向排列
                            y = 0;             // 顶部对齐
                        } else {
                            x = (i % 2) * subWidth;
                            y = (i / 2) * subHeight;
                        }
                        graphics.drawImage(subImage, x, y, null);
                    } catch (Exception e) {
                        System.err.println("图片处理失败留白处理: " + p);
                    }
                }
                graphics.dispose();
                return paperImage;
            });
            futures.add(future);
        }
        // 等待所有任务完成并收集结果
        futures.forEach(future -> {
            try {
                result.add(future.join());
            } catch (Exception e) {
                System.err.println("图片组合失败: " + e.getMessage());
            }
        });
        return result;
    }

    /**
     * 处理单个图片：下载+缩放+居中
     */
    private static BufferedImage processSingleImage(String imageUrl, int targetWidth, int targetHeight) {

        // 下载图片（保持原逻辑）
        byte[] bytes = HttpUtil.downloadBytes(imageUrl);
        BufferedImage original = ImgUtil.toImage(bytes);

        // 计算缩放比例
        double ratio = Math.min(
            (double) targetWidth / original.getWidth(),
            (double) targetHeight / original.getHeight()
        );

        // 高质量缩放实现
        int scaledWidth = (int) (original.getWidth() * ratio);
        int scaledHeight = (int) (original.getHeight() * ratio);
        BufferedImage scaled = new BufferedImage(scaledWidth, scaledHeight, BufferedImage.TYPE_INT_RGB);

        Graphics2D gScale = scaled.createGraphics();
        gScale.setRenderingHint(RenderingHints.KEY_INTERPOLATION,
            RenderingHints.VALUE_INTERPOLATION_BICUBIC);
        gScale.setRenderingHint(RenderingHints.KEY_RENDERING,
            RenderingHints.VALUE_RENDER_QUALITY);
        gScale.drawImage(original, 0, 0, scaledWidth, scaledHeight, null);
        gScale.dispose();

        // 创建目标画布（带白底）
        BufferedImage targetImage = new BufferedImage(
            targetWidth, targetHeight, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = targetImage.createGraphics();

        // 设置高质量绘制参数
        g.setRenderingHint(RenderingHints.KEY_ANTIALIASING,
            RenderingHints.VALUE_ANTIALIAS_ON);
        g.setColor(Color.WHITE);
        g.fillRect(0, 0, targetWidth, targetHeight);

        // 居中绘制
        int x = (targetWidth - scaled.getWidth()) / 2;
        int y = (targetHeight - scaled.getHeight()) / 2;
        g.drawImage(scaled, x, y, null);
        g.dispose();

        return targetImage;
    }

    /**
     * 创建指定尺寸的白底画布
     */
    private static BufferedImage createPaperImage(int width, int height) {
        BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
        Graphics2D g = image.createGraphics();
        g.setColor(Color.WHITE);
        g.fillRect(0, 0, width, height);
        g.dispose();
        return image;
    }

    /**
     * 将列表按指定大小分组
     */
    private static List<List<String>> splitIntoGroups(List<String> list, int groupSize) {
        List<List<String>> groups = new ArrayList<>();
        for (int i = 0; i < list.size(); i += groupSize) {
            int end = Math.min(i + groupSize, list.size());
            groups.add(list.subList(i, end));
        }
        return groups;
    }
}