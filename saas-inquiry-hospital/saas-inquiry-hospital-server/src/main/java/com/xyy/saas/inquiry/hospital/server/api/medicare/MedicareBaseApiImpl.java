package com.xyy.saas.inquiry.hospital.server.api.medicare;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.xyy.saas.inquiry.hospital.api.medicare.MedicareBaseApi;
import com.xyy.saas.inquiry.hospital.api.medicare.dto.MedicareSignDTO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.convert.medical.MedicareConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.medical.MedicareSigninRecordDO;
import com.xyy.saas.inquiry.hospital.server.service.medical.MedicareService;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * @Author: xucao
 * @DateTime: 2025/7/18 17:05
 * @Description: 医院签到信息查询接口实现类
 **/
@DubboService
public class MedicareBaseApiImpl implements MedicareBaseApi {

    @Resource
    private MedicareService medicareService;


    /**
     * 根据医院编码查询当前医院的签到号
     *
     * @param hospitalPref
     * @return
     */
    @Override
    public MedicareSignDTO getMedicareSignInfo(String hospitalPref) {
        MedicareSigninRecordDO medicareSigninRecordDO = medicareService.ensureSigninRecord(InquiryPrescriptionRespVO.builder().hospitalPref(hospitalPref).tenantId(TenantContextHolder.getTenantId()).build());
        return MedicareConvert.INSTANCE.getMedicareSignInfo(medicareSigninRecordDO);
    }
}
