package com.xyy.saas.inquiry.hospital.server.service.message;

import com.xyy.saas.inquiry.enums.im.ImEventPushEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.clinicalcase.InquiryClinicalCaseDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorReviewsDO;
import com.xyy.saas.inquiry.im.enums.PushContentEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import java.util.List;

/**
 * 医生im消息service
 *
 * @Author:chenxiaoyi
 * @Date:2025/03/05 14:34
 */
public interface DoctorImService {

    /**
     * 发送开具处方相关IM消息
     *
     * @param prescriptionRespDTO
     * @param inquiryPref
     */
    void sendPrescriptionImMessage(InquiryPrescriptionRespDTO prescriptionRespDTO, String inquiryPref ,ImEventPushEnum eventEnum);

    /**
     * 发送门诊病例IM消息
     */
    void sendClinicalCaseImMessage(InquiryClinicalCaseDO clinicalCaseDO, InquiryRecordDto inquiryRecordDto);

    /**
     * 发送患者评价医生通知消息
     * @param inquiryPref 问诊单号
     */
    void sendDoctorReviewsImMessage(String inquiryPref);

    /**
     * 批量通知医生问诊单发生变化
     * @param doctorList
     */
    void batchNotifyDoctorForInquiryChange(List<String> doctorList);

    /**
     * 批量推送通知医生
     * @param doctorList 医生列表
     * @param pushContentEnum 推送内容枚举
     */
    void batchPushNotifyMessage(List<String> doctorList, PushContentEnum pushContentEnum , InquiryRecordDto inquiryDto);
}
