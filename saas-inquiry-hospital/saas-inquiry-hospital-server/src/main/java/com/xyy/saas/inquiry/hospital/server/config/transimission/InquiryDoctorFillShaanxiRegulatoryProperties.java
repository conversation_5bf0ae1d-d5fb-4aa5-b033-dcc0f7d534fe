package com.xyy.saas.inquiry.hospital.server.config.transimission;


import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.transmission.DoctorExternalTransmissionRespDto;
import com.xyy.saas.inquiry.util.SpelParserUtil;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * desc 陕西医保备案配置
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */

@Data
@Configuration
@ConfigurationProperties(prefix = "inquiry.doctor.fill-shaanxi-regulatory")
public class InquiryDoctorFillShaanxiRegulatoryProperties {

    /**
     * 医师诊疗业务备案
     */
    private FillApiProperties physicianDiagnosisTreatmentRecordApi;
    /**
     * 医疗人员信息上报
     */
    private FillApiProperties medicalPersonnelInformationReportApi;


    @Data
    public static class FillApiProperties {
        // 接口名称
        private String name;

        // 判断接口调用是否成功的表达式，配置为空则默认成功
        private String evalSuccessExpr;


        /**
         * 判断接口调用是否成功
         * @param respDtoList
         * @return
         */
        public boolean evalSuccess(List<DoctorExternalTransmissionRespDto> respDtoList) {
            if (StringUtils.isBlank(evalSuccessExpr)) {
                return true;
            }
            return SpelParserUtil.parseBoolean(evalSuccessExpr, Map.of("data", respDtoList));
        }
    }

    /**
     * 获取医师诊疗业务备案接口名称
     * @return
     */
    @JsonIgnore
    public String getPhysicianDiagnosisTreatmentRecordApiName() {
        String apiName = Optional.ofNullable(physicianDiagnosisTreatmentRecordApi).map(FillApiProperties::getName).orElse("");
        if (StringUtils.isEmpty(apiName)) {
            return NodeTypeEnum.INTERNET_SUPERVISION_PHYSICIAN_DIAGNOSIS_TREATMENT_RECORD.getDesc();
        }
        return apiName;
    }

    /**
     * 判断医师诊疗业务备案接口是否调用成功
     * @param respDtoList
     * @return
     */
    @JsonIgnore
    public boolean isPhysicianDiagnosisTreatmentRecordApiSuccess(List<DoctorExternalTransmissionRespDto> respDtoList) {
        return Optional.ofNullable(physicianDiagnosisTreatmentRecordApi)
            .map(apiProperties -> apiProperties.evalSuccess(respDtoList))
            .orElse(true);
    }

    /**
     * 获取医疗人员信息上报接口名称
     * @return
     */
    @JsonIgnore
    public String getMedicalPersonnelInformationReportApiName() {
        String apiName = Optional.ofNullable(medicalPersonnelInformationReportApi).map(FillApiProperties::getName).orElse("");
        if (StringUtils.isEmpty(apiName)) {
            return NodeTypeEnum.INTERNET_SUPERVISION_MEDICAL_PERSONNEL_INFORMATION_REPORT.getDesc();
        }
        return apiName;
    }

    /**
     * 判断医疗人员信息上报接口是否调用成功
     * @param respDtoList
     * @return
     */
    @JsonIgnore
    public boolean isMedicalPersonnelInformationReportApiSuccess(List<DoctorExternalTransmissionRespDto> respDtoList) {
        return Optional.ofNullable(medicalPersonnelInformationReportApi)
            .map(apiProperties -> apiProperties.evalSuccess(respDtoList))
            .orElse(true);
    }
}
