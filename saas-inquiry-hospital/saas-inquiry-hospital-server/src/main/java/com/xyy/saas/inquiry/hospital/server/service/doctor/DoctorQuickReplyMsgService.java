package com.xyy.saas.inquiry.hospital.server.service.doctor;

import com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo.DoctorQuickReplyMsgGroupRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo.DoctorQuickReplyMsgPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo.DoctorQuickReplyMsgSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorQuickReplyMsgDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 医生快捷回复语 Service 接口
 *
 * <AUTHOR>
 */
public interface DoctorQuickReplyMsgService {

    /**
     * 创建医生快捷回复语
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDoctorQuickReplyMsg(@Valid DoctorQuickReplyMsgSaveReqVO createReqVO);

    /**
     * 更新医生快捷回复语
     *
     * @param updateReqVO 更新信息
     */
    void updateDoctorQuickReplyMsg(@Valid DoctorQuickReplyMsgSaveReqVO updateReqVO);

    /**
     * 删除医生快捷回复语
     *
     * @param id 编号
     */
    void deleteDoctorQuickReplyMsg(Long id);


    /**
     * 批量删除医生快捷回复语
     *
     * @param ids 编号
     */
    void batchDeleteDoctorQuickReplyMsg(Long[] ids);

    /**
     * 获得医生快捷回复语
     *
     * @param id 编号
     * @return 医生快捷回复语
     */
    DoctorQuickReplyMsgDO getDoctorQuickReplyMsg(Long id);

    /**
     * 获得医生快捷回复语list
     *
     * @param pageReqVO 分页查询
     * @return 医生快捷回复语List
     */
    List<DoctorQuickReplyMsgGroupRespVO> getDoctorQuickReplyMsgList(DoctorQuickReplyMsgPageReqVO pageReqVO);


    /**
     * 初始化医生常用语
     *
     * @param doctorId 医生id
     */
    void initDoctorQuickReplyMsg(Long doctorId);


}