package com.xyy.saas.inquiry.hospital.server.job;

import cn.hutool.core.util.ObjectUtil;
import com.xyy.saas.inquiry.constant.RedisKeyConstants;
import com.xyy.saas.inquiry.enums.doctor.AutoGrabStatusEnum;
import com.xyy.saas.inquiry.enums.doctor.OnlineStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryStatusEnum;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorService;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryQueryDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.util.RedisUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 医院模块Redis缓存清理定时任务 每天凌晨3点执行，检查和清理无效的Redis缓存key
 *
 * <AUTHOR>
 * @date 2024/12/27
 */
@Component
@Slf4j
public class HospitalRedisCacheCleanupJob {

    private static final String CLEANUP_LOCK_KEY = "hospital:cache:cleanup:lock";
    private static final int LOCK_TIMEOUT_HOURS = 2; // 锁超时时间2小时

    /**
     * 接诊大厅keys
     */
    private static final String CLEAN_RECEPTION_AREA_KEY = "hospital:recetion:*";

    /**
     * 自动抢单接诊中待处理问诊单keys
     */
    private static final String CLEAN_DOCTOR_AUTOGRAB_CURRENT_KEY = "doctor:current:autograb:*";

    /**
     * 开启自动抢单医生队列的key
     */
    private static final String CLEAN_DOCTOR_OPEN_AUTOGRAB_KEY = "doctor:autograb:*";

    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @DubboReference
    private InquiryApi inquiryApi;

    @Resource
    private InquiryDoctorService inquiryDoctorService;

    public void manualCleanup() {
        cleanupExpiredHospitalCache();
    }

    /**
     * 定时清理医院相关的无效Redis缓存 每天凌晨3点执行
     */
    @Scheduled(cron = "0 0 3 * * ?")
    public void cleanupExpiredHospitalCache() {
        String lockValue = String.valueOf(System.currentTimeMillis());
        boolean lockAcquired = false;

        try {
            // 尝试获取分布式锁，避免集群中多个实例同时执行
            lockAcquired = stringRedisTemplate.opsForValue().setIfAbsent(CLEANUP_LOCK_KEY, lockValue, LOCK_TIMEOUT_HOURS, TimeUnit.HOURS);

            if (!lockAcquired) {
                log.info("医院Redis缓存清理任务正在其他实例执行，跳过此次执行");
                return;
            }

            log.info("开始执行医院Redis缓存清理任务");
            long startTime = System.currentTimeMillis();

            // 清理各类医院相关的Redis key
            int totalCleaned = 0;
            // 清理接诊大厅的key
            totalCleaned = totalCleaned + cleanupKeys(CLEAN_RECEPTION_AREA_KEY, InquiryStatusEnum.QUEUING);
            // 清理医生自动抢单keu
            totalCleaned = totalCleaned + cleanupKeys(CLEAN_DOCTOR_AUTOGRAB_CURRENT_KEY, InquiryStatusEnum.INQUIRING);
            // 自动抢单医生队列的key
            totalCleaned = totalCleaned + cleanupKeys(CLEAN_DOCTOR_OPEN_AUTOGRAB_KEY, null);

            long duration = System.currentTimeMillis() - startTime;
            log.info("医院Redis缓存清理任务完成，共清理{}个无效key，耗时{}ms", totalCleaned, duration);

        } catch (Exception e) {
            log.error("医院Redis缓存清理任务执行失败", e);
        } finally {
            // 释放分布式锁
            if (lockAcquired) {
                try {
                    String currentValue = stringRedisTemplate.opsForValue().get(CLEANUP_LOCK_KEY);
                    if (lockValue.equals(currentValue)) {
                        stringRedisTemplate.delete(CLEANUP_LOCK_KEY);
                        log.info("释放医院缓存清理任务分布式锁");
                    }
                } catch (Exception e) {
                    log.warn("释放分布式锁失败", e);
                }
            }
        }
    }

    private int cleanupKeys(String pattern, InquiryStatusEnum statusEnum) {
        try {
            Set<String> keys = stringRedisTemplate.keys(pattern);
            if (keys.isEmpty()) {
                return 0;
            }
            int cleanedCount = 0;
            for (String key : keys) {
                if(ObjectUtil.isEmpty(statusEnum)){
                    cleanedCount = cleanedCount + cleanupKeyForDoctor(key);
                }
                cleanedCount = cleanedCount + cleanupKeyForInquiry(key, statusEnum);
            }
            return cleanedCount;
        } catch (Exception e) {
            log.error("清理{}类型的key失败: {}", pattern, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 清理指定类型的key
     *
     * @param key        待清理的key
     * @param statusEnum 状态枚举
     * @return 清理的key数量
     */
    private int cleanupKeyForInquiry(String key, InquiryStatusEnum statusEnum) {
        int cleanedCount = 0;
        try {
            // 获取队列中的问诊单
            List<String> inquiryPrefs = new ArrayList<>(RedisUtils.lGetAll(key).stream().map(Object::toString).toList());
            if(CollectionUtils.isEmpty(inquiryPrefs)){
                return cleanedCount;
            }
            // 获取问诊单信息
            Map<String,InquiryRecordDto> inquiryMap = inquiryApi.getInquiryRecordList(InquiryQueryDto.builder().prefs(inquiryPrefs.stream().distinct().toList()).build()).stream().collect(
                Collectors.toMap(InquiryRecordDto::getPref, inquiryRecordDto -> inquiryRecordDto,(k1,k2)->k2));
            for(String inquiryPref : inquiryPrefs){
                InquiryRecordDto inquiryRecordDto = inquiryMap.get(inquiryPref);
                if(inquiryRecordDto==null || ObjectUtil.notEqual(inquiryRecordDto.getInquiryStatus(),statusEnum.getStatusCode())){
                    RedisUtils.lRem(key, inquiryPref);
                    cleanedCount++;
                }
            }
        } catch (Exception e) {
            log.error("清理key失败: {},原因:{}", key, e.getMessage());
        }
        return cleanedCount;
    }

    /**
     * 清理指定类型的key
     *
     * @param key 待清理的key
     * @return 清理的key数量
     */
    private int cleanupKeyForDoctor(String key) {
        int cleanedCount = 0;
        try {
            // 获取当前队列的环境
            String env = Arrays.asList(key.split(":")).getLast();
            // 获取当前队列的医生
            List<String> doctorPrefs = new ArrayList<>(RedisUtils.lGetAll(key).stream().map(Object::toString).toList());
            // 查询医生信息
            Map<String,InquiryDoctorDO> doctorMap = inquiryDoctorService.getInquiryDoctorList(InquiryDoctorPageReqVO.builder().doctorPrefs(doctorPrefs.stream().distinct().toList()).build()).stream().collect(Collectors.toMap(
                InquiryDoctorDO::getPref, inquiryDoctorDO -> inquiryDoctorDO,(k1,k2) ->k2)
            );
            for(String doctorPref : doctorPrefs){
                InquiryDoctorDO doctor = doctorMap.get(doctorPref);
                //  doctor不存在或者 doctor环境不匹配==直接从自动接诊队列移除
                if(doctor==null || !StringUtils.equals(doctor.getEnvTag(),env)){
                    RedisUtils.lRem(key, doctorPref);
                    cleanedCount++;
                    continue;
                }
                // doctor 停诊 或  未开启自动抢单 == 直接从自动接诊队列移除
                if(ObjectUtil.equals(doctor.getOnlineStatus(), OnlineStatusEnum.OFFLINE) || ObjectUtil.equals(doctor.getAutoGrabStatus(), AutoGrabStatusEnum.CLOSE.getCode())){
                    RedisUtils.lRem(key, doctorPref);
                    cleanedCount++;
                }
            }
            return cleanedCount;
        } catch (Exception e) {
            log.error("清理key失败: {},原因:{}", key, e.getMessage());
        }
        return cleanedCount;
    }


} 