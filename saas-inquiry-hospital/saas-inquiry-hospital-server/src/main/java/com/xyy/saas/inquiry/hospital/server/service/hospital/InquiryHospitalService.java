package com.xyy.saas.inquiry.hospital.server.service.hospital;


import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalReqDto;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.service.prescription.dto.IssuesPrescriptionConfigDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import jakarta.validation.Valid;
import java.util.List;
import java.util.Map;
import java.util.Set;


/**
 * 医院信息 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryHospitalService {

    /**
     * 创建医院信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInquiryHospital(@Valid InquiryHospitalSaveReqVO createReqVO);

    /**
     * 更新医院信息
     *
     * @param updateReqVO 更新信息
     */
    void updateInquiryHospital(@Valid InquiryHospitalSaveReqVO updateReqVO);

    /**
     * 删除医院信息
     *
     * @param id 编号
     */
    void deleteInquiryHospital(Long id);

    /**
     * 获得医院信息
     *
     * @param id 编号
     * @return 医院信息
     */
    InquiryHospitalRespDto getInquiryHospital(Long id);


    /**
     * 获得医院信息
     *
     * @param pref 医院编码
     * @return 医院信息
     */
    InquiryHospitalRespDto getInquiryHospital(String pref);

    /**
     * 根据名称查询医院信息
     *
     * @param name
     * @return
     */
    InquiryHospitalRespDto getInquiryHospitalByName(String name);

    /**
     * 根据编码查询医院信息
     *
     * @param pref
     * @return
     */
    InquiryHospitalRespDto getInquiryHospitalInfo(String pref);


    /**
     * 获得医院信息分页
     *
     * @param pageReqVO 分页查询
     * @return 医院信息分页
     */
    PageResult<InquiryHospitalRespDto> getInquiryHospitalPage(InquiryHospitalPageReqVO pageReqVO);

    /**
     * 获取所有互联网医院列表
     *
     * @return 医院信息
     */
    List<InquiryHospitalRespDto> getInquiryHospitalAllList();

    /**
     * 获取医院列表
     *
     * @param reqDto 条件
     * @return 医院列表
     */
    List<InquiryHospitalRespDto> getInquiryHospitals(InquiryHospitalReqDto reqDto);

    /**
     * 查询医院基础信息
     *
     * @param reqDto 条件
     * @return 医院列表
     */
    List<InquiryHospitalRespDto> getInquiryHospitalsBaseInfo(InquiryHospitalReqDto reqDto);

    /**
     * 获取医院列表根据状态
     *
     * @param disable 启用禁用
     * @return 医院列表
     */
    List<InquiryHospitalRespDto> getInquiryHospitalListByDisable(Integer disable);


    /**
     * DTO 转换为 VO  组装配置信息以及关联名称
     *
     * @param dto
     * @return
     */
    InquiryHospitalRespVO convertDto2VO(InquiryHospitalRespDto dto);

    /**
     * DTO 转换为 VO  组装配置信息以及关联名称
     *
     * @param dtoList
     * @return
     */
    List<InquiryHospitalRespVO> convertDto2VOList(List<InquiryHospitalRespDto> dtoList);


    /**
     * 根据问诊记录和问诊明细 获取当前门店问诊处方笺
     *
     * @param inquiryRecordDto       问诊记录
     * @param inquiryRecordDetailDto 问诊明细
     * @return
     */
    IssuesPrescriptionConfigDto getHospitalPrescriptionConfigByInquiry(InquiryRecordDto inquiryRecordDto, InquiryRecordDetailDto inquiryRecordDetailDto);


    /**
     * 查询关联使用了指定处方笺模板的医院列表
     *
     * @param presTempIdList 请求入参
     * @return 医院id : 医院信息 map
     */
    Map<Long, Set<InquiryHospitalRespDto>> getPresTempUsedHospitalMap(List<Long> presTempIdList);

    /**
     * 根据医生pref查询医院信息
     *
     * @param userId userid
     * @return 医院信息
     */
    List<InquiryHospitalRespDto> getInquiryHospitalsByDoctorUser(Long userId);



    /**
     * 校验特殊处方CA
     *
     * @param updateReqVO
     * @return
     */
    CommonResult<String> validSpecialCa(@Valid InquiryHospitalSaveReqVO updateReqVO);
}