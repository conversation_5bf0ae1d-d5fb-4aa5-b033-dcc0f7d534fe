package com.xyy.saas.inquiry.hospital.server.convert.doctor;

import com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo.DoctorQuickReplyMsgRespVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorQuickReplyMsgDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @ClassName：QuickReplyMsgConvert
 * @Author: xucao
 * @Date: 2024/11/07 16:19
 * @Description: 医生快速回复语转换
 */
@Mapper
public interface QuickReplyMsgConvert {

    QuickReplyMsgConvert INSTANCE = Mappers.getMapper(QuickReplyMsgConvert.class);

    DoctorQuickReplyMsgRespVO convert(Doctor<PERSON><PERSON>ckReplyMsgDO doctorQuickReplyMsgDO);
}
