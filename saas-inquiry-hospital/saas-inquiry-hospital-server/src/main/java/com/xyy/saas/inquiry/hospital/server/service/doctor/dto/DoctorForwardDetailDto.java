package com.xyy.saas.inquiry.hospital.server.service.doctor.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

@Data
public class DoctorForwardDetailDto implements Serializable {

    /**
     * 医生名称
     */
    private String docName;

    /**
     * 性别 1男 2女
     */
    private Integer sex;

    /**
     * 身份证号码
     */
    private String idCard;

    /**
     * 手机号
     */
    private String docTel;

    /**
     * 医生类型：0：兼职医生 1：全职
     */
    private Integer docJobType;

    /**
     * 医师第一执业机构名称
     */
    private String workInstName;

    /**
     * 执业机构等级
     */
    private String orgGrade;

    /**
     * 职称编码
     */
    private String titleCode;

    /**
     * 职称名称
     */
    private String titleName;

    /**
     * 编制科室名称
     */
    private String docDeptName;

    /**
     * 合作状态：0 取消合作 1 合作中
     */
    private Byte cooperationStatus;

    /**
     * 渠道
     */
    private String signChannel;

    /**
     * 医师擅长专业
     */
    private String professional;

    /**
     * 个人简介
     */
    private String docComment;

    /**
     * 医保编码
     */
    private String medicareCode;

    // ************* 证照信息
    /**
     * - 头像地址
     */
    private String headPortrait;

    /**
     * 查证结构
     */
    private String employeeCard;

    /**
     * 医师职称证
     */
    private String titleCertList;

    /**
     * 胸牌
     */
    private String badgeList;


    /**
     * 执业证
     */
    private String certDocPracList;

    /**
     * 资格证
     */
    private String docCertList;

    /**
     * 身份证正面
     */
    private String idCardFront;

    /**
     * 身份证反面
     */
    private String idCardBack;

    /**
     * 电子签名URL
     */
    private String signUrl;

    /**
     * 医生电子签章
     */
    private String doctorSeal;

    // ********************* 备案信息

    /**
     * 民族编码
     */
    private String nationCode;

    /**
     * 学历
     */
    private String docEdu;


    /**
     * 通信地址（固定医院地址）
     */
    private String docAddress;

    /**
     * 机构所在省
     */
    private String provinceCode;

    // **************** 个人执业信息

    /**
     * 医师执业起始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date docMultiSitedDateStart;

    /**
     * 医师执业终止时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date docMultiSitedDateEnd;


    /**
     * 执业证号
     */
    private String pracNo;

    /**
     * 执业证取得时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date pracRecDate;

    /**
     * 资格证号
     */
    private String certNo;

    /**
     * 资格证取得时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date certRecDate;

    /**
     * 资格证签发时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date certRecIssueDate;

    /**
     * 职称证号
     */
    private String titleNo;

    /**
     * 职称证取得时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date titleRecDate;

    // *************** 收款信息

    /**
     * 收款人姓名
     */
    private String bankUser;

    /**
     * 收款人身份证号
     */
    private String bankIdCard;

    /**
     * 收款人手机号
     */
    private String bankTel;
    /**
     * 银行卡号
     */
    private String bankCode;
    /**
     * 开户行
     */
    private String bankName;

}
