package com.xyy.saas.inquiry.hospital.server.mq.consumer.prescription;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.PrescriptionIssuesVO;
import com.xyy.saas.inquiry.hospital.server.mq.message.prescription.PrescriptionAutoIssueEvent;
import com.xyy.saas.inquiry.hospital.server.service.clinicalcase.InquiryClinicalCaseService;
import com.xyy.saas.inquiry.hospital.server.service.prescription.PrescriptionService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 自动开方MQ
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_hospital_server_mq_consumer_reception_PrescriptionAutoIssueConsumer",
    topic = PrescriptionAutoIssueEvent.TOPIC)
public class PrescriptionAutoIssueConsumer {

    @Resource
    private PrescriptionService prescriptionService;

    @Resource
    private InquiryClinicalCaseService inquiryClinicalCaseService;

    public static final String GROUP_ID = PrescriptionAutoIssueConsumer.class.getName();

    @EventBusListener
    @TraceNode(node = TraceNodeEnum.ISSUE_PRESCRIPTION_AUTO_INQUIRY , prefLocation = "event.msg")
    public CommonResult prescriptionAutoIssue(PrescriptionAutoIssueEvent event) {
        // 自动开方创建病例信息
        inquiryClinicalCaseService.autoInquiryCreateClinicalCase(event.getMsg());

        CommonResult<?> commonResult = prescriptionService.issuesPrescription(new PrescriptionIssuesVO().setInquiryPref(event.getMsg()));
        log.info("prescriptionAutoIssue,inquiryPref:{},result:{}", event.getMsg(), JSON.toJSONString(commonResult));
        return commonResult;
    }

}
