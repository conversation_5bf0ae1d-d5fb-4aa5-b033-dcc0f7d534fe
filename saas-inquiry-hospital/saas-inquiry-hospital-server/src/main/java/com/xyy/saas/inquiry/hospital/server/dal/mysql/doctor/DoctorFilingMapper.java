package com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor;


import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorFilingPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorFilingDO;
import java.util.ArrayList;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;


/**
 * 医生备案信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DoctorFilingMapper extends BaseMapperX<DoctorFilingDO> {

    default PageResult<DoctorFilingDO> selectPage(DoctorFilingPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DoctorFilingDO>()
            .eqIfPresent(DoctorFilingDO::getDoctorId, reqVO.getDoctorId())
            .eqIfPresent(DoctorFilingDO::getNationCode, reqVO.getNationCode())
            .eqIfPresent(DoctorFilingDO::getAddress, reqVO.getAddress())
            .eqIfPresent(DoctorFilingDO::getFormalLevel, reqVO.getFormalLevel())
            .eqIfPresent(DoctorFilingDO::getOrgProvinceCode, reqVO.getOrgProvinceCode())
            .eqIfPresent(DoctorFilingDO::getRecordStatus, reqVO.getRecordStatus())
            .betweenIfPresent(DoctorFilingDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(DoctorFilingDO::getId));
    }

    default int updateByDoctorId(DoctorFilingDO updateObj) {
        return update(updateObj, new LambdaUpdateWrapper<DoctorFilingDO>()
            .eq(DoctorFilingDO::getDoctorId, updateObj.getDoctorId()));
    }

    default List<DoctorFilingDO> getByDoctorIds(List<Long> doctorIds) {
        if (CollUtil.isEmpty(doctorIds)) {
            return new ArrayList<>();
        }
        return selectList(new LambdaQueryWrapperX<DoctorFilingDO>().inIfPresent(DoctorFilingDO::getDoctorId, doctorIds));
    }

}