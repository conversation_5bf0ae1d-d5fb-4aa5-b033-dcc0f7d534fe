package com.xyy.saas.inquiry.hospital.server.dal.redis;

import org.apache.commons.lang3.StringUtils;

/**
 * @ClassName：RedisKeyConstants
 * @Author: xucao
 * @Date: 2024/10/28 19:14
 * @Description: 医院服务端redis key常量
 */
public interface RedisKeyConstants {


    public static final String PRESCRIPTION_RECOMMEND_DIAGNOSIS_KEY = "hospital_prescription_recommend_diagnosis_key";

    /**
     * 处方pref redis key
     */
    public static final String PRESCRIPTION_PRE_KEY = "hospital:prescription:pref";


    /**
     * 医师操作处方lockKey : inquiryPref
     */
    public static final String PRESCRIPTION_INQUIRY_LOCK_KEY = "hospital:prescription:inquiry:lockKey:";

    /**
     * 医师操作处方lockKey : inquiryPref
     */
    public static final String PRESCRIPTION_AUTO_INQUIRY_LOCK_KEY = "hospital:prescription:auto:inquiry:lockKey:";

    /**
     * 医生自动出诊时间轮key
     */
    public final static String DOCTOR_AUTO_INQUIRY_TIMER_WHEEL_KEY = "doctor:autoInquiry:timer_wheel:";

    /**
     * 医生自动出诊时间轮key
     */
    public final static String DOCTOR_AUTO_INQUIRY_TIMER_WHEEL_LOCK_KEY = "doctor_autoInquiry_timer_wheel_lockKey";

    /**
     * 医生自动出诊定时轮-start-key
     */
    public static final String DOCTOR_AUTO_INQUIRY_START = ":wheel_start:";
    /**
     * 医生自动出诊定时轮-end-key
     */
    public static final String DOCTOR_AUTO_INQUIRY_END = ":wheel_end:";


    /**
     * 医生自动出诊开始时间key
     *
     * @param key 时间轮key -slot
     * @return
     */
    public static String getDoctorAutoInquiryTimerWheelKey(int time) {
        return DOCTOR_AUTO_INQUIRY_TIMER_WHEEL_KEY + time;
    }

    /**
     * 获取医生自动出诊时间轮value
     *
     * @param doctorPref 医生编码
     * @param wheelFlag  时间轮标识
     * @param realKey    真实key
     * @return
     */
    public static String getDoctorAutoInquiryTimerWheelValue(String doctorPref, String wheelFlag, String realKey) {
        return doctorPref.concat(wheelFlag).concat(realKey);
    }

    public static String getDoctorAutoInquiryTimerWheelRealKey(String key) {
        if (StringUtils.contains(key, DOCTOR_AUTO_INQUIRY_START)) {
            return StringUtils.substringAfterLast(key, DOCTOR_AUTO_INQUIRY_START);
        }
        return StringUtils.substringAfterLast(key, DOCTOR_AUTO_INQUIRY_END);
    }

    public static String getDoctorAutoInquiryTimerWheelPref(String key) {
        if (StringUtils.contains(key, DOCTOR_AUTO_INQUIRY_START)) {
            return StringUtils.substringBefore(key, DOCTOR_AUTO_INQUIRY_START);
        }
        return StringUtils.substringBefore(key, DOCTOR_AUTO_INQUIRY_END);
    }


}
