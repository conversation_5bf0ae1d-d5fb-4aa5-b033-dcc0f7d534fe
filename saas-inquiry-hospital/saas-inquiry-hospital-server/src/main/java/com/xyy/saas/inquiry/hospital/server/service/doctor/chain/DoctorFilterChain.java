package com.xyy.saas.inquiry.hospital.server.service.doctor.chain;

import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import lombok.Setter;
import java.util.List;

/**
 * @Author: xucao
 * @Date: 2024/12/24 16:59
 * @Description: 医生过滤器链
 */
@Setter
public abstract class DoctorFilterChain {

    public abstract void filter(InquiryRecordDto inquiryDto, List<String> doctorList);

    protected Doctor<PERSON><PERSON><PERSON>Chai<PERSON> nextNode;


    public final void doFilter(InquiryRecordDto inquiryDto, List<String> doctorList) {
        // 执行当前节点的处理逻辑
        filter(inquiryDto,doctorList);
        // 获取下一个处理节点
        DoctorFilterChain nextHandler = getNextHandler();
        // 如果存在下一个处理节点且医生列表不为空
        if (nextHandler != null && !doctorList.isEmpty()) {
            nextHandler.doFilter(inquiryDto,doctorList);
        }
    }

    public DoctorFilterChain getNextHandler() {
        return this.nextNode;
    }
}
