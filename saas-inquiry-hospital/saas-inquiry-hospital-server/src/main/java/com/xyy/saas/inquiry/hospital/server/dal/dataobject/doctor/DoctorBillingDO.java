package com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 医生收款信息 DO
 *
 * <AUTHOR>
 */
@TableName("saas_doctor_billing")
@KeySequence("saas_doctor_billing_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DoctorBillingDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 医生id
     */
    private Long doctorId;
    /**
     * 收款人姓名
     */
    private String payeeName;
    /**
     * 收款人身份证号码
     */
    private String payeeIdCard;
    /**
     * 收款人手机号
     */
    private String payeeTelPhone;
    /**
     * 银行卡号
     */
    private String payeeBankNo;
    /**
     * 开户行
     */
    private String payeeBankName;

}