package com.xyy.saas.inquiry.hospital.server.service.doctor;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DOCTOR_VIDEO_NOT_EXISTS;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_DOCTOR_VIDEO_NOT_SET;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorVideoPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorVideoSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.convert.doctor.InquiryDoctorVideoConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorVideoDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryDoctorVideoMapper;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;
import java.util.List;

/**
 * 医生录屏记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InquiryDoctorVideoServiceImpl implements InquiryDoctorVideoService {

    @Resource
    private InquiryDoctorVideoMapper inquiryDoctorVideoMapper;

    @Override
    public Long createInquiryDoctorVideo(InquiryDoctorVideoSaveReqVO createReqVO) {
        // 插入
        InquiryDoctorVideoDO inquiryDoctorVideo = InquiryDoctorVideoConvert.INSTANCE.initConvertVO2DO(createReqVO);
        inquiryDoctorVideoMapper.insert(inquiryDoctorVideo);
        // 返回
        return inquiryDoctorVideo.getId();
    }

    @Override
    public void updateInquiryDoctorVideo(InquiryDoctorVideoSaveReqVO updateReqVO) {
        // 校验存在
        validateInquiryDoctorVideoExists(updateReqVO.getId());
        // 更新
        InquiryDoctorVideoDO updateObj = BeanUtils.toBean(updateReqVO, InquiryDoctorVideoDO.class);
        inquiryDoctorVideoMapper.updateById(updateObj);
    }

    @Override
    public void deleteInquiryDoctorVideo(Long id) {
        // 校验存在
        validateInquiryDoctorVideoExists(id);
        // 删除
        inquiryDoctorVideoMapper.deleteById(id);
    }

    private void validateInquiryDoctorVideoExists(Long id) {
        if (inquiryDoctorVideoMapper.selectById(id) == null) {
            throw exception(INQUIRY_DOCTOR_VIDEO_NOT_EXISTS);
        }
    }

    @Override
    public InquiryDoctorVideoDO getInquiryDoctorVideo(Long id) {
        return inquiryDoctorVideoMapper.selectById(id);
    }

    @Override
    public List<InquiryDoctorVideoDO> selectByDoctorPref(String doctorPref) {
        return inquiryDoctorVideoMapper.selectListByCondition(InquiryDoctorVideoPageReqVO.builder().doctorPref(doctorPref).status(CommonStatusEnum.ENABLE.getStatus()).build());
    }

    @Override
    public PageResult<InquiryDoctorVideoDO> getInquiryDoctorVideoPage(InquiryDoctorVideoPageReqVO pageReqVO) {
        return inquiryDoctorVideoMapper.selectPage(pageReqVO);
    }

    @Override
    public String selectVideoUrlByPref(String pref) {
        if(StringUtils.isBlank(pref)){
            throw exception(INQUIRY_DOCTOR_VIDEO_NOT_SET);
        }
        InquiryDoctorVideoDO videoDO = inquiryDoctorVideoMapper.selectOne(InquiryDoctorVideoDO::getPref, pref);
        if (ObjectUtil.isEmpty(videoDO)) {
            throw exception(INQUIRY_DOCTOR_VIDEO_NOT_SET);
        }
        return  videoDO.getVideoUrl();
    }

}