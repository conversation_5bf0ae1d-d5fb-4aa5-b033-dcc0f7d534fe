package com.xyy.saas.inquiry.hospital.server.dal.mysql.medicare;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.medical.MedicareSigninRecordDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 医院/药店签到记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface MedicareSigninRecordMapper extends BaseMapperX<MedicareSigninRecordDO> {

    /**
     * 根据医院编码查询已签到的记录（如有多条取最新的）
     *
     * @param medicareInstitutionCode 医药机构编码
     * @return 已签到的记录
     */
    default MedicareSigninRecordDO selectSignedInRecord(String medicareInstitutionCode) {
        return selectOne(new LambdaQueryWrapperX<MedicareSigninRecordDO>()
                .eq(MedicareSigninRecordDO::getMedicareInstitutionCode, medicareInstitutionCode)
                .eq(MedicareSigninRecordDO::getSigninStatus, 1) // 1-已签到
                .orderByDesc(MedicareSigninRecordDO::getSigninTime)
                .last("limit 1"));
    }
} 