package com.xyy.saas.inquiry.hospital.server.service.doctor.chain;

import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.constant.RedisKeyConstants;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/12/25 11:59
 * @Description: 同省医生过滤器
 * @see PrescriptionIntervalFilterChain previous （自动开方间隔期过滤）
 * @see RepeatDistributeFilter<PERSON>hain next （真人问诊已派单医生过滤）
 */
@Component
@Slf4j
public class ProvinceFilter<PERSON>hain extends <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {

    @Override
    @TraceNode(node = TraceNodeEnum.DOCTOR_FILTER_PROVINCE , prefLocation = "inquiryDto.pref")
    public void filter(InquiryRecordDto inquiryDto, List<String> doctorList) {
        log.info("问诊单号：{},自动开方同省医生过滤，医生列表：{}", inquiryDto.getPref(),JSON.toJSONString(doctorList));
        // 非自动开方问诊直接返回
        if (ObjectUtil.equals(AutoInquiryEnum.NO.getCode(), inquiryDto.getAutoInquiry())) {
            return;
        }
        // 获取当前租户所在省
        String province = inquiryDto.getProvinceCode();
        // 自动开方需要过滤掉同省医生
        doctorList.removeIf(doctor -> StringUtils.equals(province, RedisKeyConstants.getDoctorProvinceCodeKey(doctor)));
    }
}
