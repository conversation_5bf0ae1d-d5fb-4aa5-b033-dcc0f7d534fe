package com.xyy.saas.inquiry.hospital.server.service.doctor;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.parser.Feature;
import com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo.DoctorQuickReplyMsgGroupRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo.DoctorQuickReplyMsgPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo.DoctorQuickReplyMsgSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.convert.doctor.QuickReplyMsgConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorQuickReplyMsgDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.DoctorQuickReplyMsgMapper;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.*;

/**
 * 医生快捷回复语 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DoctorQuickReplyMsgServiceImpl implements DoctorQuickReplyMsgService {

    private static final Logger log = LoggerFactory.getLogger(DoctorQuickReplyMsgServiceImpl.class);
    @Resource
    private DoctorQuickReplyMsgMapper doctorQuickReplyMsgMapper;

    @Autowired
    private ConfigApi configApi;
    // 快捷语配置key
    static final String DOCTOR_QUICK_REPLY_MSG = "doctor.quick.reply.msg";

    // 快捷语标题最大20条
    static final Integer QUICK_REPLY_MSG_TITLE_LIMIT = 20;

    // 快捷语内容最多50条
    static final Integer QUICK_REPLY_MSG_CONTENT_LIMIT = 50;


    @Override
    public Long createDoctorQuickReplyMsg(DoctorQuickReplyMsgSaveReqVO createReqVO) {
        // 插入
        DoctorQuickReplyMsgDO doctorQuickReplyMsg = BeanUtils.toBean(createReqVO, DoctorQuickReplyMsgDO.class);

        if (createReqVO.getParentId() == null) { // 新增标题
            Long titleCount = doctorQuickReplyMsgMapper.selectTitleCount(createReqVO.getDoctorId());
            if (titleCount != null && titleCount > QUICK_REPLY_MSG_TITLE_LIMIT) {
                throw exception(DOCTOR_QUICK_REPLY_MSG_TITLE_LIMIT, QUICK_REPLY_MSG_TITLE_LIMIT);
            }
        } else { // 新增短语
            Long titleCount = doctorQuickReplyMsgMapper.selectContentCount(createReqVO.getDoctorId(), createReqVO.getParentId());
            if (titleCount != null && titleCount > QUICK_REPLY_MSG_CONTENT_LIMIT) {
                throw exception(DOCTOR_QUICK_REPLY_MSG_CONTENT_LIMIT, QUICK_REPLY_MSG_CONTENT_LIMIT);
            }
        }
        doctorQuickReplyMsgMapper.insert(doctorQuickReplyMsg);
        // 返回
        return doctorQuickReplyMsg.getId();
    }

    @Override
    public void updateDoctorQuickReplyMsg(DoctorQuickReplyMsgSaveReqVO updateReqVO) {
        // 校验存在
        validateDoctorQuickReplyMsgExists(updateReqVO.getId());
        // 更新
        DoctorQuickReplyMsgDO updateObj = BeanUtils.toBean(updateReqVO, DoctorQuickReplyMsgDO.class);
        doctorQuickReplyMsgMapper.updateById(updateObj);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void deleteDoctorQuickReplyMsg(Long id) {
        // 校验存在
        DoctorQuickReplyMsgDO msgDO = validateDoctorQuickReplyMsgExists(id);
        if (msgDO.getParentId() != null) { // 删除短语
            doctorQuickReplyMsgMapper.deleteById(id);
            return;
        }
        // 删除标题 + 下层短语
        DoctorQuickReplyMsgPageReqVO msgPageReqVO = new DoctorQuickReplyMsgPageReqVO().setDoctorId(msgDO.getDoctorId()).setParentId(msgDO.getId());
        List<DoctorQuickReplyMsgDO> msgDOS = doctorQuickReplyMsgMapper.selectList(msgPageReqVO);
        if (CollUtil.isNotEmpty(msgDOS)) {
            doctorQuickReplyMsgMapper.deleteByIds(CollectionUtils.convertList(msgDOS, DoctorQuickReplyMsgDO::getId));
        }
        doctorQuickReplyMsgMapper.deleteById(id);
    }

    /**
     * 批量删除医生快捷回复语
     *
     * @param ids 编号
     */
    @Override
    public void batchDeleteDoctorQuickReplyMsg(Long[] ids) {
        if (ids == null || ids.length == 0) {
            return;
        }
        doctorQuickReplyMsgMapper.deleteByIds(Arrays.asList(ids));
    }

    private DoctorQuickReplyMsgDO validateDoctorQuickReplyMsgExists(Long id) {
        DoctorQuickReplyMsgDO msgDO = doctorQuickReplyMsgMapper.selectById(id);
        if (msgDO == null) {
            throw exception(DOCTOR_QUICK_REPLY_MSG_NOT_EXISTS);
        }
        return msgDO;
    }

    @Override
    public DoctorQuickReplyMsgDO getDoctorQuickReplyMsg(Long id) {
        return doctorQuickReplyMsgMapper.selectById(id);
    }

    @Override
    public List<DoctorQuickReplyMsgGroupRespVO> getDoctorQuickReplyMsgList(DoctorQuickReplyMsgPageReqVO pageReqVO) {
        List<DoctorQuickReplyMsgDO> msgDOList = doctorQuickReplyMsgMapper.selectList(pageReqVO);
        if (org.springframework.util.CollectionUtils.isEmpty(msgDOList)) {
            return List.of();
        }
        //分组过滤填充
        return msgDOList.stream().filter(m -> m.getParentId() == null).sorted(Comparator.comparing(DoctorQuickReplyMsgDO::getSorted)).map(g -> {
            return DoctorQuickReplyMsgGroupRespVO.builder().title(g.getTitle()).sorted(g.getSorted()).id(g.getId()).itemList(
                msgDOList.stream().filter(i -> i.getParentId() != null && i.getParentId().equals(g.getId())).sorted(Comparator.comparing(DoctorQuickReplyMsgDO::getSorted)).map(QuickReplyMsgConvert.INSTANCE::convert).toList()
            ).build();
        }).collect(Collectors.toList());
    }


    @Override
    public void initDoctorQuickReplyMsg(Long doctorId) {
        // 存在不处理
        Long count = doctorQuickReplyMsgMapper.selectCount(DoctorQuickReplyMsgDO::getDoctorId, doctorId);
        if (count != null && count > 0) {
            return;
        }
        Map<String, List<DoctorQuickReplyMsgDO>> doctorReplyMsg = getDefaultDoctorReplyMsg();
        if (CollUtil.isEmpty(doctorReplyMsg)) {
            return;
        }
        // 写入
        AtomicInteger i = new AtomicInteger(0);
        for (Map.Entry<String, List<DoctorQuickReplyMsgDO>> entry : doctorReplyMsg.entrySet()) {
            DoctorQuickReplyMsgDO msgDO = new DoctorQuickReplyMsgDO().setDoctorId(doctorId).setTitle(entry.getKey()).setSorted(i.getAndIncrement());
            // 写入主表
            doctorQuickReplyMsgMapper.insert(msgDO);
            if (CollUtil.isEmpty(entry.getValue())) {
                continue;
            }
            // 写入明细
            doctorQuickReplyMsgMapper.insertBatch(entry.getValue().stream().peek(m -> {
                m.setDoctorId(doctorId).setParentId(msgDO.getId());
            }).collect(Collectors.toSet()));
        }
    }

    /**
     * 获取默认快捷回复语config
     *
     * @return
     */
    private Map<String, List<DoctorQuickReplyMsgDO>> getDefaultDoctorReplyMsg() {
        try {
            String value = configApi.getConfigValueByKey(DOCTOR_QUICK_REPLY_MSG);
            if (StringUtils.isBlank(value)) {
                return null;
            }
            LinkedHashMap<String, List<String>> maps = JSON.parseObject(value, LinkedHashMap.class, Feature.OrderedField);
            Map<String, List<DoctorQuickReplyMsgDO>> map = new HashMap<>();
            for (Map.Entry<String, List<String>> entry : maps.entrySet()) {
                if (CollUtil.isEmpty(entry.getValue())) {
                    continue;
                }
                AtomicInteger i = new AtomicInteger(0);
                List<DoctorQuickReplyMsgDO> replyMsgDOS = entry.getValue().stream().map(s -> new DoctorQuickReplyMsgDO().setTitle(entry.getKey()).setContent(s).setSorted(i.getAndIncrement())).collect(Collectors.toList());
                map.put(entry.getKey(), replyMsgDOS);
            }
            return map;
        } catch (Exception e) {
            log.error("DoctorQuickReplyMsgServiceImpl.getDefaultDoctorReplyMsg,error:{}", e.getMessage(), e);
        }
        return null;
    }


}