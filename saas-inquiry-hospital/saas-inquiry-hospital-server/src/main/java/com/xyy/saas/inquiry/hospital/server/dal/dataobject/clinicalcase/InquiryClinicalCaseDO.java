package com.xyy.saas.inquiry.hospital.server.dal.dataobject.clinicalcase;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import cn.iocoder.yudao.framework.mybatis.core.type.StringListTypeHandler;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.pojo.inquiry.ClinicalCaseExtDto;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 门诊病例 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_inquiry_clinical_case", autoResultMap = true)
@KeySequence("saas_inquiry_clinical_case_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class InquiryClinicalCaseDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 病例号
     */
    private String pref;

    /**
     * 门店id
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 问诊编号
     */
    private String inquiryPref;
    /**
     * 互联网医院编号
     */
    private String hospitalPref;
    /**
     * 互联网医院名称
     */
    private String hospitalName;
    /**
     * 医生编号
     */
    private String doctorPref;
    /**
     * 医生姓名
     */
    private String doctorName;
    /**
     * 科室编码
     */
    private String deptPref;
    /**
     * 科室编码
     */
    private String deptName;
    /**
     * 患者pref
     */
    private String patientPref;
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 患者身份证号
     */
    private String patientIdCard;
    /**
     * 患者年龄
     */
    private String patientAge;
    /**
     * 患者性别 1、男   2、女
     */
    private Integer patientSex;

    /**
     * 主诉
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> mainSuit;
    /**
     * 过敏史  eg：青霉素|头孢
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> allergic;

    /**
     * 既往史
     */
    private String patientHisDesc;
    /**
     * 现病史
     */
    private String currentIllnessDesc;


    /**
     * 复诊标识 0初次 1复诊
     */
    private Integer followUp;
    /**
     * 主要症状
     */
    private String mainSymptoms;

    /**
     * 处理措施
     */
    private String measures;
    /**
     * 是否需要留院观察 0否 1是
     */
    private Integer observation;
    /**
     * 转诊标识 0非转诊
     */
    private Integer referral;

    /**
     * 西医诊断编码
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> diagnosisCode;
    /**
     * 西医诊断说明
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> diagnosisName;

    /**
     * 中医诊断编码
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> tcmDiagnosisCode;
    /**
     * 中医诊断说明
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> tcmDiagnosisName;

    /**
     * 中医辨证代码
     */
    private String tcmSyndromeCode;

    /**
     * 中医辨证名称
     */
    private String tcmSyndromeName;

    /**
     * 中医治法代码
     */
    private String tcmTreatmentMethodCode;

    /**
     * 中医治法名称
     */
    private String tcmTreatmentMethodName;

    /**
     * 病例扩展字段
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private ClinicalCaseExtDto ext;

    @JsonIgnore
    public ClinicalCaseExtDto extGet() {
        if (ext == null) {
            ext = new ClinicalCaseExtDto();
        }
        return ext;
    }

}