package com.xyy.saas.inquiry.hospital.server.api.prescription;

import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionDetailApi;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionDetailQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionDetailRespDTO;
import com.xyy.saas.inquiry.hospital.server.convert.prescription.InquiryPrescriptionDetailConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDetailDO;
import com.xyy.saas.inquiry.hospital.server.service.prescription.InquiryPrescriptionDetailService;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;
import java.util.List;

/**
 * @Author:chenxiaoyi
 * @Date:2025/02/18 20:50
 */
@DubboService
public class InquiryPrescriptionDetailApiImpl implements InquiryPrescriptionDetailApi {

    @Resource
    private InquiryPrescriptionDetailService inquiryPrescriptionDetailService;

    @Override
    public List<InquiryPrescriptionDetailRespDTO> getPrescriptionDetail(String prescriptionPref) {
        List<InquiryPrescriptionDetailDO> prescriptionDetailDOS = inquiryPrescriptionDetailService.getInquiryPrescriptionDetailsByPref(prescriptionPref);
        return InquiryPrescriptionDetailConvert.INSTANCE.convertDTOs(prescriptionDetailDOS);
    }

    @Override
    public List<InquiryPrescriptionDetailRespDTO> getInquiryPrescriptionDetailListByCondition(InquiryPrescriptionDetailQueryDTO queryDTO) {
        List<InquiryPrescriptionDetailDO> inquiryPrescriptionDetails = inquiryPrescriptionDetailService.getInquiryPrescriptionDetails(queryDTO);
        return InquiryPrescriptionDetailConvert.INSTANCE.convertDTOs(inquiryPrescriptionDetails);
    }
}
