package com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 医生审核记录 DO
 *
 * <AUTHOR>
 */
@TableName("saas_doctor_audited_record")
@KeySequence("saas_doctor_audited_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DoctorAuditedRecordDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 医生id
     */
    private Long doctorId;
    /**
     * 审核人姓名
     */
    private String auditorName;
    /**
     * 审核人工号
     */
    private Long auditorId;
    /**
     * 审核结果  1、审核通过  2、审核驳回
     */
    private Integer auditResult;

    /**
     * 审核驳回原因
     */
    private String diffReason;
    /**
     * 审核时间
     */
    private LocalDateTime auditTime;

}