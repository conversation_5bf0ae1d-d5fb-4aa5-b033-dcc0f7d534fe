package com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 医生执业信息 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_doctor_practice")
@KeySequence("saas_inquiry_doctor_practice_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DoctorPracticeDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 医生id
     */
    private Long doctorId;
    /**
     * 第一执业机构名称
     */
    private String firstPracticeName;
    /**
     * 第一执业机构等级，例如：0=三甲, 1=三乙 ...
     */
    private Integer firstPracticeLevel;
    /**
     * 第一执业机构科室名称
     */
    private String firstPracticeDeptName;


    /**
     * 医生渠道
     */
    private Integer canal;

    /**
     * 邀请人姓名
     */
    private String inviterName;

    /**
     * 邀请人工号
     */
    private String inviterNo;

    /**
     * 专业职称代码，例如：2
     */
    private Integer titleCode;
    /**
     * 专业职称证书编号
     */
    private String titleNo;
    /**
     * 专业职称证书取得时间，例如：2016-03-20
     */
    private LocalDateTime titleTime;
    /**
     * 开始执业时间，例如：2021-03-20
     */
    private LocalDateTime startPracticeTime;
    /**
     * 执业结束时间，例如：2029-03-20
     */
    private LocalDateTime endPracticeDate;
    /**
     * 执业证书号
     */
    private String professionalNo;
    /**
     * 执业证书取得时间，例如：2016-03-20
     */
    private LocalDateTime professionalTime;

    /**
     * 医生医保编码
     */
    private String doctorMedicareNo;

    /**
     * 资格证书号
     */
    private String qualificationNo;
    /**
     * 资格证书取得时间，例如：2016-03-20
     */
    private LocalDateTime qualificationTime;

}