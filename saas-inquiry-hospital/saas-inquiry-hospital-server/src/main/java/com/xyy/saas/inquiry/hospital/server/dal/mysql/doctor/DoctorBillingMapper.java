package com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorBillingPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorBillingDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 医生收款信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DoctorBillingMapper extends BaseMapperX<DoctorBillingDO> {

    default PageResult<DoctorBillingDO> selectPage(DoctorBillingPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DoctorBillingDO>()
            .eqIfPresent(DoctorBillingDO::getDoctorId, reqVO.getDoctorId())
            .likeIfPresent(DoctorBillingDO::getPayeeName, reqVO.getPayeeName())
            .eqIfPresent(DoctorBillingDO::getPayeeIdCard, reqVO.getPayeeIdCard())
            .eqIfPresent(DoctorBillingDO::getPayeeTelPhone, reqVO.getPayeeTelPhone())
            .eqIfPresent(DoctorBillingDO::getPayeeBankNo, reqVO.getPayeeBankNo())
            .likeIfPresent(DoctorBillingDO::getPayeeBankName, reqVO.getPayeeBankName())
            .betweenIfPresent(DoctorBillingDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(DoctorBillingDO::getId));
    }

    default int updateByDoctorId(DoctorBillingDO updateObj) {
        return update(updateObj, new LambdaUpdateWrapper<DoctorBillingDO>()
            .eq(DoctorBillingDO::getDoctorId, updateObj.getDoctorId()));
    }

}