package com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 医生出诊状态关系 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_doctor_status")
@KeySequence("saas_inquiry_doctor_status_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryDoctorStatusDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 医生编码
     */
    private String doctorPref;
    /**
     * 审方类型 1:图文 2:视频 3:电话
     */
    private Integer inquiryWayType;
    /**
     * 开方类型：0手动开方  1自动开方
     */
    private Integer inquiryType;
    /**
     * 问诊业务类型 1:药店问诊 2:远程审方
     */
    private Integer inquiryBizType;
    /**
     * 出诊状态：0闭诊 1出诊
     */
    private Integer status;

}