package com.xyy.saas.inquiry.hospital.server.service.doctor;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.enums.doctor.DoctorTypeEnum;
import com.xyy.saas.inquiry.enums.user.CertificateTypeEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.identification.InquiryProfessionIdentificationDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorDetailRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryProfessionIdentificationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryProfessionIdentificationSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.convert.indentification.InquiryProfessionIdentificationConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryProfessionIdentificationDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryProfessionIdentificationMapper;
import jakarta.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import org.springframework.validation.annotation.Validated;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.*;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_PROFESSION_IDENTIFICATION_NOT_EXISTS;


/**
 * 问诊职业(医生药师)证件信息 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InquiryProfessionIdentificationServiceImpl implements InquiryProfessionIdentificationService {

    private static final Logger log = LoggerFactory.getLogger(InquiryProfessionIdentificationServiceImpl.class);
    @Resource
    private InquiryProfessionIdentificationMapper inquiryProfessionIdentificationMapper;

    @Override
    public Long createInquiryProfessionIdentification(InquiryProfessionIdentificationSaveReqVO createReqVO) {
        // 插入
        InquiryProfessionIdentificationDO inquiryProfessionIdentification = BeanUtils.toBean(createReqVO, InquiryProfessionIdentificationDO.class);
        inquiryProfessionIdentificationMapper.insert(inquiryProfessionIdentification);
        // 返回
        return inquiryProfessionIdentification.getId();
    }

    @Override
    public void updateInquiryProfessionIdentification(InquiryProfessionIdentificationSaveReqVO updateReqVO) {
        // 校验存在
        validateInquiryProfessionIdentificationExists(updateReqVO.getId());
        // 更新
        InquiryProfessionIdentificationDO updateObj = BeanUtils.toBean(updateReqVO, InquiryProfessionIdentificationDO.class);
        inquiryProfessionIdentificationMapper.updateById(updateObj);
    }

    @Override
    public void deleteInquiryProfessionIdentification(Long id) {
        // 校验存在
        validateInquiryProfessionIdentificationExists(id);
        // 删除
        inquiryProfessionIdentificationMapper.deleteById(id);
    }

    private void validateInquiryProfessionIdentificationExists(Long id) {
        if (inquiryProfessionIdentificationMapper.selectById(id) == null) {
            throw exception(INQUIRY_PROFESSION_IDENTIFICATION_NOT_EXISTS);
        }
    }

    @Override
    public InquiryProfessionIdentificationDO getInquiryProfessionIdentification(Long id) {
        return inquiryProfessionIdentificationMapper.selectById(id);
    }

    @Override
    public PageResult<InquiryProfessionIdentificationDO> getInquiryProfessionIdentificationPage(InquiryProfessionIdentificationPageReqVO pageReqVO) {
        return inquiryProfessionIdentificationMapper.selectPage(pageReqVO);
    }

    /**
     * 医生新增场景获取证件信息
     *
     * @param saveReqVO 医生新增参数
     * @return 证件列表
     */
    @Override
    public List<InquiryProfessionIdentificationDO> getListByDoctorCreate(InquiryDoctorSaveReqVO saveReqVO) {
        List<InquiryProfessionIdentificationDO> certificates = new ArrayList<>();
        for (CertificateTypeEnum certInfo : CertificateTypeEnum.values()) {
            Object value = invokeMethod(saveReqVO, certInfo.getGetMethodName());
            if (ObjectUtils.isEmpty(value)) {
                continue;
            }
            if (value instanceof List) {
                @SuppressWarnings("unchecked")
                List<String> imgUrls = (List<String>) value;
                for (String imgUrl : imgUrls) {
                    certificates.add(InquiryProfessionIdentificationDO.builder()
                        .personId(saveReqVO.getDoctorId())
                        .doctorType(DoctorTypeEnum.DOCTOR.getCode())
                        .certificateType(certInfo.getType())
                        .certificateImgUrl(imgUrl)
                        .build());
                }
            } else if (value instanceof String) {
                certificates.add(InquiryProfessionIdentificationDO.builder()
                    .personId(saveReqVO.getDoctorId())
                    .doctorType(DoctorTypeEnum.DOCTOR.getCode())
                    .certificateType(certInfo.getType())
                    .certificateImgUrl(value.toString())
                    .build());
            }
        }
        return certificates;
    }

    /**
     * 查询医生证件信息
     *
     * @param result 医生详情
     */
    @Override
    public void setDoctorIdentification(InquiryDoctorDetailRespVO result) {
        List<InquiryProfessionIdentificationDO> certificates = inquiryProfessionIdentificationMapper.selectList(InquiryProfessionIdentificationDO::getPersonId, result.getId());
        if (CollectionUtils.isEmpty(certificates)) {
            return;
        }
        Map<Integer, List<InquiryProfessionIdentificationDO>> certMap = certificates.stream().collect(Collectors.groupingBy(InquiryProfessionIdentificationDO::getCertificateType));
        certMap.forEach((k, v) -> {
            CertificateTypeEnum certInfo = CertificateTypeEnum.getByType(k);
            if (ObjectUtils.isEmpty(certInfo)) {
                return;
            }
            if(certInfo.getClassType() == String.class){
                invokeMethod(result, certInfo.getSetMethodName(), new String[]{v.getFirst().getCertificateImgUrl()}, new Class<?>[]{String.class});
            }else if(certInfo.getClassType() == List.class){
                invokeMethod(result, certInfo.getSetMethodName(), new Object[]{v.stream().map(InquiryProfessionIdentificationDO::getCertificateImgUrl).toList()}, new Class<?>[]{List.class});
            }
        });
    }


    @Override
    public void saveProfessionIdentifications(List<InquiryProfessionIdentificationDto> professionIdentifications) {
        if (CollUtil.isEmpty(professionIdentifications)) {
            return;
        }
        Long personId = professionIdentifications.getFirst().getPersonId();
        Integer doctorType = professionIdentifications.getFirst().getDoctorType();
        Set<Integer> certTypes = professionIdentifications.stream().map(InquiryProfessionIdentificationDto::getCertificateType).filter(Objects::nonNull).collect(Collectors.toSet());
        // 删除后新增
        inquiryProfessionIdentificationMapper.deleteByPersonIdType(personId, doctorType, certTypes);
        inquiryProfessionIdentificationMapper.insertBatch(InquiryProfessionIdentificationConvert.INSTANCE.convert(professionIdentifications));
    }


    @Override
    public List<InquiryProfessionIdentificationDto> getInquiryProfessionIdentification(Long doctorId, DoctorTypeEnum doctorTypeEnum) {

        List<InquiryProfessionIdentificationDO> identificationDOS = inquiryProfessionIdentificationMapper.selectList(doctorId, doctorTypeEnum, null);

        return InquiryProfessionIdentificationConvert.INSTANCE.converts(identificationDOS);
    }

    @Override
    public InquiryProfessionIdentificationDto getProfessionIdentifications(Long doctorId, DoctorTypeEnum doctorTypeEnum, CertificateTypeEnum certificateTypeEnum) {

        List<InquiryProfessionIdentificationDO> identificationDOS = inquiryProfessionIdentificationMapper.selectList(doctorId, doctorTypeEnum, certificateTypeEnum);

        return InquiryProfessionIdentificationConvert.INSTANCE.convert(CollUtil.getFirst(identificationDOS));
    }


    @Override
    public void deleteInquiryProfessionIdentification(Long doctorId, DoctorTypeEnum doctorTypeEnum) {
        inquiryProfessionIdentificationMapper.deleteByPersonIdType(doctorId, doctorTypeEnum == null ? null : doctorTypeEnum.getCode(), null);
    }

    /**
     * 动态调用方法
     *
     * @param obj    对象
     * @param method 方法名
     * @return 方法返回值
     */
    private static Object invokeMethod(Object obj, String method) {
        try {
            Method m = obj.getClass().getMethod(method);
            return m.invoke(obj);
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            return null;
        }
    }

    /**
     * 反射调用带有参数的方法
     *
     * @param obj        对象实例
     * @param methodName 方法名称
     * @param args       参数列表
     * @param argTypes   参数类型
     * @return 方法返回值
     */
    private static void invokeMethod(Object obj, String methodName, Object[] args, Class<?>[] argTypes) {
        try {
            Method method = obj.getClass().getMethod(methodName, argTypes);
            method.invoke(obj, args);
        } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
            log.error("method invoke error,reson", e);
        }
    }

}