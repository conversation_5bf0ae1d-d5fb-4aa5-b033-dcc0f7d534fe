package com.xyy.saas.inquiry.hospital.server.service.doctor.chain;

import com.alibaba.fastjson2.JSON;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.hospital.server.dal.redis.inquiry.HospitalInquiryRedisDao;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import java.util.List;

/**
 * @Author: xucao
 * @Date: 2024/12/26 13:55
 * @see ProvinceFilterChain previous （自动开方同省份医生过滤）
 * @see GrabFirstFilterChain next （自动抢单-抢单优先模式）
 * @Description: 真人问诊已派单医生过滤
 */
@Component
@Slf4j
public class RepeatDistributeFilterChain extends DoctorFilterChain{

    @Resource
    private HospitalInquiryRedisDao hospitalInquiryRedisDao;

    @Override
    @TraceNode(node = TraceNodeEnum.DOCTOR_FILTER_REPEATDISTRIBUTE , prefLocation = "inquiryDto.pref")
    public void filter(InquiryRecordDto inquiryDto, List<String> doctorList) {
        log.info("问诊单号：{},真人问诊已派单医生过滤，当前医生列表：{}", inquiryDto.getPref(),JSON.toJSONString(doctorList));
        //自动开方无需过滤
        if(inquiryDto.isAutoInquiry()){
            return;
        }
        List<String> sendList = hospitalInquiryRedisDao.getInquirySendList(inquiryDto.getPref());
        doctorList.removeIf(doctor -> sendList.contains(doctor));
    }
}
