package com.xyy.saas.inquiry.hospital.server.convert.doctor;

import com.xyy.saas.inquiry.enums.im.ImEventPushEnum;
import com.xyy.saas.inquiry.im.api.message.dto.ImEventMessageExtDto;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryImMessageDto;
import com.xyy.saas.inquiry.im.api.message.dto.NotificationPushDto;
import com.xyy.saas.inquiry.im.api.message.dto.NotificationPushDto.Action;
import com.xyy.saas.inquiry.im.api.message.dto.NotificationPushDto.NotificationPushExtDto;
import com.xyy.saas.inquiry.im.enums.PushContentEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import org.jetbrains.annotations.NotNull;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.text.MessageFormat;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 医生IM消息类相关转换
 */
@Mapper
public interface InquiryDoctorImConvert {
    InquiryDoctorImConvert INSTANCE = Mappers.getMapper(InquiryDoctorImConvert.class);

    /**
     * 问诊推送医生事件消息
     * @param doctorImAccount
     * @param eventEnum
     * @return
     */
    default InquiryImMessageDto convertSystemMsgToDoctor(String doctorImAccount , ImEventPushEnum eventEnum , String inquiryPref) {
       return InquiryImMessageDto.builder().toAccount(doctorImAccount).msg(eventEnum.getDesc()).extDto(ImEventMessageExtDto.builder().eventType(eventEnum.getCode()).eventInfo(
               ImEventMessageExtDto.EventInfo.builder().inquiryPref(inquiryPref).build()
       ).build()).build();
    }


    /**
     * 问诊结束推送商家端事件消息
     * @param inquiryPref 问诊单号
     * @param patientImAccount 患者IM账号
     * @param eventEnum 事件枚举
     * @return
     */
    default InquiryImMessageDto convertSystemMsgForInquiryEndToPatient(String inquiryPref , String patientImAccount , ImEventPushEnum eventEnum) {
        return InquiryImMessageDto.builder().toAccount(patientImAccount).msg(eventEnum.getDesc()).extDto(ImEventMessageExtDto.builder().eventType(eventEnum.getCode()).eventInfo(
                ImEventMessageExtDto.EventInfo.builder().inquiryPref(inquiryPref).build()
        ).build()).build();
    }

    default NotificationPushDto convertNotificationPushDto(List<Long> userList , PushContentEnum pushContentEnum, InquiryRecordDto inquiryDto) {
        return NotificationPushDto.builder()
            .title(pushContentEnum.getTitle())
            .body(pushContentEnum.getBody())
            .userIdList(userList)
            .ext(NotificationPushExtDto.builder().action(convertAction(pushContentEnum,inquiryDto)).build())
            .build();
    }

    default Action convertAction( PushContentEnum pushContentEnum, InquiryRecordDto inquiryDto) {
        Map<String, String> paramMap = getParam(pushContentEnum, inquiryDto);
        return Action.builder()
            .openType(pushContentEnum.getOpenType())
            .pageUrl(MessageFormat.format(pushContentEnum.getPageUrl(), inquiryDto.getPref()))
            .pageParams(paramMap)
            .build();
    }

    /**
     * 获取推送的参数
     * @param pushContentEnum 推送内容枚举
     * @param inquiryDto 问诊单
     * @return
     */
    private Map<String, String> getParam(PushContentEnum pushContentEnum, InquiryRecordDto inquiryDto) {
        Map<String, String> paramMap = new HashMap<>();
        // 问诊单号
        paramMap.put("inquiryPref", inquiryDto.getPref());
        // 推送事件code
        paramMap.put("pushCode", pushContentEnum.getCode());
        return paramMap;
    }
}
