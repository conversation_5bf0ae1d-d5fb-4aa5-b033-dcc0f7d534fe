package com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorPracticePageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorPracticeDO;
import org.apache.ibatis.annotations.Mapper;


/**
 * 医生执业信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DoctorPracticeMapper extends BaseMapperX<DoctorPracticeDO> {

    default PageResult<DoctorPracticeDO> selectPage(DoctorPracticePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DoctorPracticeDO>()
            .eqIfPresent(DoctorPracticeDO::getDoctorId, reqVO.getDoctorId())
            .likeIfPresent(DoctorPracticeDO::getFirstPracticeName, reqVO.getFirstPracticeName())
            .eqIfPresent(DoctorPracticeDO::getFirstPracticeLevel, reqVO.getFirstPracticeLevel())
            .eqIfPresent(DoctorPracticeDO::getTitleCode, reqVO.getTitleCode())
            .eqIfPresent(DoctorPracticeDO::getTitleNo, reqVO.getTitleNo())
            .betweenIfPresent(DoctorPracticeDO::getTitleTime, reqVO.getTitleTime())
            .betweenIfPresent(DoctorPracticeDO::getStartPracticeTime, reqVO.getStartPracticeTime())
            .betweenIfPresent(DoctorPracticeDO::getEndPracticeDate, reqVO.getEndPracticeDate())
            .eqIfPresent(DoctorPracticeDO::getProfessionalNo, reqVO.getProfessionalNo())
            .betweenIfPresent(DoctorPracticeDO::getProfessionalTime, reqVO.getProfessionalTime())
            .eqIfPresent(DoctorPracticeDO::getQualificationNo, reqVO.getQualificationNo())
            .betweenIfPresent(DoctorPracticeDO::getQualificationTime, reqVO.getQualificationTime())
            .betweenIfPresent(DoctorPracticeDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(DoctorPracticeDO::getId));
    }

    default int updateByDoctorId(DoctorPracticeDO updateObj) {
        return update(updateObj, new LambdaUpdateWrapper<DoctorPracticeDO>()
            .eq(DoctorPracticeDO::getDoctorId, updateObj.getDoctorId()));
    }

}