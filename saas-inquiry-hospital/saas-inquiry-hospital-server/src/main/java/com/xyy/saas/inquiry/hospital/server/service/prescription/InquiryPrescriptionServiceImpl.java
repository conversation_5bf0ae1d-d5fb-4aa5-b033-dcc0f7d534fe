package com.xyy.saas.inquiry.hospital.server.service.prescription;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception0;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_PRESCRIPTION_DETAIL_NOT_EXISTS;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_PRESCRIPTION_MAX_SIZE_ERROR;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_PRESCRIPTION_NOT_EXISTS;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_PRESCRIPTION_NO_DATA_ERROR;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_PRESCRIPTION_NO_PRICING_ERROR;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_PRESCRIPTION_PDF_DOWN_FAIL;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_PRESCRIPTION_PRICING_DETAIL_ERROR;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_PRESCRIPTION_PRICING_ERROR;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_PRESCRIPTION_PRICING_NO_AUDIT_ERROR;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.util.MyBatisUtils;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.system.api.permission.PermissionApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.google.common.collect.Maps;
import com.xyy.saas.inquiry.annotation.InquiryDateType;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.drugstore.api.user.UserApi;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrintStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.QuerySourceEnum;
import com.xyy.saas.inquiry.enums.prescription.PrescriptionPricingTypeEnum;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.enums.transmitter.NodeTypeEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionFlushQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.hospital.enums.PricingOperateTypeEnum;
import com.xyy.saas.inquiry.hospital.server.constant.QuerySceneEnum;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalBindRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionAbandonVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionDetailSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionExcelRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPdfReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPricingVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPricingVO.PrescriptionDetailPricingVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.InquiryPrescriptionCountVO;
import com.xyy.saas.inquiry.hospital.server.convert.prescription.InquiryPrescriptionConvert;
import com.xyy.saas.inquiry.hospital.server.convert.prescription.InquiryPrescriptionDetailConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDetailDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital.InquiryHospitalMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.prescription.InquiryPrescriptionDetailMapper;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.prescription.InquiryPrescriptionMapper;
import com.xyy.saas.inquiry.hospital.server.mq.producer.prescription.PrescriptionPricingProducer;
import com.xyy.saas.inquiry.hospital.server.service.doctor.DoctorPracticeService;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorService;
import com.xyy.saas.inquiry.hospital.server.service.hospital.HospitalEmployeeService;
import com.xyy.saas.inquiry.hospital.server.service.prescription.external.SaasPrescriptionExternalService;
import com.xyy.saas.inquiry.hospital.server.service.prescription.strategy.query.QueryStrategy;
import com.xyy.saas.inquiry.hospital.server.util.DateUtil;
import com.xyy.saas.inquiry.hospital.server.util.FileUtils;
import com.xyy.saas.inquiry.hospital.server.util.ImageCombinerUtil;
import com.xyy.saas.inquiry.mq.prescription.AbandonPrescriptionPostPassingEvent;
import com.xyy.saas.inquiry.mq.prescription.AbandonPrescriptionPostPassingProducer;
import com.xyy.saas.inquiry.mq.prescription.AbandonRestorePrescriptionPostPassingEvent;
import com.xyy.saas.inquiry.mq.prescription.AbandonRestorePrescriptionPostPassingProducer;
import com.xyy.saas.inquiry.mq.prescription.PrescriptionPricingEvent;
import com.xyy.saas.inquiry.mq.prescription.dto.PrescriptionMqCommonMessage;
import com.xyy.saas.inquiry.mq.prescription.dto.PrescriptionPricingMessage;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pharmacist.api.pharmacist.InquiryPharmacistApi;
import com.xyy.saas.inquiry.pharmacist.api.pharmacist.dto.InquiryPharmacistDto;
import com.xyy.saas.inquiry.pojo.patient.PatientSimpleDTO;
import com.xyy.saas.inquiry.util.FileApiUtil;
import com.xyy.saas.inquiry.util.UrlConUtil;
import com.xyy.saas.transmitter.api.servicepack.TransmissionServicePackApi;
import com.xyy.saas.transmitter.api.servicepack.dto.TransmissionFunConfigOptionDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionConfigReqDTO;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 处方记录 Service 实现类
 *
 * <AUTHOR>
 */
@Slf4j
@Service
@Validated
public class InquiryPrescriptionServiceImpl implements InquiryPrescriptionService {

    @Resource
    private InquiryPrescriptionMapper inquiryPrescriptionMapper;

    @Resource
    private InquiryPrescriptionDetailMapper inquiryPrescriptionDetailMapper;

    @Resource
    private InquiryDoctorService inquiryDoctorService;

    @Resource
    private InquiryPharmacistApi inquiryPharmacistApi;

    @Resource
    private TenantApi tenantApi;

    @Resource
    private UserApi userApi;

    @DubboReference
    private InquiryApi inquiryApi;

    @DubboReference
    private TransmissionServicePackApi transmissionServicePackApi;

    @Resource
    private PrescriptionPricingProducer prescriptionPricingProducer;

    @Resource
    private InquiryHospitalMapper inquiryHospitalMapper;

    @Resource
    private DoctorPracticeService doctorPracticeService;


    @Autowired
    private ConfigApi configApi;

    @Autowired
    private FileApi fileApi;

    @Resource
    private InquiryPrescriptionFlushService inquiryPrescriptionFlushService;

    @Resource
    private PermissionApi permissionApi;

    @Resource
    private AbandonPrescriptionPostPassingProducer abandonPrescriptionPostPassingProducer;

    @Resource
    private AbandonRestorePrescriptionPostPassingProducer abandonRestorePrescriptionPostPassingProducer;

    @Resource
    private HospitalEmployeeService hospitalEmployeeService;


    /**
     * 处方查询参数组装策略
     */
    private static final Map<QuerySceneEnum, QueryStrategy> queryParamhandleMap = Maps.newHashMap();

    @Resource
    public void initHandler(List<QueryStrategy> strategies) {
        strategies.forEach(strategy -> queryParamhandleMap.put(strategy.getQueryScene(), strategy));
    }

    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private InquiryPrescriptionServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }

    @Override
    public InquiryPrescriptionRespDTO createInquiryPrescription(InquiryPrescriptionSaveReqVO createReqVO) {
        // 插入
        InquiryPrescriptionDO inquiryPrescription = InquiryPrescriptionConvert.INSTANCE.initPrescriptionDO(createReqVO);
        // 设置租户信息
        Optional.ofNullable(tenantApi.getTenant(createReqVO.getTenantId())).ifPresent(tenantDto -> inquiryPrescription.setTenantName(tenantDto.getName()));
        // 设置医生信息
        Optional.ofNullable(inquiryDoctorService.getRequireInquiryDoctorByDoctorPref(createReqVO.getDoctorPref())).ifPresent(inquiryDoctorRespDto -> inquiryPrescription.setDoctorName(inquiryDoctorRespDto.getName()));
        // 设置药师信息
        // Optional.ofNullable(inquiryPharmacistApi.getPharmacistByPref(createReqVO.getPharmacistPref())).ifPresent(pharmacistDto -> inquiryPrescription.setPharmacistName(pharmacistDto.getName()));
        inquiryPrescriptionMapper.insert(inquiryPrescription);
        // 返回
        return InquiryPrescriptionConvert.INSTANCE.convertDO2DTO(inquiryPrescription);
    }

    @Override
    public void updateInquiryPrescription(InquiryPrescriptionSaveReqVO updateReqVO) {
        // 校验存在
        validateInquiryPrescriptionExists(updateReqVO.getId());
        // 更新
        InquiryPrescriptionDO updateObj = BeanUtils.toBean(updateReqVO, InquiryPrescriptionDO.class);
        inquiryPrescriptionMapper.updateById(updateObj);
        // 场景：强制更新分配人 为null也设置
        if (updateReqVO.isUpdateDistributeUserId()) {
            inquiryPrescriptionMapper.update(new UpdateWrapper<InquiryPrescriptionDO>().set("distribute_user_id", updateReqVO.getDistributeUserId()).eq("id", updateObj.getId()));
        }
    }

    @Override
    public void deleteInquiryPrescription(Long id) {
        // 校验存在
        validateInquiryPrescriptionExists(id);
        // 删除
        inquiryPrescriptionMapper.deleteById(id);
    }

    private InquiryPrescriptionDO validateInquiryPrescriptionExists(Long id) {
        final InquiryPrescriptionDO prescriptionDO = inquiryPrescriptionMapper.selectById(id);
        if (prescriptionDO == null) {
            throw exception(INQUIRY_PRESCRIPTION_NOT_EXISTS);
        }
        return prescriptionDO;
    }

    private InquiryPrescriptionDO validateInquiryPrescriptionExists(String pref) {
        InquiryPrescriptionDO prescriptionDO = inquiryPrescriptionMapper.selectOne(InquiryPrescriptionDO::getPref, pref);
        if (prescriptionDO == null) {
            throw exception(INQUIRY_PRESCRIPTION_NOT_EXISTS);
        }
        return prescriptionDO;
    }

    @Override
    @InquiryDateType
    public InquiryPrescriptionRespVO getInquiryPrescription(Long id) {
        InquiryPrescriptionDO prescriptionDO = inquiryPrescriptionMapper.selectById(id);
        if (ObjectUtil.isEmpty(prescriptionDO)) {
            throw exception(INQUIRY_PRESCRIPTION_NOT_EXISTS);
        }
        // 处理处方日期
        List<InquiryPrescriptionDO> inquiryPrescriptionDOS = inquiryPrescriptionFlushService.getPrescriptionsHandleDateType(Collections.singletonList(prescriptionDO));

        // 组装编码
        InquiryPrescriptionRespVO respVO = InquiryPrescriptionConvert.INSTANCE.convertVO(inquiryPrescriptionDOS.getFirst());
        Optional.ofNullable(inquiryHospitalMapper.selectOne(InquiryHospitalDO::getPref, prescriptionDO.getHospitalPref())).ifPresent(inquiryHospitalDO -> {
            respVO.setHospitalName(inquiryHospitalDO.getName());
            respVO.setInstitutionCode(inquiryHospitalDO.getInstitutionCode());
        });
        Optional.ofNullable(inquiryDoctorService.getDoctorCardInfoByDoctorPref(prescriptionDO.getDoctorPref())).ifPresent(p -> {
            respVO.setDoctorMedicareNo(p.getDoctorMedicareNo());
        });

        // 问诊单信息
        InquiryRecordDto inquiryRecord = inquiryApi.getInquiryRecord(prescriptionDO.getInquiryPref());
        // 问诊单详情
        InquiryRecordDetailDto inquiryRecordDetail = inquiryApi.getInquiryRecordDetail(prescriptionDO.getInquiryPref());
        InquiryPrescriptionConvert.INSTANCE.fillInquiryDetail(respVO, inquiryRecordDetail, inquiryRecord);

        // 处理处方划价标记 - WEB && 是门店
        handlePrescriptionPriceStatus(respVO);
        return respVO;
    }

    private void handlePrescriptionPriceStatus(InquiryPrescriptionRespVO respVO) {
        Integer pricingStatus = respVO.extGet().getPricingStatus();
        if (PrescriptionPricingTypeEnum.isPriced(pricingStatus)) {
            return;
        }
        // 如果不是已划价- 判断是否监管划价，如果不是则设置成普通划价状态
        boolean needPriced = supervisionPricingHospital(respVO.getTenantId(), respVO.getHospitalPref());
        if (!(needPriced || !Objects.equals(pricingStatus, PrescriptionPricingTypeEnum.NEED_PRICING.getCode()))) {
            respVO.extGet().setPricingStatus(PrescriptionPricingTypeEnum.DEFAULT.getCode());
        }
    }

    @Override
    public String printInquiryPrescription(Long id) {
        InquiryPrescriptionDO prescriptionDO = inquiryPrescriptionMapper.selectById(id);
        if (ObjectUtil.isEmpty(prescriptionDO)) {
            throw exception(INQUIRY_PRESCRIPTION_NOT_EXISTS);
        }
        // 校验是否监管划价
        if (supervisionPricingHospital(prescriptionDO.getTenantId(), prescriptionDO.getHospitalPref())
            && Objects.equals(prescriptionDO.extGet().getPricingStatus(), PrescriptionPricingTypeEnum.NEED_PRICING.getCode())) {
            throw exception(INQUIRY_PRESCRIPTION_NO_PRICING_ERROR, prescriptionDO.getPref());
        }
        // 处理处方笺日期格式
        List<InquiryPrescriptionDO> inquiryPrescriptionDOS = inquiryPrescriptionFlushService.getPrescriptionsHandleDateType(Collections.singletonList(prescriptionDO));

        // 修改打印状态为已打印
        inquiryPrescriptionMapper.updateById(InquiryPrescriptionDO.builder().id(id).printStatus(PrintStatusEnum.PRINTED.getStatusCode()).build());

        return inquiryPrescriptionDOS.getFirst().getPrescriptionPdfUrl();
    }


    @Override
    public List<String> printInquiryPrescriptions(InquiryPrescriptionPdfReqVO pdfReqVO) {

        List<InquiryPrescriptionDO> prescriptionPdfs = getAndCheckPrescriptionPdfs(pdfReqVO);

        // 批量修改打印状态为已打印
        inquiryPrescriptionMapper.batchUpdatePrintStatus(prescriptionPdfs.stream().map(InquiryPrescriptionDO::getId).toList(), PrintStatusEnum.PRINTED.getStatusCode());

        return prescriptionPdfs.stream().map(p -> StringUtils.defaultIfBlank(p.getPrescriptionPdfUrl(), p.getPrescriptionImgUrl())).collect(Collectors.toList());
    }


    @Override
    public List<String> batchCombinerPrintInquiryPrescription(InquiryPrescriptionPdfReqVO pdfReqVO) {
        // 获取可以打印的处方
        List<InquiryPrescriptionDO> prescriptionPdfs = getAndCheckPrescriptionPdfs(pdfReqVO);

        // 合并打印
        List<File> files = ImageCombinerUtil.combineImages(prescriptionPdfs.stream().map(InquiryPrescriptionDO::getPrescriptionImgUrl).toList(), pdfReqVO.getCombinerGroupSize());

        // 上传cos 后删除
        List<String> fileUrls = files.stream().map(file -> FileApiUtil.createFile(FileUtil.readBytes(file))).toList();

        files.forEach(file -> {
            FileUtil.del(file);
        });

        // 批量修改打印状态为已打印
        inquiryPrescriptionMapper.batchUpdatePrintStatus(prescriptionPdfs.stream().map(InquiryPrescriptionDO::getId).toList(), PrintStatusEnum.PRINTED.getStatusCode());

        return fileUrls;
    }

    @Override
    public void exportInquiryPrescriptionPdf(InquiryPrescriptionPdfReqVO pdfReqVO, HttpServletResponse response) {

        List<InquiryPrescriptionDO> prescriptionPdfs = getAndCheckPrescriptionPdfs(pdfReqVO);

        // 1：批量下载文件
        try (ZipOutputStream zos = new ZipOutputStream(response.getOutputStream(), StandardCharsets.UTF_8)) {

            response.setContentType("application/zip");

            prescriptionPdfs.parallelStream().forEach(prescription -> {
                String url = StringUtils.defaultIfBlank(prescription.getPrescriptionImgUrl(), prescription.getPrescriptionPdfUrl());
                String fileName = FileUtils.buildFileName(prescription.getPatientName() + "-" + prescription.getPref() + "-" + DateTimeFormatter.ofPattern("yyyyMMdd").format(prescription.getOutPrescriptionTime()),
                    url);

                try (InputStream in = UrlConUtil.getStreamRetry("GET", url, 3000)) {
                    ZipEntry entry = new ZipEntry(fileName);
                    synchronized (zos) {
                        zos.putNextEntry(entry);
                        IOUtils.copy(in, zos);
                        zos.closeEntry();
                    }
                } catch (IOException e) {
                }
            });
        } catch (IOException e) {
            log.error("文件写入Zip异常：{}", e.getMessage(), e);
            throw exception(INQUIRY_PRESCRIPTION_PDF_DOWN_FAIL, e);
        }
    }

    /**
     * 打印、 导出 获取并校验处方pdf数据  + 处理刷日期格式
     *
     * @param pdfReqVO
     * @return
     */
    private List<InquiryPrescriptionDO> getAndCheckPrescriptionPdfs(InquiryPrescriptionPdfReqVO pdfReqVO) {

        int maxSize = NumberUtils.toInt(configApi.getConfigValueByKey(pdfReqVO.getMaxSizeKey()), 50);
        if (CollUtil.size(pdfReqVO.getIds()) > maxSize) {
            throw exception(INQUIRY_PRESCRIPTION_MAX_SIZE_ERROR, pdfReqVO.getOperate(), maxSize);
        }

        List<InquiryPrescriptionDO> prescriptionDOS = inquiryPrescriptionMapper.selectBatchIds(pdfReqVO.getIds());
        if (CollUtil.isEmpty(prescriptionDOS)) {
            throw exception(INQUIRY_PRESCRIPTION_NO_DATA_ERROR, pdfReqVO.getOperate());
        }
        // 过滤未完成的处方
        // String unOperatePref = prescriptionDOS.stream().filter(p -> !PrescriptionStatusEnum.isEndStatus(p.getStatus())).map(InquiryPrescriptionDO::getPref)
        //     .collect(Collectors.joining(","));
        // if (StringUtils.isNotBlank(unOperatePref)) {
        //     throw exception(INQUIRY_PRESCRIPTION_UN_OPERATE_ERROR, pdfReqVO.getOperate(), unOperatePref);
        // }

        // prescriptionDOS = prescriptionDOS.stream().filter(p -> PrescriptionStatusEnum.isEndStatus(p.getStatus())).toList();
        // if (CollUtil.isEmpty(prescriptionDOS)) {
        //     throw exception(INQUIRY_PRESCRIPTION_NO_DATA_ERROR, pdfReqVO.getOperate());
        // }

        // 校验 监管处方划价
        TransmissionFunConfigOptionDTO configOptionDTO = getInternetConfigOptionDTOCommonResult(prescriptionDOS.getFirst().getTenantId());
        if (configOptionDTO != null && StringUtils.isNotBlank(configOptionDTO.getPricingHospitalPref())) {
            List<InquiryPrescriptionDO> list = prescriptionDOS.stream().filter(p -> StringUtils.contains(configOptionDTO.getPricingHospitalPref(), p.getHospitalPref())
                && Objects.equals(p.extGet().getPricingStatus(), PrescriptionPricingTypeEnum.NEED_PRICING.getCode())).toList();
            if (CollUtil.isNotEmpty(list)) {
                throw exception(INQUIRY_PRESCRIPTION_NO_PRICING_ERROR, list.stream().map(InquiryPrescriptionDO::getPref).collect(Collectors.toList()));
            }
        }
        // 处理处方笺日期格式并返回
        return inquiryPrescriptionFlushService.getPrescriptionsHandleDateType(prescriptionDOS);
    }

    @Override
    @InquiryDateType
    public PageResult<InquiryPrescriptionRespVO> getInquiryPrescriptionPage(InquiryPrescriptionPageReqVO pageReqVO) {
        handleParam(pageReqVO);
        if (pageReqVO.isHospitalEmployee() && org.springframework.util.CollectionUtils.isEmpty(pageReqVO.getHospitalList())) {
            return PageResult.empty();
        }
        PageResult<InquiryPrescriptionDO> pageResult = inquiryPrescriptionMapper.selectPage(pageReqVO);
        return InquiryPrescriptionConvert.INSTANCE.convertPage(pageResult);
    }

    /**
     * 处方数量统计
     *
     * @return 处方数量统计
     */
    @Override
    public InquiryPrescriptionCountVO getInquiryPrescriptionCount() {
        InquiryPrescriptionCountVO result = new InquiryPrescriptionCountVO();
        AdminUserRespDTO user = userApi.getUser();
        // 门店处方数量统计
        if (!TenantConstant.isSystemTenant()) {
            setPrescriptionCounts(result, TenantContextHolder.getTenantId());
        }

        // 药师处方数量统计
        if (RoleCodeEnum.pharmacist(user.getRoleCodes())) {
            InquiryPharmacistDto pharmacist = inquiryPharmacistApi.getPharmacistByUserId(user.getId());
            if (pharmacist != null) {
                setAuditPrescriptionCounts(result, pharmacist);
            }
        }

        return result;
    }

    @Override
    public InquiryPrescriptionRespVO queryByCondition(InquiryPrescriptionQueryDTO prescriptionQueryDTO) {
        InquiryPrescriptionDO inquiryPrescriptionDO = inquiryPrescriptionMapper.queryByCondition(prescriptionQueryDTO);
        return InquiryPrescriptionConvert.INSTANCE.convertVO(inquiryPrescriptionDO);
    }


    @Override
    public Long getPrescriptionCount(InquiryPrescriptionPageReqVO reqVO) {
        return inquiryPrescriptionMapper.getPrescriptionCount(reqVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean distributePharmacist(Long id, Long userId) {
        validateInquiryPrescriptionExists(id);
        return inquiryPrescriptionMapper.distributePharmacist(id, userId, PrescriptionStatusEnum.WAIT_APPROVAL.getStatusCode()) > 0;
    }

    @Override
    public boolean releasePharmacist(Long id, Long userId) {
        validateInquiryPrescriptionExists(id);
        return inquiryPrescriptionMapper.releasePharmacist(id, userId, PrescriptionStatusEnum.WAIT_APPROVAL.getStatusCode()) > 0;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void postProcessIssuesPrescription(String pref) {
        log.info("处理以上开方后置处理postProcessIssuesPrescription,pref={},t:{}", pref, TenantContextHolder.getTenantId());
        InquiryPrescriptionDO prescriptionDO = validateInquiryPrescriptionExists(pref);
        // 处理处方划价标记
        if (supervisionPricingHospital(prescriptionDO.getTenantId(), prescriptionDO.getHospitalPref())) {
            log.info("处理处方划价标记,pref={}", pref);
            inquiryPrescriptionMapper.updateById(InquiryPrescriptionDO.builder().id(prescriptionDO.getId())
                .ext(prescriptionDO.extGet().setPricingStatus(PrescriptionPricingTypeEnum.NEED_PRICING.getCode())).build());
        }
    }

    /**
     * 获取需要监管划价的医院
     *
     * @param tenantId 门店id
     * @return true 需要监管划价 ， false不需要
     */
    private boolean supervisionPricingHospital(Long tenantId, String hospitalPref) {
        TransmissionFunConfigOptionDTO configItem = getInternetConfigOptionDTOCommonResult(tenantId);
        return configItem != null && StringUtils.contains(configItem.getPricingHospitalPref(), hospitalPref);
    }

    private TransmissionFunConfigOptionDTO getInternetConfigOptionDTOCommonResult(Long tenantId) {
        try {
            TransmissionConfigReqDTO configReqDTO = TransmissionConfigReqDTO.builder().tenantId(tenantId).nodeType(NodeTypeEnum.INTERNET_SUPERVISION_CONDITION).build();
            CommonResult<TransmissionFunConfigOptionDTO> configItem = transmissionServicePackApi.selectConfigItem(configReqDTO, TransmissionFunConfigOptionDTO.class);
            if (configItem.isSuccess() && configItem.getData() != null) {
                return configItem.getData();
            }
        } catch (Exception e) {
            log.error("获取需要监管划价的医院失败,tenantId:{},", tenantId, e);
        }
        return null;
    }

    @Resource
    @Lazy
    private SaasPrescriptionExternalService saasPrescriptionExternalService;


    @Override
    public Long pricingInquiryPrescription(InquiryPrescriptionPricingVO pricingVO) {
        // 1.处方划价
        InquiryPrescriptionDO prescriptionDO = getSelf().pricing(pricingVO);
        // 2.发送处方划价mq
        if (Objects.equals(pricingVO.getType(), PricingOperateTypeEnum.CONFIRM.getType())) {
            prescriptionPricingProducer.sendMessage(PrescriptionPricingEvent.builder().msg(PrescriptionPricingMessage.builder().prescriptionPref(pricingVO.getPrescriptionPref()).build()).build(), LocalDateTime.now().plusSeconds(1));
            // saasPrescriptionExternalService.externalSupervision1(pricingVO.getPrescriptionPref());
        }

        return prescriptionDO.getId();
    }

    @Override
    public IPage<PatientSimpleDTO> getPrescriptionPatientList(InquiryPrescriptionQueryDTO queryDTO) {
        InquiryPrescriptionPageReqVO reqVO = InquiryPrescriptionPageReqVO.builder().pharmacistPref(queryDTO.getPharmacistPref()).tenantId(queryDTO.getTenantId()).startTime(queryDTO.getAuditPrescriptionTime()[0])
            .endTime(queryDTO.getAuditPrescriptionTime()[1])
            .patientName(queryDTO.getPatientName()).build();
        reqVO.setPageNo(queryDTO.getPageNum());
        reqVO.setPageSize(queryDTO.getPageSize());
        return inquiryPrescriptionMapper.getPrescriptionPatientList(MyBatisUtils.buildPage(reqVO), reqVO);
    }

    @Transactional(rollbackFor = Exception.class)
    public InquiryPrescriptionDO pricing(InquiryPrescriptionPricingVO pricingVO) {
        InquiryPrescriptionDO prescriptionDO = validateInquiryPrescriptionExists(pricingVO.getPrescriptionPref());
        if (!Objects.equals(prescriptionDO.getStatus(), PrescriptionStatusEnum.APPROVAL.getStatusCode())) {
            throw exception(INQUIRY_PRESCRIPTION_PRICING_NO_AUDIT_ERROR);
        }
        if (prescriptionDO.extGet() != null && PrescriptionPricingTypeEnum.isPriced(prescriptionDO.extGet().getPricingStatus())) {
            throw exception(INQUIRY_PRESCRIPTION_PRICING_ERROR);
        }
        BigDecimal totalPrice = pricingVO.getPricingPrice();
        // 1.校验、save明细商品单价
        if (CollUtil.isNotEmpty(pricingVO.getDetailPricingVos())) {
            Map<Long, PrescriptionDetailPricingVO> detailPricingVOMap = pricingVO.getDetailPricingVos().stream().collect(Collectors.toMap(PrescriptionDetailPricingVO::getId, Function.identity(), (a, b) -> b));
            List<InquiryPrescriptionDetailDO> detailDOS = inquiryPrescriptionDetailMapper.selectByPrescriptionPref(prescriptionDO.getPref());
            if (CollUtil.size(detailDOS) != CollUtil.size(pricingVO.getDetailPricingVos()) || detailDOS.stream().anyMatch(d -> !detailPricingVOMap.containsKey(d.getId()))) {
                throw exception(INQUIRY_PRESCRIPTION_PRICING_DETAIL_ERROR);
            }
            List<InquiryPrescriptionDetailDO> updateDetailDOList = detailDOS.stream().filter(d -> detailPricingVOMap.containsKey(d.getId())).map(d -> {
                PrescriptionDetailPricingVO detailPricingVO = detailPricingVOMap.get(d.getId());
                return InquiryPrescriptionDetailConvert.INSTANCE.convertPricing(d, detailPricingVO);
            }).collect(Collectors.toList());
            if (CollUtil.isNotEmpty(updateDetailDOList)) {
                inquiryPrescriptionDetailMapper.updateById(updateDetailDOList);
                totalPrice = updateDetailDOList.stream().map(InquiryPrescriptionDetailDO::getActualAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO).setScale(2, RoundingMode.HALF_UP);
            }
        }
        // 2.更新处方主单划价信息
        inquiryPrescriptionMapper.updateById(InquiryPrescriptionDO.builder().id(prescriptionDO.getId())
            .ext(prescriptionDO.extGet().setPricingStatus(
                    Objects.equals(pricingVO.getType(), PricingOperateTypeEnum.CONFIRM.getType()) ? PrescriptionPricingTypeEnum.endPriced(prescriptionDO.extGet().getPricingStatus()) : prescriptionDO.extGet().getPricingStatus())
                .setPricingPrice(totalPrice)
                .setSetlInvoiceNumber(pricingVO.getSetlInvoiceNumber())).build());
        return prescriptionDO;
    }

    /**
     * 门店查询总处方量
     *
     * @param result   返回结果
     * @param tenantId 租户id
     */
    private void setPrescriptionCounts(InquiryPrescriptionCountVO result, Long tenantId) {
        // 组装查询参数
        InquiryPrescriptionPageReqVO reqVO = InquiryPrescriptionPageReqVO.builder().tenantId(tenantId).enable(CommonStatusEnum.ENABLE.getStatus()).build();
        // 查询门店所有处方数量
        result.setTotalPrescriptionCount(inquiryPrescriptionMapper.getPrescriptionCount(reqVO));
        // 设置时间查询昨日处方
        reqVO.setOutPrescriptionTime(DateUtil.getYesterdayStartAndEnd());
        result.setYesterdayPrescriptionCount(inquiryPrescriptionMapper.getPrescriptionCount(reqVO));
        // 设置时间查询本月处方
        reqVO.setOutPrescriptionTime(DateUtil.getMonthStartAndPreviousMonthEnd());
        result.setMonthPrescriptionCount(inquiryPrescriptionMapper.getPrescriptionCount(reqVO));

    }

    /**
     * 药师查询总处方量
     *
     * @param result     返回结果
     * @param pharmacist 药师信息
     */
    private void setAuditPrescriptionCounts(InquiryPrescriptionCountVO result, InquiryPharmacistDto pharmacist) {
        // 组装查询参数
        InquiryPrescriptionPageReqVO req = InquiryPrescriptionPageReqVO.builder()
            .pharmacistPref(pharmacist.getPref())
            .auditPrescriptionTime(DateUtil.getMonthStartAndPreviousMonthEnd())
            .enable(TenantConstant.isSystemTenant() ? null : CommonStatusEnum.ENABLE.getStatus()) // 门店查有效的
            .build();
        // 查询月审方量
        result.setMonthAuditPrescriptionCount(inquiryPrescriptionMapper.getPrescriptionCount(req));
        // 总审方量去除时间条件即可
        req.setAuditPrescriptionTime(null);
        // 总审方量
        result.setTotalAuditPrescriptionCount(inquiryPrescriptionMapper.getPrescriptionCount(req));
    }


    /**
     * 处方查询参数组装
     *
     * @param pageReqVO 查询参数
     */
    private void handleParam(InquiryPrescriptionPageReqVO pageReqVO) {
        boolean isPharmacist = permissionApi.hasAnyRoles(Objects.requireNonNull(getLoginUser()).getId(), RoleCodeEnum.PHARMACIST.getCode());
        // 管理后台且非药师，查询不做处理
        if (QuerySourceEnum.WEB.equals(pageReqVO.getQuerySource()) && TenantConstant.isSystemTenant() && !isPharmacist) {
            webQueryPrescription(pageReqVO);
            return;
        }

        // 获取参数组装策略
        QueryStrategy strategy = queryParamhandleMap.get(QuerySceneEnum.getEnumByCode(pageReqVO.getQueryScene()));
        // 设置参数
        strategy.setParam(pageReqVO);
    }

    /**
     * web 端查询处方
     *
     * @param pageReqVO
     */
    private void webQueryPrescription(InquiryPrescriptionPageReqVO pageReqVO) {
        // 获取用户信息
        AdminUserRespDTO userInfo = userApi.getUser();
        if (!RoleCodeEnum.isHospitalEmployee(userInfo.getRoleCodes())) {
            return;
        }
        List<HospitalBindRespVO> hospitalBindRespVOS = hospitalEmployeeService.getBindHospitalsByUserId(userInfo.getId());
        List<String> hospitalPrefs = hospitalBindRespVOS.stream().map(HospitalBindRespVO::getHospitalPref).toList();
        pageReqVO.setHospitalEmployee(Boolean.TRUE);
        pageReqVO.setHospitalList(hospitalPrefs);
    }

    @Override
    public List<InquiryPrescriptionExcelRespVO> getInquiryPrescriptionExcelList(InquiryPrescriptionPageReqVO pageReqVO) {
        PageResult<InquiryPrescriptionRespVO> pageResult = this.getInquiryPrescriptionPage(pageReqVO);

        if (pageResult == null || CollUtil.isEmpty(pageResult.getList())) {
            throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), "暂无数据导出");
        }

        List<String> prescriptionPrefList = pageResult.getList().stream().map(InquiryPrescriptionRespVO::getPref).filter(StringUtils::isNotBlank).toList();
        List<InquiryPrescriptionDetailDO> detailDOS = inquiryPrescriptionDetailMapper.selectByPrescriptionPrefList(prescriptionPrefList);
        Map<String, List<InquiryPrescriptionDetailDO>> detailDOMap = Optional.ofNullable(detailDOS).orElse(Collections.emptyList())
            .stream().collect(Collectors.groupingBy(InquiryPrescriptionDetailDO::getPrescriptionPref));

        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

        List<InquiryPrescriptionExcelRespVO> resultList = new ArrayList<>();

        for (InquiryPrescriptionRespVO inquiryPrescriptionRespVO : pageResult.getList()) {
            InquiryPrescriptionExcelRespVO inquiryPrescriptionExcelRespVO = InquiryPrescriptionConvert.INSTANCE.convertVO2ExcelVO(inquiryPrescriptionRespVO);

            if (inquiryPrescriptionRespVO.extGet().getPricingPrice() != null
                && inquiryPrescriptionRespVO.extGet().getPricingPrice().compareTo(BigDecimal.ZERO) > 0) {
                inquiryPrescriptionExcelRespVO.setPricingPriceStr(inquiryPrescriptionRespVO.extGet().getPricingPrice().toString());
            }

            inquiryPrescriptionExcelRespVO.setOutPrescriptionTimeStr(inquiryPrescriptionRespVO.getOutPrescriptionTime() != null
                ? formatter.format(inquiryPrescriptionRespVO.getOutPrescriptionTime()) : "");
            inquiryPrescriptionExcelRespVO.setAuditPrescriptionTimeStr(inquiryPrescriptionRespVO.getAuditPrescriptionTime() != null
                ? formatter.format(inquiryPrescriptionRespVO.getAuditPrescriptionTime()) : "");

            // 构建处方药品excel信息
            if (detailDOMap.containsKey(inquiryPrescriptionRespVO.getPref())) {
                for (int i = 0; i < detailDOMap.get(inquiryPrescriptionRespVO.getPref()).size(); i++) {
                    InquiryPrescriptionDetailDO inquiryPrescriptionDetailDO = detailDOMap.get(inquiryPrescriptionRespVO.getPref()).get(i);
                    if (i == 0) {
                        // 构建处方药品信息
                        this.buildInquiryPrescriptionExcelDrugInfo(inquiryPrescriptionExcelRespVO, inquiryPrescriptionDetailDO);
                        if (inquiryPrescriptionRespVO.getMedicineType() != null && inquiryPrescriptionRespVO.getMedicineType() == MedicineTypeEnum.CHINESE_MEDICINE.getCode()
                            && inquiryPrescriptionRespVO.getExt() != null) {
                            inquiryPrescriptionExcelRespVO.setTcmDosageCombinationDesc(
                                "共" + inquiryPrescriptionRespVO.getExt().getTcmTotalDosage() + "剂;每日" + inquiryPrescriptionRespVO.getExt().getTcmDailyDosage() + "剂;每剂分" + inquiryPrescriptionRespVO.getExt().getTcmUsage()
                                    + "次用药;");
                            inquiryPrescriptionExcelRespVO.setTcmDirections(inquiryPrescriptionRespVO.getExt().getTcmDirections());
                            inquiryPrescriptionExcelRespVO.setTcmProcessingMethod(inquiryPrescriptionRespVO.getExt().getTcmProcessingMethod());
                        }
                        resultList.add(inquiryPrescriptionExcelRespVO);
                    } else {
                        InquiryPrescriptionExcelRespVO drugInquiryPrescriptionExcelRespVO = new InquiryPrescriptionExcelRespVO();
                        // 构建处方药品信息
                        this.buildInquiryPrescriptionExcelDrugInfo(drugInquiryPrescriptionExcelRespVO, inquiryPrescriptionDetailDO);
                        resultList.add(drugInquiryPrescriptionExcelRespVO);
                    }
                }
                continue;
            }

            // 兜底逻辑 , 以免处方单没有详情数据
            resultList.add(inquiryPrescriptionExcelRespVO);
        }

        log.info("[getInquiryPrescriptionExcelList][导出数据:{}]", resultList.size());

        return resultList;

    }


    /**
     * 构建处方药品信息
     *
     * @param drugInquiryPrescriptionExcelRespVO
     * @param inquiryPrescriptionDetailDO
     */
    private void buildInquiryPrescriptionExcelDrugInfo(InquiryPrescriptionExcelRespVO drugInquiryPrescriptionExcelRespVO, InquiryPrescriptionDetailDO inquiryPrescriptionDetailDO) {
        drugInquiryPrescriptionExcelRespVO.setCommonName(inquiryPrescriptionDetailDO.getCommonName());
        drugInquiryPrescriptionExcelRespVO.setAttributeSpecification(inquiryPrescriptionDetailDO.getAttributeSpecification());
        drugInquiryPrescriptionExcelRespVO.setQuantity(inquiryPrescriptionDetailDO.getQuantity());
        drugInquiryPrescriptionExcelRespVO.setDirections(inquiryPrescriptionDetailDO.getDirections());
        drugInquiryPrescriptionExcelRespVO.setSingleDose(inquiryPrescriptionDetailDO.getSingleDose());
        drugInquiryPrescriptionExcelRespVO.setSingleUnit(inquiryPrescriptionDetailDO.getSingleUnit());
        drugInquiryPrescriptionExcelRespVO.setUseFrequency(inquiryPrescriptionDetailDO.getUseFrequency());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void abandon(InquiryPrescriptionAbandonVO abandonVO) {

        InquiryPrescriptionDO prescriptionDO = validateInquiryPrescriptionExists(abandonVO.getId());

        if (!PrescriptionStatusEnum.isEndStatus(prescriptionDO.getStatus())) {
            throw exception0(-1, "处方状态未完结,不可操作");
        }

        InquiryRecordDto inquiryRecord = inquiryApi.getInquiryRecord(prescriptionDO.getInquiryPref());

        abandonVO.setAbandonUser(userApi.getUser().getNickname());

        InquiryPrescriptionDO updateObj = InquiryPrescriptionConvert.INSTANCE.convertAbandon2DO(abandonVO, prescriptionDO);
        // 废弃处方
        inquiryPrescriptionMapper.updateById(updateObj);

        InquiryRecordDto recordDto = InquiryRecordDto.builder().id(inquiryRecord.getId()).enable(CommonStatusEnum.DISABLE.getStatus()).build();
        // 废弃问诊
        inquiryApi.updateInquiry(recordDto);

        // 发送废弃处方后置处理MQ
        abandonPrescriptionPostPassingMQ(prescriptionDO);

    }

    private void abandonPrescriptionPostPassingMQ(InquiryPrescriptionDO prescriptionDO) {
        try {
            // 构建MQ消息体
            PrescriptionMqCommonMessage message = PrescriptionMqCommonMessage.builder()
                .prescriptionPref(prescriptionDO.getPref())
                .prescriptionImgUrl(prescriptionDO.getPrescriptionImgUrl())
                .prescriptionPdfUrl(prescriptionDO.getPrescriptionPdfUrl())
                .build();

            // 构建MQ事件
            AbandonPrescriptionPostPassingEvent event = AbandonPrescriptionPostPassingEvent.builder()
                .msg(message)
                .build();

            // 发送MQ消息
            abandonPrescriptionPostPassingProducer.sendMessage(event);

            log.info("【abandonPrescriptionPostPassingMQ】MQ消息发送成功，处方单号：{}", prescriptionDO.getPref());

        } catch (Exception e) {
            log.error("【abandonPrescriptionPostPassingMQ】MQ消息发送失败，处方单号：{}，异常信息：{}",
                prescriptionDO.getPref(), e.getMessage(), e);
        }
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void abandonRestore(InquiryPrescriptionAbandonVO abandonVO) {

        if (CollUtil.size(abandonVO.getIds()) > 10) {
            throw exception0(-1, "最大仅支持选择10条数据哦~");
        }

        List<InquiryPrescriptionDO> dos = inquiryPrescriptionMapper.selectBatchIds(abandonVO.getIds());

        List<InquiryRecordDto> records = inquiryApi.getInquiryRecords(CollectionUtils.convertList(dos, InquiryPrescriptionDO::getInquiryPref));

        List<InquiryPrescriptionDO> updatePrescriptionDos = dos.stream().map(record -> InquiryPrescriptionDO.builder().id(record.getId()).enable(CommonStatusEnum.ENABLE.getStatus()).build()).toList();
        // 修改处方
        inquiryPrescriptionMapper.updateBatch(updatePrescriptionDos);

        List<InquiryRecordDto> updateRecords = records.stream().map(record -> InquiryRecordDto.builder().id(record.getId()).enable(CommonStatusEnum.ENABLE.getStatus()).build()).toList();
        // 修改问诊
        inquiryApi.updateInquirys(updateRecords);

        // 批量发送恢复处方后置处理MQ
        batchSendAbandonRestorePrescriptionPostPassingMQ(dos);

    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateInquiryPrescriptionByAdmin(InquiryPrescriptionSaveReqVO updateReqVO) {
        // 校验存在
        validateInquiryPrescriptionExists(updateReqVO.getId());
        validateInquiryPrescriptionDetailExistsByReqVOList(updateReqVO.getInquiryPrescriptionDetailList());

        // 更新处方
        InquiryPrescriptionDO inquiryPrescriptionDO = BeanUtils.toBean(updateReqVO, InquiryPrescriptionDO.class);
        inquiryPrescriptionMapper.updateById(inquiryPrescriptionDO);
        // 更新处方详情
        InquiryPrescriptionDetailDO inquiryPrescriptionDetailDO = BeanUtils.toBean(updateReqVO, InquiryPrescriptionDetailDO.class);
        inquiryPrescriptionDetailMapper.updateById(inquiryPrescriptionDetailDO);
    }

    /**
     * 条件查询处方列表
     *
     * @param queryReqVo
     * @return
     */
    @Override
    public List<InquiryPrescriptionDO> queryListByCondition(InquiryPrescriptionPageReqVO queryReqVo) {
        return inquiryPrescriptionMapper.selectList(queryReqVo);
    }


    @Override
    public List<InquiryPrescriptionDO> queryListByPrefs(List<String> prefs) {
        return inquiryPrescriptionMapper.selectList(InquiryPrescriptionDO::getPref, prefs);
    }

    /**
     * 校验处方详情是否存在
     *
     * @param inquiryPrescriptionDetailList
     */
    private void validateInquiryPrescriptionDetailExistsByReqVOList(List<InquiryPrescriptionDetailSaveReqVO> inquiryPrescriptionDetailList) {

        if (CollUtil.isEmpty(inquiryPrescriptionDetailList)) {
            throw exception(INQUIRY_PRESCRIPTION_DETAIL_NOT_EXISTS);
        }

        List<Long> idList = inquiryPrescriptionDetailList.stream().filter(item -> item.getId() != null).map(InquiryPrescriptionDetailSaveReqVO::getId).distinct().toList();

        validateInquiryPrescriptionDetailExistsByIdList(idList);
    }

    /**
     * 校验处方详情是否存在
     *
     * @param idList
     */
    private void validateInquiryPrescriptionDetailExistsByIdList(List<Long> idList) {

        if (CollUtil.isEmpty(idList)) {
            throw exception(INQUIRY_PRESCRIPTION_DETAIL_NOT_EXISTS);
        }

        List<InquiryPrescriptionDetailDO> inquiryPrescriptionDetailDOList = inquiryPrescriptionDetailMapper.selectByIdList(idList);

        if (CollUtil.isEmpty(inquiryPrescriptionDetailDOList) || inquiryPrescriptionDetailDOList.size() != idList.size()) {
            throw exception(INQUIRY_PRESCRIPTION_DETAIL_NOT_EXISTS);
        }
    }


    @Override
    public PageResult<InquiryPrescriptionDO> getFlushOfflinePrescriptionPage(InquiryPrescriptionFlushQueryDTO flushQueryDTO) {
        return inquiryPrescriptionMapper.getFlushOfflinePrescriptionPage(flushQueryDTO);
    }

    @Override
    public InquiryPrescriptionRespVO getInquiryPrescriptionWithPdf(InquiryPrescriptionQueryDTO queryDTO) {

        InquiryPrescriptionDO prescriptionDO = inquiryPrescriptionMapper.queryByCondition(queryDTO);
        if (prescriptionDO == null) {
            return null;
        }
        // 处理日期格式
        List<InquiryPrescriptionDO> inquiryPrescriptionDOS = inquiryPrescriptionFlushService.getPrescriptionsHandleDateType(Collections.singletonList(prescriptionDO));

        return InquiryPrescriptionConvert.INSTANCE.convertVO(inquiryPrescriptionDOS.getFirst());
    }

    /**
     * 批量发送处方信息到三方SaaS系统
     *
     * @param prescriptionDOs 处方DO列表
     */
    private void batchSendAbandonRestorePrescriptionPostPassingMQ(List<InquiryPrescriptionDO> prescriptionDOs) {
        if (CollUtil.isEmpty(prescriptionDOs)) {
            return;
        }

        log.info("【batchSendAbandonRestorePrescriptionPostPassingMQ】开始处理，处方数量：{}", prescriptionDOs.size());

        // 遍历每个处方，单独发送MQ消息
        for (InquiryPrescriptionDO prescriptionDO : prescriptionDOs) {
            try {
                // 构建MQ消息体
                PrescriptionMqCommonMessage message = PrescriptionMqCommonMessage.builder()
                    .prescriptionPref(prescriptionDO.getPref())
                    .prescriptionImgUrl(prescriptionDO.getPrescriptionImgUrl())
                    .prescriptionPdfUrl(prescriptionDO.getPrescriptionPdfUrl())
                    .build();

                // 构建MQ事件
                AbandonRestorePrescriptionPostPassingEvent event = AbandonRestorePrescriptionPostPassingEvent.builder()
                    .msg(message)
                    .build();

                // 发送MQ消息
                abandonRestorePrescriptionPostPassingProducer.sendMessage(event);

                log.info("【batchSendAbandonRestorePrescriptionPostPassingMQ】单个处方MQ消息发送成功，处方单号：{}", prescriptionDO.getPref());

            } catch (Exception e) {
                log.error("【batchSendAbandonRestorePrescriptionPostPassingMQ】单个处方MQ消息发送失败，处方单号：{}，异常信息：{}",
                    prescriptionDO.getPref(), e.getMessage(), e);
                // 单个失败不影响其他处方的发送，继续处理下一个
            }
        }

        log.info("【batchSendAbandonRestorePrescriptionPostPassingMQ】处理完成，总处方数量：{}", prescriptionDOs.size());
    }
}