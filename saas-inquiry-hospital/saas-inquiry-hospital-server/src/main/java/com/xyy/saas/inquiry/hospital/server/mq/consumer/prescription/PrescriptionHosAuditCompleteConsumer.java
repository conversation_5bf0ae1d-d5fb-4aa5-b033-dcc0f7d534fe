package com.xyy.saas.inquiry.hospital.server.mq.consumer.prescription;// package com.xyy.saas.inquiry.hospital.server.mq.consumer.prescription;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.hospital.server.service.prescription.external.SaasPrescriptionExternalService;
import com.xyy.saas.inquiry.mq.prescription.PrescriptionHosAuditCompletedEvent;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 医院药师审方完成 - 上传处方到外部监管系统
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_hospital_server_mq_consumer_PrescriptionHosAuditCompleteConsumer",
    topic = PrescriptionHosAuditCompletedEvent.TOPIC)
public class PrescriptionHosAuditCompleteConsumer {

    @Resource
    private SaasPrescriptionExternalService saasPrescriptionExternalService;


    @EventBusListener
    public void externalPrescriptionSupervision(PrescriptionHosAuditCompletedEvent hosAuditCompletedEvent) {
        try {
            saasPrescriptionExternalService.externalSupervision1(hosAuditCompletedEvent.getMsg());
        } catch (Exception e) {
            log.error("externalPrescriptionSupervision1 error", e);
        }
    }

}
