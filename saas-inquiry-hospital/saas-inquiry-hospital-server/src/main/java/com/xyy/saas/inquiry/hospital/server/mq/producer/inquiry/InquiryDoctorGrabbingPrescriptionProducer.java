package com.xyy.saas.inquiry.hospital.server.mq.producer.inquiry;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.mq.inquiry.InquiryDoctorGrabbingPrescriptionEvent;
import org.springframework.stereotype.Component;

/**
 * @Author: cxy
 * @Description: 问诊医生抢单
 */
@Component
@EventBusProducer(
    topic = InquiryDoctorGrabbingPrescriptionEvent.TOPIC
)
public class InquiryDoctorGrabbingPrescriptionProducer extends EventBusRocketMQTemplate {

}
