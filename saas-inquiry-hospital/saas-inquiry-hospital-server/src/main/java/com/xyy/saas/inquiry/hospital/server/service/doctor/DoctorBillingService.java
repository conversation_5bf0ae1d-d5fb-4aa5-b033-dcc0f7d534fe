package com.xyy.saas.inquiry.hospital.server.service.doctor;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorBillingPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorBillingSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorBillingDO;
import jakarta.validation.Valid;


/**
 * 医生收款信息 Service 接口
 *
 * <AUTHOR>
 */
public interface DoctorBillingService {

    /**
     * 创建医生收款信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDoctorBilling(@Valid DoctorBillingSaveReqVO createReqVO);

    /**
     * 更新医生收款信息
     *
     * @param updateReqVO 更新信息
     */
    void updateDoctorBilling(@Valid DoctorBillingSaveReqVO updateReqVO);

    /**
     * 删除医生收款信息
     *
     * @param id 编号
     */
    void deleteDoctorBilling(Long id);

    /**
     * 获得医生收款信息
     *
     * @param id 编号
     * @return 医生收款信息
     */
    DoctorBillingDO getDoctorBilling(Long id);

    /**
     * 获得医生收款信息分页
     *
     * @param pageReqVO 分页查询
     * @return 医生收款信息分页
     */
    PageResult<DoctorBillingDO> getDoctorBillingPage(DoctorBillingPageReqVO pageReqVO);

}