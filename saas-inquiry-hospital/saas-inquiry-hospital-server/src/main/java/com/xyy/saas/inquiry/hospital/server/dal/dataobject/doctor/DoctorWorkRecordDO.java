package com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 医生工作履历记录 DO
 *
 * <AUTHOR>
 */
@TableName("saas_doctor_work_record")
@KeySequence("saas_doctor_work_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DoctorWorkRecordDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 医生id
     */
    private Long doctorId;
    /**
     * 工作单位名称
     */
    private String workUnitName;
    /**
     * 职位
     */
    private String jobPosition;
    /**
     * 证明人
     */
    private String prover;
    /**
     * 开始时间,eg:2021-03-20
     */
    private LocalDateTime startDate;
    /**
     * 结束时间,eg:2029-03-20
     */
    private LocalDateTime endDate;

}