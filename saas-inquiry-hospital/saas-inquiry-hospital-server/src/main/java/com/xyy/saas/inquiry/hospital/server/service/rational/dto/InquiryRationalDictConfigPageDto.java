package com.xyy.saas.inquiry.hospital.server.service.rational.dto;

import com.xyy.saas.inquiry.pojo.forward.ForwardPageDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * @Author:ch<PERSON><PERSON><PERSON><PERSON>
 * @Date:2023/11/06 18:40
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
public class InquiryRationalDictConfigPageDto extends ForwardPageDto {

    /**
     * 名称 eg:新生儿/抗生素
     */
    private String name;

}
