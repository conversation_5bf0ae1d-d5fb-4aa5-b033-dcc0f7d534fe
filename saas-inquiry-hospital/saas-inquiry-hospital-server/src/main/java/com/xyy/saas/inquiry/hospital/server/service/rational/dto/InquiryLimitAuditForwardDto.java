package com.xyy.saas.inquiry.hospital.server.service.rational.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 合理用药转发接口dto
 */
@Data
public class InquiryLimitAuditForwardDto implements Serializable {

    private String birthday;

    private String organSign;

    private String age;

    private int sex;

    /**
     * 1.肝功能异常限制、2.肾功能异常限制、3.肝肾功能异常限制
     */
    private Integer healthSituation;

    /**
     * 1.妊娠期限制、2.哺乳期限制、3.妊娠哺乳期限制
     */
    private Integer womenSituation;

    /**
     * 过敏成分名称集合
     */
    private List<String> integerList;


    /**
     * 诊断集合
     */
    private List<String> diagnosisNames;

    /**
     * 诊断编码集合
     */
    private List<String> diagnosisCodes;

    /**
     * 药品通用名集合
     */
    private List<String> drugs;

    /**
     * 药品集合
     */
    private List<DrugForwardDto> drugDtoList;

    /**
     * 药品类型
     */
    private Byte medicineType;

    /**
     * 是否是新问诊系统
     */
    private Boolean newSystem;

    /**
     * 问诊新系统需要校验的code集合
     */
    private List<Integer> limitDrugAuditCodeList;

}
