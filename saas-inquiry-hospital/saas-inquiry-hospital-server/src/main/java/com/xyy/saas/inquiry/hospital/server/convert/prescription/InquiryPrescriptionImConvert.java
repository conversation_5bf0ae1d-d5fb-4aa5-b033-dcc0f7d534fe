package com.xyy.saas.inquiry.hospital.server.convert.prescription;

import cn.hutool.core.util.ObjectUtil;
import com.xyy.saas.inquiry.enums.im.ImSourceTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionDateTypeEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorCardInfoDto;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.clinicalcase.InquiryClinicalCaseDO;
import com.xyy.saas.inquiry.im.api.message.dto.ImEvaluationMessageExtDto;
import com.xyy.saas.inquiry.im.api.message.dto.ImNotifyBubbleMessageExtDto;
import com.xyy.saas.inquiry.im.api.message.dto.ImPrecriptionMessageExtDto;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryImMessageDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import java.text.MessageFormat;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: xucao
 * @Date: 2025/2/12 10:05
 * @Description: 处方IM相关转换
 * @Version: 1.0
 */
@Mapper
public interface InquiryPrescriptionImConvert {

    InquiryPrescriptionImConvert INSTANCE = Mappers.getMapper(InquiryPrescriptionImConvert.class);

    String PRESCRIPTION_TEXT_MSG = "您好，您的处方已开具，请查收";
    String PRESCRIPTION_END_TEXT_MSG = "温馨提示:请严格按原处方和药品说明书使用，严禁超量超范围使用;如用药过程中出现病情变化或其它不适症状，请立即停药并及时就医。";
    String PRESCRIPTION_TIME_OUT_TEXT_MSG = "您好，您的本次问诊无需开方，如有其他健康用药咨询问题请再次发起问诊！";
    String PRESCRIPTION_CANCEL_TEXT_MSG = "您好，您的本次问诊医生无法为您开方，无法开方的原因：{0}。如有其他健康用药咨询问题请再次发起问诊!";
    String HIDE_PRESCRIPTION_TEXT_MSG = "医生给您开具了处方单，请联系药店工作人员后台查看";
    String HIDE_PRESCRIPTION_END_MSG = "本次问诊已结束";
    String ISSUE_PRESCRIPTION_TIME_OUT_END_MSG = "医生超时未处理，本次问诊自动取消";

    default List<InquiryImMessageDto> convertPrescriptionMessageList(InquiryPrescriptionRespDTO prescriptionRespDTO, InquiryRecordDto inquiryRecordDto, String doctorIm, String patientIm, InquiryDoctorCardInfoDto doctorCardInfoDto,
        InquiryHospitalRespDto hospitalRespDto, Boolean isHidePrescription, Integer dateType) {
        List<InquiryImMessageDto> result = new ArrayList<>();
        // 医生开具处方场景
        if (ObjectUtil.isNotEmpty(prescriptionRespDTO)) {
            result = buildMessageListForHasPre(prescriptionRespDTO, inquiryRecordDto, doctorIm, patientIm, isHidePrescription, dateType);
        } else {
            // 不开处方场景
            result = buildMessageListForCancelPre(inquiryRecordDto, doctorIm, patientIm, dateType);
        }
        // 插入问诊评价卡片消息
        result.add(convertPrescriptionEvaluationCardMsg(doctorIm, patientIm, inquiryRecordDto, doctorCardInfoDto, hospitalRespDto));
        return result;
    }

    /**
     * 构建消息列表 - 不开方场景
     *
     * @param inquiryRecordDto
     * @param doctorIm
     * @param patientIm
     */
    private List<InquiryImMessageDto> buildMessageListForCancelPre(InquiryRecordDto inquiryRecordDto, String doctorIm, String patientIm, Integer dateType) {
        List<InquiryImMessageDto> result = new ArrayList<>();
        // 医生取消开方场景
        if (ObjectUtil.equals(inquiryRecordDto.getInquiryStatus(), InquiryStatusEnum.DOCTOR_CANCELED.getStatusCode())) {
            result.add(convertPrescriptionTextMsg(doctorIm, patientIm, inquiryRecordDto, MessageFormat.format(PRESCRIPTION_CANCEL_TEXT_MSG, inquiryRecordDto.getCancelReason())));
            // 插入问诊结束系统通知气泡消息
            result.add(convertNotifyBubbleMsg(doctorIm, patientIm, inquiryRecordDto, dateType, HIDE_PRESCRIPTION_END_MSG));
            return result;
        }
        // 开方超时场景
        result.add(convertPrescriptionTextMsg(doctorIm, patientIm, inquiryRecordDto, PRESCRIPTION_TIME_OUT_TEXT_MSG));
        // 插入开方超时结束系统通知气泡消息
        result.add(convertNotifyBubbleMsg(doctorIm, patientIm, inquiryRecordDto, dateType, ISSUE_PRESCRIPTION_TIME_OUT_END_MSG));
        return result;
    }

    /**
     * 构建消息列表 - 开具处方场景
     *
     * @param prescriptionRespDTO
     * @param inquiryRecordDto
     * @param doctorIm
     * @param patientIm
     * @param isHidePrescription
     * @return
     */
    private List<InquiryImMessageDto> buildMessageListForHasPre(InquiryPrescriptionRespDTO prescriptionRespDTO, InquiryRecordDto inquiryRecordDto, String doctorIm, String patientIm, Boolean isHidePrescription, Integer dateType) {
        List<InquiryImMessageDto> result = new ArrayList<>();
        // 当前处方是否需要隐藏
        if (isHidePrescription) {
            // 插入系统通知气泡消息
            result.add(convertNotifyBubbleMsg(doctorIm, patientIm, inquiryRecordDto, dateType, HIDE_PRESCRIPTION_TEXT_MSG));
        } else {
            // 开具处方的消息
            result.add(convertPrescriptionTextMsg(doctorIm, patientIm, inquiryRecordDto, PRESCRIPTION_TEXT_MSG));
            // 插入处方卡片消息
            result.add(convertPrescriptionCardMsg(prescriptionRespDTO, doctorIm, patientIm, dateType));
        }
        // 插入问诊结束语
        result.add(convertPrescriptionTextMsg(doctorIm, patientIm, inquiryRecordDto, PRESCRIPTION_END_TEXT_MSG));
        // 插入问诊结束系统通知气泡消息
        result.add(convertNotifyBubbleMsg(doctorIm, patientIm, inquiryRecordDto, dateType, HIDE_PRESCRIPTION_END_MSG));
        return result;
    }

    /**
     * 处方评价卡片消息
     *
     * @param doctorIm
     * @param patientIm
     * @param inquiryRecordDto
     * @param doctorCardInfoDto
     * @param hospitalRespDto
     * @return
     */
    default InquiryImMessageDto convertPrescriptionEvaluationCardMsg(String doctorIm, String patientIm, InquiryRecordDto inquiryRecordDto, InquiryDoctorCardInfoDto doctorCardInfoDto, InquiryHospitalRespDto hospitalRespDto) {
        return InquiryImMessageDto.builder().fromAccount(doctorIm).inquiryPref(inquiryRecordDto.getPref()).toAccount(patientIm).msg("").extDto(
            ImEvaluationMessageExtDto.builder().inquiryPref(inquiryRecordDto.getPref()).sourceType(ImSourceTypeEnum.CardEvaForm.getCode()).evaluateInfo(
                    ImEvaluationMessageExtDto.EvaluateInfo.builder().name(doctorCardInfoDto.getName()).titleName(doctorCardInfoDto.getTitleName()).hospital(hospitalRespDto.getName()).office(inquiryRecordDto.getDeptName()).status(0).build())
                .build()).build();
    }

    /**
     * 处方文本消息
     *
     * @param doctorIm
     * @param patientIm
     * @param inquiryRecordDto
     * @return
     */
    default InquiryImMessageDto convertPrescriptionTextMsg(String doctorIm, String patientIm, InquiryRecordDto inquiryRecordDto, String message) {
        return InquiryImMessageDto.builder().fromAccount(doctorIm).inquiryPref(inquiryRecordDto.getPref()).toAccount(patientIm).msg(message).build();
    }

    /**
     * 处方卡片消息
     *
     * @param prescriptionRespDTO
     * @param doctorIm
     * @param patientIm
     * @return
     */
    default InquiryImMessageDto convertPrescriptionCardMsg(InquiryPrescriptionRespDTO prescriptionRespDTO, String doctorIm, String patientIm, Integer dateType) {
        return InquiryImMessageDto.builder().fromAccount(doctorIm).inquiryPref(prescriptionRespDTO.getInquiryPref()).toAccount(patientIm).msg("").extDto(
            ImPrecriptionMessageExtDto.builder().inquiryPref(prescriptionRespDTO.getInquiryPref()).sourceType(ImSourceTypeEnum.CardPre.getCode()).prescriptionInfo(
                ImPrecriptionMessageExtDto.PrescriptionInfo.builder().patientName(prescriptionRespDTO.getPatientName()).patientSex(prescriptionRespDTO.getPatientSex()).patientAge(prescriptionRespDTO.getPatientAge())
                    .doctorName(prescriptionRespDTO.getDoctorName()).deptName(prescriptionRespDTO.getDeptName())
                    .outPrescriptionTime(prescriptionRespDTO.getOutPrescriptionTime())
                    .outPrescriptionTimeStr(PrescriptionDateTypeEnum.formatDate(dateType, prescriptionRespDTO.getOutPrescriptionTime()))
                    .medicationType(prescriptionRespDTO.getMedicineType())
                    .pref(prescriptionRespDTO.getPref()).build()).build()).build();
    }


    /**
     * 通知气泡消息
     *
     * @param doctorIm
     * @param patientIm
     * @param inquiryRecordDto
     * @param message
     * @return
     */
    default InquiryImMessageDto convertNotifyBubbleMsg(String doctorIm, String patientIm, InquiryRecordDto inquiryRecordDto, Integer dateType, String message) {
        LocalDateTime now = LocalDateTime.now();
        return InquiryImMessageDto.builder().fromAccount(doctorIm).inquiryPref(inquiryRecordDto.getPref()).toAccount(patientIm).msg(message).extDto(
            ImNotifyBubbleMessageExtDto.builder().inquiryPref(inquiryRecordDto.getPref()).sourceType(ImSourceTypeEnum.BubbleNotify.getCode()).notifyInfo(
                ImNotifyBubbleMessageExtDto.NotifyInfo.builder()
                    .notifyTime(now).notifyTimeStr(PrescriptionDateTypeEnum.formatDate(dateType, now)).build()).build()).build();
    }


    default InquiryImMessageDto convertClinicalCaseMessage(InquiryClinicalCaseDO clinicalCaseDO, LocalDateTime startTime, String doctorIm, String patientIm, Integer dateType) {
        return InquiryImMessageDto.builder()
            .fromAccount(doctorIm)
            .inquiryPref(clinicalCaseDO.getInquiryPref())
            .toAccount(patientIm).msg("").extDto(
                ImPrecriptionMessageExtDto.builder()
                    .inquiryPref(clinicalCaseDO.getInquiryPref())
                    .sourceType(ImSourceTypeEnum.CardClinical.getCode())
                    .prescriptionInfo(
                        ImPrecriptionMessageExtDto.PrescriptionInfo.builder()
                            .patientName(clinicalCaseDO.getPatientName())
                            .patientSex(clinicalCaseDO.getPatientSex())
                            .patientAge(clinicalCaseDO.getPatientAge())
                            .doctorName(clinicalCaseDO.getDoctorName())
                            .deptName(clinicalCaseDO.getDeptName())
                            .startTime(startTime) // 医生接诊时间
                            .startTimeStr(PrescriptionDateTypeEnum.formatDate(dateType, startTime))
                            .pref(clinicalCaseDO.getPref()).build())
                    .build())
            .build();

    }
}
