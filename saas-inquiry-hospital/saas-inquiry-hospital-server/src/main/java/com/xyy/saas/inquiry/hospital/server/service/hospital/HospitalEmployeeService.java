package com.xyy.saas.inquiry.hospital.server.service.hospital;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalBindReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalBindRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalUserPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalUserSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalEmployeeRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalAvailableRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalAvailablePageReqVO;
import java.util.List;

/**
 * 医院员工服务接口
 *
 * <AUTHOR>
 */
public interface HospitalEmployeeService {

    /**
     * 创建医院员工
     *
     * @param createReqVO 创建信息
     * @return 员工编号
     */
    Long createHospitalEmployee(HospitalUserSaveReqVO createReqVO);

    /**
     * 分页查询医院员工信息
     *
     * @param pageReqVO 分页查询条件
     * @return 分页结果
     */
    PageResult<HospitalEmployeeRespVO> getHospitalEmployeePage(HospitalUserPageReqVO pageReqVO);


    /**
     * 分页查询用户绑定的医院列表
     *
     * @param pageReqVO 分页查询条件
     * @return 绑定的医院分页列表
     */
    PageResult<HospitalBindRespVO> getBindHospitalsByUserId(HospitalAvailablePageReqVO pageReqVO);


    /**
     * 绑定员工到医院
     *
     * @param bindReqVO 绑定信息
     * @return 关系记录ID
     */
    Long bindEmployeeToHospital(HospitalBindReqVO bindReqVO);



    /**
     * 解绑员工与医院关系
     *
     * @param bindId id
     */
    void unbindEmployeeFromHospital(Long bindId);

    /**
     * 分页查询当前用户可绑定的医院列表
     *
     * @param pageReqVO 分页查询条件
     * @return 可绑定的医院分页列表
     */
    PageResult<HospitalAvailableRespVO> getAvailableHospitalsByUserId(HospitalAvailablePageReqVO pageReqVO);

    /**
     * 获取用户已绑定的医院列表
     * @param userId 用户编号
     * @return
     */
    List<HospitalBindRespVO> getBindHospitalsByUserId(Long userId);
}