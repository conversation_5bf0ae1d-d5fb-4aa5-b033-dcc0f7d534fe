package com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDeptDoctorGroupVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDepartmentRelationDO;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 医院科室信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryHospitalDepartmentRelationMapper extends BaseMapperX<InquiryHospitalDepartmentRelationDO> {

    default PageResult<InquiryHospitalDepartmentRelationDO> selectPage(InquiryHospitalDepartmentRelationPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InquiryHospitalDepartmentRelationDO>()
            .eqIfPresent(InquiryHospitalDepartmentRelationDO::getDeptId, reqVO.getDeptId())
            .eqIfPresent(InquiryHospitalDepartmentRelationDO::getHospitalId, reqVO.getHospitalId())
            .eqIfPresent(InquiryHospitalDepartmentRelationDO::getHospitalPref, reqVO.getHospitalPref())
            .eqIfPresent(InquiryHospitalDepartmentRelationDO::getDeptPref, reqVO.getDeptPref())
            .likeIfPresent(InquiryHospitalDepartmentRelationDO::getDeptName, reqVO.getDeptName())
            .eqIfPresent(InquiryHospitalDepartmentRelationDO::getDeptParentId, reqVO.getDeptParentId())
            .betweenIfPresent(InquiryHospitalDepartmentRelationDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(InquiryHospitalDepartmentRelationDO::getId));
    }


    Boolean deleteByHospitalId(Long hospitalId);

    IPage<InquiryHospitalDeptDoctorGroupVO> selectHospitalDeptPage(Page<InquiryHospitalDepartmentRelationPageReqVO> objectPage, InquiryHospitalDepartmentRelationPageReqVO pageReqVO);

    default List<InquiryHospitalDepartmentRelationDO> selectHospitalDeptmentList(InquiryHospitalDepartmentRelationPageReqVO queryVO) {
        return selectList(new LambdaQueryWrapperX<InquiryHospitalDepartmentRelationDO>()
            .eqIfPresent(InquiryHospitalDepartmentRelationDO::getHospitalPref, queryVO.getHospitalPref())
            .inIfPresent(InquiryHospitalDepartmentRelationDO::getHospitalPref, queryVO.getHospitalPrefs())
            .eq(InquiryHospitalDepartmentRelationDO::getDisabled, 0)
        );
    }
}