package com.xyy.saas.inquiry.hospital.server.api.prescription;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.INQUIRY_REMOTE_AUDIT_CANCEL_FAIL;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import com.xyy.saas.inquiry.constant.PrescriptionConstant;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionStatusEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.RemoteAuditPrescriptionApi;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.RemotePrescriptionDTO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionPageReqVO;
import com.xyy.saas.inquiry.hospital.server.convert.prescription.InquiryPrescriptionConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDO;
import com.xyy.saas.inquiry.hospital.server.service.prescription.InquiryPrescriptionService;
import com.xyy.saas.inquiry.hospital.server.service.prescription.RemoteAuditPrescriptionService;
import com.xyy.saas.inquiry.pharmacist.api.audit.InquiryPrescriptionAuditApi;
import com.xyy.saas.inquiry.signature.api.signature.InquirySignatureImageApi;
import com.xyy.saas.inquiry.signature.api.signature.dto.InquirySignatureImageDto;
import jakarta.annotation.Resource;
import java.util.List;
import org.apache.dubbo.config.annotation.DubboReference;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * @Author: xucao
 * @DateTime: 2025/4/22 16:03
 * @Description: 远程问诊接口实现
 **/
@DubboService
public class RemoteAuditPrescriptionApiImpl implements RemoteAuditPrescriptionApi {

    @Resource
    private RemoteAuditPrescriptionService remoteAuditPrescriptionService;

    @DubboReference
    private InquiryPrescriptionAuditApi inquiryPrescriptionAuditApi;

    @DubboReference
    private InquirySignatureImageApi inquirySignatureImageApi;

    @Resource
    private InquiryPrescriptionService inquiryPrescriptionService;

    @DubboReference
    private ConfigApi configApi;

    /**
     * 保存远程审方处方
     *
     * @param prescriptionUpdateDTO 远程审方 处方信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void saveRemotePrescription(RemotePrescriptionDTO prescriptionUpdateDTO) {
        // 为远程审方的处方图片添加【远程审方】水印
        String url = inquirySignatureImageApi.signatureImageMerge(
            InquirySignatureImageDto.builder()
                .sourceUrl(prescriptionUpdateDTO.extGet().getRemotePrescriptionImg())
                .mergeUrl(configApi.getConfigValueByKey(PrescriptionConstant.REMOTE_PRESCRIPTION_SIGNATURE_URL))
                .offset(NumberUtil.parseDouble(configApi.getConfigValueByKey(PrescriptionConstant.REMOTE_PRESCRIPTION_SIGNATURE_OFFSET), -0.5))
                .scaleRatio(10.0) // 水印缩放为原图宽高的10%
                .build());
        prescriptionUpdateDTO.setPrescriptionImgUrl(url);
        // 保存远程审方处方
        InquiryPrescriptionDO inquiryPrescriptionDO = InquiryPrescriptionConvert.INSTANCE.convertRemotePrescriptionDTO2DO(prescriptionUpdateDTO);
        remoteAuditPrescriptionService.saveRemotePrescription(inquiryPrescriptionDO);
        // 推送远程审方处方
        inquiryPrescriptionAuditApi.remotePrescriptionPushAuditPool(InquiryPrescriptionConvert.INSTANCE.convertDTO2AuditDto(prescriptionUpdateDTO, inquiryPrescriptionDO));
    }

    /**
     * 取消远程审方处方
     *
     * @param inquiryPref 问诊单号
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelRemotePrescription(String inquiryPref) {
        // 查询远程审方处方
        InquiryPrescriptionDO prescriptionDO = remoteAuditPrescriptionService.selectByInquiryPref(inquiryPref);
        // 非待审核状态无法取消
        if (ObjectUtil.notEqual(prescriptionDO.getStatus(), PrescriptionStatusEnum.WAIT_APPROVAL.getStatusCode())) {
            throw exception(INQUIRY_REMOTE_AUDIT_CANCEL_FAIL);
        }
        // 调药师服务取消问诊
        inquiryPrescriptionAuditApi.remotePrescriptionRemoveAuditPool(InquiryPrescriptionConvert.INSTANCE.convert2PrescriptionAuditDTO(prescriptionDO));
        // 删除远程审方处方
        remoteAuditPrescriptionService.delRemotePrescription(prescriptionDO);
    }

    /**
     * 批量取消远程审方处方
     *
     * @param inquiryPrefList 问诊单列表
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchCancelRemotePrescription(List<String> inquiryPrefList) {
        List<InquiryPrescriptionDO> prescriptions = inquiryPrescriptionService.queryListByCondition(InquiryPrescriptionPageReqVO.builder().inquiryPrefList(inquiryPrefList).build())
            .stream().filter(item -> ObjectUtil.equals(item.getStatus(), PrescriptionStatusEnum.WAIT_APPROVAL.getStatusCode()) && ObjectUtil.equals(item.getInquiryBizType(), InquiryBizTypeEnum.REMOTE_INQUIRY.getCode())).toList();
        if (CollectionUtils.isEmpty(prescriptions)) {
            return;
        }
        // 调药师服务批量取消远程审方
        inquiryPrescriptionAuditApi.remotePrescriptionBatchRemoveAuditPool(InquiryPrescriptionConvert.INSTANCE.convert2BatchAuditDTO(prescriptions));

        // 批量删除远程审方处方
        remoteAuditPrescriptionService.batchDelRemotePrescription(prescriptions);
    }
}
