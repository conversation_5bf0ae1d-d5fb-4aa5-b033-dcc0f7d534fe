package com.xyy.saas.inquiry.hospital.server.mq.message.doctor;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import com.xyy.saas.inquiry.hospital.server.mq.message.doctor.dto.DoctorDistributeMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Author: xucao
 * @Date: 2024/12/26 9:54
 * @Description: 医生调度事件
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class DoctorDistributeEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "DOCTOR_DISTRIBUTE";

    private DoctorDistributeMessage msg;

    @JsonCreator
    public DoctorDistributeEvent(@JsonProperty("msg") DoctorDistributeMessage msg) {
        this.msg = msg;
    }

    /**
     * 消息tag
     */
    @Override
    public String getTag() {
        return "";
    }
}
