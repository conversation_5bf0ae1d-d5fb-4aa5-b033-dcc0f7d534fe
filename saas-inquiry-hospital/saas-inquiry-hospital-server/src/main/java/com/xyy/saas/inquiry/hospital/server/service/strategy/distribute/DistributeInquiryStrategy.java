package com.xyy.saas.inquiry.hospital.server.service.strategy.distribute;

import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import java.util.List;

/**
 * @Author: xucao
 * @Date: 2024/12/25 15:21
 * @Description: 问诊派单策略
 */
public interface DistributeInquiryStrategy {


    /**
     * 问诊派单
     * @param inquiryDto 问诊单信息
     * @param doctorList 医生列表
     */
    void distributeInquiry(InquiryRecordDto inquiryDto , List<String> doctorList);

    AutoInquiryEnum getInquiryType();
}
