package com.xyy.saas.inquiry.hospital.server.convert.hospital;

import com.xyy.saas.inquiry.hospital.api.dept.dto.InquiryDeptDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDepartmentDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @ClassName：InquiryHospitalDepartmentConvert
 * @Author: xucao
 * @Date: 2024/11/04 11:21
 * @Description: 医院科室转换类
 */
@Mapper
public interface InquiryHospitalDepartmentConvert {

    InquiryHospitalDepartmentConvert INSTANCE = Mappers.getMapper(InquiryHospitalDepartmentConvert.class);

    @Mapping(target = "pref", expression = "java(com.xyy.saas.inquiry.util.PrefUtil.getHospitalDeptPref())")
    InquiryHospitalDepartmentDO initConvetVO2DO(InquiryHospitalDepartmentSaveReqVO saveReqVO);

    InquiryDeptDto convetDO2DTO(InquiryHospitalDepartmentDO dept);
}
