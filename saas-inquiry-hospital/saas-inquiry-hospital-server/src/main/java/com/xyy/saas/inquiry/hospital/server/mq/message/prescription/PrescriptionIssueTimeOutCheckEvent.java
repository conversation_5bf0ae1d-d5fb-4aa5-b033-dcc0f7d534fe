package com.xyy.saas.inquiry.hospital.server.mq.message.prescription;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import com.xyy.saas.inquiry.hospital.server.mq.message.prescription.dto.PrescriptionIssueTimeOutCheckMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 处方开方超时检查MQ
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class PrescriptionIssueTimeOutCheckEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "PRESCRIPTION_ISSUE_TIME_OUT_CHECK";

    private String msg;

    private PrescriptionIssueTimeOutCheckMessage notifyConfigMessage;

    @JsonCreator
    public PrescriptionIssueTimeOutCheckEvent(@JsonProperty("msg") String msg , @JsonProperty("notifyConfigMessage") PrescriptionIssueTimeOutCheckMessage notifyConfigMessage) {
        this.msg = msg;
        this.notifyConfigMessage = notifyConfigMessage;
    }


    @Override
    public String getTag() {
        return "";
    }

}
