package com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 处方图片扩展 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_prescription_url")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class InquiryPrescriptionUrlDO extends BaseDO {

    /**
     * 主键
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;
    /**
     * 处方编号
     */
    private String pref;
    /**
     * 处方日期格式类型 1年月日-时分秒 2年月日
     */
    private Integer dateType;
    /**
     * 处方笺图片url
     */
    private String prescriptionImgUrl;
    /**
     * 处方笺PDFurl
     */
    private String prescriptionPdfUrl;

}