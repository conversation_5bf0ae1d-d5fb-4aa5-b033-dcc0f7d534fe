package com.xyy.saas.inquiry.hospital.server.convert.medical;

import com.xyy.saas.inquiry.hospital.server.controller.admin.medical.vo.MedicalRegistrationRespVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.medical.MedicalRegistrationDO;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.hospital.api.medicare.dto.MedicalRegistrationDto;
import com.xyy.saas.inquiry.hospital.api.medicare.dto.MedicalRegistrationInquiryUpdateDto;
import com.xyy.saas.inquiry.hospital.api.medicare.transmission.MedicalRegistrationTransmissionRespDto;
import com.xyy.saas.inquiry.pojo.transmitter.his.RegistrationTransmitterDTO;
import java.util.List;
import java.util.Optional;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;

/**
 * @Author: cxy
 * @Date: 2024/10/25 16:02
 * @Description: 就诊登记
 */
@Mapper
public interface RegistrationConvert {

    RegistrationConvert INSTANCE = Mappers.getMapper(RegistrationConvert.class);


    default MedicalRegistrationDO convertInquirySaveDo(InquiryRecordDto saveDto, MedicalRegistrationTransmissionRespDto transmissionDto) {
        MedicalRegistrationDO medicalRegistrationDO = convertTransmission(transmissionDto);
        convertFillDo(saveDto, medicalRegistrationDO);
        return medicalRegistrationDO;
    }

    @Mapping(target = "pref", ignore = true)
    @Mapping(target = "bizId", source = "pref")
    @Mapping(target = "bizType", expression = "java(com.xyy.saas.inquiry.enums.system.BizTypeEnum.HYWZ.getCode())")
    @Mapping(target = "hospitalPref", expression = "java(saveDto.getChoiceHospitalList().getFirst())")
    void convertFillDo(InquiryRecordDto saveDto, @MappingTarget MedicalRegistrationDO medicalRegistrationDO);

    MedicalRegistrationDO convertTransmission(MedicalRegistrationTransmissionRespDto transmissionDto);


    MedicalRegistrationRespVO convert(MedicalRegistrationDO registrationDO);

    List<MedicalRegistrationRespVO> convertList(List<MedicalRegistrationDO> registrationDOList);

    MedicalRegistrationDto convertDto(MedicalRegistrationRespVO registrationInfo);

    @Mapping(target = "idCardNo", source = "patientIdCard")
    @Mapping(target = "idCard", source = "patientIdCard")
    @Mapping(target = "fullName", source = "patientName")
    @Mapping(target = "userId", expression = "java(cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId())")
    @Mapping(target = "businessNo", source = "pref")
    @Mapping(target = "inquiryPref", source = "pref")
    @Mapping(target = "hospitalPref", expression = "java(saveDto.getChoiceHospitalList().getFirst())")
    RegistrationTransmitterDTO convertTransmission(InquiryRecordDto saveDto);

    default RegistrationTransmitterDTO convertTransmission(MedicalRegistrationInquiryUpdateDto updateDto, MedicalRegistrationDO registrationDO) {
        RegistrationTransmitterDTO transmissionReqDataBaseDTO = new RegistrationTransmitterDTO()
            .setMedicalVisitId(registrationDO.getMedicalVisitId())
            .setStatus(updateDto.getStatus());

        transmissionReqDataBaseDTO.setIdCard(registrationDO.getPatientIdCard())
            .setFullName(registrationDO.getPatientName())
            .setBusinessNo(updateDto.getInquiryPref());
        return transmissionReqDataBaseDTO;
    }

    default RegistrationTransmitterDTO convertTras(MedicalRegistrationDO registrationDO) {
        RegistrationTransmitterDTO registrationTransmitterDTO = new RegistrationTransmitterDTO();
        registrationTransmitterDTO.setMedicalVisitId(registrationDO.getMedicalVisitId());
        registrationTransmitterDTO.setPsnNo(registrationDO.getPsnNo());
        registrationTransmitterDTO.setInquiryPref(registrationDO.getBizId());
        registrationTransmitterDTO.setInsuredAreaNo(registrationDO.getInsuredAreaNo());
        return registrationTransmitterDTO;
    }
}
