package com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

/**
 * 医院员工关系 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_hospital_employee")
@KeySequence("saas_inquiry_hospital_employee_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class HospitalEmployeeDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;

    /**
     * 医院ID
     */
    private String hospitalPref;

    /**
     * 医院名称
     */
    private String hospitalName;

    /**
     * 用户ID（关联system_users表）
     */
    private Long userId;

    /**
     * 帐号状态（0绑定 1解绑）
     */
    private Integer bindStatus;

}