package com.xyy.saas.inquiry.hospital.server.mq.consumer.reception;

import cn.hutool.core.util.ObjectUtil;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.hospital.server.service.reception.HospitalReceptionAreaService;
import com.xyy.saas.inquiry.mq.inquiry.InquiryRecordCreateEvent;
import com.xyy.saas.inquiry.mq.inquiry.dto.InquiryCreateMessage;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * @Desc 接诊大厅消费创建的问诊单
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/9/5 下午2:34
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_hospital_server_mq_consumer_reception_RecetionAreaConsumer",
    topic = InquiryRecordCreateEvent.TOPIC)
public class RecetionAreaConsumer {

    @Resource
    private HospitalReceptionAreaService hospitalReceptionAreaService;

    @DubboReference
    private InquiryApi inquiryApi;

    /**
     * 接诊大厅消费创建的问诊单
     *
     * @param event 接诊大厅入列事件
     */
    @EventBusListener
    @TraceNode(node = TraceNodeEnum.RECEPTION_AREA_DISTRIBUTE , prefLocation = "event.msg.inquiryPref")
    public void receiveInquiryRecord(InquiryRecordCreateEvent event) {
        InquiryCreateMessage msg = event.getMsg();
        log.info("接诊大厅收到新的问诊，单号：{}", msg.getInquiryPref());
        // 查询问诊单信息
        InquiryRecordDto inquiryRecordDto = inquiryApi.getInquiryDtoByPref(msg.getInquiryPref());
        if(ObjectUtil.isEmpty(inquiryRecordDto)){
            log.warn("接诊大厅收到新的问诊，单号：{}，但是查询问诊单不存在,不再执行调度派单逻辑", msg.getInquiryPref());
            return;
        }
        // 设置可接诊医院、科室信息
        inquiryRecordDto.setHospitalDeptDto(msg.getHospitalDeptDto());
        hospitalReceptionAreaService.distributeDoctorForInquiry(inquiryRecordDto);
    }

}
