package com.xyy.saas.inquiry.hospital.server.service.doctor;

import com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo.DoctorReviewsSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorReviewsDO;
import jakarta.validation.Valid;

/**
 * 医生问诊评价 Service 接口
 *
 * <AUTHOR>
 */
public interface DoctorReviewsService {

    /**
     * 创建医生问诊评价
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Boolean createDoctorReviews(@Valid DoctorReviewsSaveReqVO createReqVO);


    /**
     * 获得医生问诊评价
     *
     * @param inquiryPref 编号
     * @return 医生问诊评价
     */
    DoctorReviewsDO getDoctorReviewsByInquiryPref(String inquiryPref);


}