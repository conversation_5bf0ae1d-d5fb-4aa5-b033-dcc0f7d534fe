package com.xyy.saas.inquiry.hospital.server.convert.medical;

import com.xyy.saas.inquiry.enums.medicare.InstitutionTypeEnum;
import com.xyy.saas.inquiry.enums.medicare.SigninStatusEnum;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.hospital.api.medicare.dto.MedicareRegistrationDto;
import com.xyy.saas.inquiry.hospital.api.medicare.dto.MedicareSignDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.transmission.PrescriptionExternalTransmissionRespDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.medical.MedicareSigninRecordDO;
import com.xyy.saas.inquiry.pojo.medicare.MedicareSignInDto;
import com.xyy.saas.inquiry.pojo.transmitter.his.RegistrationTransmitterDTO;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionTransmitterDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import java.time.LocalDateTime;
import java.util.HashMap;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 医保相关转换器
 *
 * <AUTHOR>
 */
@Mapper
public interface MedicareConvert {

    MedicareConvert INSTANCE = Mappers.getMapper(MedicareConvert.class);

    /**
     * 构建签到传输数据
     *
     * @param prescriptionRespVO 处方信息
     * @return 传输数据
     */
    default RegistrationTransmitterDTO buildSigninTransmitterData(InquiryPrescriptionRespVO prescriptionRespVO) {
        return new RegistrationTransmitterDTO().setHospitalPref(prescriptionRespVO.getHospitalPref());
    }

    /**
     * 创建医保签到记录
     *
     * @param prescriptionRespVO 处方信息
     * @return 签到记录DO
     */
    default MedicareSigninRecordDO buildMedicareSigninRecord(InquiryPrescriptionRespVO prescriptionRespVO,
        MedicareSignInDto dto) {
        return MedicareSigninRecordDO.builder()
            .tenantId(prescriptionRespVO.getTenantId())
            .institutionType(InstitutionTypeEnum.HOSPITAL.getCode())
            .institutionCode(dto.getMedicareInstitutionCode())
            .medicareInstitutionCode(prescriptionRespVO.getHospitalPref())
            .operatorName(dto.getOperatorName())
            .operatorCode(dto.getOperatorCode())
            .signNo(dto.getSignNo())
            .signinStatus(SigninStatusEnum.SIGNIN.getCode())
            .signinTime(LocalDateTime.now())
            .build();
    }

    /**
     * 医保签到记录DO转换为ExtDto
     *
     * @param signinRecord 签到记录DO
     * @return 签到扩展DTO
     */
    default MedicareSignInDto convertSigninRecord2ExtDto(MedicareSigninRecordDO signinRecord) {
        if (signinRecord == null) {
            return null;
        }
        return MedicareSignInDto.builder()
            .signNo(signinRecord.getSignNo() != null ? signinRecord.getSignNo() : "")
            .medicareInstitutionCode(signinRecord.getMedicareInstitutionCode() != null ? signinRecord.getMedicareInstitutionCode() : "")
            .operatorCode(signinRecord.getOperatorCode() != null ? signinRecord.getOperatorCode() : "")
            .operatorName(signinRecord.getOperatorName() != null ? signinRecord.getOperatorName() : "")
            .signinStatus(signinRecord.getSigninStatus() != null ? signinRecord.getSigninStatus() : 0)
            .signinTime(signinRecord.getSigninTime())
            .build();
    }

    /**
     * 从响应数据中提取签到号
     *
     * @param signinResponse 签到响应
     * @return 签到号
     */
    default String extractSignNoFromResponse(PrescriptionExternalTransmissionRespDto signinResponse) {
        try {
            // 根据实际DSL响应数据结构提取签到号
            // 这里需要根据9001接口的实际响应来调整
            if (signinResponse != null && signinResponse.getMedicareSigninExt() != null) {
                return signinResponse.getMedicareSigninExt().getSignNo();
            }
            return "SIGN" + System.currentTimeMillis();
        } catch (Exception e) {
            return "SIGN" + System.currentTimeMillis();
        }
    }

    /**
     * 构建医保挂号登记DTO
     *
     * @param prescriptionRespVO 处方信息
     * @param transmitterDTO     传输数据
     * @param responseData       响应数据
     * @return 医保挂号登记DTO
     */
    default MedicareRegistrationDto buildMedicareRegistrationDto(InquiryPrescriptionRespVO prescriptionRespVO,
        PrescriptionTransmitterDTO transmitterDTO,
        MedicareRegistrationDto medicareRegistrationDto) {
        return MedicareRegistrationDto.builder()
            .tenantId(prescriptionRespVO.getTenantId())
            .bizId(prescriptionRespVO.getInquiryPref())
            .bizType(BizTypeEnum.HYWZ.getCode())
            .patientPref(prescriptionRespVO.getPatientPref())
            .patientName(transmitterDTO.getFullName())
            .patientMobile(transmitterDTO.getPatientMobile())
            .patientIdCard(transmitterDTO.getIdCard())
            .bizVisitId(prescriptionRespVO.getPref())
            .medicalVisitDate(LocalDateTime.now())
            .medicalVisitId(medicareRegistrationDto.getMedicalVisitId())
            .medType(medicareRegistrationDto.getMedType())
            .insuredAreaNo(medicareRegistrationDto.getInsuredAreaNo())
            .tenantAreaNo(medicareRegistrationDto.getTenantAreaNo())
            .psnNo(medicareRegistrationDto.getPsnNo())
            .iptOtpNo(medicareRegistrationDto.getIptOtpNo())
            .status(1)
            .deptPref(prescriptionRespVO.getDeptPref())
            .deptName(prescriptionRespVO.getDeptName())
            .hospitalPref(prescriptionRespVO.getHospitalPref())
            .hospitalName(prescriptionRespVO.getHospitalName())
            .acctUsedFlag(1)
            .bookTime(LocalDateTime.now())
            .planTime(LocalDateTime.now())
            .build();
    }

    /**
     * 从响应数据中提取医保就诊ID
     *
     * @param responseData 响应数据
     * @return 医保就诊ID
     */
    default String extractMedicalVisitId(PrescriptionExternalTransmissionRespDto responseData) {
        try {

            return "";
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 从响应数据中提取参保地编号
     *
     * @param responseData 响应数据
     * @return 参保地编号
     */
    default String extractInsuredAreaNo(PrescriptionExternalTransmissionRespDto responseData) {
        try {

            return "";
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 从响应数据中提取就医地编号
     *
     * @param responseData 响应数据
     * @return 就医地编号
     */
    default String extractTenantAreaNo(PrescriptionExternalTransmissionRespDto responseData) {
        try {
            if (responseData != null && responseData.getMedicareSigninExt() != null) {
                return responseData.getMedicareSigninExt().getTenantAreaNo();
            }
            return "";
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 从响应数据中提取参保人员编号
     *
     * @param responseData 响应数据
     * @return 参保人员编号
     */
    default String extractPsnNo(PrescriptionExternalTransmissionRespDto responseData) {
        try {

            return "";
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 从响应数据中提取住院/门诊号
     *
     * @param responseData 响应数据
     * @return 住院/门诊号
     */
    default String extractIptOtpNo(PrescriptionExternalTransmissionRespDto responseData) {
        try {

            return "";
        } catch (Exception e) {
            return "";
        }
    }

    /**
     * 将签到信息设置到传输请求的aux字段中
     *
     * @param transmissionReqDTO 传输请求对象
     * @param signinRecord       签到记录
     */
    default void setSigninInfoToAux(TransmissionReqDTO transmissionReqDTO, MedicareSigninRecordDO signinRecord) {
        try {
            if (transmissionReqDTO != null && signinRecord != null) {
                // 将签到记录转换为扩展DTO
                MedicareSignInDto signinExtDto = convertSigninRecord2ExtDto(signinRecord);

                // 将签到信息添加到aux字段
                if (transmissionReqDTO.getAux() == null) {
                    transmissionReqDTO.setAux(new HashMap<>());
                }
                transmissionReqDTO.getAux().put("medicareSigninInfo", signinExtDto);
            }
        } catch (Exception e) {

        }
    }

    MedicareSignDTO getMedicareSignInfo(MedicareSigninRecordDO medicareSigninRecordDO);

} 