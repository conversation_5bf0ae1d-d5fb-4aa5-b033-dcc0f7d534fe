package com.xyy.saas.inquiry.hospital.server.mq.consumer.prescription;


import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.service.prescription.InquiryPrescriptionService;
import com.xyy.saas.inquiry.hospital.server.service.prescription.SynInquiryPrescriptionToSaaS;
import com.xyy.saas.inquiry.mq.prescription.DoctorSignaturePostPassingEvent;
import com.xyy.saas.inquiry.mq.prescription.PrescriptionCompletedEvent;
import com.xyy.saas.inquiry.mq.prescription.dto.PrescriptionMqCommonMessage;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 处方完结回传SAAS消费者
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_hospital_server_mq_consumer_prescription_PrescriptionCompletedToSaasConsumer",
    topic = PrescriptionCompletedEvent.TOPIC)
public class PrescriptionCompletedToSaasConsumer {
    public static final String GROUP_ID = PrescriptionCompletedToSaasConsumer.class.getName();


    @Resource
    private SynInquiryPrescriptionToSaaS synInquiryPrescriptionToSaaS;

    @Resource
    private InquiryPrescriptionService inquiryPrescriptionService;


    @EventBusListener
    public void prescriptionCompletedToSaasConsumer(PrescriptionCompletedEvent event) {
        log.info("处方完结回传Saas消费者,event: {}", JsonUtils.toJsonString(event));
        // 查询处方信息
        InquiryPrescriptionRespVO prescriptionRespVO = inquiryPrescriptionService.queryByCondition(
            InquiryPrescriptionQueryDTO.builder().pref(event.getMsg()).build()
        );
        if(prescriptionRespVO != null) {
            synInquiryPrescriptionToSaaS.toSaas(PrescriptionMqCommonMessage.builder()
                .prescriptionPref(prescriptionRespVO.getPref())
                .prescriptionImgUrl(prescriptionRespVO.getPrescriptionImgUrl())
                .prescriptionPdfUrl(prescriptionRespVO.getPrescriptionPdfUrl()).build());
        }
    }
}
