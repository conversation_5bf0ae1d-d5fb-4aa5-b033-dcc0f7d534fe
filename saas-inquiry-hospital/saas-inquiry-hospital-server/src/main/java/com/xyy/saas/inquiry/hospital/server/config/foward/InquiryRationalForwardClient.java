package com.xyy.saas.inquiry.hospital.server.config.foward;

import com.xyy.saas.inquiry.hospital.server.controller.app.rational.vo.InquiryRationalDictConfigVo;
import com.xyy.saas.inquiry.hospital.server.controller.app.rational.vo.RationalTipsVO;
import com.xyy.saas.inquiry.hospital.server.service.rational.dto.InquiryLimitAuditForwardDto;
import com.xyy.saas.inquiry.hospital.server.service.rational.dto.InquiryRationalDictConfigPageDto;
import com.xyy.saas.inquiry.pojo.forward.ForwardPageResultDto;
import com.xyy.saas.inquiry.pojo.ForwardResult;
import java.util.List;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

/**
 * @Author:chenxiaoyi
 * @Date:2024/11/27 20:41
 */


@HttpExchange(accept = "application/json", contentType = "application/json")
public interface InquiryRationalForwardClient {

    /**
     * 转发获取默认过敏成分列表
     *
     * @return ForwardResult
     */
    @PostExchange("/rational/forward/irritability/getDefaultAllergy")
    ForwardResult<List<InquiryRationalDictConfigVo>> getDefaultAllergy();

    /**
     * 转发获取常用推荐过敏
     *
     * @return ForwardResult
     */
    @PostExchange("/rational/forward/irritability/getRecommendAllergy")
    ForwardResult<List<InquiryRationalDictConfigVo>> getRecommendAllergy();

    /**
     * 转发获取成分过敏列表
     *
     * @return ForwardResult
     */
    @PostExchange("/rational/forward/irritability/queryList")
    ForwardResult<ForwardPageResultDto<InquiryRationalDictConfigVo>> queryAllergyList(@RequestBody InquiryRationalDictConfigPageDto dto);

    /**
     * 转发合理用药接口
     *
     * @return ForwardResult
     */
    @PostExchange("/rational/forward/limitDrugAudit")
    ForwardResult<List<RationalTipsVO>> limitDrugAudit(@RequestBody InquiryLimitAuditForwardDto limitAuditForwardDto);


}
