package com.xyy.saas.inquiry.hospital.server.convert.hospital;

import com.xyy.saas.inquiry.hospital.server.controller.admin.diagnosis.vo.InquiryDiagnosisSimpleVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryDiagnosisDepartmentRelationSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.diagnosis.InquiryDiagnosisDepartmentRelationDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDepartmentDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
 * @Author: cxy
 * @Date: 2024/11/04 11:21
 * @Description: 医院科室转换类
 */
@Mapper
public interface InquiryDiagnosisDepartmentConvert {

    InquiryDiagnosisDepartmentConvert INSTANCE = Mappers.getMapper(InquiryDiagnosisDepartmentConvert.class);


    default List<InquiryDiagnosisDepartmentRelationDO> convertSaveVOs(InquiryDiagnosisDepartmentRelationSaveReqVO createReqVO, InquiryHospitalDepartmentDO departmentDO) {
        return createReqVO.getDiagnosisVOList().stream().map(d -> InquiryDiagnosisDepartmentRelationDO.builder()
            .deptId(departmentDO.getId())
            .deptName(departmentDO.getDeptName())
            .deptPref(departmentDO.getPref())
            .diagnosisCode(d.getDiagnosisCode())
            .diagnosisName(d.getDiagnosisName())
            .build()).toList();
    }

    List<InquiryDiagnosisSimpleVO> convertVO(List<InquiryDiagnosisDepartmentRelationDO> relationDOS);
}
