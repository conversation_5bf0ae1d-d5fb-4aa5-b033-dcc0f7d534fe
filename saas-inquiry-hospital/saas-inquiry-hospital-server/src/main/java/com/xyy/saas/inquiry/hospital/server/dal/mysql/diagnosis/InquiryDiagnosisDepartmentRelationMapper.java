package com.xyy.saas.inquiry.hospital.server.dal.mysql.diagnosis;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.hospital.api.diagnosis.dto.InquiryDiagnosisDepartmentRelationReqDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryDiagnosisDepartmentRelationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.diagnosis.InquiryDiagnosisDepartmentRelationDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 科室诊断关联 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryDiagnosisDepartmentRelationMapper extends BaseMapperX<InquiryDiagnosisDepartmentRelationDO> {

    default PageResult<InquiryDiagnosisDepartmentRelationDO> selectPage(
        InquiryDiagnosisDepartmentRelationPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InquiryDiagnosisDepartmentRelationDO>()
            .eqIfPresent(InquiryDiagnosisDepartmentRelationDO::getDiagnosisCode, reqVO.getDiagnosisCode())
            .likeIfPresent(InquiryDiagnosisDepartmentRelationDO::getDiagnosisName, reqVO.getDiagnosisName())
            .eqIfPresent(InquiryDiagnosisDepartmentRelationDO::getDeptId, reqVO.getDeptId())
            .eqIfPresent(InquiryDiagnosisDepartmentRelationDO::getDeptPref, reqVO.getDeptPref())
            .likeIfPresent(InquiryDiagnosisDepartmentRelationDO::getDeptName, reqVO.getDeptName())
            .betweenIfPresent(InquiryDiagnosisDepartmentRelationDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(InquiryDiagnosisDepartmentRelationDO::getId));
    }

    default List<InquiryDiagnosisDepartmentRelationDO> queryDiagnosisDepartmentRelation(InquiryDiagnosisDepartmentRelationReqDto req) {
        return selectList(new LambdaQueryWrapperX<InquiryDiagnosisDepartmentRelationDO>()
            .inIfPresent(InquiryDiagnosisDepartmentRelationDO::getDiagnosisCode, req.getDiagnosisCodes())
            .eqIfPresent(InquiryDiagnosisDepartmentRelationDO::getDiagnosisCode,req.getDiagnosisCode())
            .inIfPresent(InquiryDiagnosisDepartmentRelationDO::getDeptId, req.getDeptIds())
            .eqIfPresent(InquiryDiagnosisDepartmentRelationDO::getDeptId, req.getDeptId())
            .inIfPresent(InquiryDiagnosisDepartmentRelationDO::getDeptPref, req.getDeptPrefs())
            .orderByDesc(InquiryDiagnosisDepartmentRelationDO::getId));
    }

    int insertOrUpdateBatch(List<InquiryDiagnosisDepartmentRelationDO> list);

}