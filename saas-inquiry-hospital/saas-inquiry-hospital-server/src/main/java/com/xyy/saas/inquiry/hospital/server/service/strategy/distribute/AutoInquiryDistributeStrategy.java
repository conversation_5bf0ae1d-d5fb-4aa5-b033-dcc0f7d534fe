package com.xyy.saas.inquiry.hospital.server.service.strategy.distribute;

import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.hospital.server.mq.message.doctor.DoctorDistributeEvent;
import com.xyy.saas.inquiry.hospital.server.mq.message.doctor.dto.DoctorDistributeMessage;
import com.xyy.saas.inquiry.hospital.server.mq.producer.doctor.DoctorDistributeProducer;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.util.MathUtil;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/12/25 19:01
 * @Description: 自动开方调度派单策略
 */
@Component
@Slf4j
public class AutoInquiryDistributeStrategy implements DistributeInquiryStrategy{

    @Resource
    private DoctorDistributeProducer doctorDistributeProducer;

    /**
     * 问诊派单
     *
     * @param inquiryDto 问诊单信息
     * @param doctorList 医生列表
     */
    @Override
    @TraceNode(node = TraceNodeEnum.DOCTOR_DISTRIBUTE_AUTO_INQUIRY , prefLocation = "inquiryDto.pref")
    public void distributeInquiry(InquiryRecordDto inquiryDto, List<String> doctorList) {
        log.info("问诊单:{}，接诊大厅自动派单开始,医生：{}", inquiryDto.getPref(), JSON.toJSONString(doctorList));
        //医生调度mq
        doctorDistributeProducer.sendMessage(DoctorDistributeEvent.builder().msg(DoctorDistributeMessage.builder().inquiryPref(inquiryDto.getPref()).doctorList(doctorList).build()).build(), LocalDateTime.now().plusSeconds(MathUtil.getRandomNumber(3,6)));
    }

    @Override
    public AutoInquiryEnum getInquiryType() {
        return AutoInquiryEnum.YES;
    }
}
