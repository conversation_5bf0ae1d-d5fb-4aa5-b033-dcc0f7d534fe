package com.xyy.saas.inquiry.hospital.server.api.hospital;

import com.xyy.saas.inquiry.enums.doctor.DoctorTypeEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryHospitalDeptDoctorDto;
import com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalDeptDoctorRelationApi;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalDetpDoctorRelationService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;

import java.util.List;

/**
 * 医院科室医生关系 API 实现类
 *
 * @Author: xucao
 * @Date: 2025/01/15 10:00
 * @Description: 医院科室医生关系服务API实现，提供批量操作和查询功能
 */
@DubboService
@Slf4j
public class InquiryHospitalDeptDoctorRelationApiImpl implements InquiryHospitalDeptDoctorRelationApi {

    @Resource
    private InquiryHospitalDetpDoctorRelationService inquiryHospitalDetpDoctorRelationService;

    /**
     * 批量保存医院科室医生关系 采用先删除后新增的策略，确保数据一致性
     *
     * @param relations 医院科室医生关系列表
     */
    @Override
    public void saveHospitalDeptDoctorRelations(List<InquiryHospitalDeptDoctorDto> relations) {
        inquiryHospitalDetpDoctorRelationService.saveHospitalDeptDoctorRelations(relations);
    }

    /**
     * 根据医生编码查询医院科室关系列表
     *
     * @param doctorPref 医生编码
     * @return 医院科室医生关系列表
     */
    @Override
    public List<InquiryHospitalDeptDoctorDto> getHospitalDeptDoctorByPref(String doctorPref, String hospitalPref, DoctorTypeEnum doctorTypeEnum) {
        return inquiryHospitalDetpDoctorRelationService.getHospitalDeptDoctorByPref(doctorPref, hospitalPref, doctorTypeEnum);
    }

}
