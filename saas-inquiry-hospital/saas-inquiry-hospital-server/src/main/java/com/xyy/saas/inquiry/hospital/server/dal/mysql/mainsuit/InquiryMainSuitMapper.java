package com.xyy.saas.inquiry.hospital.server.dal.mysql.mainsuit;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.hospital.server.controller.admin.mainsuit.vo.InquiryMainSuitPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.mainsuit.InquiryMainSuitDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 主诉信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryMainSuitMapper extends BaseMapperX<InquiryMainSuitDO> {

    default PageResult<InquiryMainSuitDO> selectPage(InquiryMainSuitPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InquiryMainSuitDO>()
            .likeIfPresent(InquiryMainSuitDO::getMainSuitName, reqVO.getMainSuitName())
            .eqIfPresent(InquiryMainSuitDO::getStatus, reqVO.getStatus())
            .eqIfPresent(InquiryMainSuitDO::getSexLimit, reqVO.getSexLimit())
            .betweenIfPresent(InquiryMainSuitDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(InquiryMainSuitDO::getId));
    }

}