package com.xyy.saas.inquiry.hospital.server.mq.consumer.prescription;// package com.xyy.saas.inquiry.hospital.server.mq.consumer.prescription;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.hospital.server.service.prescription.InquiryPrescriptionFlushService;
import com.xyy.saas.inquiry.mq.prescription.PrescriptionPricingEvent;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 处方划价 -   重新生成处方笺
 *
 * <AUTHOR>
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_hospital_server_mq_consumer_PrescriptionPricingConsumer",
    topic = PrescriptionPricingEvent.TOPIC)
public class PrescriptionPricingConsumer {

    @Resource
    private InquiryPrescriptionFlushService inquiryPrescriptionFlushService;

    public static final String GROUP_ID = PrescriptionPricingConsumer.class.getName();

    @EventBusListener
    public void prescriptionPricingConsumer(PrescriptionPricingEvent pricingEvent) {
        inquiryPrescriptionFlushService.flushPrescriptionPrice(pricingEvent.getMsg().getPrescriptionPref());
    }

}
