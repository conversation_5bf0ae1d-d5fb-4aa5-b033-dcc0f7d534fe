package com.xyy.saas.inquiry.hospital.server.dal.mysql.prescription;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionDetailQueryDTO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.InquiryPrescriptionDetailDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 处方记录详情 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryPrescriptionDetailMapper extends BaseMapperX<InquiryPrescriptionDetailDO> {


    default List<InquiryPrescriptionDetailDO> selectByCondition(InquiryPrescriptionDetailQueryDTO queryDTO) {
        return selectList(new LambdaQueryWrapperX<InquiryPrescriptionDetailDO>()
            .eqIfPresent(InquiryPrescriptionDetailDO::getTenantId, queryDTO.getTenantId())
            .eqIfPresent(InquiryPrescriptionDetailDO::getPrescriptionPref, queryDTO.getPrescriptionPref())
            .inIfPresent(InquiryPrescriptionDetailDO::getPrescriptionPref, queryDTO.getPrescriptionPrefList())
            .eqIfPresent(InquiryPrescriptionDetailDO::getProductPref, queryDTO.getProductPref())
            .eqIfPresent(InquiryPrescriptionDetailDO::getStandardId, queryDTO.getStandardId())
            .likeIfPresent(InquiryPrescriptionDetailDO::getProductName, queryDTO.getProductName())
            .likeIfPresent(InquiryPrescriptionDetailDO::getCommonName, queryDTO.getCommonName())
            .eqIfPresent(InquiryPrescriptionDetailDO::getApprovalNumber, queryDTO.getApprovalNumber())
            .orderByDesc(InquiryPrescriptionDetailDO::getId));
    }

    default List<InquiryPrescriptionDetailDO> selectByPrescriptionPref(String prescriptionPref) {
        return selectList(new LambdaQueryWrapperX<InquiryPrescriptionDetailDO>()
            .eq(InquiryPrescriptionDetailDO::getPrescriptionPref, prescriptionPref));
    }

    default List<InquiryPrescriptionDetailDO> selectByPrescriptionPrefs(List<String> prescriptionPrefs) {
        return selectList(new LambdaQueryWrapperX<InquiryPrescriptionDetailDO>()
            .in(InquiryPrescriptionDetailDO::getPrescriptionPref, prescriptionPrefs));
    }

    default List<InquiryPrescriptionDetailDO> selectByIdList(List<Long> idList) {
        return selectList(new LambdaQueryWrapperX<InquiryPrescriptionDetailDO>()
            .eq(InquiryPrescriptionDetailDO::getDeleted, false)
            .eq(InquiryPrescriptionDetailDO::getId, idList));
    }

    default List<InquiryPrescriptionDetailDO> selectByPrescriptionPrefList(List<String> prescriptionPrefList) {
        return selectList(new LambdaQueryWrapperX<InquiryPrescriptionDetailDO>()
            .in(InquiryPrescriptionDetailDO::getPrescriptionPref, prescriptionPrefList));
    }

}