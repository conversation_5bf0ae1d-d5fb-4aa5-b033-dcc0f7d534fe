package com.xyy.saas.inquiry.hospital.server.mq.producer.inquiry;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.mq.inquiry.InquiryEndEvent;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/12/11 11:50
 * @Description: 问诊结束事件生产者
 */
@Component
@EventBusProducer(
    topic = InquiryEndEvent.TOPIC
)
public class InquiryEndProducer extends EventBusRocketMQTemplate {

}
