package com.xyy.saas.inquiry.hospital.server.service.doctor;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.enums.doctor.DoctorTypeEnum;
import com.xyy.saas.inquiry.enums.user.CertificateTypeEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.identification.InquiryProfessionIdentificationDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorDetailRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryProfessionIdentificationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryProfessionIdentificationSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryProfessionIdentificationDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 问诊职业(医生药师)证件信息 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryProfessionIdentificationService {

    /**
     * 创建问诊职业(医生药师)证件信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInquiryProfessionIdentification(@Valid InquiryProfessionIdentificationSaveReqVO createReqVO);

    /**
     * 更新问诊职业(医生药师)证件信息
     *
     * @param updateReqVO 更新信息
     */
    void updateInquiryProfessionIdentification(@Valid InquiryProfessionIdentificationSaveReqVO updateReqVO);

    /**
     * 删除问诊职业(医生药师)证件信息
     *
     * @param id 编号
     */
    void deleteInquiryProfessionIdentification(Long id);

    /**
     * 获得问诊职业(医生药师)证件信息
     *
     * @param id 编号
     * @return 问诊职业(医生药师)证件信息
     */
    InquiryProfessionIdentificationDO getInquiryProfessionIdentification(Long id);

    /**
     * 获得问诊职业(医生药师)证件信息分页
     *
     * @param pageReqVO 分页查询
     * @return 问诊职业(医生药师)证件信息分页
     */
    PageResult<InquiryProfessionIdentificationDO> getInquiryProfessionIdentificationPage(InquiryProfessionIdentificationPageReqVO pageReqVO);

    /**
     * 医生新增场景获取证件信息
     *
     * @param saveReqVO
     * @return
     */
    List<InquiryProfessionIdentificationDO> getListByDoctorCreate(InquiryDoctorSaveReqVO saveReqVO);

    /**
     * 获取医生证件信息
     *
     * @param result
     */
    void setDoctorIdentification(InquiryDoctorDetailRespVO result);

    /**
     * 保存资质证件信息 先删再增
     *
     * @param professionIdentifications 资质列表
     */
    void saveProfessionIdentifications(List<InquiryProfessionIdentificationDto> professionIdentifications);

    /**
     * 查询用户证件信息
     *
     * @param personId
     * @param doctorTypeEnum 类型
     * @return 批量证件
     */
    List<InquiryProfessionIdentificationDto> getInquiryProfessionIdentification(Long personId, DoctorTypeEnum doctorTypeEnum);

    /**
     * 查询用户某个证件信息
     *
     * @param personId
     * @param doctorTypeEnum      类型
     * @param certificateTypeEnum 证件
     * @return 单个证件
     */
    InquiryProfessionIdentificationDto getProfessionIdentifications(Long personId, DoctorTypeEnum doctorTypeEnum, CertificateTypeEnum certificateTypeEnum);

    /**
     * 删除资质证件
     *
     * @param personId
     * @param doctorTypeEnum 类型
     */
    void deleteInquiryProfessionIdentification(Long personId, DoctorTypeEnum doctorTypeEnum);
}