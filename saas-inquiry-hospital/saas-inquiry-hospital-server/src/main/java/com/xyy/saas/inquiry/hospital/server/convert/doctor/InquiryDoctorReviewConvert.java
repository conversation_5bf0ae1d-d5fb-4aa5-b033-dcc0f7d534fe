package com.xyy.saas.inquiry.hospital.server.convert.doctor;

import com.xyy.saas.inquiry.hospital.api.doctor.review.dto.DoctorReviewDto;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorReviewsDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: xucao
 * @DateTime: 2025/3/17 16:06
 * @Description: 医生问诊评价转换器
 **/
@Mapper
public interface InquiryDoctorReviewConvert {

    InquiryDoctorReviewConvert INSTANCE = Mappers.getMapper(InquiryDoctorReviewConvert.class);

    DoctorReviewDto convertDO2DTO(DoctorReviewsDO reviewsDO);
}
