package com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo.DoctorReviewsPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorReviewsDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 医生问诊评价 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DoctorReviewsMapper extends BaseMapperX<DoctorReviewsDO> {

    default PageResult<DoctorReviewsDO> selectPage(DoctorReviewsPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DoctorReviewsDO>()
            .eqIfPresent(DoctorReviewsDO::getInquiryPref, reqVO.getInquiryPref())
            .eqIfPresent(DoctorReviewsDO::getPrescriptionPref, reqVO.getPrescriptionPref())
            .eqIfPresent(DoctorReviewsDO::getDoctorPref, reqVO.getDoctorPref())
            .eqIfPresent(DoctorReviewsDO::getSatisfactionScore, reqVO.getSatisfactionScore())
            .eqIfPresent(DoctorReviewsDO::getSatisfactionItem, reqVO.getSatisfactionItem())
            .eqIfPresent(DoctorReviewsDO::getReviewsContent, reqVO.getReviewsContent())
            .betweenIfPresent(DoctorReviewsDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(DoctorReviewsDO::getId));
    }

}