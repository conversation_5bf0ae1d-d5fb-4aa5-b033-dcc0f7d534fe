package com.xyy.saas.inquiry.hospital.server.service.doctor;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorAuditedRecordPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorAuditedRecordSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorAuditedRecordDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.DoctorAuditedRecordMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants.DOCTOR_AUDITED_RECORD_NOT_EXISTS;

/**
 * 医生审核记录 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class DoctorAuditedRecordServiceImpl implements DoctorAuditedRecordService {

    @Resource
    private DoctorAuditedRecordMapper doctorAuditedRecordMapper;

    @Override
    public Long createDoctorAuditedRecord(DoctorAuditedRecordSaveReqVO createReqVO) {
        // 插入
        DoctorAuditedRecordDO doctorAuditedRecord = BeanUtils.toBean(createReqVO, DoctorAuditedRecordDO.class);
        doctorAuditedRecordMapper.insert(doctorAuditedRecord);
        // 返回
        return doctorAuditedRecord.getId();
    }

    @Override
    public void updateDoctorAuditedRecord(DoctorAuditedRecordSaveReqVO updateReqVO) {
        // 校验存在
        /*validateDoctorAuditedRecordExists(updateReqVO.get);*/
        // 更新
        DoctorAuditedRecordDO updateObj = BeanUtils.toBean(updateReqVO, DoctorAuditedRecordDO.class);
        doctorAuditedRecordMapper.updateById(updateObj);
    }

    @Override
    public void deleteDoctorAuditedRecord(Long id) {
        // 校验存在
        validateDoctorAuditedRecordExists(id);
        // 删除
        doctorAuditedRecordMapper.deleteById(id);
    }

    private void validateDoctorAuditedRecordExists(Long id) {
        if (doctorAuditedRecordMapper.selectById(id) == null) {
            throw exception(DOCTOR_AUDITED_RECORD_NOT_EXISTS);
        }
    }

    @Override
    public DoctorAuditedRecordDO getDoctorAuditedRecord(Long id) {
        return doctorAuditedRecordMapper.selectById(id);
    }

    @Override
    public PageResult<DoctorAuditedRecordDO> getDoctorAuditedRecordPage(DoctorAuditedRecordPageReqVO pageReqVO) {
        return doctorAuditedRecordMapper.selectPage(pageReqVO);
    }

}