package com.xyy.saas.inquiry.hospital.server.service.mainsuit;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.server.controller.admin.mainsuit.vo.InquiryMainSuitPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.mainsuit.vo.InquiryMainSuitSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.mainsuit.InquiryMainSuitDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 主诉信息 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryMainSuitService {

    /**
     * 创建主诉信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMainSuit(@Valid InquiryMainSuitSaveReqVO createReqVO);

    /**
     * 更新主诉信息
     *
     * @param updateReqVO 更新信息
     */
    void updateMainSuit(@Valid InquiryMainSuitSaveReqVO updateReqVO);

    /**
     * 删除主诉信息
     *
     * @param id 编号
     */
    void deleteMainSuit(Long id);

    /**
     * 获得主诉信息
     *
     * @param id 编号
     * @return 主诉信息
     */
    InquiryMainSuitDO getMainSuit(Long id);


    /**
     * 获取热门主诉 前5条
     *
     * @return
     */
    List<InquiryMainSuitDO> getHotMainSuit();

    /**
     * 获得主诉信息分页
     *
     * @param pageReqVO 分页查询
     * @return 主诉信息分页
     */
    PageResult<InquiryMainSuitDO> getMainSuitPage(InquiryMainSuitPageReqVO pageReqVO);


}