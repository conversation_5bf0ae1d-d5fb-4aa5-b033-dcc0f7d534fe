package com.xyy.saas.inquiry.hospital.server.service.doctor.chain;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.constant.InquiryConstant;
import com.xyy.saas.inquiry.constant.RedisKeyConstants;
import com.xyy.saas.inquiry.enums.inquiry.GrabModelEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.hospital.server.dal.redis.doctor.DoctorRedisDao;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.util.MathUtil;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @Author: xucao
 * @Date: 2025/01/07 11:52
 * @see RepeatDistribute<PERSON><PERSON>er<PERSON><PERSON><PERSON> previous （真人问诊重复派单过滤）
 * @see InquirySendFilter<PERSON>hain next （从可派单医生中选取定量医生派单）
 * @Description: 自动抢单-抢单优先模式
 */
@Component
public class GrabFirstFilterChain extends DoctorFilterChain{

    @Resource
    private DoctorRedisDao doctorRedisDao;

    @Resource
    private ConfigApi configApi;

    @Override
    @TraceNode(node = TraceNodeEnum.DOCTOR_FILTER_GRABFIRST , prefLocation = "inquiryDto.pref")
    public void filter(InquiryRecordDto inquiryDto, List<String> doctorList) {
        //派单优先模式直接返回
        if(ObjectUtil.equals(GrabModelEnum.DISPATCH_FIRST, inquiryDto.getGrabModelEnum()) || inquiryDto.isAutoInquiry()){
            return;
        }
        // 获取所有开启了自动抢单的医生
        List<String> grabDoctorList = doctorRedisDao.getGrabDoctorList(RedisKeyConstants.getDoctorAutoGrabKey(inquiryDto.getEnv()));
        // 过滤掉已有自动抢单问诊中的医生
        grabDoctorList.removeIf(doctor -> doctorRedisDao.getDoctorCurrentAutoGrabList(doctor, inquiryDto.getEnv()).size() >= MathUtil.formatNumberWithDefault(configApi.getConfigValueByKey(InquiryConstant.INQUIRY_DOCTOR_AUTOGRAB_NUM),1) || !doctorList.contains(doctor));
        if(CollectionUtils.isEmpty(grabDoctorList)){
            return;
        }
        // 过滤结果取交集（仅保留开启了自动抢单且可以自动抢单的医生）
        doctorList.retainAll(grabDoctorList);
    }
}
