package com.xyy.saas.inquiry.hospital.server.dal.dataobject.medical;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.pojo.registration.MedicalRegistrationExtDto;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 医疗就诊登记(挂号) DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_medical_registration", autoResultMap = true)
@KeySequence("saas_medical_registration_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MedicalRegistrationDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 就诊编号
     */
    private String pref;
    /**
     * 租户编号
     */
    private Long tenantId;
    /**
     * 业务id
     */
    private String bizId;
    /**
     * 业务类型 0-问诊,1-智慧脸...
     */
    private Integer bizType;
    /**
     * 患者pref
     */
    private String patientPref;
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 患者手机号
     */
    private String patientMobile;
    /**
     * 患者身份证号
     */
    private String patientIdCard;
    /**
     * 业务就诊流水号
     */
    private String bizVisitId;
    /**
     * 医保就诊id
     */
    private String medicalVisitId;
    /**
     * 医保就诊登记时间
     */
    private LocalDateTime medicalVisitDate;
    /**
     * 医疗类别码
     */
    private String medType;
    /**
     * 参保地编号
     */
    private String insuredAreaNo;
    /**
     * 就医地编号
     */
    private String tenantAreaNo;
    /**
     * 参保人员编号
     */
    private String psnNo;
    /**
     * 住院/门诊号
     */
    private String iptOtpNo;
    /**
     * 单据状态
     */
    private Integer status;
    /**
     * 登记科室编号
     */
    private String deptPref;
    /**
     * 登记科室名称
     */
    private String deptName;
    /**
     * 医院编号
     */
    private String hospitalPref;
    /**
     * 医院名称
     */
    private String hospitalName;
    /**
     * 个人账户使用标志 0|不使用,1|使用
     */
    private Integer acctUsedFlag;
    /**
     * 预约登记时间
     */
    private LocalDateTime bookTime;
    /**
     * 预约执行日期
     */
    private LocalDateTime planTime;
    /**
     * 就诊挂号登记扩展字段
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private MedicalRegistrationExtDto ext;

    @JsonIgnore
    public MedicalRegistrationExtDto extGet() {
        if (ext == null) {
            ext = new MedicalRegistrationExtDto();
        }
        return ext;
    }
}