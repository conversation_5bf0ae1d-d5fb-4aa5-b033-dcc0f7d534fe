package com.xyy.saas.inquiry.hospital.server.dal.mysql.hospital;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalBindPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalBindReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalUserPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.HospitalAvailablePageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.HospitalEmployeeDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 医院员工关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface HospitalEmployeeMapper extends BaseMapperX<HospitalEmployeeDO> {

    default PageResult<HospitalEmployeeDO> selectPage(HospitalUserPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<HospitalEmployeeDO>()
                .eqIfPresent(HospitalEmployeeDO::getBindStatus, reqVO.getStatus())
                .eqIfPresent(HospitalEmployeeDO::getUserId, reqVO.getUserId())
                .betweenIfPresent(HospitalEmployeeDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(HospitalEmployeeDO::getHospitalPref, reqVO.getHospitalPref())
                .likeIfPresent(HospitalEmployeeDO::getHospitalName, reqVO.getHospitalName())
                .orderByDesc(HospitalEmployeeDO::getId));
    }

    default List<HospitalEmployeeDO> selectList(HospitalUserPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<HospitalEmployeeDO>()
                .eqIfPresent(HospitalEmployeeDO::getBindStatus, reqVO.getStatus())
                .eqIfPresent(HospitalEmployeeDO::getUserId, reqVO.getUserId())
                .betweenIfPresent(HospitalEmployeeDO::getCreateTime, reqVO.getCreateTime())
                .eqIfPresent(HospitalEmployeeDO::getHospitalPref, reqVO.getHospitalPref())
                .orderByDesc(HospitalEmployeeDO::getId));
    }

    default HospitalEmployeeDO selectByUserIdAndHospitalPref(HospitalBindReqVO bindReqVO) {
        return selectOne(new LambdaQueryWrapperX<HospitalEmployeeDO>()
                .eq(HospitalEmployeeDO::getUserId, bindReqVO.getUserId())
                .eq(HospitalEmployeeDO::getHospitalPref, bindReqVO.getHospitalPref())
            .eq(HospitalEmployeeDO::getBindStatus, CommonStatusEnum.ENABLE.getStatus())
        );
    }
}