package com.xyy.saas.inquiry.hospital.server.service.medical;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.server.controller.admin.medical.vo.MedicalInsuranceOrderPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.medical.vo.MedicalInsuranceOrderSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.medical.MedicalInsuranceOrderDO;
import jakarta.validation.Valid;

/**
 * 医保订单信息 Service 接口
 *
 * <AUTHOR>
 */
public interface MedicalInsuranceOrderService {

    /**
     * 创建医保订单信息
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createMedicalInsuranceOrder(@Valid MedicalInsuranceOrderSaveReqVO createReqVO);

    /**
     * 更新医保订单信息
     *
     * @param updateReqVO 更新信息
     */
    void updateMedicalInsuranceOrder(@Valid MedicalInsuranceOrderSaveReqVO updateReqVO);

    /**
     * 删除医保订单信息
     *
     * @param id 编号
     */
    void deleteMedicalInsuranceOrder(Long id);

    /**
     * 获得医保订单信息
     *
     * @param id 编号
     * @return 医保订单信息
     */
    MedicalInsuranceOrderDO getMedicalInsuranceOrder(Long id);

    /**
     * 获得医保订单信息分页
     *
     * @param pageReqVO 分页查询
     * @return 医保订单信息分页
     */
    PageResult<MedicalInsuranceOrderDO> getMedicalInsuranceOrderPage(MedicalInsuranceOrderPageReqVO pageReqVO);

}