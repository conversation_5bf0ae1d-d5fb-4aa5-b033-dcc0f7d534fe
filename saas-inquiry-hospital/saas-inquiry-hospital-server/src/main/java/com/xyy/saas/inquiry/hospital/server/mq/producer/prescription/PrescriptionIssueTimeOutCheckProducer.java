package com.xyy.saas.inquiry.hospital.server.mq.producer.prescription;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.hospital.server.mq.message.prescription.PrescriptionIssueTimeOutCheckEvent;
import org.springframework.stereotype.Component;

/**
 * 处方开具超时检查Producer
 *
 * @Author:chen<PERSON><PERSON>i
 * @Date:2024/12/18 11:25
 */
@Component
@EventBusProducer(
    topic = PrescriptionIssueTimeOutCheckEvent.TOPIC
)
public class PrescriptionIssueTimeOutCheckProducer extends EventBusRocketMQTemplate {

}
