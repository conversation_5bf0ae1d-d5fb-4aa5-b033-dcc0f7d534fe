package com.xyy.saas.inquiry.hospital.server.dal.redis.prescription;

import com.xyy.saas.inquiry.hospital.server.dal.redis.doctor.DoctorRedisDao;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Repository;

/**
 * @ClassName：PrescriptionRedisDAO
 * @Author: xucao
 * @Date: 2024/10/28 19:15
 * @Description: 处方相关redis 操作
 */
@Repository
@Slf4j
public class PrescriptionRedisDAO {

    @Resource
    protected RedisTemplate<String, Object> redisTemplate;

    @Resource
    private DoctorRedisDao doctorRedisDao;


}
