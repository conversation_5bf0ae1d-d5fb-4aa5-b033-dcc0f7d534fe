package com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorWorkRecordPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorWorkRecordDO;
import org.apache.ibatis.annotations.Mapper;


/**
 * 医生工作履历记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface Doctor<PERSON>orkRecordMapper extends BaseMapperX<DoctorWorkRecordDO> {

    default PageResult<DoctorWorkRecordDO> selectPage(DoctorWorkRecordPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<DoctorWorkRecordDO>()
            .eqIfPresent(DoctorWorkRecordDO::getDoctorId, reqVO.getDoctorId())
            .likeIfPresent(DoctorWorkRecordDO::getWorkUnitName, reqVO.getWorkUnitName())
            .eqIfPresent(DoctorWorkRecordDO::getJobPosition, reqVO.getJobPosition())
            .eqIfPresent(DoctorWorkRecordDO::getProver, reqVO.getProver())
            .betweenIfPresent(DoctorWorkRecordDO::getStartDate, reqVO.getStartDate())
            .betweenIfPresent(DoctorWorkRecordDO::getEndDate, reqVO.getEndDate())
            .betweenIfPresent(DoctorWorkRecordDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(DoctorWorkRecordDO::getId));
    }

    void deleteByDoctorId(Long doctorId);

}