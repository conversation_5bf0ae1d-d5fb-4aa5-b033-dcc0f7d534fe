package com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.enums.doctor.DoctorTypeEnum;
import com.xyy.saas.inquiry.enums.user.CertificateTypeEnum;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryProfessionIdentificationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryProfessionIdentificationDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Set;


/**
 * 问诊职业(医生药师)证件信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryProfessionIdentificationMapper extends BaseMapperX<InquiryProfessionIdentificationDO> {

    default PageResult<InquiryProfessionIdentificationDO> selectPage(InquiryProfessionIdentificationPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InquiryProfessionIdentificationDO>()
            .eqIfPresent(InquiryProfessionIdentificationDO::getPersonId, reqVO.getPersonId())
            .eqIfPresent(InquiryProfessionIdentificationDO::getDoctorType, reqVO.getDoctorType())
            .eqIfPresent(InquiryProfessionIdentificationDO::getCertificateType, reqVO.getCertificateType())
            .likeIfPresent(InquiryProfessionIdentificationDO::getCertificateName, reqVO.getCertificateName())
            .eqIfPresent(InquiryProfessionIdentificationDO::getCertificateNo, reqVO.getCertificateNo())
            .eqIfPresent(InquiryProfessionIdentificationDO::getCertificateImgUrl, reqVO.getCertificateImgUrl())
            .betweenIfPresent(InquiryProfessionIdentificationDO::getRegisterTime, reqVO.getRegisterTime())
            .betweenIfPresent(InquiryProfessionIdentificationDO::getValidTime, reqVO.getValidTime())
            .betweenIfPresent(InquiryProfessionIdentificationDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(InquiryProfessionIdentificationDO::getId));
    }

    void deleteByPersonId(Long personId);


    void deleteByPersonIdType(Long personId, Integer doctorType, Set<Integer> certTypes);

    default List<InquiryProfessionIdentificationDO> selectList(Long personId, DoctorTypeEnum doctorTypeEnum, CertificateTypeEnum certificateTypeEnum) {
        return selectList(new LambdaQueryWrapperX<InquiryProfessionIdentificationDO>()
            .eq(InquiryProfessionIdentificationDO::getPersonId, personId)
            .eq(doctorTypeEnum != null, InquiryProfessionIdentificationDO::getDoctorType, doctorTypeEnum == null ? null : doctorTypeEnum.getCode())
            .eq(certificateTypeEnum != null, InquiryProfessionIdentificationDO::getCertificateType, certificateTypeEnum == null ? null : certificateTypeEnum.getType()));
    }
}