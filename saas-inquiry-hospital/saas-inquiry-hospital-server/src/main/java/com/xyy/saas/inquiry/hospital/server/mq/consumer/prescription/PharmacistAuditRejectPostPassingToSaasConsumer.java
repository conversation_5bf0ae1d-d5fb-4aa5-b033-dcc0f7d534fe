package com.xyy.saas.inquiry.hospital.server.mq.consumer.prescription;


import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.hospital.server.service.prescription.SynInquiryPrescriptionToSaaS;
import com.xyy.saas.inquiry.mq.prescription.PharmacistAuditRejectPostPassingEvent;

import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_hospital_server_mq_consumer_prescription_PharmacistAuditRejectPostPassingToSaasConsumer",
    topic = PharmacistAuditRejectPostPassingEvent.TOPIC)
public class PharmacistAuditRejectPostPassingToSaasConsumer {
    public static final String GROUP_ID = PharmacistAuditRejectPostPassingToSaasConsumer.class.getName();


    @Resource
    private SynInquiryPrescriptionToSaaS synInquiryPrescriptionToSaaS;

    @EventBusListener
    public void pharmacistAuditRejectPostPassingToSaasConsumer(PharmacistAuditRejectPostPassingEvent event) {
        log.info("药师审核驳回传Saas消费者,event: {}", JsonUtils.toJsonString(event));
        synInquiryPrescriptionToSaaS.toSaas(event.getMsg());
    }
}
