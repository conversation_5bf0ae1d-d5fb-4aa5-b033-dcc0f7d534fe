package com.xyy.saas.inquiry.hospital.server.mq.consumer.doctor;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryStatusEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.hospital.enums.ErrorCodeConstants;
import com.xyy.saas.inquiry.hospital.server.controller.app.prescription.vo.PrescriptionGrabbingVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.mq.message.doctor.DoctorDistributeEvent;
import com.xyy.saas.inquiry.hospital.server.mq.message.doctor.dto.DoctorDistributeMessage;
import com.xyy.saas.inquiry.hospital.server.mq.message.prescription.PrescriptionAutoIssueEvent;
import com.xyy.saas.inquiry.hospital.server.mq.producer.prescription.PrescriptionAutoIssueProducer;
import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorService;
import com.xyy.saas.inquiry.hospital.server.service.prescription.PrescriptionService;
import com.xyy.saas.inquiry.hospital.server.service.reception.HospitalReceptionAreaService;
import com.xyy.saas.inquiry.im.api.user.InquiryImUserApi;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.util.MathUtil;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/12/26 11:03
 * @Description: 医生调度消费者
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_hospital_server_mq_consumer_doctor_DoctorDistributeConsumer",
    topic = DoctorDistributeEvent.TOPIC)
public class DoctorDistributeConsumer {

    @Resource
    private InquiryApi inquiryApi;

    @Resource
    private PrescriptionService prescriptionService;

    private DoctorDistributeConsumer getSelf() {
        return SpringUtil.getBean(this.getClass());
    }

    @Resource
    private PrescriptionAutoIssueProducer prescriptionAutoIssueProducer;

    @Resource
    private HospitalReceptionAreaService hospitalReceptionAreaService;

    @Resource
    private InquiryImUserApi inquiryImUserApi;

    @Resource
    private InquiryDoctorService inquiryDoctorService;

    @EventBusListener
    public void onMessage(DoctorDistributeEvent event) {
        DoctorDistributeMessage message = event.getMsg();
        log.info("收到问诊消息,问诊单号：{}", message.getInquiryPref());
        // 查询问诊单信息
        InquiryRecordDto inquiryDto = inquiryApi.getInquiryDtoByPref(message.getInquiryPref());
        // 判断问诊单是否存在
        if (ObjectUtil.isEmpty(inquiryDto)) {
            return;
        }
        // 设置医院医院科室信息
        inquiryDto.setHospitalDeptDto(message.getHospitalDeptDto());
        // 判断接诊状态
        if (inquiryDto.getInquiryStatus() != InquiryStatusEnum.QUEUING.getStatusCode()) {
            return;
        }
        // 自动开方处理 -- 直接接诊，延迟开方
        if (inquiryDto.isAutoInquiry()) {
            getSelf().autoInquiryHandle(inquiryDto, message.getDoctorList());
            return;
        }
        // 人工接诊处理
        getSelf().manualInquiryHandle(inquiryDto);
    }


    /**
     * 真人开方处理
     *
     * @param inquiryDto
     */
    public void manualInquiryHandle(InquiryRecordDto inquiryDto) {
        // 真人开方无人接诊情况下继续派单调度
        hospitalReceptionAreaService.distributeDoctorForInquiry(inquiryDto);
    }

    /**
     * 自动开方处理
     *
     * @param inquiryDto 问诊单信息
     * @param doctorList 接诊医生
     */
    @TraceNode(node = TraceNodeEnum.DOCTOR_RECEPTION_AUTO_INQUIRY , prefLocation = "inquiryDto.pref")
    public void autoInquiryHandle(InquiryRecordDto inquiryDto, List<String> doctorList) {
        log.info("自动开方开始抢单接诊,问诊单号：{}", inquiryDto.getPref());
        String doctorPref = doctorList.getFirst();
        // 检查创建医生im账号
        checkAndCreateDoctorImAccount(doctorPref);
        // 调用抢单接诊服务
        CommonResult result = prescriptionService.grabbingPrescriptionByDoctor(PrescriptionGrabbingVO.builder().doctorPref(doctorPref).inquiryPref(inquiryDto.getPref()).build());
        log.info("问诊单号：{},自动开方调用抢单接诊回参：{}", inquiryDto.getPref(), JSON.toJSONString(result));
        if (result.isError()) {
            log.error("自动接诊失败,失败原因：{}", result.getMsg());
            throw exception(ErrorCodeConstants.AUTO_INQUIRY_RECEPTION_ERROR);
        }
        // 视频自动开方不进行开方调度
        if (inquiryDto.isVideoAutoInquiry()) {
            return;
        }
        // 接诊成功后，延迟10-15秒开具处方
        prescriptionAutoIssueProducer.sendMessage(PrescriptionAutoIssueEvent.builder().msg(inquiryDto.getPref()).build(), LocalDateTime.now().plusSeconds(MathUtil.getRandomNumber(10, 15)));
    }

    /**
     * 检查创建医生im账号
     *
     * @param doctorPref 医生编号
     */
    private void checkAndCreateDoctorImAccount(String doctorPref) {
        try {
            InquiryDoctorDO doctor = inquiryDoctorService.getRequireInquiryDoctorByDoctorPref(doctorPref);
            inquiryImUserApi.getAndCreateInquiryImUser(doctor.getUserId(), ClientChannelTypeEnum.APP);
        } catch (Exception e) {
            log.error("检查创建医生im账号失败,医生编号：{}", doctorPref, e);
        }

    }
}
