package com.xyy.saas.inquiry.hospital.server.dal.dataobject.medical;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 医院/药店签到记录 DO
 *
 * <AUTHOR>
 */
@TableName("saas_medicare_signin_record")
@KeySequence("saas_medicare_signin_record_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MedicareSigninRecordDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    
    /**
     * 两定机构类型 1-医院 2-药店
     */
    private Integer institutionType;
    
    /**
     * 两定机构编码
     */
    private String institutionCode;
    
    /**
     * 医药机构编码
     */
    private String medicareInstitutionCode;
    
    /**
     * 操作员姓名
     */
    private String operatorName;
    
    /**
     * 操作员编码
     */
    private String operatorCode;
    
    /**
     * 签到号
     */
    private String signNo;
    
    /**
     * 签到状态 1-已签到 2-已签退
     */
    private Integer signinStatus;
    
    /**
     * 签到时间
     */
    private LocalDateTime signinTime;
    
    /**
     * 签退时间
     */
    private LocalDateTime signoutTime;
    
    /**
     * 租户ID
     */
    private Long tenantId;
} 