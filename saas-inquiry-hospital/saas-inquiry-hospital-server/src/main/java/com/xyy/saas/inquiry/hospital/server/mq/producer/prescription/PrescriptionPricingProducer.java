package com.xyy.saas.inquiry.hospital.server.mq.producer.prescription;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.mq.prescription.PrescriptionPricingEvent;
import org.springframework.stereotype.Component;

/**
 * 处方划价mqProducer
 *
 * @Author:ch<PERSON><PERSON><PERSON><PERSON>
 * @Date:2024/12/18 11:25
 */
@Component
@EventBusProducer(
    topic = PrescriptionPricingEvent.TOPIC
)
public class PrescriptionPricingProducer extends EventBusRocketMQTemplate {

}
