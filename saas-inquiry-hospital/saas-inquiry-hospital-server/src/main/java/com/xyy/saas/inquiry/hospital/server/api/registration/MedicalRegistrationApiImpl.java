package com.xyy.saas.inquiry.hospital.server.api.registration;

import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.hospital.api.medicare.MedicalRegistrationApi;
import com.xyy.saas.inquiry.hospital.api.medicare.dto.MedicalRegistrationDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.medical.vo.MedicalRegistrationRespVO;
import com.xyy.saas.inquiry.hospital.server.convert.medical.RegistrationConvert;
import com.xyy.saas.inquiry.hospital.server.service.medical.MedicalRegistrationService;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;

/**
 * @Author:chenxiaoyi
 * @Date:2025/02/10 17:50
 */
@DubboService
public class MedicalRegistrationApiImpl implements MedicalRegistrationApi {

    @Resource
    private MedicalRegistrationService medicalRegistrationService;

    @Override
    public MedicalRegistrationDto getMedicalRegistrationInfo(BizTypeEnum bizTypeEnum, String bizId) {
        final MedicalRegistrationRespVO registrationInfo = medicalRegistrationService.getMedicalRegistrationInfo(bizTypeEnum, bizId);
        return RegistrationConvert.INSTANCE.convertDto(registrationInfo);
    }
}
