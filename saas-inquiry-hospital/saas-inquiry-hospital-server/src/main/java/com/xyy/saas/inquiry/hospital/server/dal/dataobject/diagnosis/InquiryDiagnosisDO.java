package com.xyy.saas.inquiry.hospital.server.dal.dataobject.diagnosis;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 问诊诊断信息 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_diagnosis")
@KeySequence("saas_inquiry_diagnosis_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryDiagnosisDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 诊断编码
     */
    private String diagnosisCode;
    /**
     * 诊断名称
     */
    private String diagnosisName;
    /**
     * 诊断类型：0-默认(西医),1-中医
     */
    private Integer diagnosisType;
    /**
     * 展示诊断名称
     */
    private String showName;
    /**
     * 状态 0启用 1禁用
     */
    private Integer status;
    /**
     * 性别限制：0无限制,1限男,2限女
     */
    private Integer sexLimit;
    /**
     * 数据类型：1-常规, 2-系统默认 3-推荐
     */
    private Integer dataType;


}