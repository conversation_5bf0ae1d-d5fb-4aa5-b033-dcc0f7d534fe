package com.xyy.saas.inquiry.hospital.server.service.doctor;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.system.api.dict.DictDataApi;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.xyy.saas.inquiry.enums.doctor.AuditStatusEnum;
import com.xyy.saas.inquiry.hospital.server.config.foward.InquiryDoctorForwardClient;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorAuditedRecordSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorDetailRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.forward.InquiryDoctorForwardRespVO;
import com.xyy.saas.inquiry.hospital.server.convert.doctor.InquiryDoctorConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.service.doctor.dto.DoctorForwardDetailDto;
import com.xyy.saas.inquiry.hospital.server.service.doctor.dto.DoctorForwardDetailRespDto;
import com.xyy.saas.inquiry.hospital.server.service.doctor.dto.DoctorSyncQueryDto;
import com.xyy.saas.inquiry.pojo.ForwardResult;
import com.xyy.saas.inquiry.pojo.forward.ForwardPageResultDto;
import com.xyy.saas.inquiry.signature.api.ca.InquirySignatureCaAuthApi;
import com.xyy.saas.inquiry.signature.api.ca.dto.SyncCreateCaDto;
import com.xyy.saas.inquiry.util.FileApiUtil;
import jakarta.annotation.Resource;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.CompletableFuture;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author:chenxiaoyi
 * @Date:2025/03/03 13:40
 */
@Service
public class DoctorSyncServiceImpl implements DoctorSyncService {


    @Resource
    private InquiryDoctorForwardClient inquiryDoctorForwardClient;

    @Resource
    private DictDataApi dictDataApi;

    @Autowired
    private FileApi fileApi;

    @Autowired
    private AdminUserApi adminUserApi;

    @Resource
    private InquiryDoctorService inquiryDoctorService;

    @Resource
    private DoctorAuditedRecordService doctorAuditedRecordService;

    @DubboReference
    private InquirySignatureCaAuthApi inquirySignatureCaAuthApi;

    @Override
    public CommonResult<PageResult<InquiryDoctorForwardRespVO>> queryUserDoctorPhysicianLists(DoctorSyncQueryDto dto) {
        dto.setPageNum(dto.getPageNo());
        ForwardResult<ForwardPageResultDto<InquiryDoctorForwardRespVO>> forwardResult = inquiryDoctorForwardClient.queryUserDoctorPhysicianLists(dto);
        if (forwardResult.isSuccess()) {
            return CommonResult.success(new PageResult(forwardResult.getResult().getList(), forwardResult.getResult().getTotal()));
        }
        return CommonResult.error(forwardResult.getMsg());
    }

    @Override
    public CommonResult<InquiryDoctorDetailRespVO> queryUserDoctorPhysician(String guid) {
        ForwardResult<DoctorForwardDetailRespDto> forwardResult = inquiryDoctorForwardClient.syncUserDoctorPhysician(guid);
        if (!forwardResult.isSuccess()) {
            return CommonResult.error(forwardResult.getMsg());
        }
        // 组装同步的医生详情数据Vo
        DoctorForwardDetailDto dto = forwardResult.getResult().getDto();
        InquiryDoctorDetailRespVO detailRespVO = InquiryDoctorConvert.INSTANCE.convertSync(dto);
        Optional.ofNullable(forwardResult.getResult().getCaInfo()).ifPresent(l -> detailRespVO.setDoctorElectronSignImgUrl(l.getSignUrl()));
        Optional.ofNullable(forwardResult.getResult().getDto().getDoctorSeal()).ifPresent(detailRespVO::setDoctorElectronSignChapterUrl);
        // 处理字典
        Optional.ofNullable(dictDataApi.parseDictData(null, "hospital_level", dto.getOrgGrade()))
            .ifPresent(d -> detailRespVO.setFirstPracticeLevel(NumberUtils.toInt(d.getValue(), 1))); // 机构等级
        Optional.ofNullable(dictDataApi.parseDictData(null, "formal_level", dto.getDocEdu()))
            .ifPresent(d -> detailRespVO.setFormalLevel(NumberUtils.toInt(d.getValue(), 1))); // 学历

        return CommonResult.success(detailRespVO);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<InquiryDoctorDO> createInquiryDoctor(InquiryDoctorSaveReqVO createReqVO) {
        ForwardResult<DoctorForwardDetailRespDto> forwardResult = inquiryDoctorForwardClient.syncUserDoctorPhysician(createReqVO.getGuid());
        if (!forwardResult.isSuccess()) {
            return CommonResult.error(forwardResult.getMsg());
        }
        AdminUserRespDTO user = adminUserApi.getUserByMobile(createReqVO.getMobile());
        // 存在任意三要素不一致的情况
        if (user != null && (!StringUtils.equals(user.getNickname(), forwardResult.getResult().getDto().getDocName())
            || !Objects.equals(user.getSex(), forwardResult.getResult().getDto().getSex())
            || !StringUtils.equals(user.getIdCard(), forwardResult.getResult().getDto().getIdCard()))) {
            return CommonResult.error("当前用户基础信息已存在，且三要素不匹配(姓名、性别、身份证号),请检查处理一致后再操作同步");
        }
        // 确认同步时 处理图片上传
        handleDoctorImgUpload(createReqVO);
        // 判断医生新增或者修改
        InquiryDoctorDO inquiryDoctor = user != null ? inquiryDoctorService.getDoctorByUserId(user.getId()) : null;
        if (inquiryDoctor == null) {
            inquiryDoctor = inquiryDoctorService.createInquiryDoctor(createReqVO.setAuditStatus(AuditStatusEnum.APPROVED.getCode()));
            // 创建审核记录
            doctorAuditedRecordService.createDoctorAuditedRecord(DoctorAuditedRecordSaveReqVO.builder().doctorId(inquiryDoctor.getId())
                .auditorId(SecurityFrameworkUtils.getLoginUserId())
                .auditorName(SecurityFrameworkUtils.getLoginUserNickname())
                .auditResult(AuditStatusEnum.APPROVED.getCode()).build());
        } else {
            inquiryDoctorService.updateInquiryDoctor(createReqVO.setId(inquiryDoctor.getId()));
        }
        // 创建CA信息
        SyncCreateCaDto syncCreateCaDto = InquiryDoctorConvert.INSTANCE.convertDoctorCa(inquiryDoctor, forwardResult.getResult().getCaInfo(), forwardResult.getResult().getPersonInfo());
        if (StringUtils.isNotBlank(forwardResult.getResult().getDto().getDoctorSeal()) && syncCreateCaDto.getCaInfo() != null) {
            syncCreateCaDto.getCaInfo().setSealUrl(forwardResult.getResult().getDto().getDoctorSeal());
        }
        inquirySignatureCaAuthApi.createSyncSignatureCaAuth(syncCreateCaDto);
        return CommonResult.success(inquiryDoctor);
    }

    private static final String[] SIGN_PREFIX = new String[]{"?sign=", "?token="};

    /**
     * 异步处理图像上传
     *
     * @param detailRespVO
     */
    private void handleDoctorImgUpload(InquiryDoctorSaveReqVO detailRespVO) {
        CompletableFuture.allOf(
            CompletableFuture.runAsync(() -> {
                if (StringUtils.containsAny(detailRespVO.getPhoto(), SIGN_PREFIX)) {
                    detailRespVO.setPhoto(FileApiUtil.createFile(detailRespVO.getPhoto())); // 头像
                }
            }).exceptionally(e -> null),

            CompletableFuture.runAsync(() -> {
                if (StringUtils.containsAny(detailRespVO.getVerifiyImgUrl(), SIGN_PREFIX)) {
                    detailRespVO.setVerifiyImgUrl(FileApiUtil.createFile(detailRespVO.getVerifiyImgUrl())); // 查证结果
                }
            }).exceptionally(e -> null),

            CompletableFuture.runAsync(() -> {
                if (StringUtils.containsAny(detailRespVO.getPersonalImgUrl(), SIGN_PREFIX)) {
                    detailRespVO.setPersonalImgUrl(FileApiUtil.createFile(detailRespVO.getPersonalImgUrl())); // 胸牌
                }
            }).exceptionally(e -> null),

            CompletableFuture.runAsync(() -> {
                if (StringUtils.containsAny(detailRespVO.getIdCardFrontImgUrl(), SIGN_PREFIX)) {
                    detailRespVO.setIdCardFrontImgUrl(FileApiUtil.createFile(detailRespVO.getIdCardFrontImgUrl())); // 身份证正面
                }
            }).exceptionally(e -> null),

            CompletableFuture.runAsync(() -> {
                if (StringUtils.containsAny(detailRespVO.getIdCardReverseImgUrl(), SIGN_PREFIX)) {
                    detailRespVO.setIdCardReverseImgUrl(FileApiUtil.createFile(detailRespVO.getIdCardReverseImgUrl())); // 身份证反面
                }
            }).exceptionally(e -> null),

            CompletableFuture.runAsync(() -> {
                if (StringUtils.containsAny(detailRespVO.getTitleImgUrl(), SIGN_PREFIX)) {
                    detailRespVO.setTitleImgUrl(FileApiUtil.createFile(StringUtils.split(detailRespVO.getTitleImgUrl(), ",")[0])); // 职称证
                }
            }).exceptionally(e -> null),

            CompletableFuture.runAsync(() -> {
                if (CollUtil.isNotEmpty(detailRespVO.getOccupationImgUrls())) {
                    detailRespVO.setOccupationImgUrls(detailRespVO.getOccupationImgUrls().stream().map(u -> {    // 执业证
                        return StringUtils.containsAny(u, SIGN_PREFIX) ? FileApiUtil.createFile(u) : u;
                    }).toList());
                }
            }).exceptionally(e -> null),

            CompletableFuture.runAsync(() -> {
                if (CollUtil.isNotEmpty(detailRespVO.getQualificationImgUrls())) {
                    detailRespVO.setQualificationImgUrls(detailRespVO.getQualificationImgUrls().stream().map(u -> {  // 资格证
                        return StringUtils.containsAny(u, SIGN_PREFIX) ? FileApiUtil.createFile(u) : u;
                    }).toList());
                }
            }).exceptionally(e -> null)
        ).join();
    }
}
