package com.xyy.saas.inquiry.hospital.server.service.doctor;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorStatusPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorStatusRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorStatusSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorStatusDO;
import jakarta.validation.Valid;


/**
 * 医生出诊状态关系 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryDoctorStatusService {

    /**
     * 创建医生出诊状态关系
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInquiryDoctorStatus(@Valid InquiryDoctorStatusSaveReqVO createReqVO);

    /**
     * 更新医生出诊状态关系
     *
     * @param updateReqVO 更新信息
     */
    void updateInquiryDoctorStatus(@Valid InquiryDoctorStatusSaveReqVO updateReqVO);

    /**
     * 删除医生出诊状态关系
     *
     * @param id 编号
     */
    void deleteInquiryDoctorStatus(Long id);

    /**
     * 获得医生出诊状态关系
     *
     * @param id 编号
     * @return 医生出诊状态关系
     */
    InquiryDoctorStatusDO getInquiryDoctorStatus(Long id);

    /**
     * 获得医生出诊状态关系分页
     *
     * @param pageReqVO 分页查询
     * @return 医生出诊状态关系分页
     */
    PageResult<InquiryDoctorStatusDO> getInquiryDoctorStatusPage(InquiryDoctorStatusPageReqVO pageReqVO);

    /**
     * 医生出诊
     *
     * @param saveReqVO 出诊入参
     * @return 出诊结果
     */
    Boolean startReceipt(InquiryDoctorStatusSaveReqVO saveReqVO);

    /**
     * 医生停诊
     *
     * @param userId 用户id
     * @return
     */
    Boolean stopReceipt(Long userId);

    /**
     * 获取医生接诊范围
     *
     * @param userId
     * @return
     */
    InquiryDoctorStatusRespVO getDoctorReceiptScope(Long userId);

    /**
     * 更新医生接诊范围
     *
     * @param reqVO
     * @return
     */
    Boolean updateDoctorReceiptScope(InquiryDoctorStatusSaveReqVO reqVO);

    /**
     * 医生接诊权限变更时，维护更新已派单缓存
     *
     * @param doctorDO
     */
    void updateDoctorCanReceiptInquiryList(InquiryDoctorDO doctorDO);

    /**
     * 处理自动开方医生状态
     *
     * @param doctor 医生
     */
    void handleAutoInquiryDoctorStatus(InquiryDoctorDO doctor);
}