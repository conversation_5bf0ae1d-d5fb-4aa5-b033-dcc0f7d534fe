package com.xyy.saas.inquiry.hospital.server.dal.redis.hospital;

import com.xyy.saas.inquiry.util.RedisUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.springframework.stereotype.Repository;

/**
 * @Author: xucao
 * @Date: 2024/12/17 20:22
 * @Description: 医院科室相关redis操作类
 */
@Repository
public class HospitalRedisDao {
    /**
     * 根据接诊大厅队列的key,获取问诊单列表
     * @param key 接诊大厅的key
     * @return 问诊单列表
     */
    public List<String> getInquiryListByRecetionArea(String key){
        // 获取队列中的所有元素
        return new ArrayList<>(RedisUtils.lGetAll(key).stream().filter(Objects::nonNull).map(Object::toString).toList());
    }

    //从指定接诊大厅移除指定问诊单
    public void removeInquiryFromRecetionArea(String key, String inquiryPref){
        RedisUtils.lRem(key,inquiryPref);
    }

}
