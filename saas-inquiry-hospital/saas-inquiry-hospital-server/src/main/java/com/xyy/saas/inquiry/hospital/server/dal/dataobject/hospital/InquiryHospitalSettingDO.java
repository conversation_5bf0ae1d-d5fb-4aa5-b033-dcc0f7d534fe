package com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital;

import cn.iocoder.yudao.framework.common.util.json.JsonUtils;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import lombok.experimental.Accessors;

import java.util.Optional;
import java.util.function.Supplier;

/**
 * 医院参数配置 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_hospital_setting")
@KeySequence("saas_inquiry_hospital_setting_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class InquiryHospitalSettingDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 医院id
     */
    private Long hospitalId;
    /**
     * 参数类型 eg:1 默认处方笺模版（西药）
     */
    private Integer paramType;
    /**
     * 参数类型 eg:1 默认处方笺模版（西药）
     */
    private String paramName;
    /**
     * 值 eg:1 模板id
     */
    private String paramValue;
    /**
     * 参数描述
     */
    private String description;
    /**
     * 配置拓展字段
     */
    private String ext;


    public InquiryHospitalSettingDO setterParamValue(Supplier<Object> setter, boolean isNullDelete) {
        return Optional.ofNullable(setter.get()).map(String::valueOf)
            .map(this::setParamValue).orElseGet(() -> {
                if (isNullDelete) {
                    this.setDeleted(true);
                    this.setParamValue("");
                }
                return this;
            });
    }

    public InquiryHospitalSettingDO setterExt(Supplier<Object> setter, boolean isNullDelete) {
        return Optional.ofNullable(setter.get()).map(JsonUtils::toJsonString)
            .map(this::setExt).orElseGet(() -> {
                if (isNullDelete) {
                    this.setDeleted(true);
                    this.setExt(null);
                }
                return this;
            });
    }
}