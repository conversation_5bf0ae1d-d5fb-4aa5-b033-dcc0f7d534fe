package com.xyy.saas.inquiry.hospital.server.service.doctor;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorWorkRecordPageReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.DoctorWorkRecordSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorWorkRecordDO;
import jakarta.validation.Valid;

/**
 * 医生工作履历记录 Service 接口
 *
 * <AUTHOR>
 */
public interface DoctorWorkRecordService {

    /**
     * 创建医生工作履历记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createDoctorWorkRecord(@Valid DoctorWorkRecordSaveReqVO createReqVO);

    /**
     * 更新医生工作履历记录
     *
     * @param updateReqVO 更新信息
     */
    void updateDoctorWorkRecord(@Valid DoctorWorkRecordSaveReqVO updateReqVO);

    /**
     * 删除医生工作履历记录
     *
     * @param id 编号
     */
    void deleteDoctorWorkRecord(Long id);

    /**
     * 获得医生工作履历记录
     *
     * @param id 编号
     * @return 医生工作履历记录
     */
    DoctorWorkRecordDO getDoctorWorkRecord(Long id);

    /**
     * 获得医生工作履历记录分页
     *
     * @param pageReqVO 分页查询
     * @return 医生工作履历记录分页
     */
    PageResult<DoctorWorkRecordDO> getDoctorWorkRecordPage(DoctorWorkRecordPageReqVO pageReqVO);

}