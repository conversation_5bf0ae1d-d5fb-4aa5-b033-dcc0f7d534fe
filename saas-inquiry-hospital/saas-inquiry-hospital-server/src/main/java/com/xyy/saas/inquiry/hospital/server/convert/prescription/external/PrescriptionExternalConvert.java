package com.xyy.saas.inquiry.hospital.server.convert.prescription.external;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.web.core.util.LoginUserContextUtils;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.transmission.PrescriptionExternalTransmissionRespDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.medical.vo.MedicalRegistrationRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.external.vo.SaasPrescriptionExternalRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.external.vo.SaasPrescriptionExternalSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.prescription.vo.InquiryPrescriptionRespVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.prescription.external.SaasPrescriptionExternalDO;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionSupervisionConditionTransmitterDTO;
import com.xyy.saas.inquiry.pojo.transmitter.internet.PrescriptionTransmitterDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 处方外配
 *
 * @Author:chenxiaoyi
 * @Date:2024/10/12 17:17
 */
@Mapper
public interface PrescriptionExternalConvert {

    PrescriptionExternalConvert INSTANCE = Mappers.getMapper(PrescriptionExternalConvert.class);

    SaasPrescriptionExternalDO convert(SaasPrescriptionExternalSaveReqVO createReqVO);

    default SaasPrescriptionExternalSaveReqVO convertUpdateVo(PrescriptionTransmitterDTO prescriptionRespVO, PrescriptionExternalTransmissionRespDto transmissionRespDto) {

        if (prescriptionRespVO == null && transmissionRespDto == null) {
            return null;
        }

        SaasPrescriptionExternalSaveReqVO saasPrescriptionExternalSaveReqVO = new SaasPrescriptionExternalSaveReqVO();

        if (prescriptionRespVO != null) {
            saasPrescriptionExternalSaveReqVO.setBizId(prescriptionRespVO.getPref());
            saasPrescriptionExternalSaveReqVO.setTenantId(prescriptionRespVO.getTenantId());
            saasPrescriptionExternalSaveReqVO.setDeptPref(prescriptionRespVO.getDeptPref());
            saasPrescriptionExternalSaveReqVO.setDeptName(prescriptionRespVO.getDeptName());
            saasPrescriptionExternalSaveReqVO.setDoctorPref(prescriptionRespVO.getDoctorPref());
            saasPrescriptionExternalSaveReqVO.setDoctorName(prescriptionRespVO.getDoctorName());
            saasPrescriptionExternalSaveReqVO.setPharmacistPref(prescriptionRespVO.getPharmacistPref());
            saasPrescriptionExternalSaveReqVO.setPharmacistName(prescriptionRespVO.getPharmacistName());
            saasPrescriptionExternalSaveReqVO.setPatientPref(prescriptionRespVO.getPatientPref());
            saasPrescriptionExternalSaveReqVO.setPatientName(prescriptionRespVO.getPatientName());
            saasPrescriptionExternalSaveReqVO.setPatientIdCard(prescriptionRespVO.getPatientIdCard());
        }
        if (transmissionRespDto != null) {
            saasPrescriptionExternalSaveReqVO.setElectronicRxSn(transmissionRespDto.getElectronicRxSn());
            saasPrescriptionExternalSaveReqVO.setExternalRxPref(transmissionRespDto.getExternalRxPref());
            saasPrescriptionExternalSaveReqVO.setFixMedicalInstitutionsCode(transmissionRespDto.getFixMedicalInstitutionsCode());
            saasPrescriptionExternalSaveReqVO.setFixMedicalInstitutionsName(transmissionRespDto.getFixMedicalInstitutionsName());
            saasPrescriptionExternalSaveReqVO.setMedicareRxNo(transmissionRespDto.getMedicareRxNo());
            saasPrescriptionExternalSaveReqVO.setMedicareRxTraceCode(transmissionRespDto.getMedicareRxTraceCode());

            saasPrescriptionExternalSaveReqVO.extGet().setSignDigest(transmissionRespDto.getSignDigest());
            saasPrescriptionExternalSaveReqVO.extGet().setSignCertSn(transmissionRespDto.getSignCertSn());
            saasPrescriptionExternalSaveReqVO.extGet().setSignCertDn(transmissionRespDto.getSignCertDn());
            saasPrescriptionExternalSaveReqVO.extGet().setRxChkOpinions(transmissionRespDto.getRxChkOpinions());
        }
        saasPrescriptionExternalSaveReqVO.setBizType(com.xyy.saas.inquiry.enums.system.BizTypeEnum.HYWZ.getCode());
        return saasPrescriptionExternalSaveReqVO;
    }

    @Mapping(target = "fullName", source = "patientName")
    @Mapping(target = "businessNo", source = "pref")
    PrescriptionTransmitterDTO convertTransmission(InquiryPrescriptionRespVO prescriptionRespVO);

    default PrescriptionTransmitterDTO convertTransmission(InquiryPrescriptionRespVO prescriptionRespVO, SaasPrescriptionExternalDO externalDO) {
        PrescriptionTransmitterDTO dto = convertTransmission(prescriptionRespVO);
        dto.setElectronicRxSn(externalDO == null ? null : externalDO.getElectronicRxSn());
        dto.setExtPref(externalDO == null ? null : externalDO.getPref());
        dto.setUserId(LoginUserContextUtils.getLoginUserId());
        return dto;
    }

    PrescriptionSupervisionConditionTransmitterDTO convertSupervisionCondition(InquiryPrescriptionRespVO prescriptionRespVO);


    default PrescriptionTransmitterDTO convertPrescriptionTransmission(InquiryPrescriptionRespVO prescriptionRespVO, InquiryRecordDetailDto inquiryRecordDetail, TenantDto tenant) {
        PrescriptionTransmitterDTO prescriptionTransmitterDTO = convert(prescriptionRespVO);
        prescriptionTransmitterDTO.setIdCard(inquiryRecordDetail.getPatientIdCard());
        prescriptionTransmitterDTO.setPatientIdCard(inquiryRecordDetail.getPatientIdCard());
        prescriptionTransmitterDTO.setTenantInfo(tenant);
        return prescriptionTransmitterDTO;
    }

    @Mapping(target = "fullName", source = "patientName")
    @Mapping(target = "businessNo", source = "inquiryPref")
    @Mapping(target = "userId", expression = "java(cn.iocoder.yudao.framework.web.core.util.LoginUserContextUtils.getLoginUserId())")
    PrescriptionTransmitterDTO convert(InquiryPrescriptionRespVO prescriptionRespVO);


    PageResult<SaasPrescriptionExternalRespVO> convertPage(PageResult<SaasPrescriptionExternalDO> externalDOPageResult);

    @Mapping(target = "pref", ignore = true)
    @Mapping(target = "id", ignore = true)
    @Mapping(target = "bizId", source = "prescriptionRespVO.pref")
    @Mapping(target = "bizType", expression = "java(com.xyy.saas.inquiry.enums.system.BizTypeEnum.HYWZ.getCode())")
    @Mapping(target = "tenantId", source = "prescriptionRespVO.tenantId")
    @Mapping(target = "deptPref", source = "prescriptionRespVO.deptPref")
    @Mapping(target = "deptName", source = "prescriptionRespVO.deptName")
    @Mapping(target = "patientPref", source = "prescriptionRespVO.patientPref")
    @Mapping(target = "patientName", source = "prescriptionRespVO.patientName")
    @Mapping(target = "patientIdCard", source = "prescriptionRespVO.patientIdCard")
    @Mapping(target = "ext.prescriptionPdfUrl", source = "prescriptionRespVO.prescriptionPdfUrl")
    @Mapping(target = "requestStatus", expression = "java(com.xyy.saas.inquiry.enums.transmitter.RequestStatusEnum.FAILED.getCode())")
    @Mapping(target = "medicalVisitId", expression = "java(medicalRegistrationInfo == null ? null : medicalRegistrationInfo.getMedicalVisitId())")
    SaasPrescriptionExternalSaveReqVO convertSaveVo(PrescriptionTransmitterDTO prescriptionRespVO, MedicalRegistrationRespVO medicalRegistrationInfo);

    default void fillPrescriptionExternal(SaasPrescriptionExternalDO prescriptionExternalDO, PrescriptionExternalTransmissionRespDto data) {

        prescriptionExternalDO.extGet().setSignDigest(data.getSignDigest());
        prescriptionExternalDO.extGet().setSignCertSn(data.getSignCertSn());
        prescriptionExternalDO.extGet().setSignCertDn(data.getSignCertDn());
        prescriptionExternalDO.extGet().setRxChkOpinions(data.getRxChkOpinions());

        prescriptionExternalDO.setFixMedicalInstitutionsCode(data.getFixMedicalInstitutionsCode());
        prescriptionExternalDO.setFixMedicalInstitutionsName(data.getFixMedicalInstitutionsName());
        prescriptionExternalDO.setExternalRxPref(data.getExternalRxPref());
        prescriptionExternalDO.setElectronicRxSn(data.getElectronicRxSn());
        prescriptionExternalDO.setMedicareRxNo(data.getMedicareRxNo());
        prescriptionExternalDO.setMedicareRxTraceCode(data.getMedicareRxTraceCode());
    }
}

