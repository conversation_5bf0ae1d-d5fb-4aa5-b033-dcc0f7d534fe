package com.xyy.saas.inquiry.hospital.server.service.hospital;

import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryHospitalDeptDoctorDO;
import com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor.InquiryHospitalDeptDoctorMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * @Author: xucao
 * @DateTime: 2025/4/11 18:21
 * @Description: 医院科室医生服务
 * @Version: 1.0
 **/
@Service
public class InquiryHospitalDeptDoctorServiceImpl implements InquiryHospitalDeptDoctorService{

    @Resource
    private InquiryHospitalDeptDoctorMapper inquiryHospitalDeptDoctorMapper;

    @Override
    public List<InquiryHospitalDeptDoctorDO> selectList(InquiryHospitalDepartmentRelationPageReqVO reqVO) {
        return inquiryHospitalDeptDoctorMapper.selectList(reqVO);
    }
}
