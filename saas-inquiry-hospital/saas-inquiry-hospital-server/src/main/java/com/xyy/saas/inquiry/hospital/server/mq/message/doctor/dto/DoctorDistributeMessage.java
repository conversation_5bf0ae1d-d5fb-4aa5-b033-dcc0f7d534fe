package com.xyy.saas.inquiry.hospital.server.mq.message.doctor.dto;

import java.io.Serializable;
import java.util.List;
import com.xyy.saas.inquiry.pojo.HospitalDeptDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: xucao
 * @Date: 2024/12/26 10:00
 * @Description: 医生调度消息体
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class DoctorDistributeMessage implements Serializable {

    /**
     * 问诊单号
     */
    private String inquiryPref;

    /**
     * 医生列表
     */
    private List<String> doctorList;

    /**
     * 医院科室信息
     */
    private HospitalDeptDto hospitalDeptDto;
}
