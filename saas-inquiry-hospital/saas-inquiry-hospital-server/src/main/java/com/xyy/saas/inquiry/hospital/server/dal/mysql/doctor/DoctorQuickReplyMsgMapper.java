package com.xyy.saas.inquiry.hospital.server.dal.mysql.doctor;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.hospital.server.controller.app.doctor.vo.DoctorQuickReplyMsgPageReqVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.DoctorQuickReplyMsgDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 医生快捷回复语 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface DoctorQuickReplyMsgMapper extends BaseMapperX<DoctorQuickReplyMsgDO> {

    default List<DoctorQuickReplyMsgDO> selectList(DoctorQuickReplyMsgPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<DoctorQuickReplyMsgDO>()
            .eq(DoctorQuickReplyMsgDO::getDoctorId, reqVO.getDoctorId())
            .eqIfPresent(DoctorQuickReplyMsgDO::getParentId, reqVO.getParentId())
            .eqIfPresent(DoctorQuickReplyMsgDO::getTitle, reqVO.getTitle())
            .eqIfPresent(DoctorQuickReplyMsgDO::getContent, reqVO.getContent())
            .betweenIfPresent(DoctorQuickReplyMsgDO::getCreateTime, reqVO.getCreateTime())
            .orderByAsc(DoctorQuickReplyMsgDO::getSorted));
    }

    default Long selectTitleCount(Long doctorId) {
        return selectCount(new LambdaQueryWrapperX<DoctorQuickReplyMsgDO>().eq(DoctorQuickReplyMsgDO::getDoctorId, doctorId).isNull(DoctorQuickReplyMsgDO::getParentId));
    }

    default Long selectContentCount(Long doctorId, Long parentId) {
        return selectCount(new LambdaQueryWrapperX<DoctorQuickReplyMsgDO>().eq(DoctorQuickReplyMsgDO::getDoctorId, doctorId).eq(DoctorQuickReplyMsgDO::getParentId, parentId));
    }
}