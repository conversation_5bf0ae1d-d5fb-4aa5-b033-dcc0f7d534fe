package com.xyy.saas.inquiry.hospital.api.prescription;


import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionDetailQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionDetailRespDTO;
import java.util.List;

/**
 * 处方记录 Api 接口
 *
 * <AUTHOR>
 */
public interface InquiryPrescriptionDetailApi {


    /**
     * 查询处方详情 - 根据处方单号
     *
     * @param prescriptionPref 处方编号
     */
    List<InquiryPrescriptionDetailRespDTO> getPrescriptionDetail(String prescriptionPref);

    /**
     * 根据条件查询处方详情
     *
     * @param queryDTO
     * @return
     */
    List<InquiryPrescriptionDetailRespDTO> getInquiryPrescriptionDetailListByCondition(InquiryPrescriptionDetailQueryDTO queryDTO);
}