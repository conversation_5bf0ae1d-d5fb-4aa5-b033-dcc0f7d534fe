package com.xyy.saas.inquiry.hospital.api.hospital.dto;

import com.xyy.saas.inquiry.pojo.condition.ConditionGroup;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import lombok.experimental.Accessors;

@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Accessors(chain = true)
public class InquiryHospitalSpecificPrescriptionTemplateDto extends ConditionGroup {

    /**
     * 处方笺模板id
     */
    private Long prescriptionTemplateId;

}