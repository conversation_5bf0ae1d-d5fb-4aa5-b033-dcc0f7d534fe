package com.xyy.saas.inquiry.hospital.api.diagnosis.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquiryDiagnosisDepartmentRelationDto implements Serializable {

    private Long id;

    /**
     * 诊断编码
     */
    private String diagnosisCode;

    /**
     * 诊断名称
     */
    private String diagnosisName;

    /**
     * 展示诊断名称
     */
    private String showName;

    /**
     * 医院科室id
     */
    private Long deptId;

    /**
     * 科室编码
     */
    private String deptPref;

    /**
     * 科室名称
     */
    private String deptName;

    /**
     * 是否禁用
     */
    private Boolean disable;

}