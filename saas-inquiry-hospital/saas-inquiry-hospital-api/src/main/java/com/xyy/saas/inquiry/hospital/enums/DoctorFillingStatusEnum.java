package com.xyy.saas.inquiry.hospital.enums;

import lombok.Getter;

/**
 * 处方划价操作类型
 *
 * @Author:<PERSON><PERSON><PERSON><PERSON><PERSON>
 * @Date:2025/03/10 13:42
 */
@Getter
public enum DoctorFillingStatusEnum {

    NOT(0, "未备案"),
    DONE(1, "已备案"),
    ;

    private int type;

    private String desc;

    DoctorFillingStatusEnum(int type, String desc) {
        this.type = type;
        this.desc = desc;
    }
}
