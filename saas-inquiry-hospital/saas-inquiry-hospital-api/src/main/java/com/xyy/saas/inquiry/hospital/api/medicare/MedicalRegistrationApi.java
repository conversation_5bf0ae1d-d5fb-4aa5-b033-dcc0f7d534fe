package com.xyy.saas.inquiry.hospital.api.medicare;

import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.hospital.api.medicare.dto.MedicalRegistrationDto;


/**
 * @Author:chen<PERSON><PERSON>i
 * @Date:2025/02/10 17:48
 */
public interface MedicalRegistrationApi {

    /**
     * 获取就诊登记信息
     *
     * @param bizTypeEnum 业务线类型
     * @param bizId       业务id
     * @return 就诊登记dto
     */
    MedicalRegistrationDto getMedicalRegistrationInfo(BizTypeEnum bizTypeEnum, String bizId);


}
