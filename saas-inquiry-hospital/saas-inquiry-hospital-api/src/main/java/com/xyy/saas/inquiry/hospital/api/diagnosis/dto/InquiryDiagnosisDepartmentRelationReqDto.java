package com.xyy.saas.inquiry.hospital.api.diagnosis.dto;

import java.io.Serializable;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:chenxiaoyi
 * @Date:2024/09/30 10:46
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryDiagnosisDepartmentRelationReqDto implements Serializable {

    /**
     * 诊断编码集合
     */
    private List<String> diagnosisCodes;

    /**
     *诊断编码
     */
    private String diagnosisCode;

    /**
     * 科室ids
     */
    private List<Long> deptIds;


    private Long deptId;

    /**
     * 科室prefs
     */
    private List<String> deptPrefs;


}
