package com.xyy.saas.inquiry.hospital.api.medicare.transmission;

import com.xyy.saas.inquiry.pojo.BaseDto;
import com.xyy.saas.inquiry.pojo.registration.MedicalRegistrationExtDto;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 医疗就诊登记(挂号) Dto
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class MedicalRegistrationTransmissionRespDto extends BaseDto {

    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 患者手机号
     */
    private String patientMobile;
    /**
     * 患者身份证号
     */
    private String patientIdCard;
    /**
     * 业务就诊流水号
     */
    private String bizVisitId;
    /**
     * 医保就诊id
     */
    private String medicalVisitId;
    /**
     * 医保就诊登记时间
     */
    private LocalDateTime medicalVisitDate;
    /**
     * 医疗类别码
     */
    private String medType;
    /**
     * 参保地编号
     */
    private String insuredAreaNo;
    /**
     * 就医地编号
     */
    private String tenantAreaNo;
    /**
     * 参保人员编号
     */
    private String psnNo;
    /**
     * 住院/门诊号
     */
    private String iptOtpNo;
    /**
     * 单据状态
     */
    private Integer status;
    /**
     * 登记科室编号
     */
    private String deptPref;
    /**
     * 登记科室名称
     */
    private String deptName;
    /**
     * 医院编号
     */
    private String hospitalPref;
    /**
     * 医院名称
     */
    private String hospitalName;
    /**
     * 个人账户使用标志 0|不使用,1|使用
     */
    private Integer acctUsedFlag;
    /**
     * 预约登记时间
     */
    private LocalDateTime bookTime;
    /**
     * 预约执行日期
     */
    private LocalDateTime planTime;
    /**
     * 就诊挂号登记扩展字段
     */
    private MedicalRegistrationExtDto ext;

}