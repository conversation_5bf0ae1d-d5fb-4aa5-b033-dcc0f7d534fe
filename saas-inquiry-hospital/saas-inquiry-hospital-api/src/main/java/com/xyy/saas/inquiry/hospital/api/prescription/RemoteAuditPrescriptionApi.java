package com.xyy.saas.inquiry.hospital.api.prescription;

import com.xyy.saas.inquiry.hospital.api.prescription.dto.RemotePrescriptionDTO;
import java.util.List;

/**
 * @Author: xucao
 * @DateTime: 2025/4/22 15:22
 * @Description: 远程审方接口
 **/
public interface RemoteAuditPrescriptionApi {

    /**
     * 保存远程审方处方
     *
     * @param prescriptionUpdateDTO 远程审方 处方信息
     */
    void saveRemotePrescription(RemotePrescriptionDTO prescriptionUpdateDTO);

    /**
     * 取消远程审方处方
     *
     * @param inquiryPref 问诊单号
     */
    void cancelRemotePrescription(String inquiryPref);

    /**
     * 批量取消远程审方处方
     *
     * @param inquiryPrefList 问诊单列表
     */
    void batchCancelRemotePrescription(List<String> inquiryPrefList);
}
