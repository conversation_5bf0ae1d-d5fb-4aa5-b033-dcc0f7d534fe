package com.xyy.saas.inquiry.hospital.api.prescription.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2024/12/03 19:19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class InquiryPrescriptionUrlDTO implements Serializable {

    private Long id;

    @Schema(description = "处方编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1517")
    private String pref;

    @Schema(description = "处方笺图片url", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    private String prescriptionImgUrl;

    @Schema(description = "处方笺PDFurl", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    private String prescriptionPdfUrl;

}
