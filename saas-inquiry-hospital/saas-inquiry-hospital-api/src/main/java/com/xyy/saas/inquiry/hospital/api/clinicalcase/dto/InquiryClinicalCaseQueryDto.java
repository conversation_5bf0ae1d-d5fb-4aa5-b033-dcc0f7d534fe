package com.xyy.saas.inquiry.hospital.api.clinicalcase.dto;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import io.swagger.v3.oas.annotations.media.Schema;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 门诊病例分页 Request VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString(callSuper = true)
public class InquiryClinicalCaseQueryDto implements Serializable {

    @Schema(description = "病例号")
    private String pref;

    /**
     * 门店id
     */
    @Schema(description = "门店id")
    private Long tenantId;

    @Schema(description = "业务类型")
    private Integer bizType;

    @Schema(description = "问诊编号")
    private String inquiryPref;

    @Schema(description = "互联网医院编号")
    private String hospitalPref;

    @Schema(description = "医生编号")
    private String doctorPref;

    @Schema(description = "科室编码")
    private String deptPref;

    @Schema(description = "患者pref")
    private String patientPref;

    @Schema(description = "患者姓名", example = "赵六")
    private String patientName;

    @Schema(description = "患者身份证号")
    private String patientIdCard;

    @Schema(description = "复诊标识 0初次 1复诊")
    private Integer followUp;

    @Schema(description = "主要症状")
    private String mainSymptoms;

    @Schema(description = "门诊诊断说明")
    private String outpatientDiagnosisDesc;

    @Schema(description = "处理措施")
    private String measures;

    @Schema(description = "是否需要留院观察 0否 1是")
    private Integer observation;

    @Schema(description = "转诊标识 0非转诊")
    private Integer referral;

    @Schema(description = "西医诊断编码")
    private String diagnosisCode;

    @Schema(description = "中医诊断编码")
    private String tcmDiagnosisCode;

    @Schema(description = "中医辨证代码")
    private String tcmSyndromeCode;

    @Schema(description = "中医治法代码")
    private String tcmTreatmentMethodCode;

    @Schema(description = "中医四诊描述")
    private String tcmFourDiagnosticDesc;

    @Schema(description = "是否上传舌象 0否 1是")
    private Integer tcmUploadTongueImage;

    @Schema(description = "中医辨证分析")
    private String tcmDialecticalAnalysis;


    @Schema(description = "备注说明", example = "随便")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}