package com.xyy.saas.inquiry.hospital.api.prescription;


import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionFlushQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionUpdateDTO;
import com.xyy.saas.inquiry.pojo.patient.PatientSimpleDTO;
import jakarta.validation.Valid;

/**
 * 处方记录 Api 接口
 *
 * <AUTHOR>
 */
public interface InquiryPrescriptionApi {

    /**
     * 获得处方记录
     *
     * @param queryDTO 查询参数
     * @return 处方记录
     */
    InquiryPrescriptionRespDTO getInquiryPrescription(InquiryPrescriptionQueryDTO queryDTO);


    /**
     * 获得处方记录 + 处理图片日期
     *
     * @param queryDTO 查询参数
     * @return 处方记录
     */
    InquiryPrescriptionRespDTO getInquiryPrescriptionWithPdf(InquiryPrescriptionQueryDTO queryDTO);

    /**
     * 药师查询处方患者列表
     *
     * @param queryDTO 查询参数
     * @return 处方记录
     */
    IPage<PatientSimpleDTO> getPrescriptionPatientList(InquiryPrescriptionQueryDTO queryDTO);

    /**
     * 更新处方
     *
     * @param prescriptionUpdateDTO 更新Dto
     */
    void updateInquiryPrescription(@Valid InquiryPrescriptionUpdateDTO prescriptionUpdateDTO);

    /**
     * 统计处方数量
     *
     * @param queryDTO 查询dto
     * @return 数量
     */
    Long getPrescriptionCount(InquiryPrescriptionQueryDTO queryDTO);

    /**
     * 处方分配药师审核
     *
     * @param id     处方id
     * @param userId userId
     * @return 是否分配成功
     */
    boolean distributePharmacist(Long id, Long userId);

    /**
     * 处方释放审核药师
     *
     * @param id     处方id
     * @param userId userId
     * @return 是否分配成功
     */
    boolean releasePharmacist(Long id, Long userId);

    /**
     * 处方开具后置处理
     *
     * @param pref 处方编号
     */
    void postProcessIssuesPrescription(String pref);

    /**
     * 获得处方记录分页
     *
     * @param inquiryPrescriptionQueryDTO 分页查询
     * @return 处方记录分页
     */
    PageResult<InquiryPrescriptionRespDTO> getInquiryPrescriptionPage(@Valid InquiryPrescriptionQueryDTO inquiryPrescriptionQueryDTO);

    /**
     * 获取门店刷处方列表
     *
     * @param inquiryPrescriptionQueryDTO 分页查询
     * @return 处方记录分页
     */
    PageResult<InquiryPrescriptionRespDTO> getFlushOfflinePrescriptionPage(InquiryPrescriptionFlushQueryDTO inquiryPrescriptionFlushQueryDTO);


}