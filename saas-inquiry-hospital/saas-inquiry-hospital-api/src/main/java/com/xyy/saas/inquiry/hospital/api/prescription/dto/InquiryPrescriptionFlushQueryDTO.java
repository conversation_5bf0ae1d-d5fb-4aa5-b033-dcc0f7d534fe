package com.xyy.saas.inquiry.hospital.api.prescription.dto;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:chenxiaoyi
 * @Date:2024/12/03 19:19
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class InquiryPrescriptionFlushQueryDTO extends PageParam {

    private Long maxId;

    @Schema(description = "租户ID", example = "1517")
    private Long tenantId;

    @Schema(description = "处方状态  0、待开方   1、已取消   2、待审核     3、审核中  4、审核通过   5、审核驳回", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer status;

    /**
     * {@link com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum}
     */
    @Schema(description = "当前审方人类型 1-医生,2-药店,3-平台,4-医院", example = "2")
    private Integer auditorType;

    @Schema(description = "当前审方人类型s 1-医生,2-药店,3-平台,4-医院", example = "2")
    private List<Integer> auditorTypes;

    @Schema(description = "使用状态：0 初始 1可用 2已用 3过期  4失效", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer useStatus;

    /**
     * 问诊业务类型 {@link com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum}
     */
    private Integer inquiryBizType;


    @Schema(description = "医师出方时间")
    private LocalDateTime outPrescriptionTime;

}
