package com.xyy.saas.inquiry.hospital.api.prescription.dto;

import cn.iocoder.yudao.framework.mybatis.core.type.StringListTypeHandler;
import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionExtDto;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

/**
 * @Author: xucao
 * @DateTime: 2025/4/22 15:24
 * @Description: 远程审方处方 dto
 **/
@Data
public class RemotePrescriptionDTO implements Serializable {

    /**
     * 处方编码
     */
    private String pref;

    /**
     * 租户id
     */
    private Long tenantId;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 问诊编号
     */
    private String inquiryPref;
    /**
     * 互联网医院编码
     */
    private String hospitalPref;

    /**
     * 患者编码
     */
    private String patientPref;
    /**
     * 患者姓名
     */
    private String patientName;
    /**
     * 患者性别：1 男 2 女
     */
    private Integer patientSex;
    /**
     * 患者年龄
     */
    private String patientAge;
    /**
     * 患者手机号
     */
    private String patientMobile;
    /**
     * 医师姓名
     */
    private String doctorName;
    /**
     * 药师编码
     */
    private String pharmacistPref;
    /**
     * 药师姓名
     */
    private String pharmacistName;

    /**
     * 处方状态  0、待开方   1、已取消   2、待审核     3、审核中  4、审核通过   5、审核驳回
     */
    private Integer status;
    /**
     * 当前审方人类型 1-医生,2-药店,3-平台,4-医院 {@link com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum}
     */
    private Integer auditorType;
    /**
     * 处方分发状态 0-未分配,1-已分配
     */
    private Integer distributeStatus;
    /**
     * 处方分配的用户id
     */
    private Long distributeUserId;

    /**
     * 用药类型：0西药，1中药
     */
    private Integer medicineType;

    /**
     * 失效原因
     */
    private String invalidReason;
    /**
     * 失效时间
     */
    private LocalDateTime invalidTime;
    /**
     * 费别
     */
    private String feeType;
    /**
     * 问诊开始时间
     */
    private LocalDateTime inquiryStartTime;
    /**
     * 问诊结束时间
     */
    private LocalDateTime inquiryEndTime;
    /**
     * 医师出方时间
     */
    private LocalDateTime outPrescriptionTime;
    /**
     * 药师审核时间
     */
    private LocalDateTime auditPrescriptionTime;
    /**
     * 订单号
     */
    private String orderNo;


    /**
     * 互联网平台处方号
     */
    private String thirdPrescriptionNo;

    /**
     * 主诉
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> mainSuit;
    /**
     * 诊断编码
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> diagnosisCode;
    /**
     * 诊断说明
     */
    @TableField(typeHandler = StringListTypeHandler.class)
    private List<String> diagnosisName;
    /**
     * 处方笺图片url
     */
    private String prescriptionImgUrl;
    /**
     * 处方笺PDFurl
     */
    private String prescriptionPdfUrl;
    /**
     * 病历img图片url
     */
    private String caseImgUrl;
    /**
     * 问诊方式  1、图文问诊  2、视频问诊  3、电话问诊
     */
    private Integer inquiryWayType;
    /**
     * 问诊业务类型 1、药店问诊  2、远程审方
     */
    private Integer inquiryBizType;
    /**
     * 客户端渠类型 0、app  1、pc  2、小程序
     */
    private Integer clientChannelType;
    /**
     * 医生客户端类型 0、app  1、pc
     */
    private Integer doctorChannelType;
    /**
     * 医生操作系统类型 Android ，iOS，Windows，Mac，Linux
     */
    private String doctorOsType;
    /**
     * 问诊渠道 0、荷叶 1、智慧脸  2、海典ERP
     */
    private Integer bizChannelType;
    /**
     * 处方打印状态（0-未打印、1-已打印、NULL -未知）
     */
    private Integer printStatus;
    /**
     * 签章平台类型 1、法大大  2、自绘
     */
    private Integer signPlatform;
    /**
     * 处方拓展字段
     */
    private PrescriptionExtDto ext;

    @JsonIgnore
    public PrescriptionExtDto extGet() {
        if (ext == null) {
            ext = new PrescriptionExtDto();
        }
        return ext;
    }

    /**
     * 扣额度的id
     */
    private Long costId;
}
