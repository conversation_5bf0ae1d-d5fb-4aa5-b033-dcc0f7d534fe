package com.xyy.saas.inquiry.signature.server.dal.mysql.prescriptiontemplate;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo.InquiryPrescriptionTemplatePageReqVO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.prescriptiontemplate.InquiryPrescriptionTemplateDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 处方笺模板 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryPrescriptionTemplateMapper extends BaseMapperX<InquiryPrescriptionTemplateDO> {

    default PageResult<InquiryPrescriptionTemplateDO> selectPage(InquiryPrescriptionTemplatePageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InquiryPrescriptionTemplateDO>()
            .eqIfPresent(InquiryPrescriptionTemplateDO::getId, reqVO.getId())
            .inIfPresent(InquiryPrescriptionTemplateDO::getId, reqVO.getIdList())
            .eqIfPresent(InquiryPrescriptionTemplateDO::getDisable, reqVO.getDisable())
            .likeIfPresent(InquiryPrescriptionTemplateDO::getName, reqVO.getName())
            .eqIfPresent(InquiryPrescriptionTemplateDO::getType, reqVO.getType())
            .inIfPresent(InquiryPrescriptionTemplateDO::getType, reqVO.getTypeList())
            .eqIfPresent(InquiryPrescriptionTemplateDO::getUrl, reqVO.getUrl())
            .betweenIfPresent(InquiryPrescriptionTemplateDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(InquiryPrescriptionTemplateDO::getCreateTime));
    }

}