package com.xyy.saas.inquiry.signature.server.service.fdd.handler;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.signature.server.service.fdd.bean.FddCallbackHandlerCode;
import com.xyy.saas.inquiry.signature.server.service.fdd.enums.FddCallbackEvent;
import org.springframework.stereotype.Component;

/**
 * 法大大 默认回调处理器
 *
 * @Author:chenxia<PERSON>i
 * @Date:2024/02/29 10:48
 */
@Component(FddCallbackHandlerCode.DEFAULT)
public class DefaultFddCallbackHandler extends AbstractFddCallbackHandler {

    @Override
    public CommonResult<Object> handle(FddCallbackEvent eventEnum, String appId, String bizContent) {
        // 记录相关回调日志
//        insertLogAsync(eventEnum, null, bizContent);

        return CommonResult.success("记录log成功");
    }

}
