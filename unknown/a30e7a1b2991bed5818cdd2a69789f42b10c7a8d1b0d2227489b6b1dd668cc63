package com.xyy.saas.inquiry.im.server.mq.consumer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.enums.inquiry.TranscodingStatusEnum;
import com.xyy.saas.inquiry.im.server.dal.dataobject.tencent.out.TencentTrtcBaseRespDO;
import com.xyy.saas.inquiry.im.server.dal.dataobject.trtc.InquiryTranscodingDO;
import com.xyy.saas.inquiry.im.server.mq.message.TranscodingTaskEndEvent;
import com.xyy.saas.inquiry.im.server.mq.message.dto.TranscodingTaskMessage;
import com.xyy.saas.inquiry.im.server.service.tencent.TencentTrtcClient;
import com.xyy.saas.inquiry.im.server.service.trtc.InquiryTranscodingService;
import com.xyy.saas.inquiry.patient.api.inquiry.InquiryApi;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/12/15 17:29
 * @Description: 转码任务结束消息消费，进行转存储
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_im_server_mq_consumer_TranscodingTaskEndConsumer",
    topic = TranscodingTaskEndEvent.TOPIC)
public class TranscodingTaskEndConsumer {

    @Resource
    private InquiryApi inquiryApi;

    @Resource
    private TencentTrtcClient tencentTrtcClient;

    @Resource
    private InquiryTranscodingService inquiryTranscodingService;

    @EventBusListener
    public void onMessage(TranscodingTaskEndEvent event) {
        TranscodingTaskMessage msg = event.getMsg();
        //获取问诊单信息
        InquiryRecordDto inquiryDto = inquiryApi.getInquiryRecord(msg.getInquiryPref());
        //查询当前转码任务
        InquiryTranscodingDO transcodingDO = inquiryTranscodingService.queryByInquiryPref(msg.getInquiryPref());
        //根据taskId查询转码结果，得到存储地址
        TencentTrtcBaseRespDO trtcBaseRespDO = tencentTrtcClient.getTranscodingResult(msg.getTaskId());
        if(StringUtils.isBlank(trtcBaseRespDO.getMp4Url())){
            inquiryTranscodingService.updateTranscodingRecord(transcodingDO, trtcBaseRespDO, TranscodingStatusEnum.TRANSCODING_RESULT_NOT_FOUND);
        }
        //更新问诊单视频url
        inquiryApi.updateInquiry(InquiryRecordDto.builder().mp4Url(trtcBaseRespDO.getMp4Url()).id(inquiryDto.getId()).build());
        //删除转码任务记录
        inquiryTranscodingService.deleteByInquiryPref(msg.getInquiryPref());
    }
}
