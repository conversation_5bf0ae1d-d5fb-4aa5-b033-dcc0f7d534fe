package com.xyy.saas.inquiry.hospital.server.api.dept;

import com.xyy.saas.inquiry.hospital.api.dept.InquiryDeptApi;
import com.xyy.saas.inquiry.hospital.api.dept.dto.InquiryDeptDto;
import com.xyy.saas.inquiry.hospital.api.dept.dto.InquiryHospitalDepartmentRelationDto;
import com.xyy.saas.inquiry.hospital.api.dept.dto.InquiryHospitalDeptQueryReqDto;
import com.xyy.saas.inquiry.hospital.server.controller.admin.hospital.vo.InquiryHospitalDepartmentRelationPageReqVO;
import com.xyy.saas.inquiry.hospital.server.convert.hospital.InquiryHospitalDepartmentConvert;
import com.xyy.saas.inquiry.hospital.server.convert.hospital.InquiryHospitalDepartmentRelationConvert;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDepartmentDO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.hospital.InquiryHospitalDepartmentRelationDO;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalDepartmentService;
import com.xyy.saas.inquiry.hospital.server.service.hospital.InquiryHospitalDetpDoctorRelationService;
import jakarta.annotation.Resource;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;
import java.util.List;

/**
 * @Author: xucao
 * @Date: 2024/12/24 10:26
 * @Description: 科室api实现层
 */
@Service
public class InquiryDeptApiImpl implements InquiryDeptApi {

    @Resource
    private InquiryHospitalDepartmentService inquiryHospitalDepartmentService;

    @Resource
    private InquiryHospitalDetpDoctorRelationService inquiryHospitalDetpDoctorRelationService;

    /**
     * 根据科室id查询科室信息
     *
     * @param id 科室id
     * @return 科室信息
     */
    @Override
    public InquiryDeptDto getInquiryDeptById(Long id) {
        InquiryHospitalDepartmentDO dept = inquiryHospitalDepartmentService.getInquiryHospitalDepartment(id);
        return InquiryHospitalDepartmentConvert.INSTANCE.convetDO2DTO(dept);
    }

    @Override
    public List<InquiryDeptDto> listInquiryDeptByPref(List<String> prefList) {
        if (CollectionUtils.isEmpty(prefList)) {
            return List.of();
        }
        return inquiryHospitalDepartmentService.getDeptPrefMap(prefList)
            .values().stream().map(InquiryHospitalDepartmentConvert.INSTANCE::convetDO2DTO).toList();
    }

    /**
     * 查询医院科室列表
     * @param queryDto 查询参数
     * @return 医院科室列表
     */
    @Override
    public List<InquiryHospitalDepartmentRelationDto> getHospitalDeptList(InquiryHospitalDeptQueryReqDto queryDto) {
        List<InquiryHospitalDepartmentRelationDO> deptList = inquiryHospitalDetpDoctorRelationService.selectHospitalDeptmentList(InquiryHospitalDepartmentRelationPageReqVO.builder()
            .hospitalPrefs(queryDto.getHospitalPrefs()).hospitalPref(queryDto.getHospitalPref()).build());
        return InquiryHospitalDepartmentRelationConvert.INSTANCE.convertDOSTODTOS(deptList);
    }

}
