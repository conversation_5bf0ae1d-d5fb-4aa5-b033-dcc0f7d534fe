package com.xyy.saas.inquiry.signature.server.mq.message;

import com.xyy.saas.inquiry.pojo.prescription.ParticipantItem;
import java.io.Serializable;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 处方签章平台确认Message
 *
 * <AUTHOR>
 */
@Data
@Builder
@Accessors(chain = true)
public class PrescriptionSignaturePlatformRequireMessage implements Serializable {

    /**
     * 处方合同Pref
     */
    private String contractPref;

    /**
     * 当前参与方
     */
    private ParticipantItem participantItem;

    /**
     * 消息消费次数
     */
    private int consumerCount;


}
