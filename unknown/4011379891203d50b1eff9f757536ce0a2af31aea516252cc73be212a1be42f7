package com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

/**
 * 医生备案信息 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_filing")
@KeySequence("saas_inquiry_filing_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DoctorFilingDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 医生id
     */
    private Long doctorId;
    /**
     * 民族编码 1、汉族 ....
     */
    private Integer nationCode;
    /**
     * 通信地址
     */
    private String address;
    /**
     * 学历，eg:1、博士研究生 2、硕士研究生 3、本科 ...
     */
    private Integer formalLevel;
    /**
     * 机构所在省份编码，eg: 420000
     */
    private String orgProvinceCode;

    /**
     * 备案状态 1、待审核  2、审核中  3、审核通过  4、审核驳回
     */
    private Integer recordStatus;

}