package com.xyy.saas.inquiry.hospital.server.service.doctor;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorDetailRespVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.InquiryDoctorSaveReqVO;
import com.xyy.saas.inquiry.hospital.server.controller.admin.doctor.vo.forward.InquiryDoctorForwardRespVO;
import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
import com.xyy.saas.inquiry.hospital.server.service.doctor.dto.DoctorSyncQueryDto;

/**
 * @Author:chenxiaoyi
 * @Date:2025/03/03 13:40
 */
public interface DoctorSyncService {

    CommonResult<PageResult<InquiryDoctorForwardRespVO>> queryUserDoctorPhysicianLists(DoctorSyncQueryDto dto);


    CommonResult<InquiryDoctorDetailRespVO> queryUserDoctorPhysician(String guid);

    /**
     * 同步创建医生信息
     *
     * @param createReqVO
     * @return
     */
    CommonResult<InquiryDoctorDO> createInquiryDoctor(InquiryDoctorSaveReqVO createReqVO);


}
