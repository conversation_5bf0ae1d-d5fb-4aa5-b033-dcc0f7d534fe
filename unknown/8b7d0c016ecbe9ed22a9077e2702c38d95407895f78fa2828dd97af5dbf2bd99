package com.xyy.saas.transmitter.server.controller.admin.config.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 协议配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
public class TransmissionConfigItemPageReqVO extends PageParam {

    @Schema(description = "协议配置包ID列表", example = "[31380, 31381]")
    private List<Integer> configPackageIds;

    @Schema(description = "协议配置包ID", example = "31380")
    private Integer configPackageId;

    @Schema(description = "节点名称", requiredMode = Schema.RequiredMode.REQUIRED)
    private String itemName;

    @Schema(description = "父节点id", example = "16154")
    private Integer parentItemId;

    @Schema(description = "配置类型（1-视图配置、2-逻辑配置、3-协议配置）", example = "2")
    private Integer dslType;

    @Schema(description = "接口编码", example = "3501")
    private String apiCode;

    @Schema(description = "节点业务类型（比如:1-药监-日结存、2-药监-商品信息...）", example = "1")
    private Integer nodeType;

    @Schema(description = "节点业务类型列表", example = "[1,2]")
    private List<Integer> nodeTypes;

    @Schema(description = "描述", example = "随便")
    private String description;

    @Schema(description = "配置值,yaml")
    private String configValue;

    @Schema(description = "是否禁用")
    private Boolean disable;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}