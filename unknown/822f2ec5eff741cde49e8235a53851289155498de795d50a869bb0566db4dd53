package com.xyy.saas.inquiry.signature.server.service.fdd.bean;

import com.fasc.open.api.bean.base.BaseReq;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2025/03/13 10:02
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class FddBaseReqDto<T extends BaseReq> implements Serializable {

    /**
     * 法大大应用ID
     */
    private String appId;

    /**
     * 配置id 0-默认 1-东方中讯
     */
    private Integer configId;

    private T data;

    public static <T extends BaseReq> FddBaseReqDto<T> buildReq(String appId, T data) {
        return new FddBaseReqDto<T>(appId, data);
    }

    public static <T extends BaseReq> FddBaseReqDto<T> buildReq(Integer configId, T data) {
        return new FddBaseReqDto<T>(configId, data);
    }

    public FddBaseReqDto(Integer configId, T data) {
        this.configId = configId;
        this.data = data;
    }

    public FddBaseReqDto(String appId, T data) {
        this.appId = appId;
        this.data = data;
    }
}
