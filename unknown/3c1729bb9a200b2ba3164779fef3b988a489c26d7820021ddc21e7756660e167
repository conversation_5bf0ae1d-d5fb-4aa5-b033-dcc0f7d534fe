package com.xyy.saas.inquiry.product.server.controller.admin.bpm.vo;

import cn.iocoder.yudao.module.bpm.controller.admin.task.vo.instance.BpmProcessInstanceRespVO;
import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

@Schema(description = "管理后台 - 审批流关联业务 Response VO")
@Data
@ExcelIgnoreUnannotated
public class BpmBusinessRelationRespVO2 extends BpmProcessInstanceRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "26585")
    private BpmBusinessRelationRespVO businessRelation;

}