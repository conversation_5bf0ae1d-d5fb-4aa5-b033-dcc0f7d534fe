package com.xyy.saas.inquiry.pharmacist.server.service.migration.dto;

import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2025/06/05 16:09
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class MigrationEmployeeRespDto implements java.io.Serializable {


    private List<Emp> employees;

    private List<Staff> drugstoreStaffPos;

    @Data
    public static class Emp {

        /**
         * 电话
         */
        private String phone;

        /**
         * 姓名
         */
        private String nickName;
    }

    @Data
    public static class Staff {

        /**
         * 员工姓名
         */
        private String staffName;

        /**
         * 员工角色，1调配，2发药 Deployment((byte) 1, "调配"), Dispensing((byte) 2, "发药"), Pharmacists((byte) 3, "药师"), Checker((byte) 4, "核对")
         *
         * @see com.xyy.saas.remote.web.core.enums.StaffRoleEnum
         */
        private Byte staffRole;

        /**
         * 员工签名
         */
        private String staffSignImg;

        /**
         * 状态，0禁用，1启用
         */
        private Byte status;
    }
}
