package com.xyy.saas.inquiry.pharmacist.server.mq.message;

import com.xyy.saas.inquiry.enums.prescription.template.PrescriptionTemplateFieldEnum;
import com.xyy.saas.inquiry.pojo.prescription.ParticipantItem;
import java.io.Serializable;
import java.time.LocalDateTime;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 处方签章消息传递Dto
 *
 * <AUTHOR>
 */
@Builder
@Data
@Accessors(chain = true)
public class SignaturePassingMessageDTO implements Serializable {

    /**
     * 业务ID - 处方号
     */
    private String bizId;

    /**
     * 审核记录表ID
     */
    private Long auditRecordId;

    /**
     * 总级数
     */
    private Integer totalLevel;

    /**
     * 当前参与方
     */
    private ParticipantItem participantItem;

    /**
     * 下一级节点字段 {@link PrescriptionTemplateFieldEnum}
     */
    private String nextActorField;

    /**
     * 处方图片url
     */
    private String imgUrl;

    /**
     * 处方pdfUrl
     */
    private String pdfUrl;

    /**
     * 回调时间
     */
    private LocalDateTime callBackTime;


}
