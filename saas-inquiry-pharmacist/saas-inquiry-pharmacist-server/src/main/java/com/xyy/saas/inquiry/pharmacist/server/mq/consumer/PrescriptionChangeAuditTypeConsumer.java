package com.xyy.saas.inquiry.pharmacist.server.mq.consumer;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantParamConfigApi;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantParamConfigDTO;
import com.xyy.saas.inquiry.drugstore.mq.message.TenantParamConfigUpdateEvent;
import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionAuditTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionStatusEnum;
import com.xyy.saas.inquiry.enums.signature.SignatureSealValueTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionApi;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionFlushQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.pharmacist.server.constant.PrescriptionAuditConstant;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.PrescriptionOfflineAuditEvent;
import com.xyy.saas.inquiry.pharmacist.server.mq.producer.PrescriptionOfflineAuditProducer;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * 处方审核类型变更MQ
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/03 16:29
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_pharmacist_server_mq_consumer_PrescriptionChangeAuditTypeConsumer",
    topic = TenantParamConfigUpdateEvent.TOPIC)
public class PrescriptionChangeAuditTypeConsumer {


    @DubboReference
    private TenantParamConfigApi tenantParamConfigApi;

    @DubboReference
    private InquiryPrescriptionApi inquiryPrescriptionApi;

    @Resource
    private PrescriptionOfflineAuditProducer prescriptionOfflineAuditProducer;


    @Resource
    private ConfigApi configApi;


    public Long getOfflineDelayTime() {
        return NumberUtils.toLong(configApi.getConfigValueByKey(PrescriptionAuditConstant.PRESCRIPTION_CHANGE_OFFLINE_DELAY_TIME), 5);
    }

    public Integer getChangeAuditTypePageSize() {
        return NumberUtils.toInt(configApi.getConfigValueByKey(PrescriptionAuditConstant.PRESCRIPTION_CHANGE_OFFLINE_PAGE_SIZE), 20);
    }

    @EventBusListener
    public void prescriptionChangeAuditTypeConsumer(TenantParamConfigUpdateEvent changeEvent) {

        TenantParamConfigDTO changeEventMsg = changeEvent.getMsg();
        // 仅处理审方类型变更
        if (!Objects.equals(changeEventMsg.getParamType(), TenantParamConfigTypeEnum.INQUIRY_PRES_AUDIT_TYPE.getType())) {
            return;
        }

        TenantParamConfigDTO tenantParamConfigDTO = tenantParamConfigApi.queryTenantParamConfig(changeEventMsg.getTenantId(), TenantParamConfigTypeEnum.fromType(changeEventMsg.getParamType()));

        if (tenantParamConfigDTO == null) {
            log.warn("[prescriptionChangeAuditTypeConsumer][参数配置不存在] tenantId:{} paramType:{}", changeEventMsg.getTenantId(), changeEventMsg.getParamType());
            return;
        }
        //  检查是否从非线下状态变更为线下状态
        boolean changedToOffline =
            !StringUtils.equals(changeEventMsg.getParamValue(), PrescriptionAuditTypeEnum.OFFLINE.getStatusCode() + "")
                && StringUtils.equals(tenantParamConfigDTO.getParamValue(), PrescriptionAuditTypeEnum.OFFLINE.getStatusCode() + "");

        if (!changedToOffline) {
            log.info("[prescriptionChangeAuditTypeConsumer] 审方类型不满足线下审方 跳过----");
            return;
        }

        // 旧的不是线下，新的是线下 刷历史处方走线下审方 where id > ? and  tenant_id =  ? and out_prescription_time < ? and  inquiry_biz_type = 1   and status = 2 and auditor_type in (2,3) order by id asc
        InquiryPrescriptionFlushQueryDTO flushQueryDTO = InquiryPrescriptionFlushQueryDTO.builder()
            .tenantId(tenantParamConfigDTO.getTenantId())
            .outPrescriptionTime(changeEventMsg.getOperateTime())
            .inquiryBizType(InquiryBizTypeEnum.DRUGSTORE_INQUIRY.getCode())
            .auditorTypes(List.of(AuditorTypeEnum.DRUGSTORE_PHARMACIST.getCode(), AuditorTypeEnum.PLATFORM_PHARMACIST.getCode()))
            .status(PrescriptionStatusEnum.WAIT_APPROVAL.getStatusCode()).build();
        flushQueryDTO.setPageNo(1);
        flushQueryDTO.setPageSize(getChangeAuditTypePageSize());
        log.info("[prescriptionChangeAuditTypeConsumer] 刷历史待审处方走 线下审方开始----dto:{}", JSON.toJSONString(flushQueryDTO));
        int time = 0;

        // 默认循环 1000 * 100  可处理10w处方
        for (int i = 0; i < 1000; i++) {

            PageResult<InquiryPrescriptionRespDTO> pageResult = inquiryPrescriptionApi.getFlushOfflinePrescriptionPage(flushQueryDTO);

            log.info("[prescriptionChangeAuditTypeConsumer][刷历史待审处方进度]  已处理 {} 批 , 剩余总量 {}", i + 1, pageResult.getTotal());

            List<InquiryPrescriptionRespDTO> list = pageResult.getList();
            if (CollUtil.isEmpty(list)) {
                break;
            }
            // 设置最大id
            list.stream().mapToLong(InquiryPrescriptionRespDTO::getId).max().ifPresent(flushQueryDTO::setMaxId);

            LocalDateTime now = LocalDateTime.now();

            for (InquiryPrescriptionRespDTO prescriptionRespDTO : list) {
                try {
                    if (Objects.equals(prescriptionRespDTO.getAuditorType(), AuditorTypeEnum.PLATFORM_PHARMACIST.getCode())
                        && Objects.equals(prescriptionRespDTO.getExt().getSealValueType(), SignatureSealValueTypeEnum.PLAT_PHA_SIGN.getCode())) {
                        continue;
                    }
                    // 每条数据延迟5s发送线下审方MQ
                    prescriptionOfflineAuditProducer.sendMessage(PrescriptionOfflineAuditEvent.builder().msg(prescriptionRespDTO.getPref()).build(), now.plusSeconds(time * getOfflineDelayTime()));
                    time++;
                } catch (Exception e) {
                    log.error("[prescriptionChangeAuditTypeConsumer][处理处方异常] pref:{}", prescriptionRespDTO.getPref(), e);
                }
            }
        }
    }
}
