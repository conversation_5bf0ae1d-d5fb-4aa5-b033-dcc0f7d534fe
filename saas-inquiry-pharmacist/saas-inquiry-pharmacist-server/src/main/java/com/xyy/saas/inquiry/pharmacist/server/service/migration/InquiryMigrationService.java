package com.xyy.saas.inquiry.pharmacist.server.service.migration;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.migration.vo.InquiryMigrationBatchReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.migration.vo.InquiryMigrationImportReqDto;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.migration.vo.InquiryMigrationPageReqVO;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.migration.InquiryMigrationDO;
import com.xyy.saas.inquiry.pojo.excel.ImportResultDto;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 荷叶老问诊迁移 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryMigrationService {


    /**
     * 获得荷叶老问诊迁移分页
     *
     * @param pageReqVO 分页查询
     * @return 荷叶老问诊迁移分页
     */
    PageResult<InquiryMigrationDO> getInquiryMigrationPage(InquiryMigrationPageReqVO pageReqVO);


    /**
     * 批量导入
     *
     * @param importReqDto
     * @return
     */
    ImportResultDto batchImportMigration(@Valid InquiryMigrationImportReqDto importReqDto);

    /**
     * 删除荷叶老问诊迁移
     *
     * @param id 编号
     */
    void deleteInquiryMigration(List<Long> ids);


    /**
     * 作废迁移
     *
     * @param id
     */
    void invalidateInquiryMigration(Long id);


    /**
     * 荷叶老问诊迁移
     *
     * @param batchReqVO
     * @return
     */
    void batchInquiryMigration(InquiryMigrationBatchReqVO batchReqVO);

    /**
     * 重新迁移
     *
     * @param batchReqVO
     */
    void batchReInquiryMigration(InquiryMigrationBatchReqVO batchReqVO);


}