package com.xyy.saas.inquiry.pharmacist.server.mq.message.migration;

import com.xyy.saas.inquiry.enums.migration.MigrationActionEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 医院平台药师免签审核事件msg
 *
 * <AUTHOR>
 */
@Builder
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class InquiryMigrationMsgDto implements Serializable {


    private String organSign;


    private Integer migrationType;

    /**
     * 迁移类型 1立即迁移 2生成迁移计划 {@link  MigrationActionEnum}
     */
    private Integer migrationAction;

    @Schema(description = "计划开始迁移时间", example = "2")
    private LocalDateTime startTime;
}
