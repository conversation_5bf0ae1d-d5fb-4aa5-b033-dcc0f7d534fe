package com.xyy.saas.inquiry.pharmacist.server.convert.migration;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.ip.core.Area;
import cn.iocoder.yudao.framework.ip.core.enums.AreaTypeEnum;
import cn.iocoder.yudao.framework.ip.core.utils.AreaUtils;
import cn.iocoder.yudao.module.system.api.tenant.migration.dto.MigrationTrailPackageDto;
import com.xyy.saas.inquiry.enums.migration.MigrationStatusEnum;
import com.xyy.saas.inquiry.enums.migration.MigrationTypeEnum;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.migration.vo.InquiryMigrationBatchReqVO;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.migration.InquiryMigrationDO;
import com.xyy.saas.inquiry.pharmacist.server.service.migration.dto.InquiryMigrationDto;
import com.xyy.saas.inquiry.pojo.migration.MigrationDrugStoreRespDto;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.Objects;

/**
 * @Author:chenxiaoyi
 * @Date:2024/10/12 17:17
 */
@Mapper
public interface InquiryMigrationConvert {

    InquiryMigrationConvert INSTANCE = Mappers.getMapper(InquiryMigrationConvert.class);

    default InquiryMigrationDO convertInitMigrationDo(MigrationDrugStoreRespDto drugStoreRespDto) {
        InquiryMigrationDO inquiryMigrationDO = new InquiryMigrationDO();

        inquiryMigrationDO.setStatus(MigrationStatusEnum.PENDING_MIGRATION.getCode());
        inquiryMigrationDO.setMigrationType(MigrationTypeEnum.STORE.getCode());
        inquiryMigrationDO.setOrganSign(drugStoreRespDto.getOrganSign());
        inquiryMigrationDO.setName(drugStoreRespDto.getDrugstoreName());

        // 转换省市区
        inquiryMigrationDO.setProvince(drugStoreRespDto.getProvince());
        inquiryMigrationDO.setCity(drugStoreRespDto.getCity());
        inquiryMigrationDO.setArea(drugStoreRespDto.getArea());

        if (StringUtils.isNotBlank(drugStoreRespDto.getProvince())) {
            Area p1 = AreaUtils.getByTypeName(AreaTypeEnum.PROVINCE, drugStoreRespDto.getProvince());
            if (p1 != null) {
                inquiryMigrationDO.setProvinceCode(p1.getId().toString());

                if (StringUtils.isNotBlank(drugStoreRespDto.getCity())) {
                    p1.getChildren().stream().filter(p2 -> StringUtils.equalsIgnoreCase(p2.getName(), drugStoreRespDto.getCity()) && Objects.equals(p2.getType(), AreaTypeEnum.CITY.getType())).findFirst().ifPresent(p2 -> {
                        inquiryMigrationDO.setCityCode(p2.getId().toString());

                        if (StringUtils.isNotBlank(drugStoreRespDto.getArea())) {
                            p2.getChildren().stream().filter(p3 -> StringUtils.equalsIgnoreCase(p3.getName(), drugStoreRespDto.getArea()) && Objects.equals(p3.getType(), AreaTypeEnum.DISTRICT.getType())).findFirst().ifPresent(p3 -> {
                                inquiryMigrationDO.setAreaCode(p3.getId().toString());
                            });
                        }
                    });
                }
            }
        }

        return inquiryMigrationDO;
    }

    default InquiryMigrationDO convertReMigrationDo(Long id, InquiryMigrationBatchReqVO batchReqVO) {
        InquiryMigrationDO inquiryMigrationDO = new InquiryMigrationDO();
        inquiryMigrationDO.setId(id);
        inquiryMigrationDO.setStatus(MigrationStatusEnum.PENDING_MIGRATION.getCode());
        inquiryMigrationDO.setMigrationType(batchReqVO.getMigrationType());
        inquiryMigrationDO.setStartTime(batchReqVO.getStartTime());
        inquiryMigrationDO.setForceOverride(CommonStatusEnum.ENABLE.getStatus());
        inquiryMigrationDO.setRemark("");
        return inquiryMigrationDO;
    }

    InquiryMigrationDto convertDto(InquiryMigrationDO inquiryMigrationDO);

    MigrationTrailPackageDto convertTrailPackage(InquiryMigrationDto migrationDto, Integer packageTrialDay);
}
