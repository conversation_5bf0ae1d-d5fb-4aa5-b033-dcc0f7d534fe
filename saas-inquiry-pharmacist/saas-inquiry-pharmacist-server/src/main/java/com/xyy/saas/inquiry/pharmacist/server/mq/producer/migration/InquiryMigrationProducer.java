package com.xyy.saas.inquiry.pharmacist.server.mq.producer.migration;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.migration.InquiryMigrationEvent;
import org.springframework.stereotype.Component;

/**
 * @Desc 问诊迁移 producer
 * <AUTHOR>
 */
@Component
@EventBusProducer(
    topic = InquiryMigrationEvent.TOPIC
)
public class InquiryMigrationProducer extends EventBusRocketMQTemplate {


}
