package com.xyy.saas.inquiry.pharmacist.server.convert.pharmacist;

import com.xyy.saas.inquiry.enums.im.ImEventPushEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.im.api.message.dto.ImEventMessageExtDto;
import com.xyy.saas.inquiry.im.api.message.dto.InquiryImMessageDto;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryPharmacistDO;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.dto.PharmacistReceiverImUserDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
 * @Author: xucao
 * @DateTime: 2025/4/1 14:07
 * @Description: 药师IM转换
 **/
@Mapper
public interface InquiryPharmacistImConvert {

    InquiryPharmacistImConvert INSTANCE = Mappers.getMapper(InquiryPharmacistImConvert.class);

    default InquiryImMessageDto convertPrescriptionAuditMsgToPatient(String patientImAccount, ImEventPushEnum eventEnum, String inquiryPref) {
        return InquiryImMessageDto.builder().toAccount(patientImAccount).msg(eventEnum.getDesc()).extDto(ImEventMessageExtDto.builder().eventType(eventEnum.getCode()).eventInfo(
            ImEventMessageExtDto.EventInfo.builder().inquiryPref(inquiryPref).build()
        ).build()).build();
    }

    default InquiryImMessageDto convertPrescriptionReceive(String patientImAccount, InquiryPharmacistDO pharmacist, ImEventPushEnum eventEnum, String inquiryPref) {
        return InquiryImMessageDto.builder().toAccount(patientImAccount).msg(eventEnum.getDesc()).extDto(ImEventMessageExtDto.builder().eventType(eventEnum.getCode()).eventInfo(
            ImEventMessageExtDto.EventInfo.builder().inquiryPref(inquiryPref).pharmacistName(pharmacist.getName()).build()
        ).build()).build();
    }

    default InquiryImMessageDto convertDefaultMsg(PharmacistReceiverImUserDto receiverImUserDto, Integer actionType, InquiryPrescriptionRespDTO prescription, ImEventPushEnum eventEnum) {
        return InquiryImMessageDto.builder().toAccount(receiverImUserDto.getImAccount()).msg(eventEnum.getDesc()).extDto(ImEventMessageExtDto.builder().eventType(eventEnum.getCode()).eventInfo(
            ImEventMessageExtDto.EventInfo.builder()
                .prescriptionPref(prescription.getPref())
                .inquiryPref(prescription.getInquiryPref())
                .startActionType(actionType)
                .pharmacistName(receiverImUserDto.getPharmacistName())
                .patientName(receiverImUserDto.getPatientName())
                .build()
        ).build()).build();
    }

    default InquiryImMessageDto convertHandMsg(PharmacistReceiverImUserDto receiverImUserDto, Integer handleResult, InquiryPrescriptionRespDTO prescription, ImEventPushEnum eventEnum) {
        return InquiryImMessageDto.builder().toAccount(receiverImUserDto.getImAccount()).msg(eventEnum.getDesc()).extDto(ImEventMessageExtDto.builder().eventType(eventEnum.getCode()).eventInfo(
            ImEventMessageExtDto.EventInfo.builder()
                .prescriptionPref(prescription.getPref())
                .inquiryPref(prescription.getInquiryPref())
                .videoHandleStatus(handleResult)
                .pharmacistName(receiverImUserDto.getPharmacistName())
                .patientName(receiverImUserDto.getPatientName())
                .build()
        ).build()).build();
    }

    default InquiryImMessageDto convertPrescriptionPushWaitAuditMsgToPharmacist(List<String> imAccountList, ImEventPushEnum eventEnum, String prescriptionPref) {
        return InquiryImMessageDto.builder().toAccountList(imAccountList).msg(eventEnum.getDesc()).extDto(ImEventMessageExtDto.builder().eventType(eventEnum.getCode()).eventInfo(
            ImEventMessageExtDto.EventInfo.builder().prescriptionPref(prescriptionPref).build()
        ).build()).build();
    }
}
