package com.xyy.saas.inquiry.pharmacist.server.job;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.baomidou.lock.annotation.Lock4j;
import com.xyy.saas.inquiry.enums.doctor.OnlineStatusEnum;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistPageReqVO;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryPharmacistDO;
import com.xyy.saas.inquiry.pharmacist.server.dal.mysql.pharmacist.InquiryPharmacistMapper;
import com.xyy.saas.inquiry.pharmacist.server.dal.redis.RedisKeyConstants;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

@Component
@Slf4j
public class PharmacistOfflineJobService {


    @Resource
    private InquiryPharmacistMapper inquiryPharmacistMapper;

    private static final int PAGE_SIZE = 100;

    /**
     *
     */
    @Lock4j(keys = "'" + RedisKeyConstants.PHARMACIST_JOB_OFFLINE_LOCK_KEY + "'")
    public void jobHandPharmacistOffline() {

        InquiryPharmacistPageReqVO reqVO = new InquiryPharmacistPageReqVO();
        reqVO.setOnlineStatus(OnlineStatusEnum.ONLINE.getCode());
        reqVO.setPageSize(PAGE_SIZE);
        reqVO.setMaxId(Long.MIN_VALUE);

        int processedPages = 0;
        while (processedPages < 10000) {

            PageResult<InquiryPharmacistDO> pageResult = inquiryPharmacistMapper.selectPage(reqVO);
            log.info("药师定时下线任务，处理第 {} 页,每页:{}条,一共{}条", processedPages + 1, PAGE_SIZE, pageResult.getTotal());
            List<InquiryPharmacistDO> pharmacistList = pageResult.getList();

            if (CollUtil.isEmpty(pharmacistList)) {
                break; // 无更多数据，终止循环
            }

            List<Long> ids = pharmacistList.stream()
                .map(InquiryPharmacistDO::getId)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
            if (CollUtil.isEmpty(ids)) {
                break;
            }
            // 更新游标为当前页最大ID
            Long maxId = pharmacistList.stream()
                .mapToLong(InquiryPharmacistDO::getId)
                .max()
                .orElse(0L);
            reqVO.setMaxId(maxId);

            processedPages++;

            // 批量下线药师
            inquiryPharmacistMapper.offline(ids);

            // 处理其他打卡相关?

        }
    }

}
