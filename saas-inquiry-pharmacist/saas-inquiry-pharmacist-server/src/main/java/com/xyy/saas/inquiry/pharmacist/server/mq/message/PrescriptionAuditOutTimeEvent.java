package com.xyy.saas.inquiry.pharmacist.server.mq.message;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 处方审核超时事件
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class PrescriptionAuditOutTimeEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "PRESCRIPTION_AUDIT_OUT_TIME";

    private PrescriptionAuditOutTimeMessageDTO msg;


    @JsonCreator
    public PrescriptionAuditOutTimeEvent(@JsonProperty("msg") PrescriptionAuditOutTimeMessageDTO msg) {
        this.msg = msg;
    }


    @Override
    public String getTag() {
        return "";
    }

}
