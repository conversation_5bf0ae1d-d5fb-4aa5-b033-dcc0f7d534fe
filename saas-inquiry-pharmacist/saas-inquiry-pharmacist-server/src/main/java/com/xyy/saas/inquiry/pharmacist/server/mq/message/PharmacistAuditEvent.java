package com.xyy.saas.inquiry.pharmacist.server.mq.message;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 门店药师延迟审核事件
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class PharmacistAuditEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "PHARMACIST_AUDIT";

    private PharmacistAuditMessageDTO msg;


    @JsonCreator
    public PharmacistAuditEvent(@JsonProperty("msg") PharmacistAuditMessageDTO msg) {
        this.msg = msg;
    }


    @Override
    public String getTag() {
        return "";
    }

}
