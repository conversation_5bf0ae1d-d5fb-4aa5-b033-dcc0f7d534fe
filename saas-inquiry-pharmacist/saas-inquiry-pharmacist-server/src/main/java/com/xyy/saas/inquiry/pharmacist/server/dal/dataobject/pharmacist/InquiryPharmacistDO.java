package com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.enums.doctor.AuditStatusEnum;
import com.xyy.saas.inquiry.enums.doctor.DoctorJobTypeEnum;
import com.xyy.saas.inquiry.enums.doctor.DrawnSignEnum;
import com.xyy.saas.inquiry.enums.doctor.OnlineStatusEnum;
import com.xyy.saas.inquiry.enums.doctor.PharmacistTypeEnum;
import com.xyy.saas.inquiry.pojo.parmacist.PharmacistExtDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 药师信息 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_inquiry_pharmacist", autoResultMap = true)
@KeySequence("saas_inquiry_pharmacist_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class InquiryPharmacistDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 药师编码
     */
    private String pref;

    /**
     * 姓名
     */
    private String name;
    /**
     * 性别 1男 2女
     */
    private Integer sex;
    /**
     * 身份证号码
     */
    private String idCard;
    /**
     * 联系电话
     */
    private String mobile;
    /**
     * 审核状态 0、待审核  1、审核通过  2、审核驳回 {@link AuditStatusEnum}
     */
    private Integer auditStatus;

    /**
     * 证件照地址
     */
    private String photo;
    /**
     * 个人简介
     */
    private String biography;
    /**
     * 药师执业资格,中药或西药
     */
    private Integer qualification;
    /**
     * 药师类型 平台药师 / 门店药师 / 医院药师 {@link PharmacistTypeEnum}
     */
    private Integer pharmacistType;

    /**
     * 药师性质 {@link com.xyy.saas.inquiry.enums.doctor.PharmacistNatureEnum}
     */
    // @TableField(updateStrategy = FieldStrategy.ALWAYS)
    private Integer pharmacistNature;
    /**
     * 毕业学校
     */
    private String school;

    /**
     * 民族
     */
    private Integer nationCode;

    /**
     * 学历
     */
    private Integer formalLevel;

    /**
     * 通信地址
     */
    private String address;

    /**
     * 在线状态：0闭诊 1出诊 {@link OnlineStatusEnum}
     */
    private Integer onlineStatus;

    /**
     * 是否手绘签名(不走认证自己绘制)：0否  1是 {@link DrawnSignEnum}
     */
    private Integer drawnSign;

    /**
     * 执业省份
     */
    private String provinceCode;

    /**
     * 药师工作类型 1全职/ 2兼职
     * <p>
     * {@link DoctorJobTypeEnum}
     */
    private Integer jobType;

    /**
     * 指纹
     */
    private String fingerPrint;

    /**
     * 备注
     */
    private String remark;

    /**
     * 环境标志：prod-真实数据；test-测试数据；show-线上演示数据
     */
    private String envTag;


    /**
     * PC是否需要指纹审核 默认是 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer fingerPrintAudit;


    @TableField(typeHandler = JsonTypeHandler.class)
    private PharmacistExtDto ext;


    @JsonIgnore
    public PharmacistExtDto extGet() {
        if (ext == null) {
            ext = new PharmacistExtDto();
        }
        return ext;
    }


}