package com.xyy.saas.inquiry.pharmacist.server.service.audit.dto;

import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryPharmacistDO;
import com.xyy.saas.inquiry.pojo.BaseDto;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author:chen<PERSON><PERSON>i
 * @Date:2024/12/03 19:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class InquiryPharmacistReceiveDTO extends BaseDto {

    private String pref;

    private InquiryPharmacistDO pharmacist;

}
