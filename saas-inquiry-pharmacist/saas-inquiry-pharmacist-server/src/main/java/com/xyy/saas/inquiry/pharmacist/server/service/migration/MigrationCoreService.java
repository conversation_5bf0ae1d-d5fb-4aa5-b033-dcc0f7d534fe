package com.xyy.saas.inquiry.pharmacist.server.service.migration;

import java.time.LocalDateTime;

/**
 * 荷叶老问诊迁移 Service 接口
 *
 * <AUTHOR>
 */
public interface MigrationCoreService {

    /**
     * 迁移
     *
     * @param organSign 机构号
     */
    void migration(String organSign);


    /**
     * 创建套餐迁移计划 先修改老系统状态，发延迟MQ
     *
     * @param organSign
     */
    void migrationPackagePlan(String organSign, LocalDateTime endTime);

}