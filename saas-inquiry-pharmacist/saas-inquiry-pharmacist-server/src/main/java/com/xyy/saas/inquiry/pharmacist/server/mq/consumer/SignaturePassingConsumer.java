package com.xyy.saas.inquiry.pharmacist.server.mq.consumer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.pharmacist.server.service.signature.InquiryPharmacistSignaturePassingService;
import com.xyy.saas.inquiry.signature.mq.SignaturePassingEvent;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * {@link com.xyy.saas.inquiry.signature.server.service.prescription.InquirySignaturePrescriptionServiceImpl#signaturePrescriptionCallback}
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/03 16:29
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_pharmacist_server_mq_consumer_SignaturePassingConsumer",
    topic = SignaturePassingEvent.TOPIC)
public class SignaturePassingConsumer {

    @Resource
    private InquiryPharmacistSignaturePassingService pharmacistSignaturePassingService;

    @EventBusListener
    public void signaturePassingConsumer(SignaturePassingEvent signaturePassingEvent) {
        pharmacistSignaturePassingService.prescriptionSignaturePassing(signaturePassingEvent.getMsg());
    }


}
