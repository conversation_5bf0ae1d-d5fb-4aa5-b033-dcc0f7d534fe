package com.xyy.saas.inquiry.pharmacist.server.mq.consumer;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantParamConfigApi;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantParamConfigDTO;
import com.xyy.saas.inquiry.drugstore.mq.message.TenantParamConfigUpdateEvent;
import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionStatusEnum;
import com.xyy.saas.inquiry.enums.prescription.PrescriptionRemoteAuditTypeEnum;
import com.xyy.saas.inquiry.enums.system.EnvTagEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantTypeEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionApi;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionFlushQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionUpdateDTO;
import com.xyy.saas.inquiry.pharmacist.server.constant.PrescriptionAuditConstant;
import com.xyy.saas.inquiry.pharmacist.server.dal.redis.audit.PharmacistAuditRedisService;
import com.xyy.saas.inquiry.pojo.TenantDto;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 处方远程审核类型变更MQ
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/03 16:29
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_pharmacist_server_mq_consumer_PrescriptionChangeRemoteAuditTypeConsumer",
    topic = TenantParamConfigUpdateEvent.TOPIC)
public class PrescriptionChangeRemoteAuditTypeConsumer {


    @Autowired
    private TenantApi tenantApi;

    @DubboReference
    private TenantParamConfigApi tenantParamConfigApi;

    @DubboReference
    private InquiryPrescriptionApi inquiryPrescriptionApi;

    @Resource
    protected PharmacistAuditRedisService pharmacistAuditRedisService;

    @Resource
    private ConfigApi configApi;

    public Long getRemoteChangeDelayTime() {
        return NumberUtils.toLong(configApi.getConfigValueByKey(PrescriptionAuditConstant.PRESCRIPTION_CHANGE_REMOTE_AUDIT_DELAY_TIME), 200);
    }


    @EventBusListener
    public void prescriptionChangeRemoteAuditTypeConsumer(TenantParamConfigUpdateEvent changeEvent) {

        TenantParamConfigDTO changeEventMsg = changeEvent.getMsg();
        // 仅处理远程审方类型变更
        if (!Objects.equals(changeEventMsg.getParamType(), TenantParamConfigTypeEnum.PRESCRIPTION_REMOTE_AUDIT_TYPE.getType())) {
            return;
        }

        Long tenantId = changeEventMsg.getTenantId();
        TenantParamConfigDTO tenantParamConfigDTO = tenantParamConfigApi.queryTenantParamConfig(tenantId, TenantParamConfigTypeEnum.fromType(changeEventMsg.getParamType()));

        if (tenantParamConfigDTO == null) {
            log.warn("[prescriptionChangeRemoteAuditTypeConsumer][参数配置不存在] tenantId:{} paramType:{}", tenantId, changeEventMsg.getParamType());
            return;
        }
        TenantDto tenantDto = tenantApi.getTenant(tenantId);
        //  旧的是总部- 新的是门店
        boolean head2Store =
            StringUtils.equals(changeEventMsg.getParamValue(), PrescriptionRemoteAuditTypeEnum.HEAD_AUDIT.getCode() + "")
                && StringUtils.equals(tenantParamConfigDTO.getParamValue(), PrescriptionRemoteAuditTypeEnum.STORE_AUDIT.getCode() + "");
        //  旧的是门店- 新的是总部
        boolean store2Head =
            StringUtils.equals(changeEventMsg.getParamValue(), PrescriptionRemoteAuditTypeEnum.STORE_AUDIT.getCode() + "")
                && StringUtils.equals(tenantParamConfigDTO.getParamValue(), PrescriptionRemoteAuditTypeEnum.HEAD_AUDIT.getCode() + "");

        if (store2Head) {
            if (!Objects.equals(TenantTypeEnum.CHAIN_STORE, tenantDto.getWzTenantType()) || tenantDto.getHeadTenantId() == null) {
                log.warn("[prescriptionChangeRemoteAuditTypeConsumer][远程审方门店切总部,信息异常] tenant:{} ", tenantDto.getPref());
                return;
            }
        }

        if (head2Store || store2Head) {
            log.info("[prescriptionChangeRemoteAuditTypeConsumer] 远程审方类型 总部 - 门店切换,tenant:{},head2Store:{},store2Head:{}", tenantDto.getPref(), head2Store, store2Head);

            // 门店远程审方类型切换  where id > ? and  tenant_id =  ? and out_prescription_time < ? and  inquiry_biz_type = 2   and status = 2 and auditor_type in (2) order by id asc
            InquiryPrescriptionFlushQueryDTO flushQueryDTO = InquiryPrescriptionFlushQueryDTO.builder()
                .tenantId(tenantParamConfigDTO.getTenantId())
                .outPrescriptionTime(changeEventMsg.getOperateTime())
                .inquiryBizType(InquiryBizTypeEnum.REMOTE_INQUIRY.getCode())
                .auditorTypes(List.of(AuditorTypeEnum.DRUGSTORE_PHARMACIST.getCode()))
                .status(PrescriptionStatusEnum.WAIT_APPROVAL.getStatusCode()).build();
            flushQueryDTO.setPageNo(1);
            flushQueryDTO.setPageSize(50);
            log.info("[prescriptionChangeRemoteAuditTypeConsumer] 刷历史远程审方类型待审处方走 ---dto:{}", JSON.toJSONString(flushQueryDTO));

            // 默认循环 1000 * 50  可处理10w处方
            for (int i = 0; i < 1000; i++) {

                PageResult<InquiryPrescriptionRespDTO> pageResult = inquiryPrescriptionApi.getFlushOfflinePrescriptionPage(flushQueryDTO);
                log.info("[prescriptionChangeRemoteAuditTypeConsumer][刷历史远程审方类型进度]  已处理 {} 批 , 剩余总量 {}", i + 1, pageResult.getTotal());

                List<InquiryPrescriptionRespDTO> list = pageResult.getList();
                if (CollUtil.isEmpty(list)) {
                    break;
                }
                // 设置最大id
                list.stream().mapToLong(InquiryPrescriptionRespDTO::getId).max().ifPresent(flushQueryDTO::setMaxId);

                for (InquiryPrescriptionRespDTO ppDto : list) {
                    try {
                        handleWaitReviewPool(ppDto, head2Store, tenantDto, store2Head);
                        TimeUnit.MILLISECONDS.sleep(getRemoteChangeDelayTime());
                    } catch (Exception e) {
                        log.error("[prescriptionChangeRemoteAuditTypeConsumer]刷历史远程审方类型异常] tenant:{} , pref:{}", tenantDto.getPref(), ppDto.getPref(), e);
                    }
                }
            }
        }
    }

    private void handleWaitReviewPool(InquiryPrescriptionRespDTO ppDto, boolean head2Store, TenantDto tenantDto, boolean store2Head) {
        if (head2Store) {
            log.info("[prescriptionChangeRemoteAuditTypeConsumer][远程审方类型 - 总部切门店] tenant:{} , pref:{} ", tenantDto.getPref(), ppDto.getPref());
            // 移除总部远程审方池
            pharmacistAuditRedisService.prescriptionWaitingReviewChainPoolRemove(EnvTagEnum.getByEnv(tenantDto.getEnvTag()), MedicineTypeEnum.fromCode(ppDto.getMedicineType()),
                Optional.ofNullable(ppDto.getExt().getHeadTenantId()).orElse(tenantDto.getHeadTenantId()),
                ppDto.getPref());
            // 推入门店药师审方池
            pharmacistAuditRedisService.prescriptionWaitingReviewDrugstorePoolPush(EnvTagEnum.getByEnv(tenantDto.getEnvTag()), MedicineTypeEnum.fromCode(ppDto.getMedicineType()), ppDto.getTenantId(), ppDto.getPref(),
                ppDto.getOutPrescriptionTime());
        }

        if (store2Head) {
            // 设置处方ext-headTenantId
            inquiryPrescriptionApi.updateInquiryPrescription(InquiryPrescriptionUpdateDTO.builder().id(ppDto.getId()).ext(ppDto.getExt().setHeadTenantId(tenantDto.getHeadTenantId())).build());

            log.info("[prescriptionChangeRemoteAuditTypeConsumer][远程审方类型 - 门店切总部] tenant:{} , pref:{} ", tenantDto.getPref(), ppDto.getPref());
            // 移除门店药师审方池
            pharmacistAuditRedisService.prescriptionWaitingReviewDrugstorePoolRemove(EnvTagEnum.getByEnv(tenantDto.getEnvTag()), MedicineTypeEnum.fromCode(ppDto.getMedicineType()), ppDto.getTenantId(), ppDto.getPref());
            // 推入连锁总部审方池
            pharmacistAuditRedisService.prescriptionWaitingReviewChainPoolPush(EnvTagEnum.getByEnv(tenantDto.getEnvTag()), MedicineTypeEnum.fromCode(ppDto.getMedicineType()), tenantDto.getHeadTenantId(),
                ppDto.getPref(),
                ppDto.getOutPrescriptionTime());
        }
    }
}
