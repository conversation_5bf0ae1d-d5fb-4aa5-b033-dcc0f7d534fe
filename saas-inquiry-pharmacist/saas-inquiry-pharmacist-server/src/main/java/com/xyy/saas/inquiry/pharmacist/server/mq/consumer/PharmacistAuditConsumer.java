package com.xyy.saas.inquiry.pharmacist.server.mq.consumer;

import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.enums.doctor.AuditStatusEnum;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistUpdateStatusReqVO;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryPharmacistDO;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.PharmacistAuditEvent;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.InquiryPharmacistService;
import jakarta.annotation.Resource;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 门店药师延迟审核Consumer
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/03 16:29
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_pharmacist_server_mq_consumer_PharmacistAuditConsumer",
    topic = PharmacistAuditEvent.TOPIC)
public class PharmacistAuditConsumer {


    @Resource
    private InquiryPharmacistService inquiryPharmacistService;

    @EventBusListener
    public void pharmacistAuditConsumer(PharmacistAuditEvent auditOutTimeEvent) {
        try {
            InquiryPharmacistDO pharmacist = inquiryPharmacistService.getPharmacistByPref(auditOutTimeEvent.getMsg().getPref());
            if (!Objects.equals(pharmacist.getAuditStatus(), AuditStatusEnum.PENDING.getCode())) {
                log.info("pharmacistAuditConsumer药师延迟自动审核MQ-当前状态不为待审核,pref:{},status:{}", auditOutTimeEvent.getMsg(), pharmacist.getAuditStatus());
                return;
            }
            TenantUtils.execute(auditOutTimeEvent.getMsg().getTenantId(),
                () -> inquiryPharmacistService.auditInquiryPharmacist(new InquiryPharmacistUpdateStatusReqVO().setId(pharmacist.getId()).setAuditStatus(AuditStatusEnum.APPROVED.getCode())));
        } catch (Exception e) {
            log.error("pharmacistAuditConsumer药师延迟自动审核MQ异常,pref:{},msg:{}", auditOutTimeEvent.getMsg(), e.getMessage(), e);
        }
    }


}
