package com.xyy.saas.inquiry.pharmacist.server.service.signature.strategy;

import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantPackageCostApi;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantParamConfigApi;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantPackageCostDto;
import com.xyy.saas.inquiry.enums.inquiry.InquiryAuditTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionAuditTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.CostRecordTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum;
import com.xyy.saas.inquiry.mq.prescription.PrescriptionPharmacistCompletedEvent;
import com.xyy.saas.inquiry.mq.prescription.PrescriptionPharmacistCompletedProducer;
import com.xyy.saas.inquiry.mq.prescription.dto.PrescriptionMqCommonMessage;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.dto.InquiryPharmacistPrescriptionDTO;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.strategy.PrescriptionAuditStrategy;
import com.xyy.saas.inquiry.pharmacist.server.service.signature.dto.SignaturePassingHandleDto;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 普通药师签章
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/03 19:30
 */
@Component("pharmacistSign")
@Slf4j
public class PharmacistSignaturePassingStrategy extends AbstractInquirySignaturePassingStrategy {

    @DubboReference
    private TenantParamConfigApi tenantParamConfigApi;

    @DubboReference
    private TenantPackageCostApi tenantPackageCostApi;

    @Resource
    private PrescriptionPharmacistCompletedProducer prescriptionPharmacistCompletedProducer;

    private final Map<PrescriptionAuditTypeEnum, PrescriptionAuditStrategy> prescriptionAuditStrategyMap = new HashMap<>();

    @Autowired
    public void initHandler(List<PrescriptionAuditStrategy> strategies) {
        strategies.stream().filter(s -> s.getPrescriptionAuditType() != null).forEach(strategy -> prescriptionAuditStrategyMap.put(strategy.getPrescriptionAuditType(), strategy));
    }


    /**
     * 药师审方完成
     *
     * @param sphDto
     */
    @Override
    public void handleSelf(SignaturePassingHandleDto sphDto) {

        // IM通知商家处方审核
        inquiryPharmacistImService.sendPrescriptionAuditMsgToPatient(sphDto.getPrescription().getInquiryPref());

        // 发送药师审方完成事件MQ
        PrescriptionMqCommonMessage message = PrescriptionMqCommonMessage.builder()
            .prescriptionPref(sphDto.getPrescription().getPref())
            .prescriptionPdfUrl(sphDto.getPrescription().getPrescriptionPdfUrl())
            .prescriptionImgUrl(sphDto.getPrescription().getPrescriptionImgUrl())
            .auditorType(sphDto.getPrescription().getAuditorType())
            .build();
        prescriptionPharmacistCompletedProducer.sendMessage(PrescriptionPharmacistCompletedEvent.builder().msg(message).build());
    }

    /**
     * <p>处理处方推送药师审方池<p>
     * <p> 2024-12-04 16:30 luoMi+huDieLan 确认平台审方逻辑: <p>
     * <p> 1.判断门店存在有生效的平台审方套餐,如果有则推平台审方池  <p>
     * <p> 2.如果没有,判断门店选择的审方类型,如果同样是平台审方,走门店审方(默认)  <p>
     * <p> 3.门店设置审方类型:如果套餐存在平台审方则可以选到平台,否则默认给门店审方  <p>
     *
     * <p> 2025-06-01 需求改动: 扣减额度走的哪个套餐-就走套餐的审核类型<p>
     *
     * <p> {@link PrescriptionAuditTypeEnum} <p>
     *
     * @param sphDto
     */
    @Override
    public void executeNext(SignaturePassingHandleDto sphDto) {

        InquiryPharmacistPrescriptionDTO prescription = sphDto.getPrescription();

        // 查门店审方类型
        Integer inquiryPresAuditType = TenantUtils.execute(prescription.getTenantId(), () -> tenantParamConfigApi.getParamConfigValueSelf2SysInteger(TenantParamConfigTypeEnum.INQUIRY_PRES_AUDIT_TYPE));

        // 查相应套餐是否是平台审方
        TenantPackageCostDto tenantPackageCostDto = tenantPackageCostApi.getTenantPackageCostByLogBizId(prescription.getInquiryPref(), CostRecordTypeEnum.INQUIRY);

        boolean isPlatformReview = tenantPackageCostDto != null && Objects.equals(tenantPackageCostDto.getInquiryAuditType(), InquiryAuditTypeEnum.PLATFORM.getCode());

        PrescriptionAuditTypeEnum auditType = PrescriptionAuditTypeEnum.getAuditType(inquiryPresAuditType, isPlatformReview);

        log.info("【SignaturePassing】门店药师签章回调处理executeNext,pref:{},tenantId:{},auditType:{}", prescription.getPref(), prescription.getTenantId(),
            auditType.getDesc());

        prescriptionAuditStrategyMap.get(auditType).pushPrescriptionAuditPool(prescription);
    }
}
