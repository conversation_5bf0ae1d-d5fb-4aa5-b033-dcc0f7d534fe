package com.xyy.saas.inquiry.pharmacist.server.convert.pharmacist;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryTenantPharmacistRelationBindReqVO;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryTenantPharmacistRelationDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author:chenxiaoyi
 * @Date:2024/10/12 17:17
 */
@Mapper
public interface InquiryTenantPharmacistRelationConvert {

    InquiryTenantPharmacistRelationConvert INSTANCE = Mappers.getMapper(InquiryTenantPharmacistRelationConvert.class);

    default List<InquiryTenantPharmacistRelationDO> convertBindBatch(InquiryTenantPharmacistRelationBindReqVO bindReqVO) {
        return bindReqVO.getTenantIds().stream()
            .map(b -> InquiryTenantPharmacistRelationDO.builder()
                .pharmacistId(bindReqVO.getPharmacistId())
                .tenantId(b)
                .status(CommonStatusEnum.ENABLE.getStatus())
                .build())
            .collect(Collectors.toList());
    }
}
