package com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.migration;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * 荷叶老问诊迁移 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_inquiry_migration", autoResultMap = true)
// @KeySequence("saas_inquiry_migration_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class InquiryMigrationDO extends BaseDO {

    /**
     * 主键
     */
    @TableId(type = IdType.AUTO)
    private Long id;
    /**
     * 机构编码
     */
    private String organSign;

    /**
     * 名称
     */
    private String name;
    /**
     * 迁移后门店id
     */
    private Long tenantId;
    /**
     * 省
     */
    private String province;
    /**
     * 省编码
     */
    private String provinceCode;
    /**
     * 市
     */
    private String city;
    /**
     * 市编码
     */
    private String cityCode;
    /**
     * 区
     */
    private String area;
    /**
     * 区编码
     */
    private String areaCode;
    /**
     * 迁移状态 0待确认 1待迁移 2迁移中 3已迁移
     */
    private Integer status;
    /**
     * 迁移类型 1门店、2套餐
     */
    private Integer migrationType;
    /**
     * 迁移计划开始时间
     */
    private LocalDateTime startTime;
    /**
     * 门店迁移完成始时间
     */
    private LocalDateTime storeEndTime;
    /**
     * 套餐迁移完成始时间
     */
    private LocalDateTime packageEndTime;
    /**
     * 门店状态 0待迁移 1失败 2成功
     */
    private Integer storeStatus;
    /**
     * 员工状态 0待迁移 1失败 2成功
     */
    private Integer employeeStatus;
    /**
     * 药师状态 0待迁移 1失败 2成功
     */
    private Integer pharmacistStatus;
    /**
     * 患者状态 0待迁移 1失败 2成功
     */
    private Integer patientStatus;
    /**
     * 门店迁移状态 0待迁移 1失败 2成功
     */
    private Integer storeMigrationStatus;
    /**
     * 套餐迁移状态 0待迁移 1失败 2成功
     */
    private Integer packageMigrationStatus;
    /**
     * 备注
     */
    private String remark;

    /**
     * 是否强制覆盖 0是,1否
     */
    private Integer forceOverride;

    /**
     * 扩展信息
     */
    private String ext;

}