package com.xyy.saas.inquiry.pharmacist.server.config;

import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.forward.InquiryPharmacistForwardRespVO;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.dto.PharmacistForwardDetailRespDto;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.dto.PharmacistSyncQueryDto;
import com.xyy.saas.inquiry.pojo.ForwardResult;
import com.xyy.saas.inquiry.pojo.forward.ForwardPageResultDto;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.service.annotation.GetExchange;
import org.springframework.web.service.annotation.HttpExchange;
import org.springframework.web.service.annotation.PostExchange;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2024/11/27 20:41
 */


@HttpExchange(accept = "application/json", contentType = "application/json")
public interface InquiryPharmacistForwardClient {

    /**
     * 转发获取药师列表
     *
     * @return ForwardResult
     */
    @PostExchange("/doctor/forward/queryUserDoctorPharmacistLists")
    ForwardResult<ForwardPageResultDto<InquiryPharmacistForwardRespVO>> queryUserDoctorPharmacistLists(@RequestBody PharmacistSyncQueryDto dto);

    /**
     * 转发获取药师同步详情 + CA
     *
     * @return ForwardResult
     */
    @GetExchange("/doctor/forward/syncUserDoctorPharmacist")
    ForwardResult<PharmacistForwardDetailRespDto> syncUserDoctorPharmacist(@RequestParam String guid);


}
