package com.xyy.saas.inquiry.pharmacist.server.convert.signature;

import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.enums.prescription.PrescriptionAuditStatusEnum;
import com.xyy.saas.inquiry.enums.signature.SignatureStatusEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditSaveReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditVO;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.dto.InquiryPharmacistPrescriptionDTO;
import com.xyy.saas.inquiry.pharmacist.server.service.signature.dto.SignaturePassingHandleDto;
import com.xyy.saas.inquiry.pojo.prescription.ParticipantItem;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureAuditDto;
import java.time.LocalDateTime;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author:chenxiaoyi
 * @Date:2024/10/12 17:17
 */
@Mapper
public interface InquiryPharmacistSignatureConvert {

    InquiryPharmacistSignatureConvert INSTANCE = Mappers.getMapper(InquiryPharmacistSignatureConvert.class);

    default PrescriptionSignatureAuditDto convertSignAuditDto(InquiryPharmacistPrescriptionDTO ppDto, InquiryPrescriptionAuditVO auditVO, AdminUserRespDTO userRespDTO) {
        PrescriptionSignatureAuditDto signatureAuditDto = new PrescriptionSignatureAuditDto();
        signatureAuditDto.setPrescriptionPref(ppDto.getPref());
        signatureAuditDto.setInquiryBizType(ppDto.getInquiryBizType());
        signatureAuditDto.setPrescriptionImgUrl(ppDto.getPrescriptionImgUrl());
        signatureAuditDto.setAuditRecordId(auditVO.getAuditRecordId());
        signatureAuditDto.setAuditorTypeEnum(auditVO.getAuditorTypeEnum());
        signatureAuditDto.setCoordinate(auditVO.getCoordinate());
        signatureAuditDto.setParticipantItem(
            userRespDTO == null ? new ParticipantItem() : ParticipantItem.builder().userId(userRespDTO.getId()).name(userRespDTO.getNickname()).mobile(userRespDTO.getMobile()).signImgUrl(auditVO.getAuditSignImgUrl()).build());
        return signatureAuditDto;
    }

    default InquiryPrescriptionAuditSaveReqVO convertSaveAuditRecord(SignaturePassingHandleDto sphDto, PrescriptionAuditStatusEnum prescriptionAuditStatusEnum, SignatureStatusEnum signatureStatusEnum) {
        return InquiryPrescriptionAuditSaveReqVO.builder()
            .id(sphDto.getSpMessage().getAuditRecordId())
            .pref(sphDto.getPrescription().getPref())
            .auditApprovalType(sphDto.getPrescription().getInquiryWayType())
            .auditStatus(prescriptionAuditStatusEnum.getCode())
            .signatureStatus(signatureStatusEnum.getStatusCode())
            .auditLevel(sphDto.getSpMessage().getParticipantItem().getSort())
            .auditorSignImgUrl(sphDto.getSpMessage().getParticipantItem().getSignImgUrl())
            .auditorCallbackTime(sphDto.getSpMessage().getCallBackTime()).build();
    }

    default void fillRecord2AuditVO(InquiryPrescriptionAuditSaveReqVO auditSaveReqVO, InquiryPharmacistPrescriptionDTO prescription, InquiryRecordDto inquiryRecord, InquiryDoctorDto doctor) {
        auditSaveReqVO.setTenantId(prescription.getTenantId())
            .setAuditorId(doctor.getUserId())
            .setAuditorName(doctor.getName())
            .setAuditorType(AuditorTypeEnum.DOCTOR.getCode())
            .setAuditorReceiveTime(inquiryRecord.getStartTime())
            .setAuditorApprovalTime(prescription.getOutPrescriptionTime())
            .setAuditorSignatureTime(prescription.getOutPrescriptionTime())
            .setAuditorCallbackTime(LocalDateTime.now())
            .setClientOsType(inquiryRecord.getClientOsType())
            .setClientChannelType(inquiryRecord.getClientChannelType());

    }
}
