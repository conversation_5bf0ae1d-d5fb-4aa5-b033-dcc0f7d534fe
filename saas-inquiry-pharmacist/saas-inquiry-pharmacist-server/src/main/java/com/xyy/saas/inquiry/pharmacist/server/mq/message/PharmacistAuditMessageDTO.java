package com.xyy.saas.inquiry.pharmacist.server.mq.message;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 药师延迟审核msg
 *
 * <AUTHOR>
 */
@Builder
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class PharmacistAuditMessageDTO implements Serializable {

    /**
     * 药师编号
     */
    private String pref;

    /**
     * 门店id
     */
    private Long tenantId;


}
