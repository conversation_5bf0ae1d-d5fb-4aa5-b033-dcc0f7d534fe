package com.xyy.saas.inquiry.pharmacist.server.service.migration.dto;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2025/06/05 16:09
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class MigrationDrugStoreReqDto implements java.io.Serializable {

    private List<String> organSigns;

    /**
     * 机构号
     */
    // private String organSign;

    /**
     * 迁移状态
     */
    private Integer migrationStatus;

    /**
     * 套餐迁移时间
     */
    private Date migrationTime;

    // /**
    //  * 套餐GUID
    //  */
    // private List<String> packageGuids;
    //
    //
    // /**
    //  * 套餐状态 0未生效,1生效中,2已过期,3已用尽,4退款,5作废,10初始状态
    //  *
    //  * @see com.xyy.saas.remote.web.core.enums.ValidStatusEnum
    //  */
    // private Integer packageValidStatus;

    private List<packVa> packages;


    @Data
    public static class packVa implements Serializable {

        /**
         * 套餐GUID
         */
        private String packageGuid;

        /**
         * 套餐状态 0未生效,1生效中,2已过期,3已用尽,4退款,5作废,10初始状态
         *
         * @see com.xyy.saas.remote.web.core.enums.ValidStatusEnum
         */
        private Integer packageValidStatus;

    }

}
