package com.xyy.saas.inquiry.pharmacist.server.service.migration.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2025/06/05 16:09
 */
@Data
@Accessors(chain = true)
@AllArgsConstructor
@NoArgsConstructor
public class InquiryMigrationDto implements java.io.Serializable {

    /**
     * 主键
     */
    private Long id;
    /**
     * 机构编码
     */
    private String organSign;

    /**
     * 名称
     */
    private String name;

    /**
     * 迁移后门店id
     */
    private Long tenantId;

    /**
     * 医院编码
     */
    private String hospitalPref;

    /**
     * 是否强制覆盖 0是,1否
     */
    private Integer forceOverride;

    /**
     * 管理员手机号
     */
    private String adminMobile;
}
