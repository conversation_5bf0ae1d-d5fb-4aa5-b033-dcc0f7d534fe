package com.xyy.saas.inquiry.pharmacist.server.service.signature.dto;

import com.xyy.saas.inquiry.pharmacist.server.service.audit.dto.InquiryPharmacistPrescriptionDTO;
import com.xyy.saas.inquiry.signature.mq.SignaturePassingMessage;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:chenxiaoyi
 * @Date:2024/12/04 13:39
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SignaturePassingHandleDto {

    /**
     * 处方数据
     */
    private InquiryPharmacistPrescriptionDTO prescription;

    /**
     * 签章消息
     */
    private SignaturePassingMessage spMessage;


}
