package com.xyy.saas.inquiry.pharmacist.server.mq.message.migration;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 问诊迁移
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class InquiryMigrationEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "INQUIRY_MIGRATION";

    private InquiryMigrationMsgDto msg;


    @JsonCreator
    public InquiryMigrationEvent(@JsonProperty("msg") InquiryMigrationMsgDto msg) {
        this.msg = msg;
    }


    @Override
    public String getTag() {
        return "";
    }

}
