package com.xyy.saas.inquiry.pharmacist.server.mq.message;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 处方线下审方事件
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class PrescriptionOfflineAuditEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "PRESCRIPTION_OFFLINE_AUDIT";

    private String msg;


    @JsonCreator
    public PrescriptionOfflineAuditEvent(@JsonProperty("msg") String msg) {
        this.msg = msg;
    }


    @Override
    public String getTag() {
        return "";
    }

}
