package com.xyy.saas.inquiry.pharmacist.server.mq.message;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 医院平台药师免签审核事件
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class HosPlatPhaFreeAuditEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "HOS_PLAT_PHA_FREE_AUDIT";

    private HosPlatPhaFreeAuditMsgDto msg;


    @JsonCreator
    public HosPlatPhaFreeAuditEvent(@JsonProperty("msg") HosPlatPhaFreeAuditMsgDto msg) {
        this.msg = msg;
    }


    @Override
    public String getTag() {
        return "";
    }

}
