package com.xyy.saas.inquiry.pharmacist.server.service.migration;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_MIGRATION_INVALIDATE_ERROR;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_MIGRATION_LIMIT_MAX;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_MIGRATION_NOT_EXISTS;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_MIGRATION_OPERATE_ERROR;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_MIGRATION_RIGHT_NOW_LIMIT_MAX;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_MIGRATION_STATUS_OPERATE_ERROR;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.alibaba.nacos.shaded.com.google.common.collect.Lists;
import com.xyy.saas.inquiry.enums.migration.MigrationActionEnum;
import com.xyy.saas.inquiry.enums.migration.MigrationPointStatusEnum;
import com.xyy.saas.inquiry.enums.migration.MigrationStatusEnum;
import com.xyy.saas.inquiry.enums.migration.MigrationTypeEnum;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.migration.vo.InquiryMigrationBatchReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.migration.vo.InquiryMigrationExcelVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.migration.vo.InquiryMigrationImportReqDto;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.migration.vo.InquiryMigrationPageReqVO;
import com.xyy.saas.inquiry.pharmacist.server.convert.migration.InquiryMigrationConvert;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.migration.InquiryMigrationDO;
import com.xyy.saas.inquiry.pharmacist.server.dal.mysql.migration.InquiryMigrationMapper;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.migration.InquiryMigrationEvent;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.migration.InquiryMigrationMsgDto;
import com.xyy.saas.inquiry.pharmacist.server.mq.producer.migration.InquiryMigrationProducer;
import com.xyy.saas.inquiry.pharmacist.server.service.migration.dto.MigrationDrugStoreReqDto;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.excel.ImportResultDto;
import com.xyy.saas.inquiry.pojo.migration.MigrationDrugStoreRespDto;
import com.xyy.saas.inquiry.util.excel.EasyExcelUtil;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * 荷叶老问诊迁移 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InquiryMigrationServiceImpl implements InquiryMigrationService {

    @Resource
    private InquiryMigrationMapper inquiryMigrationMapper;

    @Resource
    private InquiryMigrationForwardService inquiryMigrationForwardService;

    @Autowired
    private EasyExcelUtil easyExcelUtil;

    @Resource
    private ConfigApi configApi;

    @Resource
    private TenantApi tenantApi;

    @Resource
    private InquiryMigrationProducer inquiryMigrationProducer;

    @Resource
    private MigrationCoreService migrationCoreService;

    private static final String BATCH_TENANT_MIGRATION_EXCEL_NAME = "批量导入迁移门店";

    private static final String TENANT_MIGRATION_EXCEL_MAX_IMPORT_SIZE = "system.tenant.migration.max.import.size";

    private static final String TENANT_MIGRATION_MAX_SIZE = "system.tenant.migration.max.size";


    public InquiryMigrationServiceImpl getSelf() {
        return SpringUtil.getBean(this.getClass());
    }

    /**
     * 最大导入限制
     *
     * @return
     */
    private int getMaxImportSize() {
        return NumberUtil.parseInt(configApi.getConfigValueByKey(TENANT_MIGRATION_EXCEL_MAX_IMPORT_SIZE), 1000);
    }

    /**
     * 最大待迁移限制
     *
     * @return
     */
    private int getMaxMigrationSize() {
        return NumberUtil.parseInt(configApi.getConfigValueByKey(TENANT_MIGRATION_MAX_SIZE), 2000);
    }

    @Override
    public PageResult<InquiryMigrationDO> getInquiryMigrationPage(InquiryMigrationPageReqVO pageReqVO) {
        return inquiryMigrationMapper.selectPage(pageReqVO);
    }


    @Override
    @Transactional
    public void deleteInquiryMigration(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return;
        }

        List<InquiryMigrationDO> migrationDOS = inquiryMigrationMapper.selectBatchIds(ids);
        if (CollUtil.isEmpty(migrationDOS)) {
            return;
        }

        inquiryMigrationMapper.deleteByIds(ids);

        // 老系统修改状态-未迁移
        inquiryMigrationForwardService.updateDrugStoreMigrationStatus(
            new MigrationDrugStoreReqDto().setOrganSigns(CollectionUtils.convertList(migrationDOS, InquiryMigrationDO::getOrganSign)).setMigrationStatus(MigrationStatusEnum.PENDING_CONFIRMATION.getCode()));
    }


    @Override
    public ImportResultDto batchImportMigration(InquiryMigrationImportReqDto importReqDto) {

        importReqDto.setExcelName(BATCH_TENANT_MIGRATION_EXCEL_NAME).setLimitCount(getMaxImportSize());

        return easyExcelUtil.importData(importReqDto, InquiryMigrationExcelVO.class, this::processBatchImportMigration);
    }

    /**
     * 批量导入迁移门店
     */
    private void processBatchImportMigration(InquiryMigrationImportReqDto reqDto, List<InquiryMigrationExcelVO> readyImportDataList) {
        // 1. 数据校验
        EasyExcelUtil.validateDuplicates(readyImportDataList, InquiryMigrationExcelVO::getOrganSign, "门店编码重复:");
        // 检查迁移条数
        checkMigrationSize(reqDto.getMigrationAction(), CollUtil.size(readyImportDataList), reqDto.getStartTime());

        // 2. 分批处理数据
        List<InquiryMigrationDO> migrationList = new ArrayList<>();
        for (List<InquiryMigrationExcelVO> batchList : Lists.partition(readyImportDataList, 200)) {
            processBatch(batchList, reqDto, migrationList);
        }
        // 3. 批量保存
        if (CollUtil.isNotEmpty(migrationList)) {
            inquiryMigrationMapper.insertBatch(migrationList);
            // 待确认数量
            reqDto.setConfirmCount(migrationList.stream().filter(m -> Objects.equals(m.getStatus(), MigrationStatusEnum.PENDING_CONFIRMATION.getCode())).count());

            // 更新老系统迁移状态
            List<String> organSigns = migrationList.stream().filter(m -> Objects.equals(m.getStatus(), MigrationStatusEnum.PENDING_MIGRATION.getCode())).map(InquiryMigrationDO::getOrganSign).toList();
            inquiryMigrationForwardService.updateDrugStoreMigrationStatus(new MigrationDrugStoreReqDto()
                .setOrganSigns(organSigns)
                .setMigrationStatus(MigrationStatusEnum.PENDING_MIGRATION.getCode()));

            // 4.如果是立即执行,则放入MQ
            if (MigrationActionEnum.isImme(reqDto.getMigrationAction())) {
                for (String organSign : organSigns) {
                    inquiryMigrationProducer.sendMessage(InquiryMigrationEvent.builder().msg(new InquiryMigrationMsgDto().setOrganSign(organSign)).build(), LocalDateTime.now().plusSeconds(1));
                    // migrationCoreService.migration(organSign);
                }
            }
        }
    }

    private void processBatch(List<InquiryMigrationExcelVO> batchList, InquiryMigrationImportReqDto reqDto,
        List<InquiryMigrationDO> migrationList) {
        if (CollUtil.isEmpty(batchList)) {
            return;
        }
        // 1. 获取机构号列表
        List<String> organSigns = CollectionUtils.convertList(batchList, InquiryMigrationExcelVO::getOrganSign);

        // 2. 查询已存在的迁移记录
        Map<String, InquiryMigrationDO> migrationMap = inquiryMigrationMapper.queryList(InquiryMigrationPageReqVO.builder().organSigns(organSigns).build())
            .stream().collect(Collectors.toMap(InquiryMigrationDO::getOrganSign, Function.identity(), (a, b) -> b));

        // 3. 查询老系统门店数据
        Map<String, MigrationDrugStoreRespDto> drugStoreMap = inquiryMigrationForwardService.queryDrugStoreLists(organSigns.stream().filter(o -> !migrationMap.containsKey(o)).toList())
            .stream().collect(Collectors.toMap(MigrationDrugStoreRespDto::getOrganSign, Function.identity(), (a, b) -> b));

        // 4. 查询新系统租户信息
        List<String> drugStoreNames = drugStoreMap.values().stream()
            .flatMap(dto -> Stream.of(dto.getDrugstoreName(), dto.getBusinessLicenseName(), dto.getBusinessLicenseNumber()))
            .filter(Objects::nonNull)
            .distinct()
            .collect(Collectors.toList());
        List<TenantDto> tenantDtos = tenantApi.getTenantListByNames(drugStoreNames);

        // 5. 处理每个门店
        for (InquiryMigrationExcelVO excelVO : batchList) {
            processSingleStore(excelVO, reqDto, migrationMap, drugStoreMap, tenantDtos, migrationList);
        }
    }

    private void processSingleStore(InquiryMigrationExcelVO excelVO, InquiryMigrationImportReqDto reqDto,
        Map<String, InquiryMigrationDO> migrationMap, Map<String, MigrationDrugStoreRespDto> drugStoreMap,
        List<TenantDto> tenantDtos, List<InquiryMigrationDO> migrationList) {
        // 1. 检查是否已存在迁移记录
        InquiryMigrationDO existingMigration = migrationMap.get(excelVO.getOrganSign());
        if (existingMigration != null) {
            excelVO.setErrMsg("该门店在新荷叶系统中迁移状态为[" + MigrationStatusEnum.getByCode(existingMigration.getStatus()) + "]，如需操作 请去迁移记录-对应状态中迁移");
            return;
        }

        // 2. 检查老系统是否存在该门店
        MigrationDrugStoreRespDto drugStoreRespDto = drugStoreMap.get(excelVO.getOrganSign());
        if (drugStoreRespDto == null) {
            excelVO.setErrMsg("无法在老荷叶系统中找到对应门店ID");
            return;
        }

        // 3. 创建迁移记录
        InquiryMigrationDO migrationDO = InquiryMigrationConvert.INSTANCE.convertInitMigrationDo(drugStoreRespDto);
        migrationDO.setStartTime(Optional.ofNullable(reqDto.getStartTime()).orElse(LocalDateTime.now()));

        // 4. 检查重复信息
        List<String> duplicates = tenantDtos.stream().filter(t ->
            StringUtils.equalsAnyIgnoreCase(t.getName(), drugStoreRespDto.getDrugstoreName(), drugStoreRespDto.getBusinessLicenseName())
                || StringUtils.equalsAnyIgnoreCase(t.getBusinessLicenseName(), drugStoreRespDto.getDrugstoreName(), drugStoreRespDto.getBusinessLicenseName())
                || StringUtils.equalsIgnoreCase(t.getBusinessLicenseNumber(), drugStoreRespDto.getBusinessLicenseNumber())
        ).map(t -> t.getName().concat("(").concat(t.getPref()).concat(")")).distinct().toList();

        if (CollUtil.isNotEmpty(duplicates)) {
            migrationDO.setStatus(MigrationStatusEnum.PENDING_CONFIRMATION.getCode());
            migrationDO.setStartTime(null);
            migrationDO.setRemark("与新荷叶系统 " + String.join(";", duplicates) + " 门店名或营业执照信息重复");
        }

        migrationList.add(migrationDO);
    }

    /**
     * 检查迁移条数
     */
    private void checkMigrationSize(Integer migrationAction, int readyImportDataListSize, LocalDateTime startTime) {
        // 1. 立即迁移 校验条数>10
        if (MigrationActionEnum.isImme(migrationAction) && readyImportDataListSize > 10) {
            throw exception(INQUIRY_MIGRATION_RIGHT_NOW_LIMIT_MAX, 10);
        }

        // 2. 创建迁移任务 校验时间内待迁移条数>2000
        if (!MigrationActionEnum.isImme(migrationAction)) {
            int pendingCount = inquiryMigrationMapper.countPendingMigrations(startTime);
            if (pendingCount + readyImportDataListSize > getMaxMigrationSize()) {
                throw exception(INQUIRY_MIGRATION_LIMIT_MAX, getMaxMigrationSize());
            }
        }
    }

    @Override
    public void invalidateInquiryMigration(Long id) {
        InquiryMigrationDO inquiryMigrationDO = validateInquiryMigrationExists(id);

        if (inquiryMigrationDO.getTenantId() == null) {
            throw exception(INQUIRY_MIGRATION_INVALIDATE_ERROR);
        }

        if (Objects.equals(inquiryMigrationDO.getStatus(), MigrationStatusEnum.MIGRATED.getCode())
            && Objects.equals(inquiryMigrationDO.getStoreMigrationStatus(), MigrationPointStatusEnum.SUCCESS.getCode())
            && !Objects.equals(inquiryMigrationDO.getPackageMigrationStatus(), MigrationPointStatusEnum.SUCCESS.getCode())) {
            // 老系统修改状态-未迁移
            inquiryMigrationForwardService.updateDrugStoreMigrationStatus(
                new MigrationDrugStoreReqDto().setOrganSigns(Collections.singletonList(inquiryMigrationDO.getOrganSign())).setMigrationStatus(MigrationStatusEnum.PENDING_CONFIRMATION.getCode()));
            // 删除数据
            inquiryMigrationMapper.deleteById(id);
            // 新系统修改门店状态-禁用
            tenantApi.updateTenantStatus(inquiryMigrationDO.getTenantId(), CommonStatusEnum.DISABLE.getStatus());
            return;
        }
        throw exception(INQUIRY_MIGRATION_STATUS_OPERATE_ERROR, "作废迁移");

    }

    private InquiryMigrationDO validateInquiryMigrationExists(Long id) {
        InquiryMigrationDO inquiryMigrationDO = inquiryMigrationMapper.selectById(id);
        if (inquiryMigrationDO == null) {
            throw exception(INQUIRY_MIGRATION_NOT_EXISTS);
        }
        return inquiryMigrationDO;
    }

    /**
     * <p> 1.待确认 - 立即迁移 / 生成任务 / 批量生成迁移任务 </p>
     *
     * <p> 2.待迁移 - 立即迁移 </p>
     *
     * @param batchReqVO
     */
    @Override
    public void batchInquiryMigration(InquiryMigrationBatchReqVO batchReqVO) {

        List<InquiryMigrationDO> migrationDOS = getSelf().inquiryMigration(batchReqVO);

        if (MigrationActionEnum.isImme(batchReqVO.getMigrationAction())) {
            for (InquiryMigrationDO migrationDO : migrationDOS) {
                inquiryMigrationProducer.sendMessage(InquiryMigrationEvent.builder().msg(new InquiryMigrationMsgDto().setOrganSign(migrationDO.getOrganSign())).build(), LocalDateTime.now().plusSeconds(1));
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public List<InquiryMigrationDO> inquiryMigration(InquiryMigrationBatchReqVO batchReqVO) {
        // 1.检查迁移条数
        checkMigrationSize(batchReqVO.getMigrationAction(), CollUtil.size(batchReqVO.getIds()), batchReqVO.getStartTime());

        // 2.判断门店是否已迁移
        List<InquiryMigrationDO> migrationDOS = inquiryMigrationMapper.selectBatchIds(batchReqVO.getIds());

        String migratedStoreNames = migrationDOS.stream().filter(m ->
            Objects.equals(m.getStatus(), MigrationStatusEnum.MIGRATED.getCode())
                && Objects.equals(m.getStatus(), MigrationStatusEnum.MIGRATING.getCode())).map(InquiryMigrationDO::getName).collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(migratedStoreNames)) {
            throw exception(INQUIRY_MIGRATION_OPERATE_ERROR, "该门店已迁移 或在 迁移中:" + migratedStoreNames);
        }

        // 3.判断是否存在重复门店,存在则拦截报错
        Map<String, MigrationDrugStoreRespDto> drugStoreMap = inquiryMigrationForwardService.queryDrugStoreLists(CollectionUtils.convertList(migrationDOS, InquiryMigrationDO::getOrganSign))
            .stream().collect(Collectors.toMap(MigrationDrugStoreRespDto::getOrganSign, Function.identity(), (a, b) -> b));

        // 4. 查询新系统租户信息
        List<String> drugStoreNames = drugStoreMap.values().stream()
            .flatMap(dto -> Stream.of(dto.getDrugstoreName(), dto.getBusinessLicenseName(), dto.getBusinessLicenseNumber()))
            .filter(StringUtils::isNotBlank)
            .distinct()
            .collect(Collectors.toList());
        List<TenantDto> tenantDtos = tenantApi.getTenantListByNames(drugStoreNames);

        List<String> errMsgs = new ArrayList<>();

        for (InquiryMigrationDO migrationDO : migrationDOS) {
            // 检查老系统是否存在该门店
            MigrationDrugStoreRespDto drugStoreRespDto = drugStoreMap.get(migrationDO.getOrganSign());

            if (drugStoreRespDto == null) {
                errMsgs.add("无法在老荷叶系统中找到对应门店" + migrationDO.getOrganSign());
                continue;
            }

            // 校验门店重复
            List<String> duplicates = tenantDtos.stream().filter(t ->
                StringUtils.equalsAnyIgnoreCase(t.getName(), drugStoreRespDto.getDrugstoreName(), drugStoreRespDto.getBusinessLicenseName())
                    || StringUtils.equalsAnyIgnoreCase(t.getBusinessLicenseName(), drugStoreRespDto.getDrugstoreName(), drugStoreRespDto.getBusinessLicenseName())
                    || StringUtils.equalsIgnoreCase(t.getBusinessLicenseNumber(), drugStoreRespDto.getBusinessLicenseNumber())
            ).map(t -> t.getName().concat(t.getPref())).distinct().toList();

            if (duplicates.stream().distinct().count() > 1) {
                errMsgs.add("门店信息在新系统重复:" + String.join(",", duplicates));
                continue;
            }

            // 设置成待迁移 +  时间 + 强制覆盖
            Integer forceOverride = Objects.equals(migrationDO.getStatus(), MigrationStatusEnum.PENDING_MIGRATION.getCode())
                ? null : CommonStatusEnum.ENABLE.getStatus();
            migrationDO.setStatus(MigrationActionEnum.isImme(batchReqVO.getMigrationAction()) ? MigrationStatusEnum.MIGRATING.getCode() : MigrationStatusEnum.PENDING_MIGRATION.getCode())
                .setRemark("")
                .setStartTime(batchReqVO.getStartTime())
                .setForceOverride(forceOverride);
        }

        if (CollUtil.isNotEmpty(errMsgs)) {
            throw exception(INQUIRY_MIGRATION_OPERATE_ERROR, String.join(",", errMsgs));
        }

        // 更新迁移数据状态
        inquiryMigrationMapper.updateBatch(migrationDOS);

        // 更新老系统迁移状态
        inquiryMigrationForwardService.updateDrugStoreMigrationStatus(new MigrationDrugStoreReqDto()
            .setOrganSigns(migrationDOS.stream().map(InquiryMigrationDO::getOrganSign).toList())
            .setMigrationStatus(MigrationStatusEnum.PENDING_MIGRATION.getCode()));

        return migrationDOS;
    }

    /**
     * <p> 已迁移 - 单个重迁门店(待迁移) / 单个重迁套餐(门店已迁移) / 批量重迁门店(待迁移) / 批量重迁套餐(门店已迁移) </p>
     *
     * @param batchReqVO
     */
    @Override
    public void batchReInquiryMigration(InquiryMigrationBatchReqVO batchReqVO) {

        List<InquiryMigrationDO> migrationDOS = getSelf().reInquiryMigration(batchReqVO);

        // 立即迁移
        if (MigrationActionEnum.isImme(batchReqVO.getMigrationAction())) {
            for (InquiryMigrationDO migrationDO : migrationDOS) {
                inquiryMigrationProducer.sendMessage(InquiryMigrationEvent.builder().msg(new InquiryMigrationMsgDto().setOrganSign(migrationDO.getOrganSign())).build(), LocalDateTime.now().plusSeconds(1));
            }
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public List<InquiryMigrationDO> reInquiryMigration(InquiryMigrationBatchReqVO batchReqVO) {
        // 1.检查迁移条数
        checkMigrationSize(batchReqVO.getMigrationAction(), CollUtil.size(batchReqVO.getIds()), batchReqVO.getStartTime());

        List<InquiryMigrationDO> migrationDOS = inquiryMigrationMapper.selectBatchIds(batchReqVO.getIds());

        String migratedStoreNames = migrationDOS.stream().filter(m ->
            Objects.equals(m.getStatus(), MigrationStatusEnum.PENDING_MIGRATION.getCode())
                && Objects.equals(m.getStatus(), MigrationStatusEnum.MIGRATING.getCode())).map(InquiryMigrationDO::getName).collect(Collectors.joining(","));
        if (StringUtils.isNotBlank(migratedStoreNames)) {
            throw exception(INQUIRY_MIGRATION_OPERATE_ERROR, "该门店已待迁移 或 迁移中:" + migratedStoreNames);
        }

        // 如果是重迁门店 校验状态
        if (Objects.equals(batchReqVO.getMigrationType(), MigrationTypeEnum.STORE.getCode())) {
            String failNames = migrationDOS.stream().filter(m -> !Objects.equals(m.getStoreMigrationStatus(), MigrationPointStatusEnum.FAILED.getCode()))
                .map(InquiryMigrationDO::getName).collect(Collectors.joining(","));
            if (StringUtils.isNotBlank(failNames)) {
                throw exception(INQUIRY_MIGRATION_OPERATE_ERROR, "门店状态不可操作重新迁移:" + failNames);
            }
        }

        // 如果是重迁套餐 校验状态
        if (Objects.equals(batchReqVO.getMigrationType(), MigrationTypeEnum.PACKAGE.getCode())) {
            String failNames = migrationDOS.stream().filter(m -> !(Objects.equals(m.getStoreMigrationStatus(), MigrationPointStatusEnum.SUCCESS.getCode())
                    && Objects.equals(m.getPackageMigrationStatus(), MigrationPointStatusEnum.FAILED.getCode())))
                .map(InquiryMigrationDO::getName).collect(Collectors.joining(","));
            if (StringUtils.isNotBlank(failNames)) {
                throw exception(INQUIRY_MIGRATION_OPERATE_ERROR, "套餐状态不可操作重新迁移:" + failNames);
            }
        }
        // 更新迁移数据状态
        List<InquiryMigrationDO> list = migrationDOS.stream()
            .map(m -> InquiryMigrationConvert.INSTANCE.convertReMigrationDo(m.getId(), batchReqVO)).toList();

        inquiryMigrationMapper.updateBatch(list);

        // 计划迁移
        if (!MigrationActionEnum.isImme(batchReqVO.getMigrationAction())) {
            // 生成迁移计划 - 回更打标状态 不开体验套餐
            inquiryMigrationForwardService.updateDrugStoreMigrationStatus(new MigrationDrugStoreReqDto()
                .setOrganSigns(migrationDOS.stream().map(InquiryMigrationDO::getOrganSign).toList())
                .setMigrationTime(Date.from(batchReqVO.getStartTime().atZone(ZoneId.systemDefault()).toInstant())));
        }

        return migrationDOS;
    }

}