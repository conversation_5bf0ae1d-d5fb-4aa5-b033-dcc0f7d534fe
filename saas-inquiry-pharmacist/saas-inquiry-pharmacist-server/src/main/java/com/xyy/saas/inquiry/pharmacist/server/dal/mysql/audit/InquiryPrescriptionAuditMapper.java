package com.xyy.saas.inquiry.pharmacist.server.dal.mysql.audit;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.pharmacist.server.controller.app.audit.vo.InquiryPrescriptionAuditPageReqVO;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.audit.InquiryPrescriptionAuditDO;
import org.apache.ibatis.annotations.Mapper;
import java.util.List;

/**
 * 处方审核记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryPrescriptionAuditMapper extends BaseMapperX<InquiryPrescriptionAuditDO> {

    default PageResult<InquiryPrescriptionAuditDO> selectPage(InquiryPrescriptionAuditPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InquiryPrescriptionAuditDO>()
            .eqIfPresent(InquiryPrescriptionAuditDO::getPref, reqVO.getPref())
            .eqIfPresent(InquiryPrescriptionAuditDO::getAuditorType, reqVO.getAuditorType())
            .inIfPresent(InquiryPrescriptionAuditDO::getAuditorType, reqVO.getAuditorTypeList())
            .eqIfPresent(InquiryPrescriptionAuditDO::getAuditLevel, reqVO.getAuditLevel())
            .eqIfPresent(InquiryPrescriptionAuditDO::getAuditStatus, reqVO.getAuditStatus())
            .eqIfPresent(InquiryPrescriptionAuditDO::getAuditorId, reqVO.getAuditorId())
            .likeIfPresent(InquiryPrescriptionAuditDO::getAuditorName, reqVO.getAuditorName())
            .eqIfPresent(InquiryPrescriptionAuditDO::getAuditApprovalType, reqVO.getAuditApprovalType())
            .betweenIfPresent(InquiryPrescriptionAuditDO::getAuditorReceiveTime, reqVO.getAuditorReceiveTime())
            .betweenIfPresent(InquiryPrescriptionAuditDO::getAuditorApprovalTime, reqVO.getAuditorApprovalTime())
            .eqIfPresent(InquiryPrescriptionAuditDO::getAuditorRejectedReason, reqVO.getAuditorRejectedReason())
            .eqIfPresent(InquiryPrescriptionAuditDO::getAuditorCaSign, reqVO.getAuditorCaSign())
            .eqIfPresent(InquiryPrescriptionAuditDO::getAuditorSignImgUrl, reqVO.getAuditorSignImgUrl())
            .eqIfPresent(InquiryPrescriptionAuditDO::getSignatureStatus, reqVO.getSignatureStatus())
            .betweenIfPresent(InquiryPrescriptionAuditDO::getAuditorSignatureTime, reqVO.getAuditorSignatureTime())
            .betweenIfPresent(InquiryPrescriptionAuditDO::getAuditorCallbackTime, reqVO.getAuditorCallbackTime())
            .eqIfPresent(InquiryPrescriptionAuditDO::getClientChannelType, reqVO.getClientChannelType())
            .eqIfPresent(InquiryPrescriptionAuditDO::getClientOsType, reqVO.getClientOsType())
            .betweenIfPresent(InquiryPrescriptionAuditDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(InquiryPrescriptionAuditDO::getId));
    }

    default List<InquiryPrescriptionAuditDO> selectListByCondition(InquiryPrescriptionAuditPageReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<InquiryPrescriptionAuditDO>()
            .eqIfPresent(InquiryPrescriptionAuditDO::getPref, reqVO.getPref())
            .inIfPresent(InquiryPrescriptionAuditDO::getPref, reqVO.getPrefList())
            .eqIfPresent(InquiryPrescriptionAuditDO::getAuditorType, reqVO.getAuditorType())
            .inIfPresent(InquiryPrescriptionAuditDO::getAuditorType, reqVO.getAuditorTypeList())
            .eqIfPresent(InquiryPrescriptionAuditDO::getAuditLevel, reqVO.getAuditLevel())
            .eqIfPresent(InquiryPrescriptionAuditDO::getAuditStatus, reqVO.getAuditStatus())
            .eqIfPresent(InquiryPrescriptionAuditDO::getAuditorId, reqVO.getAuditorId())
            .likeIfPresent(InquiryPrescriptionAuditDO::getAuditorName, reqVO.getAuditorName())
            .eqIfPresent(InquiryPrescriptionAuditDO::getAuditApprovalType, reqVO.getAuditApprovalType())
            .betweenIfPresent(InquiryPrescriptionAuditDO::getAuditorReceiveTime, reqVO.getAuditorReceiveTime())
            .betweenIfPresent(InquiryPrescriptionAuditDO::getAuditorApprovalTime, reqVO.getAuditorApprovalTime())
            .eqIfPresent(InquiryPrescriptionAuditDO::getAuditorRejectedReason, reqVO.getAuditorRejectedReason())
            .eqIfPresent(InquiryPrescriptionAuditDO::getAuditorCaSign, reqVO.getAuditorCaSign())
            .eqIfPresent(InquiryPrescriptionAuditDO::getAuditorSignImgUrl, reqVO.getAuditorSignImgUrl())
            .eqIfPresent(InquiryPrescriptionAuditDO::getSignatureStatus, reqVO.getSignatureStatus())
            .betweenIfPresent(InquiryPrescriptionAuditDO::getAuditorSignatureTime, reqVO.getAuditorSignatureTime())
            .betweenIfPresent(InquiryPrescriptionAuditDO::getAuditorCallbackTime, reqVO.getAuditorCallbackTime())
            .eqIfPresent(InquiryPrescriptionAuditDO::getClientChannelType, reqVO.getClientChannelType())
            .eqIfPresent(InquiryPrescriptionAuditDO::getClientOsType, reqVO.getClientOsType())
            .betweenIfPresent(InquiryPrescriptionAuditDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(InquiryPrescriptionAuditDO::getId));
    }

}