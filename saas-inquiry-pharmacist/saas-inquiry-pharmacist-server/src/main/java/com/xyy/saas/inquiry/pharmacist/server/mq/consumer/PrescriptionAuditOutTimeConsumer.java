package com.xyy.saas.inquiry.pharmacist.server.mq.consumer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.PrescriptionAuditOutTimeEvent;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.InquiryPrescriptionAuditService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 处方审核超时MQ
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/03 16:29
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_pharmacist_server_mq_consumer_PrescriptionAuditOutTimeConsumer",
    topic = PrescriptionAuditOutTimeEvent.TOPIC)
public class PrescriptionAuditOutTimeConsumer {


    @Resource
    private InquiryPrescriptionAuditService inquiryPrescriptionAuditService;

    @EventBusListener
    public void prescriptionAuditOutTimeConsumer(PrescriptionAuditOutTimeEvent auditOutTimeEvent) {
        try {
            inquiryPrescriptionAuditService.auditOutTime(auditOutTimeEvent.getMsg());
        } catch (Exception e) {
            log.error("prescriptionAuditOutTimeConsumer:{}", e.getMessage(), e);
        }
    }


}
