package com.xyy.saas.inquiry.pharmacist.server.mq.consumer;

import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionStatusEnum;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionApi;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.pharmacist.server.constant.PrescriptionAuditConstant;
import com.xyy.saas.inquiry.pharmacist.server.convert.prescription.InquiryPharmacistPrescriptionConvert;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.PrescriptionOfflineAuditEvent;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.dto.InquiryPharmacistPrescriptionDTO;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.strategy.DrugstoreAuditStrategy;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.strategy.OfflineAuditStrategy;
import com.xyy.saas.inquiry.pharmacist.server.service.audit.strategy.PlatformAuditStrategy;
import jakarta.annotation.Resource;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

/**
 * 处方线下审方MQ
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/03 16:29
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_pharmacist_server_mq_consumer_PrescriptionOfflineAuditConsumer",
    topic = PrescriptionOfflineAuditEvent.TOPIC)
public class PrescriptionOfflineAuditConsumer {


    @Resource
    private OfflineAuditStrategy offlineAuditStrategy;

    @Resource
    private PlatformAuditStrategy platformAuditStrategy;

    @Resource
    private DrugstoreAuditStrategy drugstoreAuditStrategy;

    @DubboReference
    private InquiryPrescriptionApi inquiryPrescriptionApi;

    @Resource
    private ConfigApi configApi;


    @EventBusListener
    public void prescriptionAuditOutTimeConsumer(PrescriptionOfflineAuditEvent offlineAuditEvent) {

        if (StringUtils.equals(configApi.getConfigValueByKey(PrescriptionAuditConstant.PRESCRIPTION_CHANGE_OFFLINE_AUDIT_SWITCH), "1")) {
            return;
        }

        // 校验处方状态,完成不再处理
        InquiryPrescriptionRespDTO prescription = inquiryPrescriptionApi.getInquiryPrescription(InquiryPrescriptionQueryDTO.builder().pref(offlineAuditEvent.getMsg()).build());
        if (prescription == null) {
            return;
        }

        log.info("[prescriptionAuditOutTimeConsumer] 转处方线下审方MQ,处方pref :{} 状态:{}", prescription.getPref(), prescription.getStatus());
        if (!PrescriptionStatusEnum.isCanAuditStatus(prescription.getStatus())) {
            return;
        }

        InquiryPharmacistPrescriptionDTO ppDto = InquiryPharmacistPrescriptionConvert.INSTANCE.convertDTO(prescription);
        Integer auditorType = prescription.getAuditorType();

        TenantUtils.execute(prescription.getTenantId(), () -> {
            // 转线下审方
            offlineAuditStrategy.autoAuditPrescription(ppDto, RoleCodeEnum.PHARMACIST, AuditorTypeEnum.fromCode(auditorType));

            if (Objects.equals(AuditorTypeEnum.PLATFORM_PHARMACIST.getCode(), auditorType)) {
                log.info("[prescriptionAuditOutTimeConsumer] 转处方线下审方-移除平台审方池,处方pref :{}", prescription.getPref());
                try {
                    platformAuditStrategy.removePrescriptionAuditPool(ppDto);
                } catch (Exception e) {
                    log.error("[prescriptionAuditOutTimeConsumer] 转处方线下审方-移除平台审方池异常,处方pref :{}", prescription.getPref(), e);
                }
            }

            if (Objects.equals(AuditorTypeEnum.DRUGSTORE_PHARMACIST.getCode(), auditorType)) {
                log.info("[prescriptionAuditOutTimeConsumer] 转处方线下审方-移除门店审方池,处方pref :{}", prescription.getPref());
                try {
                    drugstoreAuditStrategy.removePrescriptionAuditPool(ppDto);
                } catch (Exception e) {
                    log.error("[prescriptionAuditOutTimeConsumer] 转处方线下审方-移除门店审方池,处方pref :{}", prescription.getPref(), e);
                }
            }
        });
    }
}
