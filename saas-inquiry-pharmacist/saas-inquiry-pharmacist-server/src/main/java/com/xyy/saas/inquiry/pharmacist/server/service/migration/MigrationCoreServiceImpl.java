package com.xyy.saas.inquiry.pharmacist.server.service.migration;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_MIGRATION_DRUGSTORE_FAIL;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_MIGRATION_HOSPITAL_FAIL;
import static com.xyy.saas.inquiry.pharmacist.enums.ErrorCodeConstants.INQUIRY_MIGRATION_OPERATE_ERROR;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.exception.ServiceException;
import cn.iocoder.yudao.framework.tenant.core.util.TenantUtils;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.system.api.dict.DictDataApi;
import cn.iocoder.yudao.module.system.api.permission.PermissionApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.system.api.tenant.migration.TenantMigrationApi;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserSaveDTO;
import com.xyy.saas.inquiry.enums.doctor.AuditStatusEnum;
import com.xyy.saas.inquiry.enums.doctor.PharmacistTypeEnum;
import com.xyy.saas.inquiry.enums.migration.MigrationPointStatusEnum;
import com.xyy.saas.inquiry.enums.migration.MigrationStatusEnum;
import com.xyy.saas.inquiry.enums.migration.MigrationTypeEnum;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalApi;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.patient.api.patient.PatientApi;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistRespVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistSaveReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.forward.InquiryPharmacistForwardRespVO;
import com.xyy.saas.inquiry.pharmacist.server.convert.migration.InquiryMigrationConvert;
import com.xyy.saas.inquiry.pharmacist.server.convert.pharmacist.InquiryPharmacistConvert;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.migration.InquiryMigrationDO;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryPharmacistDO;
import com.xyy.saas.inquiry.pharmacist.server.dal.mysql.migration.InquiryMigrationMapper;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.migration.InquiryMigrationEvent;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.migration.InquiryMigrationMsgDto;
import com.xyy.saas.inquiry.pharmacist.server.mq.producer.migration.InquiryMigrationProducer;
import com.xyy.saas.inquiry.pharmacist.server.service.migration.dto.InquiryMigrationDto;
import com.xyy.saas.inquiry.pharmacist.server.service.migration.dto.MigrationDrugStoreReqDto;
import com.xyy.saas.inquiry.pharmacist.server.service.migration.dto.MigrationDrugStoreReqDto.packVa;
import com.xyy.saas.inquiry.pharmacist.server.service.migration.dto.MigrationEmployeeRespDto;
import com.xyy.saas.inquiry.pharmacist.server.service.migration.dto.MigrationEmployeeRespDto.Emp;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.InquiryPharmacistService;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.PharmacistSyncService;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.dto.PharmacistForwardDetailDto;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.dto.PharmacistForwardDetailRespDto;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.migration.MigrationDrugStoreRespDto;
import com.xyy.saas.inquiry.pojo.migration.MigrationPackageRespDto;
import com.xyy.saas.inquiry.pojo.migration.MigrationPatientDto;
import com.xyy.saas.inquiry.signature.api.ca.InquirySignatureCaAuthApi;
import com.xyy.saas.inquiry.signature.api.ca.dto.SyncCreateCaDto;
import com.xyy.saas.inquiry.signature.api.signature.InquiryUserSignatureInformationApi;
import com.xyy.saas.inquiry.util.RedisUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 荷叶老问诊迁移 核心Service
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class MigrationCoreServiceImpl implements MigrationCoreService {

    @Resource
    private InquiryMigrationMapper inquiryMigrationMapper;

    @Resource
    private InquiryMigrationForwardService inquiryMigrationForwardService;

    @Resource
    private InquiryMigrationProducer inquiryMigrationProducer;

    @Resource
    private ConfigApi configApi;

    @Autowired
    private TenantMigrationApi tenantMigrationApi;

    @Resource
    private InquiryHospitalApi inquiryHospitalApi;

    @Autowired
    private TenantApi tenantApi;

    @Resource
    private PharmacistSyncService pharmacistSyncService;

    @Autowired
    private FileApi fileApi;

    @Autowired
    private AdminUserApi adminUserApi;

    @Resource
    private DictDataApi dictDataApi;

    @Resource
    private InquirySignatureCaAuthApi inquirySignatureCaAuthApi;

    @Resource
    private InquiryPharmacistService inquiryPharmacistService;

    @Resource
    private PatientApi patientApi;

    @Resource
    private InquiryUserSignatureInformationApi inquiryUserSignatureInformationApi;

    @Autowired
    private PermissionApi permissionApi;

    /**
     * 迁移套餐体验版天数
     */
    private static final String TENANT_MIGRATION_PACKAGE_TRIAL_DAY = "system.tenant.migration.package.trial.day";

    /**
     * 迁移手机号锁
     */
    private static final String TENANT_MIGRATION_MOBILE_LOCK = "tenant.migration.mobile.lock:";

    /**
     * 迁移节点
     */
    private final List<MigrationPoint> consumers = new ArrayList<>();

    @Data
    @Builder
    public static class MigrationPoint {

        private String desc;
        private Consumer<InquiryMigrationDto> handleConsumer;
        private BiConsumer<InquiryMigrationDO, Integer> statusConsumer;
    }

    @PostConstruct
    public void init() {
        consumers.add(MigrationPoint.builder().desc("门店").statusConsumer(InquiryMigrationDO::setStoreStatus).handleConsumer(this::handleStore).build());
        consumers.add(MigrationPoint.builder().desc("员工").statusConsumer(InquiryMigrationDO::setEmployeeStatus).handleConsumer(this::handleEmployee).build());
        consumers.add(MigrationPoint.builder().desc("药师").statusConsumer(InquiryMigrationDO::setPharmacistStatus).handleConsumer(this::handlePharmacist).build());
        consumers.add(MigrationPoint.builder().desc("患者").statusConsumer(InquiryMigrationDO::setPatientStatus).handleConsumer(this::handlePatient).build());
    }


    public MigrationCoreServiceImpl getSelf() {
        return SpringUtil.getBean(MigrationCoreServiceImpl.class);
    }

    /**
     * 获取套餐试用天数
     */
    private int getPackageTrialDay() {
        return NumberUtil.parseInt(configApi.getConfigValueByKey(TENANT_MIGRATION_PACKAGE_TRIAL_DAY), 3);
    }


    @Override
    public void migration(String organSign) {
        InquiryMigrationDO inquiryMigrationDO = inquiryMigrationMapper.selectOne(InquiryMigrationDO::getOrganSign, organSign);
        if (inquiryMigrationDO == null) {
            log.info("系统迁移 - 当前机构不存在迁移计划,无需迁移,organSign:{}", organSign);
            return;
        }

        // 迁移门店
        if (Objects.equals(inquiryMigrationDO.getMigrationType(), MigrationTypeEnum.STORE.getCode())) {
            getSelf().migrationStore(inquiryMigrationDO);
            return;
        }

        // 迁移套餐
        if (Objects.equals(inquiryMigrationDO.getMigrationType(), MigrationTypeEnum.PACKAGE.getCode())) {
            getSelf().migrationPackage(inquiryMigrationDO);
        }
    }

    @Override
    public void migrationPackagePlan(String organSign, LocalDateTime endTime) {

        // InquiryMigrationDO inquiryMigrationDO = inquiryMigrationMapper.selectOne(InquiryMigrationDO::getOrganSign, organSign);
        //
        // if (inquiryMigrationDO == null || !(Objects.equals(inquiryMigrationDO.getStoreMigrationStatus(), MigrationPointStatusEnum.SUCCESS.getCode())
        //     && Objects.equals(inquiryMigrationDO.getPackageMigrationStatus(), MigrationPointStatusEnum.FAILED.getCode()))) {
        //     log.warn("系统迁移 - 门店迁移失败，无法处理迁移套餐,organSign:{}", organSign);
        //     return;
        // }
        // LocalDateTime localDateTime = endTime.toLocalDate().atStartOfDay();
        // // localDateTime 转成Date
        // Date date = Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
        // // 回更打标状态 不开体验套餐
        // inquiryMigrationForwardService.updateDrugStoreMigrationStatus(new MigrationDrugStoreReqDto().setOrganSign(organSign).setMigrationTime(date));
    }

    /**
     * 迁移门店
     */
    public void migrationStore(InquiryMigrationDO inquiryMigrationDO) {
        Long id = inquiryMigrationDO.getId();
        String organSign = inquiryMigrationDO.getOrganSign();
        Long trailPackageId = null;
        log.info("系统迁移 - 迁移门店 - 四项开始 -- organSign:{} ", organSign);

        if (!Objects.equals(inquiryMigrationDO.getStatus(), MigrationStatusEnum.PENDING_MIGRATION.getCode())
            && !Objects.equals(inquiryMigrationDO.getStatus(), MigrationStatusEnum.MIGRATING.getCode())) {
            log.warn("系统迁移 - 迁移门店 - 当前机构未处于[待迁移|迁移中]状态，无需迁移,organSign:{}", organSign);
            return;
        }
        // 套餐迁移时间
        LocalDateTime localDateTime = LocalDateTime.now().plusDays(getPackageTrialDay()).toLocalDate().atStartOfDay();

        // 状态- 迁移中
        updateMigrationStatus(id, null, null, MigrationStatusEnum.MIGRATING, null, null);
        try {
            InquiryMigrationDto migrationDto = InquiryMigrationConvert.INSTANCE.convertDto(inquiryMigrationDO);

            for (MigrationPoint consumer : consumers) {
                try {
                    log.info("系统迁移 - 迁移门店 - organSign:{},{} 开始", consumer.getDesc(), organSign);
                    // 依次迁移
                    consumer.getHandleConsumer().accept(migrationDto);
                    // 更新节点状态 - 成功
                    updateMigrationStatus(id, migrationDto.getTenantId(), null, MigrationStatusEnum.MIGRATING, MigrationPointStatusEnum.SUCCESS, consumer);

                    log.info("系统迁移 - 迁移门店 - organSign:{},{} 结束", consumer.getDesc(), organSign);
                } catch (Exception e) {
                    log.error("系统迁移 - 迁移门店 - organSign:{},{} 异常 ,e:{}", organSign, consumer.getDesc(), e.getMessage(), e);
                    // 更新节点状态 - 失败
                    updateMigrationStatus(id, migrationDto.getTenantId(), (e instanceof ServiceException) ? ((ServiceException) e).getMessage() : e.getMessage(), MigrationStatusEnum.MIGRATED, MigrationPointStatusEnum.FAILED, consumer);
                    return;
                }
            }
            // 创建体验套餐
            trailPackageId = tenantMigrationApi.createTrailPackage(InquiryMigrationConvert.INSTANCE.convertTrailPackage(migrationDto, getPackageTrialDay()));

            // 迁移状态 - 已迁移，下一步迁移套餐
            inquiryMigrationMapper.updateById(new InquiryMigrationDO().setId(id)
                .setPackageEndTime(localDateTime)
                .setStatus(MigrationStatusEnum.MIGRATED.getCode())
                .setMigrationType(MigrationTypeEnum.PACKAGE.getCode()));

            // 回更打标状态 + 时间
            inquiryMigrationForwardService.updateDrugStoreMigrationStatus(new MigrationDrugStoreReqDto()
                .setOrganSigns(Collections.singletonList(organSign))
                .setMigrationStatus(MigrationStatusEnum.MIGRATING.getCode())
                .setMigrationTime(Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant())) // 套餐迁移时间
            );

            log.info("系统迁移 - 迁移门店 - 四项结束 -- organSign:{},延迟迁移套餐:{} ", organSign, localDateTime);
        } catch (Exception e) {

            e = (e instanceof ServiceException) ? ((ServiceException) e) : e;

            log.error("系统迁移 - 迁移门店2rd - organSign:{} 异常 ,e:{}", organSign, e.getMessage(), e);
            // 删除新系统迁移的套餐
            tenantMigrationApi.deleteMigrationPackage(trailPackageId == null ? null : Collections.singletonList(trailPackageId));

            updateMigrationStatus(id, null, StringUtils.defaultIfBlank(e.getMessage(), e.toString()), MigrationStatusEnum.MIGRATED, MigrationPointStatusEnum.FAILED, null);
        }
        // 发送套餐迁移延迟MQ
        inquiryMigrationProducer.sendMessage(InquiryMigrationEvent.builder().msg(new InquiryMigrationMsgDto().setOrganSign(organSign)).build()
            , localDateTime);
    }

    /**
     * 更新迁移状态
     */
    private void updateMigrationStatus(Long id, Long tenantId, String remark, MigrationStatusEnum status, MigrationPointStatusEnum pointStatus, MigrationCoreServiceImpl.MigrationPoint consumer) {

        InquiryMigrationDO migrationDO = new InquiryMigrationDO().setId(id).setTenantId(tenantId)
            .setStatus(status.getCode())
            .setStoreMigrationStatus(pointStatus == null ? null : pointStatus.getCode())
            .setRemark(StringUtils.substring(remark, 0, 500));

        if (consumer != null && pointStatus != null) {
            consumer.getStatusConsumer().accept(migrationDO, pointStatus.getCode());
            migrationDO.setStoreEndTime(LocalDateTime.now()); // 设置迁移时间
        }
        // 最后一个患者迁移完成，设置门店迁移完成时间
        if (Objects.equals(migrationDO.getPatientStatus(), MigrationPointStatusEnum.SUCCESS.getCode())) {
            migrationDO.setStoreEndTime(LocalDateTime.now());
            // 门店迁移成功 启用门店
            tenantApi.updateTenantStatus(tenantId, CommonStatusEnum.ENABLE.getStatus());
        }

        inquiryMigrationMapper.updateById(migrationDO);
    }

    /**
     * 迁移门店时,如果门店不存在,先设置成禁用
     */
    private void handleStore(InquiryMigrationDto migrationDto) {

        MigrationDrugStoreRespDto drugStoreRespDto = inquiryMigrationForwardService.queryDrugStore(migrationDto.getOrganSign());
        // 校验老系统是否存在
        if (drugStoreRespDto == null) {
            throw exception(INQUIRY_MIGRATION_DRUGSTORE_FAIL, migrationDto.getOrganSign());
        }
        if (StringUtils.length(drugStoreRespDto.getContactPhone()) > 11) {
            throw exception(INQUIRY_MIGRATION_OPERATE_ERROR, "门店手机号超过11位:" + drugStoreRespDto.getContactPhone());
        }

        // 校验门店医院在新系统是否存在
        if (StringUtils.isNotBlank(drugStoreRespDto.getHospitalName())) {
            InquiryHospitalRespDto hospital = inquiryHospitalApi.getInquiryHospitalByName(drugStoreRespDto.getHospitalName());
            if (hospital == null) {
                throw exception(INQUIRY_MIGRATION_HOSPITAL_FAIL, drugStoreRespDto.getHospitalName());
            }
            migrationDto.setHospitalPref(hospital.getPref());
        }
        // 校验重复
        checkTenantRepeat(migrationDto, drugStoreRespDto);
        // 校验门店负责人手机号
        drugStoreRespDto.setContactPhone(checkAndGetAdminPhone(migrationDto, drugStoreRespDto));
        // 创建迁移门店 +锁
        lockMigration(drugStoreRespDto.getContactPhone(), () -> {
            Long tenantId = tenantMigrationApi.migrationTenant(drugStoreRespDto.setTenantId(migrationDto.getTenantId()));
            migrationDto.setTenantId(tenantId).setAdminMobile(drugStoreRespDto.getContactPhone());
        });
    }

    private String checkAndGetAdminPhone(InquiryMigrationDto migrationDto, MigrationDrugStoreRespDto drugStoreRespDto) {
        try {
            MigrationEmployeeRespDto respDto = inquiryMigrationForwardService.queryDrugStoreEmployee(migrationDto.getOrganSign());
            if (CollUtil.isNotEmpty(respDto.getEmployees())) {
                List<String> empPhones = respDto.getEmployees().stream().map(Emp::getPhone).filter(StringUtils::isNotBlank).toList();
                if (empPhones.contains(drugStoreRespDto.getContactPhone())) {
                    return drugStoreRespDto.getContactPhone();
                }
                return empPhones.getFirst();
            }
        } catch (Exception e) {
            log.error("[migrateDrugStore][drugStoreRespDto({})] 获取联系人手机号异常", drugStoreRespDto, e);
        }
        return drugStoreRespDto.getContactPhone();

    }

    // 校验重复
    private void checkTenantRepeat(InquiryMigrationDto migrationDto, MigrationDrugStoreRespDto drugStoreRespDto) {
        if (migrationDto.getTenantId() != null) {
            return;
        }
        // 4. 查询新系统租户信息
        List<String> drugStoreNames = Stream.of(drugStoreRespDto.getDrugstoreName(), drugStoreRespDto.getBusinessLicenseName(), drugStoreRespDto.getBusinessLicenseNumber())
            .filter(StringUtils::isNotBlank)
            .distinct()
            .collect(Collectors.toList());
        List<TenantDto> tenantDtos = tenantApi.getTenantListByNames(drugStoreNames);

        if (CollUtil.isEmpty(tenantDtos)) {
            return;
        }

        List<String> list = tenantDtos.stream().filter(t ->
            StringUtils.equalsAnyIgnoreCase(t.getName(), drugStoreRespDto.getDrugstoreName(), drugStoreRespDto.getBusinessLicenseName())
                || StringUtils.equalsAnyIgnoreCase(t.getBusinessLicenseName(), drugStoreRespDto.getDrugstoreName(), drugStoreRespDto.getBusinessLicenseName())
                || StringUtils.equalsIgnoreCase(t.getBusinessLicenseNumber(), drugStoreRespDto.getBusinessLicenseNumber())
        ).map(t -> t.getName().concat(t.getPref())).distinct().toList();

        // 重复门店超过1个
        if (list.size() > 1) {
            throw exception(INQUIRY_MIGRATION_OPERATE_ERROR, String.join(",", list));
        }
        // 处理覆盖
        if (CollUtil.isNotEmpty(list)) {
            if (CommonStatusEnum.isDisable(migrationDto.getForceOverride())) {
                throw exception(INQUIRY_MIGRATION_OPERATE_ERROR, "门店信息重复 " + String.join(",", list));
            }
            log.info("系统迁移 - 迁移门店 - 门店覆盖 - organSign:{},pref:{}", migrationDto.getOrganSign(), tenantDtos.getFirst().getPref());
            migrationDto.setTenantId(tenantDtos.getFirst().getId());
        }
    }

    public void lockMigration(String mobile, Runnable runnable) {
        String requestId = RandomStringUtils.randomAlphanumeric(16);
        final String lockKey = TENANT_MIGRATION_MOBILE_LOCK.concat(mobile);
        try {
            boolean lock = RedisUtils.tryLockWithSpin(lockKey, requestId);
            if (lock) {
                runnable.run();
            } else {
                throw exception(INQUIRY_MIGRATION_OPERATE_ERROR, "获取锁失败,需要人工重试 " + lockKey);
            }
        } finally {
            // 释放锁
            RedisUtils.releaseLock(lockKey, requestId);
        }
    }

    /**
     * 处理员工
     */
    private void handleEmployee(InquiryMigrationDto migrationDto) {

        MigrationEmployeeRespDto respDto = inquiryMigrationForwardService.queryDrugStoreEmployee(migrationDto.getOrganSign());

        TenantUtils.execute(migrationDto.getTenantId(), () -> {

            for (MigrationEmployeeRespDto.Emp emp : Optional.ofNullable(respDto.getEmployees()).orElse(List.of())) {
                if (StringUtils.length(emp.getPhone()) > 11) {
                    throw exception(INQUIRY_MIGRATION_OPERATE_ERROR, "员工手机号超过11位:" + emp.getPhone());
                }
            }
            // 强制重迁移 - 先解绑所有非管理员员工
            if (CommonStatusEnum.isEnable(migrationDto.getForceOverride())) {
                tenantMigrationApi.unBindEmployee(migrationDto.getTenantId());
            }

            // 处理调配发药等签名员工
            for (MigrationEmployeeRespDto.Staff staff : Optional.ofNullable(respDto.getDrugstoreStaffPos()).orElse(List.of())) {
                List<RoleCodeEnum> roleCodeEnums = getRoleCodeEnum(staff);
                inquiryUserSignatureInformationApi.createMigrationUserSignatureInformation(staff.getStaffName(), staff.getStaffSignImg(), roleCodeEnums
                    , Objects.equals(staff.getStatus(), (byte) 1) ? CommonStatusEnum.ENABLE.getStatus() : CommonStatusEnum.DISABLE.getStatus());
            }

            // 处理门店员工 + 角色
            for (MigrationEmployeeRespDto.Emp emp : Optional.ofNullable(respDto.getEmployees()).orElse(List.of())) {
                if (StringUtils.equals(emp.getPhone(), migrationDto.getAdminMobile())) {
                    continue;
                }
                lockMigration(emp.getPhone(), () -> {

                    AdminUserSaveDTO userSaveDTO = new AdminUserSaveDTO();
                    userSaveDTO.setCheckRelation(false);// 不检查，有就绑定上
                    userSaveDTO.setNickname(emp.getNickName());
                    userSaveDTO.setMobile(emp.getPhone());
                    // 手机号截取后六位
                    userSaveDTO.setPassword(StringUtils.substring(emp.getPhone(), 5, 12));
                    Long userId = tenantMigrationApi.migrationEmployee(userSaveDTO);
                    // 分配管理员角色
                    permissionApi.assignUserRoleWithSystemRoleCode(userId, RoleCodeEnum.STORE_ADMIN.getCode());
                });
            }

        });
    }

    private static List<RoleCodeEnum> getRoleCodeEnum(MigrationEmployeeRespDto.Staff staff) {
        List<RoleCodeEnum> list = new ArrayList<>();

        if (Objects.equals(staff.getStaffRole(), (byte) 1)) {
            list.add(RoleCodeEnum.ALLOCATION);
        }
        if (Objects.equals(staff.getStaffRole(), (byte) 2)) {
            list.add(RoleCodeEnum.DISPENSING);
            list.add(RoleCodeEnum.CHECK);
        }
        if (Objects.equals(staff.getStaffRole(), (byte) 3)) {
            list.add(RoleCodeEnum.PHARMACIST);
        }
        if (Objects.equals(staff.getStaffRole(), (byte) 4)) {
            list.add(RoleCodeEnum.CHECK);
        }
        return list;
    }

    /**
     * 处理药师
     */
    private void handlePharmacist(InquiryMigrationDto migrationDto) {

        List<InquiryPharmacistForwardRespVO> storePharmacist = inquiryMigrationForwardService.queryDrugStorePharmacist(migrationDto.getOrganSign());

        TenantUtils.execute(migrationDto.getTenantId(), () -> {

            for (InquiryPharmacistForwardRespVO pharmacist : storePharmacist) {

                if (StringUtils.length(pharmacist.getTelephone()) > 11) {
                    throw exception(INQUIRY_MIGRATION_OPERATE_ERROR, "药师手机号超过11位:" + pharmacist.getTelephone());
                }

                lockMigration(pharmacist.getTelephone(), () -> {
                    // 药师存在-跳过
                    InquiryPharmacistDO pharmacistByMobile = inquiryPharmacistService.getPharmacistByMobile(pharmacist.getTelephone());

                    if (pharmacistByMobile != null) {

                        if (Objects.equals(pharmacistByMobile.getPharmacistType(), PharmacistTypeEnum.DRUGSTORE.getCode())) {
                            // 判断 + 绑定关系
                            tenantMigrationApi.bindTenant(pharmacistByMobile.getUserId());
                            // 直接处理分配系统药师角色
                            permissionApi.assignUserRoleWithSystemRoleCode(pharmacistByMobile.getUserId(), RoleCodeEnum.PHARMACIST.getCode());
                        } else {
                            log.info("系统迁移 - 迁移门店 - 药师不为门店药师 - 跳过,organSign:{},手机号:{}", migrationDto.getOrganSign(), pharmacist.getTelephone());
                        }
                    } else {
                        PharmacistForwardDetailRespDto drugStorePharmacist = inquiryMigrationForwardService.getDrugStorePharmacist(pharmacist.getGuid());
                        if (drugStorePharmacist != null) {
                            PharmacistForwardDetailDto dto = drugStorePharmacist.getDto();
                            InquiryPharmacistRespVO pharmacistRespVO = InquiryPharmacistConvert.INSTANCE.convertSync(dto);
                            // 处理字典
                            Optional.ofNullable(dictDataApi.parseDictData(null, "formal_level", dto.getDocEdu()))
                                .ifPresent(d -> pharmacistRespVO.setFormalLevel(NumberUtils.toInt(d.getValue(), 1))); // 学历

                            // 1.创建新系统药师
                            AdminUserRespDTO user = adminUserApi.getUserByMobile(pharmacist.getTelephone());
                            // 判断医生新增或者修改
                            InquiryPharmacistDO inquiryPharmacistDO = user != null ? inquiryPharmacistService.getPharmacistByUserId(user.getId()) : null;
                            if (inquiryPharmacistDO == null) {
                                InquiryPharmacistSaveReqVO saveReqVO = InquiryPharmacistConvert.INSTANCE.convertSyncVo(pharmacistRespVO);
                                // 确认同步时 处理图片上传
                                pharmacistSyncService.handlePharmacistImgUpload(saveReqVO);
                                // 创建药师
                                inquiryPharmacistDO = inquiryPharmacistService.createInquiryPharmacistSystem(saveReqVO.setAuditStatus(AuditStatusEnum.APPROVED.getCode()));
                                // 创建CA信息
                                SyncCreateCaDto syncCreateCaDto = InquiryPharmacistConvert.INSTANCE.convertPharmacistCa(inquiryPharmacistDO, drugStorePharmacist.getCaInfo(), drugStorePharmacist.getPersonInfo());
                                inquirySignatureCaAuthApi.createSyncSignatureCaAuth(syncCreateCaDto);
                            }
                        }
                    }
                });
            }
        });
    }

    /**
     * 处理患者 门店下的历史患者信息近1000条：姓名、手机号、性别、身份证号（若存在，则迁移）年龄（若存在身份证号，根据身份证号计算年龄）
     */
    private void handlePatient(InquiryMigrationDto migrationDto) {

        List<MigrationPatientDto> list = inquiryMigrationForwardService.queryDrugStorePatient(migrationDto.getOrganSign());

        if (CollUtil.isNotEmpty(list)) {
            list = list.stream().filter(p ->
                StringUtils.length(p.getTelephone()) <= 11
                    && StringUtils.length(p.getIdCard()) <= 32
                    && StringUtils.length(p.getUserName()) <= 20).collect(Collectors.toList());
        }

        List<MigrationPatientDto> finalList = list;
        TenantUtils.execute(migrationDto.getTenantId(), () -> patientApi.migratePatientInfos(finalList));
    }


    /**
     * 迁移套餐
     */
    public void migrationPackage(InquiryMigrationDO inquiryMigrationDO) {

        String organSign = inquiryMigrationDO.getOrganSign();

        log.info("系统迁移 - 迁移套餐 - 开始 -- organSign:{} ", organSign);

        if (!Objects.equals(inquiryMigrationDO.getStoreMigrationStatus(), MigrationPointStatusEnum.SUCCESS.getCode())
            || Objects.equals(inquiryMigrationDO.getPackageMigrationStatus(), MigrationPointStatusEnum.SUCCESS.getCode())) {
            log.warn("系统迁移 - 迁移套餐 - 门店不是已成功or套餐已迁移，无法处理,organSign:{}", organSign);
            return;
        }

        List<Long> createPackageIds = new ArrayList<>();

        List<packVa> oldPackages = new ArrayList<>();

        MigrationDrugStoreRespDto drugStoreRespDto = inquiryMigrationForwardService.queryDrugStore(organSign);

        // 校验门店医院在新系统是否存在

        try {
            String hospitalPref = getDrugStoreHospitalPref(drugStoreRespDto);

            inquiryMigrationMapper.updateById(new InquiryMigrationDO().setId(inquiryMigrationDO.getId()).setStatus(MigrationStatusEnum.MIGRATING.getCode()));

            // 1.套餐迁移中 - 将问诊服务开关 关闭
            inquiryMigrationForwardService.updateDrugStoreMigrationStatus(new MigrationDrugStoreReqDto().setOrganSigns(Collections.singletonList(organSign)).setMigrationStatus(MigrationStatusEnum.PACKAGING_MIGRATION.getCode()));

            // 2.1查旧系统套餐
            List<MigrationPackageRespDto> packageRespDtos = inquiryMigrationForwardService.queryDrugStorePackage(organSign);

            if (CollUtil.isNotEmpty(packageRespDtos)) {

                oldPackages = packageRespDtos.stream().map(p -> {
                    packVa packVa = new packVa();
                    packVa.setPackageGuid(p.getGuid());
                    packVa.setPackageValidStatus(p.getValidStatus().intValue());
                    return packVa;
                }).toList();

                packageRespDtos.forEach(m -> {
                    m.setHospitalPref(hospitalPref);
                    m.setTenantId(inquiryMigrationDO.getTenantId());
                });
                // 2.2 创建新系统套餐
                createPackageIds = tenantMigrationApi.migrationPackage(packageRespDtos);
            }

            // 2.3 将旧系统 生效中 + 未生效的套餐作废 且回 更旧系统状态
            List<packVa> packVas = oldPackages.stream().filter(p -> Objects.equals(p.getPackageValidStatus(), 0) || Objects.equals(p.getPackageValidStatus(), 1)).map(p -> {
                packVa packVa = new packVa();
                packVa.setPackageGuid(p.getPackageGuid());
                packVa.setPackageValidStatus(5);
                return packVa;
            }).collect(Collectors.toList());

            inquiryMigrationForwardService.updateDrugStoreMigrationStatus(new MigrationDrugStoreReqDto()
                .setOrganSigns(Collections.singletonList(organSign))
                .setMigrationTime(new Date())
                .setPackages(packVas)
                .setMigrationStatus(MigrationStatusEnum.MIGRATED.getCode()));

            // 3.标记已迁移 + 套餐迁移信息
            inquiryMigrationMapper.updateById(new InquiryMigrationDO().setId(inquiryMigrationDO.getId())
                .setStatus(MigrationStatusEnum.MIGRATED.getCode())
                .setPackageMigrationStatus(MigrationPointStatusEnum.SUCCESS.getCode())
                .setPackageEndTime(LocalDateTime.now()));

            log.info("系统迁移 - 迁移套餐 - 结束 -- organSign:{} ", organSign);

        } catch (Exception e) {

            e = (e instanceof ServiceException) ? ((ServiceException) e) : e;

            log.error("系统迁移 - 迁移套餐 - 异常,organSign:{},e:{}", organSign, e.getMessage(), e);
            // 迁移异常,回滚旧系统状态-门店已迁移
            inquiryMigrationForwardService.updateDrugStoreMigrationStatus(new MigrationDrugStoreReqDto().setOrganSigns(Collections.singletonList(organSign))
                .setMigrationStatus(MigrationStatusEnum.MIGRATING.getCode()).setPackages(oldPackages.stream().filter(p -> Objects.equals(p.getPackageValidStatus(), 0) || Objects.equals(p.getPackageValidStatus(), 1)).toList()));
            // 删除新系统迁移的套餐
            tenantMigrationApi.deleteMigrationPackage(createPackageIds);
            // 标记迁移失败
            inquiryMigrationMapper.updateById(new InquiryMigrationDO().setId(inquiryMigrationDO.getId())
                .setStatus(MigrationStatusEnum.MIGRATED.getCode())
                .setPackageMigrationStatus(MigrationPointStatusEnum.FAILED.getCode())
                .setRemark(StringUtils.substring(StringUtils.defaultIfBlank(e.getMessage(), e.toString()), 0, 500))
                .setPackageEndTime(LocalDateTime.now()));
        }
    }

    private String getDrugStoreHospitalPref(MigrationDrugStoreRespDto drugStoreRespDto) {
        if (StringUtils.isNotBlank(drugStoreRespDto.getHospitalName())) {
            InquiryHospitalRespDto hospital = inquiryHospitalApi.getInquiryHospitalByName(drugStoreRespDto.getHospitalName());
            if (hospital == null) {
                throw exception(INQUIRY_MIGRATION_HOSPITAL_FAIL, drugStoreRespDto.getHospitalName());
            }
            return hospital.getPref();
        }
        return null;
    }

}