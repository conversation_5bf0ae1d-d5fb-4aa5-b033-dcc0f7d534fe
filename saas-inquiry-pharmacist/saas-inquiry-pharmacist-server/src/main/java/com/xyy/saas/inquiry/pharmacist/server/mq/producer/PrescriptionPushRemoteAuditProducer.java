package com.xyy.saas.inquiry.pharmacist.server.mq.producer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.mq.prescription.PrescriptionPushRemoteAuditEvent;
import org.springframework.stereotype.Component;

/**
 * @Desc 门店处方推送远程审方Producer
 * <AUTHOR>
 */
@Component
@EventBusProducer(
    topic = PrescriptionPushRemoteAuditEvent.TOPIC
)
public class PrescriptionPushRemoteAuditProducer extends EventBusRocketMQTemplate {


}
