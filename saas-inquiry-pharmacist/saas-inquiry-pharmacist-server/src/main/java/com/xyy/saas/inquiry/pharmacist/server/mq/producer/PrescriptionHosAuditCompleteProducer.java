package com.xyy.saas.inquiry.pharmacist.server.mq.producer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.mq.prescription.PrescriptionHosAuditCompletedEvent;
import org.springframework.stereotype.Component;

/**
 * @Desc 处方 医院药师审核完成
 * <AUTHOR>
 */
@Component
@EventBusProducer(
    topic = PrescriptionHosAuditCompletedEvent.TOPIC
)
public class PrescriptionHosAuditCompleteProducer extends EventBusRocketMQTemplate {


}
