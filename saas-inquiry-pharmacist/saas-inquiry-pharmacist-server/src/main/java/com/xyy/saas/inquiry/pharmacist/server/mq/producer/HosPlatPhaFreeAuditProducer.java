package com.xyy.saas.inquiry.pharmacist.server.mq.producer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.pharmacist.server.mq.message.HosPlatPhaFreeAuditEvent;
import org.springframework.stereotype.Component;

/**
 * @Desc 医院平台药师免签审核 producer
 * <AUTHOR>
 */
@Component
@EventBusProducer(
    topic = HosPlatPhaFreeAuditEvent.TOPIC
)
public class HosPlatPhaFreeAuditProducer extends EventBusRocketMQTemplate {


}
