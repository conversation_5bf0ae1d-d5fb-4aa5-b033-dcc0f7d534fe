package com.xyy.saas.inquiry.pharmacist.server.dal.mysql.pharmacist;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistPageReqVO;
import com.xyy.saas.inquiry.pharmacist.server.controller.admin.pharmacist.vo.InquiryPharmacistRespVO;
import com.xyy.saas.inquiry.pharmacist.server.dal.dataobject.pharmacist.InquiryPharmacistDO;
import com.xyy.saas.inquiry.pharmacist.server.service.pharmacist.dto.PharmacistQueryDto;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 药师信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryPharmacistMapper extends BaseMapperX<InquiryPharmacistDO> {


    void deleteById(Integer id);

    IPage<InquiryPharmacistRespVO> pagePharmacistSystem(Page<InquiryPharmacistRespVO> objectPage, @Param("reqVO") InquiryPharmacistPageReqVO reqVO);

    IPage<InquiryPharmacistRespVO> pagePharmacistStore(Page<InquiryPharmacistRespVO> objectPage, @Param("reqVO") InquiryPharmacistPageReqVO reqVO);

    List<InquiryPharmacistDO> queryByCondition(@Param("reqVO") InquiryPharmacistPageReqVO pageReqVO);


    default PageResult<InquiryPharmacistDO> selectPage(InquiryPharmacistPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InquiryPharmacistDO>()
            .eqIfPresent(InquiryPharmacistDO::getUserId, reqVO.getUserId())
            .eqIfPresent(InquiryPharmacistDO::getOnlineStatus, reqVO.getOnlineStatus())
            .geIfPresent(InquiryPharmacistDO::getId, reqVO.getMaxId())
            .likeIfPresent(InquiryPharmacistDO::getName, reqVO.getName())
            .eqIfPresent(InquiryPharmacistDO::getAuditStatus, reqVO.getAuditStatus())
            .eqIfPresent(InquiryPharmacistDO::getQualification, reqVO.getQualification())
            .eqIfPresent(InquiryPharmacistDO::getPharmacistType, reqVO.getPharmacistType())
            .betweenIfPresent(InquiryPharmacistDO::getCreateTime, reqVO.getCreateTime())
            .orderByAsc(InquiryPharmacistDO::getId));
    }

    default List<InquiryPharmacistDO> queryByDtoCondition(PharmacistQueryDto queryDto) {
        return selectList(new LambdaQueryWrapperX<InquiryPharmacistDO>()
            .eqIfPresent(InquiryPharmacistDO::getPharmacistType, queryDto.getPharmacistType())
            .eqIfPresent(InquiryPharmacistDO::getJobType, queryDto.getJobType())
            .eqIfPresent(InquiryPharmacistDO::getQualification, queryDto.getQualification())
            .eqIfPresent(InquiryPharmacistDO::getAuditStatus, queryDto.getAuditStatus())
            .eqIfPresent(InquiryPharmacistDO::getOnlineStatus, queryDto.getOnlineStatus())
            .eqIfPresent(InquiryPharmacistDO::getEnvTag, queryDto.getEnvTag())
            .inIfPresent(InquiryPharmacistDO::getQualification, queryDto.getQualifications())
            .inIfPresent(InquiryPharmacistDO::getUserId, queryDto.getUserIds())
            .eqIfPresent(InquiryPharmacistDO::getPharmacistNature, queryDto.getPharmacistNature())
        );
    }

    void offline(@Param("ids") List<Long> ids);
}