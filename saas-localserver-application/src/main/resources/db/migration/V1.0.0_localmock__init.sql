-- SQL for local mock environment, using H2 or MySQL syntax

-- Table for simulating Redis Key-Value store

CREATE TABLE IF NOT EXISTS local_mock_kv_store
(
    `key` VARCHAR(512) NOT NULL PRIMARY KEY,
    `value` CLOB,                -- 替换 LONGTEXT
    expire_at TIMESTAMP,           -- 移除 NULL 关键字（H2 默认允许 NULL）
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 移除 ON UPDATE（H2 不支持）
);

CREATE TABLE IF NOT EXISTS local_mock_message_queue
(
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    topic VARCHAR(255) NOT NULL,
    tag VARCHAR(255),
    message_body CLOB NOT NULL,     -- 替换 LONGTEXT
    status VARCHAR(20) DEFAULT 'UNPROCESSED' NOT NULL,
    consumer_group VARCHAR(255),
    consumed_at TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP -- 移除 ON UPDATE
);
CREATE INDEX IF NOT EXISTS idx_topic_status ON local_mock_message_queue(topic, status); -- 单独创建索引
