(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-2d0ab81f"],{1626:function(e,t,a){"use strict";a.r(t),a("96cf");var l=a("1da1"),o=a("5530"),r=a("7845"),n=[{label:"小票号",prop:"billNo",component:"el-input"},{label:"销售时间",prop:"billTime",component:"el-input"},{label:"收银员",prop:"cashier",component:"el-input"},{label:"客户姓名",prop:"customer",component:"el-input"},{label:"费用总额",prop:"totalAmount",component:"el-input"},{label:"订单类型",prop:"orderCanalStr",component:"el-input"},{label:"医保药师",prop:"pracPsnName",component:"el-input"},{label:"医保药师编码",prop:"pracPsnCode",component:"el-input"},{label:"备注",prop:"remarks",component:"el-input",width:"490px"}],i=[{label:"商品编码",prop:"pharmacyPref",hidden:!1,width:120},{label:"商品名称",prop:"productName",hidden:!1,width:150},{label:"国家医保编码",prop:"projectCode",hidden:!1,width:120},{label:"注册名",prop:"registeredName",hidden:!1,width:120},{label:"规格",prop:"attributeSpecification",hidden:!1,width:120},{label:"单位",prop:"unit",hidden:!1,width:100},{label:"批准文号",prop:"approvalNumber",hidden:!1,width:150},{label:"生产企业",prop:"manufacturer",hidden:!1,width:180},{label:"销售数量",prop:"saleNumber",hidden:!1,width:150},{label:"单价",prop:"salePrice",hidden:!1,width:120},{label:"金额",prop:"saleAmount",hidden:!1,width:150}],d=a("b775");function s(e){return Object(d.a)({url:"/medicare/offline/sales/record/findDetails",method:"post",data:e}).then((function(e){return e.result}))}var c,p={name:"AccountDetailsDetail",components:{DataTable:r.a},props:{isShow:{type:Boolean,default:!1},rowData:{type:Object,default:function(){return{}}}},data:function(){return{loading:!1,tableLoading:!1,paginationShow:!1,formItems:n,formModel:{billNo:"",billTime:"",cashier:"",customer:"",totalAmount:"",orderCanalStr:"",pracPsnName:"",pracPsnCode:"",remarks:""},tableConf:{tableId:"AccountDetailsDetail",height:"100%",ref:"AccountDetailsDetail",showIndex:!0,data:[],colModel:i}}},computed:{},created:function(){var e=this;this.$nextTick((function(){e.queryList()}))},methods:{queryList:(c=Object(l.a)(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return this.loading=!0,e.prev=1,e.next=4,s({billNo:this.rowData.billNo});case 4:t=e.sent,this.loading=!1,this.tableConf.data=t.details||[],this.formModel={billNo:t.billNo,billTime:t.billTime,cashier:t.cashier,customer:t.customer,totalAmount:t.totalAmount,orderCanalStr:t.orderCanalStr,pracPsnName:t.pracPsnName,pracPsnCode:t.pracPsnCode,remarks:t.remarks},e.next=13;break;case 10:e.prev=10,e.t0=e.catch(1),this.loading=!1;case 13:case"end":return e.stop()}}),e,this,[[1,10]])}))),function(){return c.apply(this,arguments)}),goBack:function(){this.$emit("update:is-show",!1),this.formModel={billNo:"",billTime:"",cashier:"",customer:"",totalAmount:"",orderCanalStr:"",pracPsnName:"",pracPsnCode:"",remarks:""}}}},h=a("2877"),u=Object(h.a)(p,(function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{staticClass:"g-fullscreen-dialog",attrs:{title:"",visible:e.isShow,fullscreen:"","show-close":!1,modal:!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.isShow=t}}},[a("ll-list-page-layout",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"g-list-page"},[a("div",{attrs:{slot:"nav-bar"},slot:"nav-bar"},[a("el-button",{on:{click:e.goBack}},[e._v("返回")])],1),e._v(" "),a("ll-form",{attrs:{slot:"search-form",model:e.formModel,"form-items":e.formItems,disabled:"","label-width":"110px"},slot:"search-form"}),e._v(" "),a("data-table",{key:e.tableConf.tableId,ref:e.tableConf.ref,attrs:{"table-conf":e.tableConf,"pagination-show":e.paginationShow},on:{"query-change":e.queryList}})],1)],1)}),[],!1,null,"1631796e",null).exports,b=a("ed08"),m={components:{DataTable:r.a,AccountDetailsDetail:u},data:function(){return{detailDialogShow:!1,loading:!1,loadingText:"加载中···",total:0,formModel:{orderNo:"",dataRange:[Object(b.d)(0),Object(b.d)(0)],orderType:""},rowData:{},formItem:[{label:"订单信息",prop:"billNo",component:"el-input",attrs:{clearable:!1,placeholder:"小票号"},width:"250px"},{label:"订单日期",prop:"dataRange",component:"ll-date-picker",attrs:{type:"date","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd",clearable:!1,width:"200px"},width:"300px",labelWidth:"80px"},{label:"订单类型",labelWidth:"80px",prop:"orderCanal",component:"ll-select",attrs:{options:[{label:"全部",value:""},{label:"医保订单",value:1},{label:"零售订单",value:0}],clearable:!0,placeholder:"全部"}}],tableConf:{reserveSelection:!1,tableId:"AccountDetails",rowKey:"billNo",height:"100%",ref:"AccountDetails",showIndex:!0,showSelection:!1,currRow:{},data:[],colModel:[{label:"小票号",prop:"billNo",hidden:!1,width:180},{label:"零售日期",prop:"billTimeYYYYMMDD",hidden:!1,width:150},{label:"收银员",prop:"cashier",hidden:!1,width:180},{label:"客户姓名",prop:"customer",hidden:!1,width:180},{label:"总金额",prop:"totalAmount",hidden:!1,width:150},{label:"订单类型",prop:"orderCanalStr",hidden:!1,width:120},{label:"医保药师编码",prop:"pracPsnCode",hidden:!1,width:150},{label:"医保药师",prop:"pracPsnName",hidden:!1,width:150},{label:"销售内容",prop:"context",hidden:!1,width:250}]},selectData:[]}},computed:{modelQuery:function(){var e=Object(o.a)(Object(o.a)({},this.formModel),{},{startDate:this.formModel.dataRange?this.formModel.dataRange[0]:"",endDate:this.formModel.dataRange?this.formModel.dataRange[1]:""});return delete e.dataRange,e}},mounted:function(){var e=this;this.$nextTick((function(){e.queryList()}))},methods:{handleQuery:function(){this.queryList()},handleReset:function(){this.queryList()},queryList:function(){var e=Object(l.a)(regeneratorRuntime.mark((function e(t){var a,l,r,n,i,s;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=3;break}return this.$refs[this.tableConf.ref].init(),e.abrupt("return");case 3:return a=t.pageSize,l=t.page,r=Object(o.a)(Object(o.a)({},this.modelQuery),{},{pageNum:l,pageSize:a}),this.loading=!0,e.prev=6,e.next=9,c=r,Object(d.a)({url:"/medicare/offline/sales/record/pageQuery",method:"post",data:c}).then((function(e){return e.result}));case 9:n=e.sent,i=n.list,s=n.totalRecord,this.loading=!1,this.tableConf.data=i||[],this.total=s,e.next=20;break;case 17:e.prev=17,e.t0=e.catch(6),this.loading=!1;case 20:case"end":return e.stop()}var c}),e,this,[[6,17]])})));return function(t){return e.apply(this,arguments)}}(),goDetail:function(e){this.detailDialogShow=!0,this.rowData=e},hideDetailDialog:function(e){this.detailDialogShow=!1}}},f=Object(h.a)(m,(function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ll-list-page-layout",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"g-list-page g-page-style",attrs:{"element-loading-text":e.loadingText}},[a("ll-search-form",{attrs:{slot:"search-form",model:e.formModel,"form-items":e.formItem,"label-width":"100px"},on:{submit:e.handleQuery,reset:e.handleReset},slot:"search-form"}),e._v(" "),a("data-table",{directives:[{name:"table-height",rawName:"v-table-height"}],key:e.tableConf.tableId,ref:e.tableConf.ref,attrs:{"table-conf":e.tableConf,total:e.total,"row-dblclick":e.goDetail},on:{"query-change":e.queryList}},[a("template",{slot:"operation"},[a("el-table-column",{attrs:{label:"操作","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.goDetail(t.row)}}},[e._v("详情")])]}}])})],1)],2),e._v(" "),e.detailDialogShow?a("account-details-detail",{attrs:{"is-show":e.detailDialogShow,"row-data":e.rowData},on:{"update:isShow":function(t){e.detailDialogShow=t},"update:is-show":function(t){e.detailDialogShow=t},hide:e.hideDetailDialog}}):e._e()],1)}),[],!1,null,"6eb8f68a",null);t.default=f.exports}}]);