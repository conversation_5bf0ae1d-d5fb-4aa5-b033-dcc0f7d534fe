(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-commons"],{7331:function(e,t,n){"use strict";n("ad83")},7845:function(e,t,n){"use strict";var a,i=n("2909"),r=n("b85c"),l=n("5530"),o=(n("96cf"),n("1da1")),s=(n("c5f6"),{name:"Pagination",props:{total:{type:Number,default:0},currentPage:{type:Number,default:1},lastPage:{type:Number,default:1},pageSize:{type:Number,default:50},pageSizes:{type:Array,default:function(){return[10,20,30,40,50]}},layout:{type:String,default:"total, sizes, prev, pager, next"}},data:function(){return{}},computed:{currPage:{get:function(){return this.currentPage},set:function(e){}}},methods:{handleSizeChange:function(e){this.$emit("handle-size-change",e)},handleCurrentChange:function(e){this.$emit("handle-current-change",e)},handleFirstPage:function(){this.currPage=1,this.$emit("handle-first-page",1)},handleLastPage:function(){this.currPage=this.lastPage,this.$emit("handle-last-page",this.lastPage)}}}),c=n("2877"),u=Object(c.a)(s,(function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"common-pagination"},[n("el-pagination",{attrs:{"prev-text":"上一页","next-text":"下一页",total:e.total,"page-count":e.total,"current-page":e.currPage,layout:e.layout,"page-size":e.pageSize,"page-sizes":e.pageSizes},on:{"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}})],1)}),[],!1,null,null,null).exports,h={commonName:{width:"144"},productName:{width:"144"},productCode:{width:"102"},date:{width:"98"},dateTime:{width:"150"},creater:{width:"102"},manufacturer:{width:"144"},productKind:{width:"84"},billStatus:{width:"102"},incomeTaxRate:{width:"98"},amount:{width:"86"},price:{width:"102"},taxPrice:{width:"114"},department:{width:"102"},todo:{width:"102"},dosageForm:{width:"102"},specifications:{width:"138"},unit:{width:"102"},remark:{width:"192"}},d={name:"SimpleTable",components:{pagination:u},props:{paginationShow:{type:Boolean,default:!0},tableConf:{type:Object,default:function(){return{ref:{type:String,default:""},height:{type:String,default:"100%"},data:{type:Array,default:function(){return[]}},colModel:{type:Array,default:function(){return[]}},showSelection:{type:Boolean,default:!1},showIndex:{type:Boolean,default:!1},showExpand:{type:Boolean,default:!1},preColumnsNum:{type:Number,default:0}}}},data:{type:Array,default:function(){return[]}},selectable:{type:Function,default:function(){return function(){}}},pagination:{type:Object,default:function(){return{show:!0,currentPage:1,pageSize:50,pageSizes:[10,20,50],total:0}}},pageLayout:{type:String,default:"total, sizes, prev, pager, next"},currentChange:{type:Function,default:function(){return function(){}}},rowClick:{type:Function,default:function(){return function(){}}},rowDblclick:{type:Function,default:function(){return function(){}}},rowClassName:{type:Function,default:function(){return function(){}}},headerCellClassName:{type:Function,default:function(){return function(){}}},total:{type:Number,default:0}},data:function(){return{isScroll:!0,LlSceneMapList:h,dragState:{start:-9,end:-9,dragging:!1,direction:void 0},virtualStyleObj:{left:"auto",right:"auto",top:"auto",height:"0px"},key:1,originVirtualStyleObj:null,innerTotal:this.total||0,innerCurrentPage:1,innerPageSize:20}},computed:{getIndex:function(){return this.tableConf.showSelection?this.tableConf.showIndex?2:1:this.tableConf.showIndex?1:0},colModelPreColumnsNum:function(){return this.tableConf.showSelection?this.tableConf.preColumnsNum+2:this.tableConf.preColumnsNum+1},queryInfo:function(){return{page:this.innerCurrentPage,pageSize:this.innerPageSize}}},watch:{"tableConf.colModel":{handler:function(){var e=this;setTimeout((function(){e.$refs[e.tableConf.ref].doLayout()}),0)},deep:!0},total:function(e){this.innerTotal=e},innerCurrentPage:function(){this.queryChange("page")},innerPageSize:function(e){this.$emit("update:pageSize",e)}},created:function(){this.innerPageSize=this.pagination.pageSize},mounted:function(){},updated:(a=Object(o.a)(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,this.$nextTick();case 2:(t=this.$refs[this.tableConf.ref||"simpleTable"].$refs.bodyWrapper).scrollHeight>t.clientHeight?this.isScroll=!0:this.isScroll=!1,this.$refs[this.tableConf.ref||"simpleTable"].doLayout();case 5:case"end":return e.stop()}}),e,this)}))),function(){return a.apply(this,arguments)}),methods:{load:function(e,t,n){this.$emit("load",e,t,n)},init:function(){this.innerCurrentPage=1,this.queryChange("init")},queryChange:function(e){var t=Object(l.a)({type:e},this.queryInfo);this.$emit("query-change",t)},handleSizeChange:function(e){this.innerPageSize=e,this.queryChange("size"),this.$emit("handle-size-change",e)},handleCurrentChange:function(e){this.innerCurrentPage=e,this.$emit("handle-current-change",e)},handleFirstPage:function(e){this.$emit("handle-first-page",e)},handleLastPage:function(e){this.$emit("handle-last-page",e)},handleSelectionChange:function(e){this.$emit("handle-selection-change",e)},handleSortChange:function(e){var t=e.column,n=e.prop,a=e.order;this.$emit("sort-change",{column:t,prop:n,order:a})},handleChange:function(e,t,n){this.$emit("select",e,t,n)},handleAllChange:function(e){this.$emit("select-all",e),this.$emit("select",e)},clearSelection:function(){this.$refs[this.tableConf.ref].clearSelection()},renderHeader:function(e,t){var n=this;return e("div",{class:["thead-cell","drop","datatable-drop"],domProps:{draggable:!0,effectAllowed:"all"},style:{display:"inline"},on:{dragstart:function(e){n.handleMouseDown(e,t)},dragover:function(e){e.preventDefault(),n.handleMouseMove(e,t)}}},[t.column.label])},handleMouseDown:function(e,t){var n=t.column;this.dragState.dragging=!0,this.dragState.start=parseInt(n.columnKey);var a,i=document.getElementsByClassName("w-table")[0],l=document.getElementsByClassName("virtual"),o=Object(r.a)(l);try{for(o.s();!(a=o.n()).done;){var s=a.value;s.style.height=i.clientHeight-1+"px",s.style.width=s.parentElement.parentElement.clientWidth+"px"}}catch(e){o.e(e)}finally{o.f()}document.addEventListener("dragend",this.handleMouseUp)},handleMouseUp:function(){this.dragColumn(this.dragState),this.dragState={start:-6,end:-6,dragging:!1,direction:void 0},document.removeEventListener("dragend",this.handleMouseUp)},handleMouseMove:function(e,t){var n=t.column;if(!this.dragState.dragging)return!1;var a=parseInt(n.columnKey);a-this.dragState.start!=0?(this.dragState.direction=a-this.dragState.start<0?"left":"right",this.dragState.end=parseInt(n.columnKey)):this.dragState.direction=void 0},dragColumn:function(e){var t,n=this,a=e.start,r=e.end;if(!(r<0)){var l=this.tableConf.colModel.splice(a,1);(t=this.tableConf.colModel).splice.apply(t,[r,0].concat(Object(i.a)(l))),setTimeout((function(){n.key=+new Date,window.localStorage.setItem(n.tableConf.tableId,JSON.stringify(n.tableConf.colModel))}),500)}},handleFormatter:function(e,t,n){return n||0===n?n:"--"}}},f=(n("7331"),Object(c.a)(d,(function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"simple-table",attrs:{id:"tableWrapper"}},[n("el-table",{key:e.key,ref:e.tableConf.ref||"simpleTable",class:{drag_table:!0,scroll_table:e.isScroll,no_scroll_table:!e.isScroll},attrs:{height:e.tableConf.height,data:e.tableConf.data,"summary-method":e.tableConf.getSummaries,"span-method":e.tableConf.spanMethod,"show-summary":e.tableConf.showSummary||!1,"render-header":e.renderHeader,"row-class-name":e.rowClassName,"header-cell-class-name":e.headerCellClassName,"row-key":e.tableConf.rowKey||"id","expand-row-keys":e.tableConf.expandRowKeys||[],"cell-style":e.tableConf.cellStyle,"highlight-current-row":"",border:"",size:"medium",lazy:"",load:e.load},on:{"row-click":e.rowClick,"row-dblclick":e.rowDblclick,"sort-change":e.handleSortChange,"selection-change":e.handleSelectionChange,select:e.handleChange,"select-all":e.handleAllChange}},[e.tableConf.showSelection?n("el-table-column",{attrs:{selectable:e.selectable,"reserve-selection":e.tableConf.reserveSelection||!1,fixed:e.tableConf.showSelectionFixed||!1,type:"selection",align:"center",width:"50"}}):e._e(),e._v(" "),e._t("row-expand"),e._v(" "),e.tableConf.showIndex?n("el-table-column",{attrs:{align:"center",type:"index",label:"序号",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){t.row;var a=t.$index;return[n("span",[e._v(e._s((e.innerCurrentPage-1)*e.innerPageSize+a+1))])]}}],null,!1,3876586566)}):e._e(),e._v(" "),e._t("auditAndActiveState"),e._v(" "),e._l(e.tableConf.colModel,(function(t,a){return[t.hidden?e._e():e._t(t.prop,[t.hidden?e._e():n("el-table-column",{key:a,attrs:{"column-key":a.toString(),"render-header":e.renderHeader,prop:t.prop,fixed:t.fixed,label:t.label,align:t.align?t.align:"center",sortable:!!t.sortable&&t.sortable,"min-width":t.width?t.width:t.llScene?e.LlSceneMapList[t.llScene].width:"","show-overflow-tooltip":!0,formatter:t.formatter||(t.llFormatter?e.handleFormatter:void 0),"default-sort":t.defaultSort,"sort-orders":["ascending","descending",null]}})],{item:t,renderHeader:e.renderHeader,index:a.toString()})]})),e._v(" "),e._t("operation"),e._v(" "),n("div",{staticClass:"no-data",attrs:{slot:"empty"},slot:"empty"})],2),e._v(" "),e.paginationShow?n("pagination",{key:e.tableConf.paginationKey,ref:"pagination",attrs:{total:e.innerTotal,"page-size":e.innerPageSize,layout:e.pageLayout,"page-sizes":e.pagination.pageSizes,"current-page":e.innerCurrentPage},on:{"handle-size-change":e.handleSizeChange,"handle-current-change":e.handleCurrentChange}}):e._e()],1)}),[],!1,null,null,null));t.a=f.exports},"7b3d":function(e,t,n){"use strict";var a=n("5530"),i=(n("c5f6"),n("ed08")),r={name:"DebounceBtn",inheritAttrs:!1,props:{type:{type:String,default:"primary"},disabled:{type:Boolean,default:!1},delay:{type:Number,default:1e3}},data:function(){return{}},watch:{"$listeners.click":{handler:function(e,t){this.clickEve=Object(i.a)(this.$listeners.click,this.delay,!0),this.listenEve=Object(a.a)(Object(a.a)({},this.$listeners),{},{click:this.clickEve||function(){}})},immediate:!0}},mounted:function(){this.clickEve=Object(i.a)(this.$listeners.click,this.delay,!0)},beforeDestroy:function(){this.clickEve=null}},l=n("2877"),o=Object(l.a)(r,(function(){var e=this.$createElement;return(this._self._c||e)("el-button",this._g(this._b({attrs:{type:this.type,disabled:this.disabled}},"el-button",this.$attrs,!1),this.listenEve),[this._t("default")],2)}),[],!1,null,"3de9dfbc",null);t.a=o.exports},"7be7":function(e,t,n){},ad83:function(e,t,n){e.exports={menuBg:"#fff",menuText:"#343d54",menuActiveText:"#6BBAFF",sideBarWidth:"230px"}},c273:function(e,t,n){"use strict";n("456d"),n("386d"),n("6762"),n("2fdb"),n("96cf");var a,i,r,l=n("1da1"),o=(n("ac6a"),n("5530")),s={name:"MerchandiseTableDialog",components:{DataTable:n("7845").a},inheritAttrs:!1,props:{formItem:{type:Array,default:function(){return[]}},tableConf:{type:Object,default:function(){return{}},required:!0},queryListFn:{type:Function,default:function(){return function(){return{total:0,list:[]}}},required:!0},isHasCheckAll:{type:Boolean,default:!1},getParamsFn:{type:Function,default:function(e){return Object(o.a)({},e)}},rules:{type:Object,default:function(){return{}}},getResultData:{type:Function,default:function(e){var t=e.list,n=e.totalRecord;return{list:t,totalRecord:void 0===n?0:n}}}},data:function(){return{btnDis:!0,formModel:{},currentRow:null,total:0,isAllCheck:!1,isAwait:!1,selectArrLength:0,isDisAll:!1,selectData:[],loading:!1,loadData:[],allExceptSelectionRows:[]}},computed:{rowKey:function(){return this.tableConf.rowKey||"id"}},watch:{"$attrs.visible":function(e){var t=this;e&&this.$nextTick((function(){t.$refs.searchForm&&t.$refs.searchForm.handleReset(),t.total=0,t.tableConf.data=[]}))},selectData:{handler:function(e,t){return e&&0===e.length?this.btnDis=!0:this.btnDis=!1},deep:!0},formItem:{handler:function(){this.initFormModel()},deep:!0},queryListFn:{handler:function(){this.queryList()},deep:!0}},methods:{initFormModel:function(){var e=this;this.formModel={},this.formItem.forEach((function(t){var n=t.hasOwnProperty("formModelDefaultValue")?t.formModelDefaultValue:"";e.$set(e.formModel,t.prop,n)}))},queryList:(r=Object(l.a)(regeneratorRuntime.mark((function e(t){var n,a,i,r,l,s,c,u=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=3;break}return this.$refs.merchandiseTable&&this.$refs.merchandiseTable.init(),e.abrupt("return");case 3:return e.prev=3,this.loading=!0,n=t.pageSize,a=t.page,i=Object(o.a)(Object(o.a)({},this.formModel),{},{pageSize:n,pageNum:a}),i=this.getParamsFn(i),e.next=10,this.queryListFn(i);case 10:r=e.sent,l=this.getResultData(r),s=l.list,c=l.totalRecord,this.tableConf.data=s||[],this.total=c,this.isDisAll=0===this.total,this.loading=!1,s.length>0&&this.tableConf.showSelection&&(this.isAllCheck?(this.$refs.merchandiseTable.$refs[this.tableConf.ref].clearSelection(),this.tableConf.data.filter((function(e){return!u.getRowPrefArr(u.allExceptSelectionRows).includes(e[u.rowKey])})).forEach((function(e){u.$refs.merchandiseTable.$refs[u.tableConf.ref].toggleRowSelection(e,!0)}))):this.$nextTick((function(){var e=u.selectData.map((function(e){return e[u.rowKey]}));u.tableConf.data.forEach((function(t){e.includes(t[u.rowKey])&&u.$refs.merchandiseTable.$refs[u.tableConf.ref].toggleRowSelection(t,!0)}))}))),e.next=23;break;case 19:e.prev=19,e.t0=e.catch(3),this.loading=!1;case 23:case"end":return e.stop()}}),e,this,[[3,19]])}))),function(e){return r.apply(this,arguments)}),handleSelectionAll:function(e){},handleSelectionChange:function(e){this.isHasCheckAll||(this.selectData=e)},handleCancel:function(){this.selectData=[],this.$emit("update:visible",!1),this.$emit("handleCancel",!1)},handleSubmit:function(){this.currentRow&&this.$refs.merchandiseTable.$refs[this.tableConf.ref].setCurrentRow(),this.resetData(),this.queryList()},handleReset:function(){this.closeForModel(),this.initFormModel(),this.$emit("handleResetFormModel",!0),this.currentRow&&this.$refs.merchandiseTable.$refs[this.tableConf.ref].setCurrentRow(),this.resetData(),this.queryList()},selectAll:(i=Object(l.a)(regeneratorRuntime.mark((function e(t){var n,a,i,r=this;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t){e.next=13;break}return this.isAllCheck=!0,this.isAwait=!0,this.tableConf.data.forEach((function(e){r.$refs.merchandiseTable.$refs[r.tableConf.ref].toggleRowSelection(e,!0)})),n=Object(o.a)(Object(o.a)({},this.formModel),{},{rows:this.total,page:1}),n=this.getParamsFn(n),e.next=8,this.queryListFn(n);case 8:a=e.sent,(i=a.list)&&i.length>0&&(this.isAwait=!1,this.selectData=i),e.next=15;break;case 13:this.isAllCheck=!1,this.initialize();case 15:case"end":return e.stop()}}),e,this)}))),function(e){return i.apply(this,arguments)}),handleSelection:function(e){this.isHasCheckAll&&(this.filterSelection(e),this.isAllCheck=this.selectData.length===this.total)},getRowPrefArr:function(e){var t=this;return e.map((function(e){return e[t.rowKey]}))},filterSelection:function(e){var t=this,n=this.getRowPrefArr(e),a=this.tableConf.data.filter((function(e){return!n.includes(e[t.rowKey])}));this.allExceptSelectionRows=this.uniqueArr(this.allExceptSelectionRows.concat(a)),this.allExceptSelectionRows=this.allExceptSelectionRows.filter((function(n){return!t.getRowPrefArr(e).includes(n[t.rowKey])}));var i=this.getRowPrefArr(this.allExceptSelectionRows);this.selectData=this.selectData.filter((function(e){return!i.includes(e[t.rowKey])}));var r=this.getRowPrefArr(this.selectData),l=e.filter((function(e){return!r.includes(e[t.rowKey])}));this.selectData=this.selectData.concat(l)},initialize:function(){this.allExceptSelectionRows=[],this.selectData=[],this.$refs.merchandiseTable.$refs[this.tableConf.ref].clearSelection()},handleSearch:function(){this.search(),this.isAllCheck=!1,this.initialize()},uniqueArr:function(e){var t=this,n={};return e.filter((function(e){return!Object.keys(n).includes(e[t.rowKey])&&(n[e[t.rowKey]]=!0,!0)}))},rowDblclick:function(e){this.currentRow=e,this.$emit("rowDblclick",e)},rowClick:function(e){this.currentRow=e,this.$emit("rowClick",e),this.tableConf.showSelection||this.$set(this.selectData,0,e)},handleConfirm:(a=Object(l.a)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(this.selectData.length){e.next=3;break}return this.$message.error("请选择商品"),e.abrupt("return");case 3:this.$emit("confirm",{currentRow:this.currentRow,selectData:this.selectData,isAllCheck:this.isAllCheck}),this.$emit("update:visible",!1);case 5:case"end":return e.stop()}}),e,this)}))),function(){return a.apply(this,arguments)}),resetData:function(){this.currentRow=null,this.selectData=[],this.isAllCheck=!1},closeForModel:function(){if(Object.keys(this.formModel).length>=1)for(var e in this.formModel)Array.isArray(this.formModel[e])?this.formModel[e]=[]:null==this.formModel[e]?this.formModel[e]=void 0:this.formModel[e]=""},upDateTable:function(){this.queryList()}}},c=(n("d011"),n("2877")),u=Object(c.a)(s,(function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dialog",e._g(e._b({ref:"merchandiseTableDialog",staticClass:"g-dialog"},"el-dialog",e.$attrs,!1),e.$listeners),[n("ll-list-page-layout",[n("template",{slot:"nav-bar"},[e._t("tabTable")],2),e._v(" "),e.formItem.length>0?n("ll-search-form",{ref:"searchForm",attrs:{slot:"search-form",model:e.formModel,"form-items":e.formItem,rules:e.rules},on:{submit:e.handleSubmit,reset:e.handleReset},slot:"search-form"},[e._l(e.formItem.filter((function(e){return e.slotName})),(function(t){return n("template",{slot:"form-item-"+t.slotName},[e._t(t.slotName,null,{formModel:e.formModel})],2)}))],2):e._e(),e._v(" "),e.$attrs.visible?n("data-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],key:e.tableConf.tableId,ref:"merchandiseTable",attrs:{"row-click":e.rowClick,"row-dblclick":e.rowDblclick,"table-conf":e.tableConf,total:e.total},on:{"query-change":e.queryList,"handle-selection-change":e.handleSelectionChange,"select-all":e.handleSelectionAll,select:e.handleSelection}},[e.$scopedSlots.rowOperation?n("template",{slot:"operation"},[n("el-table-column",{attrs:{label:"操作","min-width":"100",align:"center",fixed:"right"},scopedSlots:e._u([{key:"default",fn:function(t){var n=t.row;return[e._t("rowOperation",null,{row:n})]}}],null,!0)})],1):e._e()],2):e._e()],2),e._v(" "),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[e.isHasCheckAll?n("el-checkbox",{attrs:{disabled:e.isDisAll},on:{change:e.selectAll},model:{value:e.isAllCheck,callback:function(t){e.isAllCheck=t},expression:"isAllCheck"}},[e._v("选择全部商品(已选择"+e._s(e.selectData.length)+"项)")]):e._e(),e._v(" "),n("el-button",{on:{click:e.handleCancel}},[e._v("取 消")]),e._v(" "),n("el-button",{attrs:{type:"primary",disabled:e.btnDis},on:{click:e.handleConfirm}},[e._v("确 定")])],1)],1)}),[],!1,null,"544abf6a",null);t.a=u.exports},d011:function(e,t,n){"use strict";n("7be7")}}]);