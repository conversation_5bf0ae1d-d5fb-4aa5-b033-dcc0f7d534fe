(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-ba7c2c3c"],{"386d":function(e,t,a){"use strict";var o=a("cb7c"),i=a("83a1"),r=a("5f1b");a("214f")("search",1,(function(e,t,a,n){return[function(a){var o=e(this),i=null==a?void 0:a[t];return void 0!==i?i.call(a,o):new RegExp(a)[t](String(o))},function(e){var t=n(a,e,this);if(t.done)return t.value;var l=o(e),d=String(this),s=l.lastIndex;i(s,0)||(l.lastIndex=0);var c=r(l,d);return i(l.lastIndex,s)||(l.lastIndex=s),null===c?-1:c.index}]}))},"422c":function(e,t,a){"use strict";a("eff1")},4810:function(e,t,a){},"65c0":function(e,t,a){"use strict";a("4810")},"83a1":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}},eff1:function(e,t,a){},f8aa:function(e,t,a){"use strict";a.r(t);var o,i=a("5530"),r=(a("96cf"),a("1da1")),n=a("7845"),l=a("3835"),d=[{label:"商品编码",prop:"organid",hidden:!1,width:100},{label:"商品名",prop:"organname",hidden:!1,width:200},{label:"国家医保编码",prop:"yibaoid",hidden:!1,width:120},{label:"注册名",prop:"yibaoname",hidden:!1,width:200},{label:"规格",prop:"quyu",hidden:!1,width:150},{label:"单位",prop:"quyuname",hidden:!1,width:200},{label:"批准文号",prop:"ip",hidden:!1,width:150},{label:"生产企业",prop:"ip",hidden:!1,width:150},{label:"库存数量",prop:"ip",hidden:!1,width:150}],s=[{label:"商品编号",prop:"organid",hidden:!1,width:100},{label:"商品名称",prop:"organname",hidden:!1,width:200},{label:"通用名称",prop:"yibaoid",hidden:!1,width:120},{label:"单位",prop:"yibaoname",hidden:!1,width:200},{label:"规格",prop:"quyu",hidden:!1,width:150},{label:"库存",prop:"quyuname",hidden:!1,width:200},{label:"生产厂家",prop:"ip",hidden:!1,width:150},{label:"批准文号",prop:"ip",hidden:!1,width:150},{label:"产地",prop:"ip",hidden:!1,width:150}],c=a("c273"),u=a("7b3d"),p=a("ed08"),h={name:"LicensedPharmacistDetail",components:{DebounceBtn:u.a,DataTable:n.a,merchandiseTableDialog:c.a},props:{isShow:{type:Boolean,default:!1},inventoryType:{type:String,default:"Add"},row:{type:Object,default:function(){return{}}}},data:function(){return{queryProductTemproryList:"",total:0,ruleForm:{employeeid:"",name:"",employeetype:"0",yaoshiid:"",zhucheid:""},rules:{employeeid:[{required:!0,message:"人员编码不能为空",trigger:"blur"}],name:[{required:!0,message:"人员名称不能为空",trigger:"blur"}]},formModel:{orderNo:"",dataRange:Object(p.e)(),employeetype:""},formItem:[{label:"盘点单号",prop:"orderNo",component:"el-input",attrs:{clearable:!1,placeholder:"按损溢单/源单号",disabled:!0},width:"250px"},{label:"盘点时间",prop:"dataRange",component:"el-date-picker",attrs:{type:"datetime",valueFormat:"yyyy-MM-dd HH:mm:ss",format:"yyyy-MM-dd HH:mm:ss",disabled:"Add"!==this.inventoryType},width:"300px"},{label:"备注",prop:"employeetype",component:"el-input",attrs:{disabled:!1}}],tableConf:{reserveSelection:!1,tableId:"FixedMedicalOrg",rowKey:"id",height:"100%",ref:"FixedMedicalOrg",showIndex:!0,showSelection:!1,currRow:{},data:[],colModel:d},formItemAddProdect:[{label:"商品信息",prop:"productName",component:"el-input",attrs:{placeholder:"商品编码/商品名称/国家编码",clearable:!0},width:"180px"},{label:"批准文号",prop:"productName",component:"el-input",attrs:{placeholder:"批准文号",clearable:!0},width:"180px"}],tableConfAddProdect:{rowKey:"productPref",showSelection:!0,reserveSelection:!0,tableId:"addProductDialog",height:"100%",ref:"addProductDialog",showIndex:!0,currRow:{},data:[],colModel:s},showProductDialog:!1,showBatchDialog:!1}},mounted:function(){var e=this;this.inventoryType,this.$nextTick((function(){e.queryList()}))},methods:{handleResetFormModel:function(){this.resetCustomType=[]},tmpExtractProduct:function(e){this.closeExtract()},closeExtract:function(){this.$refs.extractProductDialog.tableConf.data=[],this.$refs.extractProductDialog.total=0,this.showProductDialog=!1},getParamsFn:function(e){var t=Object(i.a)(Object(i.a)({},e),{},{sourceStore:this.formData.organSign?this.formData.organSign:"",productType:this.formModel.productType?this.formModel.productType:"",pageSize:e.rows,pageNum:e.page});return delete t.rows,delete t.page,t},rowDblclick:function(){},handleQuery:function(){this.queryList()},handleReset:function(){this.queryList()},getDetail:function(){},resetForm:function(){this.ruleForm={organid:"",organname:"",yibaoid:"",yibaoname:"",quyu:[],quyuname:"",ip:"",mac:"",tel:""}},goBack:function(){var e=this;if(!this.isEdit)return this.resetForm(),void this.$emit("hide",{flag:"detail"});this.$confirm("是否放弃本次操作？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.resetForm(),e.$emit("hide",{flag:"edit"})})).catch((function(){}))},handleSubmit:function(){var e=this;this.$refs.ruleForm.validate(function(){var t=Object(r.a)(regeneratorRuntime.mark((function t(a){var o,r,n,d,s,c,u,p;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:a&&(o=e.ruleForm.region||[void 0,void 0,void 0],r=Object(l.a)(o,3),n=r[0],d=void 0===n?{}:n,s=r[1],c=void 0===s?{}:s,u=r[2],p=void 0===u?{}:u,Object(i.a)(Object(i.a)({},e.ruleForm),{},{provinceCode:d.areaCode,cityCode:c.areaCode,areaCode:p.areaCode,provinceName:d.areaName,cityName:c.areaName,areaName:p.areaName}));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},queryList:(o=Object(r.a)(regeneratorRuntime.mark((function e(t){var a,o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=3;break}return this.$refs[this.tableConf.ref].init(),e.abrupt("return");case 3:a=t.pageSize,o=t.page,Object(i.a)(Object(i.a)({},this.formModel),{},{pageNo:o,pageSize:a}),this.loading=!0;try{this.loading=!1,this.tableConf.data=[{organid:"XD222222",organname:"测试测测测",yibaoid:"3242342342",yibaoname:"测试测试医保药店",quyu:"区域",quyuname:"区域佛山分散发",ip:"***********",mac:"SSDD-DDD-YIJ-OBJK",tel:"02733333988"}],this.total=total||0}catch(e){this.loading=!1}case 7:case"end":return e.stop()}}),e,this)}))),function(e){return o.apply(this,arguments)})}},f=(a("65c0"),a("2877")),b=Object(f.a)(h,(function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{staticClass:"g-fullscreen-dialog",attrs:{title:"",visible:e.isShow,"show-close":!1,modal:!1,"close-on-click-modal":!1,fullscreen:""},on:{"update:visible":function(t){e.isShow=t}}},[a("ll-list-page-layout",[a("div",{attrs:{slot:"nav-bar"},slot:"nav-bar"},[a("el-button",{on:{click:e.goBack}},[e._v("返回")]),e._v(" "),"Add"!==e.inventoryType?a("el-button",{attrs:{type:"primary"}},[e._v("保存")]):e._e(),e._v(" "),a("debounce-btn",{attrs:{type:"primary"},on:{click:function(t){return e.handleSubmit()}}},[e._v(" 提交 ")]),e._v(" "),"Add"!==e.inventoryType?a("debounce-btn",{attrs:{type:"primary"}},[e._v(" 显示差异数据 ")]):e._e()],1),e._v(" "),a("ll-search-form",{attrs:{slot:"search-form",model:e.formModel,"form-items":e.formItem,"label-width":"100px","has-reset":!1,"has-search":!1},on:{submit:e.handleQuery,reset:e.handleReset},slot:"search-form"}),e._v(" "),a("template",{slot:"action-bar_left"},[a("ll-button",{attrs:{delay:2e3,type:"primary"},on:{click:function(t){e.showProductDialog=!0}}},[e._v("添加商品")])],1),e._v(" "),a("data-table",{directives:[{name:"table-height",rawName:"v-table-height"}],key:e.tableConf.tableId,ref:e.tableConf.ref,attrs:{"table-conf":e.tableConf,total:e.total,"row-dblclick":e.rowDblclick},on:{"query-change":e.queryList}},[a("template",{slot:"operation"},[a("el-table-column",{attrs:{align:"center",fixed:"right",label:"操作",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return t.row,[a("ll-button",{attrs:{delay:2e3,type:"primary"}},[e._v("删除")])]}}])})],1)],2),e._v(" "),a("merchandise-table-dialog",{ref:"extractProductDialog",attrs:{"append-to-body":"","is-has-check-all":!1,"form-item":e.formItemAddProdect,visible:e.showProductDialog,"table-conf":e.tableConfAddProdect,title:"添加商品",width:"90vw",top:"50px","query-list-fn":e.queryProductTemproryList,"get-params-fn":e.getParamsFn},on:{"update:visible":function(t){e.showProductDialog=t},closed:e.closeExtract,confirm:e.tmpExtractProduct,handleResetFormModel:e.handleResetFormModel}})],2)],1)}),[],!1,null,"966023ec",null).exports,m={name:"FixedMedicalOrg",components:{DataTable:n.a,FixedMedicalOrgDetail:b},data:function(){return{InventoryType:"Add",dialogVisible:!1,isEdit:!1,detailDialogShow:!1,loading:!1,loadingText:"加载中···",total:0,formModel:{orderNo:"",dataRange:["",""],employeetype:""},row:{},formItem:[{label:"单据信息",prop:"orderNo",component:"el-input",attrs:{clearable:!1,placeholder:"按损溢单/源单号"},width:"250px"},{label:"盘点日期",prop:"dataRange",component:"ll-date-picker",attrs:{type:"date","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd",clearable:!1,width:"200px"},width:"330px",labelWidth:"80px"},{label:"盘点状态",prop:"employeetype",component:"ll-select",attrs:{options:[{label:"全部",value:""},{label:"盘点中",value:0},{label:"已完成",value:1}],clearable:!0,placeholder:"全部"}}],tableConf:{reserveSelection:!1,tableId:"FixedMedicalOrg",rowKey:"id",height:"100%",ref:"FixedMedicalOrg",showIndex:!0,showSelection:!1,currRow:{},data:[],colModel:[{label:"盘点单号",prop:"organid",hidden:!1,width:100},{label:"盘点日期",prop:"organname",hidden:!1,width:200},{label:"盘点状态",prop:"yibaoid",hidden:!1,width:120},{label:"创建人",prop:"yibaoname",hidden:!1,width:200},{label:"盘点人",prop:"quyu",hidden:!1,width:150},{label:"备注",prop:"ip",hidden:!1,width:150}]},selectData:[]}},computed:{},mounted:function(){var e=this;this.$nextTick((function(){e.queryList()}))},methods:{InventoryConfirm:function(){this.InventoryType="Confirm",this.detailDialogShow=!0},Add:function(){this.InventoryType="Add",this.detailDialogShow=!0},handleClose:function(){this.dialogVisible=!1},handleQuery:function(){this.queryList()},handleReset:function(){this.queryList()},queryList:function(){var e=Object(r.a)(regeneratorRuntime.mark((function e(t){var a,o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=3;break}return this.$refs[this.tableConf.ref].init(),e.abrupt("return");case 3:a=t.pageSize,o=t.page,Object(i.a)(Object(i.a)({},this.formModel),{},{pageNo:o,pageSize:a}),this.loading=!0;try{this.loading=!1,this.tableConf.data=[],this.total=total}catch(e){this.loading=!1}case 7:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),getDetail:function(e){this.detailDialogShow=!0,this.isEdit=!1,this.row=e},rowDblclick:function(e){this.detailDialogShow=!0,this.isEdit=!1,this.row=e},editRow:function(e){this.detailDialogShow=!0,this.isEdit=!0,this.row=e},hideDetailDialog:function(e){this.detailDialogShow=!1}}},y=(a("422c"),Object(f.a)(m,(function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ll-list-page-layout",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"g-list-page g-page-style",attrs:{"element-loading-text":e.loadingText}},[a("ll-search-form",{attrs:{slot:"search-form",model:e.formModel,"form-items":e.formItem,"label-width":"100px"},on:{submit:e.handleQuery,reset:e.handleReset},slot:"search-form"}),e._v(" "),a("template",{slot:"action-bar_left"},[a("ll-button",{attrs:{delay:2e3,type:"primary"},on:{click:e.Add}},[e._v("新增")]),e._v(" "),a("ll-button",{attrs:{delay:2e3,type:"primary"},on:{click:function(t){e.dialogVisible=!0}}},[e._v("全部盘点")])],1),e._v(" "),a("data-table",{directives:[{name:"table-height",rawName:"v-table-height"}],key:e.tableConf.tableId,ref:e.tableConf.ref,attrs:{"table-conf":e.tableConf,total:e.total,"row-dblclick":e.rowDblclick},on:{"query-change":e.queryList}},[a("template",{slot:"operation"},[a("el-table-column",{attrs:{label:"操作","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.InventoryConfirm(t.row)}}},[e._v("盘点确认")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.editRow(t.row)}}},[e._v("删除")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.getDetail(t.row)}}},[e._v("详情")])]}}])})],1)],2),e._v(" "),e.detailDialogShow?a("fixed-medical-org-detail",{attrs:{"is-show":e.detailDialogShow,"inventory-type":e.InventoryType,row:e.row},on:{hide:e.hideDetailDialog}}):e._e(),e._v(" "),a("el-dialog",{attrs:{title:"提示",visible:e.dialogVisible,width:"30%","before-close":e.handleClose},on:{"update:visible":function(t){e.dialogVisible=t}}},[a("div",{staticClass:"tipInfo"},[a("div",[e._v("盘点单已生成，单据编号:IPN15834228")]),e._v(" "),a("div",[e._v("温馨提示：盘点完成之前不要做入库或出库操作，否则可能会对盘点结果造成影响！")])]),e._v(" "),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){e.dialogVisible=!1}}},[e._v("我知道了")])],1)])],2)}),[],!1,null,"9aa68bfe",null));t.default=y.exports}}]);