(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-48d1ffd8"],{3072:function(e,t,n){"use strict";n.d(t,"b",(function(){return a})),n.d(t,"a",(function(){return o})),n.d(t,"f",(function(){return l})),n.d(t,"e",(function(){return i})),n.d(t,"d",(function(){return c})),n.d(t,"g",(function(){return u})),n.d(t,"c",(function(){return s}));var r=n("b775");function a(e){return Object(r.a)({url:"/medicare/merchant/listOrganSignByParam",method:"post",data:e}).then((function(e){return e.result}))}function o(e){return Object(r.a)({url:"/medicare/merchant/getById",method:"post",data:e}).then((function(e){return e.result}))}function l(e){return Object(r.a)({url:"/medicare/merchant/update",method:"post",data:e})}function i(e){return Object(r.a)({url:"/medicare/merchant/employee/queryByOrganSign",method:"post",data:e}).then((function(e){return e.result}))}function c(e){return Object(r.a)({url:"/medicare/merchant/employee/queryByEmployeeId",method:"post",data:e}).then((function(e){return e.result}))}function u(e){return Object(r.a)({url:"/medicare/merchant/employee/updateEmployee",method:"post",data:e}).then((function(e){return e.result}))}function s(e){return Object(r.a)({url:"/medicare/product/listProductCatalogByParam",method:"post",data:e}).then((function(e){return e.result}))}},"386d":function(e,t,n){"use strict";var r=n("cb7c"),a=n("83a1"),o=n("5f1b");n("214f")("search",1,(function(e,t,n,l){return[function(n){var r=e(this),a=null==n?void 0:n[t];return void 0!==a?a.call(n,r):new RegExp(n)[t](String(r))},function(e){var t=l(n,e,this);if(t.done)return t.value;var i=r(e),c=String(this),u=i.lastIndex;a(u,0)||(i.lastIndex=0);var s=o(i,c);return a(i.lastIndex,u)||(i.lastIndex=u),null===s?-1:s.index}]}))},"5c1a":function(e,t,n){},6560:function(e,t,n){},"6d0b":function(e,t,n){"use strict";n.d(t,"f",(function(){return a})),n.d(t,"e",(function(){return o})),n.d(t,"h",(function(){return l})),n.d(t,"d",(function(){return i})),n.d(t,"g",(function(){return c})),n.d(t,"b",(function(){return u})),n.d(t,"c",(function(){return s})),n.d(t,"a",(function(){return p}));var r=n("b775");function a(e){return Object(r.a)({url:"/medicare/offline/purchase/bill/pageQuery",method:"post",data:e}).then((function(e){return e}))}function o(e){return Object(r.a)({url:"/medicare/offline/purchase/bill/findDetails",method:"post",data:e}).then((function(e){return e}))}function l(e){return Object(r.a)({url:"/medicare/offline/provider/baseInfo/pageQuery",method:"post",data:e}).then((function(e){return e.result}))}function i(e){return Object(r.a)({url:"/medical/supervision/offline/inventory/list",method:"post",data:e}).then((function(e){return e.result}))}function c(e){return Object(r.a)({url:"/medicare/offline/purchase/bill/saveOrUpdate",method:"post",data:e}).then((function(e){return e.result}))}function u(e){return Object(r.a)({url:"/medicare/offline/purchase/bill/delete",method:"post",data:e}).then((function(e){return e.result}))}function s(e){return Object(r.a)({url:"/medicare/offline/purchase/bill/void",method:"post",data:e}).then((function(e){return e.result}))}function p(e){return Object(r.a)({url:"/medicare/offline/purchase/bill/approve",method:"post",data:e}).then((function(e){return e.result}))}},7137:function(e,t,n){"use strict";n("96cf");var r,a=n("1da1"),o=n("7845"),l=n("6d0b"),i={name:"SupplierDialog",components:{DataTable:o.a},props:{isShow:{type:Boolean,default:!1}},data:function(){return{pageTotal:0,selectItem:null,formData:{providerInfo:""},formItem:[{label:"供应商",prop:"providerInfo",component:"el-input",attrs:{placeholder:"",maxlength:30,clearable:!0}}],tableConf:{tableId:"SupplierDialog",ref:"SupplierDialog",height:"calc(80% - 20px)",showIndex:!0,showSelection:!1,data:[],colModel:[{label:"供应商编号",prop:"pharmacyPref",width:180,hidden:!1},{label:"供应商名称",prop:"providerName",width:180,hidden:!1},{label:"企业类型",prop:"providerTypeName",width:180,hidden:!1}]}}},mounted:function(){var e=this;this.$nextTick((function(){e.getList()}))},methods:{search:function(){this.getList()},getList:(r=Object(a.a)(regeneratorRuntime.mark((function e(t){var n,r,a,o,i,c;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=3;break}return this.$refs.dataTable.init(),e.abrupt("return");case 3:return n=t.pageSize,r=t.page,a={providerInfo:this.formData.providerInfo,pageSize:n,pageNum:r},e.next=7,Object(l.h)(a);case 7:o=e.sent,i=o.list,c=o.totalRecord,this.tableConf.data=i,this.pageTotal=c;case 12:case"end":return e.stop()}}),e,this)}))),function(e){return r.apply(this,arguments)}),hideDialog:function(){this.$emit("hide")},rowSelect:function(e){this.selectItem=e},determine:function(){this.selectItem?(this.$emit("updateSupplier",this.selectItem),this.hideDialog()):this.$message.warning("请选择供应商")}}},c=(n("aadf"),n("2877")),u=Object(c.a)(i,(function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dialog",{staticClass:"g-dialog",attrs:{title:"供应商信息",visible:e.isShow,"close-on-click-modal":!1,"before-close":e.hideDialog,width:"90%",top:"8vh"},on:{"update:visible":function(t){e.isShow=t}}},[n("ll-search-form",{attrs:{model:e.formData,"form-items":e.formItem,"label-width":"100px"},on:{submit:e.search,reset:e.search}}),e._v(" "),n("data-table",{ref:"dataTable",attrs:{"table-conf":e.tableConf,total:e.pageTotal,"row-click":e.rowSelect,"row-dblclick":e.determine},on:{"query-change":e.getList}}),e._v(" "),n("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{on:{click:e.hideDialog}},[e._v("取 消")]),e._v(" "),n("el-button",{attrs:{type:"primary"},on:{click:e.determine}},[e._v("确 定")])],1)],1)}),[],!1,null,"3460fed0",null);t.a=u.exports},"83a1":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}},"905b":function(e,t,n){"use strict";n.r(t);var r,a=n("5530"),o=(n("96cf"),n("1da1")),l=n("7845"),i=[{label:"单据信息",prop:"billNo",component:"el-input",attrs:{placholder:"入库单号"}},{label:"开始时间",prop:"startDate",component:"el-date-picker"},{label:"结束时间",prop:"endDate",component:"el-date-picker"},{label:"供应商",prop:"supplierName",component:"el-input"}],c=[{prop:"billNo",label:"入库单号",width:"180"},{prop:"billTime",label:"入库日期",width:"180"},{prop:"supplierName",label:"供应商",width:"180"},{prop:"billingUser",label:"采购员",width:"180"},{prop:"status",label:"审核状态",width:"180",formatter:function(e){return{0:"未提交",1:"已提交",2:"已审核",3:"已作废"}[e.status]}},{prop:"context",label:"采购内容",width:"180"},{prop:"remark",label:"备注",width:"180"}],u=[{label:"入库单号",prop:"billNo",component:"el-input",attrs:{placholder:"入库单号",disabled:!0}},{label:"入库时间",prop:"billTime",component:"el-date-picker",attrs:{}},{label:"供应商",prop:"supplierNo",slotName:"supplierNo"},{label:"供应商名称",prop:"supplierName",component:"el-input",attrs:{disabled:!0}},{label:"采购员",prop:"billingUser",component:"el-input"},{label:"备注",prop:"remark",component:"el-input"}],s=[{label:"商品编码",prop:"pharmacyPref",align:"center",width:"180px"},{label:"国家医保编码",prop:"projectCode",align:"center",width:"180px"},{label:"商品名",prop:"productName",align:"center",width:"180px"},{label:"规格",prop:"attributeSpecification",align:"center",width:"180px"},{label:"单位",prop:"unit",align:"center",width:"180px"},{label:"批准文号",prop:"approvalNNumber",align:"center",width:"180px"},{label:"生产企业",prop:"manufacturer",align:"center",width:"180px"},{label:"生产批号",prop:"lotNumber",align:"center",width:"180px"},{label:"生产日期",prop:"producedDate",align:"center",width:"180px"},{label:"有效期",prop:"expirationDate",align:"center",width:"180px"},{label:"处方药标志",prop:"prescriptionYn",align:"center",width:"180px",formatter:function(e){}},{label:"入库数量",prop:"changeNumber",align:"center",width:"180px"}],p=[{label:"商品名称",prop:"mixedQuery",component:"el-input",attrs:{placeholder:"商品编号/通用名称/商品名称/助记码"}},{label:"商品批号",prop:"lotNumber",component:"el-input"},{label:"原调剂申请单",prop:"originalAllocateBillNo",component:"el-input"}],d=n("3072"),f=n("c273"),h=n("7137"),m={components:{DataTable:l.a,MerchandiseTableDialog:f.a,SupplierDialog:h.a},data:function(){return{loading:!1,loadingText:"",showProductDialog:!1,showSupplierDialog:!1,formItems:u,currentRowIndex:0,productFormitems:p,formModel:{billNo:"",startDate:"",endDate:"",supplierName:""},productTableConf:{tableId:"productTableConf",height:"100%",ref:"productTableConf",showIndex:!0,rowKey:function(e){return"".concat(e.pharmacyPref,"-").concat(e.inventoryTracePref)},showSelection:!0,data:[],colModel:s,total:0},listProductCatalogByParam:d.c,tableConf:{reserveSelection:!1,tableId:"warehousingBill",rowKey:"id",height:"100%",ref:"warehousingBill",showIndex:!0,showSelection:!1,currRow:{},colModel:s,data:[{}],total:0}}},methods:{rowDbClick:function(){},handleClose:function(){this.$emit("close")},getSelectProduct:function(){},rowClick:function(e){this.currentRowIndex=e.index},queryList:function(e){e||this.$refs[this.tableConf.ref].init()},handleAdd:function(){this.showProductDialog=!0},updateSupplier:function(e){}}},b=(n("b68d"),n("2877")),g=Object(b.a)(m,(function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("ll-list-page-layout",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"g-list-page g-page-style",attrs:{"element-loading-text":e.loadingText}},[n("template",{slot:"nav-bar"},[n("el-button",{on:{click:e.handleClose}},[e._v("取消")]),e._v(" "),n("el-button",{attrs:{type:"primary"}},[e._v("提交")]),e._v(" "),n("el-button",{attrs:{type:"primary"}},[e._v("保存")])],1),e._v(" "),n("ll-form",{attrs:{slot:"search-form","form-items":e.formItems,model:e.formModel},slot:"search-form"},[n("template",{slot:"form-item-supplierNo"},[n("el-input",{attrs:{readonly:!0},nativeOn:{click:function(t){e.showSupplierDialog=!0}}})],1)],2),e._v(" "),n("template",{slot:"action-bar_left"},[n("el-button",{attrs:{type:"primary"},on:{click:e.handleAdd}},[e._v("新增行")])],1),e._v(" "),n("data-table",{directives:[{name:"table-height",rawName:"v-table-height"}],key:e.tableConf.tableId,ref:e.tableConf.ref,attrs:{"table-conf":e.tableConf,total:e.tableConf.total,"row-dblclick":e.rowDbClick,"row-click":e.rowClick},on:{"query-change":e.queryList}},[n("template",{slot:"pharmacyPref"},[n("el-table-column",{attrs:{label:"商品编码",width:"180",prop:"pharmacyPref"}},[[n("span",{on:{click:function(t){e.showProductDialog=!0}}},[e._v("商品编码")])]],2)],1),e._v(" "),n("template",{slot:"lotNumber"},[n("el-table-column",{attrs:{label:"生产批号",width:"180",prop:"lotNumber"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[r.index===e.currentRowIndex?n("el-input",{model:{value:r.lotNumber,callback:function(t){e.$set(r,"lotNumber",t)},expression:"row.lotNumber"}}):n("span",[e._v(e._s(r.lotNumber))])]}}])})],1),e._v(" "),n("template",{slot:"producedDate"},[n("el-table-column",{attrs:{label:"生产日期",width:"180",prop:"producedDate"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[r.index===e.currentRowIndex?n("el-date-picker",{model:{value:r.producedDate,callback:function(t){e.$set(r,"producedDate",t)},expression:"row.producedDate"}}):n("span",[e._v(e._s(r.lotNumber))])]}}])})],1),e._v(" "),n("template",{slot:"expirationDate"},[n("el-table-column",{attrs:{label:"有效期",width:"180",prop:"expirationDate"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[r.index===e.currentRowIndex?n("el-date-picker",{model:{value:r.expirationDate,callback:function(t){e.$set(r,"expirationDate",t)},expression:"row.expirationDate"}}):n("span",[e._v(e._s(r.expirationDate))])]}}])})],1),e._v(" "),n("template",{slot:"changeNumber"},[n("el-table-column",{attrs:{label:"入库数量",width:"180",prop:"changeNumber"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[r.index===e.currentRowIndex?n("el-input",{model:{value:r.changeNumber,callback:function(t){e.$set(r,"changeNumber",t)},expression:"row.changeNumber"}}):e._e()]}}])})],1)],2),e._v(" "),n("merchandise-table-dialog",{attrs:{title:"添加商品",width:"80%",visible:e.showProductDialog,"form-item":e.productFormitems,"table-conf":e.productTableConf,"query-list-fn":e.listProductCatalogByParam},on:{"update:visible":function(t){e.showProductDialog=t},confirm:e.getSelectProduct}}),e._v(" "),e.showSupplierDialog?n("supplier-dialog",{attrs:{"is-show":e.showSupplierDialog},on:{hide:function(t){e.showSupplierDialog=!1},updateSupplier:e.updateSupplier}}):e._e()],2)}),[],!1,null,"4d453590",null).exports,w=n("6d0b"),v={components:{DataTable:l.a,Detail:g},data:function(){return{loading:!1,loadingText:"",showDetail:!1,formItems:i,formModel:{billNo:"",startDate:"",endDate:"",supplierName:""},tableConf:{reserveSelection:!1,tableId:"warehousingBill",rowKey:"id",height:"100%",ref:"warehousingBill",showIndex:!0,showSelection:!1,currRow:{},colModel:c,data:[{}],total:0}}},mounted:function(){this.queryList()},methods:{rowDbClick:function(){},handleFormSubmit:function(){},handleReset:function(){this.queryList()},queryList:(r=Object(o.a)(regeneratorRuntime.mark((function e(t){var n,r,o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=3;break}return this.$refs[this.tableConf.ref].init(),e.abrupt("return");case 3:return e.prev=3,n=t.page,r=t.pageSize,o=Object(a.a)(Object(a.a)({},this.formModel),{},{page:n,pageSize:r}),e.next=9,Object(w.f)(o);case 9:e.next=14;break;case 11:e.prev=11,e.t0=e.catch(3);case 14:case"end":return e.stop()}}),e,this,[[3,11]])}))),function(e){return r.apply(this,arguments)}),handleEdit:function(){},handleDetail:function(){},handleDelete:function(){}}},x=Object(b.a)(v,(function(){var e=this,t=e.$createElement,n=e._self._c||t;return e.showDetail?n("detail",{on:{close:function(t){e.showDetail=!1}}}):n("ll-list-page-layout",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"g-list-page g-page-style",attrs:{"element-loading-text":e.loadingText}},[n("ll-search-form",{attrs:{slot:"search-form",model:e.formModel,"form-items":e.formItem},on:{submit:e.handleFormSubmit,reset:e.handleFormSubmit},slot:"search-form"}),e._v(" "),n("template",{slot:"action-bar_left"},[n("el-button",{attrs:{type:"primary"},on:{click:function(t){e.showDetail=!0}}},[e._v("新增")])],1),e._v(" "),n("data-table",{directives:[{name:"table-height",rawName:"v-table-height"}],key:e.tableConf.tableId,ref:e.tableConf.ref,attrs:{"table-conf":e.tableConf,total:e.tableConf.total,"row-dblclick":e.rowDbClick},on:{"query-change":e.queryList}},[n("template",{slot:"operation"},[n("el-table-column",{attrs:{align:"center",fixed:"right",label:"操作",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[[n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleDetail(r)}}},[e._v(" 审核 ")])],e._v(" "),[n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleDetail(r)}}},[e._v(" 作废 ")])],e._v(" "),[n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleEdit(r)}}},[e._v(" 编辑 ")])],e._v(" "),[n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleDelete(r)}}},[e._v(" 删除 ")])],e._v(" "),[n("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleDetail(r)}}},[e._v(" 详情 ")])]]}}],null,!1,2418209585)})],1)],2)],2)}),[],!1,null,"2d6e565f",null);t.default=x.exports},aadf:function(e,t,n){"use strict";n("5c1a")},b68d:function(e,t,n){"use strict";n("6560")}}]);