(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-6e49a70d"],{3072:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"a",(function(){return l})),a.d(t,"f",(function(){return i})),a.d(t,"e",(function(){return o})),a.d(t,"d",(function(){return d})),a.d(t,"g",(function(){return s})),a.d(t,"c",(function(){return c}));var r=a("b775");function n(e){return Object(r.a)({url:"/medicare/merchant/listOrganSignByParam",method:"post",data:e}).then((function(e){return e.result}))}function l(e){return Object(r.a)({url:"/medicare/merchant/getById",method:"post",data:e}).then((function(e){return e.result}))}function i(e){return Object(r.a)({url:"/medicare/merchant/update",method:"post",data:e})}function o(e){return Object(r.a)({url:"/medicare/merchant/employee/queryByOrganSign",method:"post",data:e}).then((function(e){return e.result}))}function d(e){return Object(r.a)({url:"/medicare/merchant/employee/queryByEmployeeId",method:"post",data:e}).then((function(e){return e.result}))}function s(e){return Object(r.a)({url:"/medicare/merchant/employee/updateEmployee",method:"post",data:e}).then((function(e){return e.result}))}function c(e){return Object(r.a)({url:"/medicare/product/listProductCatalogByParam",method:"post",data:e}).then((function(e){return e.result}))}},"62c9":function(e,t,a){},e1aa:function(e,t,a){"use strict";a.r(t);var r,n,l=a("5530"),i=(a("96cf"),a("1da1")),o=a("7845"),d=(a("20d6"),a("7f7f"),a("6762"),a("2fdb"),a("2909")),s={props:{dataOption:{type:Array,default:function(){return[]}},filterable:{type:Boolean,default:!1},defaultValue:{type:Array,default:function(){return[]}}},data:function(){return{orgArr:[],selectValue:[],allId:"",showItems:!1,showAll:!0}},watch:{dataOption:function(e,t){this.selectValue=e.map((function(e){return e.id})),this.allId=this.selectValue[0];var a=this.defaultValue.length;a&&a!==this.selectValue.length-1&&(this.selectValue=Object(d.a)(this.defaultValue)),this.orgArr=this.selectValue,this.handlComp(this.selectValue),this.$emit("getData",this.deleAllId(this.selectValue),e.length===this.selectValue.length)}},methods:{getStrlen:function(e){for(var t={len:0},a=/[\u4e00-\u9fa5]/,r=0;r<e.length;r++)a.test(e.charAt(r))&&t.len++;return t.len+e.length},handlComp:function(e){if(e.length){e.includes(this.allId)?this.showAll=!0:this.showAll=!1;var t=this.dataOption.filter((function(t){return t.id===e[0]}));this.getStrlen(t[0].name)>=11?this.showItems=!0:this.showItems=!1}},deleAllId:function(e){var t=JSON.parse(JSON.stringify(e)),a=t.indexOf(this.allId);return-1!==a&&t.splice(a,1),t},changeitem:function(e,t){var a=this,r=this.dataOption.map((function(e){return e.id}));this.orgArr.includes(this.allId)?t.includes(this.allId)?(t.splice(t.findIndex((function(e){return e===a.allId})),1),this.orgArr=t,this.selectValue=this.orgArr):(this.orgArr=[],this.selectValue=[]):(t.includes(this.allId)||t.length===this.dataOption.length-1)&&(this.orgArr=r,this.selectValue=r),this.handlComp(this.selectValue),this.$emit("getData",this.deleAllId(this.selectValue),this.dataOption.length===this.selectValue.length)},refData:function(){this.selectValue=this.dataOption.map((function(e){return e.id}));var e=this.defaultValue.length;e&&e!==this.selectValue.length-1&&(this.selectValue=Object(d.a)(this.defaultValue)),this.orgArr=this.selectValue,this.handlComp(this.selectValue),this.$emit("getData",this.deleAllId(this.selectValue),this.dataOption.length===this.selectValue.length)}}},c=(a("fef6"),a("2877")),u=Object(c.a)(s,(function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"selectNore"},[a("el-select",{class:e.showItems?"selectOne":e.showAll?"selectAll":"selectItem",attrs:{multiple:"",filterable:e.filterable,placeholder:"请选择"},on:{change:function(t){return e.changeitem(t,e.selectValue)}},model:{value:e.selectValue,callback:function(t){e.selectValue=t},expression:"selectValue"}},e._l(e.dataOption,(function(e){return a("el-option",{key:e.id,attrs:{label:e.name,value:e.id}})})),1)],1)}),[],!1,null,"1e757b4b",null).exports,h=[{label:"商品编码",prop:"pharmacyPref",hidden:!1,width:120},{label:"商品名称",prop:"productName",hidden:!1,width:180},{label:"通用名称",prop:"commonName",hidden:!1,width:180},{label:"规格",prop:"attributeSpecification",hidden:!1,width:120},{label:"单位",prop:"unit",hidden:!1,width:100},{label:"剂型",prop:"dosageForm",hidden:!1,width:80},{label:"生产厂家",prop:"manufacturer",hidden:!1,width:150},{label:"条形码",prop:"barCode",hidden:!1,width:120},{label:"批准文号",prop:"approvalNumber",hidden:!1,width:200},{label:"上市许可人",prop:"drugPermissionPerson",hidden:!1,width:120},{label:"商品分类",prop:"productType",hidden:!1,width:120},{label:"国家药品编码",prop:"projectCode",hidden:!1,width:200},{label:"注册名称",prop:"registeredProductName",hidden:!1,width:150},{label:"国家商品名称",prop:"projectName",hidden:!1,width:150},{label:"注册规格",prop:"registeredAttributeSpecification",hidden:!1,width:100},{label:"注册剂型",prop:"registeredDosageForm",hidden:!1,width:100},{label:"包装材质",prop:"packagingMaterial",hidden:!1,width:100},{label:"最小包装数量",prop:"minimumPackageNumber",hidden:!1,width:100},{label:"最小制剂单位",prop:"minimumPreparationUnit",hidden:!1,width:100},{label:"最小包装单位",prop:"minimumPackageUnit",hidden:!1,width:100},{label:"药品企业",prop:"pharmaceuticalEnterprises",hidden:!1,width:200},{label:"药品本位码",prop:"standardCode",hidden:!1,width:120},{label:"甲乙类",prop:"projectLevel",hidden:!1,width:100},{label:"医保限价",prop:"productFixedPrice",hidden:!1,width:120},{label:"处方药标识",prop:"prescriptionFlag",hidden:!1,width:100}],p=a("2934"),f=a("3072"),m={components:{DataTable:o.a,selectMore:u},data:function(){return{loading:!1,loadingText:"加载中···",total:0,formModel:{productName:"",orderNo1:"",productTypeCodes:[]},options:{productTypeOption:[]},formItem:[{label:"商品信息",prop:"productName",component:"el-input",attrs:{clearable:!1,placeholder:"商品编号/商品名称/通用名"},width:"220px"},{label:"批准文号",prop:"approvalNumber",component:"el-input",attrs:{clearable:!1},width:"200px"},{label:"生产厂家",prop:"manufacturer",component:"el-input",attrs:{clearable:!1},width:"200px"},{label:"商品分类",prop:"productTypeCodes",component:"ll-select",slotName:"productTypeCodes"}],tableConf:{reserveSelection:!1,tableId:"ProductInfo",rowKey:"productPref",height:"100%",ref:"ProductInfo",showIndex:!0,showSelection:!1,currRow:{},data:[],colModel:h},selectData:[]}},computed:{},created:function(){this.getProductType()},mounted:function(){var e=this;this.$nextTick((function(){e.queryList()}))},methods:{getProductType:(n=Object(i.a)(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(p.a)({categoryCode:"productType"});case 3:t=e.sent,this.options.productTypeOption=[{name:"全部",id:"all"}].concat(t.map((function(e){return{name:e.value,id:e.code}}))),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0);case 10:case"end":return e.stop()}}),e,this,[[0,7]])}))),function(){return n.apply(this,arguments)}),handleQuery:function(){this.queryList()},handleReset:function(){this.$refs.selectMore.refData(),this.queryList()},selectProductTypeChange:function(e){this.formModel.productTypeCodes=e},queryList:(r=Object(i.a)(regeneratorRuntime.mark((function e(t){var a,r,n,i,o,d;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=3;break}return this.$refs[this.tableConf.ref].init(),e.abrupt("return");case 3:return a=t.pageSize,r=t.page,n=Object(l.a)(Object(l.a)({},this.formModel),{},{pageNum:r,pageSize:a}),this.loading=!0,e.prev=6,e.next=9,Object(f.c)(n);case 9:i=e.sent,o=i.list,d=i.totalRecord,this.loading=!1,this.tableConf.data=o||[],this.total=d,e.next=20;break;case 17:e.prev=17,e.t0=e.catch(6),this.loading=!1;case 20:case"end":return e.stop()}}),e,this,[[6,17]])}))),function(e){return r.apply(this,arguments)})}},b=Object(c.a)(m,(function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ll-list-page-layout",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"g-list-page g-page-style",attrs:{"element-loading-text":e.loadingText}},[a("ll-search-form",{attrs:{slot:"search-form",model:e.formModel,"form-items":e.formItem,"label-width":"100px"},on:{submit:e.handleQuery,reset:e.handleReset},slot:"search-form"},[a("template",{slot:"form-item-productTypeCodes"},[a("select-more",{ref:"selectMore",attrs:{filterable:"","data-option":e.options.productTypeOption},on:{getData:e.selectProductTypeChange}})],1)],2),e._v(" "),a("data-table",{directives:[{name:"table-height",rawName:"v-table-height"}],key:e.tableConf.tableId,ref:e.tableConf.ref,attrs:{"table-conf":e.tableConf,total:e.total},on:{"query-change":e.queryList}})],1)}),[],!1,null,"db718ac6",null);t.default=b.exports},fef6:function(e,t,a){"use strict";a("62c9")}}]);