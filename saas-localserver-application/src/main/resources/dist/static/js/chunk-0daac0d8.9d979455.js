(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-0daac0d8"],{"04d3":function(t,s,a){"use strict";a("2bba")},"255e":function(t,s,a){t.exports=a.p+"static/img/welcome.0eba71cf.png"},"2bba":function(t,s,a){},"7abe":function(t,s,a){"use strict";a.r(s);var e=[function(){var t=this.$createElement,s=this._self._c||t;return s("div",{staticClass:"dashboard-container"},[s("div",{staticClass:"welcome flex"},[s("div",{staticClass:"align-center"},[s("h3",{staticClass:"f24"},[this._v("欢迎使用智慧脸药店管理系统")]),this._v(" "),s("div",{staticClass:"h10"}),this._v(" "),s("img",{attrs:{src:a("255e"),alt:""}})])])])}],i={name:"home",data:function(){return{}}},n=(a("04d3"),a("2877")),c=Object(n.a)(i,(function(){this.$createElement;return this._self._c,this._m(0)}),e,!1,null,"43685a2a",null);s.default=c.exports}}]);