(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-1132c2fc"],{"1db4":function(t,s,i){"use strict";i.r(s);var a=[function(){var t=this.$createElement,s=this._self._c||t;return s("div",{staticClass:"pic-404"},[s("img",{staticClass:"pic-404__parent",attrs:{src:i("a36b"),alt:"404"}}),this._v(" "),s("img",{staticClass:"pic-404__child left",attrs:{src:i("26fc"),alt:"404"}}),this._v(" "),s("img",{staticClass:"pic-404__child mid",attrs:{src:i("26fc"),alt:"404"}}),this._v(" "),s("img",{staticClass:"pic-404__child right",attrs:{src:i("26fc"),alt:"404"}})])}],c={name:"Page404",computed:{message:function(){return"页面不存在"}}},l=(i("7ecd"),i("2877")),n=Object(l.a)(c,(function(){var t=this,s=t.$createElement,i=t._self._c||s;return i("div",{staticClass:"wscn-http404-container"},[i("div",{staticClass:"wscn-http404"},[t._m(0),t._v(" "),i("div",{staticClass:"bullshit"},[i("div",{staticClass:"bullshit__oops"},[t._v("警告")]),t._v(" "),i("div",{staticClass:"bullshit__headline"},[t._v(t._s(t.message))]),t._v(" "),i("div",{staticClass:"bullshit__info"},[t._v("请检查您输入的地址是否正确或点击按钮返回首页")]),t._v(" "),i("router-link",{staticClass:"bullshit__return-home",attrs:{to:"/"}},[t._v("\n        返回首页\n      ")])],1)])])}),a,!1,null,"480aa2d3",null);s.default=n.exports},"26fc":function(t,s,i){t.exports=i.p+"static/img/404_cloud.0f4bc32b.png"},"4a96":function(t,s,i){},"7ecd":function(t,s,i){"use strict";i("4a96")},a36b:function(t,s,i){t.exports=i.p+"static/img/404.a57b6f31.png"}}]);