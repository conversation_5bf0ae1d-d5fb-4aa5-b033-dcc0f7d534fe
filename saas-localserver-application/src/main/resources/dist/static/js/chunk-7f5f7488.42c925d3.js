(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-7f5f7488"],{"386d":function(e,t,r){"use strict";var a=r("cb7c"),n=r("83a1"),l=r("5f1b");r("214f")("search",1,(function(e,t,r,o){return[function(r){var a=e(this),n=null==r?void 0:r[t];return void 0!==n?n.call(r,a):new RegExp(r)[t](String(a))},function(e){var t=o(r,e,this);if(t.done)return t.value;var i=a(e),c=String(this),s=i.lastIndex;n(s,0)||(i.lastIndex=0);var u=l(i,c);return n(i.lastIndex,s)||(i.lastIndex=s),null===u?-1:u.index}]}))},"5c1a":function(e,t,r){},"6d0b":function(e,t,r){"use strict";r.d(t,"f",(function(){return n})),r.d(t,"e",(function(){return l})),r.d(t,"h",(function(){return o})),r.d(t,"d",(function(){return i})),r.d(t,"g",(function(){return c})),r.d(t,"b",(function(){return s})),r.d(t,"c",(function(){return u})),r.d(t,"a",(function(){return p}));var a=r("b775");function n(e){return Object(a.a)({url:"/medicare/offline/purchase/bill/pageQuery",method:"post",data:e}).then((function(e){return e}))}function l(e){return Object(a.a)({url:"/medicare/offline/purchase/bill/findDetails",method:"post",data:e}).then((function(e){return e}))}function o(e){return Object(a.a)({url:"/medicare/offline/provider/baseInfo/pageQuery",method:"post",data:e}).then((function(e){return e.result}))}function i(e){return Object(a.a)({url:"/medical/supervision/offline/inventory/list",method:"post",data:e}).then((function(e){return e.result}))}function c(e){return Object(a.a)({url:"/medicare/offline/purchase/bill/saveOrUpdate",method:"post",data:e}).then((function(e){return e.result}))}function s(e){return Object(a.a)({url:"/medicare/offline/purchase/bill/delete",method:"post",data:e}).then((function(e){return e.result}))}function u(e){return Object(a.a)({url:"/medicare/offline/purchase/bill/void",method:"post",data:e}).then((function(e){return e.result}))}function p(e){return Object(a.a)({url:"/medicare/offline/purchase/bill/approve",method:"post",data:e}).then((function(e){return e.result}))}},7137:function(e,t,r){"use strict";r("96cf");var a,n=r("1da1"),l=r("7845"),o=r("6d0b"),i={name:"SupplierDialog",components:{DataTable:l.a},props:{isShow:{type:Boolean,default:!1}},data:function(){return{pageTotal:0,selectItem:null,formData:{providerInfo:""},formItem:[{label:"供应商",prop:"providerInfo",component:"el-input",attrs:{placeholder:"",maxlength:30,clearable:!0}}],tableConf:{tableId:"SupplierDialog",ref:"SupplierDialog",height:"calc(80% - 20px)",showIndex:!0,showSelection:!1,data:[],colModel:[{label:"供应商编号",prop:"pharmacyPref",width:180,hidden:!1},{label:"供应商名称",prop:"providerName",width:180,hidden:!1},{label:"企业类型",prop:"providerTypeName",width:180,hidden:!1}]}}},mounted:function(){var e=this;this.$nextTick((function(){e.getList()}))},methods:{search:function(){this.getList()},getList:(a=Object(n.a)(regeneratorRuntime.mark((function e(t){var r,a,n,l,i,c;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=3;break}return this.$refs.dataTable.init(),e.abrupt("return");case 3:return r=t.pageSize,a=t.page,n={providerInfo:this.formData.providerInfo,pageSize:r,pageNum:a},e.next=7,Object(o.h)(n);case 7:l=e.sent,i=l.list,c=l.totalRecord,this.tableConf.data=i,this.pageTotal=c;case 12:case"end":return e.stop()}}),e,this)}))),function(e){return a.apply(this,arguments)}),hideDialog:function(){this.$emit("hide")},rowSelect:function(e){this.selectItem=e},determine:function(){this.selectItem?(this.$emit("updateSupplier",this.selectItem),this.hideDialog()):this.$message.warning("请选择供应商")}}},c=(r("aadf"),r("2877")),s=Object(c.a)(i,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-dialog",{staticClass:"g-dialog",attrs:{title:"供应商信息",visible:e.isShow,"close-on-click-modal":!1,"before-close":e.hideDialog,width:"90%",top:"8vh"},on:{"update:visible":function(t){e.isShow=t}}},[r("ll-search-form",{attrs:{model:e.formData,"form-items":e.formItem,"label-width":"100px"},on:{submit:e.search,reset:e.search}}),e._v(" "),r("data-table",{ref:"dataTable",attrs:{"table-conf":e.tableConf,total:e.pageTotal,"row-click":e.rowSelect,"row-dblclick":e.determine},on:{"query-change":e.getList}}),e._v(" "),r("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:e.hideDialog}},[e._v("取 消")]),e._v(" "),r("el-button",{attrs:{type:"primary"},on:{click:e.determine}},[e._v("确 定")])],1)],1)}),[],!1,null,"3460fed0",null);t.a=s.exports},"83a1":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}},aadf:function(e,t,r){"use strict";r("5c1a")},acdb:function(e,t,r){"use strict";r("bd48")},bd48:function(e,t,r){},d990:function(e,t,r){"use strict";r.r(t);var a,n,l,o=r("5530"),i=(r("96cf"),r("1da1")),c=r("7845"),s=[{label:"单据信息",prop:"billNo",component:"el-input",attrs:{placholder:"入库单号"}},{label:"开始时间",prop:"startDate",component:"el-date-picker",attrs:{"value-format":"yyyy-MM-dd"}},{label:"结束时间",prop:"endDate",component:"el-date-picker",attrs:{"value-format":"yyyy-MM-dd"}},{label:"供应商",prop:"supplierName",component:"el-input"}],u=[{prop:"billNo",label:"入库单号",width:"180"},{prop:"billTime",label:"入库日期",width:"180"},{prop:"supplierName",label:"供应商",width:"180"},{prop:"billingUser",label:"采购员",width:"180"},{prop:"status",label:"审核状态",width:"180",formatter:function(e){return{0:"未提交",1:"已提交",2:"已审核",3:"已作废"}[e.status]}},{prop:"context",label:"采购内容",width:"180"},{prop:"remarks",label:"备注",width:"180"}],p=[{label:"商品编码",prop:"pharmacyPref",align:"center",width:"180px"},{label:"国家医保编码",prop:"projectCode",align:"center",width:"180px"},{label:"商品名",prop:"productName",align:"center",width:"180px"},{label:"规格",prop:"attributeSpecification",align:"center",width:"180px"},{label:"单位",prop:"unit",align:"center",width:"180px"},{label:"批准文号",prop:"approvalNumber",align:"center",width:"180px"},{label:"生产企业",prop:"manufacturer",align:"center",width:"180px"},{label:"生产批号",prop:"lotNumber",align:"center",width:"180px"},{label:"生产日期",prop:"producedDate",align:"center",width:"180px"},{label:"有效期",prop:"expirationDate",align:"center",width:"180px"},{label:"处方药标志",prop:"prescriptionYn",align:"center",width:"180px",formatter:function(e){}},{label:"入库数量",prop:"changeNumber",align:"center",width:"180px"}],d=[{label:"商品信息",prop:"productInfo",component:"el-input",attrs:{placeholder:"商品编号/通用名称/商品名称/助记码"}},{label:"批准文号",prop:"approvalNumber",component:"el-input",attrs:{placeholder:"批准文号"}}],f=[{label:"商品编号",prop:"pharmacyPref",hidden:!1,align:"left",width:150},{label:"商品名称",prop:"productName",hidden:!1,align:"left",width:150},{label:"通用名称",prop:"commonName",hidden:!1,align:"left",width:150},{label:"单位",prop:"unit",hidden:!1,align:"left",width:150},{label:"规格",prop:"attributeSpecification",hidden:!1,align:"left",width:150},{label:"库存",prop:"stockNumber",hidden:!1,align:"left",width:150},{label:"生产厂家",prop:"manufacturer",hidden:!1,align:"left",width:150},{label:"批准文号",prop:"approvalNumber",hidden:!1,align:"left",width:150},{label:"产地",prop:"producingArea",hidden:!1,align:"left",width:150}],h=r("b85c"),b=(r("456d"),r("20d6"),r("ac6a"),r("ed08")),m=r("5f87"),g=r("c273"),v=r("7137"),w=r("6d0b"),x={components:{DataTable:c.a,MerchandiseTableDialog:g.a,SupplierDialog:v.a},props:{bill:{type:Object,default:function(){return{}}},pageType:{type:String,default:"add"}},data:function(){return{loading:!1,loadingText:"",showProductDialog:!1,showSupplierDialog:!1,formItems:[{label:"入库单号",prop:"billNo",component:"el-input",attrs:{placholder:"入库单号",disabled:!0}},{label:"入库时间",prop:"billTime",component:"el-date-picker",attrs:{disabled:!1,type:"datetime","value-format":"yyyy-MM-dd hh:mm:ss"}},{label:"供应商",prop:"supplierNo",slotName:"supplierNo",attrs:{disabled:!0}},{label:"供应商名称",prop:"supplierName",component:"el-input",attrs:{disabled:!0}},{label:"采购员",prop:"billingUser",component:"el-input",attrs:{disabled:!1}},{label:"备注",prop:"remarks",component:"el-input",attrs:{disabled:!1}}],rules:{supplierNo:[{required:!0,message:"该字段必填",trigger:"blur"}],supplierName:[{required:!0,message:"该字段必填",trigger:"blur"}],billingUser:[{required:!0,message:"该字段必填",trigger:"blur"}]},currentRowIndex:0,productFormitems:d,formModel:{billNo:"",billTime:Object(b.e)(),supplierNo:"",billingUser:Object(m.c)(),supplierName:"",remarks:""},productTableConf:{tableId:"productTableConf",height:"100%",ref:"productTableConf",showIndex:!0,rowKey:function(e){return"".concat(e.pharmacyPref,"-").concat(e.inventoryTracePref)},showSelection:!0,data:[],colModel:f,total:0},inventoryProductList:w.d,tableConf:{reserveSelection:!1,tableId:"warehousingBill",rowKey:"id",height:"100%",ref:"warehousingBill",showIndex:!0,showSelection:!1,currRow:{},colModel:p,data:[],total:0},errorIndex:[],errorMsg:""}},computed:{isDetail:function(){return"detail"===this.pageType}},created:function(){this.isDetail&&this.formItems.forEach((function(e){e.attrs.disabled=!0})),"add"!==this.pageType&&this.getDetailData()},methods:{handleShowSupplier:function(){this.isDetail||(this.showSupplierDialog=!0)},getDetailData:(n=Object(i.a)(regeneratorRuntime.mark((function e(){var t,r,a;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(t in this.formModel)this.formModel[t]=this.bill[t];return e.prev=1,e.next=4,Object(w.e)({billNo:this.bill.billNo});case 4:r=e.sent,a=r.result,this.tableConf.data=a,e.next=13;break;case 10:e.prev=10,e.t0=e.catch(1);case 13:case"end":return e.stop()}}),e,this,[[1,10]])}))),function(){return n.apply(this,arguments)}),handleClose:function(){this.$emit("close")},getSelectProduct:function(e){var t=this,r=e.selectData;r.forEach((function(e){var r=t.tableConf.data.findIndex((function(t){return t.pharmacyPref===e.pharmacyPref}));r>-1?t.tableConf.data.splice(r,1,e):t.tableConf.data.push(e)})),this.tableConf.data=r.map((function(e){return Object(o.a)(Object(o.a)({},e),{},{changeNumber:e.stockNumber})}))},rowClick:function(e){"detail"!==this.pageType&&(this.currentRowIndex=e.index)},handleAdd:function(){this.showProductDialog=!0},updateSupplier:function(e){var t=e.supplierName,r=e.supplierNo;this.formModel.supplierName=t,this.formModel.supplierNo=r},checkBillDate:function(){var e={lotNumber:"请输入生产批号",producedDate:"请输入生产时间",expirationDate:"请输入有效期",changeNumber:"请输入入库数量"},t=Object.keys(e),r="",a=0;for(this.errorIndex=[],this.errorMsg="";a<this.tableConf.data.length;){var n,l=this.tableConf.data[a],o=Object(h.a)(t);try{for(o.s();!(n=o.n()).done;){var i=n.value;if(!l[i]){r||(r=i,this.errorMsg=e[i],t=[i]),this.errorIndex.push(a+1);break}}}catch(e){o.e(e)}finally{o.f()}a++}this.errorMsg&&this.$message.error("第".concat(this.errorIndex.join("、"),"行数据").concat(this.errorMsg))},handleSubmit:function(e){var t=this;this.tableConf.data.length||this.$message.error("请选择商品"),this.$refs.llForm.$children[0].validate((function(r){return r&&(t.checkBillDate(),t.errorMsg||t.save(e)),!1}))},save:(a=Object(i.a)(regeneratorRuntime.mark((function e(t){var r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return r=Object(o.a)(Object(o.a)({},this.formModel),{},{submitted:t,details:this.tableConf.data}),e.prev=1,e.next=4,Object(w.g)(r);case 4:this.$message.success("".concat(t?"提交成功":"暂存成功")),this.handleClose(),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(1);case 11:case"end":return e.stop()}}),e,this,[[1,8]])}))),function(e){return a.apply(this,arguments)})}},y=(r("acdb"),r("2877")),D=Object(y.a)(x,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("ll-list-page-layout",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"g-list-page g-page-style",attrs:{"element-loading-text":e.loadingText}},[r("template",{slot:"nav-bar"},[r("el-button",{on:{click:e.handleClose}},[e._v("取消")]),e._v(" "),e.isDetail?e._e():r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleSubmit(1)}}},[e._v("提交")]),e._v(" "),e.isDetail?e._e():r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.handleSubmit(0)}}},[e._v("保存")])],1),e._v(" "),r("ll-form",{ref:"llForm",attrs:{slot:"search-form","form-items":e.formItems,model:e.formModel,rules:e.rules},slot:"search-form"},[r("template",{slot:"form-item-supplierNo"},[r("el-input",{attrs:{disabled:e.isDetail,readonly:!0},nativeOn:{click:function(t){return e.handleShowSupplier(t)}},model:{value:e.formModel.supplierNo,callback:function(t){e.$set(e.formModel,"supplierNo",t)},expression:"formModel.supplierNo"}})],1)],2),e._v(" "),r("template",{slot:"action-bar_left"},[e.isDetail?e._e():r("el-button",{attrs:{type:"primary"},on:{click:e.handleAdd}},[e._v("新增行")])],1),e._v(" "),r("data-table",{directives:[{name:"table-height",rawName:"v-table-height"}],key:e.tableConf.tableId,ref:e.tableConf.ref,attrs:{"table-conf":e.tableConf,total:e.tableConf.total,"pagination-show":!1,"row-click":e.rowClick}},[r("template",{slot:"pharmacyPref"},[r("el-table-column",{attrs:{label:"商品编码",width:"180",prop:"pharmacyPref"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[r("span",{on:{click:function(t){e.showProductDialog=!0}}},[e._v(e._s(a.pharmacyPref))])]}}])})],1),e._v(" "),r("template",{slot:"lotNumber"},[r("el-table-column",{attrs:{label:"生产批号",width:"180",prop:"lotNumber"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.index===e.currentRowIndex?r("el-input",{model:{value:a.lotNumber,callback:function(t){e.$set(a,"lotNumber",t)},expression:"row.lotNumber"}}):r("span",[e._v(e._s(a.lotNumber))])]}}])})],1),e._v(" "),r("template",{slot:"producedDate"},[r("el-table-column",{attrs:{label:"生产日期",width:"180",prop:"producedDate"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.index===e.currentRowIndex?r("el-date-picker",{model:{value:a.producedDate,callback:function(t){e.$set(a,"producedDate",t)},expression:"row.producedDate"}}):r("span",[e._v(e._s(a.producedDate))])]}}])})],1),e._v(" "),r("template",{slot:"expirationDate"},[r("el-table-column",{attrs:{label:"有效期",width:"180",prop:"expirationDate"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.index===e.currentRowIndex?r("el-date-picker",{model:{value:a.expirationDate,callback:function(t){e.$set(a,"expirationDate",t)},expression:"row.expirationDate"}}):r("span",[e._v(e._s(a.expirationDate))])]}}])})],1),e._v(" "),r("template",{slot:"changeNumber"},[r("el-table-column",{attrs:{label:"入库数量",width:"180",prop:"changeNumber"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[a.index===e.currentRowIndex?r("el-input",{model:{value:a.changeNumber,callback:function(t){e.$set(a,"changeNumber",t)},expression:"row.changeNumber"}}):r("span",[e._v(e._s(a.changeNumber))])]}}])})],1)],2),e._v(" "),r("merchandise-table-dialog",{attrs:{title:"添加商品",width:"80%",visible:e.showProductDialog,"form-item":e.productFormitems,"table-conf":e.productTableConf,"query-list-fn":e.inventoryProductList},on:{"update:visible":function(t){e.showProductDialog=t},confirm:e.getSelectProduct}}),e._v(" "),e.showSupplierDialog?r("supplier-dialog",{attrs:{"is-show":e.showSupplierDialog},on:{hide:function(t){e.showSupplierDialog=!1},updateSupplier:e.updateSupplier}}):e._e()],2)}),[],!1,null,"1bb4c1f2",null).exports,k={components:{DataTable:c.a,Detail:D},data:function(){return{loading:!1,loadingText:"数据加载中",showDetail:!1,formItems:s,formModel:{billNo:"",startDate:"",endDate:"",supplierName:""},tableConf:{reserveSelection:!1,tableId:"warehousingBill",rowKey:"id",height:"100%",ref:"warehousingBill",showIndex:!0,showSelection:!1,currRow:{},colModel:u,data:[],total:0},type:"add",bill:{}}},mounted:function(){this.queryList()},methods:{rowDbClick:function(){},handleAdd:function(){this.showDetail=!0,this.type="add"},handleSubmit:function(){this.queryList()},queryList:(l=Object(i.a)(regeneratorRuntime.mark((function e(t){var r,a,n,l,i,c,s;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=3;break}return this.$refs[this.tableConf.ref].init(),e.abrupt("return");case 3:return e.prev=3,this.loading=!0,r=t.page,a=t.pageSize,n=Object(o.a)(Object(o.a)({},this.formModel),{},{page:r,pageSize:a}),e.next=10,Object(w.f)(n);case 10:l=e.sent,i=l.result,c=i.list,s=i.totalRecord,this.tableConf.data=c,this.tableConf.total=s,e.next=21;break;case 18:e.prev=18,e.t0=e.catch(3);case 21:this.loading=!1;case 22:case"end":return e.stop()}}),e,this,[[3,18]])}))),function(e){return l.apply(this,arguments)}),handleDetail:function(e,t){this.bill=e,this.type=t,this.showDetail=!0},handleDestory:function(e){var t=this,r=e.billNo;this.$confirm("当前为作废操作，资料作废将无法退回，是否继续作废？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(Object(i.a)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(w.c)({billNo:r});case 3:t.$message.success("作废成功"),t.queryList(),e.next=10;break;case 7:e.prev=7,e.t0=e.catch(0);case 10:case"end":return e.stop()}}),e,null,[[0,7]])})))).catch((function(){}))},handleDelete:function(e){var t=this,r=e.billNo;this.$confirm("当前为删除操作，资料删除将无法恢复，是否继续删除？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(Object(i.a)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(w.b)({billNo:r});case 3:t.$message.success("删除成功"),t.queryList(),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0);case 11:case"end":return e.stop()}}),e,null,[[0,8]])})))).catch((function(){}))},handleApproveBill:function(e){var t=this,r=e.billNo;this.$confirm("当前为审核确认操作，是否确定审核？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then(Object(i.a)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(w.a)({billNo:r});case 3:t.$message.success("审核成功"),t.queryList(),e.next=11;break;case 8:e.prev=8,e.t0=e.catch(0);case 11:case"end":return e.stop()}}),e,null,[[0,8]])})))).catch((function(){}))},close:function(){var e=this;this.showDetail=!1,this.$nextTick((function(){e.queryList()}))}}},N=Object(y.a)(k,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return e.showDetail?r("detail",{attrs:{bill:e.bill,"page-type":e.type},on:{close:e.close}}):r("ll-list-page-layout",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"g-list-page g-page-style",attrs:{"element-loading-text":e.loadingText}},[r("ll-search-form",{attrs:{slot:"search-form","form-items":e.formItems,model:e.formModel},on:{submit:e.handleSubmit,reset:e.handleSubmit},slot:"search-form"}),e._v(" "),r("template",{slot:"action-bar_left"},[r("el-button",{attrs:{type:"primary"},on:{click:e.handleAdd}},[e._v("新增")])],1),e._v(" "),r("data-table",{directives:[{name:"table-height",rawName:"v-table-height"}],key:e.tableConf.tableId,ref:e.tableConf.ref,attrs:{"table-conf":e.tableConf,total:e.tableConf.total,"row-dblclick":e.rowDbClick},on:{"query-change":e.queryList}},[r("template",{slot:"operation"},[r("el-table-column",{attrs:{align:"center",fixed:"right",label:"操作",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){var a=t.row;return[1===a.status?[r("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleApproveBill(a)}}},[e._v(" 审核 ")]),e._v(" "),r("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleDestory(a)}}},[e._v(" 作废 ")])]:e._e(),e._v(" "),0===a.status?[r("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleDetail(a,"edit")}}},[e._v(" 编辑 ")]),e._v(" "),r("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleDelete(a)}}},[e._v(" 删除 ")])]:e._e(),e._v(" "),[r("el-button",{attrs:{type:"text"},on:{click:function(t){return e.handleDetail(a,"detail")}}},[e._v(" 详情 ")])]]}}],null,!1,1709981270)})],1)],2)],2)}),[],!1,null,"1a320fad",null);t.default=N.exports}}]);