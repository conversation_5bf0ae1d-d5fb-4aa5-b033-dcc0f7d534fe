(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-27bd67ca"],{7747:function(e,t,r){"use strict";r.r(t);var a,n,o=r("5530"),i=(r("96cf"),r("1da1")),l=r("7845"),c=r("7b3d"),d=r("8f29"),s={components:{DataTable:l.a,DebounceBtn:c.a},data:function(){return{loading:!1,loadingText:"加载中···",total:0,formModel:{stockType:"",productInfo:""},row:{},formItem:[{label:"商品信息",prop:"productInfo",component:"el-input",attrs:{clearable:!1,placeholder:"商品编号/国家医保编码/通用名/商品名称"},width:"300px"},{label:"库存",prop:"stockType",component:"ll-select",attrs:{options:[{label:"全部",value:""},{label:"大于零",value:"1"},{label:"小于零",value:"-1"},{label:"等于零",value:"0"}],clearable:!0,placeholder:"全部"}}],tableConf:{reserveSelection:!1,tableId:"ProductBatchInventory",rowKey:"id",height:"100%",ref:"ProductBatchInventory",showIndex:!0,showSelection:!1,currRow:{},data:[],colModel:[{label:"商品编码",prop:"pharmacyPref",hidden:!1,width:150},{label:"商品名称",prop:"productName",hidden:!1,width:200},{label:"通用名称",prop:"commonName",hidden:!1,width:200},{label:"国家医保编码",prop:"projectCode",hidden:!1,width:150},{label:"注册名称",prop:"registeredProductName",hidden:!1,width:200},{label:"规格",prop:"attributeSpecification",hidden:!1,width:150},{label:"单位",prop:"unit",hidden:!1,width:100},{label:"批准文号",prop:"approvalNumber",hidden:!1,width:150},{label:"生产企业",prop:"manufacturer",hidden:!1,width:180},{label:"产地",prop:"producingArea",hidden:!1,width:150},{label:"生产批号",prop:"lotNumber",hidden:!1,width:150},{label:"生产日期",prop:"producedDate",hidden:!1,width:150},{label:"有效期",prop:"expirationDate",hidden:!1,width:150},{label:"处方药标志",prop:"prescriptionFlag",hidden:!1,width:120},{label:"库存数量",prop:"stockNumber",hidden:!1,width:150}]}}},computed:{},created:function(){var e=this.$route.params.pharmacyPref;e&&(this.formModel.productInfo=e)},mounted:function(){var e=this;this.$nextTick((function(){e.queryList()}))},methods:{handleQuery:function(){this.queryList()},handleReset:function(){this.formModel.productInfo="",this.queryList()},queryList:(n=Object(i.a)(regeneratorRuntime.mark((function e(t){var r,a,n,i,l,c;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=3;break}return this.$refs[this.tableConf.ref].init(),e.abrupt("return");case 3:return r=t.pageSize,a=t.page,n=Object(o.a)(Object(o.a)({},this.formModel),{},{pageNum:a,pageSize:r}),this.loading=!0,e.prev=6,e.next=9,Object(d.c)(n);case 9:i=e.sent,l=i.list,c=i.totalRecord,this.loading=!1,this.tableConf.data=l||[],this.total=c,e.next=20;break;case 17:e.prev=17,e.t0=e.catch(6),this.loading=!1;case 20:case"end":return e.stop()}}),e,this,[[6,17]])}))),function(e){return n.apply(this,arguments)}),syncStock:(a=Object(i.a)(regeneratorRuntime.mark((function e(){var t;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(d.a)();case 3:(t=e.sent).msg&&this.$alert(t.msg,"提示",{confirmButtonText:"确定",callback:function(){}}),e.next=9;break;case 7:e.prev=7,e.t0=e.catch(0);case 9:case"end":return e.stop()}}),e,this,[[0,7]])}))),function(){return a.apply(this,arguments)})}},u=r("2877"),p=Object(u.a)(s,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("ll-list-page-layout",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"g-list-page g-page-style",attrs:{"element-loading-text":e.loadingText}},[r("ll-search-form",{attrs:{slot:"search-form",model:e.formModel,"form-items":e.formItem,"label-width":"100px"},on:{submit:e.handleQuery,reset:e.handleReset},slot:"search-form"}),e._v(" "),r("template",{slot:"action-bar_left"},[r("debounce-btn",{attrs:{type:"primary",delay:2e3},on:{click:e.syncStock}},[e._v(" 库存同步 ")])],1),e._v(" "),r("data-table",{directives:[{name:"table-height",rawName:"v-table-height"}],key:e.tableConf.tableId,ref:e.tableConf.ref,attrs:{"table-conf":e.tableConf,total:e.total},on:{"query-change":e.queryList}})],2)}),[],!1,null,"3e7baaac",null);t.default=p.exports},"8f29":function(e,t,r){"use strict";r.d(t,"b",(function(){return n})),r.d(t,"c",(function(){return o})),r.d(t,"a",(function(){return i}));var a=r("b775");function n(e){return Object(a.a)({url:"/medicare/supervision/offline/inventory/list",method:"post",data:e}).then((function(e){return e.result}))}function o(e){return Object(a.a)({url:"/medicare/supervision/offline/inventory/lotNumber/list",method:"post",data:e}).then((function(e){return e.result}))}function i(e){return Object(a.a)({url:"/medicare/supervision/offline/inventory/fullSync",method:"post",data:e})}}}]);