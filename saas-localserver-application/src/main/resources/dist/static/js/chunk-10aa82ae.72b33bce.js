(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-10aa82ae"],{"386d":function(e,t,a){"use strict";var o=a("cb7c"),i=a("83a1"),r=a("5f1b");a("214f")("search",1,(function(e,t,a,l){return[function(a){var o=e(this),i=null==a?void 0:a[t];return void 0!==i?i.call(a,o):new RegExp(a)[t](String(o))},function(e){var t=l(a,e,this);if(t.done)return t.value;var n=o(e),d=String(this),c=n.lastIndex;i(c,0)||(n.lastIndex=0);var s=r(n,d);return i(n.lastIndex,c)||(n.lastIndex=c),null===s?-1:s.index}]}))},"83a1":function(e,t){e.exports=Object.is||function(e,t){return e===t?0!==e||1/e==1/t:e!=e&&t!=t}},9834:function(e,t,a){"use strict";a("ac5d")},ac5d:function(e,t,a){},c6b8:function(e,t,a){"use strict";a.r(t);var o,i=a("5530"),r=(a("96cf"),a("1da1")),l=a("7845"),n=a("3835"),d=a("c273"),c=a("7b3d"),s=a("ed08"),p={name:"LicensedPharmacistDetail",components:{DebounceBtn:c.a,DataTable:l.a,merchandiseTableDialog:d.a},props:{isShow:{type:Boolean,default:!1},isEdit:{type:Boolean,default:!1},row:{type:Object,default:function(){return{}}}},data:function(){return{queryProductTemproryList:"",total:0,ruleForm:{employeeid:"",name:"",employeetype:"0",yaoshiid:"",zhucheid:""},rules:{employeeid:[{required:!0,message:"人员编码不能为空",trigger:"blur"}],name:[{required:!0,message:"人员名称不能为空",trigger:"blur"}]},formModel:{orderNo:"",dataRange:Object(s.e)(),employeetype:""},formItem:[{label:"损溢单号",prop:"orderNo",component:"el-input",attrs:{clearable:!1,placeholder:"按损溢单/源单号"},width:"250px"},{label:"损溢时间",prop:"dataRange",component:"el-date-picker",attrs:{type:"datetime",valueFormat:"yyyy-MM-dd HH:mm:ss",format:"yyyy-MM-dd HH:mm:ss"},width:"330px",labelWidth:"80px"},{label:"备注",prop:"employeetype",component:"el-input"}],tableConf:{reserveSelection:!1,tableId:"FixedMedicalOrg",rowKey:"id",height:"100%",ref:"FixedMedicalOrg",showIndex:!0,showSelection:!1,currRow:{},data:[],colModel:[{label:"商品编码",prop:"organid",hidden:!1,width:100},{label:"生产批号",prop:"pihao",hidden:!1,width:150},{label:"实际数量",prop:"number",hidden:!1,width:150},{label:"损溢原因",prop:"yuanyin",hidden:!1,width:150},{label:"商品名",prop:"organname",hidden:!1,width:200},{label:"国家医保编码",prop:"yibaoid",hidden:!1,width:120},{label:"注册名",prop:"yibaoname",hidden:!1,width:200},{label:"规格",prop:"quyu",hidden:!1,width:150},{label:"单位",prop:"quyuname",hidden:!1,width:200},{label:"批准文号",prop:"ip",hidden:!1,width:150},{label:"生产企业",prop:"ip",hidden:!1,width:150},{label:"生产日期",prop:"ip",hidden:!1,width:150},{label:"有效期",prop:"ip",hidden:!1,width:150},{label:"库存数量",prop:"ip",hidden:!1,width:150},{label:"损溢数量",prop:"ip",hidden:!1,width:150}]},formItemAddProdect:[{label:"商品信息",prop:"productName",component:"el-input",attrs:{placeholder:"商品编码/商品名称/国家编码",clearable:!0},width:"180px"},{label:"批准文号",prop:"productName",component:"el-input",attrs:{placeholder:"批准文号",clearable:!0},width:"180px"}],tableConfAddProdect:{rowKey:"productPref",showSelection:!0,reserveSelection:!0,tableId:"addProductDialog",height:"100%",ref:"addProductDialog",showIndex:!0,currRow:{},data:[],colModel:[{label:"商品编号",prop:"organid",hidden:!1,width:100},{label:"商品名称",prop:"organname",hidden:!1,width:200},{label:"通用名称",prop:"yibaoid",hidden:!1,width:120},{label:"单位",prop:"yibaoname",hidden:!1,width:200},{label:"规格",prop:"quyu",hidden:!1,width:150},{label:"库存",prop:"quyuname",hidden:!1,width:200},{label:"生产厂家",prop:"ip",hidden:!1,width:150},{label:"批准文号",prop:"ip",hidden:!1,width:150},{label:"产地",prop:"ip",hidden:!1,width:150}]},formItemBatchProdect:[{label:"批号信息",prop:"productName",component:"el-input",attrs:{placeholder:"批号编码",clearable:!0},width:"180px"}],tableConfBatchProdect:{rowKey:"productPref",showSelection:!0,reserveSelection:!0,tableId:"addProductDialog",height:"100%",ref:"addProductDialog",showIndex:!0,currRow:{},data:[],colModel:[{label:"商品编号",prop:"organid",hidden:!1,width:100},{label:"商品名称",prop:"organname",hidden:!1,width:200},{label:"通用名称",prop:"yibaoid",hidden:!1,width:120},{label:"规格",prop:"quyu",hidden:!1,width:150},{label:"生产厂家",prop:"ip",hidden:!1,width:150},{label:"产地",prop:"ip",hidden:!1,width:150},{label:"单位",prop:"yibaoname",hidden:!1,width:200},{label:"批号",prop:"ip",hidden:!1,width:150},{label:"生产日期",prop:"ip",hidden:!1,width:150},{label:"有效期至",prop:"ip",hidden:!1,width:150},{label:"库存数量",prop:"ip",hidden:!1,width:150}]},showProductDialog:!1,showBatchDialog:!1}},mounted:function(){var e=this;this.isEdit||this.getDetail(),this.$nextTick((function(){e.queryList()}))},methods:{handleResetFormModel:function(){this.resetCustomType=[]},tmpExtractProduct:function(e){this.closeExtract()},closeExtract:function(){this.$refs.extractProductDialog.tableConf.data=[],this.$refs.extractProductDialog.total=0,this.showProductDialog=!1},getParamsFn:function(e){var t=Object(i.a)(Object(i.a)({},e),{},{sourceStore:this.formData.organSign?this.formData.organSign:"",productType:this.formModel.productType?this.formModel.productType:"",pageSize:e.rows,pageNum:e.page});return delete t.rows,delete t.page,t},rowDblclick:function(){},handleQuery:function(){this.queryList()},handleReset:function(){this.queryList()},getDetail:function(){},resetForm:function(){this.ruleForm={organid:"",organname:"",yibaoid:"",yibaoname:"",quyu:[],quyuname:"",ip:"",mac:"",tel:""}},goBack:function(){var e=this;if(!this.isEdit)return this.resetForm(),void this.$emit("hide",{flag:"detail"});this.$confirm("是否放弃本次操作？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.resetForm(),e.$emit("hide",{flag:"edit"})})).catch((function(){}))},handleSubmit:function(){var e=this;this.$refs.ruleForm.validate(function(){var t=Object(r.a)(regeneratorRuntime.mark((function t(a){var o,r,l,d,c,s,p,u;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:a&&(o=e.ruleForm.region||[void 0,void 0,void 0],r=Object(n.a)(o,3),l=r[0],d=void 0===l?{}:l,c=r[1],s=void 0===c?{}:c,p=r[2],u=void 0===p?{}:p,Object(i.a)(Object(i.a)({},e.ruleForm),{},{provinceCode:d.areaCode,cityCode:s.areaCode,areaCode:u.areaCode,provinceName:d.areaName,cityName:s.areaName,areaName:u.areaName}));case 1:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},queryList:(o=Object(r.a)(regeneratorRuntime.mark((function e(t){var a,o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=3;break}return this.$refs[this.tableConf.ref].init(),e.abrupt("return");case 3:a=t.pageSize,o=t.page,Object(i.a)(Object(i.a)({},this.formModel),{},{pageNo:o,pageSize:a}),this.loading=!0;try{this.loading=!1,this.tableConf.data=[{organid:"XD222222",organname:"测试测测测",yibaoid:"3242342342",yibaoname:"测试测试医保药店",quyu:"区域",quyuname:"区域佛山分散发",ip:"***********",mac:"SSDD-DDD-YIJ-OBJK",tel:"02733333988"}],this.total=total||0}catch(e){this.loading=!1}case 7:case"end":return e.stop()}}),e,this)}))),function(e){return o.apply(this,arguments)})}},u=(a("9834"),a("2877")),h=Object(u.a)(p,(function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{staticClass:"g-fullscreen-dialog",attrs:{title:"",visible:e.isShow,"show-close":!1,modal:!1,"close-on-click-modal":!1,fullscreen:""},on:{"update:visible":function(t){e.isShow=t}}},[a("ll-list-page-layout",[a("div",{attrs:{slot:"nav-bar"},slot:"nav-bar"},[a("el-button",{on:{click:e.goBack}},[e._v("返回")]),e._v(" "),a("debounce-btn",{attrs:{type:"primary"},on:{click:function(t){return e.handleSubmit()}}},[e._v(" 保存 ")]),e._v(" "),a("debounce-btn",{attrs:{type:"primary"},on:{click:function(t){return e.handleSubmit()}}},[e._v(" 提交 ")])],1),e._v(" "),a("ll-search-form",{attrs:{slot:"search-form",model:e.formModel,"form-items":e.formItem,"label-width":"100px"},on:{submit:e.handleQuery,reset:e.handleReset},slot:"search-form"}),e._v(" "),a("template",{slot:"action-bar_left"},[a("ll-button",{attrs:{delay:2e3,type:"primary"},on:{click:function(t){e.showProductDialog=!0}}},[e._v("添加商品")])],1),e._v(" "),a("data-table",{directives:[{name:"table-height",rawName:"v-table-height"}],key:e.tableConf.tableId,ref:e.tableConf.ref,attrs:{"table-conf":e.tableConf,total:e.total,"row-dblclick":e.rowDblclick},on:{"query-change":e.queryList}},[a("template",{slot:"organid"},[a("el-table-column",{attrs:{label:"商品编码",prop:"organid",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{attrs:{placeholder:""},model:{value:t.row.organid,callback:function(a){e.$set(t.row,"organid",a)},expression:"scope.row.organid"}})]}}])})],1),e._v(" "),a("template",{slot:"pihao"},[a("el-table-column",{attrs:{label:"生产批号",prop:"pihao",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{attrs:{placeholder:""},on:{focus:function(t){e.showBatchDialog=!0}},model:{value:t.row.pihao,callback:function(a){e.$set(t.row,"pihao",a)},expression:"scope.row.pihao"}})]}}])})],1),e._v(" "),a("template",{slot:"number"},[a("el-table-column",{attrs:{label:"实际数量",prop:"number",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{attrs:{placeholder:""},model:{value:t.row.number,callback:function(a){e.$set(t.row,"number",a)},expression:"scope.row.number"}})]}}])})],1),e._v(" "),a("template",{slot:"yuanyin"},[a("el-table-column",{attrs:{label:"损溢原因",prop:"yuanyin",width:"160",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{attrs:{placeholder:""},model:{value:t.row.yuanyin,callback:function(a){e.$set(t.row,"yuanyin",a)},expression:"scope.row.yuanyin"}})]}}])})],1),e._v(" "),a("template",{slot:"operation"},[a("el-table-column",{attrs:{align:"center",fixed:"right",label:"操作",width:"150"},scopedSlots:e._u([{key:"default",fn:function(t){return t.row,[a("ll-button",{attrs:{delay:2e3,type:"primary"}},[e._v("删除")])]}}])})],1)],2),e._v(" "),a("merchandise-table-dialog",{ref:"extractProductDialog",attrs:{"append-to-body":"","is-has-check-all":!0,"form-item":e.formItemAddProdect,visible:e.showProductDialog,"table-conf":e.tableConfAddProdect,title:"添加商品",width:"90vw",top:"50px","query-list-fn":e.queryProductTemproryList,"get-params-fn":e.getParamsFn},on:{"update:visible":function(t){e.showProductDialog=t},closed:e.closeExtract,confirm:e.tmpExtractProduct,handleResetFormModel:e.handleResetFormModel}}),e._v(" "),a("merchandise-table-dialog",{ref:"extractProductDialog",attrs:{"append-to-body":"","is-has-check-all":!0,"form-item":e.formItemBatchProdect,visible:e.showBatchDialog,"table-conf":e.tableConfBatchProdect,title:"选择商品批号",width:"90vw",top:"50px","query-list-fn":e.queryProductTemproryList,"get-params-fn":e.getParamsFn},on:{"update:visible":function(t){e.showBatchDialog=t},closed:e.closeExtract,confirm:e.tmpExtractProduct,handleResetFormModel:e.handleResetFormModel}})],2)],1)}),[],!1,null,"25456396",null).exports,b={name:"FixedMedicalOrg",components:{DataTable:l.a,detail:h},data:function(){return{isEdit:!1,detailDialogShow:!1,loading:!1,loadingText:"加载中···",total:0,formModel:{orderNo:"",dataRange:["",""],employeetype:""},row:{},formItem:[{label:"单据信息",prop:"orderNo",component:"el-input",attrs:{clearable:!1,placeholder:"按损溢单/来源单号"},width:"250px"},{label:"损溢日期",prop:"dataRange",component:"ll-date-picker",attrs:{type:"date","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期","value-format":"yyyy-MM-dd",clearable:!1,width:"200px"},width:"330px",labelWidth:"80px"},{label:"损溢类型",prop:"employeetype",component:"ll-select",attrs:{options:[{label:"全部",value:""},{label:"报损",value:0},{label:"报溢",value:1}],clearable:!0,placeholder:"全部"}}],tableConf:{reserveSelection:!1,tableId:"FixedMedicalOrg",rowKey:"id",height:"100%",ref:"FixedMedicalOrg",showIndex:!0,showSelection:!1,currRow:{},data:[],colModel:[{label:"损溢单号",prop:"organid",hidden:!1,width:100},{label:"损溢日期",prop:"organname",hidden:!1,width:200},{label:"损溢品规数",prop:"yibaoid",hidden:!1,width:120},{label:"报损总数量",prop:"yibaoname",hidden:!1,width:200},{label:"报溢总数量",prop:"quyu",hidden:!1,width:150},{label:"来源单据",prop:"quyuname",hidden:!1,width:200},{label:"备注",prop:"ip",hidden:!1,width:150}]},selectData:[]}},computed:{},mounted:function(){var e=this;this.$nextTick((function(){e.queryList()}))},methods:{handleQuery:function(){this.queryList()},handleReset:function(){this.queryList()},queryList:function(){var e=Object(r.a)(regeneratorRuntime.mark((function e(t){var a,o;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=3;break}return this.$refs[this.tableConf.ref].init(),e.abrupt("return");case 3:a=t.pageSize,o=t.page,Object(i.a)(Object(i.a)({},this.formModel),{},{pageNo:o,pageSize:a}),this.loading=!0;try{this.loading=!1,this.tableConf.data=[],this.total=total}catch(e){this.loading=!1}case 7:case"end":return e.stop()}}),e,this)})));return function(t){return e.apply(this,arguments)}}(),getDetail:function(e){this.detailDialogShow=!0,this.isEdit=!1,this.row=e},rowDblclick:function(e){this.detailDialogShow=!0,this.isEdit=!1,this.row=e},editRow:function(e){this.detailDialogShow=!0,this.isEdit=!0,this.row=e},hideDetailDialog:function(e){this.detailDialogShow=!1}}},m=Object(u.a)(b,(function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ll-list-page-layout",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"g-list-page g-page-style",attrs:{"element-loading-text":e.loadingText}},[a("ll-search-form",{attrs:{slot:"search-form",model:e.formModel,"form-items":e.formItem,"label-width":"100px"},on:{submit:e.handleQuery,reset:e.handleReset},slot:"search-form"}),e._v(" "),a("template",{slot:"action-bar_left"},[a("ll-button",{attrs:{delay:2e3,type:"primary"},on:{click:function(t){e.detailDialogShow=!0}}},[e._v("新增")])],1),e._v(" "),a("data-table",{directives:[{name:"table-height",rawName:"v-table-height"}],key:e.tableConf.tableId,ref:e.tableConf.ref,attrs:{"table-conf":e.tableConf,total:e.total,"row-dblclick":e.rowDblclick},on:{"query-change":e.queryList}},[a("template",{slot:"operation"},[a("el-table-column",{attrs:{label:"操作","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.editRow(t.row)}}},[e._v("编辑")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.editRow(t.row)}}},[e._v("删除")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.getDetail(t.row)}}},[e._v("详情")])]}}])})],1)],2),e._v(" "),e.detailDialogShow?a("detail",{attrs:{"is-show":e.detailDialogShow,"is-edit":e.isEdit,row:e.row},on:{hide:e.hideDetailDialog}}):e._e()],2)}),[],!1,null,"5449c0b5",null);t.default=m.exports}}]);