(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-383c27be"],{"002a":function(e,t,r){},3072:function(e,t,r){"use strict";r.d(t,"b",(function(){return n})),r.d(t,"a",(function(){return o})),r.d(t,"f",(function(){return i})),r.d(t,"e",(function(){return l})),r.d(t,"d",(function(){return c})),r.d(t,"g",(function(){return s})),r.d(t,"c",(function(){return u}));var a=r("b775");function n(e){return Object(a.a)({url:"/medicare/merchant/listOrganSignByParam",method:"post",data:e}).then((function(e){return e.result}))}function o(e){return Object(a.a)({url:"/medicare/merchant/getById",method:"post",data:e}).then((function(e){return e.result}))}function i(e){return Object(a.a)({url:"/medicare/merchant/update",method:"post",data:e})}function l(e){return Object(a.a)({url:"/medicare/merchant/employee/queryByOrganSign",method:"post",data:e}).then((function(e){return e.result}))}function c(e){return Object(a.a)({url:"/medicare/merchant/employee/queryByEmployeeId",method:"post",data:e}).then((function(e){return e.result}))}function s(e){return Object(a.a)({url:"/medicare/merchant/employee/updateEmployee",method:"post",data:e}).then((function(e){return e.result}))}function u(e){return Object(a.a)({url:"/medicare/product/listProductCatalogByParam",method:"post",data:e}).then((function(e){return e.result}))}},c7a3:function(e,t,r){"use strict";r("002a")},dfc8:function(e,t,r){"use strict";r.r(t);var a,n,o,i,l=r("5530"),c=(r("ac6a"),r("2909")),s=(r("96cf"),r("1da1")),u=r("7845"),d=(r("7f7f"),r("7b3d")),p=r("3072"),m={name:"LicensedPharmacistDetail",components:{DebounceBtn:d.a},props:{isShow:{type:Boolean,default:!1},isEdit:{type:Boolean,default:!1},roles:{type:Array,default:function(){return[]}},row:{type:Object,default:function(){return{}}}},data:function(){return{loading:!1,formData:{code:"",name:"",roleType:"01",pracPsnCode:"",pracCertNo:""},rules:{pracPsnCode:[{validator:function(e,t,r){t?/^[DNYE]/.test(t)?r():r(new Error("首字母必须是D、N、Y、E中的任意一个!")):r()},trigger:"blur"}]}}},mounted:function(){this.getDetail()},methods:{getDetail:(a=Object(s.a)(regeneratorRuntime.mark((function e(){var t,r,a,n,o,i;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(p.d)({id:this.row.employeeId});case 3:t=e.sent,r=t.code,a=t.name,n=t.roleType,o=t.pracPsnCode,i=t.pracCertNo,this.formData={code:r,name:a,roleType:this.isEdit&&""==n?"01":n,pracPsnCode:o,pracCertNo:i},e.next=14;break;case 12:e.prev=12,e.t0=e.catch(0);case 14:case"end":return e.stop()}}),e,this,[[0,12]])}))),function(){return a.apply(this,arguments)}),resetForm:function(){this.formData={code:"",name:"",roleType:"01",pracPsnCode:"",pracCertNo:""}},goBack:function(){var e=this;if(!this.isEdit)return this.resetForm(),void this.$emit("hide",!1);this.$confirm("是否放弃本次操作？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.resetForm(),e.$emit("hide",!1)})).catch((function(){}))},onPracPsnCodeInput:function(e){this.formData.pracPsnCode=e.substr(0,1).toUpperCase()+e.substr(1)},handleSubmit:function(){var e=this;this.$refs["rule-form"].validate(function(){var t=Object(s.a)(regeneratorRuntime.mark((function t(r){var a;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(r){t.next=2;break}return t.abrupt("return");case 2:return a=Object(l.a)(Object(l.a)({},e.formData),{},{id:e.row.employeeId}),t.prev=3,e.loading=!0,t.next=7,Object(p.g)(a);case 7:t.sent,e.loading=!1,e.$emit("hide",!0),t.next=15;break;case 12:t.prev=12,t.t0=t.catch(3),e.loading=!1;case 15:case"end":return t.stop()}}),t,null,[[3,12]])})));return function(e){return t.apply(this,arguments)}}())}}},f=(r("c7a3"),r("2877")),h=Object(f.a)(m,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("el-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"g-fullscreen-dialog",attrs:{title:"",visible:e.isShow,"show-close":!1,modal:!1,"close-on-click-modal":!1,fullscreen:""},on:{"update:visible":function(t){e.isShow=t}}},[r("ll-list-page-layout",[r("div",{attrs:{slot:"nav-bar"},slot:"nav-bar"},[r("el-button",{on:{click:e.goBack}},[e._v("返回")]),e._v(" "),e.isEdit?r("debounce-btn",{attrs:{type:"primary"},on:{click:e.handleSubmit}},[e._v(" 提交 ")]):e._e()],1),e._v(" "),r("el-form",{ref:"rule-form",staticClass:"rule-form",attrs:{model:e.formData,rules:e.rules,disabled:!e.isEdit,"label-width":"150px"}},[r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:10}},[r("el-form-item",{attrs:{label:"人员编码",prop:"code"}},[r("el-input",{attrs:{disabled:""},model:{value:e.formData.code,callback:function(t){e.$set(e.formData,"code",t)},expression:"formData.code"}})],1)],1),e._v(" "),r("el-col",{attrs:{span:10}},[r("el-form-item",{attrs:{label:"人员名称",prop:"name"}},[r("el-input",{attrs:{disabled:""},model:{value:e.formData.name,callback:function(t){e.$set(e.formData,"name",t)},expression:"formData.name"}})],1)],1)],1),e._v(" "),r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:10}},[r("el-form-item",{attrs:{label:"人员类型",prop:"roleType"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:e.formData.roleType,callback:function(t){e.$set(e.formData,"roleType",t)},expression:"formData.roleType"}},e._l(e.roles,(function(e){return r("el-option",{key:e.code,attrs:{label:e.value,value:e.code}})})),1)],1)],1),e._v(" "),r("el-col",{attrs:{span:10}},[r("el-form-item",{attrs:{label:"医保医 / 药师代码",prop:"pracPsnCode"}},[r("el-input",{attrs:{maxlength:"50"},on:{input:e.onPracPsnCodeInput},model:{value:e.formData.pracPsnCode,callback:function(t){e.$set(e.formData,"pracPsnCode",t)},expression:"formData.pracPsnCode"}})],1)],1)],1),e._v(" "),r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:10}},[r("el-form-item",{attrs:{label:"执业注册证编号",prop:"pracCertNo"}},[r("el-input",{attrs:{maxlength:"50"},model:{value:e.formData.pracCertNo,callback:function(t){e.$set(e.formData,"pracCertNo",t)},expression:"formData.pracCertNo"}})],1)],1)],1)],1)],1)],1)}),[],!1,null,"e49790da",null).exports,b=r("2934"),g={components:{DataTable:u.a,LicensedPharmacistDetail:h},data:function(){return{isEdit:!1,detailDialogShow:!1,loading:!1,loadingText:"加载中···",total:0,roles:[],formModel:{mixQuery:"",pracPsnCode:"",pracCertNo:"",roleType:""},row:{},formItem:[{label:"人员信息",prop:"mixQuery",component:"el-input",attrs:{clearable:!1,placeholder:"人员编号/姓名"},width:"200px"},{label:"医保代码",prop:"pracPsnCode",component:"el-input",attrs:{clearable:!1},width:"220px"},{label:"注册证编号",prop:"pracCertNo",component:"el-input",attrs:{clearable:!1},width:"220px"},{label:"人员类型",prop:"roleType",component:"ll-select",attrs:{options:[],props:{value:"code",label:"value"},clearable:!0,placeholder:"全部"}}],tableConf:{reserveSelection:!1,tableId:"LicensedPharmacist",rowKey:"id",height:"100%",ref:"LicensedPharmacist",showIndex:!0,showSelection:!1,data:[],colModel:[{label:"人员编码",prop:"code",hidden:!1,width:100},{label:"姓名",prop:"name",hidden:!1,width:150},{label:"人员类型",prop:"roleName",hidden:!1,width:120},{label:"医保医 / 药师代码",prop:"pracPsnCode",hidden:!1,width:200},{label:"执业注册证编号",prop:"pracCertNo",hidden:!1,width:200}]},selectData:[]}},computed:{},created:(i=Object(s.a)(regeneratorRuntime.mark((function e(){return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:this.getRoleType();case 1:case"end":return e.stop()}}),e,this)}))),function(){return i.apply(this,arguments)}),mounted:function(){var e=this;this.$nextTick((function(){e.queryList()}))},methods:{getRoleType:(o=Object(s.a)(regeneratorRuntime.mark((function e(){var t,r;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(b.a)({categoryCode:"roleType"});case 3:t=e.sent,this.roles=t||[],r=[{code:"",value:"全部"}].concat(Object(c.a)(t)),this.formItem.forEach((function(e){var t=e;"roleType"===e.prop&&(t.attrs.options=r)})),e.next=12;break;case 9:e.prev=9,e.t0=e.catch(0);case 12:case"end":return e.stop()}}),e,this,[[0,9]])}))),function(){return o.apply(this,arguments)}),handleQuery:function(){this.queryList()},handleReset:function(){this.queryList()},queryList:(n=Object(s.a)(regeneratorRuntime.mark((function e(t){var r,a,n,o,i,c;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=3;break}return this.$refs[this.tableConf.ref].init(),e.abrupt("return");case 3:return r=t.pageSize,a=t.page,n=Object(l.a)(Object(l.a)({},this.formModel),{},{pageNum:a,pageSize:r}),this.loading=!0,e.prev=6,e.next=9,Object(p.e)(n);case 9:o=e.sent,i=o.list,c=o.totalRecord,this.loading=!1,this.tableConf.data=i||[],this.total=c,e.next=20;break;case 17:e.prev=17,e.t0=e.catch(6),this.loading=!1;case 20:case"end":return e.stop()}}),e,this,[[6,17]])}))),function(e){return n.apply(this,arguments)}),goDetail:function(e){this.detailDialogShow=!0,this.isEdit=!1,this.row=e},editRow:function(e){this.detailDialogShow=!0,this.isEdit=!0,this.row=e},hideDetailDialog:function(e){this.detailDialogShow=!1,e&&this.queryList()}}},v=Object(f.a)(g,(function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("ll-list-page-layout",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"g-list-page g-page-style",attrs:{"element-loading-text":e.loadingText}},[r("ll-search-form",{attrs:{slot:"search-form",model:e.formModel,"form-items":e.formItem,"label-width":"100px"},on:{submit:e.handleQuery,reset:e.handleReset},slot:"search-form"}),e._v(" "),r("data-table",{directives:[{name:"table-height",rawName:"v-table-height"}],key:e.tableConf.tableId,ref:e.tableConf.ref,attrs:{"table-conf":e.tableConf,total:e.total,"row-dblclick":e.goDetail},on:{"query-change":e.queryList}},[r("template",{slot:"operation"},[r("el-table-column",{attrs:{label:"操作","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{attrs:{type:"text"},on:{click:function(r){return e.editRow(t.row)}}},[e._v("编辑")]),e._v(" "),r("el-button",{attrs:{type:"text"},on:{click:function(r){return e.goDetail(t.row)}}},[e._v("详情")])]}}])})],1)],2),e._v(" "),e.detailDialogShow?r("licensed-pharmacist-detail",{attrs:{"is-show":e.detailDialogShow,"is-edit":e.isEdit,row:e.row,roles:e.roles},on:{hide:e.hideDetailDialog}}):e._e()],1)}),[],!1,null,"6fa836e1",null);t.default=v.exports}}]);