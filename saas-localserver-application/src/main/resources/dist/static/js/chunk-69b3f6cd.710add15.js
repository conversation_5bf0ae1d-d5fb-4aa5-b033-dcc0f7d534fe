(window.webpackJsonp=window.webpackJsonp||[]).push([["chunk-69b3f6cd"],{3072:function(e,t,a){"use strict";a.d(t,"b",(function(){return n})),a.d(t,"a",(function(){return o})),a.d(t,"f",(function(){return i})),a.d(t,"e",(function(){return l})),a.d(t,"d",(function(){return c})),a.d(t,"g",(function(){return s})),a.d(t,"c",(function(){return d}));var r=a("b775");function n(e){return Object(r.a)({url:"/medicare/merchant/listOrganSignByParam",method:"post",data:e}).then((function(e){return e.result}))}function o(e){return Object(r.a)({url:"/medicare/merchant/getById",method:"post",data:e}).then((function(e){return e.result}))}function i(e){return Object(r.a)({url:"/medicare/merchant/update",method:"post",data:e})}function l(e){return Object(r.a)({url:"/medicare/merchant/employee/queryByOrganSign",method:"post",data:e}).then((function(e){return e.result}))}function c(e){return Object(r.a)({url:"/medicare/merchant/employee/queryByEmployeeId",method:"post",data:e}).then((function(e){return e.result}))}function s(e){return Object(r.a)({url:"/medicare/merchant/employee/updateEmployee",method:"post",data:e}).then((function(e){return e.result}))}function d(e){return Object(r.a)({url:"/medicare/product/listProductCatalogByParam",method:"post",data:e}).then((function(e){return e.result}))}},"7c37":function(e,t,a){"use strict";a.r(t);var r,n,o=a("5530"),i=(a("96cf"),a("1da1")),l=a("7845"),c=(a("6762"),a("2fdb"),a("a481"),a("7b3d")),s=a("3072"),d={name:"FixedMedicalOrgDetail",components:{DebounceBtn:c.a},props:{isShow:{type:Boolean,default:!1},isEdit:{type:Boolean,default:!1},row:{type:Object,default:function(){return{}}}},data:function(){return{loading:!1,formData:{organSign:"",organName:"",medicareInstitutionCode:"",medicareInstitutionName:"",areaCode:[],merchantAreaName:"",privateNetworkIp:"",macAddress:"",medicareAgencyPhone:""},rules:{medicareInstitutionCode:[{validator:function(e,t,a){t?/^[PH]\d{11}$/.test(t)?a():a(new Error("首字母必须以P或H开头,且后面为11位数字!")):a()},trigger:"blur"}]}}},mounted:function(){this.getDetail()},methods:{getDetail:(r=Object(i.a)(regeneratorRuntime.mark((function e(){var t,a,r,n,o,i,l,c,d,u;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.prev=0,e.next=3,Object(s.a)({id:this.row.merchantId});case 3:t=e.sent,a=t.organSign,r=t.organName,n=t.medicareInstitutionCode,o=t.medicareInstitutionName,i=t.merchantAreaName,l=t.privateNetworkIp,c=t.macAddress,d=t.medicareAgencyPhone,u=t.parentCodes,this.formData={organSign:a,organName:r,medicareInstitutionCode:n,medicareInstitutionName:o,merchantAreaName:i,privateNetworkIp:l,macAddress:c,areaCode:u,medicareAgencyPhone:d},e.next=18;break;case 16:e.prev=16,e.t0=e.catch(0);case 18:case"end":return e.stop()}}),e,this,[[0,16]])}))),function(){return r.apply(this,arguments)}),resetForm:function(){this.formData={organSign:"",organName:"",medicareInstitutionCode:"",medicareInstitutionName:"",areaCode:[],merchantAreaName:"",privateNetworkIp:"",macAddress:"",medicareAgencyPhone:""}},onAreaCodeChange:function(e,t){this.formData.merchantAreaName=e.reduce((function(e,t){return e+(t.areaName||"")}),"")},goBack:function(){var e=this;if(!this.isEdit)return this.resetForm(),void this.$emit("hide",!1);this.$confirm("是否放弃本次操作？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.resetForm(),e.$emit("hide",!1)})).catch((function(){}))},onPhoneInput:function(e){this.formData.medicareAgencyPhone=e.replace(/\D+/,"")},onMedicareCodeInput:function(e){this.formData.medicareInstitutionCode=e.toUpperCase()},getMerchantAreaCode:function(){for(var e=this.formData.areaCode,t="",a=2;a>=0;a--)if(e[a].areaCode){t=e[a].areaCode;break}return t},handleSubmit:function(){var e=this;this.$refs.ruleForm.validate(function(){var t=Object(i.a)(regeneratorRuntime.mark((function t(a){var r,n;return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(a){t.next=2;break}return t.abrupt("return");case 2:return delete(r=Object(o.a)(Object(o.a)({},e.formData),{},{id:e.row.merchantId,merchantAreaCode:e.getMerchantAreaCode(),platformType:"web"})).areaCode,t.prev=4,t.next=7,Object(s.f)(r);case 7:(n=t.sent).msg.includes("两定机构信息不一致")?e.$confirm(n.msg,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",dangerouslyUseHTMLString:!0}).then(Object(i.a)(regeneratorRuntime.mark((function t(){return regeneratorRuntime.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,Object(s.f)(Object(o.a)(Object(o.a)({},r),{},{forceUpdate:!0}));case 2:t.sent,e.resetForm(),e.$emit("hide",!0);case 5:case"end":return t.stop()}}),t)})))).catch((function(){})):(e.resetForm(),e.$emit("hide",!0)),t.next=13;break;case 11:t.prev=11,t.t0=t.catch(4);case 13:case"end":return t.stop()}}),t,null,[[4,11]])})));return function(e){return t.apply(this,arguments)}}())}}},u=(a("9515"),a("2877")),m=Object(u.a)(d,(function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"g-fullscreen-dialog",attrs:{title:"",visible:e.isShow,"show-close":!1,modal:!1,"close-on-click-modal":!1,fullscreen:""},on:{"update:visible":function(t){e.isShow=t}}},[a("ll-list-page-layout",[a("div",{attrs:{slot:"nav-bar"},slot:"nav-bar"},[a("el-button",{on:{click:e.goBack}},[e._v("返回")]),e._v(" "),e.isEdit?a("debounce-btn",{attrs:{type:"primary"},on:{click:function(t){return e.handleSubmit()}}},[e._v(" 提交 ")]):e._e()],1),e._v(" "),a("el-form",{ref:"ruleForm",staticClass:"rule-form",attrs:{model:e.formData,rules:e.rules,disabled:!e.isEdit,"label-width":"150px"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"机构编码",prop:"organSign"}},[a("el-input",{attrs:{disabled:""},model:{value:e.formData.organSign,callback:function(t){e.$set(e.formData,"organSign",t)},expression:"formData.organSign"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"机构名称",prop:"organName"}},[a("el-input",{attrs:{disabled:""},model:{value:e.formData.organName,callback:function(t){e.$set(e.formData,"organName",t)},expression:"formData.organName"}})],1)],1)],1),e._v(" "),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"国家医保机构编码",prop:"medicareInstitutionCode"}},[a("el-input",{attrs:{maxlength:"12"},on:{input:e.onMedicareCodeInput},model:{value:e.formData.medicareInstitutionCode,callback:function(t){e.$set(e.formData,"medicareInstitutionCode",t)},expression:"formData.medicareInstitutionCode"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"国家医保机构名称",prop:"medicareInstitutionName"}},[a("el-input",{attrs:{maxlength:"30"},model:{value:e.formData.medicareInstitutionName,callback:function(t){e.$set(e.formData,"medicareInstitutionName",t)},expression:"formData.medicareInstitutionName"}})],1)],1)],1),e._v(" "),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:20}},[a("el-form-item",{attrs:{label:"统筹区",prop:"areaCode"}},[a("ll-area",{on:{change:e.onAreaCodeChange},model:{value:e.formData.areaCode,callback:function(t){e.$set(e.formData,"areaCode",t)},expression:"formData.areaCode"}})],1)],1)],1),e._v(" "),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"统筹区名称",prop:"merchantAreaName"}},[a("el-input",{attrs:{disabled:""},model:{value:e.formData.merchantAreaName,callback:function(t){e.$set(e.formData,"merchantAreaName",t)},expression:"formData.merchantAreaName"}})],1)],1)],1),e._v(" "),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"IP",prop:"privateNetworkIp"}},[a("el-input",{attrs:{maxlength:"30"},model:{value:e.formData.privateNetworkIp,callback:function(t){e.$set(e.formData,"privateNetworkIp",t)},expression:"formData.privateNetworkIp"}})],1)],1),e._v(" "),a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"MAC",prop:"macAddress"}},[a("el-input",{attrs:{maxlength:"30"},model:{value:e.formData.macAddress,callback:function(t){e.$set(e.formData,"macAddress",t)},expression:"formData.macAddress"}})],1)],1)],1),e._v(" "),a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"联系电话",prop:"medicareAgencyPhone"}},[a("el-input",{attrs:{maxlength:"11"},on:{input:e.onPhoneInput},model:{value:e.formData.medicareAgencyPhone,callback:function(t){e.$set(e.formData,"medicareAgencyPhone",t)},expression:"formData.medicareAgencyPhone"}})],1)],1)],1)],1)],1)],1)}),[],!1,null,"1ed38b6f",null).exports,p={components:{DataTable:l.a,FixedMedicalOrgDetail:m},data:function(){return{isEdit:!1,detailDialogShow:!1,loading:!1,loadingText:"加载中···",total:0,formModel:{organInfo:"",medicareInstitutionCode:""},row:{},formItem:[{label:"机构信息",prop:"organInfo",component:"el-input",attrs:{clearable:!1,placeholder:"机构编号/名称"},width:"250px"},{label:"医保代码",prop:"medicareInstitutionCode",component:"el-input",attrs:{clearable:!1},width:"250px"}],tableConf:{reserveSelection:!1,tableId:"FixedMedicalOrg",rowKey:"id",height:"100%",ref:"FixedMedicalOrg",showIndex:!0,showSelection:!1,currRow:{},data:[],colModel:[{label:"机构编码",prop:"organSign",hidden:!1,width:120},{label:"机构名称",prop:"organName",hidden:!1,width:200},{label:"国家医保机构编码",prop:"medicareInstitutionCode",hidden:!1,width:150},{label:"国家医保机构名称",prop:"medicareInstitutionName",hidden:!1,width:200},{label:"统筹区",prop:"merchantAreaCode",hidden:!1,width:120},{label:"统筹区名称",prop:"merchantAreaName",hidden:!1,width:200},{label:"IP",prop:"privateNetworkIp",hidden:!1,width:150},{label:"MAC",prop:"macAddress",hidden:!1,width:150},{label:"医保局电话",prop:"medicareAgencyPhone",hidden:!1,width:150}]}}},computed:{},mounted:function(){var e=this;this.$nextTick((function(){e.queryList()}))},methods:{handleQuery:function(){this.queryList()},handleReset:function(){this.queryList()},queryList:(n=Object(i.a)(regeneratorRuntime.mark((function e(t){var a,r,n,i,l,c;return regeneratorRuntime.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t){e.next=3;break}return this.$refs[this.tableConf.ref].init(),e.abrupt("return");case 3:return a=t.pageSize,r=t.page,n=Object(o.a)(Object(o.a)({},this.formModel),{},{pageNum:r,pageSize:a}),this.loading=!0,e.prev=6,e.next=9,Object(s.b)(n);case 9:i=e.sent,l=i.list,c=i.totalRecord,this.loading=!1,this.tableConf.data=l||[],this.total=c,e.next=20;break;case 17:e.prev=17,e.t0=e.catch(6),this.loading=!1;case 20:case"end":return e.stop()}}),e,this,[[6,17]])}))),function(e){return n.apply(this,arguments)}),goDetail:function(e){this.detailDialogShow=!0,this.isEdit=!1,this.row=e},editRow:function(e){this.detailDialogShow=!0,this.isEdit=!0,this.row=e},hideDetailDialog:function(e){this.detailDialogShow=!1,e&&this.queryList()}}},f=Object(u.a)(p,(function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("ll-list-page-layout",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],staticClass:"g-list-page g-page-style",attrs:{"element-loading-text":e.loadingText}},[a("ll-search-form",{attrs:{slot:"search-form",model:e.formModel,"form-items":e.formItem,"label-width":"100px"},on:{submit:e.handleQuery,reset:e.handleReset},slot:"search-form"}),e._v(" "),a("data-table",{directives:[{name:"table-height",rawName:"v-table-height"}],key:e.tableConf.tableId,ref:e.tableConf.ref,attrs:{"table-conf":e.tableConf,total:e.total,"row-dblclick":e.goDetail},on:{"query-change":e.queryList}},[a("template",{slot:"operation"},[a("el-table-column",{attrs:{label:"操作","min-width":"120",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.editRow(t.row)}}},[e._v("编辑")]),e._v(" "),a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.goDetail(t.row)}}},[e._v("详情")])]}}])})],1)],2),e._v(" "),e.detailDialogShow?a("fixed-medical-org-detail",{attrs:{"is-show":e.detailDialogShow,"is-edit":e.isEdit,row:e.row},on:{hide:e.hideDetailDialog}}):e._e()],1)}),[],!1,null,"7dfc0b49",null);t.default=f.exports},9515:function(e,t,a){"use strict";a("e30a")},e30a:function(e,t,a){}}]);