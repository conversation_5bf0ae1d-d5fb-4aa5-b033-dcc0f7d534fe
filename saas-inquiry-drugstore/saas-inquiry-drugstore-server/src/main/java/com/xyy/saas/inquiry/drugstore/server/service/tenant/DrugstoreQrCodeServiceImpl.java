package com.xyy.saas.inquiry.drugstore.server.service.tenant;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.drugstore.enums.ErrorCodeConstants.DRUGSTORE_MINI_APP_QR_CODE_CREATE_ERROR;
import static com.xyy.saas.inquiry.drugstore.enums.ErrorCodeConstants.DRUGSTORE_MP_QR_CODE_CREATE_ERROR;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.exception.ServerException;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.constant.InquiryConstant;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.DrugstoreQrCodeRespVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.TenantParamConfigSaveReqVO;
import com.xyy.saas.inquiry.drugstore.server.convert.tennat.TenantParamConfigConvert;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantParamConfigDO;
import com.xyy.saas.inquiry.enums.system.EnvTagEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.util.FileApiUtil;
import com.xyy.saas.inquiry.util.TimeWatchUtil;
import jakarta.annotation.Resource;
import java.awt.Color;
import java.awt.Font;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.nio.file.Files;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 门店参数配置 Service 接口
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class DrugstoreQrCodeServiceImpl implements DrugstoreQrCodeService {

    @Resource
    private WxMaService wxMaService;

    @Resource
    private WxMpService wxMpService;

    @Resource
    private TenantParamConfigService tenantParamConfigService;

    @Autowired
    private ConfigApi configApi;

    @Autowired
    private FileApi fileApi;

    @Autowired
    private TenantApi tenantApi;

    @Value("${spring.profiles.active}")
    private String activeProfile;

    @Override
    public List<DrugstoreQrCodeRespVO> getDrugstoreInquiryQrCodeList() {
        // 获取用户问诊小程序和公众号店铺二维码
        List<TenantParamConfigDO> tenantParamConfigDOS = tenantParamConfigService.getTenantParamConfig(TenantContextHolder.getRequiredTenantId()
            , List.of(TenantParamConfigTypeEnum.INQUIRY_QR_CODE, TenantParamConfigTypeEnum.INQUIRY_MP_CODE));
        List<DrugstoreQrCodeRespVO> qrCodeRespVOS = TenantParamConfigConvert.INSTANCE.convertQrCodeList(tenantParamConfigDOS);
        // 获取荷叶其他二维码
        DrugstoreQrCodeRespVO appConfigVo = TenantParamConfigConvert.INSTANCE.convertConfig(configApi.getConfigByKey(InquiryConstant.INQUIRY_APP_QR_CODE));
        if (appConfigVo != null) {
            qrCodeRespVOS.addLast(appConfigVo);
        }
        // DrugstoreQrCodeRespVO mpConfigVo = TenantParamConfigConvert.INSTANCE.convertConfig(configApi.getConfigByKey(InquiryConstant.INQUIRY_MP_QR_CODE));
        // if (mpConfigVo != null) {
        //     qrCodeRespVOS.addLast(mpConfigVo);
        // }
        return qrCodeRespVOS;
    }

    @Override
    public List<DrugstoreQrCodeRespVO> getOrCreateInquiryQrCodes() {
        return Stream.of(getDrugstoreWxMaQrCode(), getDrugstoreWxMpQrCode()).filter(Objects::nonNull).collect(Collectors.toList());
    }

    /**
     * 获取 or 创建门店微信小程序二维码
     *
     * @return
     */
    public DrugstoreQrCodeRespVO getDrugstoreWxMaQrCode(boolean... refresh) {
        try {
            // 根据门店服务状态控制
            Integer inquiryServer = tenantParamConfigService.getParamConfigValueSys2SelfInteger(TenantParamConfigTypeEnum.INQUIRY_SERVER);
            if (Objects.equals(inquiryServer, CommonStatusEnum.DISABLE.getStatus())) {
                return null;
            }
            TenantDto tenant = tenantApi.getTenant();
            if (refresh.length < 1 || !refresh[0]) {
                TenantParamConfigDO qrConfig = tenantParamConfigService.getTenantParamConfig(TenantParamConfigTypeEnum.INQUIRY_QR_CODE);
                if (qrConfig != null && StringUtils.isNotBlank(qrConfig.getParamValue())) {
                    return TenantParamConfigConvert.INSTANCE.convertQrCode(qrConfig);
                }
            }

            // 创建小程序二维码 scene 场景码 传参 eg：tenantId=xxx&more=xx   不能带 第一个字符不能带/  ,正确示例：pages/index/inquiry
            String scene = String.join("&", String.format("tenantId=%s", tenant.getId()));

            File miniCodeFile = wxMaService.getQrcodeService().createWxaCodeUnlimit(scene, configApi.getConfigValueByKey(InquiryConstant.INQUIRY_MINI_APP_QR_CODE_PAGES)
                , false, getQrCodeEnvVersion(tenant), 430, true, null, false);

            // 合并底板和文案图片 + 上传并保存
            String qrCodeUrl = mergeImageAndTextUpload(miniCodeFile, tenant.getName(), InquiryConstant.INQUIRY_MINI_APP_QR_CODE_BACKGROUND_URL);

            TenantParamConfigSaveReqVO reqVO = TenantParamConfigConvert.INSTANCE.convert(TenantParamConfigTypeEnum.INQUIRY_QR_CODE, qrCodeUrl, tenant.getId());

            tenantParamConfigService.saveOrUpdateTenantParamConfig(reqVO);

            return TenantParamConfigConvert.INSTANCE.convertQrCodeRespVO(reqVO);
        } catch (ServerException se) {
            throw se;
        } catch (WxErrorException | IOException e) {
            throw exception(DRUGSTORE_MINI_APP_QR_CODE_CREATE_ERROR, e.getMessage());
        }
    }

    @NotNull
    private String getQrCodeEnvVersion(TenantDto tenant) {

        log.info("getQrCodeEnvVersion,env:{}", activeProfile);

        if (StringUtils.equalsIgnoreCase(activeProfile, EnvTagEnum.TEST.getEnv())) {
            return "trial";
        }
        String trial = configApi.getConfigValueByKey(InquiryConstant.INQUIRY_MINI_APP_QR_CODE_TRIAL_TENANT);
        if (StringUtils.contains(trial, tenant.getId().toString())) {
            return "trial";
        }
        return "release";
    }


    /**
     * 获取 or 创建门店微信公众号二维码
     *
     * @return
     */
    public DrugstoreQrCodeRespVO getDrugstoreWxMpQrCode(boolean... refresh) {
        try {
            // TODO 根据门店服务状态控制
            if (true) {
                return null;
            }
            // 根据门店服务状态控制
            Integer inquiryServer = tenantParamConfigService.getParamConfigValueSys2SelfInteger(TenantParamConfigTypeEnum.INQUIRY_SERVER);
            if (Objects.equals(inquiryServer, CommonStatusEnum.DISABLE.getStatus())) {
                return null;
            }
            TenantDto tenant = tenantApi.getTenant();
            if (refresh.length < 1 || !refresh[0]) {
                // 公众号二维码
                TenantParamConfigDO mpConfig = tenantParamConfigService.getTenantParamConfig(TenantParamConfigTypeEnum.INQUIRY_MP_CODE);
                if (mpConfig != null && StringUtils.isBlank(mpConfig.getParamValue())) {
                    return TenantParamConfigConvert.INSTANCE.convertQrCode(mpConfig);
                }
            }
            // 创建公众号二维码
            File mpCodefile = wxMpService.getQrcodeService().qrCodePicture(wxMpService.getQrcodeService().qrCodeCreateLastTicket(""));
            // 上传并保存
            String qrCodeUrl = mergeImageAndTextUpload(mpCodefile, tenant.getName(), InquiryConstant.INQUIRY_MP_QR_CODE_BACKGROUND_URL);

            TenantParamConfigSaveReqVO configSaveReqVO = TenantParamConfigConvert.INSTANCE.convert(TenantParamConfigTypeEnum.INQUIRY_MP_CODE, qrCodeUrl, tenant.getId());
            tenantParamConfigService.createTenantParamConfig(configSaveReqVO);
            return TenantParamConfigConvert.INSTANCE.convertQrCodeRespVO(configSaveReqVO);
        } catch (ServerException se) {
            throw se;
        } catch (WxErrorException | IOException e) {
            throw exception(DRUGSTORE_MP_QR_CODE_CREATE_ERROR, e.getMessage());
        }
    }

    /**
     * 合并图片 增加文字
     *
     * @param file      原始文件
     * @param pressText 水印文字
     * @param backUrl   背景图
     * @return
     * @throws Exception
     */
    private String mergeImageAndTextUpload(File file, String pressText, String backUrl) throws IOException, ServerException {
        // 处理缩放
        TimeWatchUtil.excute(() -> ImgUtil.scale(file, file, 2000, 2000, Color.WHITE), "ImgUtil.scale缩放图片");

        // 获取图片底板
        String miniAppBackUrl = configApi.getConfigValueByKey(backUrl);
        if (StringUtils.isBlank(miniAppBackUrl)) {
            FileUtil.del(file);
            throw exception(DRUGSTORE_MINI_APP_QR_CODE_CREATE_ERROR, "获取图片底板 为空");
        }
        // 构建输出文件
        File destFile = new File(System.getProperty("java.io.tmpdir") + File.separator + "qrCode" + File.separator + IdUtil.fastSimpleUUID() + ".jpg");
        destFile.getParentFile().mkdirs();

        try (InputStream sourceImage = new URL(miniAppBackUrl).openStream();
            OutputStream outputStream = Files.newOutputStream(destFile.toPath());) {
            // 获取图片输出流
            // 添加水印(合并图片)
            TimeWatchUtil.excute(() -> ImgUtil.pressImage(
                sourceImage, // 原图
                outputStream, // 输出图
                ImgUtil.read(file), // 水印图片
                0, // x坐标修正值。 默认在中间，偏移量相对于中间偏移
                -180, // y坐标修正值。 默认在中间，偏移量相对于中间偏移
                1.0f
            ), "ImgUtil.pressImage添加水印(合并图片)");
            // 添加文案
            TimeWatchUtil.excute(() -> ImgUtil.pressText(
                destFile, // 原图
                destFile, // 输出图
                pressText, Color.WHITE, // 文字
                new Font("黑体", Font.BOLD, 110), // 字体
                0, // x坐标修正值。 默认在中间，偏移量相对于中间偏移
                1100, // y坐标修正值。 默认在中间，偏移量相对于中间偏移
                1.0f// 透明度：alpha 必须是范围 [0.0, 1.0] 之内（包含边界值）的一个浮点数字
            ), "ImgUtil.pressText添加文案");

            return FileApiUtil.createFile(FileUtil.readBytes(destFile));
        } finally {
            // 删除临时文件
            FileUtil.del(file);
            FileUtil.del(destFile);
        }
    }


    @Override
    public DrugstoreQrCodeRespVO getAppDownloadUrl() {
        return TenantParamConfigConvert.INSTANCE.convertConfig(configApi.getConfigByKey(InquiryConstant.INQUIRY_APP_QR_CODE));
    }
}