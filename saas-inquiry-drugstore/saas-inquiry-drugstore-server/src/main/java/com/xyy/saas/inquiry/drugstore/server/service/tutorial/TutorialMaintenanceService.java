package com.xyy.saas.inquiry.drugstore.server.service.tutorial;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.tutorial.vo.TutorialMaintenancePageReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.tutorial.vo.TutorialMaintenanceSaveReqVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tutorial.TutorialMaintenanceDO;
import jakarta.validation.Valid;

/**
 * 教程维护 Service 接口
 *
 * <AUTHOR>
 */
public interface TutorialMaintenanceService {

    /**
     * 创建教程维护
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Integer createTutorialMaintenance(@Valid TutorialMaintenanceSaveReqVO createReqVO);

    /**
     * 更新教程维护
     *
     * @param updateReqVO 更新信息
     */
    void updateTutorialMaintenance(@Valid TutorialMaintenanceSaveReqVO updateReqVO);

    /**
     * 删除教程维护
     *
     * @param id 编号
     */
    void deleteTutorialMaintenance(Integer id);

    /**
     * 获得教程维护
     *
     * @param id 编号
     * @return 教程维护
     */
    TutorialMaintenanceDO getTutorialMaintenance(Integer id);

    /**
     * 获得教程维护分页
     *
     * @param pageReqVO 分页查询
     * @return 教程维护分页
     */
    PageResult<TutorialMaintenanceDO> getTutorialMaintenancePage(TutorialMaintenancePageReqVO pageReqVO);

}