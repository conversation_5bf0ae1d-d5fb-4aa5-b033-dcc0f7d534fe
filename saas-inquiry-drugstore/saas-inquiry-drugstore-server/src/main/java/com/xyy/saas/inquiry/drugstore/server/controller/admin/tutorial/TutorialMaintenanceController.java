package com.xyy.saas.inquiry.drugstore.server.controller.admin.tutorial;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.tutorial.vo.TutorialMaintenancePageReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.tutorial.vo.TutorialMaintenanceRespVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.tutorial.vo.TutorialMaintenanceSaveReqVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tutorial.TutorialMaintenanceDO;
import com.xyy.saas.inquiry.drugstore.server.service.tutorial.TutorialMaintenanceService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理后台 - 教程维护")
@RestController
@RequestMapping({"/admin-api/kernel/drugstore/tutorial-maintenance", "/app-api/kernel/drugstore/tutorial-maintenance"})
@Validated
public class TutorialMaintenanceController {

    @Resource
    private TutorialMaintenanceService tutorialMaintenanceService;

    @PostMapping("/create")
    @Operation(summary = "创建教程维护")
    @PreAuthorize("@ss.hasPermission('drugstore:tutorial-maintenance:create')")
    public CommonResult<Integer> createTutorialMaintenance(@Valid @RequestBody TutorialMaintenanceSaveReqVO createReqVO) {
        return success(tutorialMaintenanceService.createTutorialMaintenance(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新教程维护")
    @PreAuthorize("@ss.hasPermission('drugstore:tutorial-maintenance:update')")
    public CommonResult<Boolean> updateTutorialMaintenance(@Valid @RequestBody TutorialMaintenanceSaveReqVO updateReqVO) {
        tutorialMaintenanceService.updateTutorialMaintenance(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除教程维护")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('drugstore:tutorial-maintenance:delete')")
    public CommonResult<Boolean> deleteTutorialMaintenance(@RequestParam("id") Integer id) {
        tutorialMaintenanceService.deleteTutorialMaintenance(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得教程维护")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    public CommonResult<TutorialMaintenanceRespVO> getTutorialMaintenance(@RequestParam("id") Integer id) {
        TutorialMaintenanceDO tutorialMaintenance = tutorialMaintenanceService.getTutorialMaintenance(id);
        return success(BeanUtils.toBean(tutorialMaintenance, TutorialMaintenanceRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得教程维护分页")
    public CommonResult<PageResult<TutorialMaintenanceRespVO>> getTutorialMaintenancePage(@Valid TutorialMaintenancePageReqVO pageReqVO) {
        PageResult<TutorialMaintenanceDO> pageResult = tutorialMaintenanceService.getTutorialMaintenancePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TutorialMaintenanceRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出教程维护 Excel")
    @PreAuthorize("@ss.hasPermission('drugstore:tutorial-maintenance:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportTutorialMaintenanceExcel(@Valid TutorialMaintenancePageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TutorialMaintenanceDO> list = tutorialMaintenanceService.getTutorialMaintenancePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "教程维护.xls", "数据", TutorialMaintenanceRespVO.class,
            BeanUtils.toBean(list, TutorialMaintenanceRespVO.class));
    }

}