package com.xyy.saas.inquiry.drugstore.server.util;

import cn.iocoder.yudao.framework.ip.core.Area;
import cn.iocoder.yudao.framework.ip.core.enums.AreaTypeEnum;
import cn.iocoder.yudao.framework.ip.core.utils.AreaUtils;
import org.apache.commons.collections.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public class AreaExtUtils {

    /**
     * 获取区级列表
     *
     * @param area
     * @return
     */
    public static List<Area> getDistrictAreaList(Integer area) {
        Area a = AreaUtils.getArea(area);
        // 获取区一级, 递归查找子集
        return getAreaListRecursive(a, AreaTypeEnum.DISTRICT.getType()).toList();
    }

    private static Stream<Area> getAreaListRecursive(Area area, Integer type) {
        if (area == null || Objects.equals(area.getType(), type)) {
            return Stream.ofNullable(area);
        }
        if (CollectionUtils.isEmpty(area.getChildren())) {
            return Stream.empty();
        }
        return area.getChildren().stream().flatMap(a -> getAreaListRecursive(a, type));
    }


}
