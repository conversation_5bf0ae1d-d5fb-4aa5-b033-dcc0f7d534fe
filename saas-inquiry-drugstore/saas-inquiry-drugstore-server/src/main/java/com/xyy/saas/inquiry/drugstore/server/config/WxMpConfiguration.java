package com.xyy.saas.inquiry.drugstore.server.config;

import com.binarywang.spring.starter.wxjava.mp.properties.WxMpProperties;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.redis.RedisTemplateWxRedisOps;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.api.impl.WxMpServiceImpl;
import me.chanjar.weixin.mp.config.impl.WxMpRedisConfigImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * 微信 公众号配置
 *
 * @Author:chenxiaoyi
 * @Date:2024/10/10 19:55
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(value = {WxMpProperties.class})
public class WxMpConfiguration {

    private final WxMpProperties wxMpProperties;

    private final StringRedisTemplate stringRedisTemplate;

    @Autowired
    public WxMpConfiguration(WxMpProperties wxMpProperties, StringRedisTemplate stringRedisTemplate) {
        this.wxMpProperties = wxMpProperties;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    @Bean
    public WxMpService wxMpService() {
        // 第一步，创建 WxMpRedisConfigImpl 对象
        WxMpRedisConfigImpl configStorage = new WxMpRedisConfigImpl(
            new RedisTemplateWxRedisOps(stringRedisTemplate),
            wxMpProperties.getConfigStorage().getKeyPrefix());
        configStorage.setAppId(wxMpProperties.getAppId());
        configStorage.setSecret(wxMpProperties.getSecret());
//        configStorage.setToken(wxMpProperties.getToken());
//        configStorage.setAesKey(wxMpProperties.getAesKey());

        // 第二步，创建 WxMpService 对象
        WxMpService service = new WxMpServiceImpl();
        service.setWxMpConfigStorage(configStorage);
        return service;
    }

}
