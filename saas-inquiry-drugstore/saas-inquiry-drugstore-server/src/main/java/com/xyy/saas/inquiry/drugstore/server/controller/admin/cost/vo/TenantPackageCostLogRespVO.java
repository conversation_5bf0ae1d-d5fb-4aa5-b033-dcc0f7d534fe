package com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;

@Schema(description = "管理后台 - 租户问诊套餐操作记录 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TenantPackageCostLogRespVO {

    @Schema(description = "额度记录表id", requiredMode = Schema.RequiredMode.REQUIRED, example = "32484")
    @ExcelProperty("额度记录表id")
    private Long id;

    private Long tenantId;

    @ExcelProperty("门店名称")
    private String tenantName;

    @ExcelProperty("门店编码")
    private String tenantPref;


    @Schema(description = "业务类型 0-问诊,1-saas...", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("业务类型 0-问诊,1-saas...")
    private Integer bizType;

    @Schema(description = "租户套餐额度表id", requiredMode = Schema.RequiredMode.REQUIRED, example = "6091")
    @ExcelProperty("租户套餐额度表id")
    private Long costId;

    @Schema(description = "问诊id", requiredMode = Schema.RequiredMode.REQUIRED, example = "20809")
    @ExcelProperty("问诊id")
    private String bizId;

    @Schema(description = "问诊类型 1图文 2视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("问诊类型 1图文 2视频")
    private Integer wayType;

    @Schema(description = "变更额度 (扣减会存负数)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("变更额度 (扣减会存负数)")
    private Long changeCost;

    @Schema(description = "记录类型 0初始 1暂停 2退款 3作废 4问诊 5问诊取消", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("记录类型 0初始 1暂停 2退款 3作废 4问诊 5问诊取消")
    private Integer recordType;

    @Schema(description = "创建时间")
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}