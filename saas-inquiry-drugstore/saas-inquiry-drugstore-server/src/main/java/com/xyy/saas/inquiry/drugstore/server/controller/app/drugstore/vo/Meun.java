package com.xyy.saas.inquiry.drugstore.server.controller.app.drugstore.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * @Author: xucao
 * @DateTime: 2025/4/23 13:53
 * @Description: 门店app菜单 VO
 **/
@Data
public class Meun {
    @Schema(description = "菜单编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "applyPrecription")
    private String code;

    @Schema(description = "菜单名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "申请处方")
    private String name;
}
