package com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo;

import com.xyy.saas.inquiry.pojo.param.ParamConfigExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 门店参数配置新增/修改 Request VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class TenantParamConfigSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "17621")
    private Long id;

    @Schema(description = "门店id", example = "2")
    private Long tenantId;

    @Schema(description = "系统类型 0-问诊,1-saas...", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "系统类型 0-问诊,1-saas...不能为空")
    private Integer bizType;

    @Schema(description = "参数类型 eg:1荷叶问诊服务 x问诊小程序二维码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "参数类型 eg:1荷叶问诊服务 x问诊小程序二维码不能为空")
    private Integer paramType;

    @Schema(description = "参数类型 eg:1荷叶问诊服务 x问诊小程序二维码", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    @NotEmpty(message = "参数类型 eg:1荷叶问诊服务 x问诊小程序二维码不能为空")
    private String paramName;

    @Schema(description = "值 eg:1 开启 x http://xxx.jpg", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "值 eg:1 开启 x http://xxx.jpg不能为空")
    private String paramValue;

    @Schema(description = "参数描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    @NotEmpty(message = "参数描述不能为空")
    private String description;

    @Schema(description = "参数配置扩展信息")
    private ParamConfigExtDto ext;
    /**
     * 创建人
     */
    private String creator;

}