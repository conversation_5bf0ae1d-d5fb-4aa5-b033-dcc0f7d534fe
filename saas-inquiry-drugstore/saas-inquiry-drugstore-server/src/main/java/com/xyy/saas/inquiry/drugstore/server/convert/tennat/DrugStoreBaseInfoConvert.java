package com.xyy.saas.inquiry.drugstore.server.convert.tennat;

import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageRelationRespDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageReqDto;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.DrugStorePackageReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.DrugStorePackageRespVO;
import com.xyy.saas.inquiry.drugstore.server.controller.app.drugstore.vo.DrugStoreInquiryPermissionVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantPackageCostDO;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.pojo.TenantDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * @Author:chenxiaoyi
 * @Date:2024/09/05 14:36
 */
@Mapper
public interface DrugStoreBaseInfoConvert {

    DrugStoreBaseInfoConvert INSTANCE = Mappers.getMapper(DrugStoreBaseInfoConvert.class);

    TenantPackageReqDto convertPackage(DrugStorePackageReqVO reqVO);

    @Mapping(source = "inquiryPackageItems", target = "packageCosts")
    DrugStorePackageRespVO convertPackageVo(TenantPackageRelationRespDto p);

    List<DrugStorePackageRespVO> convertPackageVos(List<TenantPackageRelationRespDto> respVOList);

    DrugStorePackageRespVO convertPackageDo(TenantPackageCostDO packageCostDO);

    default DrugStoreInquiryPermissionVO convertPermissionVO(List<Integer> inquiryWayTypes , TenantDto tenantDto){
        return DrugStoreInquiryPermissionVO.builder()
            .text(inquiryWayTypes.contains(InquiryWayTypeEnum.TEXT.getCode()))
            .video(inquiryWayTypes.contains(InquiryWayTypeEnum.VIDEO.getCode()))
            .tenantId(tenantDto.getId())
            .tenantName(tenantDto.getName())
            .build();
    }
}
