package com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.io.Serializable;
import java.util.List;
import lombok.Data;

@Schema(description = "管理后台 - 区域-问诊配置选项新增/修改 Request VO")
@Data
public class InquiryOptionAreaConfigDelVO implements Serializable {

    @Schema(description = "删除地区ids")
    @NotNull(message = "删除地区不能为空")
    private List<Long> ids;

}