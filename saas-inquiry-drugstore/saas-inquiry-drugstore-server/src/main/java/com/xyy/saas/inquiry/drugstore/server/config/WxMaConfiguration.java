package com.xyy.saas.inquiry.drugstore.server.config;

import cn.binarywang.wx.miniapp.api.WxMaService;
import cn.binarywang.wx.miniapp.api.impl.WxMaServiceImpl;
import cn.binarywang.wx.miniapp.config.impl.WxMaRedisBetterConfigImpl;
import com.binarywang.spring.starter.wxjava.miniapp.properties.WxMaProperties;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.redis.RedisTemplateWxRedisOps;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.StringRedisTemplate;

/**
 * 微信 小程序配置
 *
 * @Author:chenxiaoyi
 * @Date:2024/10/10 19:55
 */
@Slf4j
@Configuration
@EnableConfigurationProperties(value = {WxMaProperties.class})
public class WxMaConfiguration {

    private final WxMaProperties wxMaProperties;

    private final StringRedisTemplate stringRedisTemplate;

    @Autowired
    public WxMaConfiguration(WxMaProperties wxMaProperties, StringRedisTemplate stringRedisTemplate) {
        this.wxMaProperties = wxMaProperties;
        this.stringRedisTemplate = stringRedisTemplate;
    }

    @Bean
    public WxMaService wxMaService() {
        // 第一步，创建 WxMaRedisBetterConfigImpl 对象
        WxMaRedisBetterConfigImpl configStorage = new WxMaRedisBetterConfigImpl(
            new RedisTemplateWxRedisOps(stringRedisTemplate),
            wxMaProperties.getConfigStorage().getKeyPrefix());
        configStorage.setAppid(wxMaProperties.getAppid());
        configStorage.setSecret(wxMaProperties.getSecret());
//        configStorage.setToken(wxMaProperties.getToken());
//        configStorage.setAesKey(wxMaProperties.getAesKey());
        // 第二步，创建 WxMpService 对象
        WxMaService service = new WxMaServiceImpl();
        service.setWxMaConfig(configStorage);
        return service;
    }

}
