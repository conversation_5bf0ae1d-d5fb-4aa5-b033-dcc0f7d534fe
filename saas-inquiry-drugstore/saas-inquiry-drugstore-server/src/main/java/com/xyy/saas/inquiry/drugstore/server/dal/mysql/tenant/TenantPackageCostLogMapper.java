package com.xyy.saas.inquiry.drugstore.server.dal.mysql.tenant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo.TenantPackageCostLogReqVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantPackageCostLogDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;

/**
 * 门店问诊套餐操作记录 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantPackageCostLogMapper extends BaseMapperX<TenantPackageCostLogDO> {

    default PageResult<TenantPackageCostLogDO> selectPage(TenantPackageCostLogReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<TenantPackageCostLogDO>()
            .eqIfPresent(TenantPackageCostLogDO::getTenantId, reqVO.getTenantId())
            .eqIfPresent(TenantPackageCostLogDO::getCostId, reqVO.getCostId())
            .inIfPresent(TenantPackageCostLogDO::getCostId, reqVO.getCostIds())
            .eqIfPresent(TenantPackageCostLogDO::getBizId, reqVO.getBizId())
            .eqIfPresent(TenantPackageCostLogDO::getChangeCost, reqVO.getChangeCost())
            .betweenIfPresent(TenantPackageCostLogDO::getCreateTime, reqVO.getCreateTime())
            .eqIfPresent(TenantPackageCostLogDO::getWayType, reqVO.getWayType())
            .eqIfPresent(TenantPackageCostLogDO::getRecordType, reqVO.getRecordType())
            .inIfPresent(TenantPackageCostLogDO::getRecordType, reqVO.getRecordTypes())
            .orderByDesc(TenantPackageCostLogDO::getId));
    }

    default TenantPackageCostLogDO selectByBizId(String bizId, Integer recordType) {
        return selectOne(new LambdaQueryWrapperX<TenantPackageCostLogDO>()
            .eq(TenantPackageCostLogDO::getBizId, bizId)
            .eqIfPresent(TenantPackageCostLogDO::getRecordType, recordType), false);
    }

    default List<TenantPackageCostLogDO> selectByBizIds(List<String> bizIds, Integer recordType) {
        return selectList(new LambdaQueryWrapperX<TenantPackageCostLogDO>()
            .in(TenantPackageCostLogDO::getBizId, bizIds)
            .eqIfPresent(TenantPackageCostLogDO::getRecordType, recordType));
    }

}