package com.xyy.saas.inquiry.drugstore.server.controller.admin.tutorial.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 教程维护分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TutorialMaintenancePageReqVO extends PageParam {

    @Schema(description = "系统类型 0-问诊,1-saas...", example = "2")
    private Integer bizType;

    @Schema(description = "教程类型  0平台操作教程   1三方URL地址", example = "2")
    private Integer tutorialType;

    @Schema(description = "教程地址", example = "https://www.iocoder.cn")
    private String tutorialUrl;

    @Schema(description = "标题")
    private String title;

    @Schema(description = "详细内容")
    private String content;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}