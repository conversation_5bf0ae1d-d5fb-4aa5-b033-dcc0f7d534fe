package com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.List;

@Schema(description = "管理后台 - 门店-问诊配置选项 Response VO")
@Data
public class InquiryOptionStoreConfigRespVO implements Serializable {

    /**
     * 选项类型 {@link com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum}
     */
    @Schema(description = "选项类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "选项类型不能为空")
    private Integer optionType;

    @Schema(description = "门店id列表", example = "420100")
    private List<Long> tenantIds;


    // 问诊单配置-抗菌药品配置
    @Schema(description = "抗菌药品配置-分级目录id", example = "1")
    private Long formAntimicrobialDrugCatalogId;
    @Schema(description = "抗菌药品配置-分级目录名称", example = "abc")
    private String formAntimicrobialDrugCatalogName;
    @Schema(description = "抗菌药品配置-分级目录文件链接", example = "https://files.test.ybm100.com/INVT/Lzinq/20241205/15f4a931b0214a7695dbdee5a0d72599.xlsx")
    private String formAntimicrobialDrugCatalogUrl;


    // 问诊流程配置-处方类型填写开关
    @Schema(description = "问诊流程配置-处方类型填写开关-是否必填", example = "true")
    private Boolean procPrescriptionTypeRequired;


    // 问诊流程配置-家庭住址填写开关
    @Schema(description = "问诊流程配置-家庭住址填写开关-是否必填", example = "true")
    private Boolean procHomeAddressRequired;


    // 问诊流程配置-监护人填写开关
    @Schema(description = "问诊流程配置-监护人填写开关-是否必填", example = "true")
    private Boolean procGuardianRequired;


    // 问诊流程配置-录屏问诊配置
    @Schema(description = "录屏问诊配置", example = "true")
    private Boolean procVideoInquiry;
    @Schema(description = "录屏问诊配置-比例", example = "50")
    @Max(value = 100, message = "录屏问诊配置-比例不能超过100")
    @Min(value = 0, message = "录屏问诊配置-比例不能小于0")
    private Integer procVideoInquiryRatio;

    // 问诊流程配置-医生接诊默认页面
    @Schema(description = "医生接诊默认页面", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    // @NotNull(message = "医生接诊默认页面不能为空")
    private String procDoctorAdmissionDefaultPage;


}