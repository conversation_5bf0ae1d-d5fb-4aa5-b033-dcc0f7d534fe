package com.xyy.saas.inquiry.drugstore.server.controller.admin.tutorial.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 教程维护 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TutorialMaintenanceRespVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11597")
    @ExcelProperty("ID")
    private Integer id;

    @Schema(description = "系统类型 0-问诊,1-saas...", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("系统类型 0-问诊,1-saas...")
    private Integer bizType;

    @Schema(description = "教程类型  0平台操作教程   1三方URL地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("教程类型  0平台操作教程   1三方URL地址")
    private Integer tutorialType;

    @Schema(description = "教程地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @ExcelProperty("教程地址")
    private String tutorialUrl;

    @Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("标题")
    private String title;

    @Schema(description = "详细内容")
    @ExcelProperty("详细内容")
    private String content;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}