package com.xyy.saas.inquiry.drugstore.server.convert.tennat;

import com.xyy.saas.inquiry.constant.TenantPackageConstant;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo.TenantPackageCostLogRespVO;
import com.xyy.saas.inquiry.mq.tenant.cost.TenantChangeCostDto;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantPackageCostDO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantPackageCostLogDO;
import com.xyy.saas.inquiry.enums.tenant.CostRecordTypeEnum;
import java.util.List;
import java.util.stream.Collectors;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author:chenxiaoyi
 * @Date:2024/09/05 14:36
 */
@Mapper
public interface TenantPackageCostLogConvert {

    TenantPackageCostLogConvert INSTANCE = Mappers.getMapper(TenantPackageCostLogConvert.class);

    @Mapping(target = "wayType", source = "inquiryWayType")
    TenantPackageCostLogDO tenantPackageCostDo2RelationDO(TenantPackageCostDO tenantPackageCostDto);


    default List<TenantPackageCostLogDO> tenantPackageCostRelationDO2RecordDos(List<TenantPackageCostDO> tenantPackageCostDOList, CostRecordTypeEnum costRecordTypeEnum) {
        return tenantPackageCostDOList.stream().map(tpc -> {
            TenantPackageCostLogDO recordRelationDO = tenantPackageCostDo2RelationDO(tpc);
            recordRelationDO.setId(null);
            recordRelationDO.setCostId(tpc.getId());
            recordRelationDO.setRecordType(costRecordTypeEnum.getCode());
            recordRelationDO.setChangeCost(costRecordTypeEnum.convertCost(tpc.getCost()));
            return recordRelationDO;
        }).collect(Collectors.toList());
    }

    default TenantPackageCostLogDO costLogConvertReBack(TenantPackageCostDO tenantPackageCostDO, TenantPackageCostLogDO logDO, TenantChangeCostDto changeCostDto) {
        if (tenantPackageCostDO == null || logDO == null) {
            return null;
        }
        return TenantPackageCostLogDO.builder().tenantId(logDO.getTenantId())
            .costId(tenantPackageCostDO.getId())
            .changeCost(TenantPackageConstant.INQUIRY_COST_UNIT)
            .bizType(tenantPackageCostDO.getBizType())
            .wayType(tenantPackageCostDO.getInquiryWayType())
            .bizId(logDO.getBizId())
            .recordType(changeCostDto.getReBackRecordType()).build();
    }

    TenantPackageCostLogRespVO convert(TenantPackageCostLogDO logDO);
}
