package com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.io.Serializable;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Schema(description = "管理后台 - 门店-问诊配置选项新增/修改 Request VO")
@Data
public class InquiryOptionStoreConfigReqVO implements Serializable {

    /**
     * 选项类型 {@link com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum}
     */
    @Schema(description = "选项类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "选项类型")
    private Integer optionType;

    @Schema(description = "门店id列表", example = "[420100]")
    private List<Long> tenantIdList;


    // 问诊单配置-抗菌药品配置
    @Schema(description = "抗菌药品配置-分级目录id", example = "1")
    private Long formAntimicrobialDrugCatalogId;


    // 问诊流程配置-处方类型填写开关
    @Schema(description = "问诊流程配置-处方类型填写开关-是否必填", example = "true")
    private Boolean procPrescriptionTypeRequired;


    // 问诊流程配置-家庭住址填写开关
    @Schema(description = "问诊流程配置-家庭住址填写开关-是否必填", example = "true")
    private Boolean procHomeAddressRequired;


    // 问诊流程配置-监护人填写开关
    @Schema(description = "问诊流程配置-监护人填写开关-是否必填", example = "true")
    private Boolean procGuardianRequired;

    @Schema(description = "监护人填写年龄最低限度,如果<=6岁 则页面需要填写监护人信息", example = "6")
    private Integer procGuardianAge;

    // 监护人填写年龄扩展
    public Map<String, Object> procGuardianAgeExt() {
        return procGuardianAge == null ? null : new HashMap<>() {{
            put("procGuardianAge", procGuardianAge);
        }};
    }

    // 问诊流程配置-录屏问诊配置
    @Schema(description = "录屏问诊配置", example = "true")
    private Boolean procVideoInquiry;
    @Schema(description = "录屏问诊配置-比例", example = "50")
    @Max(value = 100, message = "录屏问诊配置-比例不能超过100")
    @Min(value = 0, message = "录屏问诊配置-比例不能小于0")
    private Integer procVideoInquiryRatio;

    // 录屏问诊配置扩展
    public Map<String, Object> procVideoInquiryExt() {
        return procVideoInquiry == null ? null : new HashMap<>() {{
            put("procVideoInquiryRatio", procVideoInquiryRatio);
        }};
    }


    // 问诊流程配置-医生接诊默认页面
    @Schema(description = "医生接诊默认页面", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    // @NotNull(message = "医生接诊默认页面不能为空")
    private String procDoctorAdmissionDefaultPage;


    @Schema(description = "条件值", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private String optionValue;


    /**
     * {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "是否使用此配置 0开启，1关闭", example = "0")
    private Integer used;


}