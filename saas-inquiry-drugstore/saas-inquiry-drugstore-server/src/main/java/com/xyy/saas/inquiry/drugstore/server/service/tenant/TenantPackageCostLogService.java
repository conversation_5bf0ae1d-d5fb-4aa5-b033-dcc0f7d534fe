package com.xyy.saas.inquiry.drugstore.server.service.tenant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo.TenantPackageCostLogReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo.TenantPackageCostLogRespVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo.TenantPackageCostLogSaveReqVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantPackageCostLogDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 门店问诊套餐操作记录 Service 接口
 *
 * <AUTHOR>
 */
public interface TenantPackageCostLogService {

    /**
     * 创建门店问诊套餐操作记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTenantPackageCostLog(@Valid TenantPackageCostLogSaveReqVO createReqVO);

    /**
     * 更新门店问诊套餐操作记录
     *
     * @param updateReqVO 更新信息
     */
    void updateTenantPackageCostLog(@Valid TenantPackageCostLogSaveReqVO updateReqVO);

    /**
     * 删除门店问诊套餐操作记录
     *
     * @param id 编号
     */
    void deleteTenantPackageCostLog(Long id);

    /**
     * 获得门店问诊套餐操作记录
     *
     * @param id 编号
     * @return 门店问诊套餐操作记录
     */
    TenantPackageCostLogDO getTenantPackageCostLog(Long id);

    /**
     * 获得门店问诊套餐操作记录分页
     *
     * @param pageReqVO 分页查询
     * @return 门店问诊套餐操作记录分页
     */
    PageResult<TenantPackageCostLogRespVO> getTenantPackageCostLogPage(TenantPackageCostLogReqVO pageReqVO);

    /**
     * 批量记录问诊额度变更记录
     *
     * @param recordRelationDOS
     * @return
     */
    boolean batchCreateTenantPackageCostLog(List<TenantPackageCostLogDO> recordRelationDOS);
}