package com.xyy.saas.inquiry.drugstore.server.service.tutorial;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.drugstore.enums.ErrorCodeConstants.TUTORIAL_MAINTENANCE_CREATE_FAIL;
import static com.xyy.saas.inquiry.drugstore.enums.ErrorCodeConstants.TUTORIAL_MAINTENANCE_NOT_EXISTS;

import cn.hutool.core.io.FileUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.tutorial.vo.TutorialMaintenancePageReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.tutorial.vo.TutorialMaintenanceSaveReqVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tutorial.TutorialMaintenanceDO;
import com.xyy.saas.inquiry.drugstore.server.dal.mysql.tutorial.TutorialMaintenanceMapper;
import com.xyy.saas.inquiry.drugstore.server.util.HtmlUtil;
import com.xyy.saas.inquiry.enums.tutorial.TutorialTypeEnum;
import jakarta.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Objects;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 教程维护 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class TutorialMaintenanceServiceImpl implements TutorialMaintenanceService {

    @Resource
    private TutorialMaintenanceMapper tutorialMaintenanceMapper;

    @Autowired
    private FileApi fileApi;

    @Override
    public Integer createTutorialMaintenance(TutorialMaintenanceSaveReqVO createReqVO) {
        // 根据教程类型处理不同逻辑
        if (Objects.equals(TutorialTypeEnum.OWN.getCode(), createReqVO.getTutorialType())) {
            // 平台教程：生成HTML文件
            File tempFile = null;
            try {
                tempFile = File.createTempFile("tutorial_", ".html");
                FileUtil.writeString(HtmlUtil.concatHTML(createReqVO.getContent(), createReqVO.getTitle()), tempFile, StandardCharsets.UTF_8);
                // 上传文件并获取URL
                createReqVO.setTutorialUrl(fileApi.createFile(FileUtil.readBytes(tempFile))); // 更新VO中的URL
            } catch (IOException e) {
                throw exception(TUTORIAL_MAINTENANCE_CREATE_FAIL);
            } finally {
                // 确保删除临时文件
                if (tempFile != null && tempFile.exists()) {
                    FileUtil.del(tempFile);
                }
            }
        }
        TutorialMaintenanceDO tutorialMaintenance = BeanUtils.toBean(createReqVO, TutorialMaintenanceDO.class);
        tutorialMaintenanceMapper.insert(tutorialMaintenance);
        return tutorialMaintenance.getId();
    }

    @Override
    public void updateTutorialMaintenance(TutorialMaintenanceSaveReqVO updateReqVO) {
        // 校验存在
        validateTutorialMaintenanceExists(updateReqVO.getId());
        // 更新
        TutorialMaintenanceDO updateObj = BeanUtils.toBean(updateReqVO, TutorialMaintenanceDO.class);
        tutorialMaintenanceMapper.updateById(updateObj);
    }

    @Override
    public void deleteTutorialMaintenance(Integer id) {
        // 校验存在
        validateTutorialMaintenanceExists(id);
        // 删除
        tutorialMaintenanceMapper.deleteById(id);
    }

    private void validateTutorialMaintenanceExists(Integer id) {
        if (tutorialMaintenanceMapper.selectById(id) == null) {
            throw exception(TUTORIAL_MAINTENANCE_NOT_EXISTS);
        }
    }

    @Override
    public TutorialMaintenanceDO getTutorialMaintenance(Integer id) {
        return tutorialMaintenanceMapper.selectById(id);
    }

    @Override
    public PageResult<TutorialMaintenanceDO> getTutorialMaintenancePage(TutorialMaintenancePageReqVO pageReqVO) {
        return tutorialMaintenanceMapper.selectPage(pageReqVO);
    }

}