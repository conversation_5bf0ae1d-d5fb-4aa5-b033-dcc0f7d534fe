package com.xyy.saas.inquiry.drugstore.server.controller.admin.tutorial.vo;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xyy.saas.inquiry.enums.tutorial.TutorialTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;
import java.util.Objects;

@Schema(description = "管理后台 - 教程维护新增/修改 Request VO")
@Data
public class TutorialMaintenanceSaveReqVO {

    @Schema(description = "ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "11597")
    private Integer id;

    @Schema(description = "系统类型 0-问诊,1-saas...", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer bizType;

    @Schema(description = "教程类型  0平台操作教程   1三方URL地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "教程类型  0平台操作教程   1三方URL地址不能为空")
    private Integer tutorialType;

    @Schema(description = "教程地址", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    private String tutorialUrl;

    @Schema(description = "标题", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "标题不能为空")
    private String title;

    @Schema(description = "详细内容")
    private String content;

    @AssertTrue(message = "教程地址不能为空")
    @JsonIgnore
    public boolean isTurValid() {
        return !Objects.equals(TutorialTypeEnum.THIRD.getCode(), tutorialType)
            || StringUtils.isNotBlank(tutorialUrl);
    }

}