package com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.io.Serializable;

@Schema(description = "管理后台 - 门店套餐信息 Req VO")
@Data
public class DrugStorePackageReqVO implements Serializable {

    /**
     * 门店id
     */
    private Long tenantId;


    @Schema(description = "业务线", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private Integer bizType;

    @Schema(description = "套餐状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer status;

    /**
     * {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "是否生效", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer effective;

}