package com.xyy.saas.inquiry.drugstore.server.service.tenant;

import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.DrugStoreParamConfigRespVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.TenantParamConfigSaveReqVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantParamConfigDO;
import com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum;
import jakarta.validation.Valid;

import java.util.List;
import java.util.Map;

/**
 * 门店参数配置 Service 接口
 *
 * <AUTHOR>
 */
public interface TenantParamConfigService {

    /**
     * 保存或更新配置
     *
     * @param paramConfigSaveReqVO 参数信息
     */
    void saveOrUpdateTenantParamConfig(@Valid TenantParamConfigSaveReqVO paramConfigSaveReqVO);

    /**
     * 创建门店参数配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createTenantParamConfig(@Valid TenantParamConfigSaveReqVO createReqVO);

    /**
     * 更新门店参数配置
     *
     * @param updateReqVO 更新信息
     */
    void updateTenantParamConfig(@Valid TenantParamConfigSaveReqVO updateReqVO);

    /**
     * 删除门店参数配置
     *
     * @param id 编号
     */
    void deleteTenantParamConfig(Long id);

    /**
     * 获得门店参数配置
     *
     * @param id 编号
     * @return 门店参数配置
     */
    TenantParamConfigDO getTenantParamConfig(Long id);


    /**
     * 获取当前门店参数配置
     *
     * @return 门店参数VO
     */
    DrugStoreParamConfigRespVO getDrugStoreParamConfig(Long id);

    // *********************** 基础参数获取 start *********************

    /**
     * 获得门店参数配置
     *
     * @param tenantParamConfigTypeEnum 枚举类型
     * @return 门店参数配置
     */
    TenantParamConfigDO getTenantParamConfig(TenantParamConfigTypeEnum tenantParamConfigTypeEnum);

    /**
     * 获取门店参数配置值
     *
     * @param tenantId                  门店id
     * @param tenantParamConfigTypeEnum 枚举
     * @return 配置值
     */
    String getTenantParamConfigValue(TenantParamConfigTypeEnum tenantParamConfigTypeEnum);

    /**
     * 获得门店参数配置
     *
     * @param tenantParamConfigTypeEnum 枚举类型
     * @return 门店参数配置
     */
    TenantParamConfigDO getTenantParamConfig(Long tenantId, TenantParamConfigTypeEnum tenantParamConfigTypeEnum);

    /**
     * 获取门店参数配置值
     *
     * @param tenantId                  门店id
     * @param tenantParamConfigTypeEnum 枚举
     * @return 配置值
     */
    String getTenantParamConfigValue(Long tenantId, TenantParamConfigTypeEnum tenantParamConfigTypeEnum);

    /**
     * 批量获得门店参数配置
     *
     * @param tenantParamConfigTypeEnums 枚举类型
     * @return 门店参数配置
     */
    List<TenantParamConfigDO> getTenantParamConfig(Long tenantId, List<TenantParamConfigTypeEnum> tenantParamConfigTypeEnums);


    Map<Integer, TenantParamConfigDO> getTenantParamConfigMap(Long tenantId, List<TenantParamConfigTypeEnum> tenantParamConfigTypeEnums);

    /**
     * 批量获取门店配置
     *
     * @param tenantIds                 门店ids
     * @param tenantParamConfigTypeEnum 枚举类型
     * @return 门店参数配置list
     */
    List<TenantParamConfigDO> getTenantParamConfig(List<Long> tenantIds, TenantParamConfigTypeEnum tenantParamConfigTypeEnum);

    /**
     * 批量获得门店参数配置
     *
     * @param tenantIds                  门店id
     * @param tenantParamConfigTypeEnums 枚举类型
     * @return 门店参数配置
     */
    Map<Long, Map<Integer, TenantParamConfigDO>> getTenantParamConfig(List<Long> tenantIds, List<TenantParamConfigTypeEnum> tenantParamConfigTypeEnums);

    // *********************** 基础参数获取  end *********************

    // *********************** 问诊参数获取  部分优先全局设置 后 门店设置 *********************


    /**
     * 获得参数配置 先取自己，没有取全局
     *
     * @param tenantParamConfigTypeEnum 枚举类型
     * @return String类型的配置值, 没有返回空, 需要StringUtils.isBlank校验
     */
    String getParamConfigValueSelf2Sys(TenantParamConfigTypeEnum tenantParamConfigTypeEnum);

    /**
     * 获得参数配置 先取自己，没有取全局
     *
     * @param tenantParamConfigTypeEnum 枚举类型
     * @return Integer类型的配置值, 没有null
     */
    Integer getParamConfigValueSelf2SysInteger(TenantParamConfigTypeEnum tenantParamConfigTypeEnum);


    /**
     * 先取全局，没有取自己
     *
     * @param tenantId                  门店id
     * @param tenantParamConfigTypeEnum 枚举
     * @return 配置值
     */
    String getParamConfigValueSys2Self(TenantParamConfigTypeEnum tenantParamConfigTypeEnum);

    /**
     * 获得参数配置 先取全局，没有取自己
     *
     * @param tenantParamConfigTypeEnum 枚举类型
     * @return Integer类型的配置值, 没有null
     */
    Integer getParamConfigValueSys2SelfInteger(TenantParamConfigTypeEnum tenantParamConfigTypeEnum);


}