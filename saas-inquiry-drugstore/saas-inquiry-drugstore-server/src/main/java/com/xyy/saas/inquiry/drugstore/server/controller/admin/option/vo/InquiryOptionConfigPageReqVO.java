package com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 问诊配置选项分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InquiryOptionConfigPageReqVO extends PageParam {

    /**
     * 目标类型 {@link com.xyy.saas.inquiry.drugstore.enums.InquiryTargetTypeEnum}
     */
    @Schema(description = "目标类型", example = "1")
    private Integer targetType;
    /**
     * 选项类型 {@link com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum}
     */
    @Schema(description = "选项类型", example = "1")
    private Integer optionType;

    @Schema(description = "区域编码或者租户id", example = "1")
    private Long targetId;

    @Schema(description = "租户ids", example = "1")
    private List<Long> targetIds;

    @Schema(description = "区域名称或者租户名称", example = "赵六")
    private String targetName;

    @Schema(description = "省")
    private String province;

    @Schema(description = "省编码")
    private String provinceCode;

    @Schema(description = "市")
    private String city;

    @Schema(description = "市编码")
    private String cityCode;

    @Schema(description = "区")
    private String area;

    @Schema(description = "区编码")
    private String areaCode;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    /**
     * {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "是否使用此配置 0开启，1关闭", example = "0")
    private Integer used;
}