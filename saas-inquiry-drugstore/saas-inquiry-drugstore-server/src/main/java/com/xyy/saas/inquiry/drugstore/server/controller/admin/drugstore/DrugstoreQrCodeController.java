package com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.DrugstoreQrCodeRespVO;
import com.xyy.saas.inquiry.drugstore.server.service.tenant.DrugstoreQrCodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "门店二维码")
@RestController
@RequestMapping("/drugstore/qrcode")
@Validated
public class DrugstoreQrCodeController {

    @Resource
    private DrugstoreQrCodeService drugstoreQrCodeService;

    @GetMapping("/get-inquiry-list")
    @Operation(summary = "获得门店问诊二维码列表")
    @PreAuthorize("@ss.hasPermission('drugstore:qrcode:query')")
    public CommonResult<List<DrugstoreQrCodeRespVO>> getInquiryQrCodeList() {
        List<DrugstoreQrCodeRespVO> qrCodeList = drugstoreQrCodeService.getDrugstoreInquiryQrCodeList();
        return success(qrCodeList);
    }

    @GetMapping("/get-or-create-inquiry")
    @Operation(summary = "获取or创建门店问诊二维码")
    @PreAuthorize("@ss.hasPermission('drugstore:qrcode:query')")
    public CommonResult<List<DrugstoreQrCodeRespVO>> getOrCreateInquiryQrCode() {
        List<DrugstoreQrCodeRespVO> qrCodeList = drugstoreQrCodeService.getOrCreateInquiryQrCodes();
        return success(qrCodeList);
    }


    @GetMapping("/get-app-download-url")
    @Operation(summary = "问诊APP下载二维码")
    @PreAuthorize("@ss.hasPermission('drugstore:qrcode:query')")
    public CommonResult<DrugstoreQrCodeRespVO> getAppDownloadUrl() {
        DrugstoreQrCodeRespVO qrCodeList = drugstoreQrCodeService.getAppDownloadUrl();
        return success(qrCodeList);
    }


}