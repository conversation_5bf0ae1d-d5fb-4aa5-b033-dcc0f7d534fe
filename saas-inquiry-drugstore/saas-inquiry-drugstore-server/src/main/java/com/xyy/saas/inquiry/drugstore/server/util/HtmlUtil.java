package com.xyy.saas.inquiry.drugstore.server.util;

public class HtmlUtil {

    private static final String CSS_STYLE = "<style>\n" +
        "article,\n" +
        "aside,\n" +
        "details,\n" +
        "figcaption,\n" +
        "figure,\n" +
        "footer,\n" +
        "header,\n" +
        "hgroup,\n" +
        "main,\n" +
        "nav,\n" +
        "section,\n" +
        "summary {\n" +
        "    display: block;\n" +
        "}\n" +
        "\n" +
        "\n" +
        "audio,\n" +
        "canvas,\n" +
        "video {\n" +
        "    display: inline-block;\n" +
        "}\n" +
        "\n" +
        "audio:not([controls]) {\n" +
        "    display: none;\n" +
        "    height: 0;\n" +
        "}\n" +
        "\n" +
        "[hidden] {\n" +
        "    display: none;\n" +
        "}\n" +
        "\n" +
        "html {\n" +
        "    font-family: sans-serif; /* 1 */\n" +
        "    -ms-text-size-adjust: 100%; /* 2 */\n" +
        "    -webkit-text-size-adjust: 100%; /* 2 */\n" +
        "}\n" +
        "\n" +
        "\n" +
        "body {\n" +
        "    margin: 0;\n" +
        "}\n" +
        "\n" +
        "a:focus {\n" +
        "    outline: thin dotted;\n" +
        "}\n" +
        "\n" +
        "a:active,\n" +
        "a:hover {\n" +
        "    outline: 0;\n" +
        "}\n" +
        "\n" +
        "h1 {\n" +
        "    font-size: 2em;\n" +
        "    margin: 0.67em 0;\n" +
        "}\n" +
        "\n" +
        "\n" +
        "\n" +
        "abbr[title] {\n" +
        "    border-bottom: 1px dotted;\n" +
        "}\n" +
        "\n" +
        "\n" +
        "b,\n" +
        "strong {\n" +
        "    font-weight: bold;\n" +
        "}\n" +
        "\n" +
        "\n" +
        "dfn {\n" +
        "    font-style: italic;\n" +
        "}\n" +
        "\n" +
        "\n" +
        "\n" +
        "hr {\n" +
        "    -moz-box-sizing: content-box;\n" +
        "    box-sizing: content-box;\n" +
        "    height: 0;\n" +
        "}\n" +
        "\n" +
        "\n" +
        "mark {\n" +
        "    background: #ff0;\n" +
        "    color: #000;\n" +
        "}\n" +
        "\n" +
        "\n" +
        "code,\n" +
        "kbd,\n" +
        "pre,\n" +
        "samp {\n" +
        "    font-family: monospace, serif;\n" +
        "    font-size: 1em;\n" +
        "}\n" +
        "\n" +
        "\n" +
        "\n" +
        "pre {\n" +
        "    white-space: pre-wrap;\n" +
        "}\n" +
        "\n" +
        "\n" +
        "\n" +
        "q {\n" +
        "    quotes: \"\\201C\" \"\\201D\" \"\\2018\" \"\\2019\";\n" +
        "}\n" +
        "\n" +
        "\n" +
        "\n" +
        "small {\n" +
        "    font-size: 80%;\n" +
        "}\n" +
        "\n" +
        "\n" +
        "\n" +
        "sub,\n" +
        "sup {\n" +
        "    font-size: 75%;\n" +
        "    line-height: 0;\n" +
        "    position: relative;\n" +
        "    vertical-align: baseline;\n" +
        "}\n" +
        "\n" +
        "sup {\n" +
        "    top: -0.5em;\n" +
        "}\n" +
        "\n" +
        "sub {\n" +
        "    bottom: -0.25em;\n" +
        "}\n" +
        "\n" +
        "\n" +
        "\n" +
        "img {\n" +
        "    border: 0;\n" +
        "}\n" +
        "\n" +
        "\n" +
        "\n" +
        "svg:not(:root) {\n" +
        "    overflow: hidden;\n" +
        "}\n" +
        "\n" +
        "\n" +
        "\n" +
        "figure {\n" +
        "    margin: 0;\n" +
        "}\n" +
        "\n" +
        "\n" +
        "\n" +
        "fieldset {\n" +
        "    border: 1px solid #c0c0c0;\n" +
        "    margin: 0 2px;\n" +
        "    padding: 0.35em 0.625em 0.75em;\n" +
        "}\n" +
        "\n" +
        "\n" +
        "\n" +
        "legend {\n" +
        "    border: 0; /* 1 */\n" +
        "    padding: 0; /* 2 */\n" +
        "}\n" +
        "\n" +
        "\n" +
        "\n" +
        "button,\n" +
        "input,\n" +
        "select,\n" +
        "textarea {\n" +
        "    font-family: inherit; /* 1 */\n" +
        "    font-size: 100%; /* 2 */\n" +
        "    margin: 0; /* 3 */\n" +
        "}\n" +
        "\n" +
        "\n" +
        "\n" +
        "button,\n" +
        "input {\n" +
        "    line-height: normal;\n" +
        "}\n" +
        "\n" +
        "\n" +
        "\n" +
        "button,\n" +
        "select {\n" +
        "    text-transform: none;\n" +
        "}\n" +
        "\n" +
        "\n" +
        "\n" +
        "button,\n" +
        "html input[type=\"button\"], /* 1 */\n" +
        "input[type=\"reset\"],\n" +
        "input[type=\"submit\"] {\n" +
        "    -webkit-appearance: button; /* 2 */\n" +
        "    cursor: pointer; /* 3 */\n" +
        "}\n" +
        "\n" +
        "\n" +
        "\n" +
        "button[disabled],\n" +
        "html input[disabled] {\n" +
        "    cursor: default;\n" +
        "}\n" +
        "\n" +
        "\n" +
        "\n" +
        "input[type=\"checkbox\"],\n" +
        "input[type=\"radio\"] {\n" +
        "    box-sizing: border-box; /* 1 */\n" +
        "    padding: 0; /* 2 */\n" +
        "}\n" +
        "\n" +
        "\n" +
        "\n" +
        "input[type=\"search\"] {\n" +
        "    -webkit-appearance: textfield; /* 1 */\n" +
        "    -moz-box-sizing: content-box;\n" +
        "    -webkit-box-sizing: content-box; /* 2 */\n" +
        "    box-sizing: content-box;\n" +
        "}\n" +
        "\n" +
        "\n" +
        "\n" +
        "input[type=\"search\"]::-webkit-search-cancel-button,\n" +
        "input[type=\"search\"]::-webkit-search-decoration {\n" +
        "    -webkit-appearance: none;\n" +
        "}\n" +
        "\n" +
        "\n" +
        "\n" +
        "button::-moz-focus-inner,\n" +
        "input::-moz-focus-inner {\n" +
        "    border: 0;\n" +
        "    padding: 0;\n" +
        "}\n" +
        "\n" +
        "\n" +
        "\n" +
        "textarea {\n" +
        "    overflow: auto; /* 1 */\n" +
        "    vertical-align: top; /* 2 */\n" +
        "}\n" +
        "\n" +
        "\n" +
        "\n" +
        "table {\n" +
        "    border-collapse: collapse;\n" +
        "    border-spacing: 0;\n" +
        "}\n" +
        "\n" +
        "    body {\n" +
        "      padding: 10px 10% 10px 10%;\n" +
        "      /* display: flex;\n" +
        "      flex-direction: column;\n" +
        "      justify-content: center;\n" +
        "      align-items: center; */\n" +
        "      \n" +
        "    }\n" +
        "\n" +
        "    table {\n" +
        "      width: 100%!important;\n" +
        "      border-collapse: collapse;\n" +
        "    }\n" +
        "\n" +
        "    table caption {\n" +
        "      font-size: 2em;\n" +
        "      font-weight: bold;\n" +
        "      margin: 1em 0;\n" +
        "    }\n" +
        "\n" +
        "    th,\n" +
        "    td {\n" +
        "      border: 1px solid #999;\n" +
        "      text-align: center;\n" +
        "      padding: 20px;\n" +
        "    }\n" +
        "\n" +
        "    table thead tr {\n" +
        "      background-color: #008c8c;\n" +
        "      color: #fff;\n" +
        "    }\n" +
        "\n" +
        "    table tbody tr:nth-child(odd) {\n" +
        "      /* background-color: #eee; */\n" +
        "    }\n" +
        "\n" +
        "    table tbody tr:hover {\n" +
        "      background-color: #ccc;\n" +
        "    }\n" +
        "\n" +
        "    table tbody tr td:first-child {\n" +
        "      /* color: #f40; */\n" +
        "    }\n" +
        "\n" +
        "    table tfoot tr td {\n" +
        "      text-align: right;\n" +
        "      padding-right: 20px;\n" +
        "    }\n" +
        "\n" +
        "\n" +
        "    /* blockquote 样式 */\n" +
        "    blockquote {\n" +
        "      display: block;\n" +
        "      border-left: 8px solid #d0e5f2;\n" +
        "      padding: 5px 10px;\n" +
        "      margin: 10px 0;\n" +
        "      line-height: 1.4;\n" +
        "      font-size: 100%;\n" +
        "      background-color: #f1f1f1;\n" +
        "    }\n" +
        "\n" +
        "    code {\n" +
        "      display: inline-block;\n" +
        "      display: inline;\n" +
        "      zoom: 1;\n" +
        "      background-color: #f1f1f1;\n" +
        "      border-radius: 3px;\n" +
        "      padding: 3px 5px;\n" +
        "      margin: 0 3px;\n" +
        "    }\n" +
        "\n" +
        "    pre code {\n" +
        "      display: block;\n" +
        "    }\n" +
        "\n" +
        "    /* ul ol 样式 */\n" +
        "    ul,\n" +
        "    ol {\n" +
        "      margin: 10px 0 10px 20px;\n" +
        "    }\n" +
        "    li {\n" +
        "      font-size: 24px;\n" +
        "    }\n" +
        "\n" +
        "    video {\n" +
        "      width: 100%;\n" +
        "      height: 100%;\n" +
        "      margin: 4px 0;\n" +
        "    }\n" +
        "\n" +
        "    [data-w-e-type='video'] {\n" +
        "      width: 100% !important;\n" +
        "      height: 500px;\n" +
        "      /* display: flex;\n" +
        "      flex-direction: column;\n" +
        "      justify-content: center;\n" +
        "      align-items: center; */\n" +
        "    }\n" +
        "\n" +
        "    .videoBox {\n" +
        "      width: 100%;\n" +
        "      background-color: black;\n" +
        "      /* display: flex;\n" +
        "      flex-direction: column;\n" +
        "      justify-content: center;\n" +
        "      align-items: center; */\n" +
        "      margin: 16px 0;\n" +
        "    }\n" +
        "\n" +
        "    img {\n" +
        "      display: block;\n" +
        "      width: 100% !important;\n" +
        "      margin: 10px 0;\n" +
        "    }\n" +
        "\n" +
        "    .tjyContainer {\n" +
        "      height: 100%;\n" +
        "      width: 100%;\n" +
        "      padding: 10px 16px;\n" +
        "      line-height: 120px;\n" +
        "      margin: 0 auto;\n" +
        "    }\n" +
        "  </style>";

    private static final String SCRIPT_CONTENT = "<script>\n" +
        "  window.onload = function () {\n" +
        "      var videos = document.getElementsByTagName('video');//获取所有video\n" +
        "      //循环给所有video添加监听事件 当前的video开始播时  调用pauseAll 将当前播放的video的索引传值过去\n" +
        "      for (var i = videos.length - 1; i >= 0; i--) {\n" +
        "        (function (n) {\n" +
        "          videos[n].addEventListener('play', function () {\n" +
        "            pauseAll(n);\n" +
        "          })\n" +
        "        })(i)\n" +
        "      }\n" +
        "      //接收调用传来的索引 循环所有video 索引与传来的索引不相同的 暂停 重载\n" +
        "      function pauseAll(index) {\n" +
        "        for (var j = videos.length - 1; j >= 0; j--) {\n" +
        "          if (j != index) {\n" +
        "            videos[j].pause();\n" +
        "            // videos[j].load();\n" +
        "          }\n" +
        "        }\n" +
        "      };\n" +
        "\n" +
        "    }\n" +
        "    document.addEventListener(\"DOMContentLoaded\", (event) => {\n" +
        "        var videos = document.getElementsByTagName('video');//获取所有video\n" +
        "        console.log(videos)\n" +
        "        for (let  i = videos.length - 1; i >= 0; i--) {\n" +
        "              let canvas = document.createElement('canvas') //创建canvas\n" +
        "               const ctx = canvas.getContext(\"2d\"); // 绘制2d\n" +
        "              let video = videos[i]\n" +
        "               video.currentTime = 1; // 视频第一帧\n" +
        "              console.log(videos[i].clientWidth, videos[i].clientHeight)\n" +
        "                 video.oncanplay = () => {\n" +
        "                   canvas.width = video.clientWidth; // 视频宽度\n" +
        "                   canvas.height = video.clientHeight; //视频高度\n" +
        "                   ctx.drawImage(video, 0, 0, video.clientWidth, video.clientHeight);\n" +
        "                 }\n" +
        "          }\n" +
        "      });\n" +
        "</script>";

    // 将富文本内容拼接成一个完成的HTML
    public static String concatHTML(String content, String title) {
        StringBuilder sb = new StringBuilder();
        sb.append("<html>");
        sb.append("<head>");
        sb.append(CSS_STYLE);
        sb.append("<meta charset=\"UTF-8\">");
        sb.append("<title>");
        sb.append(title);
        sb.append("</title>");
        sb.append("</head>");
        sb.append("<body>");
        sb.append("<div class=\"tjyContainer\">");
        sb.append(content);
        sb.append("</div>");
        sb.append(SCRIPT_CONTENT);
        sb.append("</body>");
        sb.append("</html>");
        return sb.toString();
    }
}
