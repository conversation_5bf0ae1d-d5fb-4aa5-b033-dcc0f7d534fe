package com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2024/10/10 15:58
 */
@Schema(description = "管理后台 - 门店二维码 VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DrugstoreQrCodeRespVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "17621")
    private Long id;

    @Schema(description = "系统类型 0-问诊,1-saas...", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer bizType;

    @Schema(description = "二维码类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "10000")
    private Integer qrCodeType;

    @Schema(description = "二维码名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "问诊小程序二维码")
    private String qrCodeName;

    @Schema(description = "二维码URL", requiredMode = Schema.RequiredMode.REQUIRED, example = "http://xxx.jpg")
    private String qrCodeUrl;

    @Schema(description = "参数描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "用户通过扫描二维码可访问您的店铺小程序")
    private String description;

}
