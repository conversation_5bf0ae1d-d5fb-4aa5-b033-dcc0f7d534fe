package com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.idempotent.core.annotation.Idempotent;
import cn.iocoder.yudao.framework.idempotent.core.keyresolver.impl.UserIdempotentKeyResolver;
import cn.iocoder.yudao.framework.tenant.core.aop.TenantIgnore;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageRelationPageReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantPackageRelationRespDto;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.DrugStorePackageRelationRespVO;
import com.xyy.saas.inquiry.drugstore.server.service.tenant.DrugStoreBaseInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "门店套餐关系-drugstore")
@RestController
@RequestMapping("/drugstore/package")
@Validated
public class DrugstorePackageController {

    @Resource
    private DrugStoreBaseInfoService drugStoreBaseInfoService;

    @GetMapping("/export-excel")
    @Operation(summary = "导出门店套餐关系 Excel")
    @PreAuthorize("@ss.hasPermission('system:tenant-package-relation:export')")
    @ApiAccessLog(operateType = EXPORT)
    @TenantIgnore
    @Idempotent(timeout = 10, keyResolver = UserIdempotentKeyResolver.class, message = "请求繁忙,每次导出需间隔10秒再操作") // user维度锁住 默认10s
    public void exportTenantPackageRelationExcel(TenantPackageRelationPageReqDto reqDto,
        HttpServletResponse response) throws IOException {
        reqDto.setPageSize(2000);
        List<TenantPackageRelationRespDto> list = drugStoreBaseInfoService.pageDrugStorePackageList(reqDto).getList();
        // 导出 Excel
        ExcelUtils.write(response, "门店套餐关系.xls", "数据", DrugStorePackageRelationRespVO.class,
            BeanUtils.toBean(list, DrugStorePackageRelationRespVO.class));
    }

}