package com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.DrugStoreParamConfigRespVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.DrugStoreParamConfigSaveReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.TenantParamConfigSaveReqVO;
import com.xyy.saas.inquiry.drugstore.server.convert.tennat.TenantParamConfigConvert;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantParamConfigDO;
import com.xyy.saas.inquiry.drugstore.server.service.tenant.TenantParamConfigService;
import com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.Optional;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "APP + PC 门店参数配置")
@RestController
@RequestMapping(value = {"/admin-api/kernel/drugstore/param-config", "/app-api/kernel/drugstore/param-config"})
@Validated
public class DrugstoreParamConfigController {

    @Resource
    private TenantParamConfigService tenantParamConfigService;

    @GetMapping("/get")
    @Operation(summary = "获得门店所有参数配置")
    @Parameter(name = "id", description = "门店编号", required = true, example = "1024")
    public CommonResult<DrugStoreParamConfigRespVO> getDrugStoreParamConfig(@RequestParam(name = "id", required = false) Long id) {
        return success(tenantParamConfigService.getDrugStoreParamConfig(id));
    }


    @GetMapping("/get-by-type")
    @Operation(summary = "根据类型获得当前门店参数配置")
    @Parameter(name = "type", description = "门店编号", required = true, example = "1024")
    public CommonResult<String> getDrugStoreParamConfigByType(@RequestParam(name = "type") Integer type) {
        return success(Optional.ofNullable(tenantParamConfigService.getTenantParamConfig(TenantParamConfigTypeEnum.fromType(type))).orElse(new TenantParamConfigDO()).getParamValue());
    }


    @PostMapping("/save")
    @Operation(summary = "创建门店参数配置")
    public CommonResult<Boolean> saveDrugStoreParamConfig(@Valid @RequestBody DrugStoreParamConfigSaveReqVO reqVO) {
        TenantParamConfigSaveReqVO paramConfigSaveReqVO = TenantParamConfigConvert.INSTANCE.convert(reqVO);
        tenantParamConfigService.saveOrUpdateTenantParamConfig(paramConfigSaveReqVO);
        return success(true);
    }


}