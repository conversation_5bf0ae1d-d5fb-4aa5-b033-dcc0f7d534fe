package com.xyy.saas.inquiry.drugstore.server.convert.tennat;

import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.infra.api.config.vo.ConfigVO;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantParamConfigDTO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.DrugStoreParamConfigSaveReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.DrugstoreQrCodeRespVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.TenantParamConfigSaveReqVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantParamConfigDO;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * 门店参数配置 Convert
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantParamConfigConvert {

    TenantParamConfigConvert INSTANCE = Mappers.getMapper(TenantParamConfigConvert.class);

    List<DrugstoreQrCodeRespVO> convertQrCodeList(List<TenantParamConfigDO> tenantParamConfigDOS);

    @Mapping(target = "qrCodeType", source = "paramType")
    @Mapping(target = "qrCodeName", source = "paramName")
    @Mapping(target = "qrCodeUrl", source = "paramValue")
    DrugstoreQrCodeRespVO convertQrCode(TenantParamConfigDO qrConfig);


    @Mapping(target = "qrCodeType", source = "paramType")
    @Mapping(target = "qrCodeName", source = "paramName")
    @Mapping(target = "qrCodeUrl", source = "paramValue")
    DrugstoreQrCodeRespVO convertQrCodeRespVO(TenantParamConfigSaveReqVO qrConfig);

    @Mapping(target = "qrCodeName", source = "name")
    @Mapping(target = "qrCodeUrl", source = "value")
    @Mapping(target = "description", source = "remark")
    DrugstoreQrCodeRespVO convertConfig(ConfigVO appConfigVo);


    default TenantParamConfigSaveReqVO convert(DrugStoreParamConfigSaveReqVO reqVO) {
        TenantParamConfigSaveReqVO paramConfigSaveReqVO = convert(TenantParamConfigTypeEnum.fromType(reqVO.getParamType()), reqVO.getParamValue());
        paramConfigSaveReqVO.setExt(reqVO.getExt());
        return paramConfigSaveReqVO;
    }


    default TenantParamConfigSaveReqVO convert(TenantParamConfigTypeEnum typeEnum, String paramValue) {
        return convert(typeEnum, paramValue, TenantContextHolder.getRequiredTenantId());
    }

    default TenantParamConfigSaveReqVO convert(TenantParamConfigTypeEnum typeEnum, String paramValue, Long tenantId) {
        return TenantParamConfigSaveReqVO.builder()
            .tenantId(tenantId)
            .bizType(BizTypeEnum.HYWZ.getCode())
            .paramType(typeEnum.getType())
            .paramName(typeEnum.getName())
            .paramValue(paramValue)
            .description(typeEnum.getDescription()).build();
    }

    List<TenantParamConfigDTO> convertDtos(List<TenantParamConfigDO> paramConfigs);

    @Mapping(target = "operateTime", expression = "java(java.time.LocalDateTime.now())")
    TenantParamConfigDTO convertDto(TenantParamConfigDO tenantParamConfig);


    default void fillInquiryOptionConfig(InquiryOptionConfigRespDto optionConfig, Map<Integer, TenantParamConfigDO> paramConfigMap) {
        Optional.ofNullable(paramConfigMap.get(TenantParamConfigTypeEnum.INQUIRY_WESTERN_MEDICINE_BRING.getType()))
            .ifPresent(config -> optionConfig.setInquiryWesternMedicineBring(CommonStatusEnum.isEnable(NumberUtil.parseInt(config.getParamValue()))));

        Optional.ofNullable(paramConfigMap.get(TenantParamConfigTypeEnum.INQUIRY_CHINESE_MEDICINE_BRING.getType()))
            .ifPresent(config -> optionConfig.setInquiryChineseMedicineBring(CommonStatusEnum.isEnable(NumberUtil.parseInt(config.getParamValue()))));
    }

}
