package com.xyy.saas.inquiry.drugstore.server.service.tenant;

import com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo.DrugstoreQrCodeRespVO;

import java.util.List;

/**
 * 门店参数配置 Service 接口
 *
 * <AUTHOR>
 */
public interface DrugstoreQrCodeService {

    /**
     * 获取门店问诊二维码列表
     *
     * @return
     */
    List<DrugstoreQrCodeRespVO> getDrugstoreInquiryQrCodeList();

    /**
     * 获取 or 创建门店问诊二维码
     *
     * @return 二维码列表
     */
    List<DrugstoreQrCodeRespVO> getOrCreateInquiryQrCodes();


    /**
     * 微信小程序url
     *
     * @return
     */
    DrugstoreQrCodeRespVO getDrugstoreWxMaQrCode(boolean... refresh);

    /**
     * 微信公众号url
     *
     * @return
     */
    DrugstoreQrCodeRespVO getDrugstoreWxMpQrCode(boolean... refresh);

    /**
     * 获取app下载地址链接
     *
     * @return
     */
    DrugstoreQrCodeRespVO getAppDownloadUrl();
}