package com.xyy.saas.inquiry.drugstore.server.service.option;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionAreaConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigQueryDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionGlobalConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionStoreConfigRespDto;
import com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionAreaConfigReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionConfigPageReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionGlobalConfigReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionStoreConfigReqVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.option.InquiryOptionConfigDO;
import com.xyy.saas.inquiry.pojo.TenantDto;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 问诊配置选项 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryOptionConfigService {

    /**
     * 全局-保存问诊配置选项
     *
     * @param reqVO 保存信息
     * @return 新增&更新行数
     */
    int saveInquiryOptionGlobalConfig(@Valid InquiryOptionGlobalConfigReqVO reqVO);

    /**
     * 全局-获得问诊配置选项
     *
     * @return 问诊配置选项
     */
    InquiryOptionGlobalConfigRespDto getInquiryOptionGlobalConfig(InquiryOptionConfigQueryDto dto);

    /**
     * 区域-保存问诊配置选项
     *
     * @param reqVO 保存信息
     * @return 新增&更新行数
     */
    int saveInquiryOptionAreaConfig(@Valid InquiryOptionAreaConfigReqVO reqVO);

    /**
     * 区域-获得问诊配置选项（分页）
     *
     * @return 问诊配置选项
     */
    PageResult<InquiryOptionAreaConfigRespDto> getInquiryOptionAreaConfigPage(InquiryOptionConfigPageReqVO reqVO);

    List<InquiryOptionAreaConfigRespDto> getInquiryOptionAreaAllConfig(@Valid InquiryOptionConfigPageReqVO reqVO);


    /**
     * 区域-获取问诊配置选项
     *
     * @param id 编号
     */
    InquiryOptionAreaConfigRespDto getInquiryOptionAreaConfig(Long id);

    /**
     * 门店-保存问诊配置选项
     *
     * @param reqVO 保存信息
     * @return 新增&更新行数
     */
    int saveInquiryOptionStoreConfig(@Valid InquiryOptionStoreConfigReqVO reqVO);

    /**
     * 门店-获得问诊配置选项（分页）
     *
     * @return 问诊配置选项
     */
    PageResult<InquiryOptionStoreConfigRespDto> getInquiryOptionStoreConfigPage(InquiryOptionConfigPageReqVO reqVO);

    /**
     * 门店-获取问诊配置选项
     *
     * @param id 编号
     */
    InquiryOptionStoreConfigRespDto getInquiryOptionStoreConfig(Long id);

    /**
     * 删除问诊配置选项（区域 / 门店单条配置记录）
     *
     * @param id 编号
     */
    int deleteInquiryOptionConfig(Long id);

    /**
     * 删除问诊配置选项（区域 / 门店多条配置记录）
     *
     * @param ids 编号
     */
    int batchDeleteInquiryOptionConfig(List<Long> ids);

    /**
     * 查询问诊配置选项 （不传 optionTypeEnums 则默认查询所有配置） 根据配置类型查询 + 优先级覆盖
     *
     * @param tenantDto
     * @param optionTypeEnums
     * @return
     */
    InquiryOptionConfigRespDto getInquiryOptionConfig(TenantDto tenantDto, InquiryOptionTypeEnum... optionTypeEnums);


    PageResult<TenantRespDto> getTenantPage(TenantReqDto tenantReqDto);

    /**
     * 查询问诊配置选项
     *
     * @param targetId
     * @param optionTypeEnum
     * @return
     */
    InquiryOptionConfigDO getInquiryOptionConfig(Long targetId, InquiryOptionTypeEnum optionTypeEnum);
}