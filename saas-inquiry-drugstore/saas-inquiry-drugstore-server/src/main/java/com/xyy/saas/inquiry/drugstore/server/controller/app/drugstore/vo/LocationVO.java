package com.xyy.saas.inquiry.drugstore.server.controller.app.drugstore.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2024/10/23 11:28
 */
@Schema(description = "定位VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
public class LocationVO implements Serializable {

    @Schema(description = "经度", requiredMode = Schema.RequiredMode.REQUIRED, example = "116.397128")
    @NotBlank
    private String longitude;

    @Schema(description = "维度", requiredMode = Schema.RequiredMode.REQUIRED, example = "39.916527")
    @NotBlank
    private String latitude;

    @Schema(description = "距离限制开关 0是 1否", example = "1")
    private Integer distanceLimitSwitch = 1;

}
