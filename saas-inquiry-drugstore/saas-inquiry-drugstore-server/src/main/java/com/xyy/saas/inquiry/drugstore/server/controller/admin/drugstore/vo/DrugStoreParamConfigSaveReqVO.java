package com.xyy.saas.inquiry.drugstore.server.controller.admin.drugstore.vo;

import cn.iocoder.yudao.framework.common.validation.InEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum;
import com.xyy.saas.inquiry.pojo.param.ParamConfigExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 门店参数配置 Response VO")
@Data
public class DrugStoreParamConfigSaveReqVO {


    @Schema(description = "参数类型 eg:1荷叶问诊服务 x问诊小程序二维码", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "参数类型 eg:1荷叶问诊服务 x问诊小程序二维码不能为空")
    @InEnum(TenantParamConfigTypeEnum.class)
    private Integer paramType;


    @Schema(description = "值 eg:1 开启 x http://xxx.jpg", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "值 eg:1 开启 x http://xxx.jpg不能为空")
    private String paramValue;


    @Schema(description = "参数配置扩展信息")
    private ParamConfigExtDto ext;

}