package com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo;

import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageRelationStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

@Schema(description = "管理后台 - 门店问诊套餐额度新增/修改 Request VO")
@Data
@Accessors(chain = true)
public class TenantPackageCostSaveReqVO {

    @Schema(description = "id", requiredMode = Schema.RequiredMode.REQUIRED, example = "22198")
    private Long id;

    @Schema(description = "门店id", example = "29653")
    private Long tenantId;
    /**
     * {@link BizTypeEnum}
     */
    @Schema(description = "系统业务类型", example = "0")
    private Integer bizType;

    @Schema(description = "当前在用的套餐订单表id", requiredMode = Schema.RequiredMode.REQUIRED, example = "29653")
    @NotNull(message = "当前在用的套餐订单表id不能为空")
    private Long packageRelationId;

    @Schema(description = "问诊额度 -1不限", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "问诊额度 -1不限 不能为空")
    private Long cost;

    @Schema(description = "问诊剩余额度 -1不限", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "问诊剩余额度 -1不限不能为空")
    private Long surplusCost;

    @Schema(description = "套餐开始时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "套餐开始时间不能为空")
    private LocalDateTime startTime;

    @Schema(description = "套餐结束时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "套餐结束时间不能为空")
    private LocalDateTime endTime;

    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "版本号不能为空")
    private Long version;

    @Schema(description = "问诊形式 1图文 2视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "问诊形式 1图文 2视频不能为空")
    private Integer inquiryWayType;

    /**
     * {@link TenantPackageRelationStatusEnum}
     */
    @Schema(description = "订单状态, 0未生效, 1生效中, 2已过期, 3已用尽, 4退款, 5作废", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "订单状态, 0未生效, 1生效中, 2已过期, 3已用尽, 4退款, 5作废不能为空")
    private Integer status;

}