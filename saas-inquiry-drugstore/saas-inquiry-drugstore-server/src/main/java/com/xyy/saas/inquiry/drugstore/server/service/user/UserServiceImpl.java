package com.xyy.saas.inquiry.drugstore.server.service.user;

import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.module.system.api.user.UserCompatApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.constraints.NotEmpty;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

/**
 * @Author: xucao
 * @DateTime: 2025/5/8 15:24
 * @Description: 用户服务实现类
 **/
@Service
public class UserServiceImpl implements UserService{


    final String tokenHeader = "Authorization";


    final String tokenParameter = "token";

    @DubboReference
    private UserCompatApi userCompatApi;

    /**
     * @return
     */
    @Override
    public AdminUserRespDTO getUser() {
        // 获取当前请求的 HttpServletRequest 对象
        HttpServletRequest request = ((ServletRequestAttributes) RequestContextHolder.currentRequestAttributes()).getRequest();

        String token = SecurityFrameworkUtils.obtainAuthorization(request,
            tokenHeader, tokenParameter);

        return userCompatApi.getUser(token);
    }
}
