package com.xyy.saas.inquiry.drugstore.server.controller.app.drugstore.vo;

import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2024/10/23 11:48
 */
@Schema(description = "门店问诊权限VO")
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DrugStoreInquiryPermissionVO implements Serializable {

    @Schema(description = "租户id",example = "1")
    private Long tenantId;


    @Schema(description = "租户名称",example = "1")
    private String tenantName;

    /**
     * {@link InquiryWayTypeEnum} 是否有图文问诊权限
     */
    @Schema(description = "图文问诊权限", requiredMode = Schema.RequiredMode.REQUIRED, example = "true")
    private boolean text;
    /**
     * 是否有视频问诊权限
     */
    @Schema(description = "视频问诊权限", requiredMode = Schema.RequiredMode.REQUIRED, example = "false")
    private boolean video;

    /**
     * app 首页核心功能入口菜单列表
     */
    @Schema(description = "app首页核心功能", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private List<Meun> appHomeCoreMeunItem;


    /**
     * app 首页核心功能入口菜单列表
     */
    @Schema(description = "app首页辅助查询功能菜单", requiredMode = Schema.RequiredMode.REQUIRED, example = "0")
    private List<Meun> appHomeAuxMeunItem;

}
