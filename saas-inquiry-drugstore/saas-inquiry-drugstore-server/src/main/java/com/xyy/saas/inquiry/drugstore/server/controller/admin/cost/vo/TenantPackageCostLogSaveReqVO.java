package com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo;

import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 门店问诊套餐操作记录新增/修改 Request VO")
@Data
@Accessors(chain = true)
public class TenantPackageCostLogSaveReqVO {

    @Schema(description = "额度记录表id", requiredMode = Schema.RequiredMode.REQUIRED, example = "6106")
    private Long id;

    @Schema(description = "门店id", example = "29653")
    private Long tenantId;
    /**
     * {@link BizTypeEnum}
     */
    @Schema(description = "系统业务类型", example = "0")
    @NotNull(message = "系统业务类型不能为空")
    private Integer bizType;

    @Schema(description = "使其变化的套餐订单表id", requiredMode = Schema.RequiredMode.REQUIRED, example = "4602")
    @NotNull(message = "使其变化的套餐订单表id不能为空")
    private Long packageRelationId;

    @Schema(description = "问诊id", requiredMode = Schema.RequiredMode.REQUIRED, example = "25124")
    private String inquiryPref;

    @Schema(description = "变更额度 (扣减会存负数)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "变更额度 (扣减会存负数)不能为空")
    private Long changeCost;

    @Schema(description = "问诊类型 1图文 2视频", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "问诊类型 1图文 2视频不能为空")
    private Integer inquiryWayType;

    @Schema(description = "记录类型 0初始创建 1问诊 2退款 3作废", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "记录类型 0初始创建 1问诊 2退款 3作废不能为空")
    private Integer recordType;

}