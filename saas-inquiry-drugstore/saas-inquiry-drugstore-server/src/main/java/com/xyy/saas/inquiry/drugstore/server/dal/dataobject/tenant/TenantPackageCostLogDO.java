package com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xyy.saas.inquiry.enums.system.BizTypeEnum;
import lombok.*;
import lombok.experimental.Accessors;

/**
 * 门店问诊套餐操作记录 DO
 *
 * <AUTHOR>
 */
@TableName("saas_tenant_package_cost_log")
@KeySequence("saas_tenant_package_cost_log_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class TenantPackageCostLogDO extends BaseDO {

    /**
     * 额度记录表id
     */
    @TableId
    private Long id;
    /**
     * 门店id
     */
    private Long tenantId;
    /**
     * 系统业务类型 {@link BizTypeEnum}
     */
    private Integer bizType;
    /**
     * 使其变化的套餐额度id
     */
    private Long costId;
    /**
     * 问诊id
     */
    private String bizId;
    /**
     * 变更额度
     */
    private Long changeCost;
    /**
     * 业务方式类型 (问诊类型 1图文 2视频)
     */
    private Integer wayType;
    /**
     * 记录类型 0初始创建 1问诊 2退款 3作废
     */
    private Integer recordType;

}