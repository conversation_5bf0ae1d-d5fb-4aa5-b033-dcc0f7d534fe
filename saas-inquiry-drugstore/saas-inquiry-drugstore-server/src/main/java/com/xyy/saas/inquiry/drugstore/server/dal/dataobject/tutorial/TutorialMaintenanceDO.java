package com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tutorial;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;

/**
 * 教程维护 DO
 *
 * <AUTHOR>
 */
@TableName("saas_tutorial_maintenance")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TutorialMaintenanceDO extends BaseDO {

    /**
     * ID
     */
    @TableId(type = IdType.AUTO)
    private Integer id;
    /**
     * 系统类型 0-问诊,1-saas...
     */
    private Integer bizType;
    /**
     * 教程类型  0平台操作教程   1三方URL地址
     */
    private Integer tutorialType;
    /**
     * 教程地址
     */
    private String tutorialUrl;
    /**
     * 标题
     */
    private String title;
    /**
     * 详细内容
     */
    private String content;

}