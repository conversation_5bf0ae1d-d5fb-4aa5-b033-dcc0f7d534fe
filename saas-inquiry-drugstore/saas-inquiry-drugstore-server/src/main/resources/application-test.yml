spring:
  cloud:
    nacos:
      discovery:
        server-addr: ************:8848
  datasource:
    dynamic:
      datasource:
        master:
          url: ************************************************************************************************************************************************************************************** # MODE 使用 MySQL 模式；DATABASE_TO_UPPER 配置表和字段使用小写
          username: app_remote_prescription_w
          password: UY27Hdy9uTYe
        slave: # 模拟从库，可根据自己需要修改 # 模拟从库，可根据自己需要修改
          lazy: true # 开启懒加载，保证启动速度
          url: ************************************************************************************************************************************************************************************** # MODE 使用 MySQL 模式；DATABASE_TO_UPPER 配置表和字段使用小写
          username: app_remote_prescription_w
          password: UY27Hdy9uTYe
  data:
    redis:
      host: ************** # 地址
      port: 6379 # 端口
      password: xj2023 # 密码，建议生产环境开启
      database: 0