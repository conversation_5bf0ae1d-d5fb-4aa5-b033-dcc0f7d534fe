<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.drugstore.server.dal.mysql.tenant.TenantPackageCostMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->

  <update id="deductTenantCost">
    update saas_tenant_package_cost
    set surplus_cost = surplus_cost - #{inquiryCostUnit}
    where id = #{id}
      and surplus_cost > 0
      and surplus_cost = #{surplusCost}
  </update>

  <update id="reBackTenantCost">
    update saas_tenant_package_cost
    set surplus_cost = surplus_cost + #{inquiryCostUnit}
    where id = #{id}
      and surplus_cost = #{surplusCost}
  </update>
  <update id="updateServerTimeByIds">
    update saas_tenant_package_cost set start_time = #{costDto.startTime},end_time = #{costDto.endTime} where id in
    <foreach collection="ids" open="(" item="id" separator="," close=")">
      #{id}
    </foreach>
  </update>

</mapper>