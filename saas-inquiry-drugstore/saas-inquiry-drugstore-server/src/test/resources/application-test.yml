spring:
  cloud:
    nacos:
      discovery:
        server-addr: 10.22.68.231:8848
      config:
        server-addr: ${spring.cloud.nacos.discovery.server-addr}
        import-check:
          enabled: false
  datasource:
    dynamic:
      datasource:
        master:
          url: ************************************************************************************************************************************************************************************** # MODE 使用 MySQL 模式；DATABASE_TO_UPPER 配置表和字段使用小写
          username: app_remote_prescription_w
          password: UY27Hdy9uTYe
        slave: # 模拟从库，可根据自己需要修改 # 模拟从库，可根据自己需要修改
          lazy: true # 开启懒加载，保证启动速度
          url: ************************************************************************************************************************************************************************************** # MODE 使用 MySQL 模式；DATABASE_TO_UPPER 配置表和字段使用小写
          username: app_remote_prescription_w
          password: UY27Hdy9uTYe
  data:
    redis:
      host: ************** # 地址
      port: 6379 # 端口
      #      host: db2-saas-test.redis.ybm100.top # 地址
      #      port: 40001 # 端口
      password: xj2023 # 密码，建议生产环境开启


#dubbo:
#  registry:
##    address: zookeeper://127.0.0.1:2181 # 注册中心地址
#    address: nacos://localhost:8848 # 注册中心地址

yudao:
  info:
    version: 1.0.0
    base-package: cn.iocoder.yudao,com.xyy.saas # @MapperScan扫描包

dubbo:
  config-center:
    address: nacos://${spring.cloud.nacos.discovery.server-addr}
  registry:
    address: nacos://${spring.cloud.nacos.discovery.server-addr}
  metadata-report:
    address: nacos://${spring.cloud.nacos.discovery.server-addr}