package com.xyy.saas.inquiry.drugstore.server.tenant;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.xyy.saas.inquiry.drugstore.enums.ErrorCodeConstants.TENANT_PACKAGE_INQUIRY_RECORD_RELATION_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.drugstore.server.BaseIntegrationTest;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo.TenantPackageCostLogReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo.TenantPackageCostLogRespVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo.TenantPackageCostLogSaveReqVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantPackageCostLogDO;
import com.xyy.saas.inquiry.drugstore.server.dal.mysql.tenant.TenantPackageCostLogMapper;
import com.xyy.saas.inquiry.drugstore.server.service.tenant.TenantPackageCostLogServiceImpl;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

/**
 * {@link TenantPackageCostLogServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(TenantPackageCostLogServiceImpl.class)
public class TenantPackageCostLogServiceImplTest extends BaseIntegrationTest {

    @Resource
    private TenantPackageCostLogServiceImpl tenantPackageInquiryRecordRelationService;

    @Resource
    private TenantPackageCostLogMapper tenantPackageCostLogMapper;

    @Test
    public void testCreateTenantPackageInquiryRecordRelation_success() {
        // 准备参数
        TenantPackageCostLogSaveReqVO createReqVO = randomPojo(TenantPackageCostLogSaveReqVO.class).setId(null);

        // 调用
        Long tenantPackageInquiryRecordRelationId = tenantPackageInquiryRecordRelationService.createTenantPackageCostLog(createReqVO);
        // 断言
        assertNotNull(tenantPackageInquiryRecordRelationId);
        // 校验记录的属性是否正确
        TenantPackageCostLogDO tenantPackageInquiryRecordRelation = tenantPackageCostLogMapper.selectById(tenantPackageInquiryRecordRelationId);
        assertPojoEquals(createReqVO, tenantPackageInquiryRecordRelation, "id");
    }

    @Test
    public void testUpdateTenantPackageInquiryRecordRelation_success() {
        // mock 数据
        TenantPackageCostLogDO dbTenantPackageInquiryRecordRelation = randomPojo(TenantPackageCostLogDO.class);
        tenantPackageCostLogMapper.insert(dbTenantPackageInquiryRecordRelation);// @Sql: 先插入出一条存在的数据
        // 准备参数
        TenantPackageCostLogSaveReqVO updateReqVO = randomPojo(TenantPackageCostLogSaveReqVO.class, o -> {
            o.setId(dbTenantPackageInquiryRecordRelation.getId()); // 设置更新的 ID
        });

        // 调用
        tenantPackageInquiryRecordRelationService.updateTenantPackageCostLog(updateReqVO);
        // 校验是否更新正确
        TenantPackageCostLogDO tenantPackageInquiryRecordRelation = tenantPackageCostLogMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, tenantPackageInquiryRecordRelation);
    }

    @Test
    public void testUpdateTenantPackageInquiryRecordRelation_notExists() {
        // 准备参数
        TenantPackageCostLogSaveReqVO updateReqVO = randomPojo(TenantPackageCostLogSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> tenantPackageInquiryRecordRelationService.updateTenantPackageCostLog(updateReqVO), TENANT_PACKAGE_INQUIRY_RECORD_RELATION_NOT_EXISTS);
    }

    @Test
    public void testDeleteTenantPackageInquiryRecordRelation_success() {
        // mock 数据
        TenantPackageCostLogDO dbTenantPackageInquiryRecordRelation = randomPojo(TenantPackageCostLogDO.class);
        tenantPackageCostLogMapper.insert(dbTenantPackageInquiryRecordRelation);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbTenantPackageInquiryRecordRelation.getId();

        // 调用
        tenantPackageInquiryRecordRelationService.deleteTenantPackageCostLog(id);
        // 校验数据不存在了
        assertNull(tenantPackageCostLogMapper.selectById(id));
    }

    @Test
    public void testDeleteTenantPackageInquiryRecordRelation_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> tenantPackageInquiryRecordRelationService.deleteTenantPackageCostLog(id), TENANT_PACKAGE_INQUIRY_RECORD_RELATION_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetTenantPackageInquiryRecordRelationPage() {
        // mock 数据
        TenantPackageCostLogDO dbTenantPackageInquiryRecordRelation = randomPojo(TenantPackageCostLogDO.class, o -> { // 等会查询到
            o.setCostId(null);
            o.setBizId(null);
            o.setChangeCost(null);
            o.setCreateTime(null);
            o.setWayType(null);
            o.setRecordType(null);
        });
        tenantPackageCostLogMapper.insert(dbTenantPackageInquiryRecordRelation);
        // 测试 packageOrderId 不匹配
        tenantPackageCostLogMapper.insert(cloneIgnoreId(dbTenantPackageInquiryRecordRelation, o -> o.setCostId(null)));
        // 测试 inquiryGuid 不匹配
        tenantPackageCostLogMapper.insert(cloneIgnoreId(dbTenantPackageInquiryRecordRelation, o -> o.setBizId(null)));
        // 测试 changeCost 不匹配
        tenantPackageCostLogMapper.insert(cloneIgnoreId(dbTenantPackageInquiryRecordRelation, o -> o.setChangeCost(null)));
        // 测试 createTime 不匹配
        tenantPackageCostLogMapper.insert(cloneIgnoreId(dbTenantPackageInquiryRecordRelation, o -> o.setCreateTime(null)));
        // 测试 inquiryWayType 不匹配
        tenantPackageCostLogMapper.insert(cloneIgnoreId(dbTenantPackageInquiryRecordRelation, o -> o.setWayType(null)));
        // 测试 recordType 不匹配
        tenantPackageCostLogMapper.insert(cloneIgnoreId(dbTenantPackageInquiryRecordRelation, o -> o.setRecordType(null)));
        // 准备参数
        TenantPackageCostLogReqVO reqVO = new TenantPackageCostLogReqVO();
        reqVO.setCostId(null);
        reqVO.setBizId(null);
        reqVO.setChangeCost(null);
        reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
        reqVO.setWayType(null);
        reqVO.setRecordType(null);

        // 调用
        PageResult<TenantPackageCostLogRespVO> pageResult = tenantPackageInquiryRecordRelationService.getTenantPackageCostLogPage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbTenantPackageInquiryRecordRelation, pageResult.getList().get(0));
    }

}