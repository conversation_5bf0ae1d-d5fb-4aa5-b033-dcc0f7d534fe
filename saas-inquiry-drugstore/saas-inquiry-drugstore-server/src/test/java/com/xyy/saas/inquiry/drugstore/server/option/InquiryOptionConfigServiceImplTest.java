package com.xyy.saas.inquiry.drugstore.server.option;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static cn.iocoder.yudao.module.system.enums.ErrorCodeConstants.TENANT_NOT_EXISTS;
import static com.xyy.saas.inquiry.drugstore.enums.ErrorCodeConstants.INQUIRY_OPTION_CONFIG_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionAreaConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionStoreConfigRespDto;
import com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum;
import com.xyy.saas.inquiry.drugstore.enums.InquiryTargetTypeEnum;
import com.xyy.saas.inquiry.drugstore.server.BaseIntegrationTest;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionAreaConfigReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionConfigPageReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.option.vo.InquiryOptionStoreConfigReqVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.option.InquiryOptionConfigDO;
import com.xyy.saas.inquiry.drugstore.server.dal.mysql.option.InquiryOptionConfigMapper;
import com.xyy.saas.inquiry.drugstore.server.service.option.InquiryOptionConfigServiceImpl;
import com.xyy.saas.inquiry.pojo.TenantDto;
import jakarta.annotation.Resource;
import java.util.List;
import jakarta.annotation.Nonnull;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.context.annotation.Import;

/**
 * {@link InquiryOptionConfigServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import({InquiryOptionConfigServiceImpl.class})
public class InquiryOptionConfigServiceImplTest extends BaseIntegrationTest {

    @Resource
    private InquiryOptionConfigServiceImpl inquiryOptionConfigService;

    @Resource
    private InquiryOptionConfigMapper inquiryOptionConfigMapper;

    @MockBean
    private TenantApi tenantApi;


    @Test
    public void testCreateInquiryOptionConfig_success() {

    }

    @Test
    public void testUpdateInquiryOptionConfig_success() {
    }

    @Test
    public void testUpdateInquiryOptionConfig_notExists() {
    }

    @Test
    public void testDeleteInquiryOptionConfig_success() {
        // mock 数据
        InquiryOptionConfigDO dbInquiryOptionConfig = randomPojo(InquiryOptionConfigDO.class);
        dbInquiryOptionConfig.setExt(null);
        inquiryOptionConfigMapper.insert(dbInquiryOptionConfig);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbInquiryOptionConfig.getId();

        // 调用
        inquiryOptionConfigService.deleteInquiryOptionConfig(id);
        // 校验数据不存在了
        assertNull(inquiryOptionConfigMapper.selectById(id));
    }

    @Test
    public void testDeleteInquiryOptionConfig_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> inquiryOptionConfigService.deleteInquiryOptionConfig(id), INQUIRY_OPTION_CONFIG_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetInquiryOptionConfigPage() {
        // mock 数据
        InquiryOptionConfigDO dbInquiryOptionConfig = randomPojo(InquiryOptionConfigDO.class, o -> { // 等会查询到
            o.setTargetType(null);
            o.setOptionType(null);
            o.setTargetId(null);
            o.setTargetName(null);
            o.setProvince(null);
            o.setProvinceCode(null);
            o.setCity(null);
            o.setCityCode(null);
            o.setArea(null);
            o.setAreaCode(null);
            o.setExt(null);
            o.setCreateTime(null);
        });
        inquiryOptionConfigMapper.insert(dbInquiryOptionConfig);
        // 测试 targetType 不匹配
        inquiryOptionConfigMapper.insert(cloneIgnoreId(dbInquiryOptionConfig, o -> o.setTargetType(null)));
        // 测试 optionType 不匹配
        inquiryOptionConfigMapper.insert(cloneIgnoreId(dbInquiryOptionConfig, o -> o.setOptionType(null)));
        // 测试 targetId 不匹配
        inquiryOptionConfigMapper.insert(cloneIgnoreId(dbInquiryOptionConfig, o -> o.setTargetId(null)));
        // 测试 targetName 不匹配
        inquiryOptionConfigMapper.insert(cloneIgnoreId(dbInquiryOptionConfig, o -> o.setTargetName(null)));
        // 测试 province 不匹配
        inquiryOptionConfigMapper.insert(cloneIgnoreId(dbInquiryOptionConfig, o -> o.setProvince(null)));
        // 测试 provinceCode 不匹配
        inquiryOptionConfigMapper.insert(cloneIgnoreId(dbInquiryOptionConfig, o -> o.setProvinceCode(null)));
        // 测试 city 不匹配
        inquiryOptionConfigMapper.insert(cloneIgnoreId(dbInquiryOptionConfig, o -> o.setCity(null)));
        // 测试 cityCode 不匹配
        inquiryOptionConfigMapper.insert(cloneIgnoreId(dbInquiryOptionConfig, o -> o.setCityCode(null)));
        // 测试 area 不匹配
        inquiryOptionConfigMapper.insert(cloneIgnoreId(dbInquiryOptionConfig, o -> o.setArea(null)));
        // 测试 areaCode 不匹配
        inquiryOptionConfigMapper.insert(cloneIgnoreId(dbInquiryOptionConfig, o -> o.setAreaCode(null)));
        // 测试 ext 不匹配
        inquiryOptionConfigMapper.insert(cloneIgnoreId(dbInquiryOptionConfig, o -> o.setExt(null)));
        // 测试 createTime 不匹配
        inquiryOptionConfigMapper.insert(cloneIgnoreId(dbInquiryOptionConfig, o -> o.setCreateTime(null)));
        // 准备参数
        InquiryOptionConfigPageReqVO reqVO = new InquiryOptionConfigPageReqVO();
        reqVO.setTargetType(null);
        reqVO.setOptionType(null);
        reqVO.setTargetId(null);
        reqVO.setTargetName(null);
        reqVO.setProvince(null);
        reqVO.setProvinceCode(null);
        reqVO.setCity(null);
        reqVO.setCityCode(null);
        reqVO.setArea(null);
        reqVO.setAreaCode(null);
        reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));

        // 调用
        PageResult<InquiryOptionAreaConfigRespDto> pageResult = inquiryOptionConfigService.getInquiryOptionAreaConfigPage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbInquiryOptionConfig, pageResult.getList().get(0));
    }

    @Test
    public void testSaveInquiryOptionAreaConfig_success() {
        // 问诊单配置-超适应症审查-区域-保存 （武汉市）
        InquiryOptionAreaConfigReqVO reqVO = new InquiryOptionAreaConfigReqVO();
        reqVO.setOptionType(InquiryOptionTypeEnum.FORM_OFF_LABEL_REVIEW.getType());
        reqVO.setArea(420100);
        int i = inquiryOptionConfigService.saveInquiryOptionAreaConfig(reqVO);
        assertTrue(i > 0);

        // 问诊单配置-超适应症审查-区域-分页查询

        InquiryOptionConfigPageReqVO pageReqVO = new InquiryOptionConfigPageReqVO();
        pageReqVO.setOptionType(reqVO.getOptionType());

        PageResult<InquiryOptionAreaConfigRespDto> page = inquiryOptionConfigService.getInquiryOptionAreaConfigPage(pageReqVO);
        assertNotNull(page);
        assertTrue(page.getTotal() > 0);
    }

    @Test
    public void testSaveInquiryOptionAreaConfig2_success() {
        // 问诊流程配置-问诊合规配置-区域-保存 （武汉市东西湖区）
        InquiryOptionAreaConfigReqVO reqVO = new InquiryOptionAreaConfigReqVO();
        reqVO.setOptionType(InquiryOptionTypeEnum.PROC_INQUIRY_COMPLIANCE.getType());
        reqVO.setArea(420112);
        reqVO.setProcInquiryCompliance(true);
        reqVO.setProcInquiryComplianceForPatientAgeGe(15);
        reqVO.setProcInquiryComplianceForPatientAgeLt(60);
        reqVO.setProcInquiryComplianceAllowForPregnancyLactation(true);
        int i = inquiryOptionConfigService.saveInquiryOptionAreaConfig(reqVO);
        assertTrue(i > 0);

        // 问诊单配置-超适应症审查-区域-分页查询
        InquiryOptionConfigPageReqVO pageReqVO = new InquiryOptionConfigPageReqVO();
        pageReqVO.setOptionType(reqVO.getOptionType());
        pageReqVO.setTargetType(InquiryTargetTypeEnum.AREA.type);

        PageResult<InquiryOptionAreaConfigRespDto> page = inquiryOptionConfigService.getInquiryOptionAreaConfigPage(pageReqVO);
        assertNotNull(page);
        assertTrue(page.getTotal() > 0);
    }

    @Test
    public void testBatchDeleteInquiryOptionAreaConfig_success() {
        // 问诊单配置-超适应症审查-区域-保存 （武汉市东西湖区）
        InquiryOptionAreaConfigReqVO reqVO = new InquiryOptionAreaConfigReqVO();
        reqVO.setOptionType(InquiryOptionTypeEnum.PROC_INQUIRY_COMPLIANCE.getType());
        reqVO.setArea(420112);
        reqVO.setProcInquiryCompliance(true);
        reqVO.setProcInquiryComplianceForPatientAgeGe(15);
        reqVO.setProcInquiryComplianceForPatientAgeLt(60);
        reqVO.setProcInquiryComplianceAllowForPregnancyLactation(true);
        int i = inquiryOptionConfigService.saveInquiryOptionAreaConfig(reqVO);
        assertTrue(i > 0);

        // 问诊单配置-超适应症审查-区域-分页查询
        InquiryOptionConfigPageReqVO pageReqVO = new InquiryOptionConfigPageReqVO();
        pageReqVO.setOptionType(InquiryOptionTypeEnum.PROC_INQUIRY_COMPLIANCE.getType());
        pageReqVO.setTargetType(InquiryTargetTypeEnum.AREA.type);

        PageResult<InquiryOptionAreaConfigRespDto> page = inquiryOptionConfigService.getInquiryOptionAreaConfigPage(pageReqVO);
        assertNotNull(page);
        assertTrue(page.getTotal() > 0);

        // 问诊流程配置-问诊合规配置-区域-批量删除
        Long id = page.getList().getFirst().getId();
        assertNotNull(id);
        i = inquiryOptionConfigService.batchDeleteInquiryOptionConfig(List.of(id));
        assertTrue(i > 0);
        assertServiceException(() -> inquiryOptionConfigService.getInquiryOptionAreaConfig(id), INQUIRY_OPTION_CONFIG_NOT_EXISTS);
    }

    @Test
    public void testSaveInquiryOptionStoreConfig_success() {
        TenantDto tenant = getTenantDto();
        // 问诊单配置-超适应症审查-门店-保存
        InquiryOptionStoreConfigReqVO reqVO = new InquiryOptionStoreConfigReqVO();
        reqVO.setOptionType(InquiryOptionTypeEnum.FORM_OFF_LABEL_REVIEW.getType());
        reqVO.setTenantIdList(List.of(tenant.getId()));
        // mock 租户不存在
        when(tenantApi.getTenant()).thenReturn(null);
        assertServiceException(() -> inquiryOptionConfigService.saveInquiryOptionStoreConfig(reqVO), TENANT_NOT_EXISTS);

        // mock 租户存在
        when(tenantApi.getTenant(eq(reqVO.getTenantIdList().getFirst()))).thenReturn(tenant);
        int i = inquiryOptionConfigService.saveInquiryOptionStoreConfig(reqVO);
        assertTrue(i > 0);

        // 问诊单配置-超适应症审查-门店-分页查询
        InquiryOptionConfigPageReqVO pageReqVO = new InquiryOptionConfigPageReqVO();
        pageReqVO.setOptionType(reqVO.getOptionType());
        pageReqVO.setTargetType(InquiryTargetTypeEnum.STORE.type);
        PageResult<InquiryOptionStoreConfigRespDto> page = inquiryOptionConfigService.getInquiryOptionStoreConfigPage(pageReqVO);
        assertNotNull(page);
        assertTrue(page.getTotal() > 0);
    }


    // ### 问诊流程配置-医生接诊默认页面-门店-保存
//     POST {{baseUrl}}/kernel/drugstore/inquiry-option-config/store/save
//     Content-Type: application/json
//     Authorization: Bearer {{token}}
//     tenant-id: {{adminTenentId}}
//     X-Developer: {{developer}}
//
//     {
//         "optionType": 20009,
//         "tenantId": {{tenantId}},
//         "procDoctorAdmissionDefaultPage": "/doctor/check/check-result"
//     }
//
//
// ### 问诊流程配置-医生接诊默认页面-门店-分页查询
//     GET {{baseUrl}}/kernel/drugstore/inquiry-option-config/store/page?pageNo=1&pageSize=10&optionType=20009
//     Content-Type: application/json
//     Authorization: Bearer {{token}}
//     tenant-id: {{adminTenentId}}
//     X-Developer: {{developer}}
//
// > {%
//         if (response.body.data.list.length > 0) {
//             client.global.set("id", response.body.data.list[0].id);
//         }
// %}
//
//
// ### 问诊流程配置-医生接诊默认页面-门店-查询
//     GET {{baseUrl}}/kernel/drugstore/inquiry-option-config/store/get?id={{id}}
//     Content-Type: application/json
//     Authorization: Bearer {{token}}
//     tenant-id: {{adminTenentId}}
//     X-Developer: {{developer}}
//
//
// ### 问诊流程配置-医生接诊默认页面-门店-单个删除
//     DELETE {{baseUrl}}/kernel/drugstore/inquiry-option-config/store/delete?id={{id}}
//     Content-Type: application/json
//     Authorization: Bearer {{token}}
//     tenant-id: {{adminTenentId}}
//     X-Developer: {{developer}}
//
//
//
// ### 全局配置查询
//     GET {{baseUrl}}/kernel/drugstore/inquiry-option-config/global/query
//     Content-Type: application/json
//     Authorization: Bearer {{token}}
//     tenant-id: {{adminTenentId}}
//     X-Developer: {{developer}}
//
//
//
// ### 问诊流程配置-问诊合规配置-保存
//     POST {{baseUrl}}/kernel/drugstore/inquiry-option-config/global/save
//     Content-Type: application/json
//     Authorization: Bearer {{token}}
//     tenant-id: {{adminTenentId}}
//     X-Developer: {{developer}}
//
//     {
//         "procInquiryCompliance": true,
//         "procInquiryComplianceForPatientAgeGe": 15,
//         "procInquiryComplianceForPatientAgeLt": 60,
//         "procInquiryComplianceAllowForPregnancyLactation": false
//     }
//
//
//
// ### 问诊流程配置-图文交互对话弹出速度-保存
//     POST {{baseUrl}}/kernel/drugstore/inquiry-option-config/global/save
//     Content-Type: application/json
//     Authorization: Bearer {{token}}
//     tenant-id: {{adminTenentId}}
//     X-Developer: {{developer}}
//
//     {
//         "procTeletextInteractionDialogPopupSpeed": 500
//     }
//
//
//
// ### 问诊流程配置-问诊服务开关-保存
//     POST {{baseUrl}}/kernel/drugstore/inquiry-option-config/global/save
//     Content-Type: application/json
//     Authorization: Bearer {{token}}
//     tenant-id: {{adminTenentId}}
//     X-Developer: {{developer}}
//
//     {
//         "procInquiryServiceSwitch": true
//     }
//
//
// ### 问诊流程配置-医生接诊默认页面-保存
//     POST {{baseUrl}}/kernel/drugstore/inquiry-option-config/global/save
//     Content-Type: application/json
//     Authorization: Bearer {{token}}
//     tenant-id: {{adminTenentId}}
//     X-Developer: {{developer}}
//
//     {
//         "procDoctorAdmissionDefaultPage": "/act/check/check-result"
//     }
//
//
//
// ### 医生开方配置-开方基础属性配置-保存
//     POST {{baseUrl}}/kernel/drugstore/inquiry-option-config/global/save
//     Content-Type: application/json
//     Authorization: Bearer {{token}}
//     tenant-id: {{adminTenentId}}
//     X-Developer: {{developer}}
//
//     {
//         "presGlobalDateFormat": "yyyyMMdd HH:mm:ss",
//         "presTeletextInquiryDoctorOrderMax": 5,
//         "presDoctor2ndConfirmDialog": true,
//         "presSignatureUseInterval": 30,
//         "presDoctorCannotInquiryArea": "420100",
//         "presAutoInquiryToRealForAllergic": true,
//         "presAutoInquiryToRealForLiverRenalDysfunction": true,
//         "presAutoInquiryToRealForPregnancyLactation": true,
//         "presAutoInquiryToRealForMultiDiagnose": true,
//         "presAutoInquiryToRealForMultiDiagnoseRatio": 50,
//         "presAutoInquiryToRealForMultiDiagnoseConditions": [
//         {
//             "and": true,
//             "conditions": [
//             {
//                 "and": true,
//                 "rules": [
//                 {
//                     "type": "age",
//                     "op": "ge",
//                     "value": "18"
//                 },
//                 {
//                     "type": "age",
//                     "op": "le",
//                     "value": "65"
//                 },
//                 {
//                     "type": "area",
//                     "op": "eq",
//                     "value": "420100"
//                 }
//           ]
//             }
//       ]
//         }
//   ],
//         "presAutoInquiryToRealForSpecialAgeRange": true,
//         "presAutoInquiryToRealForSpecialAgeRangeRatio": 50,
//         "presAutoInquiryToRealForSpecialAgeRangeConditions": [
//         {
//             "and": true,
//             "conditions": [
//             {
//                 "and": true,
//                 "rules": [
//                 {
//                     "type": "age",
//                     "op": "ge",
//                     "value": "18"
//                 },
//                 {
//                     "type": "age",
//                     "op": "le",
//                     "value": "65"
//                 }
//           ]
//             }
//       ]
//         }
//   ],
//         "presAutoInquiryReturnToReal": true,
//         "presAutoInquiryReturnToRealBacklogNum": 700,
//         "presAutoInquiryReturnToRealRatio": 50
//     }
//
// ### 医生开方配置-开方基础属性配置-保存（单个属性）
//     POST {{baseUrl}}/kernel/drugstore/inquiry-option-config/global/save
//     Content-Type: application/json
//     Authorization: Bearer {{token}}
//     tenant-id: {{adminTenentId}}
//     X-Developer: {{developer}}
//
//     {
//         "presAutoInquiryToRealForMultiDiagnose": true
//     }
    @Test
    public void testGetInquiryOptionConfig() {
        TenantDto tenant = getTenantDto();
        // 获取配置
        InquiryOptionConfigRespDto dto = inquiryOptionConfigService.getInquiryOptionConfig(tenant);
        assertNotNull(dto);

    }

    private @Nonnull TenantDto getTenantDto() {
        // 荷叶测试一店
        return TenantDto.builder()
            .id(1861607133133283330L)
            .pref("MD100006")
            .name("荷叶测试一店")
            .contactUserId(1861607133531742209L)
            .contactName("墨玉")
            .contactMobile("***********")
            .businessLicenseName("荷叶测试一店")
            .businessLicenseNumber("ak1947")
            .status(0)
            .wzBizTypeStatus(0)
            .wzAccountCount(10)
            .zhlBizTypeStatus(1)
            .zhlAccountCount(0)
            .province("湖北省")
            .provinceCode("420000")
            .city("武汉市")
            .cityCode("420100")
            .area("江夏区")
            .areaCode("420115")
            .address("金融港A2-9楼")
            .envTag("prod")
            .build();
    }
}