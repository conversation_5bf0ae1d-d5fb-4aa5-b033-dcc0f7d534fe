package com.xyy.saas.inquiry.drugstore.server.tenant;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.drugstore.server.BaseIntegrationTest;
import com.xyy.saas.inquiry.drugstore.server.api.tenant.TenantPackageCostApiImpl;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo.TenantPackageCostReqVO;
import com.xyy.saas.inquiry.drugstore.server.controller.admin.cost.vo.TenantPackageCostSaveReqVO;
import com.xyy.saas.inquiry.drugstore.server.dal.dataobject.tenant.TenantPackageCostDO;
import com.xyy.saas.inquiry.drugstore.server.dal.mysql.tenant.TenantPackageCostMapper;
import com.xyy.saas.inquiry.drugstore.server.service.tenant.TenantPackageCostServiceImpl;
import jakarta.annotation.Resource;
import org.junit.jupiter.api.Disabled;
import org.junit.jupiter.api.Test;
import org.springframework.context.annotation.Import;

import static cn.iocoder.yudao.framework.common.util.date.LocalDateTimeUtils.buildBetweenTime;
import static cn.iocoder.yudao.framework.common.util.object.ObjectUtils.cloneIgnoreId;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertPojoEquals;
import static cn.iocoder.yudao.framework.test.core.util.AssertUtils.assertServiceException;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomLongId;
import static cn.iocoder.yudao.framework.test.core.util.RandomUtils.randomPojo;
import static com.xyy.saas.inquiry.drugstore.enums.ErrorCodeConstants.TENANT_PACKAGE_COST_RELATION_NOT_EXISTS;
import static org.junit.jupiter.api.Assertions.*;

/**
 * {@link TenantPackageCostServiceImpl} 的单元测试类
 *
 * <AUTHOR>
 */
@Import(value = {TenantPackageCostServiceImpl.class, TenantPackageCostApiImpl.class})
public class TenantPackageCostServiceImplTest extends BaseIntegrationTest {

    @Resource
    private TenantPackageCostServiceImpl tenantPackageCostRelationService;

    @Resource
    private TenantPackageCostMapper tenantPackageCostMapper;


    @Test
    public void testCreateTenantPackageCostRelation_success() {
        // 准备参数
        TenantPackageCostSaveReqVO createReqVO = randomPojo(TenantPackageCostSaveReqVO.class).setId(null);

        // 调用
        Long tenantPackageCostRelationId = tenantPackageCostRelationService.createTenantPackageCostRelation(createReqVO);
        // 断言
        assertNotNull(tenantPackageCostRelationId);
        // 校验记录的属性是否正确
        TenantPackageCostDO tenantPackageCostRelation = tenantPackageCostMapper.selectById(tenantPackageCostRelationId);
        assertPojoEquals(createReqVO, tenantPackageCostRelation, "id");
    }

    @Test
    public void testUpdateTenantPackageCostRelation_success() {
        // mock 数据
        TenantPackageCostDO dbTenantPackageCostRelation = randomPojo(TenantPackageCostDO.class);
        tenantPackageCostMapper.insert(dbTenantPackageCostRelation);// @Sql: 先插入出一条存在的数据
        // 准备参数
        TenantPackageCostSaveReqVO updateReqVO = randomPojo(TenantPackageCostSaveReqVO.class, o -> {
            o.setId(dbTenantPackageCostRelation.getId()); // 设置更新的 ID
        });

        // 调用
        tenantPackageCostRelationService.updateTenantPackageCostRelation(updateReqVO);
        // 校验是否更新正确
        TenantPackageCostDO tenantPackageCostRelation = tenantPackageCostMapper.selectById(updateReqVO.getId()); // 获取最新的
        assertPojoEquals(updateReqVO, tenantPackageCostRelation);
    }

    @Test
    public void testUpdateTenantPackageCostRelation_notExists() {
        // 准备参数
        TenantPackageCostSaveReqVO updateReqVO = randomPojo(TenantPackageCostSaveReqVO.class);

        // 调用, 并断言异常
        assertServiceException(() -> tenantPackageCostRelationService.updateTenantPackageCostRelation(updateReqVO), TENANT_PACKAGE_COST_RELATION_NOT_EXISTS);
    }

    @Test
    public void testDeleteTenantPackageCostRelation_success() {
        // mock 数据
        TenantPackageCostDO dbTenantPackageCostRelation = randomPojo(TenantPackageCostDO.class);
        tenantPackageCostMapper.insert(dbTenantPackageCostRelation);// @Sql: 先插入出一条存在的数据
        // 准备参数
        Long id = dbTenantPackageCostRelation.getId();

        // 调用
        tenantPackageCostRelationService.deleteTenantPackageCostRelation(id);
        // 校验数据不存在了
        assertNull(tenantPackageCostMapper.selectById(id));
    }

    @Test
    public void testDeleteTenantPackageCostRelation_notExists() {
        // 准备参数
        Long id = randomLongId();

        // 调用, 并断言异常
        assertServiceException(() -> tenantPackageCostRelationService.deleteTenantPackageCostRelation(id), TENANT_PACKAGE_COST_RELATION_NOT_EXISTS);
    }

    @Test
    @Disabled  // TODO 请修改 null 为需要的值，然后删除 @Disabled 注解
    public void testGetTenantPackageCostRelationPage() {
        // mock 数据
        TenantPackageCostDO dbTenantPackageCostRelation = randomPojo(TenantPackageCostDO.class, o -> { // 等会查询到
            o.setTenantPackageId(null);
            o.setCost(null);
            o.setSurplusCost(null);
            o.setStartTime(null);
            o.setEndTime(null);
            o.setCreateTime(null);
            o.setInquiryWayType(null);
            o.setStatus(null);
        });
        tenantPackageCostMapper.insert(dbTenantPackageCostRelation);
        // 测试 packageOrderId 不匹配
        tenantPackageCostMapper.insert(cloneIgnoreId(dbTenantPackageCostRelation, o -> o.setTenantPackageId(null)));
        // 测试 cost 不匹配
        tenantPackageCostMapper.insert(cloneIgnoreId(dbTenantPackageCostRelation, o -> o.setCost(null)));
        // 测试 surplusCost 不匹配
        tenantPackageCostMapper.insert(cloneIgnoreId(dbTenantPackageCostRelation, o -> o.setSurplusCost(null)));
        // 测试 startTime 不匹配
        tenantPackageCostMapper.insert(cloneIgnoreId(dbTenantPackageCostRelation, o -> o.setStartTime(null)));
        // 测试 endTime 不匹配
        tenantPackageCostMapper.insert(cloneIgnoreId(dbTenantPackageCostRelation, o -> o.setEndTime(null)));
        // 测试 version 不匹配
        // 测试 createTime 不匹配
        tenantPackageCostMapper.insert(cloneIgnoreId(dbTenantPackageCostRelation, o -> o.setCreateTime(null)));
        // 测试 inquiryWayType 不匹配
        tenantPackageCostMapper.insert(cloneIgnoreId(dbTenantPackageCostRelation, o -> o.setInquiryWayType(null)));
        // 测试 status 不匹配
        tenantPackageCostMapper.insert(cloneIgnoreId(dbTenantPackageCostRelation, o -> o.setStatus(null)));
        // 准备参数
        TenantPackageCostReqVO reqVO = new TenantPackageCostReqVO();
        reqVO.setTenantPackageId(null);
        reqVO.setCost(null);
        reqVO.setSurplusCost(null);
        reqVO.setStartTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
        reqVO.setEndTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
        reqVO.setCreateTime(buildBetweenTime(2023, 2, 1, 2023, 2, 28));
        reqVO.setInquiryWayType(null);
        reqVO.setStatus(null);

        // 调用
        PageResult<TenantPackageCostDO> pageResult = tenantPackageCostRelationService.getTenantPackageCostRelationPage(reqVO);
        // 断言
        assertEquals(1, pageResult.getTotal());
        assertEquals(1, pageResult.getList().size());
        assertPojoEquals(dbTenantPackageCostRelation, pageResult.getList().get(0));
    }

}