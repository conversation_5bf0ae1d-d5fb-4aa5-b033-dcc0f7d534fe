<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.xyy.saas</groupId>
    <artifactId>saas-inquiry-kernel</artifactId>
    <version>${revision}</version>
  </parent>

  <artifactId>saas-inquiry-drugstore</artifactId>
  <version>${revision}</version>
  <name>${project.artifactId}</name>
  <packaging>pom</packaging>

  <modules>
    <module>saas-inquiry-drugstore-api</module>
    <module>saas-inquiry-drugstore-server</module>
  </modules>

  <properties>
    <java.version>21</java.version>
    <drugstore.revision>${revision}</drugstore.revision>
  </properties>
  <dependencies>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

</project>
