### 请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json
X-Developer: {{developer}}

{
  "username": "15926351001",
  "password": "Abc123456"
}

> {%
  client.global.set("token", response.body.data.accessToken);
%}

### CA get
GET {{baseAppKernelUrl}}/kernel/signature/inquiry-signature-ca-auth/get
Content-Type: application/json
Authorization: Bearer {{token}}

### CA ca-auth
POST {{baseAppKernelUrl}}/kernel/signature/inquiry-signature-ca-auth/ca-auth
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "redirectUrl": "http://www.baidu.com"
}

### CA agreement-sign-url
POST {{baseAppKernelUrl}}/kernel/signature/inquiry-signature-ca-auth/seal-free-sign-url
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "signaturePlatformConfigId": 1
}



### CA create-seal
POST {{baseAppKernelUrl}}/kernel/signature/inquiry-signature-ca-auth/create-seal
Content-Type: application/json
Authorization: Bearer {{token}}

{
}