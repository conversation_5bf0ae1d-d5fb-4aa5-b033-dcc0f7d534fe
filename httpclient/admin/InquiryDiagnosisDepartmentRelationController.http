### 分页查询（科室）
GET {{baseAdminKernelUrl}}/kernel/hospital/inquiry-hospital-department/page?pageNo=1&pageSize=10
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{}


> {%
  client.global.set("deptId", response.body.data.list[0].id);
  client.global.set("deptPref", response.body.data.list[0].pref);
  client.global.set("deptName", response.body.data.list[0].deptName);
%}

### 分页查询（诊断）
GET {{baseAdminKernelUrl}}/kernel/hospital/inquiry-diagnosis/page?pageNo=1&pageSize=10
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{}

> {%
  let i = $random.integer(0, response.body.data.list.length);
  console.log(i);
  client.global.set("diagnosisCode", response.body.data.list[i].diagnosisCode);
  client.global.set("diagnosisName", response.body.data.list[i].diagnosisName);
  client.global.set("showName", response.body.data.list[i].showName);
%}


### 分页查询
GET {{baseAdminKernelUrl}}/kernel/hospital/inquiry-diagnosis-department-relation/page?pageNo=1&pageSize=10
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{
  "deptId": {{deptId}}
}

> {%
  client.global.set("id", response.body.data.list[0].id);
%}

### 添加
POST {{baseAdminKernelUrl}}/kernel/hospital/inquiry-diagnosis-department-relation/create
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{
  "diagnosisCode": "{{diagnosisCode}}",
  "diagnosisName": "{{diagnosisName}}",
  "showName": "{{showName}}",
  "deptId": "{{deptId}}",
  "deptPref": "{{deptPref}}",
  "deptName": "{{deptName}}"
}


### 删除
DELETE {{baseAdminKernelUrl}}/kernel/hospital/inquiry-diagnosis-department-relation/delete?id={{id}}
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

