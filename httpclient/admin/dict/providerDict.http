### 请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{
  "username": "admin",
  "password": "admin123"
}

> {%
  client.global.set("token", response.body.data.accessToken);
%}


### 查机构
GET {{baseAdminSystemUrl}}/transmitter/transmission-provider-dict-match/list-dict-match-organ?dictType=drug_use_frequency
Content-Type: application/json
Authorization: Bearer {{token}}

### 查分页
GET {{baseAdminSystemUrl}}/transmitter/transmission-provider-dict-match/page?organId=1&dictType=drug_use_frequency&status=0&dictName=一次
Content-Type: application/json
Authorization: Bearer {{token}}

### 手动匹配
POST {{baseAdminSystemUrl}}/transmitter/transmission-provider-dict-match/operate-match
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "organId": 1,
  "dictType": "drug_use_frequency",
  "dictId": 1858445762816335940,
  "organDictId": null,
  "status": "2"
}


### 查单条
GET {{baseAdminSystemUrl}}/transmitter/transmission-provider-dict-match/get?organId=1&dictType=drug_use_frequency&dictId=1858445762816335940
Content-Type: application/json
Authorization: Bearer {{token}}