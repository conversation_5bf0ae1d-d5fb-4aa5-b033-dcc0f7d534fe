### 请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{
  "username": "admin",
  "password": "admin123"
}

> {%
  client.global.set("token", response.body.data.accessToken);
%}


### 初始化自动开方医生时间轮
POST {{baseAdminKernelUrl}}/kernel/hospital/inquiry-doctor/init-auto-inquiry-doctor-timer-wheel
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "doctorPrefs": []
}
