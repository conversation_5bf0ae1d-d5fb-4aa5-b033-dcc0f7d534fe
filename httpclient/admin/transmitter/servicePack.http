### 请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json
X-Developer: {{developer}}

{
  "username": "admin",
  "password": "admin123"
}

> {%
  client.global.set("token", response.body.data.accessToken);
%}


### get
GET {{baseAdminTranUrl}}/transmitter/transmission-servicePack/get?id=1
Content-Type: application/json
Authorization: Bearer {{token}}


### retry
POST {{baseAdminTranUrl}}/transmitter/transmission-task-record/retry
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "ids": [
    1957
  ]
}