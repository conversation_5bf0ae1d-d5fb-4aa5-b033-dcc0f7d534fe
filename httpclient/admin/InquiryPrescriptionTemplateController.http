### 分页查询
GET {{baseAdminKernelUrl}}/kernel/signature/prescription-template/page?pageNo=1&pageSize=10
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

> {%
  client.global.set("id", response.body.data.list[0].id);
%}


### 更新
POST {{baseAdminSystemUrl}}/system/tenant/import/batch-update-package-relation
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "fileUrl": "https://files.test.ybm100.com/INVT/Lzinq/20250429/d2b2846dc93d4fbcbdef4d3151d3a03f.xlsx"
}

### 批量续费套餐
POST {{baseAdminSystemUrl}}/system/tenant/import/batch-open-package-relation
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "fileUrl": "https://files.test.ybm100.com/INVT/Lzinq/20250429/d2b2846dc93d4fbcbdef4d3151d3a03f.xlsx"
}
