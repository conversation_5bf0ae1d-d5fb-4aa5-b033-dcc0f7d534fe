### 请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{
  "username": "develop",
  "password": "Abc123456"
}

> {%
  client.global.set("token", response.body.data.accessToken);
%}


### 2.云南监管上传处方
GET {{baseAdminKernelUrl}}/kernel/supervision/prescription/upload?pref=HYWZ104234
Content-Type: application/json
Authorization: Bearer {{token}}

