###  1.门店登录  请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json

{
  "username": "system",
  "password": "Abc123456"
}

> {%
  client.global.set("tenantId", response.body.data.tenantId == null ? response.body.data.tenantList[0].id : response.body.data.tenantId);
  client.global.set("token", response.body.data.accessToken);
  console.log(response.body.data.accessToken);
  client.global.set("loginUserId", response.body.data.userId);
  client.global.set("date", new Date());
%}

### 2.创建医院员工
POST {{baseAdminKernelUrl}}/kernel/hospital/employee/create
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "username": "15000033333",
  "password": "Abc123456",
  "nickname": "22222",
  "mobile": "",
  "email": "<EMAIL>",
  "sex": 1,
  "status": 0,
  "hospitalPref": "H100026"
}

> {%
  client.global.set("hospitalEmployeeId", response.body.data);
  console.log("创建的医院员工ID: " + response.body.data);
%}


### 3.查询医院员工信息
GET {{baseAdminKernelUrl}}/kernel/hospital/employee/page?hospitalPref=H100000&pageNo=1&pageSize=10
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}


> {%
  console.log("查询返回的结果: " + response.body.data);
%}

### 4.查询用户绑定的医院
GET {{baseAdminKernelUrl}}/kernel/hospital/employee/bind-hospitals?userId=1947903602532892674
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  console.log("查询返回的结果: " + response.body.data);
%}

### 5.添加绑定关系
POST {{baseAdminKernelUrl}}/kernel/hospital/employee/bind
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "userId": 1947903602532892674,
  "hospitalPref": "H100001"
}

> {%
  console.log("查询返回的结果: " + response.body.data);
%}

### 6.解除绑定关系
DELETE {{baseAdminKernelUrl}}/kernel/hospital/employee/unbind?bindId=1947903632505405442
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  console.log("查询返回的结果: " + response.body.data);
%}



### 4.修改医院员工状态（启用）
PUT {{baseAdminSystemUrl}}/hospital/employee/update-status?id={{hospitalEmployeeId}}&status=0
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

### 5.修改医院员工状态（禁用）
PUT {{baseAdminSystemUrl}}/hospital/employee/update-status?id={{hospitalEmployeeId}}&status=1
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

### 6.重置医院员工密码
PUT {{baseAdminSystemUrl}}/hospital/employee/reset-password?id={{hospitalEmployeeId}}&password=newpassword123
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

### 7.绑定员工到医院
POST {{baseAdminSystemUrl}}/hospital/employee/bind?userId={{hospitalEmployeeId}}&hospitalPref=H002&hospitalName=北京大学第一医院
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  client.global.set("relationId", response.body.data);
  console.log("绑定关系ID: " + response.body.data);
%}

### 8.根据医院编号获取员工列表
GET {{baseAdminSystemUrl}}/hospital/employee/list-by-hospital?hospitalPref=H001
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

### 9.根据用户ID获取医院员工信息
GET {{baseAdminSystemUrl}}/hospital/employee/get-by-user?userId={{hospitalEmployeeId}}
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

### 10.检查用户是否为指定医院的员工
GET {{baseAdminSystemUrl}}/hospital/employee/check-employee?userId={{hospitalEmployeeId}}&hospitalPref=H001
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

### 11.解绑员工与医院关系
DELETE {{baseAdminSystemUrl}}/hospital/employee/unbind?userId={{hospitalEmployeeId}}&hospitalPref=H002
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

### 12.删除医院员工
DELETE {{baseAdminSystemUrl}}/hospital/employee/delete?id={{hospitalEmployeeId}}
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

### 13.创建第二个医院员工（用于测试列表查询）
POST {{baseAdminSystemUrl}}/hospital/employee/create
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "username": "hospitaluser002",
  "password": "123456",
  "nickname": "医院员工李四",
  "mobile": "13800138003",
  "email": "<EMAIL>",
  "sex": 2,
  "status": 0,
  "hospitalPref": "H001",
  "hospitalName": "北京协和医院",
  "remark": "第二个测试医院员工"
}

> {%
  client.global.set("hospitalEmployeeId2", response.body.data);
  console.log("创建的第二个医院员工ID: " + response.body.data);
%}

### 14.再次查询医院员工列表（验证多个员工）
GET {{baseAdminSystemUrl}}/hospital/employee/list-by-hospital?hospitalPref=H001
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

### 15.测试错误场景 - 创建重复用户名的员工
POST {{baseAdminSystemUrl}}/hospital/employee/create
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "username": "hospitaluser002",
  "password": "123456",
  "nickname": "重复用户名测试",
  "mobile": "13800138004",
  "email": "<EMAIL>",
  "sex": 1,
  "status": 0,
  "hospitalPref": "H001",
  "hospitalName": "北京协和医院"
}

### 16.测试错误场景 - 查询不存在的用户
GET {{baseAdminSystemUrl}}/hospital/employee/get-by-user?userId=999999
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

### 17.测试错误场景 - 检查不存在用户是否为医院员工
GET {{baseAdminSystemUrl}}/hospital/employee/check-employee?userId=999999&hospitalPref=H001
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

### 18.清理测试数据 - 删除第二个员工
DELETE {{baseAdminSystemUrl}}/hospital/employee/delete?id={{hospitalEmployeeId2}}
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

