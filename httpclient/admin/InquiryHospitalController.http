### 分页查询
GET {{baseAdminKernelUrl}}/kernel/hospital/inquiry-hospital/page?pageNo=1&pageSize=10
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

> {%
  client.global.set("id", response.body.data.list[0].id);
%}

### 编辑-查询
GET {{baseAdminKernelUrl}}/kernel/hospital/inquiry-hospital/get?id={{id}}
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

> {%
  // 将响应数据转换为字符串, 变量不能存对象
  client.global.set("hospital", JSON.stringify(response.body.data));
%}

### 编辑
< {%
  // 传参尽量传字符串，直接传数字可能导致精度丢失
  let hospital = JSON.parse(client.global.get("hospital"))
  hospital.setting = {
    defaultInquiryWesternMedicineDept: [{"1853283658231545858"}],
    defaultInquiryChineseMedicineDept: [{"1853283711327240193"}],
    defaultWesternPrescriptionTemplate: "1851904732921278466",
    defaultChinesePrescriptionTemplate: "1856602772575805441",
    extend: {
      specificPrescriptionTemplates: [{
        prescriptionTemplateId: "1856602772575805441",
        and: true,
        conditions: [
          {
            and: true,
            rules: [
              {
                type: "age",
                op: "ge",
                value: "18"
              },
              {

                type: "age",
                op: "le",
                value: "65"
              },
              {
                type: "area",
                op: "eq",
                value: "420100"
              }
            ]
          }
        ]
      }]
    }
  }
  console.log(hospital)
  client.global.set("hospital", JSON.stringify(hospital));
%}
PUT {{baseAdminKernelUrl}}/kernel/hospital/inquiry-hospital/update
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{{hospital}}


### 新增
POST {{baseAdminKernelUrl}}/kernel/hospital/inquiry-hospital/create
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}

{
  "id": 0,
  "name": "测1123",
  "level": 1,
  "address": "123",
  "phone": "13177273825",
  "email": "<EMAIL>",
  "website": "www.baidu.com",
  "hasMedicare": 1,
  "setting": {
    "defaultInquiryWesternMedicineDept": [{"1853283658231545858"}],
    "defaultInquiryChineseMedicineDept": [{"1853283658231545858"}],
    "defaultWesternPrescriptionTemplate": "1864926509328166913",
    "defaultWesternPrescriptionTemplateName": "",
    "defaultChinesePrescriptionTemplate": "1864926509328166913",
    "defaultChinesePrescriptionTemplateName": "",
    "extend": {
      "specificPrescriptionTemplates": [
        {
          "prescriptionTemplateId": "1856605418204332034",
          "and": true,
          "conditions": [
            {
              "and": true,
              "rules": [
                {
                  "type": "area",
                  "op": "eq",
                  "value": "130304"
                },
                {
                  "type": "age",
                  "op": "ge",
                  "value": "44"
                }
              ]
            }
          ]
        },
        {
          "prescriptionTemplateId": "1856605418204332034",
          "and": true,
          "conditions": [
            {
              "and": true,
              "rules": [
                {
                  "type": "slowDisease",
                  "op": "eq",
                  "value": "1"
                },
                {
                  "type": "age",
                  "op": "ge",
                  "value": "22"
                }
              ]
            }
          ]
        }
      ]
    }
  },
  "disable": 0
}




### 删除
DELETE {{baseAdminKernelUrl}}/kernel/hospital/inquiry-hospital/delete?id={{id}}
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{adminTenentId}}
X-Developer: {{developer}}
