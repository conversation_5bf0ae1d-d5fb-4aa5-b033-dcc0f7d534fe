### GET request to example server
###
POST https://saasremote.api.test.ybm100.com/api/tx/roomCallBack
Content-Type: application/json

{
  "EventGroupId": 1,
  "EventType": 103,
  "CallbackTs": 1687770731932,
  "EventInfo": {
    "RoomId": 12345,
    "EventTs": 1687770731,
    "EventMsTs": 1687770731831,
    "UserId": "test",
    "Role": 21,
    "TerminalType": 2,
    "UserType": 3,
    "Reason": 1
  }
}

### 退出房间回调
POST {{baseAppKernelUrl}}/kernel/trtc/callback/message
Content-Type: application/json

{
  "EventGroupId":	1,
  "EventType":	104,
  "CallbackTs":	1748931848159,
  "EventInfo":	{
    "RoomId":	116015,
    "EventTs":	1748931872,
    "EventMsTs":	1748931848159,
    "UserId":	"1927550448204468225"
  }
}
> {%
  if (response.body.errorCode !== 0) {
  throw new Error(response.body.msg);
  }
%}