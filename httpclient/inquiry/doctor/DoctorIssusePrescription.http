###  1.医生登录  请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json

{
  "username": "15926351002",
  "password": "Abc123456"
}

> {%
  client.global.set("tenantId", response.body.data.tenantId == null ? response.body.data.tenantList[0].id : response.body.data.tenantId);
  client.global.set("token", response.body.data.accessToken);
  console.log(response.body.data.accessToken);
  client.global.set("loginUserId", response.body.data.userId);
%}


### 2.医生出诊
POST {{baseAppKernelUrl}}/kernel/hospital/doctor-receipt/start-receipt
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "userId": "{{loginUserId}}",
  "autoGrabStatus": "1",
  "inquiryWayTypeItems": [1,2]
}

> {%
  console.log(response.body.code)
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}

### 3.医生抢单 - 问诊单号取 门店去问诊返回得单号
PUT {{baseAppKernelUrl}}/kernel/hospital/prescription/grabbing-prescription
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "inquiryPref": "{{inquiryPref}}"
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }

%}

### 4.医生开方
POST {{baseAppKernelUrl}}/kernel/hospital/prescription/issues-prescription
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "inquiryPref": {{inquiryPref}},
  "inquiryProductDto": {{inquiryProductInfo}},
  "mainSuit": {{mainSuit}},
  "diagnosis": {{diagnosis}}
}



### 5.医生查看处方记录
GET {{baseAppKernelUrl}}/kernel/patient/inquiry-query/get-record?inquiryPref={{inquiryPref}}
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}


> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}


### 5.医生开门诊病例
POST {{baseAppKernelUrl}}/kernel/hospital/inquiry-clinical-case/save
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "id": "",
  "pref": "",
  "tenantId": "1902541900682842113",
  "tenantName": "陈笑一-测试格林门店",
  "inquiryPref": "112237",
  "hospitalPref": "H100008",
  "hospitalName": "陈笑一-测试格林医院",
  "doctorPref": "D100038",
  "doctorName": "肖治坤",
  "deptPref": "KS100011",
  "deptName": "全科",
  "patientPref": "HZ112147",
  "patientName": "陈中药",
  "patientIdCard": "******************",
  "patientAge": "28",
  "patientSex": 1,
  "mainSuit": [
    "发烧"
  ],
  "allergic": [],
  "patientHisDesc": "",
  "currentIllnessDesc": "发烧",
  "followUp": 1,
  "mainSymptoms": "发烧",
  "outpatientDiagnosisDesc": "",
  "measures": "",
  "observation": 0,
  "referral": 0,
  "diagnosis": [
    {
      "diagnosisCode": "BA8Z",
      "diagnosisName": "冠心病"
    }
  ],
  "tcmDiagnosis": [
    {
      "diagnosisCode": "BA8Z",
      "diagnosisName": "冠心病"
    }
  ],
  "tcmSyndromeCode": "B02.03.02.01",
  "tcmSyndromeName": "寒湿外侵证",
  "tcmTreatmentMethodCode": "C02.19.04.01",
  "tcmTreatmentMethodName": "分消走泄",
  "ext": {
    "liverKidneyValue": 0,
    "gestationLactationValue": 0,
    "outpatientDiagnosisDesc": "",
    "tcmFourDiagnosticDesc": "",
    "tcmUploadTongueImage": 0,
    "tcmDialecticalAnalysis": ""
  }
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}



### 5.医生开门诊病例
POST {{baseAdminSystemUrl}}/product/catalog/create
Content-Type: application/json
Authorization: Bearer {{token}}

{
  "id": null,
  "name": "互联网监管目录0321",
  "type": 3,
  "uploadUrl": "http://files.test.ybm100.com/INVT/Lzinq/20250321/54b2a970c88d9a8698c2acb165fe06ee4f271352a1e57afaeb3aed98a58a4c16.xlsx",
  "provinceCode": "123",
  "totalCount": 15495,
  "matchedCount": 14781,
  "unmatchedCount": 30548,
  "disable": true,
  "env": 0,
  "remark": "你猜"
}