### 1.门店登录 - 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json

{
  "username": "15926350002",
  "password": "Abc123456"
}

> {%
  client.global.set("tenantId", response.body.data.tenantId == null ? response.body.data.tenantList[0].id : response.body.data.tenantId);
  client.global.set("token", response.body.data.accessToken);
  console.log(response.body.data.accessToken);
  client.global.set("loginUserId", response.body.data.userId);
%}



### 2.查商品信息 - 中药
GET {{baseAppSystemUrl}}/product/search/products-by-name-spec?productName=人参&attributeSpecification=&medicineType=1&pageNo=1&pageSize=10
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}
X-Developer: {{developer}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data.list.length < 1) {
    throw new Error("无商品信息");
  }
  let inquiryProductInfos = [];

  for (let product of response.body.data.list) {
    inquiryProductInfos.push({
      "pref": product.pref,
      "commonName": product.commonName,
      "productName": product.productName || product.commonName,
      "attributeSpecification": product.attributeSpecification,
      "manufacturer": product.manufacturer,
      "unitName": product.unitName,
      "quantity": 1,
    })
  }

  let inquiryProductInfo = {
    "inquiryProductInfos": inquiryProductInfos,
    "tcmTotalDosage": "10",
    "tcmDailyDosage": "3",
    "tcmUsage": "2",
    "tcmDirections": "温服",
    "tcmProcessingMethod": "打粉冲服"
  }
  console.log(JSON.stringify(inquiryProductInfo))

  client.global.set("inquiryProductInfo", JSON.stringify(inquiryProductInfo));
%}



### 3.查系统诊断 - 模拟手选
GET {{baseAppKernelUrl}}/kernel/hospital/inquiry-diagnosis/pages
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data.list.length > 0) {
    let diagnosis = [{
      "diagnosisCode": response.body.data.list[0].diagnosisCode,
      "diagnosisName": response.body.data.list[0].diagnosisName
    }]
    client.global.set("diagnosis", JSON.stringify(diagnosis));
  }
%}


### 4.查主诉
GET {{baseAppKernelUrl}}/kernel/hospital/inquiry-main-suit/pages
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data.list.length > 0) {
    let mainSuit = [response.body.data.list[0].mainSuitName]
    client.global.set("mainSuit", JSON.stringify(mainSuit));
  }
%}


### 5.查过敏史 - 使用推荐过敏史
GET {{baseAppKernelUrl}}/kernel/hospital/rational/irritability/getRecommendAllergy
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data.length > 0) {
    let allergic = [response.body.data[0].value]
    client.global.set("allergic", JSON.stringify(allergic));
  }
%}

### 6.去问诊下单
POST  {{baseAppKernelUrl}}/kernel/patient/inquiry/drugstore-inquiry
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "clientChannelType": 0,
  "clientOsType": "android",
  "inquiryWayType": 1,
  "bizChannelType": 0,
  "baseInquiryReqVO": {
    "patient": {
      "patientName": "王中药",
      "patientMobile": "13435637494",
      "patientIdCard": "******************",
      "patientAge": "29",
      "patientSex": 1
    },
    "mainSuit": {{mainSuit}},
    "allergic": {{allergic}},
    "diagnosis": {{diagnosis}},
    "slowDisease": 0,
    "liverKidneyValue": 3,
    "gestationLactationValue": 0,
    "followUp": 0,
    "medicineType": 1,
    "offlinePrescriptions": [],
    "inquiryProductInfo": {{inquiryProductInfo}}
  }
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  client.global.set("inquiryPref", response.body.data);
%}



###  1.医生登录  请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json

{
  "username": "15926350017",
  "password": "Abc123456"
}

> {%
  client.global.set("tenantId", response.body.data.tenantId == null ? response.body.data.tenantList[0].id : response.body.data.tenantId);
  client.global.set("token", response.body.data.accessToken);
  console.log(response.body.data.accessToken);
  client.global.set("loginUserId", response.body.data.userId);
%}


### 2.医生出诊
POST {{baseAppKernelUrl}}/kernel/hospital/doctor-receipt/start-receipt
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "userId": "{{loginUserId}}",
  "autoGrabStatus": "1",
  "inquiryWayTypeItems": [1,2]
}

> {%
  console.log(response.body.code)
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}

### 3.医生抢单 - 问诊单号取 门店去问诊返回得单号
PUT {{baseAppKernelUrl}}/kernel/hospital/prescription/grabbing-prescription
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "inquiryPref": "{{inquiryPref}}"
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }

%}

### 4.医生开方
POST {{baseAppKernelUrl}}/kernel/hospital/prescription/issues-prescription
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "inquiryPref": {{inquiryPref}},
  "inquiryProductDto": {{inquiryProductInfo}},
  "mainSuit": {{mainSuit}},
  "diagnosis": {{diagnosis}}
}