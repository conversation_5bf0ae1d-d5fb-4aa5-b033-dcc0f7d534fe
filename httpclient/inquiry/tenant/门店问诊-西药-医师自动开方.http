###  1.门店登录  请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json

{
  "username": "15926350002",
  "password": "Abc123456"
}

> {%
  client.global.set("tenantId", response.body.data.tenantId == null ? response.body.data.tenantList[0].id : response.body.data.tenantId);
  client.global.set("token", response.body.data.accessToken);
  console.log(response.body.data.accessToken);
  client.global.set("loginUserId", response.body.data.userId);
%}

### 2.查商品信息 - 西药
GET {{baseAppSystemUrl}}/product/search/products-by-name-spec?productName=阿奇霉素胶囊&attributeSpecification=0.25G*10S*1板&medicineType=0&pageNo=1&pageSize=10
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data.list.length < 1) {
    throw new Error("无商品信息");
  }
  let product = response.body.data.list[0];

  client.global.set("pref", product.pref);
  client.global.set("productName", product.productName);

  let inquiryProductInfo = {
    "inquiryProductInfos": [{
      "pref": product.pref,
      "standardId": product.pref,
      "commonName": product.commonName,
      "productName": product.productName,
      "attributeSpecification": product.attributeSpecification,
      "manufacturer": product.manufacturer,
      "unitName": product.unitName,
      "quantity": 1,
      "directions": "口服",
      "singleDose": "10",
      "singleUnit": "袋",
      "useFrequency": "1次/6小时",
      "useFrequencyValue": "ONETIMESSIXHOURS",
    }],
    "tcmTotalDosage": "10",
    "tcmDailyDosage": "3",
    "tcmUsage": "2",
    "tcmDirections": "温服",
    "tcmProcessingMethod": "打粉冲服"
  }
  client.global.set("inquiryProductInfo", JSON.stringify(inquiryProductInfo));
%}


### 3.根据药品查询关联诊断
POST  {{baseAppSystemUrl}}/product/search/product-diagnostics
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "pageNo": 1,
  "pageSize": 10,
  "productSearchList": [
    {
      "pref": "{{pref}}",
      "medicineType": 0,
      "productName": "{{productName}}"
    }
  ]
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data.length > 0) {
    let diagnosis = [{
      "diagnosisCode": response.body.data[0].diagnosisCode,
      "diagnosisName": response.body.data[0].diagnosisName
    }]
    client.global.set("diagnosis", JSON.stringify(diagnosis));
  }
%}



### 3.查系统诊断 - 模拟手选
GET {{baseAppKernelUrl}}/kernel/hospital/inquiry-diagnosis/pages
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data.list.length > 0 && !client.global.get("diagnosis")) {
    let diagnosis = [{
      "diagnosisCode": response.body.data.list[0].diagnosisCode,
      "diagnosisName": response.body.data.list[0].diagnosisName
    }]
    client.global.set("diagnosis", JSON.stringify(diagnosis));
  }
%}


### 4.查主诉
GET {{baseAppKernelUrl}}/kernel/hospital/inquiry-main-suit/pages
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data.list.length > 0) {
    let mainSuit = [response.body.data.list[0].mainSuitName]
    client.global.set("mainSuit", JSON.stringify(mainSuit));
  }
%}


### 5.查过敏史 - 使用推荐过敏史
GET {{baseAppKernelUrl}}/kernel/hospital/rational/irritability/getRecommendAllergy
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data.length > 0) {
    let allergic = [response.body.data[0].value]
    client.global.set("allergic", JSON.stringify(allergic));
  }
%}

### 6.去问诊下单
POST  {{baseAppKernelUrl}}/kernel/patient/inquiry/drugstore-inquiry
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "clientChannelType": 0,
  "clientOsType": "android",
  "inquiryWayType": 1,
  "bizChannelType": 0,
  "baseInquiryReqVO": {
    "patient": {
      "patientName": "李明",
      "patientMobile": "13435637493",
      "patientIdCard": "******************",
      "patientAge": "29",
      "patientSex": 1
    },
    "mainSuit": {{mainSuit}},
    "allergic": {{allergic}},
    "diagnosis": {{diagnosis}},
    "slowDisease": 0,
    "liverKidneyValue": 3,
    "gestationLactationValue": 0,
    "followUp": 0,
    "medicineType": 0,
    "offlinePrescriptions": [],
    "inquiryProductInfo": {{inquiryProductInfo}}
  }
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  client.global.set("inquiryPref", response.body.data);
%}

### 7.查询接诊队列
GET {{baseAppKernelUrl}}/kernel/patient/inquiry/inquiry-queueing?inquiryPref=110087
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}