### 请求 /login 接口 => 成功
POST {{baseAppSystemUrl}}/system/auth/weixin-mini-app-login
Content-Type: application/json
tag: Yunai.local

{"phoneCode":"1ccf50585a3fefaa6e31abd279b46decc8829c27a3631d1621a162d177c038a3","loginCode":"0a1TxZll2f2HAf46Hpml2ObQmc2TxZlL","state": "0","tenantId": 1902541900682842113}

> {%
  client.global.set("tenantId", response.body.data.tenantId == null ? response.body.data.tenantList[0].id : response.body.data.tenantId);
  client.global.set("token", response.body.data.accessToken);
  console.log(response.body.data.accessToken);
  client.global.set("loginUserId", response.body.data.userId);
%}

### 2.获取权限
GET {{baseAppKernelUrl}}/kernel/drugstore/base-info/inquiry-permission
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.errmsg);
  }
%}


### 3.查询患者
GET {{baseAppKernelUrl}}/kernel/patient/patient-info/page?pageNo=1&pageSize=10&name=张&queryScene=3
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}



> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.errmsg);
  }
%}




### 2.查商品信息 - 西药
GET {{baseAppSystemUrl}}/product/search/products-by-name-spec?productName=阿莫西林&medicineType=0&pageNo=1&pageSize=100
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data.list.length < 1) {
    throw new Error("无商品信息");
  }
  let product = response.body.data.list[0];

  client.global.set("pref", product.pref);
  client.global.set("productName", product.productName);

  let inquiryProductInfo = {
    "inquiryProductInfos": [{
      "pref": product.pref,
      "standardId": product.pref,
      "commonName": product.commonName,
      "productName": product.productName,
      "attributeSpecification": product.attributeSpecification,
      "manufacturer": product.manufacturer,
      "unitName": product.unitName,
      "quantity": 1,
      "directions": "口服",
      "singleDose": "10",
      "singleUnit": "袋",
      "useFrequency": "1次/6小时",
      "useFrequencyValue": "ONETIMESSIXHOURS",
    }],
    "tcmTotalDosage": "10",
    "tcmDailyDosage": "3",
    "tcmUsage": "2",
    "tcmDirections": "温服",
    "tcmProcessingMethod": "打粉冲服"
  }
  client.global.set("inquiryProductInfo", JSON.stringify(inquiryProductInfo));
%}


### 3.根据药品查询关联诊断
POST  {{baseAppSystemUrl}}/product/search/product-diagnostics
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "pageNo": 1,
  "pageSize": 10,
  "productSearchList": [
    {
      "pref": "{{pref}}",
      "medicineType": 0,
      "productName": "{{productName}}"
    }
  ]
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data.length > 0) {
    let diagnosis = [{
      "diagnosisCode": response.body.data[0].diagnosisCode,
      "diagnosisName": response.body.data[0].diagnosisName
    }]
    client.global.set("diagnosis", JSON.stringify(diagnosis));
  }
%}



### 3.查系统诊断 - 模拟手选
GET {{baseAppKernelUrl}}/kernel/hospital/inquiry-diagnosis/pages?diagnosisType=1
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data.list.length > 0 && !client.global.get("diagnosis")) {
    let diagnosis = [{
      "diagnosisCode": response.body.data.list[0].diagnosisCode,
      "diagnosisName": response.body.data.list[0].diagnosisName
    }]
    client.global.set("diagnosis", JSON.stringify(diagnosis));
  }
%}


### 4.查主诉
GET {{baseAppKernelUrl}}/kernel/hospital/inquiry-main-suit/pages
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data.list.length > 0) {
    let mainSuit = [response.body.data.list[0].mainSuitName]
    client.global.set("mainSuit", JSON.stringify(mainSuit));
  }
%}


### 5.查过敏史 - 使用推荐过敏史
GET {{baseAppKernelUrl}}/kernel/hospital/rational/irritability/getRecommendAllergy
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data.length > 0) {
    let allergic = [response.body.data[0].value]
    client.global.set("allergic", JSON.stringify(allergic));
  }
%}


### 6.0.去问诊下单 前置校验
POST  {{baseAppKernelUrl}}/kernel/patient/inquiry/drugstore-inquiry-pre-check
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "clientChannelType": 2,
  "clientOsType": "android",
  "inquiryWayType": 1,
  "bizChannelType": 0,
  "baseInquiryReqVO": {
    "patient": {
      "patientName": "游客4",
      "patientMobile": "13524178547",
      "patientIdCard": "******************",
      "patientAge": "19",
      "patientSex": 1
    },
    "mainSuit": {{mainSuit}},
    "allergic": {{allergic}},
    "diagnosis": {{diagnosis}},
    "slowDisease": 0,
    "liverKidneyValue": 3,
    "gestationLactationValue": 0,
    "followUp": 0,
    "medicineType": 0,
    "offlinePrescriptions": [],
    "inquiryProductInfo": {{inquiryProductInfo}}
  }
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  client.global.set("inquiryPref", response.body.data.inquiryPref);
%}



### 6.去问诊下单
POST  {{baseAppKernelUrl}}/kernel/patient/inquiry/drugstore-inquiry
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "clientChannelType": 2,
  "clientOsType": "android",
  "inquiryWayType": 1,
  "bizChannelType": 0,
  "baseInquiryReqVO": {
    "patient": {
      "patientName": "游客40",
      "patientMobile": "13924167540",
      "patientIdCard": "******************",
      "patientAge": "29",
      "patientSex": 1
    },
    "mainSuit": {{mainSuit}},
    "allergic": {{allergic}},
    "diagnosis": {{diagnosis}},
    "slowDisease": 0,
    "liverKidneyValue": 3,
    "gestationLactationValue": 0,
    "followUp": 0,
    "medicineType": 0,
    "offlinePrescriptions": [],
    "inquiryProductInfo": {{inquiryProductInfo}}
  }
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  client.global.set("inquiryPref", response.body.data.inquiryPref);
%}


### 7.查询预订单
GET {{baseAppKernelUrl}}/kernel/patient/third-party/inquiry/get-pre-inquiry-page-list
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "transmissionOrganId": 4,
  "pageNo":1,
  "pageSize":10,
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  const prefList = response.body.data.list.map(item => item.pref);
  client.global.set("preInquiryPref", response.body.data.list[0].pref);
  client.global.set("prefList", JSON.stringify(prefList));
%}




### 8.预问诊审核
PUT {{baseAppKernelUrl}}/kernel/patient/third-party/inquiry/pre-inquiry-audit
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "pref": "YWZ100052",
  "auditStatus": 1
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}


###  1.医生登录  请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json

{
  "username": "15926351002",
  "password": "Abc123456"
}

> {%
  client.global.set("tenantId", response.body.data.tenantId == null ? response.body.data.tenantList[0].id : response.body.data.tenantId);
  client.global.set("token", response.body.data.accessToken);
  console.log(response.body.data.accessToken);
  client.global.set("loginUserId", response.body.data.userId);
%}


### 2.医生出诊
POST {{baseAppKernelUrl}}/kernel/hospital/doctor-receipt/start-receipt
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "userId": "{{loginUserId}}",
  "autoGrabStatus": "0",
  "inquiryWayTypeItems": [1,2]
}

> {%
  console.log(response.body.code)
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
%}

### 3.医生抢单 - 问诊单号取 门店去问诊返回得单号
PUT {{baseAppKernelUrl}}/kernel/hospital/prescription/grabbing-prescription
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "inquiryPref": {{inquiryPref}}
}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }

%}

### 4.医生开具门诊病例
POST {{baseAppKernelUrl}}/kernel/hospital/inquiry-clinical-case/save
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "inquiryPref": 112139,
  "inquiryProductDto": {{inquiryProductInfo}},
  "mainSuit": {{mainSuit}},
  "diagnosis": {{diagnosis}},
  "allergic": {{allergic}},
  "ext": {
    "outpatientDiagnosisDesc": "门诊诊断说明"
  },
  "measures": "处理措施",
  "observation": "0",
  "referral": "0",
  "tcmSyndromeCode": "B02.03.02.01",
  "tcmSyndromeName": "寒湿外侵证",
  "tcmTreatmentMethodCode": "C03.02.10",
  "tcmTreatmentMethodName": "点穴疗法"
}



### 4.医生开方
POST {{baseAppKernelUrl}}/kernel/hospital/prescription/issues-prescription
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

{
  "inquiryPref": 112430,
  "medicineType": 0,
  "inquiryProductDto": {{inquiryProductInfo}},
  "mainSuit": [
    "111"
  ],
  "diagnosis": [
    {
      "diagnosisCode": "1",
      "xx": "2"
    }
  ],
  "clientChannelType": 0
}


### 5.医生停诊
PUT {{baseAppKernelUrl}}/kernel/hospital/doctor-receipt/stop-receipt?userId={{loginUserId}}
Content-Type: application/json
Authorization: Bearer {{token}}
tenant-id: {{tenantId}}

