###  1.门店登录  请求 /login 接口 => 成功（无验证码)
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123"
}

> {%
  client.global.set("tenantId", response.body.data.tenantId == null ? response.body.data.tenantList[0].id : response.body.data.tenantId);
  client.global.set("token", response.body.data.accessToken);
  console.log(response.body.data.accessToken);
  client.global.set("loginUserId", response.body.data.userId);
%}


### 2.查商品信息 - 西药
GET {{baseAppSystemUrl}}/product/search/products-by-name-spec?productName=阿莫西林胶囊&attributeSpecification=&medicineType=0&pageNo=1&pageSize=100
Content-Type: application/json
Authorization: Bearer {{token}}

> {%
  if (response.body.code !== 0) {
    throw new Error(response.body.msg);
  }
  if (response.body.data.list.length < 1) {
    throw new Error("无商品信息");
  }
  let product = response.body.data.list[0];

  client.global.set("pref", product.pref);
  client.global.set("productName", product.productName);

  let inquiryProductInfo = {
    "inquiryProductInfos": [{
      "pref": product.pref,
      "standardId": product.pref,
      "commonName": product.commonName,
      "productName": product.productName,
      "attributeSpecification": product.attributeSpecification,
      "manufacturer": product.manufacturer,
      "unitName": product.unitName,
      "quantity": 1,
      "directions": "口服",
      "singleDose": "10",
      "singleUnit": "袋",
      "useFrequency": "1次/6小时",
      "useFrequencyValue": "ONETIMESSIXHOURS",
    }],
    "tcmTotalDosage": "10",
    "tcmDailyDosage": "3",
    "tcmUsage": "2",
    "tcmDirections": "温服",
    "tcmProcessingMethod": "打粉冲服"
  }
  client.global.set("inquiryProductInfo", JSON.stringify(inquiryProductInfo));
%}

