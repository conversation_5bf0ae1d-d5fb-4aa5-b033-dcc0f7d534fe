### 1. 门店登录获取 token
POST {{baseAdminSystemUrl}}/system/auth/login
Content-Type: application/json
X-Developer: {{developer}}

{
  "username": "15011112222",
  "password": "Abc123456"
}

> {%
  client.global.set("tenantId", response.body.data.tenantId == null ? response.body.data.tenantList[0].id : response.body.data.tenantId);
  client.global.set("token", response.body.data.accessToken);
  console.log("Token: " + response.body.data.accessToken);
  client.global.set("loginUserId", response.body.data.userId);
%}

### 2. 根据条形码查询自建标准库商品（条码不存在）
GET {{baseAppSystemUrl}}/product/present/self-by-barcode?barcode=6923644239959
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}


### 2. 根据条形码查询自建标准库商品（条码已存在）
GET {{baseAppSystemUrl}}/product/present/self-by-barcode?barcode=6911011020137
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

### 3. 保存商品提报（小包装条码在条码网不存在）
POST {{baseAppSystemUrl}}/product/present/save
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{
  "commonName": "阿莫西林胶囊",
  "brandName": "再林",
  "spec": "0.25g*24粒",
  "barcode": "6923644239959",
  "coverImages": ["http://files.test.ybm100.com/INVT/Lzinq/20250318/49ddbe58c2a8f7b32fb2872395cf581af9ec74e16d3cbf745a29c3c3431fe32b.png"],
  "outerPackageImages": [],
  "instructionImages": [],
  "unit": "盒",
  "approvalNumber": "国药准字H23020585",
  "manufacturer": "哈药集团制药总厂"
}

### 3. 保存商品提报（小包装条码在条码网存在）
POST {{baseAppSystemUrl}}/product/present/save
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{
  "commonName": "咳嗽胶囊123",
  "brandName": "999",
  "spec": "50ml",
  "barcode": "0",
  "coverImages": ["http://files.test.ybm100.com/INVT/Lzinq/20250318/49ddbe58c2a8f7b32fb2872395cf581af9ec74e16d3cbf745a29c3c3431fe32b.png"],
  "outerPackageImages": ["http://files.test.ybm100.com/INVT/Lzinq/20250318/49ddbe58c2a8f7b32fb2872395cf581af9ec74e16d3cbf745a29c3c3431fe32b.png"],
  "instructionImages": ["http://files.test.ybm100.com/INVT/Lzinq/20250318/49ddbe58c2a8f7b32fb2872395cf581af9ec74e16d3cbf745a29c3c3431fe32b.png"],
  "unit": "毫升",
  "approvalNumber": "国药准字B66655544",
  "manufacturer": "湖北华饴木本油脂有限公司"
}

### 3. 编辑商品提报（驳回重新编辑）
POST {{baseAppSystemUrl}}/product/present/save
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

{
  "id": "1914189108748693506",
  "commonName": "测试封面",
  "brandName": "无",
  "spec": "12*13",
  "barcode": "0",
  "coverImages": ["http://files.test.ybm100.com/INVT/Lzinq/20250318/49ddbe58c2a8f7b32fb2872395cf581af9ec74e16d3cbf745a29c3c3431fe32b.png"],
  "outerPackageImages": [],
  "instructionImages": [],
  "unit": "盒",
  "approvalNumber": "国药准字B584726956",
  "manufacturer": "测试大药房"
}

### 4. 获取商品提报分页列表
GET {{baseAppSystemUrl}}/product/present/page?pageNo=1&pageSize=10
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

### 5. 获取商品提报详情
GET {{baseAppSystemUrl}}/product/present/get?id=1910222193032085505
Content-Type: application/json
Authorization: Bearer {{token}}
X-Developer: {{developer}}

### 测试响应处理脚本
> {%
  if (response.status === 200) {
    if (response.body.code === 0) {
      console.log("请求成功: " + JSON.stringify(response.body.data));
    } else {
      console.log("业务错误: " + response.body.msg);
    }
  } else {
    console.log("HTTP错误: " + response.status);
  }
%} 