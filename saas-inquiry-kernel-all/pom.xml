<?xml version="1.0" encoding="UTF-8"?>
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.xyy.saas</groupId>
    <artifactId>saas-inquiry-kernel</artifactId>
    <version>${revision}</version>
  </parent>
  <groupId>com.xyy.saas</groupId>
  <artifactId>saas-inquiry-kernel-all</artifactId>
  <version>0.0.1-SNAPSHOT</version>
  <name>saas-inquiry-kernel-all</name>
  <description>saas-inquiry-kernel-all</description>
  <url/>
  <licenses>
    <license/>
  </licenses>
  <properties>
    <java.version>21</java.version>
  </properties>
  <dependencies>

    <!--门店服务-->
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-drugstore-server</artifactId>
      <version>${revision}</version>
    </dependency>
    <!--医院服务-->
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-hospital-server</artifactId>
      <version>${revision}</version>
    </dependency>
    <!--患者服务-->
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-patient-server</artifactId>
      <version>${revision}</version>
    </dependency>
    <!--药师服务-->
    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-pharmacist-server</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-signature-server</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-im-server</artifactId>
      <version>${revision}</version>
    </dependency>

    <dependency>
      <groupId>com.xyy.saas</groupId>
      <artifactId>saas-inquiry-supervision-server</artifactId>
      <version>${revision}</version>
    </dependency>


    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>cn.iocoder.boot</groupId>
      <artifactId>yudao-spring-boot-starter-test</artifactId>
    </dependency>

    <dependency>
      <groupId>com.xyy.common</groupId>
      <artifactId>xyy-common-dubbo-client</artifactId>
      <version>1.0-SNAPSHOT</version>
    </dependency>

    <!--JMH-->
    <dependency>
      <groupId>org.openjdk.jmh</groupId>
      <artifactId>jmh-core</artifactId>
    </dependency>
    <dependency>
      <groupId>org.openjdk.jmh</groupId>
      <artifactId>jmh-generator-annprocess</artifactId>
    </dependency>

  </dependencies>

  <build>
    <!--  打包依赖到jar包中  -->
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>${maven-springboot-plugin.version}</version>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <configuration>
          <compilerArgs>
            <arg>-parameters</arg>
          </compilerArgs>
        </configuration>
      </plugin>
    </plugins>
  </build>

</project>
