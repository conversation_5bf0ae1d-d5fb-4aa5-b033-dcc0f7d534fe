package com.xyy.common.config;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.IOException;
import javax.annotation.PostConstruct;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: xucao
 * @Date: 2025/2/18 20:15
 * @Description: 必须描述类做什么事情, 实现什么功能
 */
@Configuration
public class TomcatServerConfig {
    private static final Logger log = LoggerFactory.getLogger(TomcatServerConfig.class);
    @Value("${server.port:-1}")
    private int port;
    @Value("${server.context-path:/}")
    private String contextPath;
    @Value("${server.servlet.context-path:/}")
    private String servletContextPath;
    public static String configUrl;

    @PostConstruct
    public void initFile() {
        try {
            configUrl = System.getProperty("user.dir") + "/tomcat_server.conf";
            if (configUrl.startsWith("file:")) {
                configUrl = configUrl.replace("file:", "");
            }
            File file = new File(configUrl);
            if (file.exists()) {
                file.delete();
            }
            file.createNewFile();
            log.info("文件创建成功:{}", configUrl);
        } catch (FileNotFoundException var2) {
            FileNotFoundException e = var2;
            log.error("获取路径异常：", e);
        } catch (IOException var3) {
            IOException e = var3;
            log.error("创建文件异常：", e);
        }
    }

    public int getPort() {
        return this.port;
    }

    public String getContextPath() {
        return this.contextPath;
    }

    public String getServletContextPath() {
        return this.servletContextPath;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public void setContextPath(String contextPath) {
        this.contextPath = contextPath;
    }

    public void setServletContextPath(String servletContextPath) {
        this.servletContextPath = servletContextPath;
    }

    public boolean equals(Object o) {
        if (o == this) {
            return true;
        } else if (!(o instanceof TomcatServerConfig)) {
            return false;
        } else {
            TomcatServerConfig other = (TomcatServerConfig)o;
            if (!other.canEqual(this)) {
                return false;
            } else if (this.getPort() != other.getPort()) {
                return false;
            } else {
                Object this$contextPath = this.getContextPath();
                Object other$contextPath = other.getContextPath();
                if (this$contextPath == null) {
                    if (other$contextPath != null) {
                        return false;
                    }
                } else if (!this$contextPath.equals(other$contextPath)) {
                    return false;
                }

                Object this$servletContextPath = this.getServletContextPath();
                Object other$servletContextPath = other.getServletContextPath();
                if (this$servletContextPath == null) {
                    if (other$servletContextPath != null) {
                        return false;
                    }
                } else if (!this$servletContextPath.equals(other$servletContextPath)) {
                    return false;
                }

                return true;
            }
        }
    }

    protected boolean canEqual(Object other) {
        return other instanceof TomcatServerConfig;
    }

    public int hashCode() {
        int PRIME = 59;
        int result = 1;
        result = result * 59 + this.getPort();
        Object $contextPath = this.getContextPath();
        result = result * 59 + ($contextPath == null ? 43 : $contextPath.hashCode());
        Object $servletContextPath = this.getServletContextPath();
        result = result * 59 + ($servletContextPath == null ? 43 : $servletContextPath.hashCode());
        return result;
    }

    public String toString() {
        return "TomcatServerConfig(port=" + this.getPort() + ", contextPath=" + this.getContextPath() + ", servletContextPath=" + this.getServletContextPath() + ")";
    }
}
