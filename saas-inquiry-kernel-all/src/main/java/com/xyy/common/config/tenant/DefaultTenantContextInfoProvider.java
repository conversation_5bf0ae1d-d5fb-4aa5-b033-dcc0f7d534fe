package com.xyy.common.config.tenant;


import static com.xyy.saas.inquiry.constant.TenantConstant.TENANT_CONTEXT_KEY_TENANT_DTO;

import cn.iocoder.yudao.framework.tenant.core.context.TenantContextInfoProvider;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.xyy.saas.inquiry.pojo.TenantDto;
import java.util.Map;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Component
public class DefaultTenantContextInfoProvider implements TenantContextInfoProvider {

    @Autowired
    private TenantApi tenantApi;

    /**
     * 获取租户相关信息
     *
     * @param tenantId 租户ID
     * @return 租户相关信息的键值对
     */
    @Override
    public Map<String, Object> getTenantInfo(Long tenantId) {
        TenantDto tenant = null;
        try {
            tenant = tenantApi.getTenant(tenantId);
        } catch (Throwable ignore) {}

        if (tenant == null) {
            return Map.of();
        }

        return Map.of(TENANT_CONTEXT_KEY_TENANT_DTO, tenant);
    }
}
