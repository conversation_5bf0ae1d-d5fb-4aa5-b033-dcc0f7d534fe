package com.xyy.saas.inquiry.kernel.trace.controller;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.kernel.trace.service.TraceNodeCleanupService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 链路追踪数据清理管理Controller
 */
@Tag(name = "管理后台 - 链路追踪数据清理")
@RestController
@RequestMapping("/admin-api/kernel/trace/cleanup")
@Slf4j
public class TraceCleanupController {

    @Autowired
    private TraceNodeCleanupService cleanupService;

    @PostMapping("/manual")
    @Operation(summary = "手动触发数据清理")
    public CommonResult<Integer> manualCleanup(
            @Parameter(description = "清理多少天前的数据", example = "7")
            @RequestParam(value = "days", defaultValue = "2") int days,
            @Parameter(description = "超时时间（小时），0表示不限制", example = "2")
            @RequestParam(value = "timeoutHours", defaultValue = "0") int timeoutHours) {
        
        log.info("管理员手动触发数据清理，清理{}天前的数据，超时时间: {}小时", days, timeoutHours);
        
        if (days <= 0) {
            return CommonResult.error("清理天数必须大于0");
        }
        
        if (days > 30) {
            return CommonResult.error("为了安全考虑，一次最多只能清理30天前的数据");
        }
        
        if (timeoutHours < 0 || timeoutHours > 12) {
            return CommonResult.error("超时时间必须在0-12小时之间");
        }
        
        try {
            int deletedCount = cleanupService.manualCleanup(days, timeoutHours);
            return success(deletedCount);
        } catch (Exception e) {
            log.error("手动清理数据失败", e);
            return CommonResult.error("清理失败: " + e.getMessage());
        }
    }

    @GetMapping("/expired-count")
    @Operation(summary = "获取过期数据统计")
    public CommonResult<Long> getExpiredDataCount() {
        try {
            long count = cleanupService.getExpiredDataCount();
            return success(count);
        } catch (Exception e) {
            log.error("获取过期数据统计失败", e);
            return CommonResult.error("获取统计失败: " + e.getMessage());
        }
    }

    @GetMapping("/status")
    @Operation(summary = "获取清理服务状态")
    public CommonResult<String> getCleanupStatus() {
        return success("清理服务正常运行");
    }
} 