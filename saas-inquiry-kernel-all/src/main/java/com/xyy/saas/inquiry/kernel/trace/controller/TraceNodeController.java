package com.xyy.saas.inquiry.kernel.trace.controller;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.kernel.trace.controller.vo.TraceNodePageReqVO;
import com.xyy.saas.inquiry.kernel.trace.controller.vo.TraceNodeRespVO;
import com.xyy.saas.inquiry.kernel.trace.convert.TraceNodeConvert;
import com.xyy.saas.inquiry.kernel.trace.dto.TraceNodeQuery;
import com.xyy.saas.inquiry.trace.model.TraceNodeData;
import com.xyy.saas.inquiry.trace.service.TraceNodeService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 链路追踪查询控制器
 */
@Tag(name = "管理后台 - 链路追踪")
@RestController
@RequestMapping("/admin-api/kernel/trace")
public class TraceNodeController {

    @Resource
    private TraceNodeService traceNodeService;

    @GetMapping("/page")
    @Operation(summary = "分页查询链路追踪数据")
    public CommonResult<PageResult<TraceNodeRespVO>> pageTraceNode(@Valid TraceNodePageReqVO pageVO) {
        // 转换为查询对象
        TraceNodeQuery query = TraceNodeConvert.INSTANCE.convert(pageVO);
        
        // 查询ES
        TraceNodeService.PageResult<TraceNodeData> result = traceNodeService.findByCondition(query);

        // 转换为VO
        List<TraceNodeRespVO> list = TraceNodeConvert.INSTANCE.convertList(result.getContent());
        return success(new PageResult<>(list, result.getTotal()));
    }

} 