package com.xyy.saas.inquiry.kernel.trace.service;

import com.xyy.saas.inquiry.trace.model.TraceNodeData;
import com.xyy.saas.inquiry.trace.service.TraceNodeService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.Semaphore;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * @Author: xucao
 * @DateTime: 2025/7/28 20:57
 * @Description: 链路追踪缓存池服务实现
 **/
@Service("traceBufferPoolService")
@Slf4j
public class TraceBufferPoolService {

    /**
     * 队列大小
     */
    @Value("${trace.buffer-queue-size:2000}")
    private Integer QUEUE_SIZE ;

    /**
     * 批量处理阈值
     */
    @Value("${trace.buffer-batch-size:50}")
    private Integer BATCH_SIZE;

    /**
     * 缓冲队列
     */
    private BlockingQueue<TraceNodeData> bufferQueue;

    /**
     * 批量写入线程池
     */
    private ThreadPoolExecutor batchWriterExecutor;

    /**
     * 运行状态
     */
    private final AtomicBoolean running = new AtomicBoolean(true);

    /**
     * 队列中元素数量的近似计数器，避免频繁调用 bufferQueue.size()
     */
    private final AtomicInteger queueElementCount = new AtomicInteger(0);
    
    /**
     * 信号量，控制并发执行的批量写入任务数量，避免任务积压
     */
    private final Semaphore batchWriteSemaphore = new Semaphore(5);

    /**
     * 标记是否正在处理批量任务，避免重复触发
     */
    private final AtomicBoolean batchProcessing = new AtomicBoolean(false);

    @Resource
    private TraceNodeService traceNodeService;

    @PostConstruct
    public void init() {
        // 初始化主缓冲队列
        this.bufferQueue = new LinkedBlockingQueue<>(QUEUE_SIZE);

        // 初始化批量写入线程池
        this.batchWriterExecutor = new ThreadPoolExecutor(
                2,                    // 核心线程数
                5,                    // 最大线程数
                60L, TimeUnit.SECONDS,              // 线程空闲时间
                new LinkedBlockingQueue<>(300),     // 任务队列
                r -> new Thread(r, "trace-batch-writer"), // 线程命名
                new ThreadPoolExecutor.DiscardPolicy() // 队列满时丢弃，不阻塞
        );

        log.info("链路追踪缓冲池服务启动 - 队列大小：{}，批量处理大小：{}", QUEUE_SIZE, BATCH_SIZE);
    }

    /**
     * 保存链路追踪数据到缓冲池
     *
     * @param traceNodeData 链路追踪数据
     */
    public void save(TraceNodeData traceNodeData) {
        if (!running.get() || traceNodeData == null) {
            return;
        }

        try {
            // 非阻塞写入主缓冲队列
            boolean success = bufferQueue.offer(traceNodeData);

            if (success) {
                // 增加计数器
                int currentCount = queueElementCount.incrementAndGet();

                // 简化触发逻辑，避免模运算导致的遗漏
                // 使用CAS操作避免重复触发
                if (currentCount >= BATCH_SIZE && batchProcessing.compareAndSet(false, true)) {
                    // 尝试触发批量处理，如果失败则重置状态
                    if (!triggerBatchWrite()) {
                        batchProcessing.set(false);
                    }
                }
            } else {
                // 主队列满时丢弃，不影响主业务
                log.warn("链路追踪缓冲队列已满，丢弃数据");
            }
        } catch (Exception e) {
            // 异常时丢弃，绝不影响主业务
            log.error("链路追踪数据加入缓冲队列异常", e);
        }
    }

    /**
     * 触发批量写入任务
     * @return 是否成功触发
     */
    private boolean triggerBatchWrite() {
        // 尝试获取信号量，如果获取不到直接返回，避免忙循环
        if (batchWriteSemaphore.tryAcquire()) {
            try {
                // 提交批量写入任务到专门的线程池
                batchWriterExecutor.submit(this::performBatchWrite);
                return true;
            } catch (Exception e) {
                // 提交任务失败时，需要释放信号量
                batchWriteSemaphore.release();
                log.error("提交批量写入任务失败", e);
                return false;
            }
        } else {
            // 获取不到信号量时，返回失败
            return false;
        }
    }

    /**
     * 执行批量写入操作
     */
    private void performBatchWrite() {
        try {
            // 限制单次执行最多处理100批数据，防止长时间占用线程
            int maxBatches = 100;
            int processedBatches = 0;

            // 循环处理，直到队列中数据不足一批或达到最大处理批次
            // 修改条件：当队列中有数据就处理，即使不足一批
            while (queueElementCount.get() > 0 && processedBatches < maxBatches) {
                List<TraceNodeData> batch = new ArrayList<>(BATCH_SIZE);

                // 从队列中取出一批数据，即使不足一批也处理
                int count = bufferQueue.drainTo(batch, BATCH_SIZE);
                // 更新计数器
                queueElementCount.addAndGet(-count);

                if (count > 0) {
                    // 批量写入ES
                    int saved = traceNodeService.batchSaveTraceNode(batch);
                    log.debug("批量写入链路追踪数据完成 - 期望写入：{}，实际写入：{}", count, saved);
                    processedBatches++;
                } else {
                    // 如果没有取到数据，退出循环
                    break;
                }
            }
        } catch (Exception e) {
            log.error("批量写入链路追踪数据异常", e);
        } finally {
            // 释放信号量
            batchWriteSemaphore.release();
            
            // 重置处理状态
            batchProcessing.set(false);
            
            // 检查是否还有数据需要处理，如果有则触发处理
            // 注意：这里不立即触发新任务，而是依赖定时任务或其他触发机制
            // 避免递归提交任务导致的任务堆积
        }
    }

    /**
     * 定时检查队列，确保数据及时处理
     */
    @Scheduled(fixedDelay = 3000, initialDelay = 3000)
    public void scheduledBatchWrite() {
        if (!running.get()) {
            return;
        }

        try {
            // 只要有数据且当前未在处理时就触发批量处理
            if (queueElementCount.get() > 0) {
                // 使用CAS操作避免重复触发
                if (batchProcessing.compareAndSet(false, true)) {
                    // 尝试触发批量处理，如果失败则重置状态
                    if (!triggerBatchWrite()) {
                        batchProcessing.set(false);
                    }
                }
            }
        } catch (Exception e) {
            // 重置处理状态
            batchProcessing.set(false);
            log.error("定时批量处理任务异常", e);
        }
    }

    /**
     * 优雅关闭 - 确保剩余数据不丢失
     */
    @PreDestroy
    public void shutdown() {
        log.info("链路追踪缓冲池服务正在关闭...");
        running.set(false);

        try {
            // 等待现有任务完成，最多等待5秒
            long shutdownStart = System.currentTimeMillis();
            while (batchProcessing.get() && (System.currentTimeMillis() - shutdownStart) < 5000) {
                Thread.sleep(100);
            }

            // 处理剩余数据，使用信号量控制确保与线程池任务不冲突
            int attempts = 0;
            final int maxAttempts = 50;
            while (queueElementCount.get() > 0 && attempts < maxAttempts) {
                // 尝试获取信号量后再处理数据
                if (batchWriteSemaphore.tryAcquire()) {
                    try {
                        List<TraceNodeData> batch = new ArrayList<>();
                        int count = bufferQueue.drainTo(batch, BATCH_SIZE);
                        queueElementCount.addAndGet(-count);
                        if (count > 0) {
                            traceNodeService.batchSaveTraceNode(batch);
                            log.debug("关闭时处理数据 - 处理：{}条", count);
                        }
                    } finally {
                        // 释放信号量
                        batchWriteSemaphore.release();
                    }
                } else {
                    // 如果获取不到信号量，等待一下
                    Thread.sleep(100);
                }
                attempts++;
            }

            if (queueElementCount.get() > 0) {
                log.warn("关闭时仍有 {} 条数据未处理", queueElementCount.get());
            }

            // 关闭批量写入线程池
            batchWriterExecutor.shutdown();
            if (!batchWriterExecutor.awaitTermination(30, TimeUnit.SECONDS)) {
                batchWriterExecutor.shutdownNow();
            }

            log.info("链路追踪缓冲池服务关闭完成");
        } catch (Exception e) {
            log.warn("关闭时处理剩余数据异常", e);
        }
    }
}