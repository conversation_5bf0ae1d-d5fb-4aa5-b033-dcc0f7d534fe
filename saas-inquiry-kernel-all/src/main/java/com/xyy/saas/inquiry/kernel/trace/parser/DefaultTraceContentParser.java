package com.xyy.saas.inquiry.kernel.trace.parser;

import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.trace.model.TraceNodeData;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import java.util.HashMap;
import java.util.Map;
import java.util.function.Function;

/**
 * 默认链路追踪内容解析器
 * 使用 Function 方式处理不同节点类型的内容解析
 */
@Component
@Slf4j
public class DefaultTraceContentParser extends TraceContentParser {


    @Override
    public TraceNodeData parserContent(TraceNodeData traceNodeData) {
        // 如果没有找到对应的解析函数，返回原始数据
        return traceNodeData;
    }

} 