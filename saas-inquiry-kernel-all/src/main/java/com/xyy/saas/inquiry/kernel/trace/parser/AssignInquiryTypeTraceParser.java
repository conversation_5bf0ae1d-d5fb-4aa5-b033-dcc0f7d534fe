package com.xyy.saas.inquiry.kernel.trace.parser;

import com.alibaba.fastjson2.JSONObject;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.trace.parser.TraceDataParser;
import org.springframework.stereotype.Component;
import java.lang.reflect.Method;

/**
 * @Author: xucao
 * @DateTime: 2025/6/11 10:55
 * @Description: 自动开方判定
 **/
@Component
public class AssignInquiryTypeTraceParser extends TraceDataParser {

    /**
     * 解析业务单据号
     *
     * @param method       被执行的方法
     * @param args         方法参数
     * @param result       方法返回值
     * @param prefLocation
     * @return 业务单据号
     */
    @Override
    public String parseBusinessNo(Method method, Object[] args, Object result, String prefLocation) {
        return super.defaultParseBusinessNo(method, args, result, prefLocation);
    }

    /**
     * 解析业务数据
     *
     * @param method 被执行的方法
     * @param args   方法参数
     * @param result
     * @return 业务数据，可以是任意对象，会被转换为JSON字符串存储
     */
    @Override
    public Object parseBusinessData(Method method, Object[] args, Object result) {
        JSONObject obj = new JSONObject();
        if(args == null || args.length == 0){
            return obj;
        }
        for(Object arg : args){
            if(arg instanceof InquiryRecordDto){
                JSONObject jsonObject = JSONObject.from(arg);
                obj.put("autoInquiry",jsonObject.getString("autoInquiry"));
            }
        }
        return obj;
    }
}
