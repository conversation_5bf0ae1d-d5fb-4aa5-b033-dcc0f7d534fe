package com.xyy.saas.inquiry.kernel.config;

import com.baomidou.mybatisplus.autoconfigure.ConfigurationCustomizer;
import com.xyy.saas.inquiry.annotation.forcemaster.ForceMasterInterceptor;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * @Author: xucao
 * @DateTime: 2025/4/23 21:53
 * @Description: mybatis-plus配置
 **/
@Configuration
public class MybatisPlusConfig {
    @Bean
    public ForceMasterInterceptor forceMasterInterceptor() {
        return new ForceMasterInterceptor();
    }

    @Bean
    public ConfigurationCustomizer configurationCustomizer() {
        return configuration -> {
            configuration.addInterceptor(forceMasterInterceptor());
        };
    }
}
