package com.xyy.saas.inquiry.kernel.trace.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;

/**
 * 链路追踪查询对象
 */
@Data
@Accessors(chain = true)
public class TraceNodeQuery {

    /**
     * 业务单据号
     */
    private String businessNo;

    /**
     * 节点编码
     */
    private String nodeCode;

    /**
     * 节点名称
     */
    private String nodeName;


    /**
     * 关键字
     */
    private String keywords;

    /**
     * 租户ID
     */
    private Long tenantId;

    /**
     * 环境标签
     */
    private String envTag;

    /**
     * 是否成功
     */
    private Boolean success;

    /**
     * 开始时间范围-开始
     */
    private LocalDateTime startTimeBegin;

    /**
     * 开始时间范围-结束
     */
    private LocalDateTime startTimeEnd;

    /**
     * 执行时长范围-最小值
     */
    private Long durationMin;

    /**
     * 执行时长范围-最大值
     */
    private Long durationMax;

    /**
     * 排序字段集合
     */
    private String orderBy;

    /**
     * 排序方式
     */
    private String orderDirection;

    /**
     * 页码
     */
    private Integer pageNo;

    /**
     * 每页条数
     */
    private Integer pageSize;
}