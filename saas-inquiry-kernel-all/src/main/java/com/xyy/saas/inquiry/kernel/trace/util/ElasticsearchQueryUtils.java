package com.xyy.saas.inquiry.kernel.trace.util;

import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;

/**
 * Elasticsearch查询工具类
 */
public class ElasticsearchQueryUtils {

    /**
     * 添加范围查询条件
     *
     * @param boolQuery 布尔查询构建器
     * @param field 字段名
     * @param min 最小值
     * @param max 最大值
     */
    public static void addRangeQuery(BoolQueryBuilder boolQuery, String field, Long min, Long max) {
        if (min != null || max != null) {
            RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery(field);
            if (min != null) {
                rangeQuery.gte(min);
            }
            if (max != null) {
                rangeQuery.lte(max);
            }
            boolQuery.must(rangeQuery);
        }
    }

    /**
     * 添加时间范围查询条件
     *
     * @param boolQuery 布尔查询构建器
     * @param field 字段名
     * @param begin 开始时间
     * @param end 结束时间
     */
    public static void addTimeRangeQuery(BoolQueryBuilder boolQuery, String field,
        String begin, String end) {
        if (begin != null && end != null) {
            RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery(field)
                .gte(begin)
                .lte(end);
            boolQuery.must(rangeQuery);
        }
    }

    /**
     * 添加时间范围查询条件（使用毫秒时间戳）
     *
     * @param boolQuery 布尔查询构建器
     * @param field 字段名
     * @param beginTimestamp 开始时间戳（毫秒）
     * @param endTimestamp 结束时间戳（毫秒）
     */
    public static void addTimeRangeQuery(BoolQueryBuilder boolQuery, String field,
        long beginTimestamp, long endTimestamp) {
        RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery(field)
            .gte(beginTimestamp)
            .lte(endTimestamp);
        boolQuery.must(rangeQuery);
    }

    /**
     * 安全地将对象转换为Long类型
     * 
     * @param value 待转换的值
     * @return Long值，如果转换失败返回null
     */
    public static Long convertToLong(Object value) {
        if (value == null) {
            return null;
        }
        
        if (value instanceof Long) {
            return (Long) value;
        }
        
        if (value instanceof Number) {
            return ((Number) value).longValue();
        }
        
        if (value instanceof String) {
            try {
                return Long.parseLong((String) value);
            } catch (NumberFormatException e) {
                return null;
            }
        }
        
        return null;
    }
}