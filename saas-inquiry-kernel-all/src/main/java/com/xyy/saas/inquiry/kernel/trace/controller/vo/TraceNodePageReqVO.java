package com.xyy.saas.inquiry.kernel.trace.controller.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 链路追踪分页查询请求VO
 */
@Schema(description = "管理后台 - 链路追踪分页查询 Request VO")
@Data
public class TraceNodePageReqVO {

    @Schema(description = "业务单据号")
    private String businessNo;

    @Schema(description = "节点编码")
    private String nodeCode;

    @Schema(description = "节点名称")
    private String nodeName;

    @Schema(description = "租户ID")
    private Long tenantId;

    @Schema(description = "关键字")
    private String keywords;

    @Schema(description = "环境标签")
    private String envTag;

    @Schema(description = "是否成功")
    private Boolean success;

    @Schema(description = "开始时间范围 - 开始")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startTimeBegin;

    @Schema(description = "开始时间范围 - 结束")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime startTimeEnd;

    @Schema(description = "执行时长范围 - 最小值(毫秒)")
    private Long durationMin;

    @Schema(description = "执行时长范围 - 最大值(毫秒)")
    private Long durationMax;

    @Schema(description = "排序字段", example = "startTime")
    private String orderBy;

    @Schema(description = "排序方式", example = "desc")
    private String orderDirection;

    @Schema(description = "页码", required = true, example = "1")
    private Integer pageNo;

    @Schema(description = "每页条数", required = true, example = "10")
    private Integer pageSize;
} 