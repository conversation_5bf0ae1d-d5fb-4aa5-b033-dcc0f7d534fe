package com.xyy.saas.inquiry.kernel.trace.service;

import cn.hutool.extra.spring.SpringUtil;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.kernel.trace.dao.TraceNodeDao;
import com.xyy.saas.inquiry.kernel.trace.dto.TraceNodeQuery;
import com.xyy.saas.inquiry.kernel.trace.parser.TraceContentParser;
import com.xyy.saas.inquiry.trace.model.TraceNodeData;
import com.xyy.saas.inquiry.trace.service.TraceNodeService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.util.ClassUtils;
import org.springframework.util.ReflectionUtils;

import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 链路追踪服务实现类
 */
@Service
@Slf4j
public class TraceNodeServiceImpl implements TraceNodeService {

    @Resource
    private TraceNodeDao traceNodeDao;


    @Override
    public void saveTraceNode(TraceNodeData traceNodeData) {
        try {
            traceNodeDao.save(traceNodeData);
        } catch (Exception e) {
            log.error("保存链路追踪数据失败", e);
        }
    }

    @Override
    @Async("traceNodeExecutor")
    public void saveTraceNodeAsync(TraceNodeData traceNodeData) {
        saveTraceNode(traceNodeData);
    }

    @Override
    public int batchSaveTraceNode(List<TraceNodeData> traceNodeDataList) {
        try {
            return traceNodeDao.batchSave(traceNodeDataList);
        } catch (Exception e) {
            log.error("批量保存链路追踪数据失败", e);
            return 0;
        }
    }

    @Override
    public PageResult<TraceNodeData> findByCondition(Object param) {
        try {
            TraceNodeQuery query = (TraceNodeQuery) param;
            PageResult<TraceNodeData> pageResult = traceNodeDao.findByCondition(query);
            pageResult.setContent(contentPase(pageResult.getContent()));
            return pageResult;
        } catch (Exception e) {
            log.error("查询链路追踪数据失败", e);
            return new PageResult<>(new ArrayList<>(), 0);
        }
    }

    private List<TraceNodeData> contentPase(List<TraceNodeData> content) {
        if (content == null || content.isEmpty()) {
            return content;
        }
        List<TraceNodeData> result = new ArrayList<>();
        for (TraceNodeData traceNodeData : content) {
            try {
                // 1. 根据当前节点的nodeCode，去TraceNodeEnum枚举中找到对应的contentParser
                TraceNodeEnum nodeEnum = TraceNodeEnum.findTraceNodeEnumByCode(traceNodeData.getNodeCode());
                // 2. 根据contentParser的类路径，获取转换器
                TraceContentParser contentParser = null;
                if (nodeEnum == null || (contentParser = getContentParser(nodeEnum.getContentParser())) == null) {
                    continue;
                }
                traceNodeData = contentParser.parserContent(traceNodeData);
            } catch (Exception e) {
                log.error("解析链路追踪内容失败，nodeCode: {}, businessNo: {}", traceNodeData.getNodeCode(), traceNodeData.getBusinessNo(), e);
            } finally {
                result.add(traceNodeData);
            }
        }
        return result;
    }


    /**
     * 获取内容解析器 仿照TraceNodeAspect#getParser方法实现
     *
     * @param contentParserClassName 内容解析器类名
     * @return 内容解析器实例
     */
    private TraceContentParser getContentParser(String contentParserClassName) {
        if (StringUtils.isBlank(contentParserClassName)) {
            return null;
        }
        try {
            Class<?> parserClass = ClassUtils.forName(contentParserClassName, ClassUtils.getDefaultClassLoader());
            if (!TraceContentParser.class.isAssignableFrom(parserClass)) {
                log.warn("指定的内容解析器类 {} 不是 TraceContentParser 的实现类", contentParserClassName);
                return null;
            }
            // 尝试从Spring容器中获取
            try {
                return (TraceContentParser) SpringUtil.getBean(parserClass);
            } catch (BeansException e) {
                // 如果Spring容器中不存在，则创建新实例
                return (TraceContentParser) ReflectionUtils.accessibleConstructor(parserClass).newInstance();
            }
        } catch (Exception e) {
            log.warn("创建内容解析器实例失败", e);
            return null;
        }
    }
} 