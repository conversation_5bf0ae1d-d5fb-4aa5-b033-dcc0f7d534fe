package com.xyy.saas.inquiry.kernel.trace.config;

import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpHost;
import org.elasticsearch.client.RestClient;
import org.elasticsearch.client.RestHighLevelClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.ArrayList;
import java.util.List;

/**
 * Elasticsearch配置类
 * <AUTHOR>
 */
@Data
@Configuration
@Slf4j
@ConfigurationProperties(prefix = "elasticsearch")
public class ElasticsearchConfig {

    /**
     * ES集群节点列表
     */
    private List<ElasticsearchNode> nodes = new ArrayList<>();

    /**
     * ES集群名称
     */
    @Value("${elasticsearch.cluster-name:saas-es}")
    private String clusterName;
    
    /**
     * ES版本号，用于兼容性处理
     * 例如：6.8.6 或 7.17.3
     */
    @Value("${elasticsearch.version:7.17.3}")
    private String esVersion;

    /**
     * 创建RestHighLevelClient (兼容ES 6.x和7.x)
     * @return RestHighLevelClient实例
     */
    @Bean
    public RestHighLevelClient restHighLevelClient() {
        log.info("初始化Elasticsearch RestHighLevelClient，ES版本: {}", esVersion);
        
        // 将配置的节点转换为HttpHost数组
        HttpHost[] httpHosts = nodes.stream()
                .map(node -> new HttpHost(node.getHost(), node.getPort(), "http"))
                .toArray(HttpHost[]::new);
        
        // 创建并返回RestHighLevelClient
        return new RestHighLevelClient(
                RestClient.builder(httpHosts)
                        .setRequestConfigCallback(requestConfigBuilder -> 
                            requestConfigBuilder
                                .setConnectTimeout(5000)
                                .setSocketTimeout(60000))
        );
    }

    /**
     * ES节点配置
     */
    @Data
    public static class ElasticsearchNode {
        private String host;
        private int port;
    }
} 