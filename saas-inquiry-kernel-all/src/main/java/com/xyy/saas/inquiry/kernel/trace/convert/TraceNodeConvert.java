package com.xyy.saas.inquiry.kernel.trace.convert;

import com.xyy.saas.inquiry.kernel.trace.controller.vo.TraceNodePageReqVO;
import com.xyy.saas.inquiry.kernel.trace.controller.vo.TraceNodeRespVO;
import com.xyy.saas.inquiry.kernel.trace.dto.TraceNodeQuery;
import com.xyy.saas.inquiry.trace.model.TraceNodeData;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

/**
 * 链路追踪转换类
 */
@Mapper
public interface TraceNodeConvert {

    TraceNodeConvert INSTANCE = Mappers.getMapper(TraceNodeConvert.class);

    /**
     * 将 TraceNodeData 转换为 TraceNodeRespVO
     *
     * @param bean TraceNodeData
     * @return TraceNodeRespVO
     */
    TraceNodeRespVO convert(TraceNodeData bean);

    /**
     * 将 TraceNodeData 列表转换为 TraceNodeRespVO 列表
     *
     * @param list TraceNodeData 列表
     * @return TraceNodeRespVO 列表
     */
    List<TraceNodeRespVO> convertList(List<TraceNodeData> list);

    TraceNodeQuery convert(TraceNodePageReqVO bean);
} 