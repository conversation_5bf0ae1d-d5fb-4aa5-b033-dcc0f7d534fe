package com.xyy.saas.inquiry.kernel.trace.util;

import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.admin.indices.create.CreateIndexRequest;
import org.elasticsearch.client.Request;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.Response;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.client.indices.CreateIndexResponse;
import org.elasticsearch.client.indices.GetIndexRequest;
import org.elasticsearch.xcontent.XContentType;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.io.IOException;

/**
 * Elasticsearch索引初始化工具类
 */
@Component
@Slf4j
public class ElasticsearchIndexInitializer {

    private static final String INDEX_NAME = "inquiry_trace_node";
    private static final String TYPE_NAME = "doc";

    @Resource
    private RestHighLevelClient restHighLevelClient;
    
    /**
     * ES版本号，用于兼容性处理
     */
    @Value("${elasticsearch.version:7.17.3}")
    private String esVersion;
    
    /**
     * 是否是ES 7.x版本
     */
    private boolean isES7() {
        return esVersion.startsWith("7.");
    }

    /**
     * 应用启动时初始化索引
     */
    @PostConstruct
    public void init() {
        try {
            log.info("开始初始化ES索引: {}, ES版本: {}", INDEX_NAME, esVersion);
            
            boolean exists;
            if (isES7()) {
                // ES 7.x 方式检查索引是否存在
                exists = checkIndexExistsES7();
            } else {
                // ES 6.x 方式检查索引是否存在
                exists = checkIndexExistsES6();
            }
            
            if (!exists) {
                log.info("索引 {} 不存在，开始创建", INDEX_NAME);
                if (isES7()) {
                    createIndexES7();
                } else {
                    createIndexES6();
                }
            } else {
                log.info("索引 {} 已存在，无需创建", INDEX_NAME);
            }
        } catch (Exception e) {
            log.error("初始化ES索引失败: {}", e.getMessage(), e);
            if (e.getCause() != null) {
                log.error("根本原因: {}", e.getCause().getMessage(), e.getCause());
            }
        }
    }
    
    /**
     * ES 7.x 方式检查索引是否存在
     */
    private boolean checkIndexExistsES7() throws IOException {
        GetIndexRequest getIndexRequest = new GetIndexRequest(INDEX_NAME);
        return restHighLevelClient.indices().exists(getIndexRequest, RequestOptions.DEFAULT);
    }
    
    /**
     * ES 6.x 方式检查索引是否存在
     */
    private boolean checkIndexExistsES6() throws IOException {
        // ES 6.x 使用的是旧版API，但可以通过低级REST客户端检查
        Request request = new Request("HEAD", "/" + INDEX_NAME);
        Response response = restHighLevelClient.getLowLevelClient().performRequest(request);
        return response.getStatusLine().getStatusCode() == 200;
    }
    
    /**
     * ES 7.x 方式创建索引
     */
    private void createIndexES7() throws IOException {
        // 创建索引请求
        org.elasticsearch.client.indices.CreateIndexRequest request = 
                new org.elasticsearch.client.indices.CreateIndexRequest(INDEX_NAME);
        
        // 设置索引映射
        request.source(getIndexMappingForES7(), XContentType.JSON);
        
        // 执行创建索引
        CreateIndexResponse response = 
                restHighLevelClient.indices().create(request, RequestOptions.DEFAULT);
        
        log.info("创建索引 {} {}", INDEX_NAME, response.isAcknowledged() ? "成功" : "失败");
    }
    
    /**
     * ES 6.x 方式创建索引
     */
    private void createIndexES6() throws IOException {
        // 创建索引请求
        CreateIndexRequest request = new CreateIndexRequest(INDEX_NAME);
        
        // 设置索引映射
        request.source(getIndexMappingForES6(), XContentType.JSON);
        
        // 执行创建索引
        org.elasticsearch.action.admin.indices.create.CreateIndexResponse response = 
                restHighLevelClient.indices().create(request, RequestOptions.DEFAULT);
        
        log.info("创建索引 {} {}", INDEX_NAME, response.isAcknowledged() ? "成功" : "失败");
    }
    
    /**
     * 获取ES 6.x版本的索引映射
     */
    private String getIndexMappingForES6() {
        return "{\n" +
                "  \"settings\": {\n" +
                "    \"number_of_shards\": 3,\n" +
                "    \"number_of_replicas\": 1,\n" +
                "    \"refresh_interval\": \"1s\"\n" +
                "  },\n" +
                "  \"mappings\": {\n" +
                "    \"" + TYPE_NAME + "\": {\n" +
                "      \"properties\": {\n" +
                "        \"id\": { \"type\": \"keyword\" },\n" +
                "        \"businessNo\": { \"type\": \"keyword\" },\n" +
                "        \"nodeCode\": { \"type\": \"keyword\" },\n" +
                "        \"nodeName\": { \"type\": \"keyword\" },\n" +
                "        \"nodeDesc\": { \"type\": \"keyword\" },\n" +
                "        \"startTime\": { \"type\": \"date\", \"format\": \"strict_date_optional_time||epoch_millis\" },\n" +
                "        \"endTime\": { \"type\": \"date\", \"format\": \"strict_date_optional_time||epoch_millis\" },\n" +
                "        \"duration\": { \"type\": \"long\" },\n" +
                "        \"success\": { \"type\": \"boolean\" },\n" +
                "        \"errorMsg\": { \"type\": \"text\" },\n" +
                "        \"businessData\": { \"type\": \"keyword\"},\n" +
                "        \"tenantId\": { \"type\": \"long\" },\n" +
                "        \"envTag\": { \"type\": \"keyword\" },\n" +
                "        \"createTime\": { \"type\": \"date\", \"format\": \"strict_date_optional_time||epoch_millis\" }\n" +
                "      }\n" +
                "    }\n" +
                "  }\n" +
                "}";
    }
    
    /**
     * 获取ES 7.x版本的索引映射
     */
    private String getIndexMappingForES7() {
        return "{\n" +
                "  \"settings\": {\n" +
                "    \"number_of_shards\": 3,\n" +
                "    \"number_of_replicas\": 1,\n" +
                "    \"refresh_interval\": \"1s\"\n" +
                "  },\n" +
                "  \"mappings\": {\n" +
                "    \"properties\": {\n" +
                "      \"id\": { \"type\": \"keyword\" },\n" +
                "      \"businessNo\": { \"type\": \"keyword\" },\n" +
                "      \"nodeCode\": { \"type\": \"keyword\" },\n" +
                "      \"nodeName\": { \"type\": \"keyword\" },\n" +
                "      \"nodeDesc\": { \"type\": \"keyword\" },\n" +
                "      \"startTime\": { \"type\": \"date\", \"format\": \"strict_date_optional_time||epoch_millis\" },\n" +
                "      \"endTime\": { \"type\": \"date\", \"format\": \"strict_date_optional_time||epoch_millis\" },\n" +
                "      \"duration\": { \"type\": \"long\" },\n" +
                "      \"success\": { \"type\": \"boolean\" },\n" +
                "      \"errorMsg\": { \"type\": \"text\" },\n" +
                "      \"businessData\": { \"type\": \"keyword\" },\n" +
                "      \"tenantId\": { \"type\": \"long\" },\n" +
                "      \"envTag\": { \"type\": \"keyword\" },\n" +
                "      \"createTime\": { \"type\": \"date\", \"format\": \"strict_date_optional_time||epoch_millis\" }\n" +
                "    }\n" +
                "  }\n" +
                "}";
    }
} 