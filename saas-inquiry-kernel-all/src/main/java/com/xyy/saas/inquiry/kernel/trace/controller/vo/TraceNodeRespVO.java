package com.xyy.saas.inquiry.kernel.trace.controller.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 链路追踪响应VO
 */
@Schema(description = "管理后台 - 链路追踪 Response VO")
@Data
public class TraceNodeRespVO {

    @Schema(description = "主键ID", example = "1")
    private String id;

    @Schema(description = "业务单据号", example = "INQ2024010100001")
    private String businessNo;

    @Schema(description = "节点编码", example = "CREATE_INQUIRY")
    private String nodeCode;

    @Schema(description = "节点名称", example = "创建问诊单")
    private String nodeName;

    @Schema(description = "节点说明", example = "创建问诊单并分配医院")
    private String nodeDesc;

    @Schema(description = "开始执行时间")
    private LocalDateTime startTime;

    @Schema(description = "结束时间")
    private LocalDateTime endTime;

    @Schema(description = "执行耗时(毫秒)", example = "100")
    private Long duration;

    @Schema(description = "执行结果", example = "true")
    private Boolean success;

    @Schema(description = "错误信息", example = "参数错误")
    private String errorMsg;

    @Schema(description = "业务数据", example = "{\"inquiryPref\":\"INQ2024010100001\"}")
    private String businessData;

    @Schema(description = "租户ID", example = "1")
    private Long tenantId;

    @Schema(description = "环境标识", example = "dev")
    private String envTag;

    @Schema(description = "创建时间")
    private LocalDateTime createTime;
} 