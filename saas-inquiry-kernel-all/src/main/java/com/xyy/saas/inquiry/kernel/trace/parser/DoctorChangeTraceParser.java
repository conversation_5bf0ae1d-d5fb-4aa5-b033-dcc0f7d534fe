package com.xyy.saas.inquiry.kernel.trace.parser;

import com.alibaba.fastjson2.JSONObject;
import com.xyy.saas.inquiry.trace.parser.TraceDataParser;
import org.springframework.stereotype.Component;
import java.lang.reflect.Method;

/**
 * @Author: xucao
 * @DateTime: 2025/6/19 20:06
 * @Description: 医生相关 trace解析器
 **/
@Component
public class DoctorChangeTraceParser extends TraceDataParser {

    /**
     * 解析业务单据号
     *
     * @param method       被执行的方法
     * @param args         方法参数
     * @param result       方法返回值
     * @param prefLocation
     * @return 业务单据号
     */
    @Override
    public String parseBusinessNo(Method method, Object[] args, Object result, String prefLocation) {
        return "";
    }

    /**
     * 解析业务数据
     *
     * @param method 被执行的方法
     * @param args   方法参数
     * @param result
     * @return 业务数据，可以是任意对象，会被转换为JSON字符串存储
     */
    @Override
    public Object parseBusinessData(Method method, Object[] args, Object result) {
        Object obj = super.defaultParseBusinessData(method, args, result);
        JSONObject jsonObject = JSONObject.from(obj);
        jsonObject.put("method",method.getName());
        return jsonObject;
    }
}
