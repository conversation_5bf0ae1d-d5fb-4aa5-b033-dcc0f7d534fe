package com.xyy.saas.inquiry.kernel.trace.service;

import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.bulk.BulkRequest;
import org.elasticsearch.action.bulk.BulkResponse;
import org.elasticsearch.action.delete.DeleteRequest;
import org.elasticsearch.action.search.SearchRequest;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.client.RestHighLevelClient;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.concurrent.TimeUnit;

/**
 * 链路追踪数据清理服务
 */
@Service
@Slf4j
public class TraceNodeCleanupService {

    private static final String INDEX_NAME = "inquiry_trace_node";
    private static final String TYPE_NAME = "doc";
    private static final String CLEANUP_LOCK_KEY = "trace:cleanup:lock";
    private static final int MAX_BATCH_COUNT = 2000; // 最大批次数，避免无限循环
    
    @Autowired
    private RestHighLevelClient restHighLevelClient;
    
    @Autowired
    private StringRedisTemplate redisTemplate;
    
    /**
     * 数据保留天数，默认2天
     */
    @Value("${trace.data.retention.days:2}")
    private int retentionDays;
    
    /**
     * 每批删除的文档数量，避免一次性删除过多数据
     */
    @Value("${trace.cleanup.batch.size:1000}")
    private int batchSize;
    
    /**
     * 清理任务最大执行小时数，默认4小时（2点开始，6点结束）
     */
    @Value("${trace.cleanup.max.hours:4}")
    private int maxExecutionHours;
    
    /**
     * ES版本号，用于兼容性处理
     */
    @Value("${elasticsearch.version:7.17.3}")
    private String esVersion;
    
    /**
     * 是否是ES 7.x版本
     */
    private boolean isES7() {
        return esVersion.startsWith("7.");
    }

    /**
     * 定时清理过期数据
     * 每天凌晨2点执行
     */
    @Scheduled(cron = "0 0 2 * * ?")
    public void cleanupExpiredData() {
        // 使用Redis分布式锁，避免集群中多个实例同时执行
        String lockValue = String.valueOf(System.currentTimeMillis());
        boolean lockAcquired = false;
        
        try {
            // 尝试获取分布式锁，锁定时间比最大执行时间稍长
            lockAcquired = redisTemplate.opsForValue().setIfAbsent(
                CLEANUP_LOCK_KEY, lockValue, maxExecutionHours + 1, TimeUnit.HOURS);
            
            if (!lockAcquired) {
                log.info("其他实例正在执行清理任务，跳过此次执行");
                return;
            }
            
            // 计算任务截止时间
            long startTime = System.currentTimeMillis();
            long deadlineTime = startTime + TimeUnit.HOURS.toMillis(maxExecutionHours);
            LocalDateTime deadlineDateTime = LocalDateTime.ofInstant(
                java.time.Instant.ofEpochMilli(deadlineTime), 
                ZoneId.systemDefault()
            );
            
            log.info("开始执行链路追踪数据清理任务，预计{}小时内完成，截止时间: {}", 
                maxExecutionHours, deadlineDateTime);
            
            // 计算过期时间点
            LocalDateTime expireTime = LocalDateTime.now().minusDays(retentionDays);
            long expireTimestamp = expireTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            
            log.info("清理{}天前的数据，过期时间点: {}", retentionDays, expireTime);
            
            // 分批删除过期数据，传入截止时间
            int totalDeleted = deleteExpiredDataInBatches(expireTimestamp, deadlineTime);
            
            long actualDuration = System.currentTimeMillis() - startTime;
            log.info("链路追踪数据清理完成，共删除{}条记录，实际执行时间: {}分钟", 
                totalDeleted, TimeUnit.MILLISECONDS.toMinutes(actualDuration));
            
        } catch (Exception e) {
            log.error("链路追踪数据清理失败", e);
        } finally {
            // 释放分布式锁
            if (lockAcquired) {
                try {
                    String currentValue = redisTemplate.opsForValue().get(CLEANUP_LOCK_KEY);
                    if (lockValue.equals(currentValue)) {
                        redisTemplate.delete(CLEANUP_LOCK_KEY);
                        log.info("释放清理任务分布式锁");
                    }
                } catch (Exception e) {
                    log.warn("释放分布式锁失败", e);
                }
            }
        }
    }

    /**
     * 分批删除过期数据 - 使用for循环替代while循环，避免死循环风险
     * 
     * @param expireTimestamp 过期时间戳
     * @param deadlineTime 任务截止时间戳
     * @return 删除的总记录数
     */
    private int deleteExpiredDataInBatches(long expireTimestamp, long deadlineTime) throws Exception {
        int totalDeleted = 0;
        int batchCount = 0;
        int consecutiveFailures = 0; // 连续失败次数
        final int MAX_CONSECUTIVE_FAILURES = 3; // 最大连续失败次数
        boolean isTimeoutStop = false; // 是否因超时停止
        
        // 使用for循环，设置最大批次数，避免死循环
        for (int i = 0; i < MAX_BATCH_COUNT; i++) {
            batchCount++;
            
            // 检查是否达到截止时间（手动清理时deadlineTime为0，跳过时间检查）
            if (deadlineTime > 0 && System.currentTimeMillis() >= deadlineTime) {
                log.info("达到任务截止时间，已执行{}批次，停止清理任务", batchCount - 1);
                isTimeoutStop = true;
                break;
            }
            
            try {
                // 查询一批过期数据
                SearchResponse searchResponse = queryExpiredData(expireTimestamp, batchSize);
                SearchHit[] hits = searchResponse.getHits().getHits();
                
                // 如果没有更多数据，结束循环
                if (hits.length == 0) {
                    log.info("没有更多过期数据需要清理，结束批次处理");
                    break;
                }
                
                // 批量删除
                int deletedInBatch = bulkDeleteHits(hits);
                totalDeleted += deletedInBatch;
                consecutiveFailures = 0; // 重置连续失败计数
                
                log.info("第{}批次删除{}条记录，累计删除{}条记录", batchCount, deletedInBatch, totalDeleted);
                
            } catch (Exception e) {
                consecutiveFailures++;
                log.error("第{}批次删除失败，连续失败{}次: {}", batchCount, consecutiveFailures, e.getMessage(), e);
                
                // 如果连续失败次数过多，停止清理任务
                if (consecutiveFailures >= MAX_CONSECUTIVE_FAILURES) {
                    log.error("连续失败{}次，停止清理任务以避免系统问题", MAX_CONSECUTIVE_FAILURES);
                    break;
                }
                
                // 单次失败时增加休眠时间，避免立即重试
                try {
                    Thread.sleep(1000);
                } catch (InterruptedException ie) {
                    Thread.currentThread().interrupt();
                    log.warn("清理任务被中断");
                    break;
                }
                
                // 继续下一批次处理
                continue;
            }
            
            // 批次间休眠，减少对ES的压力
            try {
                Thread.sleep(200);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
                log.warn("清理任务被中断");
                break;
            }
            
            // 检查是否达到截止时间
            if (deadlineTime > 0 && System.currentTimeMillis() >= deadlineTime) {
                log.info("达到任务截止时间，停止清理任务");
                isTimeoutStop = true;
                break;
            }
        }
        
        // 输出任务结束原因和统计信息
        if (isTimeoutStop) {
            log.warn("清理任务因超时停止，执行了{}批次，删除{}条记录。可能还有数据未清理完成", batchCount, totalDeleted);
        } else if (batchCount >= MAX_BATCH_COUNT) {
            log.warn("达到最大批次数{}，停止清理。可能还有数据未清理完成", MAX_BATCH_COUNT);
        } else if (consecutiveFailures > 0 && consecutiveFailures < MAX_CONSECUTIVE_FAILURES) {
            log.warn("清理过程中发生{}次失败，但任务已完成", consecutiveFailures);
        }
        
        return totalDeleted;
    }

    /**
     * 查询过期数据
     */
    private SearchResponse queryExpiredData(long expireTimestamp, int size) throws Exception {
        try {
            RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("createTime").lt(expireTimestamp);
            
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .query(rangeQuery)
                .size(size)
                .fetchSource(false); // 只需要ID，不需要完整源数据
            
            SearchRequest searchRequest = new SearchRequest(INDEX_NAME);
            searchRequest.source(searchSourceBuilder);
            
            // ES 6.x需要设置type
            if (!isES7()) {
                searchRequest.types(TYPE_NAME);
            }
            
            return restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);
            
        } catch (Exception e) {
            log.error("查询过期数据失败", e);
            throw e;
        }
    }

    /**
     * 批量删除文档
     */
    private int bulkDeleteHits(SearchHit[] hits) throws Exception {
        try {
            BulkRequest bulkRequest = new BulkRequest();
            
            for (SearchHit hit : hits) {
                DeleteRequest deleteRequest;
                if (isES7()) {
                    // ES 7.x 构造函数
                    deleteRequest = new DeleteRequest(INDEX_NAME, hit.getId());
                } else {
                    // ES 6.x 构造函数需要type参数
                    deleteRequest = new DeleteRequest(INDEX_NAME, TYPE_NAME, hit.getId());
                }
                bulkRequest.add(deleteRequest);
            }
            
            if (bulkRequest.numberOfActions() == 0) {
                return 0;
            }
            
            BulkResponse bulkResponse = restHighLevelClient.bulk(bulkRequest, RequestOptions.DEFAULT);
            
            if (bulkResponse.hasFailures()) {
                log.warn("批量删除部分失败: {}", bulkResponse.buildFailureMessage());
                // 计算成功删除的数量 - 使用传统方式替代Stream API
                int successCount = 0;
                for (org.elasticsearch.action.bulk.BulkItemResponse item : bulkResponse.getItems()) {
                    if (!item.isFailed()) {
                        successCount++;
                    }
                }
                
                // 部分失败不抛异常，只记录警告
                if (successCount == 0) {
                    throw new Exception("批量删除全部失败");
                }
                
                return successCount;
            }
            
            return bulkRequest.numberOfActions();
            
        } catch (Exception e) {
            log.error("批量删除文档失败", e);
            throw e;
        }
    }

    /**
     * 手动触发数据清理（用于测试或紧急清理）
     * 
     * @param days 清理多少天前的数据
     * @return 清理的记录数
     */
    public int manualCleanup(int days) {
        return manualCleanup(days, 0); // 不设置超时时间
    }

    /**
     * 手动触发数据清理（用于测试或紧急清理）
     * 
     * @param days 清理多少天前的数据
     * @param timeoutHours 超时时间（小时），0表示不限制
     * @return 清理的记录数
     */
    public int manualCleanup(int days, int timeoutHours) {
        log.info("手动触发链路追踪数据清理，清理{}天前的数据{}", days, 
            timeoutHours > 0 ? "，超时时间: " + timeoutHours + "小时" : "");
        
        try {
            long startTime = System.currentTimeMillis();
            long deadlineTime = timeoutHours > 0 ? 
                startTime + TimeUnit.HOURS.toMillis(timeoutHours) : 0;
            
            LocalDateTime expireTime = LocalDateTime.now().minusDays(days);
            long expireTimestamp = expireTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            
            int totalDeleted = deleteExpiredDataInBatches(expireTimestamp, deadlineTime);
            
            long actualDuration = System.currentTimeMillis() - startTime;
            log.info("手动清理完成，共删除{}条记录，实际执行时间: {}分钟", 
                totalDeleted, TimeUnit.MILLISECONDS.toMinutes(actualDuration));
            return totalDeleted;
            
        } catch (Exception e) {
            log.error("手动清理失败", e);
            return 0;
        }
    }

    /**
     * 获取过期数据统计信息
     * 
     * @return 过期数据条数
     */
    public long getExpiredDataCount() {
        try {
            LocalDateTime expireTime = LocalDateTime.now().minusDays(retentionDays);
            long expireTimestamp = expireTime.atZone(ZoneId.systemDefault()).toInstant().toEpochMilli();
            
            RangeQueryBuilder rangeQuery = QueryBuilders.rangeQuery("createTime").lt(expireTimestamp);
            
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder()
                .query(rangeQuery)
                .size(0); // 只获取总数，不返回具体文档
            
            SearchRequest searchRequest = new SearchRequest(INDEX_NAME);
            searchRequest.source(searchSourceBuilder);
            
            if (!isES7()) {
                searchRequest.types(TYPE_NAME);
            }
            
            SearchResponse response = restHighLevelClient.search(searchRequest, RequestOptions.DEFAULT);

            return response.getHits().getTotalHits().value;
            
        } catch (Exception e) {
            log.error("获取过期数据统计失败", e);
            return 0;
        }
    }
} 