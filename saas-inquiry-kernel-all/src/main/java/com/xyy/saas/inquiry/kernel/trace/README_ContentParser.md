# 链路追踪内容解析器使用说明

## 概述

链路追踪内容解析器用于在查询链路追踪数据时，根据节点类型自动填充业务数据，为前端提供更丰富的展示信息。

## 架构设计

### 1. 接口定义
```java
public interface TraceContentParser {
    TraceNodeData parserContent(TraceNodeData traceNodeData);
}
```

### 2. 实现方式
采用Function函数式编程方式，仿照`InquiryCancelHandle`类的设计模式：
- 使用Map存储节点编码与解析函数的映射关系
- 通过`@PostConstruct`初始化映射关系
- 支持灵活的业务逻辑扩展

## 使用方法

### 1. 配置枚举
在`TraceNodeEnum`中为需要内容解析的节点配置`contentParser`参数：

```java
// 四个参数的构造函数：code, name, parser, contentParser
CREATE_INQUIRY("CREATE_INQUIRY", "用户请求发起问诊", 
    "com.xyy.saas.inquiry.kernel.trace.parser.InquiryCreateTraceParser", 
    "com.xyy.saas.inquiry.kernel.trace.parser.InquiryTraceContentParser"),
```

### 2. 创建解析器实现
```java
@Component
@Slf4j
public class CustomTraceContentParser implements TraceContentParser {
    
    private final Map<String, Function<TraceNodeData, TraceNodeData>> contentParserMap = new HashMap<>();

    @PostConstruct
    public void init() {
        // 初始化解析函数映射
        contentParserMap.put("NODE_CODE", this::parseSpecificNode);
    }

    @Override
    public TraceNodeData parserContent(TraceNodeData traceNodeData) {
        Function<TraceNodeData, TraceNodeData> parserFunction = 
            contentParserMap.get(traceNodeData.getNodeCode());
        if (parserFunction != null) {
            return parserFunction.apply(traceNodeData);
        }
        return traceNodeData;
    }

    private TraceNodeData parseSpecificNode(TraceNodeData traceNodeData) {
        // 具体的业务数据填充逻辑
        Map<String, Object> businessInfo = new HashMap<>();
        businessInfo.put("businessNo", traceNodeData.getBusinessNo());
        // ... 填充其他业务数据
        
        traceNodeData.setBusinessData(JSON.toJSONString(businessInfo));
        return traceNodeData;
    }
}
```

### 3. 解析器获取机制
系统会按照以下优先级获取解析器：
1. 如果配置了`contentParser`类路径，优先从Spring容器中获取对应Bean
2. 如果容器中不存在，则通过反射创建新实例
3. 如果创建失败，则使用默认解析器`DefaultTraceContentParser`

## 扩展指南

### 1. 创建业务专用解析器
为不同业务模块创建专用的解析器，如：
- `InquiryTraceContentParser` - 问诊业务解析器
- `PrescriptionTraceContentParser` - 处方业务解析器
- `PaymentTraceContentParser` - 支付业务解析器

### 2. 业务数据填充
在解析器中可以：
- 调用其他服务获取关联数据
- 查询数据库补充详细信息
- 计算业务指标和统计数据
- 格式化展示内容

### 3. 异常处理
- 解析器异常不会影响查询主流程
- 解析失败时返回原始数据
- 记录详细的错误日志便于排查

## 示例场景

### 问诊创建节点解析
```java
private TraceNodeData parseCreateInquiry(TraceNodeData traceNodeData) {
    // 查询问诊详情
    InquiryRecordDto inquiry = inquiryService.getInquiryByPref(traceNodeData.getBusinessNo());
    
    Map<String, Object> businessInfo = new HashMap<>();
    if (inquiry != null) {
        businessInfo.put("patientName", inquiry.getPatientName());
        businessInfo.put("productName", inquiry.getProductName());
        businessInfo.put("inquiryType", inquiry.getInquiryType());
        businessInfo.put("hospitalName", inquiry.getHospitalName());
    }
    
    traceNodeData.setBusinessData(JSON.toJSONString(businessInfo));
    return traceNodeData;
}
```

### 处方开具节点解析
```java
private TraceNodeData parsePrescriptionIssue(TraceNodeData traceNodeData) {
    // 查询处方详情
    PrescriptionDto prescription = prescriptionService.getByInquiryPref(traceNodeData.getBusinessNo());
    
    Map<String, Object> businessInfo = new HashMap<>();
    if (prescription != null) {
        businessInfo.put("doctorName", prescription.getDoctorName());
        businessInfo.put("medicineCount", prescription.getMedicines().size());
        businessInfo.put("totalAmount", prescription.getTotalAmount());
        businessInfo.put("prescriptionUrl", prescription.getPrescriptionUrl());
    }
    
    traceNodeData.setBusinessData(JSON.toJSONString(businessInfo));
    return traceNodeData;
}
```

## 注意事项

1. **性能考虑**：避免在解析器中执行耗时操作，如果需要大量数据查询，考虑异步处理或缓存机制
2. **异常安全**：确保解析器异常不会影响主业务流程
3. **数据安全**：注意敏感信息的处理，避免泄露用户隐私数据
4. **测试覆盖**：为解析器编写充分的单元测试，确保数据解析的准确性

## 最佳实践

1. 按业务模块划分解析器，避免单个解析器过于庞大
2. 使用统一的业务数据格式，便于前端展示
3. 添加适当的日志记录，便于问题排查
4. 考虑缓存机制，避免重复查询相同数据
5. 定期清理无用的解析逻辑，保持代码简洁 