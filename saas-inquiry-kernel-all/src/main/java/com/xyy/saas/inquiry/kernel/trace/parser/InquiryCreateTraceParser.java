package com.xyy.saas.inquiry.kernel.trace.parser;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.DrugstoreInquiryReqVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryRespVO;
import com.xyy.saas.inquiry.trace.parser.TraceDataParser;
import org.springframework.stereotype.Component;

import java.lang.reflect.Method;

/**
 * 问诊单创建链路追踪解析器
 */
@Component
public class InquiryCreateTraceParser extends TraceDataParser {

    @Override
    public String parseBusinessNo(Method method, Object[] args ,Object result ,String prefLocation) {
        if (result instanceof CommonResult<?>) {
            CommonResult<InquiryRespVO> commonResult = (CommonResult<InquiryRespVO>) result;
            if (commonResult.isSuccess()) {
                return commonResult.getData().getInquiryPref();
            }
        }

        return null;
    }

    @Override
    public Object parseBusinessData(Method method, Object[] args, Object result) {
        if (args == null || args.length == 0) {
            return null;
        }
        
        // 第一个参数是问诊请求VO
        if (args[0] instanceof DrugstoreInquiryReqVO) {
            return args[0];
        }
        
        return null;
    }
} 