<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <property name="log.dir" value="${log.dir:-/data/logs/saas-inquiry-kernel-all}"/>
  <property name="log.name" value="${log.name:-saas-inquiry-kernel-all}"/>

  <property name="PATTERN" value="%date %-5p [%36.36(%32.34logger:%line)] [%X{traceId}] - %m%n"/>
  <!--<property name="PATTERN_LIMIT" value="%date %-5p [%36.36(%32.34logger:%line)] [%X{traceId}] - %replace(%msg){'^([^\\[].{2048}).*$', '$1 ...截断'}%n"/>-->
  <property name="log.pattern" value="${PATTERN}"/>
  <property name="log.pattern2" value="${PATTERN}"/>

  <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">
    <encoder>
      <pattern>${log.pattern2}</pattern>
    </encoder>
  </appender>

  <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>${log.dir}/${log.name}.log</file>
    <encoder>
      <pattern>${log.pattern}</pattern>
    </encoder>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <fileNamePattern>${log.dir}/%d{yyyy-MM-dd}/${log.name}.%d{yyyy-MM-dd}.log</fileNamePattern>
      <maxHistory>30</maxHistory>
    </rollingPolicy>
  </appender>

  <appender name="ERROR_FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <filter class="ch.qos.logback.classic.filter.ThresholdFilter">
      <level>ERROR</level>
    </filter>
    <file>${log.dir}/${log.name}-error.log</file>
    <encoder>
      <pattern>${log.pattern}</pattern>
    </encoder>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
      <fileNamePattern>${log.dir}/%d{yyyy-MM-dd}/${log.name}.%d{yyyy-MM-dd}-error.log</fileNamePattern>
      <maxHistory>30</maxHistory>
    </rollingPolicy>
  </appender>

  <root level="INFO">
    <appender-ref ref="CONSOLE"/>
    <appender-ref ref="FILE"/>
    <appender-ref ref="ERROR_FILE"/>
  </root>
</configuration>