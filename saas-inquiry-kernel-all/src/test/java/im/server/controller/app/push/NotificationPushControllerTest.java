package im.server.controller.app.push;

import com.xyy.saas.inquiry.im.server.controller.app.push.NotificationPushController;
import com.xyy.saas.inquiry.im.server.service.push.NotificationPushService;
import com.xyy.saas.inquiry.im.api.message.dto.NotificationPushDto;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.autoconfigure.web.servlet.WebMvcTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;

import jakarta.annotation.Resource;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.jsonPath;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

/**
 * {@link NotificationPushController} 的集成测试
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@WebMvcTest(NotificationPushController.class)
public class NotificationPushControllerTest {

    @Resource
    private MockMvc mockMvc;

    @MockBean
    private NotificationPushService aliEmasPushService;

    @Test
    @DisplayName("测试发送通知到手机 - 成功场景")
    public void testSendNoticeToMobile_Success() throws Exception {
        // 准备测试数据
        String requestBody = """
            {
              "userIdList": [1001, 1002],
              "title": "测试标题",
              "body": "测试内容",
              "ext": "{\\"key\\":\\"value\\"}"
            }""";

        // 模拟服务方法
        doNothing().when(aliEmasPushService).sendNoticeToMobile(any(NotificationPushDto.class));

        // 执行测试请求
        mockMvc.perform(MockMvcRequestBuilders.post("/im/ali-emas-push/send-notice")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isOk())
                .andExpect(jsonPath("$.code").value(0))
                .andExpect(jsonPath("$.data").value(true));

        // 验证服务方法被调用
        verify(aliEmasPushService).sendNoticeToMobile(any(NotificationPushDto.class));
    }

    @Test
    @DisplayName("测试发送通知到手机 - 请求参数验证失败")
    public void testSendNoticeToMobile_ValidationFailed() throws Exception {
        // 准备测试数据 - 缺少必要字段
        String requestBody = """
            {
              "userIdList": [],
              "title": "",
              "body": ""
            }""";

        // 执行测试请求
        mockMvc.perform(MockMvcRequestBuilders.post("/im/ali-emas-push/send-notice")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(requestBody))
                .andExpect(status().isBadRequest());
    }
}