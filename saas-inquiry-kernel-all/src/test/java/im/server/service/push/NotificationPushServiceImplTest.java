package im.server.service.push;

import com.xyy.saas.inquiry.im.server.service.push.NotificationPushServiceImpl;
import com.xyy.saas.inquiry.im.api.message.dto.NotificationPushDto;
import com.xyy.saas.inquiry.im.api.message.dto.NotificationPushDto.Action;
import com.xyy.saas.inquiry.im.api.message.dto.NotificationPushDto.NotificationPushExtDto;
import com.xyy.saas.inquiry.im.api.message.dto.NotificationPushDto.OpenType;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * {@link NotificationPushServiceImpl} 的单元测试
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@ExtendWith(MockitoExtension.class)
public class NotificationPushServiceImplTest {

    @InjectMocks
    private NotificationPushServiceImpl aliEmasPushService;

    @Mock
    private com.aliyun.push20160801.Client pushClient;

    @BeforeEach
    public void setUp() {
        // 可以在这里设置一些通用的测试前置条件
    }

    @Test
    @DisplayName("测试发送通知到手机 - 成功场景")
    public void testSendNoticeToMobile_Success() throws Exception {
        // 准备测试数据
        NotificationPushDto pushReq = NotificationPushDto.builder()
                .userIdList(Arrays.asList(1001L, 1002L))
                .title("测试标题")
                .body("测试内容")
                .ext(new NotificationPushExtDto().setAction(Action.builder()
                    .openType(OpenType.H5)
                    .pageUrl("/notification")
                    .pageParams(Map.of("inquiryPref", "123456"))
                    .build()))
                .build();

        // 模拟依赖方法的行为
        when(pushClient.push(any())).thenReturn(null); // 假设成功返回

        // 执行测试方法
        aliEmasPushService.sendNoticeToMobile(pushReq);

        // 验证方法调用
        verify(pushClient, times(1)).push(any());
    }

    @Test
    @DisplayName("测试发送通知到手机 - 空用户列表")
    public void testSendNoticeToMobile_EmptyUserList() throws Exception {
        // 准备测试数据
        NotificationPushDto pushReq = NotificationPushDto.builder()
                .userIdList(Collections.emptyList())
                .title("测试标题")
                .body("测试内容")
                .build();

        // 执行测试方法
        aliEmasPushService.sendNoticeToMobile(pushReq);

        // 验证方法调用 - 应该不会调用推送客户端
        verify(pushClient, never()).push(any());
    }

    @Test
    @DisplayName("测试发送通知到手机 - 异常处理")
    public void testSendNoticeToMobile_Exception() throws Exception {
        // 准备测试数据
        NotificationPushDto pushReq = NotificationPushDto.builder()
                .userIdList(List.of(1001L))
                .title("测试标题")
                .body("测试内容")
                .build();

        // 模拟依赖方法抛出异常
        when(pushClient.push(any())).thenThrow(new RuntimeException("测试异常"));

        // 执行测试方法 - 应该捕获异常并记录日志
        aliEmasPushService.sendNoticeToMobile(pushReq);

        // 验证方法调用
        verify(pushClient, times(1)).push(any());
    }
}