// package com.xyy.saas.inquiry.kernel.signature.pdf;
//
// import cn.hutool.core.io.FileApiUtil;
// import cn.iocoder.yudao.framework.common.pojo.CommonResult;
// import com.alibaba.fastjson.JSON;
// import com.xyy.saas.inquiry.kernel.SaasInquiryKernelAllApplication;
// import com.xyy.saas.inquiry.signature.server.service.pdf.PdfboxServiceImpl;
// import jakarta.annotation.Resource;
// import java.io.File;
// import java.io.IOException;
// import java.util.HashMap;
// import java.util.List;
// import java.util.Map;
// import org.junit.jupiter.api.Test;
// import org.springframework.boot.test.context.SpringBootTest;
// import org.springframework.test.context.ActiveProfiles;
// import org.springframework.util.AntPathMatcher;
//
// /**
//  * @Author:chenxiaoyi
//  * @Date:2024/12/12 10:36
//  */
// @SpringBootTest(classes = SaasInquiryKernelAllApplication.class)
// @ActiveProfiles("dev")
// public class PdfTest {
//
//
//     @Resource
//     private PdfboxServiceImpl pdfboxService;
//
//
//     @Test
//     public void prescriptionSignaturePassing_success() throws IOException {
//         String map = "{\n"
//             + "          \"date\": \"2025-01-10 14:10:45\",\n"
//             + "          \"no\": \"HYWZ100018\",\n"
//             + "          \"drugs\": \"人参   1g\\t\\t\\t白人参   1g\\t\\t\\t人参/吉林   1g\\t\\t\\t人参(吉林)   1g\\t\\t\\t人参(生晒参)   1g\\t\\t\\t人参(正山庄林下山参)   1g\\t\\t\\t人参超微饮片/吉林   1g\\t\\t\\t人参粉   1g\\t\\t\\t人参粉/本溪   1g\\t\\t\\t人参花   1g\\t\\t\\t人参极细粉   1g\\t\\t\\t人参片   1g\\t\\t\\t人参须   1g\\t\\t\\t人参叶   1g\\t\\t\\t人参饮片   1g\\t\\t\\t鲜人参   1g\\t\\t\\t鲜人参(农副品)   1g\\t\\t\\t鲜人参(优选级)   1g\\t\\t\\t鲜人参家庭欢享装   1g\\t\\t\\t鲜人参特选级   1g\\t\\t\\t新鲜人参   1g\",\n"
//             + "          \"idCard\": \"******************\",\n"
//             + "          \"inquiryProductDto\": \"{\\\"tcmProcessingMethod\\\":\\\"打粉冲服\\\",\\\"tcmDirections\\\":\\\"温服\\\",\\\"tcmUsage\\\":\\\"2\\\",\\\"tcmTotalDosage\\\":\\\"10\\\",\\\"tcmDailyDosage\\\":\\\"3\\\",\\\"inquiryProductInfos\\\":[{\\\"commonName\\\":\\\"人参\\\",\\\"quantity\\\":1,\\\"unitName\\\":\\\"袋\\\",\\\"attributeSpecification\\\":\\\"0.25KG 全须\\\",\\\"productName\\\":\\\"人参\\\",\\\"manufacturer\\\":\\\"湖南聚仁中药饮片有限公司\\\"},{\\\"commonName\\\":\\\"白人参\\\",\\\"quantity\\\":1,\\\"unitName\\\":\\\"g\\\",\\\"attributeSpecification\\\":\\\"统\\\",\\\"productName\\\":\\\"白人参\\\",\\\"manufacturer\\\":\\\"吉林长白山药业集团股份有限公司\\\"},{\\\"commonName\\\":\\\"人参/吉林\\\",\\\"quantity\\\":1,\\\"unitName\\\":\\\"袋\\\",\\\"attributeSpecification\\\":\\\"250G(全须*大支)\\\",\\\"productName\\\":\\\"人参\\\",\\\"manufacturer\\\":\\\"河北楚风中药饮片有限公司\\\"},{\\\"commonName\\\":\\\"人参(吉林)\\\",\\\"quantity\\\":1,\\\"unitName\\\":\\\"袋\\\",\\\"attributeSpecification\\\":\\\"500G(二级)\\\",\\\"productName\\\":\\\"人参\\\",\\\"manufacturer\\\":\\\"四川意和药业有限公司\\\"},{\\\"commonName\\\":\\\"人参(生晒参)\\\",\\\"quantity\\\":1,\\\"unitName\\\":\\\"瓶\\\",\\\"attributeSpecification\\\":\\\"250G\\\",\\\"productName\\\":\\\"人参(生晒参)\\\",\\\"manufacturer\\\":\\\"广州君济堂药业有限公司\\\"},{\\\"commonName\\\":\\\"人参(正山庄林下山参)\\\",\\\"quantity\\\":1,\\\"unitName\\\":\\\"盒\\\",\\\"attributeSpecification\\\":\\\"50G(8-10支)\\\",\\\"productName\\\":\\\"人参(正山庄林下山参)\\\",\\\"manufacturer\\\":\\\"广东正韩药业股份有限公司\\\"},{\\\"commonName\\\":\\\"人参超微饮片/吉林\\\",\\\"quantity\\\":1,\\\"unitName\\\":\\\"盒\\\",\\\"attributeSpecification\\\":\\\"1.0G*15袋\\\",\\\"productName\\\":\\\"人参超微饮片\\\",\\\"manufacturer\\\":\\\"湖南春光九汇现代中药有限公司\\\"},{\\\"commonName\\\":\\\"人参粉\\\",\\\"quantity\\\":1,\\\"unitName\\\":\\\"盒\\\",\\\"attributeSpecification\\\":\\\"1G*10袋\\\",\\\"productName\\\":\\\"人参粉\\\",\\\"manufacturer\\\":\\\"通化德济参药业有限公司\\\"},{\\\"commonName\\\":\\\"人参粉/本溪\\\",\\\"quantity\\\":1,\\\"unitName\\\":\\\"瓶\\\",\\\"attributeSpecification\\\":\\\"50G/瓶\\\",\\\"productName\\\":\\\"人参粉\\\",\\\"manufacturer\\\":\\\"龙宝参茸股份有限公司\\\"},{\\\"commonName\\\":\\\"人参花\\\",\\\"quantity\\\":1,\\\"unitName\\\":\\\"包\\\",\\\"attributeSpecification\\\":\\\"0.25kg\\\",\\\"productName\\\":\\\"人参花\\\",\\\"manufacturer\\\":\\\"上海药房股份有限公司\\\"},{\\\"commonName\\\":\\\"人参极细粉\\\",\\\"quantity\\\":1,\\\"unitName\\\":\\\"盒\\\",\\\"attributeSpecification\\\":\\\"3G*30袋\\\",\\\"productName\\\":\\\"人参极细粉\\\",\\\"manufacturer\\\":\\\"湖北一正药业有限公司\\\"},{\\\"commonName\\\":\\\"人参片\\\",\\\"quantity\\\":1,\\\"unitName\\\":\\\"袋\\\",\\\"attributeSpecification\\\":\\\"10G/袋\\\",\\\"productName\\\":\\\"人参片\\\",\\\"manufacturer\\\":\\\"龙宝参茸股份有限公司\\\"},{\\\"commonName\\\":\\\"人参须\\\",\\\"quantity\\\":1,\\\"unitName\\\":\\\"包\\\",\\\"attributeSpecification\\\":\\\"白直须 0.25kg\\\",\\\"productName\\\":\\\"人参须\\\",\\\"manufacturer\\\":\\\"湖南衡岳中药饮片有限公司\\\"},{\\\"commonName\\\":\\\"人参叶\\\",\\\"quantity\\\":1,\\\"unitName\\\":\\\"包\\\",\\\"attributeSpecification\\\":\\\"0.25KG\\\",\\\"productName\\\":\\\"人参叶\\\",\\\"manufacturer\\\":\\\"湖南聚仁中药饮片有限公司\\\"},{\\\"commonName\\\":\\\"人参饮片\\\",\\\"quantity\\\":1,\\\"unitName\\\":\\\"罐\\\",\\\"attributeSpecification\\\":\\\"1G*20袋\\\",\\\"productName\\\":\\\"人参饮片(破壁)\\\",\\\"manufacturer\\\":\\\"贵州联盛药业有限公司\\\"},{\\\"commonName\\\":\\\"鲜人参\\\",\\\"quantity\\\":1,\\\"unitName\\\":\\\"盒\\\",\\\"attributeSpecification\\\":\\\"普选级\\\\\\\\37G\\\",\\\"productName\\\":\\\"鲜人参\\\",\\\"manufacturer\\\":\\\"吉林省百济堂参业有限公司\\\"},{\\\"commonName\\\":\\\"鲜人参(农副品)\\\",\\\"quantity\\\":1,\\\"unitName\\\":\\\"袋\\\",\\\"attributeSpecification\\\":\\\"1支\\\",\\\"productName\\\":\\\"鲜人参\\\",\\\"manufacturer\\\":\\\"吉林省康福药业有限公司\\\"},{\\\"commonName\\\":\\\"鲜人参(优选级)\\\",\\\"quantity\\\":1,\\\"unitName\\\":\\\"盒\\\",\\\"attributeSpecification\\\":\\\"约50G/支 \\\",\\\"productName\\\":\\\"鲜人参(优选级)\\\",\\\"manufacturer\\\":\\\"康美药业股份有限公司\\\"},{\\\"commonName\\\":\\\"鲜人参家庭欢享装\\\",\\\"quantity\\\":1,\\\"unitName\\\":\\\"盒\\\",\\\"attributeSpecification\\\":\\\"200克\\\",\\\"productName\\\":\\\"鲜人参家庭欢享装\\\",\\\"manufacturer\\\":\\\"康美药业股份有限公司\\\"},{\\\"commonName\\\":\\\"鲜人参特选级\\\",\\\"quantity\\\":1,\\\"unitName\\\":\\\"盒\\\",\\\"attributeSpecification\\\":\\\"约100G/根/盒 \\\",\\\"productName\\\":\\\"鲜人参特选级\\\",\\\"manufacturer\\\":\\\"康美药业股份有限公司\\\"},{\\\"commonName\\\":\\\"新鲜人参\\\",\\\"quantity\\\":1,\\\"unitName\\\":\\\"g\\\",\\\"attributeSpecification\\\":\\\"统\\\",\\\"productName\\\":\\\"新鲜人参\\\",\\\"manufacturer\\\":\\\"吉林长白山药业集团股份有限公司\\\"}]}\",\n"
//             + "          \"sex\": \"男\",\n"
//             + "          \"mobile\": \"13435637494\",\n"
//             + "          \"diagnosis\": \"急性上呼吸道感染1 CA07.0\",\n"
//             + "          \"dept\": \"全科\",\n"
//             + "          \"instruction\": \"剂数：共 10 剂  每日 3 剂   每剂分 温服 次用药  加工方式：打粉冲服   用法：2\",\n"
//             + "          \"name\": \"王中药\",\n"
//             + "          \"tenantId\": \"1\",\n"
//             + "          \"warning\": \"注：该处方为复诊处方，沿用原治疗方案\",\n"
//             + "          \"medicineType\": \"1\",\n"
//             + "          \"age\": \"29\",\n"
//             + "          \"remarks\": \"本处方仅限运营后台使用(非本门店无效) 本处方3日内有效\"\n"
//             + "        }";
//
//         PdfboxServiceImpl pdfboxService = new PdfboxServiceImpl();
//         CommonResult commonResult = pdfboxService.generateAndUpload("http://files.test.ybm100.com/INVT/Lzinq/20250110/pdf_temp_11448149624590061587.pdf", JSON.parseObject(map, Map.class));
//         System.out.println(commonResult);
//     }
//
//     @Test
//     public void zy() throws IOException {
//
//         List<String> list = List.of("人参   1g", "人参xxxxxxx   10g", "人参aaaaaaaaaaaaaaaxxxxxxxxxxxxxa   1g"
//             , "人参qweqwxxxxxxxxxxxxxxxxxxxxxeqweqweqw   1g", "x   10g", "aaaaaaa   1g"
//             , "x   1g", "人参qweqweqweqweqwxxxxxxxxxxxxxxxxxxxxxxx   10g", "a   1g");
//
//         Map<String, String> hashMap = new HashMap<>() {{
//             put("drugs", JSON.toJSONString(list));
//             put("medicineType", "1");
//         }};
//
//         PdfboxServiceImpl pdfboxService = new PdfboxServiceImpl();
//         File destFile = FileApiUtil.createTempFile("pdf_", ".pdf", true);
//         pdfboxService.generate("http://files.test.ybm100.com/INVT/Lzinq/20250110/pdf_temp_11448149624590061587.pdf", hashMap, destFile);
//     }
//
//
//     public static void main(String[] args) {
//         AntPathMatcher matcher = new AntPathMatcher();
//         System.out.println(matcher.match("/**/system/tenant/get-list-by-user", "/app-api/system/tenant/get-list-by-user"));
//
//     }
// }
