// package com.xyy.saas.inquiry.kernel.hospital.doctor;
//
// import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
// import com.xyy.saas.inquiry.hospital.server.dal.dataobject.doctor.InquiryDoctorDO;
// import com.xyy.saas.inquiry.hospital.server.dal.redis.doctor.DoctorRedisDao;
// import com.xyy.saas.inquiry.hospital.server.mq.consumer.doctor.DoctorAutoInquirySwitchEventConsumer;
// import com.xyy.saas.inquiry.hospital.server.mq.message.doctor.DoctorAutoInquirySwitchEvent;
// import com.xyy.saas.inquiry.hospital.server.service.doctor.InquiryDoctorService;
// import com.xyy.saas.inquiry.kernel.SaasInquiryKernelAllApplication;
// import jakarta.annotation.Resource;
// import org.junit.jupiter.api.BeforeEach;
// import org.junit.jupiter.api.Test;
// import org.springframework.boot.test.context.SpringBootTest;
// import org.springframework.mock.web.MockHttpServletRequest;
// import org.springframework.test.context.ActiveProfiles;
// import org.springframework.web.context.request.RequestAttributes;
// import org.springframework.web.context.request.RequestContextHolder;
// import org.springframework.web.context.request.ServletRequestAttributes;
//
// /**
//  * @Author:chenxiaoyi
//  * @Date:2024/12/19 13:09
//  */
// @SpringBootTest(classes = SaasInquiryKernelAllApplication.class)
// @ActiveProfiles("dev")
// public class DoctorInfoTest {
//
//     @Resource
//     private DoctorAutoInquirySwitchEventConsumer doctorAutoInquirySwitchEventConsumer;
//
//     @Resource
//     private DoctorRedisDao doctorRedisDao;
//
//     @Resource
//     private InquiryDoctorService inquiryDoctorService;
//
//     @BeforeEach
//     public void before() {
//         RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
//         requestAttributes.setAttribute("login_user_id", 0L, RequestAttributes.SCOPE_REQUEST);
//         RequestContextHolder.setRequestAttributes(requestAttributes);
//         TenantContextHolder.setTenantId(1L); // 超管
//     }
//
//     @Test
//     public void doctorAutoInquirySwitchEventConsumer_test() {
//         String pref = "D100016";
//         doctorAutoInquirySwitchEventConsumer.doctorAutoInquirySwitchEvent(DoctorAutoInquirySwitchEvent.builder().msg(pref).build());
//         final InquiryDoctorDO doctor = inquiryDoctorService.getInquiryDoctorByDoctorPref(pref);
//         // doctorRedisDao.doctorStartReception(doctor, DoctorInquiryTypeEnum.MANUAL_INQUIRY.getCode());
//
//     }
//
// }
