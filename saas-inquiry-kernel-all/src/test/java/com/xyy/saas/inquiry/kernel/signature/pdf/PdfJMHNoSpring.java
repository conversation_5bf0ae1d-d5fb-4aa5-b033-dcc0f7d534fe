package com.xyy.saas.inquiry.kernel.signature.pdf;

import cn.hutool.core.io.FileUtil;
import com.itextpdf.text.DocumentException;
import com.xyy.saas.inquiry.signature.server.service.pdf.*;
import lombok.extern.slf4j.Slf4j;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.RunnerException;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@BenchmarkMode(Mode.AverageTime) // 测量平均时间
@OutputTimeUnit(TimeUnit.MILLISECONDS) // 输出单位为毫秒
@State(Scope.Group)
@Warmup(iterations = 5, time = 10, timeUnit = TimeUnit.SECONDS)
@Measurement(iterations = 5, time = 10, timeUnit = TimeUnit.SECONDS)
@Slf4j
public class PdfJMHNoSpring {

    private static PdfService openPdfService;
    private static PdfService itext5PdfService;
    private static PdfService pdfboxService;
    private static MockDataPdfService mockDataPdfService;
    private static PdfServiceFactory pdfServiceFactory;

    private static String rootPath;
    private static final String PDF_TEMP_1_PDF = "/pdf/pdf_temp_1.pdf";
    private static final String PDF_TEMP_3_PDF = "/pdf/pdf_temp_1.pdf";

    // 不能debug
    public static void main(String[] args) throws RunnerException {
        Options opt = new OptionsBuilder()
            .include(PdfJMHNoSpring.class.getName() + ".*")
            .forks(1)
            .build();

        new Runner(opt).run();
    }

    @Setup(Level.Trial)
    public static void setup() {
        openPdfService = new OpenPdfServiceImpl();
        itext5PdfService = new Itext5PdfServiceImpl();
        pdfboxService = new PdfboxServiceImpl();
        mockDataPdfService = new MockDataPdfService();

        Map<String, PdfService> pdfServiceMap = new HashMap<>();
        PdfServiceProperties pdfServiceProperties = new PdfServiceProperties();
        // 不加载字体
        pdfServiceFactory = new PdfServiceFactory(pdfServiceMap, pdfServiceProperties);
        pdfServiceFactory.init();

        rootPath = ClassLoader.getSystemResource("").getPath();
        log.info("当前项目根目录为：{}", rootPath);
    }

    @State(Scope.Benchmark)
    public static class BenchmarkState {

        volatile AtomicInteger i = new AtomicInteger();
    }

    @State(Scope.Thread)
    public static class ThreadState {

        volatile int t = 0;
    }

    // 基准测试方法，测试 openpdf生成 方法的性能
    @Group("pdf")
    @Benchmark
    public void benchmarkOpenPdfGenerate(BenchmarkState state) throws DocumentException, IOException {
        openPdfService.generate(rootPath + PDF_TEMP_1_PDF,
            mockDataPdfService.dataMap(0, null),
            FileUtil.touch(rootPath + "/pdf/temp10/" + state.i.addAndGet(1) + ".pdf"));

        openPdfService.generate(rootPath + PDF_TEMP_1_PDF,
            mockDataPdfService.dataMap(1, null),
            FileUtil.touch(rootPath + "/pdf/temp11/" + state.i.addAndGet(1) + ".pdf"));
    }

    // 基准测试方法，测试 itextpdf生成 方法的性能
    @Group("pdf")
    @Benchmark
    public void benchmarkItext5PdfGenerate(BenchmarkState state) throws DocumentException, IOException {
        itext5PdfService.generate(rootPath + PDF_TEMP_1_PDF,
            mockDataPdfService.dataMap(0, null),
            FileUtil.touch(rootPath + "/pdf/temp20/" + state.i.addAndGet(1) + ".pdf"));

        itext5PdfService.generate(rootPath + PDF_TEMP_1_PDF,
            mockDataPdfService.dataMap(1, null),
            FileUtil.touch(rootPath + "/pdf/temp21/" + state.i.addAndGet(1) + ".pdf"));
    }

    // 基准测试方法，测试 itextpdf生成 方法的性能
    @Group("pdf")
    @Benchmark
    public void benchmarkPdfboxGenerate(BenchmarkState state) throws DocumentException, IOException {
        pdfboxService.generate(rootPath + PDF_TEMP_3_PDF,
            mockDataPdfService.dataMap(0, null),
            FileUtil.touch(rootPath + "/pdf/temp30/" + state.i.addAndGet(1) + ".pdf"));

        pdfboxService.generate(rootPath + PDF_TEMP_3_PDF,
            mockDataPdfService.dataMap(1, null),
            FileUtil.touch(rootPath + "/pdf/temp31/" + state.i.addAndGet(1) + ".pdf"));
    }

    // 清理 Spring 上下文
    @TearDown(Level.Trial)
    public static void tearDown() {
        if (pdfServiceFactory != null) {
            pdfServiceFactory.destroy();
        }
    }
}
