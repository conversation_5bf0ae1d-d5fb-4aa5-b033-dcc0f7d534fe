package com.xyy.saas.inquiry.kernel.signature.pdf;

import cn.hutool.core.io.FileUtil;
import com.itextpdf.text.DocumentException;
import com.xyy.saas.inquiry.kernel.SaasInquiryKernelAllApplication;
import com.xyy.saas.inquiry.signature.server.service.pdf.Itext5PdfServiceImpl;
import com.xyy.saas.inquiry.signature.server.service.pdf.MockDataPdfService;
import com.xyy.saas.inquiry.signature.server.service.pdf.OpenPdfServiceImpl;
import com.xyy.saas.inquiry.signature.server.service.pdf.PdfService;
import lombok.extern.slf4j.Slf4j;
import org.openjdk.jmh.annotations.*;
import org.openjdk.jmh.runner.Runner;
import org.openjdk.jmh.runner.RunnerException;
import org.openjdk.jmh.runner.options.Options;
import org.openjdk.jmh.runner.options.OptionsBuilder;
import org.springframework.boot.SpringApplication;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.test.context.ActiveProfiles;

import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@ActiveProfiles("test")
@BenchmarkMode(Mode.AverageTime) // 测量平均时间
@OutputTimeUnit(TimeUnit.MILLISECONDS) // 输出单位为毫秒
@State(Scope.Group)
@Slf4j
public class PdfJMH {

    private static PdfService openPdfService;
    private static PdfService itext5PdfService;
    private static MockDataPdfService mockDataPdfService;

    // 初始化 Spring 上下文
    private static ConfigurableApplicationContext context;
    private static String rootPath;
    private static final String PDF_TEMP_1_PDF = "/pdf/pdf_temp_1.pdf";

    // 不能debug
    public static void main(String[] args) throws RunnerException {
        Options opt = new OptionsBuilder()
            .include(PdfJMH.class.getName() + ".*")
            .forks(2)
            .build();

        new Runner(opt).run();
    }

    @Setup(Level.Trial)
    public static void setup() {
        // 启动 Spring 上下文
        context = SpringApplication.run(SaasInquiryKernelAllApplication.class);
        openPdfService = context.getBean(OpenPdfServiceImpl.class);
        itext5PdfService = context.getBean(Itext5PdfServiceImpl.class);
        mockDataPdfService = context.getBean(MockDataPdfService.class);
        rootPath = ClassLoader.getSystemResource("").getPath();
        log.info("当前项目根目录为：{}", rootPath);
    }

    @State(Scope.Benchmark)
    public static class BenchmarkState {

        volatile AtomicInteger i = new AtomicInteger();
    }

    @State(Scope.Thread)
    public static class ThreadState {

        volatile int t = 0;
    }

    // 基准测试方法，测试 openpdf生成 方法的性能
    @Group("pdf")
    @Benchmark
    @Warmup(iterations = 5, time = 60, timeUnit = TimeUnit.SECONDS)
    @Measurement(iterations = 5, time = 60, timeUnit = TimeUnit.SECONDS)
    public void benchmarkOpenPdfGenerate(BenchmarkState state) throws DocumentException, IOException {
        openPdfService.generate(rootPath + PDF_TEMP_1_PDF,
            mockDataPdfService.dataMap(0, null),
            FileUtil.touch(rootPath + "/pdf/temp10/" + state.i.addAndGet(1) + ".pdf"));

        openPdfService.generate(rootPath + PDF_TEMP_1_PDF,
            mockDataPdfService.dataMap(1, null),
            FileUtil.touch(rootPath + "/pdf/temp11/" + state.i.addAndGet(1) + ".pdf"));
    }

    // 基准测试方法，测试 itextpdf生成 方法的性能
    @Group("pdf")
    @Benchmark
    @Warmup(iterations = 5, time = 60, timeUnit = TimeUnit.SECONDS)
    @Measurement(iterations = 5, time = 60, timeUnit = TimeUnit.SECONDS)
    public void benchmarkItext5PdfGenerate(BenchmarkState state) throws DocumentException, IOException {
        itext5PdfService.generate(rootPath + PDF_TEMP_1_PDF,
            mockDataPdfService.dataMap(0, null),
            FileUtil.touch(rootPath + "/pdf/temp20/" + state.i.addAndGet(1) + ".pdf"));

        itext5PdfService.generate(rootPath + PDF_TEMP_1_PDF,
            mockDataPdfService.dataMap(1, null),
            FileUtil.touch(rootPath + "/pdf/temp21/" + state.i.addAndGet(1) + ".pdf"));
    }

    // 清理 Spring 上下文
    @TearDown(Level.Trial)
    public static void tearDown() {
        log.error("Spring 上下文清理开始，当前时间：{}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        SpringApplication.exit(context, () -> 0);
        log.error("Spring 上下文清理完毕，当前时间：{}", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
    }
}
