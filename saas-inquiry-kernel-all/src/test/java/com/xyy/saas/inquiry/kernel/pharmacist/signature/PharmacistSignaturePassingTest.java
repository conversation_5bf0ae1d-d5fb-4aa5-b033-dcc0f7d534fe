// package com.xyy.saas.inquiry.kernel.pharmacist.signature;
//
// import cn.hutool.json.JSONUtil;
// import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
// import com.xyy.saas.inquiry.kernel.SaasInquiryKernelAllApplication;
// import com.xyy.saas.inquiry.pharmacist.server.service.signature.InquiryPharmacistSignaturePassingService;
// import com.xyy.saas.inquiry.signature.mq.SignaturePassingMessage;
// import jakarta.annotation.Resource;
// import org.junit.jupiter.api.BeforeEach;
// import org.junit.jupiter.api.Test;
// import org.springframework.boot.test.context.SpringBootTest;
// import org.springframework.mock.web.MockHttpServletRequest;
// import org.springframework.test.context.ActiveProfiles;
// import org.springframework.web.context.request.RequestAttributes;
// import org.springframework.web.context.request.RequestContextHolder;
// import org.springframework.web.context.request.ServletRequestAttributes;
//
// /**
//  * @Author:chenxiaoyi
//  * @Date:2024/12/12 10:36
//  */
// @SpringBootTest(classes = SaasInquiryKernelAllApplication.class)
// @ActiveProfiles("dev")
// public class PharmacistSignaturePassingTest {
//
//
//     @Resource
//     private InquiryPharmacistSignaturePassingService inquiryPharmacistSignaturePassingService;
//
//     @BeforeEach
//     public void before() {
//         RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
//         requestAttributes.setAttribute("login_user_id", 0L, RequestAttributes.SCOPE_REQUEST);
//         RequestContextHolder.setRequestAttributes(requestAttributes);
//         TenantContextHolder.setTenantId(1856621116684963842L); // cxy
//     }
//
//     /**
//      * 医生签章回调
//      */
//     @Test
//     public void prescriptionSignaturePassing_success() {
//         String a = "{\"bizId\":\"HYWZ100003\",\"totalLevel\":4,\"participantItem\":{\"userId\":1867108208615506000,\"name\":\"陈笑医\",\"mobile\":\"15926350017\",\"actorField\":\"doctorSign\",\"actorFieldName\":\"医师\",\"accessPlatform\":0,\"signImgUrl\":\"http://files.test.ybm100.com/INVT/Lzinq/20241211/bb67c36864512d1eb47e1a332a7c7bfef1a8e17f994b8cd3a162e4ea6829ea6f.png\",\"sort\":1,\"signStatus\":1},\"nextActorField\":\"pharmacistSign\",\"imgUrl\":\"http://files.test.ybm100.com/INVT/Lzinq/20241230/d7c3c2149f49da320bfc1fcdb4faeabe51187bde87217cce68c6dea077e289e1.jpg\",\"pdfUrl\":\"http://files.test.ybm100.com/INVT/Lzinq/20241230/pdf_14539562586702945606.pdf\",\"callBackTime\":\"2024-12-30T20:20:23.144309026\"}";
//         SignaturePassingMessage bean = JSONUtil.toBean(a, SignaturePassingMessage.class);
//         inquiryPharmacistSignaturePassingService.prescriptionSignaturePassing(bean);
//     }
// }
