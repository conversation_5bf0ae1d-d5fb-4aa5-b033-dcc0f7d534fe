package com.xyy.saas.localserver.purchase.server.admin.extend.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

/**
 * 管理后台 - 采购-发票信息 Response VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 采购-发票信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PurchaseInvoiceRespVO {

    /** 主键ID */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23651")
    @ExcelProperty("主键ID")
    private Long id;

    /** 采购单/收货单/配送单号 */
    @Schema(description = "采购单/收货单/配送单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("采购单/收货单/配送单号")
    private String billNo;

    /** 发票号 */
    @Schema(description = "发票号")
    @ExcelProperty("发票号")
    private String invoiceNo;

    /** 发票代码 */
    @Schema(description = "发票代码")
    @ExcelProperty("发票代码")
    private String invoiceCode;

    /** 发票类型 1：电子普通发票 2：增值税发票 3：纸质普通发票，用于委托配送 */
    @Schema(description = "发票类型 1：电子普通发票 2：增值税发票 3：纸质普通发票，用于委托配送", example = "1")
    @ExcelProperty("发票类型 1：电子普通发票 2：增值税发票 3：纸质普通发票，用于委托配送")
    private String invoiceType;

    /** 发票是否随货通行 0：不随行 1：随行，用于委托配送 */
    @Schema(description = "发票是否随货通行 0：不随行 1：随行，用于委托配送")
    @ExcelProperty("发票是否随货通行 0：不随行 1：随行，用于委托配送")
    private Boolean accompanyingShipment;

    /** 发票金额 */
    @Schema(description = "发票金额")
    @ExcelProperty("发票金额")
    private BigDecimal invoiceAmount;

    /** 发票文件名称 */
    @Schema(description = "发票文件名称", example = "李四")
    @ExcelProperty("发票文件名称")
    private String invoiceFileName;

    /** 发票文件地址 */
    @Schema(description = "发票文件地址", example = "https://www.iocoder.cn")
    @ExcelProperty("发票文件地址")
    private String invoiceFileUrl;

    /** 实际开(发)票时间 */
    @Schema(description = "实际开(发)票时间")
    @ExcelProperty("实际开(发)票时间")
    private LocalDateTime issuedTime;

    /** 备注 */
    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    /** 创建时间 */
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}