package com.xyy.saas.localserver.purchase.server.admin.extend;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseInvoiceDTO;
import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseInvoicePageReqDTO;
import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseInvoicePageReqVO;
import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseInvoiceRespVO;
import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseInvoiceSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.extend.PurchaseInvoiceConvert;
import com.xyy.saas.localserver.purchase.server.service.extend.PurchaseInvoiceService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 管理后台 - 采购发票信息 Controller
 * 处理采购发票信息的创建、更新、删除、查询等操作
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "管理后台 - 采购发票信息")
@RestController
@RequestMapping("/saas/purchase/invoice")
@Validated
public class PurchaseInvoiceController {

    @Resource
    private PurchaseInvoiceService purchaseInvoiceService;

    /**
     * 创建采购发票信息
     * 处理流程：
     * 1. 对象转换：将VO对象转换为DTO对象
     * 2. 调用服务：执行创建操作
     *
     * @param createReqVO 创建信息
     * @return 创建结果
     */
    @PostMapping("/create")
    @Operation(summary = "创建采购发票信息")
    @PreAuthorize("@ss.hasPermission('saas:purchase:invoice:create')")
    public CommonResult<Long> createPurchaseInvoice(@Valid @RequestBody PurchaseInvoiceSaveReqVO createReqVO) {
        // 1. 对象转换：将VO对象转换为DTO对象
        PurchaseInvoiceDTO createDTO = PurchaseInvoiceConvert.INSTANCE.convert2DTO(createReqVO);
        // 2. 调用服务：执行创建操作
        return success(purchaseInvoiceService.createPurchaseInvoice(createDTO));
    }

    /**
     * 更新采购发票信息
     * 处理流程：
     * 1. 对象转换：将VO对象转换为DTO对象
     * 2. 调用服务：执行更新操作
     *
     * @param updateReqVO 更新信息
     * @return 更新结果
     */
    @PutMapping("/update")
    @Operation(summary = "更新采购发票信息")
    @PreAuthorize("@ss.hasPermission('saas:purchase:invoice:update')")
    public CommonResult<Boolean> updatePurchaseInvoice(@Valid @RequestBody PurchaseInvoiceSaveReqVO updateReqVO) {
        // 1. 对象转换：将VO对象转换为DTO对象
        PurchaseInvoiceDTO updateDTO = PurchaseInvoiceConvert.INSTANCE.convert2DTO(updateReqVO);
        // 2. 调用服务：执行更新操作
        purchaseInvoiceService.updatePurchaseInvoice(updateDTO);
        return success(true);
    }

    /**
     * 删除采购发票信息
     * 处理流程：
     * 1. 调用服务：执行删除操作
     *
     * @param id 编号
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    @Operation(summary = "删除采购发票信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:purchase:invoice:delete')")
    public CommonResult<Boolean> deletePurchaseInvoice(@RequestParam("id") Long id) {
        // 调用服务：执行删除操作
        purchaseInvoiceService.deletePurchaseInvoice(id);
        return success(true);
    }

    /**
     * 获取采购发票信息
     * 处理流程：
     * 1. 调用服务：获取发票信息
     * 2. 对象转换：将DTO对象转换为VO对象
     *
     * @param id 编号
     * @return 发票信息
     */
    @GetMapping("/get")
    @Operation(summary = "获得采购发票信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:purchase:invoice:query')")
    public CommonResult<PurchaseInvoiceRespVO> getPurchaseInvoice(@RequestParam("id") Long id) {
        // 1. 调用服务：获取发票信息
        PurchaseInvoiceDTO invoice = purchaseInvoiceService.getPurchaseInvoice(id);
        // 2. 对象转换：将DTO对象转换为VO对象
        return success(PurchaseInvoiceConvert.INSTANCE.convert2VO(invoice));
    }

    /**
     * 获取采购发票信息分页
     * 处理流程：
     * 1. 对象转换：将VO对象转换为DTO对象
     * 2. 调用服务：获取分页数据
     * 3. 对象转换：将DTO对象转换为VO对象
     *
     * @param pageVO 分页查询参数
     * @return 分页结果
     */
    @GetMapping("/page")
    @Operation(summary = "获得采购发票信息分页")
    @PreAuthorize("@ss.hasPermission('saas:purchase:invoice:query')")
    public CommonResult<PageResult<PurchaseInvoiceRespVO>> getPurchaseInvoicePage(
            @Valid PurchaseInvoicePageReqVO pageVO) {
        // 1. 对象转换：将VO对象转换为DTO对象
        PurchaseInvoicePageReqDTO pageReqDTO = PurchaseInvoiceConvert.INSTANCE.convert2DTO(pageVO);
        // 2. 调用服务：获取分页数据
        PageResult<PurchaseInvoiceDTO> pageResult = purchaseInvoiceService.getPurchaseInvoicePage(pageReqDTO);
        // 3. 对象转换：将DTO对象转换为VO对象
        return success(PurchaseInvoiceConvert.INSTANCE.convert2VO(pageResult));
    }

    /**
     * 导出采购发票信息Excel
     * 处理流程：
     * 1. 设置分页大小：设置为不分页
     * 2. 对象转换：将VO对象转换为DTO对象
     * 3. 调用服务：获取数据列表
     * 4. 导出Excel：将数据导出为Excel文件
     *
     * @param pageVO   分页查询参数
     * @param response HTTP响应对象
     * @throws IOException 导出过程中的IO异常
     */
    @GetMapping("/export-excel")
    @Operation(summary = "导出采购发票信息 Excel")
    @PreAuthorize("@ss.hasPermission('saas:purchase:invoice:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPurchaseInvoiceExcel(@Valid PurchaseInvoicePageReqVO pageVO,
            HttpServletResponse response) throws IOException {
        // 1. 设置分页大小：设置为不分页
        pageVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        // 2. 对象转换：将VO对象转换为DTO对象
        PurchaseInvoicePageReqDTO pageReqDTO = PurchaseInvoiceConvert.INSTANCE.convert2DTO(pageVO);
        // 3. 调用服务：获取数据列表
        List<PurchaseInvoiceDTO> list = purchaseInvoiceService.getPurchaseInvoicePage(pageReqDTO).getList();
        // 4. 导出Excel：将数据导出为Excel文件
        ExcelUtils.write(response, "采购发票信息.xls", "数据", PurchaseInvoiceRespVO.class,
                PurchaseInvoiceConvert.INSTANCE.convert2VOList(list));
    }
}