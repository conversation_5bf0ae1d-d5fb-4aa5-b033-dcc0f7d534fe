package com.xyy.saas.localserver.purchase.server.admin.receive.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;

import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 管理后台 - 收货单明细新增/修改 Request VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 收货单明细新增/修改 Request VO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReceiveBillDetailSaveReqVO {

    /** 主键ID */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21916")
    private Long id;

    /** 收货单号 */
    @Schema(description = "收货单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String billNo;

    /** 商品编码 */
    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品编码不能为空")
    private String productPref;

    /** 批号 */
    @Schema(description = "批号")
    private String lotNo;

    /** 生产日期 */
    @Schema(description = "生产日期")
    private LocalDate productionDate;

    /** 有效期至 */
    @Schema(description = "有效期至")
    private LocalDate expiryDate;

    /** 含税成本价 */
    @Schema(description = "含税成本价", example = "7855")
    @DecimalMin(value = "0.0001", message = "原价应为0.0001-999999.9999之间")
    @Digits(integer = 6, fraction = 4, message = "原价应为0.0001-999999.9999之间")
    private BigDecimal price;

    /** 税率 */
    @Schema(description = "税率")
    @Digits(integer = 2, fraction = 2, message = "税率应为0.01-99.99之间")
    @DecimalMin(value = "0.01", message = "税率应为0.01-99.99之间")
    private BigDecimal taxRate;

    /** 折扣 */
    @Schema(description = "折扣", example = "20050")
    @Digits(integer = 2, fraction = 2, message = "折扣应为0.01-99.99之间")
    @DecimalMin(value = "0.01", message = "折扣应为0.01-99.99之间")
    private BigDecimal discount = new BigDecimal(100);

    /** 折后含税单价 */
    @Schema(description = "折后含税单价", example = "28936")
    @DecimalMin(value = "0.0001", message = "折后单价应为0.0001-999999.9999之间")
    @Digits(integer = 6, fraction = 4, message = "折后单价应为0.0001-999999.9999之间")
    private BigDecimal discountedPrice;

    /** 到货数量 */
    @Schema(description = "到货数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "到货数量不能为空")
    @Digits(integer = 6, fraction = 2, message = "出库数量应为0.01-999999.99之间")
    @DecimalMin(value = "0.01", message = "到货数量应为0.01-999999.99之间")
    private BigDecimal arriveQuantity;

    /** 收货数量 */
    @Schema(description = "收货数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @Digits(integer = 6, fraction = 2, message = "收货数量应为0-999999.99之间")
    @DecimalMin(value = "0", message = "收货数量应为0.00-999999.99之间")
    private BigDecimal receiveQuantity;

    /** 拒收数量 */
    @Schema(description = "拒收数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @Digits(integer = 6, fraction = 2, message = "拒收数量应为0-999999.99之间")
    @DecimalMin(value = "0", message = "拒收数量应为0-999999.99之间")
    private BigDecimal rejectQuantity;

    /** 收货总金额 */
    @Schema(description = "收货总金额")
    @Digits(integer = 12, fraction = 2, message = "收货总金额应为0-************.99之间")
    @DecimalMin(value = "0", message = "收货总金额应为0.00-************.99之间")
    private BigDecimal receiveAmount;

    /** 验收结论 */
    @Schema(description = "验收结论")
    private Boolean acceptConclusion;

    /** 抽样数量 */
    @Schema(description = "抽样数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @Digits(integer = 6, fraction = 2, message = "抽样数量应为0-999999.99之间")
    @DecimalMin(value = "0", message = "抽样数量应为0-999999.99之间")
    private BigDecimal sampleQuantity;

    /** 不合格品数量 */
    @Schema(description = "不合格品数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @Digits(integer = 6, fraction = 2, message = "不合格品数量应为0-999999.99之间")
    @DecimalMin(value = "0", message = "不合格品数量应为0-999999.99之间")
    private BigDecimal unqualifiedQuantity;

    /** 不合格品总金额 */
    @Schema(description = "不合格品总金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @Digits(integer = 12, fraction = 2, message = "不合格品总金额应为0-************.99之间")
    @DecimalMin(value = "0", message = "不合格品总金额应为0.00-************.99之间")
    private BigDecimal unqualifiedAmount;

    /** 不合格原因 */
    @Schema(description = "不合格原因", example = "不对")
    private String unqualifiedReason;

    /** 不合格品隔离区编码 */
    @Schema(description = "不合格品隔离区编码", example = "26747")
    private String unqualifiedPositionGuid;

    /** 合格品数量 */
    @Schema(description = "合格品数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @Digits(integer = 6, fraction = 2, message = "合格品数量应为0-999999.99之间")
    @DecimalMin(value = "0", message = "合格品数量应为0-999999.99之间")
    private BigDecimal qualifiedQuantity;

    /** 合格品总金额 */
    @Schema(description = "合格品总金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @Digits(integer = 12, fraction = 2, message = "合格品总金额应为0-************.99之间")
    @DecimalMin(value = "0", message = "合格品总金额应为0.00-************.99之间")
    private BigDecimal qualifiedAmount;

    /** 合格品储存区编码 */
    @Schema(description = "合格品储存区编码", example = "11633")
    private String qualifiedPositionGuid;

    /** 入库数量 */
    @Schema(description = "入库数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal warehouseQuantity;

    /** 入库金额 */
    @Schema(description = "入库金额")
    private BigDecimal warehouseAmount;

    /** 处理措施 */
    @Schema(description = "处理措施")
    private String treatment;

    /** 渠道ID */
    @Schema(description = "渠道ID", example = "16653")
    private String channelId;

    /** 灭菌批次 */
    @Schema(description = "灭菌批次")
    private String sterilizationBatchNo;

    /** 源行号 */
    @Schema(description = "源行号")
    private String sourceLineNo;

    /** 医保项目编码 */
    @Schema(description = "医保项目编码")
    private String medicareProjectCode;

    /** 医保项目名称 */
    @Schema(description = "医保项目名称", example = "王五")
    private String medicareProjectName;

    /** 医保项目等级 */
    @Schema(description = "医保项目等级", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer medicareProjectLevel;

    /** 医保最小包装数量 */
    @Schema(description = "医保最小包装数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer medicareMinPackageNum;

    /** 扩展信息 */
    @Schema(description = "扩展信息")
    @ExcelProperty("扩展信息")
    private ExtDTO ext;

    /** 备注 */
    @Schema(description = "备注", example = "随便")
    private String remark;

}