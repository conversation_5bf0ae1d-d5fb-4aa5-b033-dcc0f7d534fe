package com.xyy.saas.localserver.purchase.server.admin.purchase.vo;

import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

/**
 * 管理后台 - 采购明细 Response VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 采购明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PurchaseBillDetailRespVO {

    /** 主键ID */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "16976")
    @ExcelProperty("主键ID")
    private Long id;

    /** 计划单号 */
    @Schema(description = "计划单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("计划单号")
    private String planBillNo;

    /** 采购单号 */
    @Schema(description = "采购单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("采购单号")
    private String purchaseBillNo;

    /** 商品编码 */
    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编码")
    private String productPref;

    /** 批号 */
    @Schema(description = "批号")
    @ExcelProperty("批号")
    private String lotNo;

    /** 生产日期（YYYY-MM-DD） */
    @Schema(description = "生产日期")
    @ExcelProperty("生产日期")
    private LocalDate productionDate;

    /** 有效期至（YYYY-MM-DD） */
    @Schema(description = "有效期至")
    @ExcelProperty("有效期至")
    private LocalDate expiryDate;

    /** （合格）存储区编号 */
    @Schema(description = "存储区编号")
    @ExcelProperty("存储区编号")
    private String positionGuid;

    /** 供应商编码 */
    @Schema(description = "供应商编码", example = "30225")
    private String supplierGuid;

    /** 供应商名称 */
    @Schema(description = "供应商名称", example = "张三")
    private String supplierName;

    /** 进项税率（百分比） */
    @Schema(description = "进项税率")
    @ExcelProperty("进项税率")
    private BigDecimal inTaxRate;

    /** 销项税率（百分比） */
    @Schema(description = "销项税率")
    @ExcelProperty("销项税率")
    private BigDecimal outTaxRate;

    /** 采购单价 */
    @Schema(description = "采购单价", example = "12482")
    @ExcelProperty("采购单价")
    private BigDecimal price;

    /** 采购总金额 */
    @Schema(description = "采购总金额")
    @ExcelProperty("采购总金额")
    private BigDecimal purchaseAmount;

    /** 采购数量 */
    @Schema(description = "采购数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("采购数量")
    private BigDecimal purchaseQuantity;

    /** 已发货数量 */
    @Schema(description = "已发货数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("已发货数量")
    private BigDecimal deliveredQuantity;

    /** 可退数量（原单退货扣减可退数量，下游收货单的实际入库数量） */
    @Schema(description = "可退数量")
    @ExcelProperty("可退数量")
    private BigDecimal returnableQuantity;

    /** 渠道ID */
    @Schema(description = "渠道ID", example = "8991")
    @ExcelProperty("渠道ID")
    private String channelId;

    /** 源行号（和三方ERP对接时需要） */
    @Schema(description = "源行号")
    @ExcelProperty("源行号")
    private String sourceLineNo;

    /** 医保项目编码 */
    @Schema(description = "医保项目编码")
    @ExcelProperty("医保项目编码")
    private String medicareProjectCode;

    /** 医保项目名称 */
    @Schema(description = "医保项目名称", example = "芋艿")
    @ExcelProperty("医保项目名称")
    private String medicareProjectName;

    /** 医保项目等级 */
    @Schema(description = "医保项目等级", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("医保项目等级")
    private Integer medicareProjectLevel;

    /** 医保最小包装数量 */
    @Schema(description = "医保最小包装数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("医保最小包装数量")
    private Integer medicareMinPackageNum;

    /** 扩展信息（当前商品信息） */
    @Schema(description = "扩展信息")
    @ExcelProperty("扩展信息")
    private ExtDTO ext;

    /** 备注 */
    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    /** 创建时间 */
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}