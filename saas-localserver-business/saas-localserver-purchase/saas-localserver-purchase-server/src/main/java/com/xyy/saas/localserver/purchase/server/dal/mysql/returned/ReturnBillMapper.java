package com.xyy.saas.localserver.purchase.server.dal.mysql.returned;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillDTO;
import com.xyy.saas.localserver.purchase.api.returned.dto.ReturnBillPageReqDTO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.returned.ReturnBillDO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * （单体/总部/门店）退货单信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ReturnBillMapper extends BaseMapperX<ReturnBillDO> {

    default PageResult<ReturnBillDO> selectPage(ReturnBillPageReqDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<ReturnBillDO>()
                .eqIfPresent(ReturnBillDO::getBillNo, reqDTO.getBillNo())
                .eqIfPresent(ReturnBillDO::getTenantType, reqDTO.getTenantType())
                .eqIfPresent(ReturnBillDO::getHeadTenantId, reqDTO.getHeadTenantId())
                .eqIfPresent(ReturnBillDO::getPurchaseBillNo, reqDTO.getPurchaseBillNo())
                .eqIfPresent(ReturnBillDO::getMallOrderNo, reqDTO.getMallOrderNo())
                .eqIfPresent(ReturnBillDO::getInboundTenantId, reqDTO.getInboundTenantId())
                .eqIfPresent(ReturnBillDO::getBillType, reqDTO.getBillType())
                .eqIfPresent(ReturnBillDO::getStatus, reqDTO.getStatus())
                .eqIfPresent(ReturnBillDO::getSupplierGuid, reqDTO.getSupplierGuid())
                .likeIfPresent(ReturnBillDO::getSupplierName, reqDTO.getSupplierName())
                .eqIfPresent(ReturnBillDO::getProductKind, reqDTO.getProductKind())
                .eqIfPresent(ReturnBillDO::getReturnContent, reqDTO.getReturnContent())
                .eqIfPresent(ReturnBillDO::getReturnQuantity, reqDTO.getReturnQuantity())
                .eqIfPresent(ReturnBillDO::getReturnAmount, reqDTO.getReturnAmount())
                .eqIfPresent(ReturnBillDO::getCostAmount, reqDTO.getCostAmount())
                .eqIfPresent(ReturnBillDO::getOperator, reqDTO.getOperator())
                .betweenIfPresent(ReturnBillDO::getOperateTime, reqDTO.getOperateTime())
                .eqIfPresent(ReturnBillDO::getChecker, reqDTO.getChecker())
                .betweenIfPresent(ReturnBillDO::getCheckTime, reqDTO.getCheckTime())
                .eqIfPresent(ReturnBillDO::getRemark, reqDTO.getRemark())
                .eqIfPresent(ReturnBillDO::getVersion, reqDTO.getVersion())
                .betweenIfPresent(ReturnBillDO::getCreateTime, reqDTO.getCreateTime())
                // 优化 compositeBillNo 匹配逻辑
                .apply(StringUtils.isNotBlank(reqDTO.getCompositeBillNo()),
                        "INSTR(CONCAT('|', composite_bill_no, '|'), CONCAT('|', {0}, '|')) > 0",
                        reqDTO.getCompositeBillNo())
                .orderByDesc(ReturnBillDO::getId));
    }

    default ReturnBillDO selectReturnBillByBillNoAndHeadTenantId(String billNo, Long headTenantId) {
        return selectOne(ReturnBillDO::getBillNo, billNo, ReturnBillDO::getHeadTenantId, headTenantId);
    }

    default ReturnBillDO selectReturnBillByBillNoAndTenantId(String billNo, Long tenantId) {
        return selectOne(ReturnBillDO::getBillNo, billNo, ReturnBillDO::getTenantId, tenantId);
    }

    /**
     * 更新退货单状态
     *
     * @param returnBill 退货单
     * @return 更新条目数
     */
    int updateReturnBillByVersion(ReturnBillDO returnBill);

    /**
     * 根据单号和租户ID获取退货单信息（包含明细）
     *
     * @param billNo   单据编号
     * @param tenantId 租户编号
     * @return 退货单信息
     */
    ReturnBillDTO getReturnBillWithDetails(@Param("billNo") String billNo, @Param("tenantId") Long tenantId);

}