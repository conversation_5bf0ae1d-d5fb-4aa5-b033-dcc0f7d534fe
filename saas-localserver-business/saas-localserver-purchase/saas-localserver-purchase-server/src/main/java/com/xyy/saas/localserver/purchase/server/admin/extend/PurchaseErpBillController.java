package com.xyy.saas.localserver.purchase.server.admin.extend;

import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseErpBillDTO;
import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseErpBillPageReqDTO;
import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseErpBillPageReqVO;
import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseErpBillRespVO;
import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseErpBillSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.extend.PurchaseErpBillConvert;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.extend.PurchaseErpBillDO;
import com.xyy.saas.localserver.purchase.server.service.extend.PurchaseErpBillService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;
import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

/**
 * 管理后台 - 采购ERP单据信息 Controller
 * 处理采购ERP单据信息的创建、更新、删除、查询等操作
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "管理后台 - 采购ERP单据信息")
@RestController
@RequestMapping("/saas/purchase/erp-bill")
@Validated
public class PurchaseErpBillController {

    @Resource
    private PurchaseErpBillService purchaseErpBillService;

    /**
     * 创建采购ERP单据信息
     * 处理流程：
     * 1. 对象转换：将VO对象转换为DTO对象
     * 2. 调用服务：执行创建操作
     *
     * @param createReqVO 创建信息
     * @return 创建结果
     */
    @PostMapping("/create")
    @Operation(summary = "创建采购ERP单据信息")
    @PreAuthorize("@ss.hasPermission('saas:purchase:erp-bill:create')")
    public CommonResult<Long> createPurchaseErpBill(@Valid @RequestBody PurchaseErpBillSaveReqVO createReqVO) {
        // 1. 对象转换：将VO对象转换为DTO对象
        PurchaseErpBillDTO erpBillDTO = PurchaseErpBillConvert.INSTANCE.convert2DTO(createReqVO);
        // 2. 调用服务：执行创建操作
        return success(purchaseErpBillService.createPurchaseErpBill(erpBillDTO));
    }

    /**
     * 更新采购ERP单据信息
     * 处理流程：
     * 1. 对象转换：将VO对象转换为DTO对象
     * 2. 调用服务：执行更新操作
     *
     * @param updateReqVO 更新信息
     * @return 更新结果
     */
    @PutMapping("/update")
    @Operation(summary = "更新采购ERP单据信息")
    @PreAuthorize("@ss.hasPermission('saas:purchase:erp-bill:update')")
    public CommonResult<Boolean> updatePurchaseErpBill(@Valid @RequestBody PurchaseErpBillSaveReqVO updateReqVO) {
        // 1. 对象转换：将VO对象转换为DTO对象
        PurchaseErpBillDTO erpBillDTO = PurchaseErpBillConvert.INSTANCE.convert2DTO(updateReqVO);
        // 2. 调用服务：执行更新操作
        purchaseErpBillService.updatePurchaseErpBill(erpBillDTO);
        return success(true);
    }

    /**
     * 删除采购ERP单据信息
     * 处理流程：
     * 1. 调用服务：执行删除操作
     *
     * @param id 编号
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    @Operation(summary = "删除采购ERP单据信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:purchase:erp-bill:delete')")
    public CommonResult<Boolean> deletePurchaseErpBill(@RequestParam("id") Long id) {
        // 调用服务：执行删除操作
        purchaseErpBillService.deletePurchaseErpBill(id);
        return success(true);
    }

    /**
     * 获取采购ERP单据信息
     * 处理流程：
     * 1. 调用服务：获取ERP单据信息
     * 2. 对象转换：将DO对象转换为VO对象
     *
     * @param id 编号
     * @return ERP单据信息
     */
    @GetMapping("/get")
    @Operation(summary = "获得采购ERP单据信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:purchase:erp-bill:query')")
    public CommonResult<PurchaseErpBillRespVO> getPurchaseErpBill(@RequestParam("id") Long id) {
        // 1. 调用服务：获取ERP单据信息
        PurchaseErpBillDO purchaseErpBill = purchaseErpBillService.getPurchaseErpBill(id);
        // 2. 对象转换：将DO对象转换为VO对象
        return success(PurchaseErpBillConvert.INSTANCE.convert2VO(purchaseErpBill));
    }

    /**
     * 获取采购ERP单据信息分页
     * 处理流程：
     * 1. 对象转换：将VO对象转换为DTO对象
     * 2. 调用服务：获取分页数据
     * 3. 对象转换：将DTO对象转换为VO对象
     *
     * @param pageReqVO 分页查询参数
     * @return 分页结果
     */
    @GetMapping("/page")
    @Operation(summary = "获得采购ERP单据信息分页")
    @PreAuthorize("@ss.hasPermission('saas:purchase:erp-bill:query')")
    public CommonResult<PageResult<PurchaseErpBillRespVO>> getPurchaseErpBillPage(
            @Valid PurchaseErpBillPageReqVO pageReqVO) {
        // 1. 对象转换：将VO对象转换为DTO对象
        PurchaseErpBillPageReqDTO pageReqDTO = PurchaseErpBillConvert.INSTANCE.convert2DTO(pageReqVO);
        // 2. 调用服务：获取分页数据
        PageResult<PurchaseErpBillDTO> pageResult = purchaseErpBillService.getPurchaseErpBillPage(pageReqDTO);
        // 3. 对象转换：将DTO对象转换为VO对象
        return success(PurchaseErpBillConvert.INSTANCE.convert2VO(pageResult));
    }

    /**
     * 导出采购ERP单据信息Excel
     * 处理流程：
     * 1. 设置分页大小：设置为不分页
     * 2. 对象转换：将VO对象转换为DTO对象
     * 3. 调用服务：获取数据列表
     * 4. 导出Excel：将数据导出为Excel文件
     *
     * @param pageReqVO 分页查询参数
     * @param response  HTTP响应对象
     * @throws IOException 导出过程中的IO异常
     */
    @GetMapping("/export-excel")
    @Operation(summary = "导出采购ERP单据信息 Excel")
    @PreAuthorize("@ss.hasPermission('saas:purchase:erp-bill:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPurchaseErpBillExcel(@Valid PurchaseErpBillPageReqVO pageReqVO,
            HttpServletResponse response) throws IOException {
        // 1. 设置分页大小：设置为不分页
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        // 2. 对象转换：将VO对象转换为DTO对象
        PurchaseErpBillPageReqDTO pageReqDTO = PurchaseErpBillConvert.INSTANCE.convert2DTO(pageReqVO);
        // 3. 调用服务：获取数据列表
        List<PurchaseErpBillDTO> list = purchaseErpBillService.getPurchaseErpBillPage(pageReqDTO).getList();
        // 4. 导出Excel：将数据导出为Excel文件
        ExcelUtils.write(response, "采购ERP单据信息.xls", "数据", PurchaseErpBillRespVO.class,
                PurchaseErpBillConvert.INSTANCE.convert2VOList(list));
    }
}