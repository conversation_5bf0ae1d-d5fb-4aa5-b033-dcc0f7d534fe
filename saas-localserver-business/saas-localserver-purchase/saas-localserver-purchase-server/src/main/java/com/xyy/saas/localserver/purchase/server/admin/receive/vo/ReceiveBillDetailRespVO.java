package com.xyy.saas.localserver.purchase.server.admin.receive.vo;

import com.xyy.saas.inquiry.product.api.product.dto.ProductInfoDto;
import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDate;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

/**
 * 管理后台 - 收货单明细 Response VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 收货单明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ReceiveBillDetailRespVO {

    /** 主键ID */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21916")
    @ExcelProperty("主键ID")
    private Long id;

    /** 验收入库单号 */
    @Schema(description = "验收入库单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("验收入库单号")
    private String billNo;

    /** 商品编码 */
    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编码")
    private String productPref;

    /** 批号 */
    @Schema(description = "批号")
    @ExcelProperty("批号")
    private String lotNo;

    /** 生产日期 */
    @Schema(description = "生产日期")
    @ExcelProperty("生产日期")
    private LocalDate productionDate;

    /** 有效期至 */
    @Schema(description = "有效期至")
    @ExcelProperty("有效期至")
    private LocalDate expiryDate;

    /** 含税成本价 */
    @Schema(description = "含税成本价", example = "7855")
    @ExcelProperty("含税成本价")
    private BigDecimal price;

    /** 税率 */
    @Schema(description = "税率")
    @ExcelProperty("税率")
    private BigDecimal taxRate;

    /** 折扣 */
    @Schema(description = "折扣", example = "20050")
    @ExcelProperty("折扣")
    private BigDecimal discount;

    /** 折后含税单价 */
    @Schema(description = "折后含税单价", example = "28936")
    @ExcelProperty("折后含税单价")
    private BigDecimal discountedPrice;

    /** 到货数量 */
    @Schema(description = "到货数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("到货数量")
    private BigDecimal arriveQuantity;

    /** 收货数量 */
    @Schema(description = "收货数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收货数量")
    private BigDecimal receiveQuantity;

    /** 拒收数量 */
    @Schema(description = "拒收数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("拒收数量")
    private BigDecimal rejectQuantity;

    /** 收货金额 */
    @Schema(description = "收货金额")
    @ExcelProperty("收货金额")
    private BigDecimal receiveAmount;

    /** 验收结论 */
    @Schema(description = "验收结论")
    @ExcelProperty("验收结论")
    private Boolean acceptConclusion;

    /** 抽样数量 */
    @Schema(description = "抽样数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("抽样数量")
    private BigDecimal sampleQuantity;

    /** 不合格品数量 */
    @Schema(description = "不合格品数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("不合格品数量")
    private BigDecimal unqualifiedQuantity;

    /** 不合格品总金额 */
    @Schema(description = "不合格品总金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("不合格品总金额")
    private BigDecimal unqualifiedAmount;

    /** 不合格原因 */
    @Schema(description = "不合格原因", example = "不对")
    @ExcelProperty("不合格原因")
    private String unqualifiedReason;

    /** 不合格品隔离区编码 */
    @Schema(description = "不合格品隔离区编码", example = "26747")
    @ExcelProperty("不合格品隔离区编码")
    private String unqualifiedPositionGuid;

    /** 合格品数量 */
    @Schema(description = "合格品数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("合格品数量")
    private BigDecimal qualifiedQuantity;

    /** 合格品总金额 */
    @Schema(description = "合格品总金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("合格品总金额")
    private BigDecimal qualifiedAmount;

    /** 合格品储存区编码 */
    @Schema(description = "合格品储存区编码", example = "11633")
    @ExcelProperty("合格品储存区编码")
    private String qualifiedPositionGuid;

    /** 入库数量 */
    @Schema(description = "入库数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("入库数量")
    private BigDecimal warehouseQuantity;

    /** 入库金额 */
    @Schema(description = "入库金额")
    @ExcelProperty("入库金额")
    private BigDecimal warehouseAmount;

    /** 处理措施 */
    @Schema(description = "处理措施")
    @ExcelProperty("处理措施")
    private String treatment;

    /** 渠道ID */
    @Schema(description = "渠道ID", example = "16653")
    @ExcelProperty("渠道ID")
    private String channelId;

    /** 灭菌批次 */
    @Schema(description = "灭菌批次")
    @ExcelProperty("灭菌批次")
    private String sterilizationBatchNo;

    /** 源行号 */
    @Schema(description = "源行号")
    @ExcelProperty("源行号")
    private String sourceLineNo;

    /** 医保项目编码 */
    @Schema(description = "医保项目编码")
    @ExcelProperty("医保项目编码")
    private String medicareProjectCode;

    /** 医保项目名称 */
    @Schema(description = "医保项目名称", example = "王五")
    @ExcelProperty("医保项目名称")
    private String medicareProjectName;

    /** 医保项目等级 */
    @Schema(description = "医保项目等级", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("医保项目等级")
    private Integer medicareProjectLevel;

    /** 医保最小包装数量 */
    @Schema(description = "医保最小包装数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("医保最小包装数量")
    private Integer medicareMinPackageNum;

    /** 扩展信息 */
    @Schema(description = "扩展信息")
    @ExcelProperty("扩展信息")
    private ExtDTO ext;

    /** 备注 */
    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    /** 创建时间 */
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    /** 商品信息 */
    @Schema(description = "商品信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private ProductInfoDto productInfo;
}