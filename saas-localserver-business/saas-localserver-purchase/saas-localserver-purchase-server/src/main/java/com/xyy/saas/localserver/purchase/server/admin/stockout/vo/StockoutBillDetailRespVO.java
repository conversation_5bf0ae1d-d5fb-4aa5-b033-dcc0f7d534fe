package com.xyy.saas.localserver.purchase.server.admin.stockout.vo;

import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

/**
 * 管理后台 - 缺货单明细 Response VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 缺货单明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class StockoutBillDetailRespVO {

    /** 主键ID */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10161")
    @ExcelProperty("主键ID")
    private Long id;

    /** 单号 */
    @Schema(description = "单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单号")
    private String billNo;

    /** 商品编码 */
    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编码")
    private String productPref;

    /** 要货数量 */
    @Schema(description = "要货数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("要货数量")
    private BigDecimal requireQuantity;

    /** 缺货数量 */
    @Schema(description = "缺货数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("缺货数量")
    private BigDecimal stockoutQuantity;

    /** 医保项目编码 */
    @Schema(description = "医保项目编码")
    @ExcelProperty("医保项目编码")
    private String medicareProjectCode;

    /** 医保项目名称 */
    @Schema(description = "医保项目名称", example = "张三")
    @ExcelProperty("医保项目名称")
    private String medicareProjectName;

    /** 医保项目等级 */
    @Schema(description = "医保项目等级", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("医保项目等级")
    private Integer medicareProjectLevel;

    /** 医保最小包装数量 */
    @Schema(description = "医保最小包装数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("医保最小包装数量")
    private Integer medicareMinPackageNum;

    /** 扩展信息 */
    @Schema(description = "扩展信息（记录商品信息）")
    @ExcelProperty("扩展信息")
    private ExtDTO ext;

    /** 备注 */
    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    /** 创建时间 */
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}