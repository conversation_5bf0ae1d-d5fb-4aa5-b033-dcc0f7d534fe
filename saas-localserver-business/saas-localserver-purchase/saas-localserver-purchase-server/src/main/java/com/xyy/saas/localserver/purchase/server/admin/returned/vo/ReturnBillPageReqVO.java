package com.xyy.saas.localserver.purchase.server.admin.returned.vo;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.xyy.saas.localserver.purchase.enums.returned.ReturnBillStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 退货单分页 Request VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 退货单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ReturnBillPageReqVO extends PageParam {

    /** 单号 */
    @Schema(description = "单号")
    private String billNo;

    /** 采购单号 */
    @Schema(description = "采购单号")
    private String purchaseBillNo;

    /** 商城订单号 */
    @Schema(description = "商城订单号")
    private String mallOrderNo;

    /** 入库门店租户ID */
    @Schema(description = "入库门店租户ID", example = "2524")
    private Long inboundTenantId;

    /** 租户类型 */
    @Schema(description = "租户类型（1-单体门店、2-连锁门店、3-连锁总部）", example = "1")
    private Integer tenantType;

    /** 总部租户ID */
    @Schema(description = "总部租户ID", example = "2524")
    private Long headTenantId;

    /** 综合单据号 */
    @Schema(description = "综合单据号")
    private String compositeBillNo;

    /** 单据类型 */
    @Schema(description = "单据类型（1-采购退货、2-门店退货、3-门店调剂）")
    private Integer billType;

    /** 状态 */
    @Schema(description = "状态（1-待审批、2-待出库、3-待复核、4-已出库、5-已驳回、6-已撤销）", example = "2")
    private Integer status;

    /** 页面显示状态 */
    @Schema(description = "页面显示状态", example = "2")
    private Integer displayStatus;

    /** 已提交 */
    @Schema(description = "已提交", example = "false")
    private Boolean submitted;

    /** 供应商编码 */
    @Schema(description = "供应商编码")
    private String supplierGuid;

    /** 供应商名称 */
    @Schema(description = "供应商名称", example = "李四")
    private String supplierName;

    /** 商品种类 */
    @Schema(description = "商品种类")
    private Integer productKind;

    /** 退货内容 */
    @Schema(description = "退货内容")
    private String returnContent;

    /** 退货数量 */
    @Schema(description = "退货数量")
    private BigDecimal returnQuantity;

    /** 退货总金额 */
    @Schema(description = "退货总金额")
    private BigDecimal returnAmount;

    /** 成本总金额 */
    @Schema(description = "成本总金额")
    private BigDecimal costAmount;

    /** 出库操作员 */
    @Schema(description = "出库操作员")
    private String operator;

    /** 出库时间 */
    @Schema(description = "出库时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] operateTime;

    /** 复核员 */
    @Schema(description = "复核员")
    private String checker;

    /** 复核时间 */
    @Schema(description = "复核时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] checkTime;

    /** 备注 */
    @Schema(description = "备注", example = "你说的对")
    private String remark;

    /** 版本 */
    @Schema(description = "版本")
    private Integer version;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    /**
     * 退货单显示状态枚举
     */
    @Getter
    public enum ReturnBillDisplayStatus {
        /** 全部状态 */
        ALL(0, "全部") {
            @Override
            public void apply(ReturnBillPageReqVO vo) {
                // 不设置任何过滤条件
            }
        },

        /** 暂存状态 */
        TEMPORARY(1, "暂存") {
            @Override
            public void apply(ReturnBillPageReqVO vo) {
                vo.setStatus(PENDING_APPROVAL.getCode());
                vo.setSubmitted(false);
            }
        },

        /** 待审批状态 */
        PENDING_APPROVAL(2, "待审批") {
            @Override
            public void apply(ReturnBillPageReqVO vo) {
                vo.setStatus(ReturnBillStatusEnum.PENDING_APPROVAL.getCode());
                vo.setSubmitted(true);
            }
        },

        /** 待出库状态 */
        PENDING_OUTBOUND(3, "待出库") {
            @Override
            public void apply(ReturnBillPageReqVO vo) {
                vo.setStatus(ReturnBillStatusEnum.PENDING_OUTBOUND.getCode());
                vo.setSubmitted(true);
            }
        },

        /** 待复核状态 */
        PENDING_REVIEW(4, "待复核") {
            @Override
            public void apply(ReturnBillPageReqVO vo) {
                vo.setStatus(ReturnBillStatusEnum.PENDING_REVIEW.getCode());
                vo.setSubmitted(true);
            }
        },

        /** 已出库状态 */
        OUTBOUND(5, "已出库") {
            @Override
            public void apply(ReturnBillPageReqVO vo) {
                vo.setStatus(ReturnBillStatusEnum.OUTBOUND.getCode());
                vo.setSubmitted(true);
            }
        },

        /** 已驳回状态 */
        REJECTED(6, "已驳回") {
            @Override
            public void apply(ReturnBillPageReqVO vo) {
                vo.setStatus(ReturnBillStatusEnum.REJECTED.getCode());
                vo.setSubmitted(true);
            }
        },

        /** 已撤销状态 */
        REVOKED(7, "已撤销") {
            @Override
            public void apply(ReturnBillPageReqVO vo) {
                vo.setStatus(ReturnBillStatusEnum.REVOKED.getCode());
                vo.setSubmitted(true);
            }
        };

        private final int code;
        private final String name;

        ReturnBillDisplayStatus(int code, String name) {
            this.code = code;
            this.name = name;
        }

        /**
         * 应用显示状态到查询条件
         * 
         * @param vo 查询条件VO
         */
        public abstract void apply(ReturnBillPageReqVO vo);

        @JsonCreator
        public static ReturnBillDisplayStatus fromCode(int code) {
            for (ReturnBillDisplayStatus status : values()) {
                if (status.code == code) {
                    return status;
                }
            }
            throw new IllegalArgumentException("无效的状态码: " + code);
        }
    }
}