package com.xyy.saas.localserver.purchase.server.dal.dataobject.stockout;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.TableField;
import lombok.*;
import org.apache.commons.lang3.StringUtils;
import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 缺货单 DO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@TableName(value = "saas_purchase_stockout_bill", autoResultMap = true)
// @KeySequence("saas_purchase_stockout_bill_seq") // 用于
// Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class StockoutBillDO extends TenantBaseDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 单号 */
    private String billNo;

    /** 门店租户ID */
    private Long storeTenantId;

    /** 要货单号(连锁才有) */
    private String requisitionBillNo;

    /** 配送单号 */
    private String deliveryBillNo;

    /** 综合单据号（单号混合） */
    @TableField(fill = com.baomidou.mybatisplus.annotation.FieldFill.INSERT_UPDATE)
    private String compositeBillNo;

    /** 状态（0-缺货、1-已完成） */
    private Integer status;

    /** 商品种类 */
    private Integer productKind;

    /** 缺货内容 */
    private String stockoutContent;

    /** 缺货数量 */
    private BigDecimal stockoutQuantity;

    /** 要货数量 */
    private BigDecimal requireQuantity;

    /** 备注 */
    private String remark;

    /** 版本(乐观锁) */
    private Integer version;

    /**
     * 生成综合单据号
     * 规则：billNo|requisitionBillNo|deliveryBillNo
     */
    public String generateCompositeBillNo() {
        this.compositeBillNo = Stream.of(billNo, requisitionBillNo, deliveryBillNo)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining("|", "|", "|"));
        return this.compositeBillNo;
    }

}