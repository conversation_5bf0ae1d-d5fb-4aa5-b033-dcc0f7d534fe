package com.xyy.saas.localserver.purchase.server.dal.dataobject.supplier;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import java.time.LocalDate;
import com.baomidou.mybatisplus.annotation.*;
import org.apache.commons.lang3.StringUtils;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 供应商 DO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@TableName(value = "saas_purchase_supplier", autoResultMap = true)
// @KeySequence("saas_purchase_supplier_seq") // 用于
// Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SupplierDO extends TenantBaseDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 供应商编码(租户本地新建供应商生成规则：GYS+机器码+日期+4位流水) */
    private String guid;

    /** 商城供应商编码 */
    private String sourceSupplierGuid;

    /** 供应商名称 */
    private String name;

    /** 供应商类别（字典配置-20048） */
    private Integer type;

    /** 助记码（混合查询条件） */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String mnemonicCode;

    /** 系统默认 */
    private Boolean systemDefault;

    /** 来源（非系统默认供应商，数据来源默认为租户id） */
    private Long source;

    /** 法定代表人 */
    private String legalRepresentative;

    /** 注册地址 */
    private String registeredAddress;

    /** 经营范围（字典配置-10005） */
    private String businessScope;

    /** 营业执照编码 */
    private String businessLicense;

    /** 发证机关 */
    private String licenceAuthority;

    /** 注册日期 */
    private LocalDate registeredDate;

    /** 有效期至 */
    private LocalDate expirationDate;

    /** 有效期至方式（1-长期，2-手填，当为2的时候，expiration_date必须有值） */
    private Integer expirationDateType;

    /** 是否三证合一（0-否，1-是） */
    private Boolean triCertMerged;

    /** 开户银行 */
    private String depositBank;

    /** 银行账号 */
    private String bankAccount;

    /** 开户户名 */
    private String accountName;

    /** 组织机构代码 */
    private String organizationCertificationCode;

    /** 组织机构发证日期 */
    private LocalDate organizationCertificationDate;

    /** 组织机构有效期至 */
    private LocalDate organizationCertificationExpirationDate;

    /** 组织机构税务登记号 */
    private String organizationCertificationTaxNo;

    /** 组织机构代码证发证机关 */
    private String organizationCertificationAuthority;

    /** 注册地址code */
    private String registeredAddressCod;

    /** 仓库地址code */
    private String storeAddressCode;

    /** 仓库明细地址 */
    private String storeAddress;

    /** 印章印模附件 */
    private String signet;

    /** 随货同行单样式附件 */
    private String shipmentTemplate;

    /** 资质与经营范围json数据 */
    private String qualificationInfos;

    /** 委托人身份证号 */
    private String proxyIdCard;

    /** 委托人身份证有效期 */
    private LocalDate proxyIdCardExpirationDate;

    /** 码上放心-企业唯一标识 */
    private String msfxRefEntId;

    /** 码上放心-企业ID */
    private String msfxEntId;

    /** 关联分发业务 */
    private Boolean relateDistribute;

    /** 备注（存json数据） */
    private String remark;

    /** 是否禁用 */
    private Boolean disabled;

    /**
     * 生成助记码
     * 规则：guid|name|name的首字母大写
     */
    public String generateMnemonicCode() {
        this.mnemonicCode = Stream.of(guid, name, getChineseInitials(name))
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining("|", "|", "|"));
        return this.mnemonicCode;
    }

    /**
     * 获取中文拼音首字母
     *
     * @param chinese 中文字符串
     * @return 拼音首字母
     */
    private String getChineseInitials(String chinese) {
        if (StringUtils.isBlank(chinese)) {
            return "";
        }

        final HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        format.setCaseType(HanyuPinyinCaseType.UPPERCASE);
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);

        return chinese.chars()
                .filter(c -> !Character.isWhitespace(c)) // 过滤空格
                .mapToObj(c -> (char) c)
                .map(c -> {
                    // 汉字处理
                    if (c >= 0x4E00 && c <= 0x9FA5) { // 更高效的Unicode范围判断
                        String[] pinyin;
                        try {
                            pinyin = PinyinHelper.toHanyuPinyinStringArray(c, format);
                        } catch (BadHanyuPinyinOutputFormatCombination e) {
                            throw new RuntimeException(e);
                        }
                        return (pinyin != null && pinyin.length > 0) ? String.valueOf(pinyin[0].charAt(0)) : "";
                    }
                    // 字母处理
                    return Character.isLetter(c) ? String.valueOf(Character.toUpperCase(c)) : "";
                })
                .collect(Collectors.joining());
    }

}