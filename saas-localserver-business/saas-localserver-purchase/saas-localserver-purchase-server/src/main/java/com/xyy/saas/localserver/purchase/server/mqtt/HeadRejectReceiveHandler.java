package com.xyy.saas.localserver.purchase.server.mqtt;

import com.xyy.saas.localserver.entity.mqtt.MqttMessageHandler;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDTO;
import com.xyy.saas.localserver.purchase.server.admin.receive.vo.ReceiveBillSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.receive.ReceiveBillConvert;
import com.xyy.saas.localserver.purchase.server.service.receive.ReceiveBillService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

// 总部拒收消息处理
@Slf4j
@Component
public class HeadRejectReceiveHandler implements MqttMessageHandler<ReceiveBillSaveReqVO> {

    @Resource
    private ReceiveBillService receiveBillService;


    /** 总部拒收 */
    private static final String MESSAGE_TYPE = "HEAD_REJECT_RECEIVE";

    @Override
    public String getMessageType() {
        return MESSAGE_TYPE;
    }


    @Override
    public Class<ReceiveBillSaveReqVO> getDataClass() {
        return ReceiveBillSaveReqVO.class; // 直接返回具体类型
    }

    @Override
    public void handle(String topic, ReceiveBillSaveReqVO data) {

        // 对象转换
        ReceiveBillDTO receiveBill = ReceiveBillConvert.INSTANCE.convert2RejectWarehousingDTO(data);
        // 处理收货
        receiveBillService.rejectWarehousing(receiveBill);
    }
}
