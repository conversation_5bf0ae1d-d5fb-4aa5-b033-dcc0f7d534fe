package com.xyy.saas.localserver.purchase.server.dal.mysql.stockout;

import com.xyy.saas.localserver.purchase.api.stockout.dto.StockoutBillPageReqDTO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.stockout.StockoutBillDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import org.apache.ibatis.annotations.Mapper;

/**
 * 采购-缺货单信息 Mapper
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface StockoutBillMapper extends BaseMapperX<StockoutBillDO> {

    /**
     * 获取采购-缺货单信息分页
     * @param reqDTO 分页参数
     * @return 采购-缺货单信息分页
     */
    default PageResult<StockoutBillDO> selectPage(StockoutBillPageReqDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<StockoutBillDO>()
                .eqIfPresent(StockoutBillDO::getBillNo, reqDTO.getBillNo())
                .eqIfPresent(StockoutBillDO::getStoreTenantId, reqDTO.getStoreTenantId())
                .eqIfPresent(StockoutBillDO::getRequisitionBillNo, reqDTO.getRequisitionBillNo())
                .eqIfPresent(StockoutBillDO::getDeliveryBillNo, reqDTO.getDeliveryBillNo())
                .eqIfPresent(StockoutBillDO::getCompositeBillNo, reqDTO.getCompositeBillNo())
                .eqIfPresent(StockoutBillDO::getStatus, reqDTO.getStatus())
                .eqIfPresent(StockoutBillDO::getProductKind, reqDTO.getProductKind())
                .likeIfPresent(StockoutBillDO::getStockoutContent, reqDTO.getStockoutContent())
                .eqIfPresent(StockoutBillDO::getStockoutQuantity, reqDTO.getStockoutQuantity())
                .eqIfPresent(StockoutBillDO::getRequireQuantity, reqDTO.getRequireQuantity())
                .likeIfPresent(StockoutBillDO::getRemark, reqDTO.getRemark())
                .eqIfPresent(StockoutBillDO::getVersion, reqDTO.getVersion())
                .betweenIfPresent(StockoutBillDO::getCreateTime, reqDTO.getCreateTime())
                .orderByDesc(StockoutBillDO::getId));
    }
}