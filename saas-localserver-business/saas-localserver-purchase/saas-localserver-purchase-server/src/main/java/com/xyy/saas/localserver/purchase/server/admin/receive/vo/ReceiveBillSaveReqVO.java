package com.xyy.saas.localserver.purchase.server.admin.receive.vo;

import cn.hutool.core.lang.Assert;
import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseInvoiceSaveReqVO;
import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseTransportSaveReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.*;

/**
 * 管理后台 - 收货单新增/修改 Request VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 收货单新增/修改 Request VO")
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReceiveBillSaveReqVO {

    /** 主键ID */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21916")
    private Long id;

    /** 验收入库单号 */
    @Schema(description = "验收入库单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String billNo;

    /** 采购单号 */
    @Schema(description = "采购单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String purchaseBillNo;

    /** 来源单号 */
    @Schema(description = "来源单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String sourceBillNo;

    /** 配送出库单号 */
    @Schema(description = "配送出库单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String deliveryBillNo;

    /** 随货同行单号 */
    @Schema(description = "随货同行单号")
    private String shipmentNo;

    /** 商城订单号 */
    @Schema(description = "商城订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String mallOrderNo;

    /** 出库门店租户id */
    @Schema(description = "出库门店租户id")
    private Long outboundTenantId;

    /** 租户id */
    @Schema(description = "租户id")
    private Long tenantId;

    /** 租户类型（1-单体门店、2-连锁门店、3-连锁总部） */
    @Schema(description = "租户类型（1-单体门店、2-连锁门店、3-连锁总部）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    private Integer tenantType;

    /** 总部租户ID */
    @Schema(description = "总部租户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "32278")
    private Long headTenantId;

    /** 综合单据号（单号混合） */
    @Schema(description = "综合单据号（单号混合）")
    private String compositeBillNo;

    /** 单据类型（1-采购收货、2-拒收收货、3-退货收货、4-调剂收货、5-要货收货、6-铺货收货） */
    @Schema(description = "单据类型（1-采购收货、2-拒收收货、3-退货收货、4-调剂收货、5-要货收货、6-铺货收货）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "单据类型（1-采购收货、2-拒收收货、3-退货收货、4-调剂收货、5-要货收货、6-铺货收货）不能为空")
    private Integer billType;

    /** 采购方式（1-线下采购、2-无仓（B2B 无仓）、3-共仓（B2C 共仓）） */
    @Schema(description = "采购方式（1-线下采购、2-无仓（B2B 无仓）、3-共仓（B2C 共仓））", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer purchaseMode = 1;

    /** 状态（1-待收货、2-待验收、3-待入库、4-已入库、5-已拒收） */
    @Schema(description = "状态（1-待收货、2-待验收、3-待入库、4-已入库、5-已拒收）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "状态（1-待收货、2-待验收、3-待入库、4-已入库、5-已拒收）不能为空")
    private Integer status;

    /** 供应商编码 */
    @Schema(description = "供应商编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "13384")
    private String supplierGuid;

    /** 供应商名称 */
    @Schema(description = "供应商名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "赵六")
    private String supplierName;

    /** 供应商销售员 */
    @Schema(description = "供应商销售员", requiredMode = Schema.RequiredMode.REQUIRED)
    private String supplierSales;

    /** 商品种类 */
    @Schema(description = "商品种类", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer productKind;

    /** 收货数量 */
    @Schema(description = "收货数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "收货数量不能为空")
    private BigDecimal receiveQuantity;

    /** 折扣（百分比） */
    @Schema(description = "折扣（百分比）", example = "23792")
    private BigDecimal discount;

    /** 折扣总金额 */
    @Schema(description = "折扣总金额")
    private BigDecimal discountAmount;

    /** 收货内容 */
    @Schema(description = "收货内容", requiredMode = Schema.RequiredMode.REQUIRED)
    private String receiveContent;

    /** 收货金额（折后总金额） */
    @Schema(description = "收货金额（折后总金额）", requiredMode = Schema.RequiredMode.REQUIRED)
    private BigDecimal receiveAmount;

    /** 配送员 */
    @Schema(description = "配送员", requiredMode = Schema.RequiredMode.REQUIRED)
    private String deliverer;

    /** 配送出库时间 */
    @Schema(description = "配送出库时间", requiredMode = Schema.RequiredMode.REQUIRED)
    private LocalDateTime deliveryTime;

    /** 收货人 */
    @Schema(description = "收货人", requiredMode = Schema.RequiredMode.REQUIRED)
    private String receiver;

    /** 收货人电话 */
    @Schema(description = "收货人电话", requiredMode = Schema.RequiredMode.REQUIRED)
    private String receiverPhone;

    /** 收货人所在区域 */
    @Schema(description = "收货人所在区域", requiredMode = Schema.RequiredMode.REQUIRED)
    private String receiverArea;

    /** 收货人地址 */
    @Schema(description = "收货人地址", requiredMode = Schema.RequiredMode.REQUIRED)
    private String receiverAddress;

    /** 收货时间 */
    @Schema(description = "收货时间")
    private LocalDateTime receiveTime;

    /** 验收员 */
    @Schema(description = "验收员", requiredMode = Schema.RequiredMode.REQUIRED)
    private String accepter;

    /** 验收时间 */
    @Schema(description = "验收时间")
    private LocalDateTime acceptTime;

    /** 入库员 */
    @Schema(description = "入库员", requiredMode = Schema.RequiredMode.REQUIRED)
    private String warehouser;

    /** 入库时间 */
    @Schema(description = "入库时间")
    private LocalDateTime warehouseTime;

    /** 复核员 */
    @Schema(description = "复核员", requiredMode = Schema.RequiredMode.REQUIRED)
    private String checker;

    /** 复核时间 */
    @Schema(description = "复核时间")
    private LocalDateTime checkTime;

    /** 质检员 */
    @Schema(description = "质检员")
    private String qualityInspector;

    /** 质检报告单 */
    @Schema(description = "质检报告单")
    private String qualityInspectionReport;

    /** 备注 */
    @Schema(description = "备注", example = "随便")
    private String remark;

    /** 版本(乐观锁) */
    @Schema(description = "版本(乐观锁)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "版本(乐观锁)不能为空")
    private Integer version;

    /** 收货单明细 */
    @Schema(description = "收货单明细", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "收货单明细不能为空")
    private List<ReceiveBillDetailSaveReqVO> details;

    /** 采购扩展信息-运输信息 */
    @Schema(description = "采购扩展信息-运输信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private PurchaseTransportSaveReqVO transport;

    /** 采购扩展信息-发票 */
    @Schema(description = "采购扩展信息-发票", requiredMode = Schema.RequiredMode.REQUIRED)
    private PurchaseInvoiceSaveReqVO invoice;

    /**
     * 校验供应商信息
     */
    public void validateSupplier() {
        Assert.notNull(this.getSupplierGuid(), RECEIVE_BILL_SUPPLIER_NOT_EXISTS.getMsg());
        Assert.notNull(this.getSupplierName(), RECEIVE_BILL_SUPPLIER_NOT_EXISTS.getMsg());
    }

    /**
     * 校验收货明细数据完整性
     */
    public void validateReceiveDetails() {
        for (ReceiveBillDetailSaveReqVO detail : this.getDetails()) {
            Assert.notNull(detail.getReceiveQuantity(), RECEIVE_BILL_DETAIL_RECEIVE_QUANTITY_NOT_NULL.getMsg());
            Assert.notNull(detail.getPrice(), RECEIVE_BILL_DETAIL_PRICE_NOT_NULL.getMsg());
            Assert.notNull(detail.getDiscount(), RECEIVE_BILL_DETAIL_DISCOUNT_NOT_NULL.getMsg());
            Assert.notNull(detail.getDiscountedPrice(), RECEIVE_BILL_DETAIL_DISCOUNTED_PRICE_NOT_NULL.getMsg());
            Assert.notNull(detail.getReceiveAmount(), RECEIVE_BILL_DETAIL_RECEIVE_AMOUNT_NOT_NULL.getMsg());
        }
    }

    /**
     * 校验验收明细数据完整性
     */
    public void validateAcceptDetails() {
        for (ReceiveBillDetailSaveReqVO detail : this.getDetails()) {
            Assert.notNull(detail.getSampleQuantity(), RECEIVE_BILL_DETAIL_SAMPLE_QUANTITY_NOT_NULL.getMsg());
            Assert.notNull(detail.getQualifiedQuantity(),
                    RECEIVE_BILL_DETAIL_QUALIFIED_QUANTITY_NOT_NULL.getMsg());
            Assert.notNull(detail.getUnqualifiedQuantity(),
                    RECEIVE_BILL_DETAIL_UNQUALIFIED_QUANTITY_NOT_NULL.getMsg());
            Assert.notNull(detail.getAcceptConclusion(), RECEIVE_BILL_DETAIL_ACCEPT_CONCLUSION_NOT_NULL.getMsg());

            // 不合格数量大于0时，不合格原因和处理措施必填
            Optional.of(detail.getUnqualifiedQuantity().compareTo(BigDecimal.ZERO) > 0).ifPresent(conclusion -> {
                Assert.notEmpty(detail.getUnqualifiedReason(),
                        RECEIVE_BILL_DETAIL_UNQUALIFIED_REASON_NOT_NULL.getMsg());
                Assert.notEmpty(detail.getTreatment(),
                        RECEIVE_BILL_DETAIL_TREATMENT_NOT_NULL.getMsg());
            });
        }
    }

    /**
     * 校验入库明细数据完整性
     */
    public void validateWarehousingDetails() {
        for (ReceiveBillDetailSaveReqVO detail : this.getDetails()) {
            Assert.notNull(detail.getQualifiedPositionGuid(),
                    RECEIVE_BILL_DETAIL_QUALIFIED_POSITION_NOT_NULL.getMsg());
            Assert.notNull(detail.getUnqualifiedPositionGuid(),
                    RECEIVE_BILL_DETAIL_UNQUALIFIED_POSITION_NOT_NULL.getMsg());
        }
    }

    /**
     * 校验入库明细数据完整性
     */
    public void validateChecker() {
        Assert.notNull(this.getChecker(),
                RECEIVE_BILL_CHECKER_NOT_NULL.getMsg());
    }

}