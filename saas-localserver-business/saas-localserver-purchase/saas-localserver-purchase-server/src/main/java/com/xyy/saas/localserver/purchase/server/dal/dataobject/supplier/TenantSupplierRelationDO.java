package com.xyy.saas.localserver.purchase.server.dal.dataobject.supplier;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 租户供应商关系 DO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@TableName("saas_purchase_tenant_supplier_relation")
// @KeySequence("saas_purchase_tenant_supplier_relation_seq") // 用于
// Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantSupplierRelationDO extends TenantBaseDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 供应商编号 */
    private String supplierGuid;

    /** 首营状态：1-审核中，2-审核通过，3-审核未通过 */
    private Integer status;
}