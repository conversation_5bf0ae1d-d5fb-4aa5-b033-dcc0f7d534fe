package com.xyy.saas.localserver.purchase.server.dal.dataobject.purchase;

import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import lombok.*;
import java.time.LocalDate;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 采购明细 DO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@TableName("saas_purchase_bill_detail")
// @KeySequence("saas_purchase_bill_detail_seq") // 用于
// Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseBillDetailDO extends BaseDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 计划单号 */
    private String planBillNo;

    /** 采购单号 */
    private String purchaseBillNo;

    /** 商品编码 */
    private String productPref;

    /** 批号 */
    private String lotNo;

    /** 生产日期（YYYY-MM-DD） */
    private LocalDate productionDate;

    /** 有效期至（YYYY-MM-DD） */
    private LocalDate expiryDate;

    /** （合格）存储区编号 */
    private String positionGuid;

    /** 进项税率（百分比） */
    private BigDecimal inTaxRate;

    /** 销项税率（百分比） */
    private BigDecimal outTaxRate;

    /** 采购单价 */
    private BigDecimal price;

    /** 采购总金额 */
    private BigDecimal purchaseAmount;

    /** 采购数量 */
    private BigDecimal purchaseQuantity;

    /** 已发货数量 */
    private BigDecimal deliveredQuantity;

    /** 可退数量（原单退货扣减可退数量，下游收货单的实际入库数量） */
    private BigDecimal returnableQuantity;

    /** 渠道ID */
    private String channelId;

    /** 源行号（和三方ERP对接时需要） */
    private String sourceLineNo;

    /** 医保项目编码 */
    private String medicareProjectCode;

    /** 医保项目名称 */
    private String medicareProjectName;

    /** 医保项目等级 */
    private Integer medicareProjectLevel;

    /** 医保最小包装数量 */
    private Integer medicareMinPackageNum;

    /** 扩展信息（当前商品信息） */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ExtDTO ext;

    /** 备注 */
    private String remark;
}