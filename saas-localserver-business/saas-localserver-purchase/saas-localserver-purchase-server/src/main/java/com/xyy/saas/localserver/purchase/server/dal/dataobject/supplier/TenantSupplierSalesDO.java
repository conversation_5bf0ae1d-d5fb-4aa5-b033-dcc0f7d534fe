package com.xyy.saas.localserver.purchase.server.dal.dataobject.supplier;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import lombok.*;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 租户-供应商-销售人员信息 DO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@TableName("saas_purchase_tenant_supplier_sales")
// @KeySequence("saas_purchase_tenant_supplier_sales_seq") // 用于
// Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TenantSupplierSalesDO extends TenantBaseDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 供应商编号 */
    private String supplierGuid;

    /** 销售人员姓名 */
    private String salesName;

    /** 授权区域 */
    private String authorizedArea;

    /** 授权书号 */
    private String authorizationNum;

    /** 授权书号有效期 */
    private LocalDateTime authorizationNumExpirationDate;

    /** 手机号码 */
    private String phoneNumber;

    /** 授权信息 */
    private String authorizedVarieties;

    /** 身份证号 */
    private String idCard;

    /** 身份证有效期 */
    private LocalDateTime idCardExpirationDate;

    /** 身份证附件 */
    private String idCardAttachment;

    /** 授权书附件 */
    private String authorizationAttachment;

    /** 经营范围 */
    private String authorizedScope;

}