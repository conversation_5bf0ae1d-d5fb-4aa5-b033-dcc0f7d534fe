package com.xyy.saas.localserver.purchase.server.admin.purchase.vo;

import cn.hutool.core.lang.Assert;
import com.xyy.saas.localserver.purchase.enums.purchase.PurchaseBillStatusEnum;
import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseTransportSaveReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import org.hibernate.validator.constraints.Length;
import java.time.LocalDateTime;
import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.*;
import static com.xyy.saas.localserver.purchase.enums.purchase.PurchaseBillImportModeEnum.*;

/**
 * 管理后台 - 采购单新增/修改 Request VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 采购单新增/修改 Request VO")
@Data
public class PurchaseBillSaveReqVO {

    /** 主键ID */
    @Schema(description = "主键ID", example = "12103")
    private Long id;

    /** 租户本地主键ID */
    @Schema(description = "租户本地主键ID", example = "16461")
    private Long lsId;

    /** 计划单号 */
    @Schema(description = "计划单号")
    private String planBillNo;

    /** 采购单号 */
    @Schema(description = "采购单号")
    private String purchaseBillNo;

    /** 商城订单号 */
    @Schema(description = "商城订单号")
    private String mallOrderNo;

    /** 租户类型 */
    @Schema(description = "租户类型", example = "1")
    private Integer tenantType;

    /** 总部租户ID */
    @Schema(description = "总部租户ID", example = "29907")
    private Long headTenantId;

    /** 入库租户ID */
    @Schema(description = "入库租户ID", example = "10271")
    private Long inboundTenantId;

    /** 出库租户ID */
    @Schema(description = "出库租户ID", example = "10271")
    private Long outboundTenantId;

    /** 综合单据号 */
    @Schema(description = "综合单据号")
    private String compositeBillNo;

    /** 单据类型 */
    @Schema(description = "单据类型")
    private Integer billType;

    /** 导入方式 */
    @Schema(description = "导入方式")
    private Integer importMode = PURCHASE_PLAN.getCode();

    /** 是否远程收货 */
    @Schema(description = "是否远程收货")
    private Boolean remoteReceived = false;

    /** 已提交 */
    @Schema(description = "已提交", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "提交状态不能为空")
    private Boolean submitted = false;

    /** 采购状态 */
    @Schema(description = "采购状态", example = "2")
    private Integer status = PurchaseBillStatusEnum.PENDING_APPROVAL.getCode();

    /** 药品类型 */
    @Schema(description = "药品类型", example = "1")
    private Integer medicineType;

    /** 采购方式 */
    @Schema(description = "采购方式")
    private Integer purchaseMode = 1;

    /** 供应商编码 */
    @Schema(description = "供应商编码", example = "30225")
    private String supplierGuid;

    /** 供应商名称 */
    @Schema(description = "供应商名称", example = "张三")
    private String supplierName;

    /** 采购内容 */
    @Schema(description = "采购内容")
    private String purchaseContent;

    /** 采购总数量 */
    @Schema(description = "采购总数量")
    private BigDecimal purchaseQuantity;

    /** 采购金额 */
    @Schema(description = "采购金额")
    private BigDecimal purchaseAmount;

    /** 已发货数量 */
    @Schema(description = "已发货数量")
    private BigDecimal deliveredQuantity;

    /** 可退总数量 */
    @Schema(description = "可退总数量")
    private BigDecimal returnableQuantity;

    /** 商品种类 */
    @Schema(description = "商品种类")
    private Integer productKind;

    /** 计划员 */
    @Schema(description = "计划员")
    private String planner;

    /** 计划时间 */
    @Schema(description = "计划时间")
    private LocalDateTime planTime = LocalDateTime.now();

    /** 采购员 */
    @Schema(description = "采购员", requiredMode = Schema.RequiredMode.REQUIRED)
    private String purchaser;

    /** 采购时间 */
    @Schema(description = "采购时间")
    private LocalDateTime purchaseTime = LocalDateTime.now();

    /** 复核员 */
    @Schema(description = "复核员")
    private String checker;

    /** 复核时间 */
    @Schema(description = "复核时间")
    private LocalDateTime checkTime = LocalDateTime.now();

    /** 收货人 */
    @Schema(description = "收货人")
    private String receiver;

    /** 收货人电话 */
    @Schema(description = "收货人电话")
    private String receiverPhone;

    /** 收货人所在区域 */
    @Schema(description = "收货人所在区域")
    private String receiverArea;

    /** 收货人地址 */
    @Schema(description = "收货人地址")
    private String receiverAddress;

    /** 备注 */
    @Schema(description = "备注", example = "随便")
    @Length(max = 200, message = "备注长度不能超过 {max}")
    private String remark;

    /** 版本 */
    @Schema(description = "版本")
    private Integer version;

    /** 采购单明细 */
    @Schema(description = "采购单明细", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "采购单明细不能为空")
    private List<PurchaseBillDetailSaveReqVO> details;

    /** 采购扩展信息-运输信息 */
    @Schema(description = "采购扩展信息-运输信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private PurchaseTransportSaveReqVO transport;

    /**
     * 校验收货信息
     */
    public void validateReceiveInfo() {
        Assert.notBlank(this.getReceiver(), PURCHASE_BILL_RECEIVER_NOT_NULL.getMsg());
        Assert.notBlank(this.getReceiverPhone(), PURCHASE_BILL_RECEIVER_PHONE__NOT_NULL.getMsg());
        Assert.notBlank(this.getReceiverArea(), PURCHASE_BILL_RECEIVER_AREA_NOT_NULL.getMsg());
        Assert.notBlank(this.getReceiverAddress(), PURCHASE_BILL_RECEIVER_ADDRESS_NOT_NULL.getMsg());
    }

    /**
     * 校验入库门店
     */
    public void validateInBoundStore() {
        Assert.notNull(this.getInboundTenantId(), PURCHASE_BILL_INBOUND_TENANT_ID_NOT_NULL.getMsg());
    }

    /**
     * 校验入库门店
     */
    public void validateOutboundStore() {
        Assert.notNull(this.getOutboundTenantId(), PURCHASE_BILL_OUTBOUND_TENANT_ID_NOT_NULL.getMsg());
    }

    /**
     * 校验供应商信息
     */
    public void validateSupplier() {
        for (PurchaseBillDetailSaveReqVO detail : this.getDetails()) {
            Assert.notNull(detail.getSupplierGuid(), PURCHASE_BILL_SUPPLIER_NOT_EXISTS.getMsg());
            Assert.notNull(detail.getSupplierName(), PURCHASE_BILL_SUPPLIER_NOT_EXISTS.getMsg());
        }
    }
}