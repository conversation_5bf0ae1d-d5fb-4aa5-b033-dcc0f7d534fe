package com.xyy.saas.localserver.purchase.server.admin.purchase.vo;

import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.time.LocalDate;
import jakarta.validation.constraints.*;
import org.hibernate.validator.constraints.Length;

import java.math.BigDecimal;

/**
 * 管理后台 - 采购明细新增/修改 Request VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 采购明细新增/修改 Request VO")
@Data
public class PurchaseBillDetailSaveReqVO {

    /** 主键ID */
    @Schema(description = "主键ID", example = "16976")
    private Long id;

    /** 计划单号 */
    @Schema(description = "计划单号")
    private String planBillNo;

    /** 采购单号 */
    @Schema(description = "采购单号")
    private String purchaseBillNo;

    /** 商品编码 */
    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品编码不能为空")
    private String productPref;

    /** 批号 */
    @Schema(description = "批号")
    private String lotNo;

    /** 生产日期（YYYY-MM-DD） */
    @Schema(description = "生产日期")
    private LocalDate productionDate;

    /** 有效期至（YYYY-MM-DD） */
    @Schema(description = "有效期至")
    private LocalDate expiryDate;

    /** （合格）存储区编号 */
    @Schema(description = "存储区编号")
    private String positionGuid;

    /** 供应商编码 */
    @Schema(description = "供应商编码", example = "30225")
    private String supplierGuid;

    /** 供应商名称 */
    @Schema(description = "供应商名称", example = "张三")
    private String supplierName;

    /** 进项税率（百分比） */
    @Schema(description = "进项税率")
    @Digits(integer = 2, fraction = 2, message = "进项税率应为0.01-99.99之间")
    @DecimalMin(value = "0.01", message = "进项税率应为0.01-99.99之间")
    private BigDecimal inTaxRate;

    /** 销项税率（百分比） */
    @Schema(description = "销项税率")
    @Digits(integer = 2, fraction = 2, message = "销项税率应为0.01-99.99之间")
    @DecimalMin(value = "0.01", message = "销项税率应为0.01-99.99之间")
    private BigDecimal outTaxRate;

    /** 采购单价 */
    @Schema(description = "采购单价", example = "12482")
    @Digits(integer = 6, fraction = 4, message = "采购/要货 单价应为0.0001-999999.9999之间")
    @DecimalMin(value = "0.0001", message = "采购/要货 单价应为0.0001-999999.9999之间")
    private BigDecimal price;

    /** 采购总金额 */
    @Schema(description = "采购总金额")
    @Digits(integer = 12, fraction = 2, message = "采购/要货 总金额应为0.01-999999999999.99之间")
    @DecimalMin(value = "0.01", message = "采购/要货 总金额应为0.01-999999999999.99之间")
    private BigDecimal purchaseAmount;

    /** 采购数量 */
    @Schema(description = "采购数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "采购数量不能为空")
    @Digits(integer = 6, fraction = 2, message = "采购/要货 数量应为0.01-999999.99之间")
    @DecimalMin(value = "0.01", message = "采购/要货 数量应为0.01-999999.99之间")
    private BigDecimal purchaseQuantity;

    /** 已发货数量 */
    @Schema(description = "已发货数量")
    private BigDecimal deliveredQuantity;

    /** 可退数量（原单退货扣减可退数量，下游收货单的实际入库数量） */
    @Schema(description = "可退数量")
    private BigDecimal returnableQuantity;

    /** 渠道ID */
    @Schema(description = "渠道ID", example = "8991")
    private String channelId;

    /** 源行号（和三方ERP对接时需要） */
    @Schema(description = "源行号")
    private String sourceLineNo;

    /** 医保项目编码 */
    @Schema(description = "医保项目编码")
    private String medicareProjectCode;

    /** 医保项目名称 */
    @Schema(description = "医保项目名称", example = "芋艿")
    private String medicareProjectName;

    /** 医保项目等级 */
    @Schema(description = "医保项目等级")
    private Integer medicareProjectLevel;

    /** 医保最小包装数量 */
    @Schema(description = "医保最小包装数量")
    private Integer medicareMinPackageNum;

    /** 扩展信息（当前商品信息） */
    @Schema(description = "扩展信息")
    private ExtDTO ext;

    /** 备注 */
    @Schema(description = "备注", example = "你说的对")
    @Length(max = 200, message = "备注长度不能超过 {max}")
    private String remark;

}