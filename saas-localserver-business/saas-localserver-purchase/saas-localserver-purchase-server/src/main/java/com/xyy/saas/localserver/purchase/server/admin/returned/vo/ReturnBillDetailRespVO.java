package com.xyy.saas.localserver.purchase.server.admin.returned.vo;

import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import com.alibaba.excel.annotation.*;
import java.time.LocalDate;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 管理后台 - 退货单明细 Response VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 退货单明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ReturnBillDetailRespVO {

    /** 主键ID */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24583")
    @ExcelProperty("主键ID")
    private Long id;

    /** 单号 */
    @Schema(description = "单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单号")
    private String billNo;

    /** 商品编码 */
    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编码")
    private String productPref;

    /** 批号 */
    @Schema(description = "批号")
    @ExcelProperty("批号")
    private String lotNo;

    /** 生产日期 */
    @Schema(description = "生产日期")
    @ExcelProperty("生产日期")
    private LocalDate productionDate;

    /** 有效期至 */
    @Schema(description = "有效期至")
    @ExcelProperty("有效期至")
    private LocalDate expiryDate;

    /** 总部货位编号 */
    @Schema(description = "总部货位编号")
    @ExcelProperty("总部货位编号")
    private String positionGuid;

    /** 税率 */
    @Schema(description = "税率")
    @ExcelProperty("税率")
    private BigDecimal inTaxRate;

    /** 含税成本价 */
    @Schema(description = "含税成本价", example = "26185")
    @ExcelProperty("含税成本价")
    private BigDecimal price;

    /** 销项税率 */
    @Schema(description = "销项税率")
    @ExcelProperty("销项税率")
    private BigDecimal outTaxRate;

    /** 出库数量 */
    @Schema(description = "出库数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("出库数量")
    private BigDecimal outboundQuantity;

    /** 成本均价 */
    @Schema(description = "成本均价", example = "14464")
    @ExcelProperty("成本均价")
    private BigDecimal outboundPrice;

    /** 含税成本金额 */
    @Schema(description = "含税成本金额")
    @ExcelProperty("含税成本金额")
    private BigDecimal outboundAmount;

    /** 渠道ID */
    @Schema(description = "渠道ID", example = "24434")
    @ExcelProperty("渠道ID")
    private String channelId;

    /** 医保项目编码 */
    @Schema(description = "医保项目编码")
    @ExcelProperty("医保项目编码")
    private String medicareProjectCode;

    /** 医保项目名称 */
    @Schema(description = "医保项目名称", example = "王五")
    @ExcelProperty("医保项目名称")
    private String medicareProjectName;

    /** 医保项目等级 */
    @Schema(description = "医保项目等级", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("医保项目等级")
    private Integer medicareProjectLevel;

    /** 医保最小包装数量 */
    @Schema(description = "医保最小包装数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("医保最小包装数量")
    private Integer medicareMinPackageNum;

    /** 扩展信息 */
    @Schema(description = "扩展信息")
    @ExcelProperty("扩展信息")
    private ExtDTO ext;

    /** 退货原因 */
    @Schema(description = "退货原因(1-破损、2-召回、3-滞销、4-过期失效、5-近效期、6-质量问题、7-其他)", example = "不香")
    @ExcelProperty("退货原因")
    private Integer returnReason;

    /** 备注 */
    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    /** 创建时间 */
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;
}