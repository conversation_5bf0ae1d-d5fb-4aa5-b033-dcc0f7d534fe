package com.xyy.saas.localserver.purchase.server.admin.stockout;

import com.xyy.saas.localserver.purchase.api.stockout.dto.StockoutBillDTO;
import com.xyy.saas.localserver.purchase.api.stockout.dto.StockoutBillPageReqDTO;
import com.xyy.saas.localserver.purchase.server.admin.stockout.vo.StockoutBillPageReqVO;
import com.xyy.saas.localserver.purchase.server.admin.stockout.vo.StockoutBillRespVO;
import com.xyy.saas.localserver.purchase.server.admin.stockout.vo.StockoutBillSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.stockout.StockoutBillConvert;
import com.xyy.saas.localserver.purchase.server.service.stockout.StockoutBillService;
import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

/**
 * 管理后台 - 缺货单信息 Controller
 * 处理缺货单信息的创建、更新、删除、查询等操作
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "管理后台 - 缺货单信息")
@RestController
@RequestMapping("/saas/purchase/stockout-bill")
@Validated
public class StockoutBillController {

    @Resource
    private StockoutBillService stockoutBillService;

    /**
     * 创建缺货单信息
     * 处理流程：
     * 1. 对象转换：将VO对象转换为DTO对象
     * 2. 调用服务：执行创建操作
     *
     * @param createReqVO 创建信息
     * @return 创建结果
     */
    @PostMapping("/create")
    @Operation(summary = "创建缺货单信息")
    @PreAuthorize("@ss.hasPermission('saas:purchase:stockout-bill:create')")
    public CommonResult<Long> createStockoutBill(@Valid @RequestBody StockoutBillSaveReqVO createReqVO) {
        // 1. 对象转换：将VO对象转换为DTO对象
        StockoutBillDTO createDTO = StockoutBillConvert.INSTANCE.convert2DTO(createReqVO);
        // 2. 调用服务：执行创建操作
        return success(stockoutBillService.createStockoutBill(createDTO));
    }

    /**
     * 更新缺货单信息
     * 处理流程：
     * 1. 对象转换：将VO对象转换为DTO对象
     * 2. 调用服务：执行更新操作
     *
     * @param updateReqVO 更新信息
     * @return 更新结果
     */
    @PutMapping("/update")
    @Operation(summary = "更新缺货单信息")
    @PreAuthorize("@ss.hasPermission('saas:purchase:stockout-bill:update')")
    public CommonResult<Boolean> updateStockoutBill(@Valid @RequestBody StockoutBillSaveReqVO updateReqVO) {
        // 1. 对象转换：将VO对象转换为DTO对象
        StockoutBillDTO updateDTO = StockoutBillConvert.INSTANCE.convert2DTO(updateReqVO);
        // 2. 调用服务：执行更新操作
        stockoutBillService.updateStockoutBill(updateDTO);
        return success(true);
    }

    /**
     * 删除缺货单信息
     * 处理流程：
     * 1. 调用服务：执行删除操作
     *
     * @param id 编号
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    @Operation(summary = "删除缺货单信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:purchase:stockout-bill:delete')")
    public CommonResult<Boolean> deleteStockoutBill(@RequestParam("id") Long id) {
        // 调用服务：执行删除操作
        stockoutBillService.deleteStockoutBill(id);
        return success(true);
    }

    /**
     * 获取缺货单信息
     * 处理流程：
     * 1. 调用服务：获取缺货单信息
     * 2. 对象转换：将DTO对象转换为VO对象
     *
     * @param id 编号
     * @return 缺货单信息
     */
    @GetMapping("/get")
    @Operation(summary = "获得缺货单信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:purchase:stockout-bill:query')")
    public CommonResult<StockoutBillRespVO> getStockoutBill(@RequestParam("id") Long id) {
        // 1. 调用服务：获取缺货单信息
        StockoutBillDTO stockoutBill = stockoutBillService.getStockoutBill(id);
        // 2. 对象转换：将DTO对象转换为VO对象
        return success(StockoutBillConvert.INSTANCE.convert2VO(stockoutBill));
    }

    /**
     * 获取缺货单信息分页
     * 处理流程：
     * 1. 对象转换：将VO对象转换为DTO对象
     * 2. 调用服务：获取分页数据
     * 3. 对象转换：将DTO对象转换为VO对象
     *
     * @param pageReqVO 分页查询参数
     * @return 分页结果
     */
    @GetMapping("/page")
    @Operation(summary = "获得缺货单信息分页")
    @PreAuthorize("@ss.hasPermission('saas:purchase:stockout-bill:query')")
    public CommonResult<PageResult<StockoutBillRespVO>> getStockoutBillPage(@Valid StockoutBillPageReqVO pageReqVO) {
        // 1. 对象转换：将VO对象转换为DTO对象
        StockoutBillPageReqDTO pageReqDTO = StockoutBillConvert.INSTANCE.convert2DTO(pageReqVO);
        // 2. 调用服务：获取分页数据
        PageResult<StockoutBillDTO> pageResult = stockoutBillService.getStockoutBillPage(pageReqDTO);
        // 3. 对象转换：将DTO对象转换为VO对象
        return success(StockoutBillConvert.INSTANCE.convert2VO(pageResult));
    }

    /**
     * 导出缺货单信息Excel
     * 处理流程：
     * 1. 设置分页大小：设置为不分页
     * 2. 对象转换：将VO对象转换为DTO对象
     * 3. 调用服务：获取数据列表
     * 4. 导出Excel：将数据导出为Excel文件
     *
     * @param pageReqVO 分页查询参数
     * @param response  HTTP响应对象
     * @throws IOException 导出过程中的IO异常
     */
    @GetMapping("/export-excel")
    @Operation(summary = "导出缺货单信息 Excel")
    @PreAuthorize("@ss.hasPermission('saas:purchase:stockout-bill:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportStockoutBillExcel(@Valid StockoutBillPageReqVO pageReqVO,
            HttpServletResponse response) throws IOException {
        // 1. 设置分页大小：设置为不分页
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        // 2. 对象转换：将VO对象转换为DTO对象
        StockoutBillPageReqDTO pageReqDTO = StockoutBillConvert.INSTANCE.convert2DTO(pageReqVO);
        // 3. 调用服务：获取数据列表
        List<StockoutBillDTO> list = stockoutBillService.getStockoutBillPage(pageReqDTO).getList();
        // 4. 导出Excel：将数据导出为Excel文件
        ExcelUtils.write(response, "缺货单信息.xls", "数据", StockoutBillRespVO.class,
                StockoutBillConvert.INSTANCE.convert2VOList(list));
    }
}