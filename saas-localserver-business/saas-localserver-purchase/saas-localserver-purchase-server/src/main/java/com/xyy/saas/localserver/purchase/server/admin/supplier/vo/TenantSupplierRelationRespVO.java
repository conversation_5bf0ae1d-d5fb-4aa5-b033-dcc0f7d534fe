package com.xyy.saas.localserver.purchase.server.admin.supplier.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

/**
 * 管理后台 - 租户-供应商-关联关系 Response VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 租户-供应商-关联关系 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TenantSupplierRelationRespVO {

    /** 主键ID */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "17200")
    @ExcelProperty("主键ID")
    private Long id;

    /** 供应商编号 */
    @Schema(description = "供应商编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("供应商编号")
    private String supplierGuid;

    /** 首营状态 */
    @Schema(description = "首营状态：1-审核中，2-审核通过，3-审核未通过", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("首营状态：1-审核中，2-审核通过，3-审核未通过")
    private Integer status;

    /** 创建时间 */
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}