package com.xyy.saas.localserver.purchase.server.admin.supplier.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 租户-供应商-销售人员信息分页 Request VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 租户-供应商-销售人员信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TenantSupplierSalesPageReqVO extends PageParam {

    /** 供应商编号 */
    @Schema(description = "供应商编号")
    private String supplierGuid;

    /** 销售人员姓名 */
    @Schema(description = "销售人员姓名", example = "芋艿")
    private String salesName;

    /** 授权区域 */
    @Schema(description = "授权区域")
    private String authorizedArea;

    /** 授权书号 */
    @Schema(description = "授权书号")
    private String authorizationNum;

    /** 授权书号有效期 */
    @Schema(description = "授权书号有效期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] authorizationNumExpirationDate;

    /** 手机号码 */
    @Schema(description = "手机号码")
    private String phoneNumber;

    /** 授权信息 */
    @Schema(description = "授权信息")
    private String authorizedVarieties;

    /** 身份证号 */
    @Schema(description = "身份证号")
    private String idCard;

    /** 身份证有效期 */
    @Schema(description = "身份证有效期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] idCardExpirationDate;

    /** 身份证附件 */
    @Schema(description = "身份证附件")
    private String idCardAttachment;

    /** 授权书附件 */
    @Schema(description = "授权书附件")
    private String authorizationAttachment;

    /** 经营范围 */
    @Schema(description = "经营范围")
    private String authorizedScope;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
}