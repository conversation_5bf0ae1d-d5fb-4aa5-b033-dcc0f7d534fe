//package com.xyy.saas.localserver.purchase.server.admin.returned;
//
//import com.xyy.saas.localserver.purchase.server.admin.returned.vo.ReturnBillDetailPageReqVO;
//import com.xyy.saas.localserver.purchase.server.admin.returned.vo.ReturnBillDetailRespVO;
//import com.xyy.saas.localserver.purchase.server.admin.returned.vo.ReturnBillDetailSaveReqVO;
//import com.xyy.saas.localserver.purchase.server.dal.dataobject.returned.ReturnBillDetailDO;
//import com.xyy.saas.localserver.purchase.server.service.returned.ReturnBillDetailService;
//import org.springframework.web.bind.annotation.*;
//import jakarta.annotation.Resource;
//import org.springframework.validation.annotation.Validated;
//import org.springframework.security.access.prepost.PreAuthorize;
//import io.swagger.v3.oas.annotations.tags.Tag;
//import io.swagger.v3.oas.annotations.Parameter;
//import io.swagger.v3.oas.annotations.Operation;
//import jakarta.validation.*;
//import jakarta.servlet.http.*;
//import java.util.*;
//import java.io.IOException;
//import cn.iocoder.yudao.framework.common.pojo.PageParam;
//import cn.iocoder.yudao.framework.common.pojo.PageResult;
//import cn.iocoder.yudao.framework.common.pojo.CommonResult;
//import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
//import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
//import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
//import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
//import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;
//
//@Tag(name = "管理后台 - （单体/总部/门店）退货单明细")
//@RestController
//@RequestMapping("/saas/purchase/purchase-return-bill-detail")
//@Validated
//public class ReturnBillDetailController {
//
//    @Resource
//    private ReturnBillDetailService returnBillDetailService;
//
//    @PostMapping("/create")
//    @Operation(summary = "创建（单体/总部/门店）退货单明细")
//    @PreAuthorize("@ss.hasPermission('saas:purchase-return-bill-detail:create')")
//    public CommonResult<Long> createPurchaseReturnBillDetail(@Valid @RequestBody ReturnBillDetailSaveReqVO createReqVO) {
//        return success(returnBillDetailService.createPurchaseReturnBillDetail(createReqVO));
//    }
//
//    @PutMapping("/update")
//    @Operation(summary = "更新（单体/总部/门店）退货单明细")
//    @PreAuthorize("@ss.hasPermission('saas:purchase-return-bill-detail:update')")
//    public CommonResult<Boolean> updatePurchaseReturnBillDetail(@Valid @RequestBody ReturnBillDetailSaveReqVO updateReqVO) {
//        returnBillDetailService.updatePurchaseReturnBillDetail(updateReqVO);
//        return success(true);
//    }
//
//    @DeleteMapping("/delete")
//    @Operation(summary = "删除（单体/总部/门店）退货单明细")
//    @Parameter(name = "id", description = "编号", required = true)
//    @PreAuthorize("@ss.hasPermission('saas:purchase-return-bill-detail:delete')")
//    public CommonResult<Boolean> deletePurchaseReturnBillDetail(@RequestParam("id") Long id) {
//        returnBillDetailService.deletePurchaseReturnBillDetail(id);
//        return success(true);
//    }
//
//    @GetMapping("/get")
//    @Operation(summary = "获得（单体/总部/门店）退货单明细")
//    @Parameter(name = "id", description = "编号", required = true, example = "1024")
//    @PreAuthorize("@ss.hasPermission('saas:purchase-return-bill-detail:query')")
//    public CommonResult<ReturnBillDetailRespVO> getPurchaseReturnBillDetail(@RequestParam("id") Long id) {
//        ReturnBillDetailDO purchaseReturnBillDetail = returnBillDetailService.getPurchaseReturnBillDetail(id);
//        return success(BeanUtils.toBean(purchaseReturnBillDetail, ReturnBillDetailRespVO.class));
//    }
//
//    @GetMapping("/page")
//    @Operation(summary = "获得（单体/总部/门店）退货单明细分页")
//    @PreAuthorize("@ss.hasPermission('saas:purchase-return-bill-detail:query')")
//    public CommonResult<PageResult<ReturnBillDetailRespVO>> getPurchaseReturnBillDetailPage(@Valid ReturnBillDetailPageReqVO pageReqVO) {
//        PageResult<ReturnBillDetailDO> pageResult = returnBillDetailService.getPurchaseReturnBillDetailPage(pageReqVO);
//        return success(BeanUtils.toBean(pageResult, ReturnBillDetailRespVO.class));
//    }
//
//    @GetMapping("/export-excel")
//    @Operation(summary = "导出（单体/总部/门店）退货单明细 Excel")
//    @PreAuthorize("@ss.hasPermission('saas:purchase-return-bill-detail:export')")
//    @ApiAccessLog(operateType = EXPORT)
//    public void exportPurchaseReturnBillDetailExcel(@Valid ReturnBillDetailPageReqVO pageReqVO,
//              HttpServletResponse response) throws IOException {
//        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
//        List<ReturnBillDetailDO> list = returnBillDetailService.getPurchaseReturnBillDetailPage(pageReqVO).getList();
//        // 导出 Excel
//        ExcelUtils.write(response, "（单体/总部/门店）退货单明细.xls", "数据", ReturnBillDetailRespVO.class,
//                        BeanUtils.toBean(list, ReturnBillDetailRespVO.class));
//    }
//
//}