// package com.xyy.saas.localserver.purchase.server.admin.purchase;
//
// import
// com.xyy.saas.localserver.purchase.server.admin.purchase.vo.PurchaseBillPageReqVO;
// import
// com.xyy.saas.localserver.purchase.server.admin.purchase.vo.PurchaseBillRespVO;
// import
// com.xyy.saas.localserver.purchase.server.admin.purchase.vo.PurchaseBillSaveReqVO;
// import
// com.xyy.saas.localserver.purchase.server.convert.purchase.PurchaseBillConvert;
// import
// com.xyy.saas.localserver.purchase.server.dal.dataobject.purchase.PurchaseBillDO;
// import
// com.xyy.saas.localserver.purchase.server.service.purchase.PurchaseBillService;
// import org.springframework.web.bind.annotation.*;
// import jakarta.annotation.Resource;
// import org.springframework.validation.annotation.Validated;
// import org.springframework.security.access.prepost.PreAuthorize;
// import io.swagger.v3.oas.annotations.tags.Tag;
// import io.swagger.v3.oas.annotations.Parameter;
// import io.swagger.v3.oas.annotations.Operation;
// import jakarta.validation.*;
// import jakarta.servlet.http.*;
// import java.util.*;
// import java.io.IOException;
// import cn.iocoder.yudao.framework.common.pojo.PageParam;
// import cn.iocoder.yudao.framework.common.pojo.PageResult;
// import cn.iocoder.yudao.framework.common.pojo.CommonResult;
// import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
//
// import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;
// import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
// import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
// import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;
//
// @Tag(name = "管理后台 - 采购单")
// @RestController
// @RequestMapping("/saas/purchase/purchase-bill")
// @Validated
// public class PurchaseBillController {
//
// @Resource
// private PurchaseBillService purchaseBillService;
//
// @PostMapping("/savePlan")
// @Operation(summary = "保存采购计划")
// @PreAuthorize("@ss.hasPermission('saas:purchase-bill:savePlan')")
// public CommonResult<Boolean> savePlan(@Valid @RequestBody
// PurchaseBillSaveReqVO saveReqVO) {
//
// // 对象转换
// PurchaseBillDO purchaseBill =
// PurchaseBillConvert.INSTANCE.convert2PlanDO(saveReqVO);
//
// //保存采购计划
// purchaseBillService.savePurchaseBill(purchaseBill);
//
// return success(true);
// }
//
// @PostMapping("/createOrder")
// @Operation(summary = "保存采购订单")
// @PreAuthorize("@ss.hasPermission('saas:purchase-bill:saveOrder')")
// public CommonResult<Boolean> saveOrder(@Valid @RequestBody
// PurchaseBillSaveReqVO saveReqVO) {
//
// // 对象转换
// PurchaseBillDO purchaseBill =
// PurchaseBillConvert.INSTANCE.convert2OrderDO(saveReqVO);
//
// //保存采购订单
// purchaseBillService.savePurchaseBill(purchaseBill);
//
// return success(true);
// }
//
// @PostMapping("/saveRequisition")
// @Operation(summary = "保存要货单")
// @PreAuthorize("@ss.hasPermission('saas:purchase-bill:saveRequisition')")
// public CommonResult<Boolean> saveRequisition(@Valid @RequestBody
// PurchaseBillSaveReqVO saveReqVO) {
//
// // 对象转换
// PurchaseBillDO purchaseBill =
// PurchaseBillConvert.INSTANCE.convert2RequisitionDO(saveReqVO);
//
// //保存门店要货单
// purchaseBillService.savePurchaseBill(purchaseBill);
//
// return success(true);
// }
//
// @PostMapping("/saveAllocation")
// @Operation(summary = "保存调剂单")
// @PreAuthorize("@ss.hasPermission('saas:purchase-bill:saveAllocation')")
// public CommonResult<Boolean> saveAllocation(@Valid @RequestBody
// PurchaseBillSaveReqVO saveReqVO) {
//
// // 对象转换
// PurchaseBillDO purchaseBill =
// PurchaseBillConvert.INSTANCE.convert2AllocationDO(saveReqVO);
//
// //保存门店调剂单
// purchaseBillService.savePurchaseBill(purchaseBill);
//
// return success(true);
// }
//
// @PostMapping("/saveDistribution")
// @Operation(summary = "保存配送单（主配）")
// @PreAuthorize("@ss.hasPermission('saas:purchase-bill:saveDistribution')")
// public CommonResult<Boolean> saveDistribution(@Valid @RequestBody
// PurchaseBillSaveReqVO saveReqVO) {
//
// // 对象转换
// PurchaseBillDO purchaseBill =
// PurchaseBillConvert.INSTANCE.convert2DistributionDO(saveReqVO);
//
// //保存配送单
// purchaseBillService.savePurchaseBill(purchaseBill);
//
// return success(true);
// }
//
//// @PutMapping("/update")
//// @Operation(summary = "更新采购单")
//// @PreAuthorize("@ss.hasPermission('saas:purchase-bill:update')")
//// public CommonResult<Boolean> updatePurchaseBill(@Valid @RequestBody
// PurchaseBillSaveReqVO updateReqVO) {
//// purchaseBillService.updatePurchaseBill(updateReqVO);
//// return success(true);
//// }
//
// @DeleteMapping("/delete")
// @Operation(summary = "删除采购单")
// @Parameter(name = "id", description = "编号", required = true)
// @PreAuthorize("@ss.hasPermission('saas:purchase-bill:delete')")
// public CommonResult<Boolean> deletePurchaseBill(@RequestParam("id") Long id)
// {
// purchaseBillService.deletePurchaseBill(id);
// return success(true);
// }
//
// @GetMapping("/get")
// @Operation(summary = "获得采购单")
// @Parameter(name = "id", description = "编号", required = true, example =
// "1024")
// @PreAuthorize("@ss.hasPermission('saas:purchase-bill:query')")
// public CommonResult<PurchaseBillRespVO> getPurchaseBill(@RequestParam("id")
// Long id) {
// PurchaseBillDO purchaseBill = purchaseBillService.getPurchaseBill(id);
// return success(BeanUtils.toBean(purchaseBill, PurchaseBillRespVO.class));
// }
//
// @GetMapping("/page")
// @Operation(summary = "获得采购单分页")
// @PreAuthorize("@ss.hasPermission('saas:purchase-bill:query')")
// public CommonResult<PageResult<PurchaseBillRespVO>>
// getPurchaseBillPage(@Valid PurchaseBillPageReqVO pageReqVO) {
// PageResult<PurchaseBillDO> pageResult =
// purchaseBillService.getPurchaseBillPage(pageReqVO);
// return success(BeanUtils.toBean(pageResult, PurchaseBillRespVO.class));
// }
//
// @GetMapping("/export-excel")
// @Operation(summary = "导出采购单 Excel")
// @PreAuthorize("@ss.hasPermission('saas:purchase-bill:export')")
// @ApiAccessLog(operateType = EXPORT)
// public void exportPurchaseBillExcel(@Valid PurchaseBillPageReqVO pageReqVO,
// HttpServletResponse response) throws IOException {
// pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
// List<PurchaseBillDO> list =
// purchaseBillService.getPurchaseBillPage(pageReqVO).getList();
// // 导出 Excel
// ExcelUtils.write(response, "采购单.xls", "数据", PurchaseBillRespVO.class,
// BeanUtils.toBean(list, PurchaseBillRespVO.class));
// }
// }