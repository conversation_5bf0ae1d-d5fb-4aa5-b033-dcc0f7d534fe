package com.xyy.saas.localserver.purchase.server.dal.dataobject.extend;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 采购-发票信息 DO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@TableName("saas_purchase_invoice")
// @KeySequence("saas_purchase_invoice_seq") // 用于
// Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseInvoiceDO extends TenantBaseDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 采购单/收货单/配送单号 */
    private String billNo;

    /** 发票号 */
    private String invoiceNo;

    /** 发票代码 */
    private String invoiceCode;

    /** 发票类型：1-电子普通发票、2-增值税发票、3-纸质普通发票（用于委托配送） */
    private String invoiceType;

    /** 发票是否随货通行：0-不随行、1-随行（用于委托配送） */
    private Boolean accompanyingShipment;

    /** 发票金额 */
    private BigDecimal invoiceAmount;

    /** 发票文件名称 */
    private String invoiceFileName;

    /** 发票文件地址 */
    private String invoiceFileUrl;

    /** 实际开(发)票时间 */
    private LocalDateTime issuedTime;

    /** 备注 */
    private String remark;

}