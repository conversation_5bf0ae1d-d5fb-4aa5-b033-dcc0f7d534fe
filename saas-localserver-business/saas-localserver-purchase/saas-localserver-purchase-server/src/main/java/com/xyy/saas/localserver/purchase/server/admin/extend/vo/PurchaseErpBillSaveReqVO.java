package com.xyy.saas.localserver.purchase.server.admin.extend.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

/**
 * 管理后台 - 采购-三方erp单据信息新增/修改 Request VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 采购-三方erp单据信息新增/修改 Request VO")
@Data
public class PurchaseErpBillSaveReqVO {

    /** 主键ID */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18630")
    @NotNull(message = "主键ID不能为空")
    private Long id;

    /** 采购单/收货单号 */
    @Schema(description = "采购单/收货单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "采购单/收货单号不能为空")
    private String billNo;

    /** 三方erp订单号 */
    @Schema(description = "三方erp订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "三方erp订单号不能为空")
    private String erpBillNo;

    /** 三方erp销售单号 */
    @Schema(description = "三方erp销售单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "三方erp销售单号不能为空")
    private String salesBillNo;

    /** 三方erp出库单号 */
    @Schema(description = "三方erp出库单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "三方erp出库单号不能为空")
    private String outboundBillNo;

    /** 三方erp入库单号 */
    @Schema(description = "三方erp入库单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "三方erp入库单号不能为空")
    private String warehouseBillNo;

    /** 三方erp销售退回入库单号(神农XSTHRK) */
    @Schema(description = "三方erp销售退回入库单号(神农XSTHRK)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "三方erp销售退回入库单号(神农XSTHRK)不能为空")
    private String refundStorageBillNo;

    /** 综合单据号（单号混合） */
    @Schema(description = "综合单据号（单号混合）")
    private String compositeBillNo;

    @Schema(description = "三方erp取消原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "不香")
    @NotEmpty(message = "三方erp取消原因不能为空")
    private String cancelReason;

    @Schema(description = "备注", example = "你猜")
    private String remark;

}