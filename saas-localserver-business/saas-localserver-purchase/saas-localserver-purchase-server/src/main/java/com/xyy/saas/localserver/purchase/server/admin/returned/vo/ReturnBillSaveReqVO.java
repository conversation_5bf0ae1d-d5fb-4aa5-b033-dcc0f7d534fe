package com.xyy.saas.localserver.purchase.server.admin.returned.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 管理后台 - 退货单新增/修改 Request VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 退货单新增/修改 Request VO")
@Data
public class ReturnBillSaveReqVO {

    /** 主键ID */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24583")
    private Long id;

    /** 单号 */
    @Schema(description = "单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "单号不能为空")
    private String billNo;

    /** 采购单号 */
    @Schema(description = "采购单号")
    private String purchaseBillNo;

    /** 商城订单号 */
    @Schema(description = "商城订单号")
    private String mallOrderNo;

    /** 入库门店租户ID */
    @Schema(description = "入库门店租户ID", example = "2524")
    @NotNull(message = "入库门店租户ID不能为空")
    private Long inboundTenantId;

    /** 租户类型 */
    @Schema(description = "租户类型（1-单体门店、2-连锁门店、3-连锁总部）", example = "1")
    @NotNull(message = "租户类型不能为空")
    private Integer tenantType;

    /** 总部租户ID */
    @Schema(description = "总部租户ID", example = "2524")
    @NotNull(message = "总部租户ID不能为空")
    private Long headTenantId;

    /** 综合单据号 */
    @Schema(description = "综合单据号")
    private String compositeBillNo;

    /** 单据类型 */
    @Schema(description = "单据类型（1-采购退货、2-门店退货、3-门店调剂）")
    @NotNull(message = "单据类型不能为空")
    private Integer billType;

    /** 状态 */
    @Schema(description = "状态（1-待审批、2-待出库、3-待复核、4-已出库、5-已驳回、6-已撤销）", example = "2")
    @NotNull(message = "状态不能为空")
    private Integer status;

    /** 已提交 */
    @Schema(description = "已提交", example = "false")
    @NotNull(message = "已提交不能为空")
    private Boolean submitted;

    /** 供应商编码 */
    @Schema(description = "供应商编码")
    @NotEmpty(message = "供应商编码不能为空")
    private String supplierGuid;

    /** 供应商名称 */
    @Schema(description = "供应商名称", example = "李四")
    @NotEmpty(message = "供应商名称不能为空")
    private String supplierName;

    /** 商品种类 */
    @Schema(description = "商品种类")
    @NotNull(message = "商品种类不能为空")
    private Integer productKind;

    /** 退货内容 */
    @Schema(description = "退货内容")
    @NotEmpty(message = "退货内容不能为空")
    private String returnContent;

    /** 退货数量 */
    @Schema(description = "退货数量")
    @NotNull(message = "退货数量不能为空")
    @Digits(integer = 6, fraction = 2, message = "退货数量应为0.01-999999.99之间")
    @DecimalMin(value = "0.01", message = "退货数量应为0.01-999999.99之间")
    private BigDecimal returnQuantity;

    /** 退货总金额 */
    @Schema(description = "退货总金额")
    @NotNull(message = "退货总金额不能为空")
    @Digits(integer = 12, fraction = 2, message = "退货总金额应为0.01-999999999999.99之间")
    @DecimalMin(value = "0.01", message = "退货总金额应为0.01-999999999999.99之间")
    private BigDecimal returnAmount;

    /** 成本总金额 */
    @Schema(description = "成本总金额")
    @NotNull(message = "成本总金额不能为空")
    @Digits(integer = 12, fraction = 2, message = "成本总金额应为0.01-999999999999.99之间")
    @DecimalMin(value = "0.01", message = "成本总金额应为0.01-999999999999.99之间")
    private BigDecimal costAmount;

    /** 出库操作员 */
    @Schema(description = "出库操作员")
    private String operator;

    /** 出库时间 */
    @Schema(description = "出库时间")
    private LocalDateTime operateTime;

    /** 复核员 */
    @Schema(description = "复核员")
    private String checker;

    /** 复核时间 */
    @Schema(description = "复核时间")
    private LocalDateTime checkTime;

    /** 备注 */
    @Schema(description = "备注", example = "你说的对")
    private String remark;

    /** 版本 */
    @Schema(description = "版本")
    @NotNull(message = "版本不能为空")
    private Integer version;

    @Schema(description = "采购退货单明细", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "采购退货单明细不能为空")
    private List<ReturnBillDetailSaveReqVO> details;

}