package com.xyy.saas.localserver.purchase.server.dal.dataobject.returned;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import lombok.*;
import java.time.LocalDate;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 退货单明细 DO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@TableName("saas_purchase_return_bill_detail")
//@KeySequence("saas_purchase_return_bill_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReturnBillDetailDO extends TenantBaseDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 单号 */
    private String billNo;

    /** 商品编码 */
    private String productPref;

    /** 批号 */
    private String lotNo;

    /** 生产日期（YYYY-MM-DD） */
    private LocalDate productionDate;

    /** 有效期至（YYYY-MM-DD） */
    private LocalDate expiryDate;

    /** 总部（合格）货位编号 */
    private String positionGuid;

    /** 供应商编码 */
    private String supplierGuid;

    /** 供应商名称 */
    private String supplierName;

    /** 税率（百分比） */
    private BigDecimal inTaxRate;

    /** 含税成本价（单价） */
    private BigDecimal price;

    /** 销项税率 */
    private BigDecimal outTaxRate;

    /** 出库数量 */
    private BigDecimal outboundQuantity;

    /** 成本均价 */
    private BigDecimal outboundPrice;

    /** 含税成本金额（总金额） */
    private BigDecimal outboundAmount;

    /** 渠道ID */
    private String channelId;

    /** 医保项目编码 */
    private String medicareProjectCode;

    /** 医保项目名称 */
    private String medicareProjectName;

    /** 医保项目等级 */
    private Integer medicareProjectLevel;

    /** 医保最小包装数量 */
    private Integer medicareMinPackageNum;

    /** 扩展信息（当前商品信息） */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ExtDTO ext;

    /** 退货原因(1-破损、2-召回、3-滞销、4-过期失效、5-近效期、6-质量问题、7-其他) */
    private Integer returnReason;

    /** 备注 */
    private String remark;

}