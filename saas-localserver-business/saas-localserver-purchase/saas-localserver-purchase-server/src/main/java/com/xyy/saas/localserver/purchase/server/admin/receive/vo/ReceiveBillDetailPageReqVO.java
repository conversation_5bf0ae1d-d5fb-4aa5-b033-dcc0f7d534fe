package com.xyy.saas.localserver.purchase.server.admin.receive.vo;

import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import lombok.*;

import java.time.LocalDate;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 收货单明细分页 Request VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 收货单明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ReceiveBillDetailPageReqVO extends PageParam {

    /** 验收入库单号 */
    @Schema(description = "验收入库单号")
    private String billNo;

    /** 商品编码 */
    @Schema(description = "商品编码")
    private String productPref;

    /** 批号 */
    @Schema(description = "批号")
    private String lotNo;

    /** 生产日期 */
    @Schema(description = "生产日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] productionDate;

    /** 有效期至 */
    @Schema(description = "有效期至")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] expiryDate;

    /** 含税成本价 */
    @Schema(description = "含税成本价", example = "7855")
    private BigDecimal price;

    /** 税率 */
    @Schema(description = "税率")
    private BigDecimal taxRate;

    /** 折扣 */
    @Schema(description = "折扣", example = "20050")
    private BigDecimal discount;

    /** 折后含税单价 */
    @Schema(description = "折后含税单价", example = "28936")
    private BigDecimal discountedPrice;

    /** 到货数量 */
    @Schema(description = "到货数量")
    private BigDecimal arriveQuantity;

    /** 收货数量 */
    @Schema(description = "收货数量")
    private BigDecimal receiveQuantity;

    /** 拒收数量 */
    @Schema(description = "拒收数量")
    private BigDecimal rejectQuantity;

    /** 收货金额 */
    @Schema(description = "收货金额")
    private BigDecimal receiveAmount;

    /** 验收结论 */
    @Schema(description = "验收结论")
    private Boolean acceptConclusion;

    /** 抽样数量 */
    @Schema(description = "抽样数量")
    private BigDecimal sampleQuantity;

    /** 不合格品数量 */
    @Schema(description = "不合格品数量")
    private BigDecimal unqualifiedQuantity;

    /** 不合格品总金额 */
    @Schema(description = "不合格品总金额")
    private BigDecimal unqualifiedAmount;

    /** 不合格原因 */
    @Schema(description = "不合格原因", example = "不对")
    private String unqualifiedReason;

    /** 不合格品隔离区编码 */
    @Schema(description = "不合格品隔离区编码", example = "26747")
    private String unqualifiedPositionGuid;

    /** 合格品数量 */
    @Schema(description = "合格品数量")
    private BigDecimal qualifiedQuantity;

    /** 合格品总金额 */
    @Schema(description = "合格品总金额")
    private BigDecimal qualifiedAmount;

    /** 合格品储存区编码 */
    @Schema(description = "合格品储存区编码", example = "11633")
    private String qualifiedPositionGuid;

    /** 入库数量 */
    @Schema(description = "入库数量")
    private BigDecimal warehouseQuantity;

    /** 入库金额 */
    @Schema(description = "入库金额")
    private BigDecimal warehouseAmount;

    /** 处理措施 */
    @Schema(description = "处理措施")
    private String treatment;

    /** 渠道ID */
    @Schema(description = "渠道ID", example = "16653")
    private String channelId;

    /** 灭菌批次 */
    @Schema(description = "灭菌批次")
    private String sterilizationBatchNo;

    /** 源行号 */
    @Schema(description = "源行号")
    private String sourceLineNo;

    /** 医保项目编码 */
    @Schema(description = "医保项目编码")
    private String medicareProjectCode;

    /** 医保项目名称 */
    @Schema(description = "医保项目名称", example = "王五")
    private String medicareProjectName;

    /** 医保项目等级 */
    @Schema(description = "医保项目等级")
    private Integer medicareProjectLevel;

    /** 医保最小包装数量 */
    @Schema(description = "医保最小包装数量")
    private Integer medicareMinPackageNum;

    /** 扩展信息 */
    @Schema(description = "扩展信息")
    private ExtDTO ext;

    /** 备注 */
    @Schema(description = "备注", example = "随便")
    private String remark;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
}