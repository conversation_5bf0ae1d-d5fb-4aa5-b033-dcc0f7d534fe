package com.xyy.saas.localserver.purchase.server.admin.extend;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseTransportDTO;
import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseTransportPageReqDTO;
import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseTransportPageReqVO;
import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseTransportRespVO;
import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseTransportSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.extend.PurchaseTransportConvert;
import com.xyy.saas.localserver.purchase.server.service.extend.PurchaseTransportService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;

/**
 * 管理后台 - 采购运输信息 Controller
 * 处理采购运输信息的创建、更新、删除、查询等操作
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "管理后台 - 采购运输信息")
@RestController
@RequestMapping("/saas/purchase/transport")
@Validated
public class PurchaseTransportController {

    @Resource
    private PurchaseTransportService purchaseTransportService;

    /**
     * 创建采购运输信息
     * 处理流程：
     * 1. 对象转换：将VO对象转换为DTO对象
     * 2. 调用服务：执行创建操作
     *
     * @param createReqVO 创建信息
     * @return 创建结果
     */
    @PostMapping("/create")
    @Operation(summary = "创建采购运输信息")
    @PreAuthorize("@ss.hasPermission('saas:purchase:transport:create')")
    public CommonResult<Long> createPurchaseTransport(@Valid @RequestBody PurchaseTransportSaveReqVO createReqVO) {
        // 1. 对象转换：将VO对象转换为DTO对象
        PurchaseTransportDTO createDTO = PurchaseTransportConvert.INSTANCE.convert2DTO(createReqVO);
        // 2. 调用服务：执行创建操作
        return success(purchaseTransportService.createPurchaseTransport(createDTO));
    }

    /**
     * 更新采购运输信息
     * 处理流程：
     * 1. 对象转换：将VO对象转换为DTO对象
     * 2. 调用服务：执行更新操作
     *
     * @param updateReqVO 更新信息
     * @return 更新结果
     */
    @PutMapping("/update")
    @Operation(summary = "更新采购运输信息")
    @PreAuthorize("@ss.hasPermission('saas:purchase:transport:update')")
    public CommonResult<Boolean> updatePurchaseTransport(@Valid @RequestBody PurchaseTransportSaveReqVO updateReqVO) {
        // 1. 对象转换：将VO对象转换为DTO对象
        PurchaseTransportDTO updateDTO = PurchaseTransportConvert.INSTANCE.convert2DTO(updateReqVO);
        // 2. 调用服务：执行更新操作
        purchaseTransportService.updatePurchaseTransport(updateDTO);
        return success(true);
    }

    /**
     * 删除采购运输信息
     * 处理流程：
     * 1. 调用服务：执行删除操作
     *
     * @param id 编号
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    @Operation(summary = "删除采购运输信息")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:purchase:transport:delete')")
    public CommonResult<Boolean> deletePurchaseTransport(@RequestParam("id") Long id) {
        // 调用服务：执行删除操作
        purchaseTransportService.deletePurchaseTransport(id);
        return success(true);
    }

    /**
     * 获取采购运输信息
     * 处理流程：
     * 1. 调用服务：获取运输信息
     * 2. 对象转换：将DTO对象转换为VO对象
     *
     * @param id 编号
     * @return 运输信息
     */
    @GetMapping("/get")
    @Operation(summary = "获得采购运输信息")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:purchase:transport:query')")
    public CommonResult<PurchaseTransportRespVO> getPurchaseTransport(@RequestParam("id") Long id) {
        // 1. 调用服务：获取运输信息
        PurchaseTransportDTO transport = purchaseTransportService.getPurchaseTransport(id);
        // 2. 对象转换：将DTO对象转换为VO对象
        return success(PurchaseTransportConvert.INSTANCE.convert2VO(transport));
    }

    /**
     * 获取采购运输信息分页
     * 处理流程：
     * 1. 对象转换：将VO对象转换为DTO对象
     * 2. 调用服务：获取分页数据
     * 3. 对象转换：将DTO对象转换为VO对象
     *
     * @param pageVO 分页查询参数
     * @return 分页结果
     */
    @GetMapping("/page")
    @Operation(summary = "获得采购运输信息分页")
    @PreAuthorize("@ss.hasPermission('saas:purchase:transport:query')")
    public CommonResult<PageResult<PurchaseTransportRespVO>> getPurchaseTransportPage(
            @Valid PurchaseTransportPageReqVO pageVO) {
        // 1. 对象转换：将VO对象转换为DTO对象
        PurchaseTransportPageReqDTO pageReqDTO = PurchaseTransportConvert.INSTANCE.convert2DTO(pageVO);
        // 2. 调用服务：获取分页数据
        PageResult<PurchaseTransportDTO> pageResult = purchaseTransportService.getPurchaseTransportPage(pageReqDTO);
        // 3. 对象转换：将DTO对象转换为VO对象
        return success(PurchaseTransportConvert.INSTANCE.convert2VO(pageResult));
    }

    /**
     * 导出采购运输信息Excel
     * 处理流程：
     * 1. 设置分页大小：设置为不分页
     * 2. 对象转换：将VO对象转换为DTO对象
     * 3. 调用服务：获取数据列表
     * 4. 导出Excel：将数据导出为Excel文件
     *
     * @param pageVO   分页查询参数
     * @param response HTTP响应对象
     * @throws IOException 导出过程中的IO异常
     */
    @GetMapping("/export-excel")
    @Operation(summary = "导出采购运输信息 Excel")
    @PreAuthorize("@ss.hasPermission('saas:purchase:transport:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportPurchaseTransportExcel(@Valid PurchaseTransportPageReqVO pageVO,
            HttpServletResponse response) throws IOException {
        // 1. 设置分页大小：设置为不分页
        pageVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        // 2. 对象转换：将VO对象转换为DTO对象
        PurchaseTransportPageReqDTO pageReqDTO = PurchaseTransportConvert.INSTANCE.convert2DTO(pageVO);
        // 3. 调用服务：获取数据列表
        List<PurchaseTransportDTO> list = purchaseTransportService.getPurchaseTransportPage(pageReqDTO).getList();
        // 4. 导出Excel：将数据导出为Excel文件
        ExcelUtils.write(response, "采购运输信息.xls", "数据", PurchaseTransportRespVO.class,
                PurchaseTransportConvert.INSTANCE.convert2VOList(list));
    }
}