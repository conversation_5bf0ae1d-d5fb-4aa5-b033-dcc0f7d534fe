package com.xyy.saas.localserver.purchase.server.dal.mysql.supplier;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.localserver.purchase.api.supplier.dto.TenantSupplierRelationPageReqDTO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.supplier.TenantSupplierRelationDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 租户-供应商-关联关系 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface TenantSupplierRelationMapper extends BaseMapperX<TenantSupplierRelationDO> {

    /**
     * 获得租户-供应商-关联关系分页
     *
     * @param reqDTO 分页参数
     * @return 租户-供应商-关联关系分页
     */
    default PageResult<TenantSupplierRelationDO> selectPage(TenantSupplierRelationPageReqDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<TenantSupplierRelationDO>()
                .eqIfPresent(TenantSupplierRelationDO::getSupplierGuid, reqDTO.getSupplierGuid())
                .eqIfPresent(TenantSupplierRelationDO::getStatus, reqDTO.getStatus())
                .betweenIfPresent(TenantSupplierRelationDO::getCreateTime, reqDTO.getCreateTime())
                .orderByDesc(TenantSupplierRelationDO::getId));
    }

    /**
     * 根据供应商GUID查询
     * @param supplierGuid 供应商GUID
     * @return 租户-供应商-关联关系
     */
    default TenantSupplierRelationDO selectBySupplierGuid(String supplierGuid) {
        return selectOne(new LambdaQueryWrapperX<TenantSupplierRelationDO>()
                .eq(TenantSupplierRelationDO::getSupplierGuid, supplierGuid));
    }

}