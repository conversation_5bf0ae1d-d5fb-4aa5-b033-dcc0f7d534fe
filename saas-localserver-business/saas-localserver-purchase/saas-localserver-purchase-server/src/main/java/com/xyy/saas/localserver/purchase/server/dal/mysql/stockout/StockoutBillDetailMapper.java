package com.xyy.saas.localserver.purchase.server.dal.mysql.stockout;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.localserver.purchase.api.stockout.dto.StockoutBillDetailPageReqDTO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.stockout.StockoutBillDetailDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * （总部）缺货单明细 Mapper
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface StockoutBillDetailMapper extends BaseMapperX<StockoutBillDetailDO> {

    /**
     * 获得（总部）缺货单明细分页
     *
     * @param reqDTO 分页查询参数
     * @return （总部）缺货单明细分页
     */
    default PageResult<StockoutBillDetailDO> selectPage(StockoutBillDetailPageReqDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<StockoutBillDetailDO>()
                .eqIfPresent(StockoutBillDetailDO::getBillNo, reqDTO.getBillNo())
                .eqIfPresent(StockoutBillDetailDO::getProductPref, reqDTO.getProductPref())
                .eqIfPresent(StockoutBillDetailDO::getRequireQuantity, reqDTO.getRequireQuantity())
                .eqIfPresent(StockoutBillDetailDO::getStockoutQuantity, reqDTO.getStockoutQuantity())
                .eqIfPresent(StockoutBillDetailDO::getMedicareProjectCode, reqDTO.getMedicareProjectCode())
                .likeIfPresent(StockoutBillDetailDO::getMedicareProjectName, reqDTO.getMedicareProjectName())
                .eqIfPresent(StockoutBillDetailDO::getMedicareProjectLevel, reqDTO.getMedicareProjectLevel())
                .eqIfPresent(StockoutBillDetailDO::getMedicareMinPackageNum, reqDTO.getMedicareMinPackageNum())
                .eqIfPresent(StockoutBillDetailDO::getExt, reqDTO.getExt())
                .eqIfPresent(StockoutBillDetailDO::getRemark, reqDTO.getRemark())
                .betweenIfPresent(StockoutBillDetailDO::getCreateTime, reqDTO.getCreateTime())
                .orderByDesc(StockoutBillDetailDO::getId));
    }

}