package com.xyy.saas.localserver.purchase.server.dal.mysql.purchase;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.localserver.purchase.server.admin.purchase.vo.PurchaseBillDetailPageReqVO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.purchase.PurchaseBillDetailDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 采购明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PurchaseBillDetailMapper extends BaseMapperX<PurchaseBillDetailDO> {

    default PageResult<PurchaseBillDetailDO> selectPage(PurchaseBillDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<PurchaseBillDetailDO>()
                .eqIfPresent(PurchaseBillDetailDO::getPlanBillNo, reqVO.getPlanBillNo())
                .eqIfPresent(PurchaseBillDetailDO::getPurchaseBillNo, reqVO.getPurchaseBillNo())
                .eqIfPresent(PurchaseBillDetailDO::getProductPref, reqVO.getProductPref())
                .eqIfPresent(PurchaseBillDetailDO::getLotNo, reqVO.getLotNo())
                .betweenIfPresent(PurchaseBillDetailDO::getProductionDate, reqVO.getProductionDate())
                .betweenIfPresent(PurchaseBillDetailDO::getExpiryDate, reqVO.getExpiryDate())
                .eqIfPresent(PurchaseBillDetailDO::getPositionGuid, reqVO.getPositionGuid())
                .eqIfPresent(PurchaseBillDetailDO::getInTaxRate, reqVO.getInTaxRate())
                .eqIfPresent(PurchaseBillDetailDO::getOutTaxRate, reqVO.getOutTaxRate())
                .eqIfPresent(PurchaseBillDetailDO::getPrice, reqVO.getPrice())
                .eqIfPresent(PurchaseBillDetailDO::getPurchaseAmount, reqVO.getPurchaseAmount())
                .eqIfPresent(PurchaseBillDetailDO::getPurchaseQuantity, reqVO.getPurchaseQuantity())
                .eqIfPresent(PurchaseBillDetailDO::getDeliveredQuantity, reqVO.getDeliveredQuantity())
                .eqIfPresent(PurchaseBillDetailDO::getReturnableQuantity, reqVO.getReturnableQuantity())
                .eqIfPresent(PurchaseBillDetailDO::getChannelId, reqVO.getChannelId())
                .eqIfPresent(PurchaseBillDetailDO::getSourceLineNo, reqVO.getSourceLineNo())
                .eqIfPresent(PurchaseBillDetailDO::getMedicareProjectCode, reqVO.getMedicareProjectCode())
                .likeIfPresent(PurchaseBillDetailDO::getMedicareProjectName, reqVO.getMedicareProjectName())
                .eqIfPresent(PurchaseBillDetailDO::getMedicareProjectLevel, reqVO.getMedicareProjectLevel())
                .eqIfPresent(PurchaseBillDetailDO::getMedicareMinPackageNum, reqVO.getMedicareMinPackageNum())
                .eqIfPresent(PurchaseBillDetailDO::getExt, reqVO.getExt())
                .eqIfPresent(PurchaseBillDetailDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(PurchaseBillDetailDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(PurchaseBillDetailDO::getId));
    }

    /**
     * 根据计划单号删除采购单详情
     *
     * @param planBillNo 计划单号
     */
    default void deleteByPlanBillNo(String planBillNo) {
        delete(new LambdaQueryWrapperX<PurchaseBillDetailDO>()
                .eq(PurchaseBillDetailDO::getPlanBillNo, planBillNo));
    }

}