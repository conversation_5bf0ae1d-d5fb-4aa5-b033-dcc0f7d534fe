package com.xyy.saas.localserver.purchase.server.dal.mysql.extend;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseTransportPageReqDTO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.extend.PurchaseTransportDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 采购-运输信息 Mapper
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface PurchaseTransportMapper extends BaseMapperX<PurchaseTransportDO> {

    /**
     * 获得采购-运输信息分页
     *
     * @param reqDTO 分页查询参数
     * @return 采购-运输信息分页
     */
    default PageResult<PurchaseTransportDO> selectPage(PurchaseTransportPageReqDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<PurchaseTransportDO>()
                .eqIfPresent(PurchaseTransportDO::getBillNo, reqDTO.getBillNo())
                .eqIfPresent(PurchaseTransportDO::getAuthorizedRepresentative, reqDTO.getAuthorizedRepresentative())
                .likeIfPresent(PurchaseTransportDO::getCarrierName, reqDTO.getCarrierName())
                .eqIfPresent(PurchaseTransportDO::getCarrierEntity, reqDTO.getCarrierEntity())
                .eqIfPresent(PurchaseTransportDO::getCarrierEntityUscc, reqDTO.getCarrierEntityUscc())
                .eqIfPresent(PurchaseTransportDO::getTransportMode, reqDTO.getTransportMode())
                .eqIfPresent(PurchaseTransportDO::getDepartureAddress, reqDTO.getDepartureAddress())
                .betweenIfPresent(PurchaseTransportDO::getDepartureTime, reqDTO.getDepartureTime())
                .eqIfPresent(PurchaseTransportDO::getDepartureTemperature, reqDTO.getDepartureTemperature())
                .eqIfPresent(PurchaseTransportDO::getDepartureHumidity, reqDTO.getDepartureHumidity())
                .eqIfPresent(PurchaseTransportDO::getTrackingNo, reqDTO.getTrackingNo())
                .eqIfPresent(PurchaseTransportDO::getTransportVehicle, reqDTO.getTransportVehicle())
                .eqIfPresent(PurchaseTransportDO::getVehicleIdentifier, reqDTO.getVehicleIdentifier())
                .eqIfPresent(PurchaseTransportDO::getDriver, reqDTO.getDriver())
                .eqIfPresent(PurchaseTransportDO::getDriverCredential, reqDTO.getDriverCredential())
                .eqIfPresent(PurchaseTransportDO::getTransportTemperatureMonitor,
                        reqDTO.getTransportTemperatureMonitor())
                .eqIfPresent(PurchaseTransportDO::getTransportHumidityMonitor, reqDTO.getTransportHumidityMonitor())
                .betweenIfPresent(PurchaseTransportDO::getArrivalTime, reqDTO.getArrivalTime())
                .eqIfPresent(PurchaseTransportDO::getArrivalTemperature, reqDTO.getArrivalTemperature())
                .eqIfPresent(PurchaseTransportDO::getArrivalHumidity, reqDTO.getArrivalHumidity())
                .betweenIfPresent(PurchaseTransportDO::getShipmentTime, reqDTO.getShipmentTime())
                .eqIfPresent(PurchaseTransportDO::getShipmentFileUrl, reqDTO.getShipmentFileUrl())
                .eqIfPresent(PurchaseTransportDO::getRemark, reqDTO.getRemark())
                .betweenIfPresent(PurchaseTransportDO::getCreateTime, reqDTO.getCreateTime())
                .orderByDesc(PurchaseTransportDO::getId));
    }

    /**
     * 根据单号删除采购-运输信息
     *
     * @param billNo 单号
     */
    default void deleteByBillNo(String billNo) {
        delete(new LambdaQueryWrapperX<PurchaseTransportDO>()
                .eq(PurchaseTransportDO::getBillNo, billNo));
    }
}