package com.xyy.saas.localserver.purchase.server.admin.purchase.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

/**
 * 管理后台 - 采购单 Response VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 采购单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PurchaseBillRespVO {

    /** 主键ID */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12103")
    @ExcelProperty("主键ID")
    private Long id;

    /** 计划单号 */
    @Schema(description = "计划单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("计划单号")
    private String planBillNo;

    /** 采购单号 */
    @Schema(description = "采购单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("采购单号")
    private String purchaseBillNo;

    /** 商城订单号 */
    @Schema(description = "商城订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商城订单号")
    private String mallOrderNo;

    /** 租户类型 */
    @Schema(description = "租户类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("租户类型")
    private Integer tenantType;

    /** 总部租户ID */
    @Schema(description = "总部租户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "29907")
    @ExcelProperty("总部租户ID")
    private Long headTenantId;

    /** 入库租户ID */
    @Schema(description = "入库租户ID", example = "10271")
    private Long inboundTenantId;

    /** 入库租户 */
    @Schema(description = "入库租户", example = "10271")
    @ExcelProperty("入库门店")
    private Long inboundTenantName;

    /** 出库租户ID */
    @Schema(description = "出库租户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10271")
    private Long outboundTenantId;

    /** 出库租户 */
    @Schema(description = "出库租户", example = "10271")
    @ExcelProperty("出库门店")
    private Long outboundTenantName;

    /** 综合单据号 */
    @Schema(description = "综合单据号")
    @ExcelProperty("综合单据号")
    private String compositeBillNo;

    /** 单据来源 */
    @Schema(description = "单据来源", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单据来源")
    private Integer billType;

    /** 导入方式 */
    @Schema(description = "导入方式", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("导入方式")
    private Integer importMode;

    /** 是否远程收货 */
    @Schema(description = "是否远程收货", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否远程收货")
    private Boolean remoteReceived;

    /** 已提交 */
    @Schema(description = "已提交", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("已提交")
    private Boolean submitted;

    /** 采购状态 */
    @Schema(description = "采购状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private Integer status;

    /** 药品类型 */
    @Schema(description = "药品类型", example = "1")
    @ExcelProperty("药品类型")
    private Integer medicineType;

    /** 采购方式 */
    @Schema(description = "采购方式", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("采购方式")
    private Integer purchaseMode;

    /** 供应商编码 */
    @Schema(description = "供应商编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "30225")
    @ExcelProperty("供应商编码")
    private String supplierGuid;

    /** 供应商名称 */
    @Schema(description = "供应商名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("供应商名称")
    private String supplierName;

    /** 采购内容 */
    @Schema(description = "采购内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("采购内容")
    private String purchaseContent;

    /** 采购总数量 */
    @Schema(description = "采购总数量")
    @ExcelProperty("采购总数量")
    private BigDecimal purchaseQuantity;

    /** 采购金额 */
    @Schema(description = "采购金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("采购金额")
    private BigDecimal purchaseAmount;

    /** 已发货数量 */
    @Schema(description = "已发货数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("已发货数量")
    private BigDecimal deliveredQuantity;

    /** 可退总数量 */
    @Schema(description = "可退总数量")
    @ExcelProperty("可退总数量")
    private BigDecimal returnableQuantity;

    /** 缺货数量 */
    @Schema(description = "缺货数量")
    private BigDecimal stockoutQuantity;

    /** 商品种类 */
    @Schema(description = "商品种类", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品种类")
    private Integer productKind;

    /** 计划员 */
    @Schema(description = "计划员", requiredMode = Schema.RequiredMode.REQUIRED)
    private String planner;

    /** 计划员名称 */
    @Schema(description = "计划员名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("计划员")
    private String plannerName;

    /** 计划时间 */
    @Schema(description = "计划时间")
    @ExcelProperty("计划时间")
    private LocalDateTime planTime;

    /** 采购员 */
    @Schema(description = "采购员", requiredMode = Schema.RequiredMode.REQUIRED)
    private String purchaser;

    /** 采购员名称 */
    @Schema(description = "采购员名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("采购员")
    private String purchaserName;

    /** 采购时间 */
    @Schema(description = "采购时间")
    @ExcelProperty("采购时间")
    private LocalDateTime purchaseTime;

    /** 复核员 */
    @Schema(description = "复核员", requiredMode = Schema.RequiredMode.REQUIRED)
    private String checker;

    /** 复核员名称 */
    @Schema(description = "复核员名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("复核员")
    private String checkerName;

    /** 复核时间 */
    @Schema(description = "复核时间")
    @ExcelProperty("复核时间")
    private LocalDateTime checkTime;

    /** 收货人 */
    @Schema(description = "收货人", requiredMode = Schema.RequiredMode.REQUIRED)
    private String receiver;

    /** 收货人名称 */
    @Schema(description = "收货人名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收货人名称")
    private String receiverName;

    /** 收货人电话 */
    @Schema(description = "收货人电话", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收货人电话")
    private String receiverPhone;

    /** 收货人所在区域 */
    @Schema(description = "收货人所在区域", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收货人所在区域")
    private String receiverArea;

    /** 收货人地址 */
    @Schema(description = "收货人地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收货人地址")
    private String receiverAddress;

    /** 备注 */
    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    /** 版本 */
    @Schema(description = "版本", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本")
    private Integer version;

    /** 创建时间 */
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    /** 当前待办人 */
    @Schema(description = "当前待办人", requiredMode = Schema.RequiredMode.REQUIRED)
    private String currentAssignee;

    /** 当前待办人名称 */
    @Schema(description = "当前待办人名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("当前待办人")
    private String currentAssigneeName;
}