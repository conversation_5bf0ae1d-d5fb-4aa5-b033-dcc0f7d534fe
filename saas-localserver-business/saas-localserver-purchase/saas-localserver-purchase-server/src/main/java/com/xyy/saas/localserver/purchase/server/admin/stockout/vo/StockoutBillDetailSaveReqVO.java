package com.xyy.saas.localserver.purchase.server.admin.stockout.vo;

import com.alibaba.excel.annotation.ExcelProperty;
import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 管理后台 - 缺货单明细新增/修改 Request VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 缺货单明细新增/修改 Request VO")
@Data
public class StockoutBillDetailSaveReqVO {

    /** 主键ID */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "10161")
    private Long id;

    /** 单号 */
    @Schema(description = "单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "单号不能为空")
    private String billNo;

    /** 商品编码 */
    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品编码不能为空")
    private String productPref;

    /** 要货数量 */
    @Schema(description = "要货数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "要货数量不能为空")
    private BigDecimal requireQuantity;

    /** 缺货数量 */
    @Schema(description = "缺货数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "缺货数量不能为空")
    private BigDecimal stockoutQuantity;

    /** 医保项目编码 */
    @Schema(description = "医保项目编码")
    private String medicareProjectCode;

    /** 医保项目名称 */
    @Schema(description = "医保项目名称", example = "张三")
    private String medicareProjectName;

    /** 医保项目等级 */
    @Schema(description = "医保项目等级", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "医保项目等级不能为空")
    private Integer medicareProjectLevel;

    /** 医保最小包装数量 */
    @Schema(description = "医保最小包装数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "医保最小包装数量不能为空")
    private Integer medicareMinPackageNum;

    /** 扩展信息 */
    @Schema(description = "扩展信息（记录商品信息）")
    @ExcelProperty("扩展信息")
    private ExtDTO ext;

    /** 备注 */
    @Schema(description = "备注", example = "随便")
    private String remark;
}