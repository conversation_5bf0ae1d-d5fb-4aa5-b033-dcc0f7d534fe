package com.xyy.saas.localserver.purchase.server.dal.dataobject.extend;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import org.apache.commons.lang3.StringUtils;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 采购-三方erp单据信息 DO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@TableName(value = "saas_purchase_erp_bill", autoResultMap = true)
// @KeySequence("saas_purchase_erp_bill_seq") // 用于
// Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseErpBillDO extends TenantBaseDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 采购单/收货单/配送单号 */
    private String billNo;

    /** 三方erp订单号 */
    private String erpBillNo;

    /** 三方erp销售单号 */
    private String salesBillNo;

    /** 三方erp出库单号 */
    private String outboundBillNo;

    /** 三方erp入库单号 */
    private String warehouseBillNo;

    /** 三方erp销售退回入库单号(神农XSTHRK) */
    private String refundStorageBillNo;

    /** 综合单据号（单号混合） */
    @TableField(fill = com.baomidou.mybatisplus.annotation.FieldFill.INSERT_UPDATE)
    private String compositeBillNo;

    /** 三方erp取消原因 */
    private String cancelReason;

    /** 备注 */
    private String remark;

    /**
     * 生成综合单据号
     * 规则：billNo|erpBillNo|salesBillNo|outboundBillNo|warehouseBillNo|refundStorageBillNo
     *
     * @return 综合单据号
     */
    public String generateCompositeBillNo() {
        this.compositeBillNo = Stream
                .of(billNo, erpBillNo, salesBillNo, outboundBillNo, warehouseBillNo, refundStorageBillNo)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining("|", "|", "|"));
        return this.compositeBillNo;
    }
}