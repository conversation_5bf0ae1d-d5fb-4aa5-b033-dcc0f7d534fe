package com.xyy.saas.localserver.purchase.server.admin.extend.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 管理后台 - 采购-发票信息新增/修改 Request VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 采购-发票信息新增/修改 Request VO")
@Data
public class PurchaseInvoiceSaveReqVO {

    /** 主键ID */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "23651")
    @NotNull(message = "主键ID不能为空")
    private Long id;

    /** 采购单/收货单/配送单号 */
    @Schema(description = "采购单/收货单/配送单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "采购单/收货单/配送单号不能为空")
    private String billNo;

    /** 发票号 */
    @Schema(description = "发票号")
    private String invoiceNo;

    /** 发票代码 */
    @Schema(description = "发票代码")
    private String invoiceCode;

    /** 发票类型 1：电子普通发票 2：增值税发票 3：纸质普通发票，用于委托配送 */
    @Schema(description = "发票类型 1：电子普通发票 2：增值税发票 3：纸质普通发票，用于委托配送", example = "1")
    private String invoiceType;

    /** 发票是否随货通行 0：不随行 1：随行，用于委托配送 */
    @Schema(description = "发票是否随货通行 0：不随行 1：随行，用于委托配送")
    private Boolean accompanyingShipment;

    /** 发票金额 */
    @Schema(description = "发票金额")
    private BigDecimal invoiceAmount;

    /** 发票文件名称 */
    @Schema(description = "发票文件名称", example = "李四")
    private String invoiceFileName;

    /** 发票文件地址 */
    @Schema(description = "发票文件地址", example = "https://www.iocoder.cn")
    private String invoiceFileUrl;

    /** 实际开(发)票时间 */
    @Schema(description = "实际开(发)票时间")
    private LocalDateTime issuedTime;

    /** 备注 */
    @Schema(description = "备注", example = "你说的对")
    private String remark;

}