package com.xyy.saas.localserver.purchase.server.admin.purchase.vo;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.xyy.saas.localserver.purchase.enums.purchase.PurchaseBillStatusEnum;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Stream;
import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 采购单分页 Request VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 采购单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PurchaseBillPageReqVO extends PageParam {

    /** 计划单号 */
    @Schema(description = "计划单号")
    private String planBillNo;

    /** 采购单号 */
    @Schema(description = "采购单号")
    private String purchaseBillNo;

    /** 商城订单号 */
    @Schema(description = "商城订单号")
    private String mallOrderNo;

    /** 租户类型 */
    @Schema(description = "租户类型", example = "1")
    private Integer tenantType;

    /** 总部租户ID */
    @Schema(description = "总部租户ID", example = "29907")
    private Long headTenantId;

    /** 租户ID */
    @Schema(description = "租户ID", example = "29907")
    private Long tenantId;

    /** 入库租户ID */
    @Schema(description = "入库租户ID", example = "10271")
    private Long inboundTenantId;

    /** 出库租户ID */
    @Schema(description = "出库租户ID", example = "10271")
    private Long outboundTenantId;

    /** 综合单据号 */
    @Schema(description = "综合单据号")
    private String compositeBillNo;

    /** 单据类型 */
    @Schema(description = "单据类型")
    private Integer billType;

    /** 导入方式 */
    @Schema(description = "导入方式")
    private Integer importMode;

    /** 是否远程收货 */
    @Schema(description = "是否远程收货")
    private Boolean remoteReceived;

    /** 已提交 */
    @Schema(description = "已提交")
    private Boolean submitted;

    /** 采购状态 */
    @Schema(description = "采购状态", example = "2")
    private Integer status;

    /** 采购状态范围 */
    @Schema(description = "采购状态范围")
    private List<Integer> statusScope;

    /** 页面显示状态 */
    @Schema(description = "页面显示状态", example = "2")
    private Integer displayStatus;

    /** 页面显示状态名称 */
    @Schema(description = "页面显示状态名称", example = "2")
    private Integer displayStatusName;

    /** 药品类型 */
    @Schema(description = "药品类型", example = "1")
    private Integer medicineType;

    /** 采购方式 */
    @Schema(description = "采购方式")
    private Integer purchaseMode;

    /** 供应商编码 */
    @Schema(description = "供应商编码", example = "30225")
    private String supplierGuid;

    /** 供应商名称 */
    @Schema(description = "供应商名称", example = "张三")
    private String supplierName;

    /** 采购内容 */
    @Schema(description = "采购内容")
    private String purchaseContent;

    /** 采购总数量 */
    @Schema(description = "采购总数量")
    private BigDecimal purchaseQuantity;

    /** 采购金额 */
    @Schema(description = "采购金额")
    private BigDecimal purchaseAmount;

    /** 已发货数量 */
    @Schema(description = "已发货数量")
    private BigDecimal deliveredQuantity;

    /** 可退总数量 */
    @Schema(description = "可退总数量")
    private BigDecimal returnableQuantity;

    /** 商品种类 */
    @Schema(description = "商品种类")
    private Integer productKind;

    /** 计划员 */
    @Schema(description = "计划员")
    private String planner;

    /** 计划时间 */
    @Schema(description = "计划时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] planTime;

    /** 采购员 */
    @Schema(description = "采购员")
    private String purchaser;

    /** 采购时间 */
    @Schema(description = "采购时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] purchaseTime;

    /** 复核员 */
    @Schema(description = "复核员")
    private String checker;

    /** 复核时间 */
    @Schema(description = "复核时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] checkTime;

    /** 收货人 */
    @Schema(description = "收货人")
    private String receiver;

    /** 收货人电话 */
    @Schema(description = "收货人电话")
    private String receiverPhone;

    /** 收货人所在区域 */
    @Schema(description = "收货人所在区域")
    private String receiverArea;

    /** 收货人地址 */
    @Schema(description = "收货人地址")
    private String receiverAddress;

    /** 备注 */
    @Schema(description = "备注", example = "随便")
    private String remark;

    /** 版本 */
    @Schema(description = "版本")
    private Integer version;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

    /**
     * 采购计划显示状态枚举
     */
    @Getter
    public enum PurchasePlanDisplayStatus {
        // 全部状态
        ALL(0, "全部") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                // 不设置任何过滤条件
            }
        },

        // 暂存状态
        TEMPORARY(1, "暂存") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatus(PurchaseBillStatusEnum.PLAN_CREATED.getCode());
                vo.setSubmitted(false);
            }
        },

        // 未执行状态
        UN_EXECUTED(2, "未执行") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatus(PurchaseBillStatusEnum.PLAN_CREATED.getCode());
                vo.setSubmitted(true);
            }
        },

        // 已执行状态
        EXECUTED(3, "已执行") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatusScope(Stream.of(PurchaseBillStatusEnum.values())
                        .map(PurchaseBillStatusEnum::getCode)
                        .filter(statusCode -> !Objects.equals(statusCode,
                                PurchaseBillStatusEnum.PLAN_CREATED.getCode()))
                        .toList());
                vo.setSubmitted(true);
            }
        };

        private final int code;
        private final String name;

        PurchasePlanDisplayStatus(int code, String name) {
            this.code = code;
            this.name = name;
        }

        /**
         * 应用显示状态到查询条件
         * 
         * @param vo 查询条件VO
         */
        public abstract void apply(PurchaseBillPageReqVO vo);

        @JsonCreator
        public static PurchasePlanDisplayStatus fromCode(int code) {
            for (PurchasePlanDisplayStatus status : values()) {
                if (status.code == code) {
                    return status;
                }
            }
            throw new IllegalArgumentException("无效的状态码: " + code);
        }
    }

    /**
     * 采购订单显示状态枚举
     */
    @Getter
    public enum PurchaseOrderDisplayStatus {
        // 全部状态
        ALL(0, "全部") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatusScope(Stream.of(PurchaseBillStatusEnum.values())
                        .filter(status -> !status.equals(PurchaseBillStatusEnum.PLAN_CREATED))
                        .map(PurchaseBillStatusEnum::getCode)
                        .toList());
            }
        },

        // 暂存状态
        TEMPORARY(1, "暂存") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatus(PurchaseBillStatusEnum.PENDING_APPROVAL.getCode());
                vo.setSubmitted(false);
            }
        },

        // 待审批状态
        PENDING_APPROVAL(2, "待审批") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatus(PurchaseBillStatusEnum.PENDING_APPROVAL.getCode());
                vo.setSubmitted(true);
            }
        },

        // 已驳回状态
        REJECTED(3, "已驳回") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatus(PurchaseBillStatusEnum.REJECTED.getCode());
                vo.setSubmitted(true);
            }
        },

        // 采购中状态
        PURCHASING(4, "采购中") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatus(PurchaseBillStatusEnum.APPROVED_PENDING_DELIVERY.getCode());
                vo.setSubmitted(true);
            }
        },

        // 已采购状态
        PURCHASED(5, "已采购") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatusScope(List.of(
                        PurchaseBillStatusEnum.APPROVED_PENDING_DELIVERY.getCode(),
                        PurchaseBillStatusEnum.DELIVERED.getCode(),
                        PurchaseBillStatusEnum.COMPLETED.getCode()));
                vo.setSubmitted(true);
            }
        },

        // 采购失败状态
        PURCHASE_FAILED(6, "采购失败") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatus(PurchaseBillStatusEnum.FAILED.getCode());
                vo.setSubmitted(true);
            }
        },

        // 已完成状态
        COMPLETED(7, "已完成") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatus(PurchaseBillStatusEnum.COMPLETED.getCode());
                vo.setSubmitted(true);
            }
        };

        private final int code;
        private final String name;

        PurchaseOrderDisplayStatus(int code, String name) {
            this.code = code;
            this.name = name;
        }

        /**
         * 应用显示状态到查询条件
         * 
         * @param vo 查询条件VO
         */
        public abstract void apply(PurchaseBillPageReqVO vo);

        @JsonCreator
        public static PurchaseOrderDisplayStatus fromCode(int code) {
            for (PurchaseOrderDisplayStatus status : values()) {
                if (status.code == code) {
                    return status;
                }
            }
            throw new IllegalArgumentException("无效的状态码: " + code);
        }
    }

    @Getter
    public enum StoreRequisitionAllocationDisplayStatus {
        // 全部状态
        ALL(0, "全部") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatusScope(Stream.of(PurchaseBillStatusEnum.values())
                        .filter(status -> !status.equals(PurchaseBillStatusEnum.PLAN_CREATED))
                        .map(PurchaseBillStatusEnum::getCode)
                        .toList());
            }
        },

        // 待审批状态
        PENDING_APPROVAL(1, "待审批") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatus(PurchaseBillStatusEnum.PENDING_APPROVAL.getCode());
                vo.setSubmitted(true);
            }
        },

        // 已驳回状态
        REJECTED(2, "已驳回") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatus(PurchaseBillStatusEnum.REJECTED.getCode());
                vo.setSubmitted(true);
            }
        },

        // 已撤销状态
        REVOKED(3, "已撤销") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatus(PurchaseBillStatusEnum.REVOKED.getCode());
                vo.setSubmitted(true);
            }
        },

        // 待配送状态
        PENDING_DELIVERY(4, "待配送") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatus(PurchaseBillStatusEnum.APPROVED_PENDING_DELIVERY.getCode());
            }
        },

        // 待收货状态
        PENDING_RECEIVE(5, "待收货") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatus(PurchaseBillStatusEnum.IN_DELIVERY.getCode());
                vo.setSubmitted(true);
            }
        },

        // 部分收货状态
        PARTIAL_RECEIVE(6, "部分收货") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatus(PurchaseBillStatusEnum.DELIVERED.getCode());
                vo.setSubmitted(true);
            }
        },

        // 已完成状态
        COMPLETED(7, "已完成") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatus(PurchaseBillStatusEnum.COMPLETED.getCode());
                vo.setSubmitted(true);
            }
        };

        private final int code;
        private final String name;

        StoreRequisitionAllocationDisplayStatus(int code, String name) {
            this.code = code;
            this.name = name;
        }

        /**
         * 应用显示状态到查询条件
         * 
         * @param vo 查询条件VO
         */
        public abstract void apply(PurchaseBillPageReqVO vo);

        @JsonCreator
        public static StoreRequisitionAllocationDisplayStatus fromCode(int code) {
            for (StoreRequisitionAllocationDisplayStatus status : values()) {
                if (status.code == code) {
                    return status;
                }
            }
            throw new IllegalArgumentException("无效的状态码: " + code);
        }
    }

    @Getter
    public enum HeadDistributionDisplayStatus {
        // 全部状态
        ALL(0, "全部") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatusScope(Stream.of(PurchaseBillStatusEnum.values())
                        .filter(status -> !status.equals(PurchaseBillStatusEnum.PLAN_CREATED))
                        .map(PurchaseBillStatusEnum::getCode)
                        .toList());
            }
        },

        // 暂存状态
        TEMPORARY(1, "暂存") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatus(PurchaseBillStatusEnum.PENDING_APPROVAL.getCode());
                vo.setSubmitted(false);
            }
        },

        // 待出库状态
        PENDING_OUTBOUND(2, "待出库") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatus(PurchaseBillStatusEnum.APPROVED_PENDING_DELIVERY.getCode());
                vo.setSubmitted(true);
            }
        },

        // 部分出库状态
        PARTIAL_OUTBOUND(3, "部分出库") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatus(PurchaseBillStatusEnum.IN_DELIVERY.getCode());
                vo.setSubmitted(true);
            }
        },

        // 已出库状态
        OUTBOUND_COMPLETED(4, "已出库") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatus(PurchaseBillStatusEnum.DELIVERED.getCode());
            }
        },

        // 已完成状态
        COMPLETED(5, "已完成") {
            @Override
            public void apply(PurchaseBillPageReqVO vo) {
                vo.setStatus(PurchaseBillStatusEnum.COMPLETED.getCode());
            }
        };

        private final int code;
        private final String name;

        HeadDistributionDisplayStatus(int code, String name) {
            this.code = code;
            this.name = name;
        }

        /**
         * 应用显示状态到查询条件
         * 
         * @param vo 查询条件VO
         */
        public abstract void apply(PurchaseBillPageReqVO vo);

        @JsonCreator
        public static HeadDistributionDisplayStatus fromCode(int code) {
            for (HeadDistributionDisplayStatus status : values()) {
                if (status.code == code) {
                    return status;
                }
            }
            throw new IllegalArgumentException("无效的状态码: " + code);
        }
    }
}