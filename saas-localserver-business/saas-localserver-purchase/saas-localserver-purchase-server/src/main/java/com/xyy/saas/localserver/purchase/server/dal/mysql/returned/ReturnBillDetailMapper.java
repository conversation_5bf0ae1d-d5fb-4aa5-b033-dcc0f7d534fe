package com.xyy.saas.localserver.purchase.server.dal.mysql.returned;

import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.returned.ReturnBillDetailDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * （单体/总部/门店）退货单明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ReturnBillDetailMapper extends BaseMapperX<ReturnBillDetailDO> {

//    /**
//     * 获取（单体/总部/门店）退货单详情分页
//     * @param reqVO
//     * @return
//     */
//    default PageResult<ReturnBillDetailDO> selectPage(ReturnBillDetailPageReqVO reqVO) {
//        return selectPage(reqVO, new LambdaQueryWrapperX<ReturnBillDetailDO>()
//                .eqIfPresent(ReturnBillDetailDO::getBillNo, reqVO.getBillNo())
//                .eqIfPresent(ReturnBillDetailDO::getProductPref, reqVO.getProductPref())
//                .eqIfPresent(ReturnBillDetailDO::getLotNo, reqVO.getLotNo())
//                .betweenIfPresent(ReturnBillDetailDO::getProductionDate, reqVO.getProductionDate())
//                .betweenIfPresent(ReturnBillDetailDO::getExpiryDate, reqVO.getExpiryDate())
//                .eqIfPresent(ReturnBillDetailDO::getPositionGuid, reqVO.getPositionGuid())
//                .eqIfPresent(ReturnBillDetailDO::getInTaxRate, reqVO.getInTaxRate())
//                .eqIfPresent(ReturnBillDetailDO::getPrice, reqVO.getPrice())
//                .eqIfPresent(ReturnBillDetailDO::getOutTaxRate, reqVO.getOutTaxRate())
//                .eqIfPresent(ReturnBillDetailDO::getOutboundQuantity, reqVO.getOutboundQuantity())
//                .eqIfPresent(ReturnBillDetailDO::getOutboundPrice, reqVO.getOutboundPrice())
//                .eqIfPresent(ReturnBillDetailDO::getOutboundAmount, reqVO.getOutboundAmount())
//                .eqIfPresent(ReturnBillDetailDO::getChannelId, reqVO.getChannelId())
//                .eqIfPresent(ReturnBillDetailDO::getMedicareProjectCode, reqVO.getMedicareProjectCode())
//                .likeIfPresent(ReturnBillDetailDO::getMedicareProjectName, reqVO.getMedicareProjectName())
//                .eqIfPresent(ReturnBillDetailDO::getMedicareProjectLevel, reqVO.getMedicareProjectLevel())
//                .eqIfPresent(ReturnBillDetailDO::getMedicareMinPackageNum, reqVO.getMedicareMinPackageNum())
//                .eqIfPresent(ReturnBillDetailDO::getExt, reqVO.getExt())
//                .eqIfPresent(ReturnBillDetailDO::getReturnReason, reqVO.getReturnReason())
//                .eqIfPresent(ReturnBillDetailDO::getRemark, reqVO.getRemark())
//                .betweenIfPresent(ReturnBillDetailDO::getCreateTime, reqVO.getCreateTime())
//                .orderByDesc(ReturnBillDetailDO::getId));
//    }

    /**
     * 根据单号删除退货单详情
     *
     * @param billNo 计划单号
     */
    default void deleteByBillNo(String billNo) {
        delete(new LambdaQueryWrapperX<ReturnBillDetailDO>()
                .eq(ReturnBillDetailDO::getBillNo, billNo));
    }

}