package com.xyy.saas.localserver.purchase.server.admin.stockout.vo;

import com.xyy.saas.localserver.purchase.enums.stockout.StockoutBillStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.util.List;

/**
 * 管理后台 - 缺货单信息新增/修改 Request VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 缺货单信息新增/修改 Request VO")
@Data
public class StockoutBillSaveReqVO {

    /** 主键ID */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25331")
    private Long id;

    /** 单号 */
    @Schema(description = "单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "单号不能为空")
    private String billNo;

    /** 门店租户ID */
    @Schema(description = "门店租户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1518")
    @NotNull(message = "门店租户ID不能为空")
    private Long storeTenantId;

    /** 要货单号 */
    @Schema(description = "要货单号(连锁才有)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "要货单号(连锁才有)不能为空")
    private String requisitionBillNo;

    /** 配送单号 */
    @Schema(description = "配送单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "配送单号不能为空")
    private String deliveryBillNo;

    /** 综合单据号 */
    @Schema(description = "综合单据号（单号混合）")
    private String compositeBillNo;

    /** 状态 */
    @Schema(description = "状态（0-缺货、1-已完成）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "状态（0-缺货、1-已完成）不能为空")
    private Integer status = StockoutBillStatusEnum.STOCKOUT.getCode();

    /** 商品种类 */
    @Schema(description = "商品种类", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "商品种类不能为空")
    private Integer productKind;

    /** 缺货内容 */
    @Schema(description = "缺货内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "缺货内容不能为空")
    private String stockoutContent;

    /** 缺货数量 */
    @Schema(description = "缺货数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "缺货数量不能为空")
    private BigDecimal stockoutQuantity;

    /** 要货数量 */
    @Schema(description = "要货数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "要货数量不能为空")
    private BigDecimal requireQuantity;

    /** 备注 */
    @Schema(description = "备注", example = "你猜")
    private String remark;

    /** 版本 */
    @Schema(description = "版本(乐观锁)", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "版本(乐观锁)不能为空")
    private Integer version;

    /** 缺货单详情 */
    @Schema(description = "缺货单详情", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<StockoutBillDetailSaveReqVO> details;
}