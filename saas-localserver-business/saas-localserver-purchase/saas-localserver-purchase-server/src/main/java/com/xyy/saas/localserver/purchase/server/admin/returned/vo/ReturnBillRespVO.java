package com.xyy.saas.localserver.purchase.server.admin.returned.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.alibaba.excel.annotation.*;

/**
 * 管理后台 - 退货单 Response VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 退货单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ReturnBillRespVO {

    /** 主键ID */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24583")
    @ExcelProperty("主键ID")
    private Long id;

    /** 单号 */
    @Schema(description = "单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单号")
    private String billNo;

    /** 采购单号 */
    @Schema(description = "采购单号")
    @ExcelProperty("采购单号")
    private String purchaseBillNo;

    /** 商城订单号 */
    @Schema(description = "商城订单号")
    @ExcelProperty("商城订单号")
    private String mallOrderNo;

    /** 入库门店租户ID */
    @Schema(description = "入库门店租户ID", example = "2524")
    @ExcelProperty("入库门店租户ID")
    private Long inboundTenantId;

    /** 租户类型 */
    @Schema(description = "租户类型（1-单体门店、2-连锁门店、3-连锁总部）", example = "1")
    @ExcelProperty("租户类型")
    private Integer tenantType;

    /** 总部租户ID */
    @Schema(description = "总部租户ID", example = "2524")
    @ExcelProperty("总部租户ID")
    private Long headTenantId;

    /** 综合单据号 */
    @Schema(description = "综合单据号")
    @ExcelProperty("综合单据号")
    private String compositeBillNo;

    /** 单据类型 */
    @Schema(description = "单据类型（1-采购退货、2-门店退货、3-门店调剂）")
    @ExcelProperty("单据类型")
    private Integer billType;

    /** 状态 */
    @Schema(description = "状态（1-待审批、2-待出库、3-待复核、4-已出库、5-已驳回、6-已撤销）", example = "2")
    @ExcelProperty("状态")
    private Integer status;

    /** 已提交 */
    @Schema(description = "已提交", example = "false")
    @ExcelProperty("已提交")
    private Boolean submitted;

    /** 供应商编码 */
    @Schema(description = "供应商编码")
    @ExcelProperty("供应商编码")
    private String supplierGuid;

    /** 供应商名称 */
    @Schema(description = "供应商名称", example = "李四")
    @ExcelProperty("供应商名称")
    private String supplierName;

    /** 商品种类 */
    @Schema(description = "商品种类")
    @ExcelProperty("商品种类")
    private Integer productKind;

    /** 退货内容 */
    @Schema(description = "退货内容")
    @ExcelProperty("退货内容")
    private String returnContent;

    /** 退货数量 */
    @Schema(description = "退货数量")
    @ExcelProperty("退货数量")
    private BigDecimal returnQuantity;

    /** 退货总金额 */
    @Schema(description = "退货总金额")
    @ExcelProperty("退货总金额")
    private BigDecimal returnAmount;

    /** 成本总金额 */
    @Schema(description = "成本总金额")
    @ExcelProperty("成本总金额")
    private BigDecimal costAmount;

    /** 出库操作员 */
    @Schema(description = "出库操作员")
    @ExcelProperty("出库操作员")
    private String operator;

    /** 出库时间 */
    @Schema(description = "出库时间")
    @ExcelProperty("出库时间")
    private LocalDateTime operateTime;

    /** 复核员 */
    @Schema(description = "复核员")
    @ExcelProperty("复核员")
    private String checker;

    /** 复核时间 */
    @Schema(description = "复核时间")
    @ExcelProperty("复核时间")
    private LocalDateTime checkTime;

    /** 备注 */
    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    /** 版本 */
    @Schema(description = "版本")
    @ExcelProperty("版本")
    private Integer version;

    /** 创建时间 */
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    @Schema(description = "单据详情", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<ReturnBillDetailSaveReqVO> details;
}