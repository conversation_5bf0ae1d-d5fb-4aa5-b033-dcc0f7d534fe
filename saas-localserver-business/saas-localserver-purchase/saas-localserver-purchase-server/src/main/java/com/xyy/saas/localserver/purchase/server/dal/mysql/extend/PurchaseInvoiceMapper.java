package com.xyy.saas.localserver.purchase.server.dal.mysql.extend;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseInvoicePageReqDTO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.extend.PurchaseInvoiceDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 采购-发票信息 Mapper
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface PurchaseInvoiceMapper extends BaseMapperX<PurchaseInvoiceDO> {

    /**
     * 获得采购-发票信息分页
     *
     * @param reqDTO 分页查询参数
     * @return 采购-发票信息分页
     */
    default PageResult<PurchaseInvoiceDO> selectPage(PurchaseInvoicePageReqDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<PurchaseInvoiceDO>()
                .eqIfPresent(PurchaseInvoiceDO::getBillNo, reqDTO.getBillNo())
                .eqIfPresent(PurchaseInvoiceDO::getInvoiceNo, reqDTO.getInvoiceNo())
                .eqIfPresent(PurchaseInvoiceDO::getInvoiceCode, reqDTO.getInvoiceCode())
                .eqIfPresent(PurchaseInvoiceDO::getInvoiceType, reqDTO.getInvoiceType())
                .eqIfPresent(PurchaseInvoiceDO::getAccompanyingShipment, reqDTO.getAccompanyingShipment())
                .eqIfPresent(PurchaseInvoiceDO::getInvoiceAmount, reqDTO.getInvoiceAmount())
                .likeIfPresent(PurchaseInvoiceDO::getInvoiceFileName, reqDTO.getInvoiceFileName())
                .eqIfPresent(PurchaseInvoiceDO::getInvoiceFileUrl, reqDTO.getInvoiceFileUrl())
                .betweenIfPresent(PurchaseInvoiceDO::getIssuedTime, reqDTO.getIssuedTime())
                .eqIfPresent(PurchaseInvoiceDO::getRemark, reqDTO.getRemark())
                .betweenIfPresent(PurchaseInvoiceDO::getCreateTime, reqDTO.getCreateTime())
                .orderByDesc(PurchaseInvoiceDO::getId));
    }

    /**
     * 根据单号删除采购-发票信息
     *
     * @param billNo 单号
     */
    default void deleteByBillNo(String billNo) {
        delete(new LambdaQueryWrapperX<PurchaseInvoiceDO>()
                .eq(PurchaseInvoiceDO::getBillNo, billNo));
    }

}