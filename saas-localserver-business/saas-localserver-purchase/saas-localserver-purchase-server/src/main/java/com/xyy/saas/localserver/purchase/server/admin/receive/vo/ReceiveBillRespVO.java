package com.xyy.saas.localserver.purchase.server.admin.receive.vo;

import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseErpBillRespVO;
import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseInvoiceRespVO;
import com.xyy.saas.localserver.purchase.server.admin.extend.vo.PurchaseTransportRespVO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import com.alibaba.excel.annotation.*;
import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;

/**
 * 管理后台 - 收货单 Response VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 收货单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class ReceiveBillRespVO {

    /** 主键ID */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "21916")
    @ExcelProperty("主键ID")
    private Long id;

    /** 验收入库单号 */
    @Schema(description = "验收入库单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("验收入库单号")
    private String billNo;

    /** 采购单号 */
    @Schema(description = "采购单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("采购单号")
    private String purchaseBillNo;

    /** 来源单号 */
    @Schema(description = "来源单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("来源单号")
    private String sourceBillNo;

    /** 配送出库单号 */
    @Schema(description = "配送出库单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("配送出库单号")
    private String deliveryBillNo;

    /** 随货同行单号 */
    @Schema(description = "随货同行单号")
    @ExcelProperty("随货同行单号")
    private String shipmentNo;

    /** 商城订单号 */
    @Schema(description = "商城订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商城订单号")
    private String mallOrderNo;

    /** 出库门店租户id */
    @Schema(description = "出库门店租户id")
    @ExcelProperty("出库门店租户id")
    private Long outboundTenantId;

    /** 租户类型（1-单体门店、2-连锁门店、3-连锁总部） */
    @Schema(description = "租户类型（1-单体门店、2-连锁门店、3-连锁总部）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("租户类型（1-单体门店、2-连锁门店、3-连锁总部）")
    private Integer tenantType;

    /** 总部租户ID */
    @Schema(description = "总部租户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "32278")
    @ExcelProperty("总部租户ID")
    private Long headTenantId;

    /** 综合单据号（单号混合） */
    @Schema(description = "综合单据号（单号混合）")
    @ExcelProperty("综合单据号（单号混合）")
    private String compositeBillNo;

    /** 单据类型（1-采购收货、2-拒收收货、3-退货收货、4-调剂收货、5-要货收货、6-铺货收货） */
    @Schema(description = "单据类型（1-采购收货、2-拒收收货、3-退货收货、4-调剂收货、5-要货收货、6-铺货收货）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("单据类型（1-采购收货、2-拒收收货、3-退货收货、4-调剂收货、5-要货收货、6-铺货收货）")
    private Integer billType;

    /** 采购方式（1-线下采购、2-无仓（B2B 无仓）、3-共仓（B2C 共仓）） */
    @Schema(description = "采购方式（1-线下采购、2-无仓（B2B 无仓）、3-共仓（B2C 共仓））", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("采购方式（1-线下采购、2-无仓（B2B 无仓）、3-共仓（B2C 共仓））")
    private Integer purchaseMode;

    /** 状态（1-待收货、2-待验收、3-待入库、4-已入库、5-已拒收） */
    @Schema(description = "状态（1-待收货、2-待验收、3-待入库、4-已入库、5-已拒收）", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @ExcelProperty("状态（1-待收货、2-待验收、3-待入库、4-已入库、5-已拒收）")
    private Integer status;

    /** 供应商编码 */
    @Schema(description = "供应商编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("供应商编码")
    private String supplierGuid;

    /** 供应商名称 */
    @Schema(description = "供应商名称", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("供应商名称")
    private String supplierName;

    /** 供应商销售员 */
    @Schema(description = "供应商销售员", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("供应商销售员")
    private String supplierSales;

    /** 商品种类 */
    @Schema(description = "商品种类", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品种类")
    private Integer productKind;

    /** 收货数量 */
    @Schema(description = "收货数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收货数量")
    private BigDecimal receiveQuantity;

    /** 折扣（百分比） */
    @Schema(description = "折扣（百分比）", example = "23792")
    @ExcelProperty("折扣（百分比）")
    private BigDecimal discount;

    /** 折扣总金额 */
    @Schema(description = "折扣总金额")
    @ExcelProperty("折扣总金额")
    private BigDecimal discountAmount;

    /** 收货内容 */
    @Schema(description = "收货内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收货内容")
    private String receiveContent;

    /** 收货金额（折后总金额） */
    @Schema(description = "收货金额（折后总金额）", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收货金额（折后总金额）")
    private BigDecimal receiveAmount;

    /** 配送员 */
    @Schema(description = "配送员", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("配送员")
    private String deliverer;

    /** 配送出库时间 */
    @Schema(description = "配送出库时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("配送出库时间")
    private LocalDateTime deliveryTime;

    /** 收货人 */
    @Schema(description = "收货人", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收货人")
    private String receiver;

    /** 收货人电话 */
    @Schema(description = "收货人电话", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收货人电话")
    private String receiverPhone;

    /** 收货人所在区域 */
    @Schema(description = "收货人所在区域", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收货人所在区域")
    private String receiverArea;

    /** 收货人地址 */
    @Schema(description = "收货人地址", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("收货人地址")
    private String receiverAddress;

    /** 收货时间 */
    @Schema(description = "收货时间")
    @ExcelProperty("收货时间")
    private LocalDateTime receiveTime;

    /** 验收员 */
    @Schema(description = "验收员", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("验收员")
    private String accepter;

    /** 验收时间 */
    @Schema(description = "验收时间")
    @ExcelProperty("验收时间")
    private LocalDateTime acceptTime;

    /** 入库员 */
    @Schema(description = "入库员", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("入库员")
    private String warehouser;

    /** 入库时间 */
    @Schema(description = "入库时间")
    @ExcelProperty("入库时间")
    private LocalDateTime warehouseTime;

    /** 复核员 */
    @Schema(description = "复核员", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("复核员")
    private String checker;

    /** 复核时间 */
    @Schema(description = "复核时间")
    @ExcelProperty("复核时间")
    private LocalDateTime checkTime;

    /** 质检员 */
    @Schema(description = "质检员")
    @ExcelProperty("质检员")
    private String qualityInspector;

    /** 质检报告单 */
    @Schema(description = "质检报告单")
    @ExcelProperty("质检报告单")
    private String qualityInspectionReport;

    /** 备注 */
    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    /** 版本(乐观锁) */
    @Schema(description = "版本(乐观锁)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本(乐观锁)")
    private Integer version;

    /** 创建时间 */
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    /** 收货单明细 */
    @Schema(description = "收货单明细", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "收货单明细不能为空")
    private List<ReceiveBillDetailRespVO> details;

    /** 采购扩展信息-运输信息 */
    @Schema(description = "采购扩展信息-运输信息", requiredMode = Schema.RequiredMode.REQUIRED)
    private PurchaseTransportRespVO transport;

    /** 采购扩展信息-发票 */
    @Schema(description = "采购扩展信息-发票", requiredMode = Schema.RequiredMode.REQUIRED)
    private PurchaseInvoiceRespVO invoice;

    /** 采购扩展信息-三方ERP单据 */
    @Schema(description = "采购扩展信息-三方ERP单据", requiredMode = Schema.RequiredMode.REQUIRED)
    private PurchaseErpBillRespVO erpBill;

    /** 扩展信息 */
    @Schema(description = "扩展信息")
    @ExcelProperty("扩展信息")
    private ExtDTO ext;
}