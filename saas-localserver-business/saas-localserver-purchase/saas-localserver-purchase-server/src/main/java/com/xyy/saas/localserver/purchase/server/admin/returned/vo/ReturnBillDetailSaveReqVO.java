package com.xyy.saas.localserver.purchase.server.admin.returned.vo;

import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;
import java.time.LocalDate;
import java.math.BigDecimal;

/**
 * 管理后台 - 退货单明细新增/修改 Request VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 退货单明细新增/修改 Request VO")
@Data
public class ReturnBillDetailSaveReqVO {

    /** 主键ID */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "24583")
    private Long id;

    /** 单号 */
    @Schema(description = "单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "单号不能为空")
    private String billNo;

    /** 商品编码 */
    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品编码不能为空")
    private String productPref;

    /** 批号 */
    @Schema(description = "批号")
    @NotEmpty(message = "批号不能为空")
    private String lotNo;

    /** 生产日期 */
    @Schema(description = "生产日期")
    @NotNull(message = "生产日期不能为空")
    private LocalDate productionDate;

    /** 有效期至 */
    @Schema(description = "有效期至")
    @NotNull(message = "有效期至不能为空")
    private LocalDate expiryDate;

    /** 总部货位编号 */
    @Schema(description = "总部货位编号")
    @NotEmpty(message = "总部货位编号不能为空")
    private String positionGuid;

    /** 供应商编码 */
    @Schema(description = "供应商编码")
    private String supplierGuid;

    /** 供应商名称 */
    @Schema(description = "供应商名称", example = "李四")
    private String supplierName;

    /** 税率 */
    @Schema(description = "税率")
    @Digits(integer = 2, fraction = 2, message = "进项税率应为0.01-99.99之间")
    @DecimalMin(value = "0.01", message = "进项税率应为0.01-99.99之间")
    private BigDecimal inTaxRate;

    /** 含税成本价 */
    @Schema(description = "含税成本价", example = "26185")
    @NotNull(message = "含税成本价不能为空")
    @DecimalMin(value = "0.0001", message = "含税成本价应为0.0001-999999.9999之间")
    @Digits(integer = 6, fraction = 4, message = "商品单价应为0.0001-999999.9999之间")
    private BigDecimal price;

    /** 销项税率 */
    @Schema(description = "销项税率")
    @Digits(integer = 2, fraction = 2, message = "销项税率应为0.01-99.99之间")
    @DecimalMin(value = "0.01", message = "销项税率应为0.01-99.99之间")
    private BigDecimal outTaxRate;

    /** 出库数量 */
    @Schema(description = "出库数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "出库数量不能为空")
    @Digits(integer = 6, fraction = 2, message = "出库数量应为0.01-999999.99之间")
    @DecimalMin(value = "0.01", message = "出库数量应为0.01-999999.99之间")
    private BigDecimal outboundQuantity;

    /** 出库单价 */
    @Schema(description = "出库单价", example = "14464")
    @NotNull(message = "出库单价不能为空")
    @Digits(integer = 6, fraction = 4, message = "出库单价应为0.0001-999999.9999之间")
    @DecimalMin(value = "0.0001", message = "出库单价应为0.0001-999999.9999之间")
    private BigDecimal outboundPrice;

    /** 出库总金额 */
    @Schema(description = "出库总金额")
    @NotNull(message = "出库总金额不能为空")
    @Digits(integer = 12, fraction = 2, message = "出库总金额应为0.01-999999999999.99之间")
    @DecimalMin(value = "0.01", message = "出库总金额应为0.01-999999999999.99之间")
    private BigDecimal outboundAmount;

    /** 渠道ID */
    @Schema(description = "渠道ID", example = "24434")
    private String channelId;

    /** 医保项目编码 */
    @Schema(description = "医保项目编码")
    private String medicareProjectCode;

    /** 医保项目名称 */
    @Schema(description = "医保项目名称", example = "王五")
    private String medicareProjectName;

    /** 医保项目等级 */
    @Schema(description = "医保项目等级", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer medicareProjectLevel;

    /** 医保最小包装数量 */
    @Schema(description = "医保最小包装数量", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer medicareMinPackageNum;

    /** 扩展信息 */
    @Schema(description = "扩展信息")
    private ExtDTO ext;

    /** 退货原因 */
    @Schema(description = "退货原因(1-破损、2-召回、3-滞销、4-过期失效、5-近效期、6-质量问题、7-其他)", example = "不香")
    private Integer returnReason;

    /** 备注 */
    @Schema(description = "备注", example = "你猜")
    private String remark;
}