package com.xyy.saas.localserver.purchase.server.admin.purchase;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillPageReqDTO;
import com.xyy.saas.localserver.purchase.server.admin.purchase.vo.PurchaseBillPageReqVO;
import com.xyy.saas.localserver.purchase.server.admin.purchase.vo.PurchaseBillRespVO;
import com.xyy.saas.localserver.purchase.server.admin.purchase.vo.PurchaseBillSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.purchase.PurchaseBillConvert;
import com.xyy.saas.localserver.purchase.server.service.purchase.PurchaseOrderService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.io.IOException;
import java.util.List;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 采购订单 Controller
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "管理后台 - 采购订单")
@RestController
@RequestMapping("/saas/purchase/purchase-order")
@Validated
public class PurchaseOrderController {

    @Resource
    private PurchaseOrderService purchaseOrderService;

    /**
     * 创建采购订单
     * 处理流程：
     * 1. 校验供应商：验证供应商信息的有效性
     * 2. 对象转换：将VO对象转换为DTO对象
     * 3. 调用服务：执行创建操作
     *
     * @param saveReqVO 创建信息
     * @return 创建结果
     */
    @PostMapping("/save")
    @Operation(summary = "保存采购订单")
    @PreAuthorize("@ss.hasPermission('saas:purchase-order:save')")
    public CommonResult<Boolean> save(@Valid @RequestBody PurchaseBillSaveReqVO saveReqVO) {
        // 1. 校验供应商：验证供应商信息的有效性
        saveReqVO.validateSupplier();

        // 2. 对象转换：将VO对象转换为DTO对象
        PurchaseBillDTO purchaseOrder = PurchaseBillConvert.INSTANCE.convert2OrderDTO(saveReqVO);

        // 3. 调用服务：执行创建操作
        purchaseOrderService.savePurchaseOrder(purchaseOrder);
        return success(true);
    }

    /**
     * 撤销采购订单
     * 处理流程：
     * 1. 对象转换：将VO对象转换为DTO对象
     * 2. 调用服务：执行撤销操作
     *
     * @param saveReqVO 撤销信息
     * @return 撤销结果
     */
    @PostMapping("/revoke")
    @Operation(summary = "撤销采购订单")
    @PreAuthorize("@ss.hasPermission('saas:purchase-order:revoke')")
    public CommonResult<Boolean> revoke(@Valid @RequestBody PurchaseBillSaveReqVO saveReqVO) {
        // 1. 对象转换：将VO对象转换为DTO对象
        PurchaseBillDTO purchaseOrder = PurchaseBillConvert.INSTANCE.convert2RevokeDTO(saveReqVO);

        // 2. 调用服务：执行撤销操作
        purchaseOrderService.revokeRequisition(purchaseOrder);
        return success(true);
    }

    /**
     * 删除采购订单
     * 处理流程：
     * 1. 调用服务：执行删除操作
     *
     * @param id 编号
     * @return 删除结果
     */
    @DeleteMapping("/delete")
    @Operation(summary = "删除采购订单")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:purchase-order:delete')")
    public CommonResult<Boolean> delete(@RequestParam("id") Long id) {
        // 调用服务：执行删除操作
        purchaseOrderService.deletePurchaseOrder(id);
        return success(true);
    }

    /**
     * 获取采购订单
     * 处理流程：
     * 1. 调用服务：获取订单信息
     * 2. 对象转换：将DTO对象转换为VO对象
     *
     * @param id 编号
     * @return 采购订单信息
     */
    @GetMapping("/get")
    @Operation(summary = "获得采购订单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:purchase-order:query')")
    public CommonResult<PurchaseBillRespVO> get(@RequestParam("id") Long id) {
        // 1. 调用服务：获取订单信息
        PurchaseBillDTO purchaseOrder = purchaseOrderService.getPurchaseOrder(id);
        // 2. 对象转换：将DTO对象转换为VO对象
        return success(PurchaseBillConvert.INSTANCE.convert2VO(purchaseOrder));
    }

    /**
     * 获取采购订单分页
     * 处理流程：
     * 1. 处理查询参数：处理分页查询条件
     * 2. 对象转换：将VO对象转换为DTO对象
     * 3. 调用服务：获取分页数据
     * 4. 对象转换：将DTO对象转换为VO对象
     *
     * @param pageReqVO 分页查询参数
     * @return 分页结果
     */
    @GetMapping("/page")
    @Operation(summary = "获得采购订单分页")
    @PreAuthorize("@ss.hasPermission('saas:purchase-order:query')")
    public CommonResult<PageResult<PurchaseBillRespVO>> getPage(@Valid PurchaseBillPageReqVO pageReqVO) {
        // 1. 处理查询参数：处理分页查询条件
        PurchaseBillConvert.INSTANCE.processOrderPageQueryParams(pageReqVO);
        // 2. 对象转换：将VO对象转换为DTO对象
        PurchaseBillPageReqDTO pageReqDTO = PurchaseBillConvert.INSTANCE.convert2DTO(pageReqVO);
        // 3. 调用服务：获取分页数据
        PageResult<PurchaseBillDTO> pageResult = purchaseOrderService.getPurchaseOrderPage(pageReqDTO);
        // 4. 对象转换：将DTO对象转换为VO对象
        return success(PurchaseBillConvert.INSTANCE.convert2VO(pageResult));
    }

    /**
     * 导出采购订单Excel
     * 处理流程：
     * 1. 设置分页大小：设置为不分页
     * 2. 处理查询参数：处理分页查询条件
     * 3. 对象转换：将VO对象转换为DTO对象
     * 4. 调用服务：获取数据列表
     * 5. 导出Excel：将数据导出为Excel文件
     *
     * @param pageReqVO 分页查询参数
     * @param response  HTTP响应对象
     * @throws IOException 导出过程中的IO异常
     */
    @GetMapping("/export-excel")
    @Operation(summary = "导出采购订单 Excel")
    @PreAuthorize("@ss.hasPermission('saas:purchase-order:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportExcel(@Valid PurchaseBillPageReqVO pageReqVO,
            HttpServletResponse response) throws IOException {
        // 1. 设置分页大小：设置为不分页
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        // 2. 处理查询参数：处理分页查询条件
        PurchaseBillConvert.INSTANCE.processOrderPageQueryParams(pageReqVO);
        // 3. 对象转换：将VO对象转换为DTO对象
        PurchaseBillPageReqDTO pageReqDTO = PurchaseBillConvert.INSTANCE.convert2DTO(pageReqVO);
        // 4. 调用服务：获取数据列表
        List<PurchaseBillDTO> list = purchaseOrderService.getPurchaseOrderPage(pageReqDTO).getList();
        // 5. 导出Excel：将数据导出为Excel文件
        ExcelUtils.write(response, "采购订单.xls", "数据", PurchaseBillRespVO.class,
                PurchaseBillConvert.INSTANCE.convert2VOList(list));
    }
}