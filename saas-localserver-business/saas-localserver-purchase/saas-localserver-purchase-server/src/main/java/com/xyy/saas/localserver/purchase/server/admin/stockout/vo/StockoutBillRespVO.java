package com.xyy.saas.localserver.purchase.server.admin.stockout.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

import com.alibaba.excel.annotation.*;

/**
 * 管理后台 - 缺货单信息 Response VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 缺货单信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class StockoutBillRespVO {

    /** 主键ID */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "25331")
    @ExcelProperty("主键ID")
    private Long id;

    /** 单号 */
    @Schema(description = "单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单号")
    private String billNo;

    /** 门店租户ID */
    @Schema(description = "门店租户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1518")
    @ExcelProperty("门店租户ID")
    private Long storeTenantId;

    /** 要货单号 */
    @Schema(description = "要货单号(连锁才有)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("要货单号(连锁才有)")
    private String requisitionBillNo;

    /** 配送单号 */
    @Schema(description = "配送单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("配送单号")
    private String deliveryBillNo;

    /** 综合单据号 */
    @Schema(description = "综合单据号（单号混合）")
    @ExcelProperty("综合单据号（单号混合）")
    private String compositeBillNo;

    /** 状态 */
    @Schema(description = "状态（0-缺货、1-已完成）", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("状态（0-缺货、1-已完成）")
    private Integer status;

    /** 商品种类 */
    @Schema(description = "商品种类", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品种类")
    private Integer productKind;

    /** 缺货内容 */
    @Schema(description = "缺货内容", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("缺货内容")
    private String stockoutContent;

    /** 缺货数量 */
    @Schema(description = "缺货数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("缺货数量")
    private BigDecimal stockoutQuantity;

    /** 要货数量 */
    @Schema(description = "要货数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("要货数量")
    private BigDecimal requireQuantity;

    /** 备注 */
    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    /** 版本 */
    @Schema(description = "版本(乐观锁)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本(乐观锁)")
    private Integer version;

    /** 创建时间 */
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

    /** 缺货单明细 */
    @Schema(description = "缺货单明细")
    private List<StockoutBillDetailRespVO> details;

}