package com.xyy.saas.localserver.purchase.server.dal.mysql.extend;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.localserver.purchase.api.extend.dto.PurchaseErpBillPageReqDTO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.extend.PurchaseErpBillDO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;

/**
 * 采购-三方erp单据信息 Mapper
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface PurchaseErpBillMapper extends BaseMapperX<PurchaseErpBillDO> {

    /**
     * 获取采购-三方erp单据信息分页
     * @param reqDTO 查询条件
     * @return 分页结果
     */
    default PageResult<PurchaseErpBillDO> selectPage(PurchaseErpBillPageReqDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<PurchaseErpBillDO>()
                .eqIfPresent(PurchaseErpBillDO::getBillNo, reqDTO.getBillNo())
                .eqIfPresent(PurchaseErpBillDO::getErpBillNo, reqDTO.getErpBillNo())
                .eqIfPresent(PurchaseErpBillDO::getSalesBillNo, reqDTO.getSalesBillNo())
                .eqIfPresent(PurchaseErpBillDO::getOutboundBillNo, reqDTO.getOutboundBillNo())
                .eqIfPresent(PurchaseErpBillDO::getWarehouseBillNo, reqDTO.getWarehouseBillNo())
                .eqIfPresent(PurchaseErpBillDO::getRefundStorageBillNo, reqDTO.getRefundStorageBillNo())
                .eqIfPresent(PurchaseErpBillDO::getCancelReason, reqDTO.getCancelReason())
                .eqIfPresent(PurchaseErpBillDO::getRemark, reqDTO.getRemark())
                .betweenIfPresent(PurchaseErpBillDO::getCreateTime, reqDTO.getCreateTime())
                // 优化 compositeBillNo 匹配逻辑
                .apply(StringUtils.isNotBlank(reqDTO.getCompositeBillNo()),
                        "INSTR(CONCAT('|', composite_bill_no, '|'), CONCAT('|', {0}, '|')) > 0",
                        reqDTO.getCompositeBillNo())
                .orderByDesc(PurchaseErpBillDO::getId));
    }

}