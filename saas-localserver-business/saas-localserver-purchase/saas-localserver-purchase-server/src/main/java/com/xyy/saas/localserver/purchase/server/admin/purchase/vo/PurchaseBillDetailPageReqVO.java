package com.xyy.saas.localserver.purchase.server.admin.purchase.vo;

import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import lombok.*;

import java.time.LocalDate;

import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 采购明细分页 Request VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 采购明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PurchaseBillDetailPageReqVO extends PageParam {

    /** 计划单号 */
    @Schema(description = "计划单号")
    private String planBillNo;

    /** 采购单号 */
    @Schema(description = "采购单号")
    private String purchaseBillNo;

    /** 商品编码 */
    @Schema(description = "商品编码")
    private String productPref;

    /** 批号 */
    @Schema(description = "批号")
    private String lotNo;

    /** 生产日期（YYYY-MM-DD） */
    @Schema(description = "生产日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] productionDate;

    /** 有效期至（YYYY-MM-DD） */
    @Schema(description = "有效期至")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] expiryDate;

    /** （合格）存储区编号 */
    @Schema(description = "存储区编号")
    private String positionGuid;

    /** 进项税率（百分比） */
    @Schema(description = "进项税率")
    private BigDecimal inTaxRate;

    /** 销项税率（百分比） */
    @Schema(description = "销项税率")
    private BigDecimal outTaxRate;

    /** 采购单价 */
    @Schema(description = "采购单价", example = "12482")
    private BigDecimal price;

    /** 采购总金额 */
    @Schema(description = "采购总金额")
    private BigDecimal purchaseAmount;

    /** 采购数量 */
    @Schema(description = "采购数量")
    private BigDecimal purchaseQuantity;

    /** 已发货数量 */
    @Schema(description = "已发货数量")
    private BigDecimal deliveredQuantity;

    /** 可退数量（原单退货扣减可退数量，下游收货单的实际入库数量） */
    @Schema(description = "可退数量")
    private BigDecimal returnableQuantity;

    /** 渠道ID */
    @Schema(description = "渠道ID", example = "8991")
    private String channelId;

    /** 源行号（和三方ERP对接时需要） */
    @Schema(description = "源行号")
    private String sourceLineNo;

    /** 医保项目编码 */
    @Schema(description = "医保项目编码")
    private String medicareProjectCode;

    /** 医保项目名称 */
    @Schema(description = "医保项目名称", example = "芋艿")
    private String medicareProjectName;

    /** 医保项目等级 */
    @Schema(description = "医保项目等级")
    private Integer medicareProjectLevel;

    /** 医保最小包装数量 */
    @Schema(description = "医保最小包装数量")
    private Integer medicareMinPackageNum;

    /** 扩展信息（当前商品信息） */
    @Schema(description = "扩展信息")
    private ExtDTO ext;

    /** 备注 */
    @Schema(description = "备注", example = "你说的对")
    private String remark;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}