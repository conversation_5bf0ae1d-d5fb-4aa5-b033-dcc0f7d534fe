package com.xyy.saas.localserver.purchase.server.dal.dataobject.returned;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import com.baomidou.mybatisplus.annotation.*;
import org.apache.commons.lang3.StringUtils;

/**
 * 退货单 DO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@TableName("saas_purchase_return_bill")
// @KeySequence("saas_purchase_return_bill_seq") // 用于
// Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReturnBillDO extends TenantBaseDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 单号 */
    private String billNo;

    /** 采购单号 */
    private String purchaseBillNo;

    /** 商城订单号 */
    private String mallOrderNo;

    /** 入库门店租户ID */
    private Long inboundTenantId;

    /** 租户类型（1-单体门店、2-连锁门店、3-连锁总部） */
    private Integer tenantType;

    /** 总部租户ID */
    private Long headTenantId;

    /** 综合单据号（单号混合） */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String compositeBillNo;

    /** 单据类型（1-采购退货、2-门店退货、3-门店调剂） */
    private Integer billType;

    /** 状态（1-待审批、2-待出库、3-待复核、4-已出库、5-已驳回、6-已撤销） */
    private Integer status;

    /** 已提交（类同于是否暂存） */
    private Boolean submitted;

    /** 供应商编码 */
    private String supplierGuid;

    /** 供应商名称 */
    private String supplierName;

    /** 商品种类 */
    private Integer productKind;

    /** 退货内容 */
    private String returnContent;

    /** 退货数量 */
    private BigDecimal returnQuantity;

    /** 退货总金额 */
    private BigDecimal returnAmount;

    /** 成本总金额 */
    private BigDecimal costAmount;

    /** 出库操作员 */
    private String operator;

    /** 出库时间 */
    private LocalDateTime operateTime;

    /** 复核员 */
    private String checker;

    /** 复核时间 */
    private LocalDateTime checkTime;

    /** 备注 */
    private String remark;

    /** 版本(乐观锁) */
    private Integer version;

    /**
     * 生成综合单据号
     * 规则：billNo|purchaseBillNo|mallOrderNo
     */
    public String generateCompositeBillNo() {
        this.compositeBillNo = Stream.of(billNo, purchaseBillNo, mallOrderNo)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining("|", "|", "|"));
        return this.compositeBillNo;
    }
}