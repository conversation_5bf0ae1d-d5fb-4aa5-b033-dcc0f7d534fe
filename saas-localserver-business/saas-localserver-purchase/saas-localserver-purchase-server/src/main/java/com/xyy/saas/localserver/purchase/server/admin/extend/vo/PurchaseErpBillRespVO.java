package com.xyy.saas.localserver.purchase.server.admin.extend.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

/**
 * 管理后台 - 采购-三方erp单据信息 Response VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 采购-三方erp单据信息 Response VO")
@Data
@ExcelIgnoreUnannotated
public class PurchaseErpBillRespVO {

    /** 主键ID */
    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "12103")
    @ExcelProperty("主键ID")
    private Long id;

    /** 采购单/收货单号 */
    @Schema(description = "采购单/收货单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("采购单/收货单号")
    private String billNo;

    /** 三方erp订单号 */
    @Schema(description = "三方erp订单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("三方erp订单号")
    private String erpBillNo;

    /** 三方erp销售单号 */
    @Schema(description = "三方erp销售单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("三方erp销售单号")
    private String salesBillNo;

    /** 三方erp出库单号 */
    @Schema(description = "三方erp出库单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("三方erp出库单号")
    private String outboundBillNo;

    /** 三方erp入库单号 */
    @Schema(description = "三方erp入库单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("三方erp入库单号")
    private String warehouseBillNo;

    /** 三方erp销售退回入库单号(神农XSTHRK) */
    @Schema(description = "三方erp销售退回入库单号(神农XSTHRK)", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("三方erp销售退回入库单号(神农XSTHRK)")
    private String refundStorageBillNo;

    /** 综合单据号（单号混合） */
    @Schema(description = "综合单据号（单号混合）")
    @ExcelProperty("综合单据号（单号混合）")
    private String compositeBillNo;

    /** 三方erp取消原因 */
    @Schema(description = "三方erp取消原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "不香")
    @ExcelProperty("三方erp取消原因")
    private String cancelReason;

    /** 备注 */
    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    /** 创建时间 */
    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}