package com.xyy.saas.localserver.purchase.server.admin.stockout.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 缺货单信息分页 Request VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 缺货单信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class StockoutBillPageReqVO extends PageParam {

    /** 单号 */
    @Schema(description = "单号")
    private String billNo;

    /** 门店租户ID */
    @Schema(description = "门店租户ID", example = "1518")
    private Long storeTenantId;

    /** 要货单号 */
    @Schema(description = "要货单号(连锁才有)")
    private String requisitionBillNo;

    /** 配送单号 */
    @Schema(description = "配送单号")
    private String deliveryBillNo;

    /** 综合单据号 */
    @Schema(description = "综合单据号（单号混合）")
    private String compositeBillNo;

    /** 状态 */
    @Schema(description = "状态（0-缺货、1-已完成）", example = "2")
    private Integer status;

    /** 商品种类 */
    @Schema(description = "商品种类")
    private Integer productKind;

    /** 缺货内容 */
    @Schema(description = "缺货内容")
    private String stockoutContent;

    /** 缺货数量 */
    @Schema(description = "缺货数量")
    private BigDecimal stockoutQuantity;

    /** 要货数量 */
    @Schema(description = "要货数量")
    private BigDecimal requireQuantity;

    /** 备注 */
    @Schema(description = "备注", example = "你猜")
    private String remark;

    /** 版本 */
    @Schema(description = "版本(乐观锁)")
    private Integer version;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
}