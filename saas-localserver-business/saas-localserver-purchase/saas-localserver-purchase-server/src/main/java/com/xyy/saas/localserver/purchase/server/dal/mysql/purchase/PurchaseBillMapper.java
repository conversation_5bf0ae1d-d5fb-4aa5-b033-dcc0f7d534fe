package com.xyy.saas.localserver.purchase.server.dal.mysql.purchase;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillDTO;
import com.xyy.saas.localserver.purchase.api.purchase.dto.PurchaseBillPageReqDTO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.purchase.PurchaseBillDO;
import org.apache.commons.lang3.StringUtils;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 采购单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface PurchaseBillMapper extends BaseMapperX<PurchaseBillDO> {

    default PageResult<PurchaseBillDO> selectPage(PurchaseBillPageReqDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<PurchaseBillDO>()
                .likeIfPresent(PurchaseBillDO::getPlanBillNo, reqDTO.getPlanBillNo())
                .likeIfPresent(PurchaseBillDO::getPurchaseBillNo, reqDTO.getPurchaseBillNo())
                .likeIfPresent(PurchaseBillDO::getMallOrderNo, reqDTO.getMallOrderNo())
                .eqIfPresent(PurchaseBillDO::getTenantType, reqDTO.getTenantType())
                .eqIfPresent(PurchaseBillDO::getHeadTenantId, reqDTO.getHeadTenantId())
                .eqIfPresent(PurchaseBillDO::getTenantId, reqDTO.getTenantId())
                .eqIfPresent(PurchaseBillDO::getInboundTenantId, reqDTO.getInboundTenantId())
                .eqIfPresent(PurchaseBillDO::getOutboundTenantId, reqDTO.getOutboundTenantId())
                .eqIfPresent(PurchaseBillDO::getBillType, reqDTO.getBillType())
                .eqIfPresent(PurchaseBillDO::getImportMode, reqDTO.getImportMode())
                .eqIfPresent(PurchaseBillDO::getRemoteReceived, reqDTO.getRemoteReceived())
                .eqIfPresent(PurchaseBillDO::getSubmitted, reqDTO.getSubmitted())
                .eqIfPresent(PurchaseBillDO::getStatus, reqDTO.getStatus())
                .inIfPresent(PurchaseBillDO::getStatus, reqDTO.getStatusScope())
                .eqIfPresent(PurchaseBillDO::getMedicineType, reqDTO.getMedicineType())
                .eqIfPresent(PurchaseBillDO::getPurchaseMode, reqDTO.getPurchaseMode())
                .eqIfPresent(PurchaseBillDO::getSupplierGuid, reqDTO.getSupplierGuid())
                .likeIfPresent(PurchaseBillDO::getSupplierName, reqDTO.getSupplierName())
                .eqIfPresent(PurchaseBillDO::getPurchaseContent, reqDTO.getPurchaseContent())
                .eqIfPresent(PurchaseBillDO::getPurchaseQuantity, reqDTO.getPurchaseQuantity())
                .eqIfPresent(PurchaseBillDO::getPurchaseAmount, reqDTO.getPurchaseAmount())
                .eqIfPresent(PurchaseBillDO::getDeliveredQuantity, reqDTO.getDeliveredQuantity())
                .eqIfPresent(PurchaseBillDO::getReturnableQuantity, reqDTO.getReturnableQuantity())
                .eqIfPresent(PurchaseBillDO::getProductKind, reqDTO.getProductKind())
                .eqIfPresent(PurchaseBillDO::getPlanner, reqDTO.getPlanner())
                .betweenIfPresent(PurchaseBillDO::getPlanTime, reqDTO.getPlanTime())
                .eqIfPresent(PurchaseBillDO::getPurchaser, reqDTO.getPurchaser())
                .betweenIfPresent(PurchaseBillDO::getPurchaseTime, reqDTO.getPurchaseTime())
                .eqIfPresent(PurchaseBillDO::getChecker, reqDTO.getChecker())
                .betweenIfPresent(PurchaseBillDO::getCheckTime, reqDTO.getCheckTime())
                .eqIfPresent(PurchaseBillDO::getReceiver, reqDTO.getReceiver())
                .eqIfPresent(PurchaseBillDO::getReceiverPhone, reqDTO.getReceiverPhone())
                .eqIfPresent(PurchaseBillDO::getReceiverArea, reqDTO.getReceiverArea())
                .eqIfPresent(PurchaseBillDO::getReceiverAddress, reqDTO.getReceiverAddress())
                .eqIfPresent(PurchaseBillDO::getRemark, reqDTO.getRemark())
                .eqIfPresent(PurchaseBillDO::getVersion, reqDTO.getVersion())
                .betweenIfPresent(PurchaseBillDO::getCreateTime, reqDTO.getCreateTime())
                // 优化 compositeBillNo 匹配逻辑
                .apply(StringUtils.isNotBlank(reqDTO.getCompositeBillNo()),
                        "INSTR(CONCAT('|', composite_bill_no, '|'), CONCAT('|', {0}, '|')) > 0",
                        reqDTO.getCompositeBillNo())
                .orderByDesc(PurchaseBillDO::getId));
    }

    default PurchaseBillDO getPurchaseBillByBillNo(String purchaseBillNo, Long tenantId) {
        return selectOne(PurchaseBillDO::getPurchaseBillNo, purchaseBillNo, PurchaseBillDO::getTenantId, tenantId);
    }

    /**
     * 根据订单号查询采购单及其明细
     *
     * @param purchaseBillNo 采购单号
     * @param tenantId       租户ID
     * @return 采购单及其明细
     */
    PurchaseBillDTO getPurchaseBillWithDetails(@Param("purchaseBillNo") String purchaseBillNo,
                                               @Param("tenantId") Long tenantId);

    /**
     * 根据计划单号查询采购单及其明细
     *
     * @param planBillNo 计划单号
     * @param tenantId   租户ID
     * @return 采购单及其明细
     */
    PurchaseBillDTO getPurchaseBillWithDetailsByPlanBillNo(@Param("planBillNo") String planBillNo,
            @Param("tenantId") Long tenantId);

    /**
     * 根据商城订单号查询采购单及其明细
     *
     * @param mallOrderNo 商城订单号
     * @param tenantId    租户ID
     * @return 采购单及其明细
     */
    PurchaseBillDTO getPurchaseBillWithDetailsByMallOrderNo(@Param("mallOrderNo") String mallOrderNo,
            @Param("tenantId") Long tenantId);

    /**
     * 更新采购单
     *
     * @param purchaseBillDo 采购单
     * @return 更新条目数
     */
    int updatePurchaseBillByVersion(PurchaseBillDO purchaseBillDo);
}