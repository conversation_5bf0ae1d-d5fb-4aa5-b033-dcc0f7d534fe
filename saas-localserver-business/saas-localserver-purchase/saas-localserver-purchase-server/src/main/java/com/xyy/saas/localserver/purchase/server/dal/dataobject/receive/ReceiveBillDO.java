package com.xyy.saas.localserver.purchase.server.dal.dataobject.receive;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.*;
import org.apache.commons.lang3.StringUtils;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * 收货单 DO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@TableName(value = "saas_purchase_receive_bill", autoResultMap = true)
// @KeySequence("saas_purchase_receive_bill_seq") // 用于
// Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReceiveBillDO extends TenantBaseDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 收货（收货/验收/入库）单号 */
    private String billNo;

    /** 来源单号 */
    private String sourceBillNo;

    /** 配送出库单号 */
    private String deliveryBillNo;

    /** 随货同行单号 */
    private String shipmentNo;

    /** 商城订单号 */
    private String mallOrderNo;

    /** 出库门店租户id */
    private Long outboundTenantId;

    /** 租户类型（1-单体门店、2-连锁门店、3-连锁总部） */
    private Integer tenantType;

    /** 总部租户ID */
    private Long headTenantId;

    /** 综合单据号（单号混合） */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private String compositeBillNo;

    /** 单据类型（1-采购收货、2-拒收收货、3-退货收货、4-调剂收货、5-要货收货、6-铺货收货） */
    private Integer billType;

    /** 采购方式（1-线下采购、2-无仓（B2B 无仓）、3-共仓（B2C 共仓）） */
    private Integer purchaseMode;

    /** 状态（1-待收货、2-待验收、3-待入库、4-已入库、5-已拒收） */
    private Integer status;

    /** 供应商编码 */
    private String supplierGuid;

    /** 供应商名称 */
    private String supplierName;

    /** 供应商销售员 */
    private String supplierSales;

    /** 商品种类 */
    private Integer productKind;

    /** 收货数量 */
    private BigDecimal receiveQuantity;

    /** 折扣（百分比） */
    private BigDecimal discount;

    /** 折扣总金额 */
    private BigDecimal discountAmount;

    /** 收货内容 */
    private String receiveContent;

    /** 收货金额（折后总金额） */
    private BigDecimal receiveAmount;

    /** 配送员 */
    private String deliverer;

    /** 配送出库时间 */
    private LocalDateTime deliveryTime;

    /** 收货员 */
    private String receiver;

    /** 收货时间 */
    private LocalDateTime receiveTime;

    /** 验收员 */
    private String accepter;

    /** 验收时间 */
    private LocalDateTime acceptTime;

    /** 入库员 */
    private String warehouser;

    /** 入库时间 */
    private LocalDateTime warehouseTime;

    /** 复核员 */
    private String checker;

    /** 复核时间 */
    private LocalDateTime checkTime;

    /** 质检员 */
    private String qualityInspector;

    /** 质检报告单 */
    private String qualityInspectionReport;

    /** 备注 */
    private String remark;

    /** 版本(乐观锁) */
    private Integer version;

    /**
     * 生成综合单据号
     * 规则：billNo|sourceBillNo|deliveryBillNo|shipmentNo|mallOrderNo
     */
    public String generateCompositeBillNo() {
        this.compositeBillNo = Stream.of(billNo, sourceBillNo, deliveryBillNo, shipmentNo, mallOrderNo)
                .filter(StringUtils::isNotBlank)
                .collect(Collectors.joining("|", "|", "|"));
        return this.compositeBillNo;
    }
}