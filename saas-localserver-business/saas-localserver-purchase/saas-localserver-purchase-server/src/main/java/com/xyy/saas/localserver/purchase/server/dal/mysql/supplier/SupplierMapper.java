package com.xyy.saas.localserver.purchase.server.dal.mysql.supplier;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.localserver.purchase.api.supplier.dto.SupplierPageReqDTO;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.supplier.SupplierDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 供应商信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface SupplierMapper extends BaseMapperX<SupplierDO> {

    default PageResult<SupplierDO> selectPage(SupplierPageReqDTO reqDTO) {
        return selectPage(reqDTO, new LambdaQueryWrapperX<SupplierDO>()
                .eqIfPresent(SupplierDO::getGuid, reqDTO.getGuid())
                .eqIfPresent(SupplierDO::getSourceSupplierGuid, reqDTO.getSourceSupplierGuid())
                .likeIfPresent(SupplierDO::getName, reqDTO.getName())
                .eqIfPresent(SupplierDO::getType, reqDTO.getType())
                .likeIfPresent(SupplierDO::getMnemonicCode, reqDTO.getMnemonicCode())
                .eqIfPresent(SupplierDO::getSystemDefault, reqDTO.getSystemDefault())
                .eqIfPresent(SupplierDO::getSource, reqDTO.getSource())
                .eqIfPresent(SupplierDO::getLegalRepresentative, reqDTO.getLegalRepresentative())
                .eqIfPresent(SupplierDO::getRegisteredAddress, reqDTO.getRegisteredAddress())
                .eqIfPresent(SupplierDO::getBusinessScope, reqDTO.getBusinessScope())
                .eqIfPresent(SupplierDO::getBusinessLicense, reqDTO.getBusinessLicense())
                .eqIfPresent(SupplierDO::getLicenceAuthority, reqDTO.getLicenceAuthority())
                .betweenIfPresent(SupplierDO::getRegisteredDate, reqDTO.getRegisteredDate())
                .betweenIfPresent(SupplierDO::getExpirationDate, reqDTO.getExpirationDate())
                .eqIfPresent(SupplierDO::getExpirationDateType, reqDTO.getExpirationDateType())
                .eqIfPresent(SupplierDO::getTriCertMerged, reqDTO.getTriCertMerged())
                .eqIfPresent(SupplierDO::getDepositBank, reqDTO.getDepositBank())
                .eqIfPresent(SupplierDO::getBankAccount, reqDTO.getBankAccount())
                .likeIfPresent(SupplierDO::getAccountName, reqDTO.getAccountName())
                .eqIfPresent(SupplierDO::getOrganizationCertificationCode, reqDTO.getOrganizationCertificationCode())
                .betweenIfPresent(SupplierDO::getOrganizationCertificationDate, reqDTO.getOrganizationCertificationDate())
                .betweenIfPresent(SupplierDO::getOrganizationCertificationExpirationDate, reqDTO.getOrganizationCertificationExpirationDate())
                .eqIfPresent(SupplierDO::getOrganizationCertificationTaxNo, reqDTO.getOrganizationCertificationTaxNo())
                .eqIfPresent(SupplierDO::getOrganizationCertificationAuthority, reqDTO.getOrganizationCertificationAuthority())
                .eqIfPresent(SupplierDO::getRegisteredAddressCod, reqDTO.getRegisteredAddressCod())
                .eqIfPresent(SupplierDO::getStoreAddressCode, reqDTO.getStoreAddressCode())
                .eqIfPresent(SupplierDO::getStoreAddress, reqDTO.getStoreAddress())
                .eqIfPresent(SupplierDO::getSignet, reqDTO.getSignet())
                .eqIfPresent(SupplierDO::getShipmentTemplate, reqDTO.getShipmentTemplate())
                .eqIfPresent(SupplierDO::getQualificationInfos, reqDTO.getQualificationInfos())
                .eqIfPresent(SupplierDO::getProxyIdCard, reqDTO.getProxyIdCard())
                .betweenIfPresent(SupplierDO::getProxyIdCardExpirationDate, reqDTO.getProxyIdCardExpirationDate())
                .eqIfPresent(SupplierDO::getMsfxRefEntId, reqDTO.getMsfxRefEntId())
                .eqIfPresent(SupplierDO::getMsfxEntId, reqDTO.getMsfxEntId())
                .eqIfPresent(SupplierDO::getRelateDistribute, reqDTO.getRelateDistribute())
                .eqIfPresent(SupplierDO::getRemark, reqDTO.getRemark())
                .eqIfPresent(SupplierDO::getDisabled, reqDTO.getDisabled())
                .betweenIfPresent(SupplierDO::getCreateTime, reqDTO.getCreateTime())
                .orderByDesc(SupplierDO::getId));
    }

    /**
     * 根据营业执照号查询供应商信息
     *
     * @param businessLicense 营业执照号
     */
    default SupplierDO selectByBusinessLicense(String businessLicense){
        return selectOne(new LambdaQueryWrapperX<SupplierDO>()
                .eq(SupplierDO::getBusinessLicense, businessLicense));
    }

}