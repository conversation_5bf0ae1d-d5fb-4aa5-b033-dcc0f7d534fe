package com.xyy.saas.localserver.purchase.server.dal.mysql.receive;

import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.localserver.purchase.server.dal.dataobject.receive.ReceiveBillDetailDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 收货单明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface ReceiveBillDetailMapper extends BaseMapperX<ReceiveBillDetailDO> {

//    /**
//     * 获取收货单明细分页
//     * @param reqVO 查询参数
//     * @return 收货单明细分页
//     */
//    default PageResult<ReceiveBillDetailDO> selectPage(ReceiveBillDetailPageReqVO reqVO) {
//        return selectPage(reqVO, new LambdaQueryWrapperX<ReceiveBillDetailDO>()
//                .eqIfPresent(ReceiveBillDetailDO::getBillNo, reqVO.getBillNo())
//                .eqIfPresent(ReceiveBillDetailDO::getProductPref, reqVO.getProductPref())
//                .eqIfPresent(ReceiveBillDetailDO::getLotNo, reqVO.getLotNo())
//                .betweenIfPresent(ReceiveBillDetailDO::getProductionDate, reqVO.getProductionDate())
//                .betweenIfPresent(ReceiveBillDetailDO::getExpiryDate, reqVO.getExpiryDate())
//                .eqIfPresent(ReceiveBillDetailDO::getPrice, reqVO.getPrice())
//                .eqIfPresent(ReceiveBillDetailDO::getTaxRate, reqVO.getTaxRate())
//                .eqIfPresent(ReceiveBillDetailDO::getDiscount, reqVO.getDiscount())
//                .eqIfPresent(ReceiveBillDetailDO::getDiscountedPrice, reqVO.getDiscountedPrice())
//                .eqIfPresent(ReceiveBillDetailDO::getArriveQuantity, reqVO.getArriveQuantity())
//                .eqIfPresent(ReceiveBillDetailDO::getReceiveQuantity, reqVO.getReceiveQuantity())
//                .eqIfPresent(ReceiveBillDetailDO::getRejectQuantity, reqVO.getRejectQuantity())
//                .eqIfPresent(ReceiveBillDetailDO::getReceiveAmount, reqVO.getReceiveAmount())
//                .eqIfPresent(ReceiveBillDetailDO::getAcceptConclusion, reqVO.getAcceptConclusion())
//                .eqIfPresent(ReceiveBillDetailDO::getSampleQuantity, reqVO.getSampleQuantity())
//                .eqIfPresent(ReceiveBillDetailDO::getUnqualifiedQuantity, reqVO.getUnqualifiedQuantity())
//                .eqIfPresent(ReceiveBillDetailDO::getUnqualifiedAmount, reqVO.getUnqualifiedAmount())
//                .eqIfPresent(ReceiveBillDetailDO::getUnqualifiedReason, reqVO.getUnqualifiedReason())
//                .eqIfPresent(ReceiveBillDetailDO::getUnqualifiedPositionGuid,
//                        reqVO.getUnqualifiedPositionGuid())
//                .eqIfPresent(ReceiveBillDetailDO::getQualifiedQuantity, reqVO.getQualifiedQuantity())
//                .eqIfPresent(ReceiveBillDetailDO::getQualifiedAmount, reqVO.getQualifiedAmount())
//                .eqIfPresent(ReceiveBillDetailDO::getQualifiedPositionGuid, reqVO.getQualifiedPositionGuid())
//                .eqIfPresent(ReceiveBillDetailDO::getWarehouseQuantity, reqVO.getWarehouseQuantity())
//                .eqIfPresent(ReceiveBillDetailDO::getWarehouseAmount, reqVO.getWarehouseAmount())
//                .eqIfPresent(ReceiveBillDetailDO::getTreatment, reqVO.getTreatment())
//                .eqIfPresent(ReceiveBillDetailDO::getChannelId, reqVO.getChannelId())
//                .eqIfPresent(ReceiveBillDetailDO::getSterilizationBatchNo, reqVO.getSterilizationBatchNo())
//                .eqIfPresent(ReceiveBillDetailDO::getSourceLineNo, reqVO.getSourceLineNo())
//                .eqIfPresent(ReceiveBillDetailDO::getMedicareProjectCode, reqVO.getMedicareProjectCode())
//                .likeIfPresent(ReceiveBillDetailDO::getMedicareProjectName, reqVO.getMedicareProjectName())
//                .eqIfPresent(ReceiveBillDetailDO::getMedicareProjectLevel, reqVO.getMedicareProjectLevel())
//                .eqIfPresent(ReceiveBillDetailDO::getMedicareMinPackageNum, reqVO.getMedicareMinPackageNum())
//                .eqIfPresent(ReceiveBillDetailDO::getExt, reqVO.getExt())
//                .eqIfPresent(ReceiveBillDetailDO::getRemark, reqVO.getRemark())
//                .betweenIfPresent(ReceiveBillDetailDO::getCreateTime, reqVO.getCreateTime())
//                .orderByDesc(ReceiveBillDetailDO::getId));
//    }

    /**
     * 根据单据编号和租户编号删除
     * @param billNo 单据编号
     * @param tenantId 租户编号
     */
    default void deleteByBillNoAndTenant(String billNo, Long tenantId) {
        delete(new LambdaQueryWrapperX<ReceiveBillDetailDO>()
                .eq(ReceiveBillDetailDO::getBillNo, billNo)
                .eq(ReceiveBillDetailDO::getTenantId, tenantId));
    }

}