package com.xyy.saas.localserver.purchase.server.dal.dataobject.extend;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import lombok.*;
import java.time.LocalDateTime;
import java.math.BigDecimal;

import com.baomidou.mybatisplus.annotation.*;

/**
 * 采购-运输信息 DO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@TableName("saas_purchase_transport")
// @KeySequence("saas_purchase_transport_seq") // 用于
// Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PurchaseTransportDO extends TenantBaseDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 采购单/收货单/配送单号 */
    private String billNo;

    /** 委托经办人 */
    private String authorizedRepresentative;

    /** 承运人 */
    private String carrierName;

    /** 承运单位名称 */
    private String carrierEntity;

    /** 承运单位统一社会信用代码（国家标准长度） */
    private String carrierEntityUscc;

    /** 运输方式（如：road/air/ship） */
    private String transportMode;

    /** 启运地址 */
    private String departureAddress;

    /** 实际启运时间 */
    private LocalDateTime departureTime;

    /** 启运温度（℃） */
    private BigDecimal departureTemperature;

    /** 启运湿度（%） */
    private BigDecimal departureHumidity;

    /** 运单号 */
    private String trackingNo;

    /** 运输工具类型：1-汽运、2-货运、3-冷藏车、4-冷藏箱、5-保温箱、6-其他 */
    private Integer transportVehicle;

    /** 运输工具标识（车牌号/航班号等） */
    private String vehicleIdentifier;

    /** 驾驶员姓名 */
    private String driver;

    /** 驾驶员证件 */
    private String driverCredential;

    /** 运输温度（℃）监控（json格式，每天的温度） */
    private String transportTemperatureMonitor;

    /** 运输湿度（%）监控（json格式，每天的湿度） */
    private String transportHumidityMonitor;

    /** 到货时间 */
    private LocalDateTime arrivalTime;

    /** 到货温度（℃） */
    private BigDecimal arrivalTemperature;

    /** 到货湿度（%） */
    private BigDecimal arrivalHumidity;

    /** 随货同行单时间 */
    private LocalDateTime shipmentTime;

    /** 随货同行单文件地址 */
    private String shipmentFileUrl;

    /** 备注 */
    private String remark;

}