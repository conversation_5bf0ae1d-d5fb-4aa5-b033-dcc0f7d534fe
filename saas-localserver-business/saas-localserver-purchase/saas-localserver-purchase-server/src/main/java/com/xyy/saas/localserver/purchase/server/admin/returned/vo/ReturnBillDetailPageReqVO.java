package com.xyy.saas.localserver.purchase.server.admin.returned.vo;

import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 退货单明细分页 Request VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 退货单明细分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class ReturnBillDetailPageReqVO extends PageParam {

    /** 单号 */
    @Schema(description = "单号")
    private String billNo;

    /** 商品编码 */
    @Schema(description = "商品编码")
    private String productPref;

    /** 批号 */
    @Schema(description = "批号")
    private String lotNo;

    /** 生产日期 */
    @Schema(description = "生产日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] productionDate;

    /** 有效期至 */
    @Schema(description = "有效期至")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDate[] expiryDate;

    /** 总部货位编号 */
    @Schema(description = "总部货位编号")
    private String positionGuid;

    /** 税率 */
    @Schema(description = "税率")
    private BigDecimal inTaxRate;

    /** 含税成本价 */
    @Schema(description = "含税成本价", example = "26185")
    private BigDecimal price;

    /** 销项税率 */
    @Schema(description = "销项税率")
    private BigDecimal outTaxRate;

    /** 出库数量 */
    @Schema(description = "出库数量")
    private BigDecimal outboundQuantity;

    /** 成本均价 */
    @Schema(description = "成本均价", example = "14464")
    private BigDecimal outboundPrice;

    /** 含税成本金额 */
    @Schema(description = "含税成本金额")
    private BigDecimal outboundAmount;

    /** 渠道ID */
    @Schema(description = "渠道ID", example = "24434")
    private String channelId;

    /** 医保项目编码 */
    @Schema(description = "医保项目编码")
    private String medicareProjectCode;

    /** 医保项目名称 */
    @Schema(description = "医保项目名称", example = "王五")
    private String medicareProjectName;

    /** 医保项目等级 */
    @Schema(description = "医保项目等级")
    private Integer medicareProjectLevel;

    /** 医保最小包装数量 */
    @Schema(description = "医保最小包装数量")
    private Integer medicareMinPackageNum;

    /** 扩展信息 */
    @Schema(description = "扩展信息")
    private ExtDTO ext;

    /** 退货原因 */
    @Schema(description = "退货原因(1-破损、2-召回、3-滞销、4-过期失效、5-近效期、6-质量问题、7-其他)", example = "不香")
    private Integer returnReason;

    /** 备注 */
    @Schema(description = "备注", example = "你猜")
    private String remark;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;
}