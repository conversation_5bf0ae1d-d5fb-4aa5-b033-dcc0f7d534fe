package com.xyy.saas.localserver.purchase.server.dal.dataobject.receive;

import cn.iocoder.yudao.framework.tenant.core.db.TenantBaseDO;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.xyy.saas.localserver.purchase.api.extend.dto.ExtDTO;
import lombok.*;
import java.time.LocalDate;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.*;

/**
 * 收货单明细 DO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@TableName("saas_purchase_receive_bill_detail")
// @KeySequence("saas_purchase_receive_bill_detail_seq") // 用于
// Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ReceiveBillDetailDO extends TenantBaseDO {

    /** 主键ID */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /** 验收入库单号 */
    private String billNo;

    /** 商品编码 */
    private String productPref;

    /** 批号 */
    private String lotNo;

    /** 生产日期（YYYY-MM-DD） */
    private LocalDate productionDate;

    /** 有效期至（YYYY-MM-DD） */
    private LocalDate expiryDate;

    /** 含税成本价（单价） */
    private BigDecimal price;

    /** 税率（百分比） */
    private BigDecimal taxRate;

    /** 折扣（百分比） */
    private BigDecimal discount;

    /** 折后含税单价 */
    private BigDecimal discountedPrice;

    /** 到货数量（发货数量） */
    private BigDecimal arriveQuantity;

    /** 收货数量 */
    private BigDecimal receiveQuantity;

    /** 拒收数量 */
    private BigDecimal rejectQuantity;

    /** 收货金额（折后单价*收货数量） */
    private BigDecimal receiveAmount;

    /** 验收结论（1-合格、0-锁定） */
    private Boolean acceptConclusion;

    /** 抽样数量 */
    private BigDecimal sampleQuantity;

    /** 不合格品数量 */
    private BigDecimal unqualifiedQuantity;

    /** 不合格品总金额（折后总金额） */
    private BigDecimal unqualifiedAmount;

    /** 不合格原因 */
    private String unqualifiedReason;

    /** 不合格品隔离区编码 */
    private String unqualifiedPositionGuid;

    /** 合格品数量 */
    private BigDecimal qualifiedQuantity;

    /** 合格品总金额（折后总金额） */
    private BigDecimal qualifiedAmount;

    /** 合格品储存区编码 */
    private String qualifiedPositionGuid;

    /** 入库数量 */
    private BigDecimal warehouseQuantity;

    /** 入库金额（折后总金额） */
    private BigDecimal warehouseAmount;

    /** 处理措施 */
    private String treatment;

    /** 渠道ID */
    private String channelId;

    /** 灭菌批次 */
    private String sterilizationBatchNo;

    /** 源行号（和三方ERP对接时需要） */
    private String sourceLineNo;

    /** 医保项目编码 */
    private String medicareProjectCode;

    /** 医保项目名称 */
    private String medicareProjectName;

    /** 医保项目等级 */
    private Integer medicareProjectLevel;

    /** 医保最小包装数量 */
    private Integer medicareMinPackageNum;

    /** 扩展信息（当前商品信息） */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private ExtDTO ext;

    /** 备注 */
    private String remark;
}