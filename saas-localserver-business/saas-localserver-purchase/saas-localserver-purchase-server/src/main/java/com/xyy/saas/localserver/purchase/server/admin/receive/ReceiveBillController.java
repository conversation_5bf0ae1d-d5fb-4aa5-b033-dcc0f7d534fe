package com.xyy.saas.localserver.purchase.server.admin.receive;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.datasync.client.constants.DataContextHolder;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillDTO;
import com.xyy.saas.localserver.purchase.api.receive.dto.ReceiveBillPageReqDTO;
import com.xyy.saas.localserver.purchase.server.admin.receive.vo.ReceiveBillPageReqVO;
import com.xyy.saas.localserver.purchase.server.admin.receive.vo.ReceiveBillRespVO;
import com.xyy.saas.localserver.purchase.server.admin.receive.vo.ReceiveBillSaveReqVO;
import com.xyy.saas.localserver.purchase.server.convert.receive.ReceiveBillConvert;
import com.xyy.saas.localserver.purchase.server.service.receive.ReceiveBillService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.apache.commons.lang3.StringUtils;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import java.io.IOException;
import java.util.List;
import java.util.Optional;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

/**
 * 收货单 Controller
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "管理后台 - 收货单")
@RestController
@RequestMapping("/saas/purchase/receive-bill")
@Validated
public class ReceiveBillController {

    @Resource
    private ReceiveBillService receiveBillService;

    /**
     * 收货操作
     * 处理流程：
     * 1. 校验收货详情：验证收货信息的完整性和正确性
     * 2. 对象转换：将VO对象转换为DTO对象
     * 3. 调用服务：执行收货操作
     *
     * @param createReqVO 收货单创建请求
     * @return 收货单编号
     */
    @PostMapping("/receive")
    @Operation(summary = "收货")
    @PreAuthorize("@ss.hasPermission('saas:receive-bill:receive')")
    public CommonResult<Long> receive(@Valid @RequestBody ReceiveBillSaveReqVO createReqVO) {
        // 1. 校验收货详情：验证收货信息的完整性和正确性
        createReqVO.validateReceiveDetails();

        // 2. 对象转换：将VO对象转换为DTO对象
        ReceiveBillDTO receiveBill = ReceiveBillConvert.INSTANCE.convert2ReceiveDTO(createReqVO);
        return success(receiveBillService.receive(receiveBill));
    }

    /**
     * 验收操作
     * 处理流程：
     * 1. 校验验收详情：验证验收信息的完整性和正确性
     * 2. 对象转换：将VO对象转换为DTO对象
     * 3. 调用服务：执行验收操作
     *
     * @param createReqVO 验收单创建请求
     * @return 验收单编号
     */
    @PostMapping("/accept")
    @Operation(summary = "验收")
    @PreAuthorize("@ss.hasPermission('saas:receive-bill:accept')")
    public CommonResult<Long> accept(@Valid @RequestBody ReceiveBillSaveReqVO createReqVO) {
        // 1. 校验验收详情：验证验收信息的完整性和正确性
        createReqVO.validateAcceptDetails();

        // 2. 对象转换：将VO对象转换为DTO对象
        ReceiveBillDTO receiveBill = ReceiveBillConvert.INSTANCE.convert2AcceptDTO(createReqVO);
        return success(receiveBillService.accept(receiveBill));
    }

    /**
     * 入库操作
     * 处理流程：
     * 1. 校验入库详情：验证入库信息的完整性和正确性
     * 2. 对象转换：将VO对象转换为DTO对象
     * 3. 调用服务：执行入库操作
     *
     * @param createReqVO 入库单创建请求
     * @return 入库单编号
     */
    @PostMapping("/warehousing")
    @Operation(summary = "入库")
    @PreAuthorize("@ss.hasPermission('saas:receive-bill:warehousing')")
    public CommonResult<Long> warehousing(@Valid @RequestBody ReceiveBillSaveReqVO createReqVO) {
        // 1. 校验入库详情：验证入库信息的完整性和正确性
        createReqVO.validateWarehousingDetails();

        // 2. 对象转换：将VO对象转换为DTO对象
        ReceiveBillDTO receiveBill = ReceiveBillConvert.INSTANCE.convert2WarehouseDTO(createReqVO);
        return success(receiveBillService.warehousing(receiveBill));
    }

    /**
     * 回退操作
     * 处理流程：
     * 1. 调用服务：执行回退操作
     *
     * @param id 要回退的单据编号
     * @return 操作结果
     */
    @PostMapping("/rollback")
    @Operation(summary = "回退")
    @PreAuthorize("@ss.hasPermission('saas:receive-bill:rollback')")
    public CommonResult<Boolean> oneStepWarehousing(@RequestParam("id") Long id) {
        receiveBillService.rollback(id);
        return success(true);
    }

    /**
     * 一步入库操作
     * 处理流程：
     * 1. 校验供应商：验证供应商信息的有效性
     * 2. 校验收货详情：验证收货信息的完整性和正确性
     * 3. 校验验收详情：验证验收信息的完整性和正确性
     * 4. 校验入库详情：验证入库信息的完整性和正确性
     * 5. 对象转换：将VO对象转换为DTO对象
     * 6. 调用服务：执行一步入库操作
     *
     * @param createReqVO 一步入库创建请求
     * @return 拆分后的单据数量
     */
    @PostMapping("/oneStepWarehousing")
    @Operation(summary = "一步入库")
    @PreAuthorize("@ss.hasPermission('saas:receive-bill:oneStepWarehousing')")
    public CommonResult<Integer> oneStepWarehousing(@Valid @RequestBody ReceiveBillSaveReqVO createReqVO) {
        // 1. 校验供应商：验证供应商信息的有效性
        createReqVO.validateSupplier();
        // 2. 校验收货详情：验证收货信息的完整性和正确性
        createReqVO.validateReceiveDetails();
        // 3. 校验验收详情：验证验收信息的完整性和正确性
        createReqVO.validateAcceptDetails();
        // 4. 校验入库详情：验证入库信息的完整性和正确性
        createReqVO.validateWarehousingDetails();

        // 5. 对象转换：将VO对象转换为DTO对象
        ReceiveBillDTO receiveBill = ReceiveBillConvert.INSTANCE.convert2OneStepWarehousingDTO(createReqVO);
        return success(receiveBillService.oneStepWarehousing(receiveBill));
    }

    /**
     * 拒收入库操作
     * 处理流程：
     * 1. 对象转换：将VO对象转换为DTO对象
     * 2. 调用服务：执行拒收入库操作
     *
     * @param createReqVO 拒收入库创建请求
     * @return 操作结果
     */
    @PostMapping("/rejectWarehousing")
    @Operation(summary = "拒收入库")
    @PreAuthorize("@ss.hasPermission('saas:receive-bill:oneStepWarehousing')")
    public CommonResult<Boolean> rejectWarehousing(@Valid @RequestBody ReceiveBillSaveReqVO createReqVO) {
        // 1. 对象转换：将VO对象转换为DTO对象
        ReceiveBillDTO receiveBill = ReceiveBillConvert.INSTANCE.convert2RejectWarehousingDTO(createReqVO);

        // 2. 调用拒收入库
        receiveBillService.rejectWarehousing(receiveBill);

        return success(true);
    }

    /**
     * 删除收货单
     * 处理流程：
     * 1. 调用服务：执行删除操作
     *
     * @param id 要删除的单据编号
     * @return 操作结果
     */
    @DeleteMapping("/delete")
    @Operation(summary = "删除收货单")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:receive-bill:delete')")
    public CommonResult<Boolean> deleteReceiveBill(@RequestParam("id") Long id) {
        receiveBillService.deleteReceiveBill(id);
        return success(true);
    }

    /**
     * 获取收货单
     * 处理流程：
     * 1. 调用服务：获取收货单信息
     * 2. 对象转换：将DTO对象转换为VO对象
     *
     * @param id 单据编号
     * @return 收货单信息
     */
    @GetMapping("/get")
    @Operation(summary = "获得收货单")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:receive-bill:query')")
    public CommonResult<ReceiveBillRespVO> getReceiveBill(@RequestParam("id") Long id) {
        ReceiveBillDTO receiveBill = receiveBillService.getReceiveBill(id);
        return success(ReceiveBillConvert.INSTANCE.convert2RespVO(receiveBill));
    }

    /**
     * 获取收货单及详情
     * 处理流程：
     * 1. 获取租户ID：如果未提供则使用当前租户ID
     * 2. 调用服务：获取收货单及详情信息
     * 3. 对象转换：将DTO对象转换为VO对象
     *
     * @param billNo   单据编号
     * @param tenantId 租户编号（可选）
     * @return 收货单及详情信息
     */
    @GetMapping("/getBillAndDetails")
    @Operation(summary = "获得收货单分页")
    @PreAuthorize("@ss.hasPermission('saas:receive-bill:getBillAndDetails')")
    public CommonResult<ReceiveBillRespVO> getReceiveBillAndDetails(@RequestParam("billNo") String billNo,
            @RequestParam(value = "tenantId", required = false) Long tenantId) {
        ReceiveBillDTO receiveBill = receiveBillService.getReceiveBillAndDetails(
                billNo,
                Optional.ofNullable(tenantId)
                        .orElseGet(() -> {
                            String tenantIdStr = DataContextHolder.getTenantId();
                            return StringUtils.isNotBlank(tenantIdStr)
                                    ? Long.valueOf(tenantIdStr)
                                    : null; // 或默认租户ID
                        })
        );
        return success(ReceiveBillConvert.INSTANCE.convert2RespVO(receiveBill));
    }

    /**
     * 获取收货单分页
     * 处理流程：
     * 1. 处理查询参数：处理分页查询条件
     * 2. 对象转换：将VO对象转换为DTO对象
     * 3. 调用服务：获取分页数据
     * 4. 对象转换：将DTO对象转换为VO对象
     *
     * @param pageReqVO 分页查询条件
     * @return 分页结果
     */
    @GetMapping("/page")
    @Operation(summary = "获得收货单分页")
    @PreAuthorize("@ss.hasPermission('saas:receive-bill:query')")
    public CommonResult<PageResult<ReceiveBillRespVO>> getReceiveBillPage(@Valid ReceiveBillPageReqVO pageReqVO) {
        // 1. 处理查询参数：处理分页查询条件
        ReceiveBillConvert.INSTANCE.processQueryParams(pageReqVO);
        // 2. 对象转换：将VO对象转换为DTO对象
        ReceiveBillPageReqDTO pageReqDTO = ReceiveBillConvert.INSTANCE.convert2DTO(pageReqVO);
        // 3. 调用服务：获取分页数据
        PageResult<ReceiveBillDTO> pageResult = receiveBillService.getReceiveBillPage(pageReqDTO);
        // 4. 对象转换：将DTO对象转换为VO对象
        return success(ReceiveBillConvert.INSTANCE.convert2VO(pageResult));
    }

    /**
     * 导出收货单Excel
     * 处理流程：
     * 1. 设置分页大小：设置为不分页
     * 2. 处理查询参数：处理分页查询条件
     * 3. 对象转换：将VO对象转换为DTO对象
     * 4. 调用服务：获取数据列表
     * 5. 导出Excel：将数据导出为Excel文件
     *
     * @param pageReqVO 分页查询条件
     * @param response  HTTP响应对象
     * @throws IOException 导出过程中的IO异常
     */
    @GetMapping("/export-excel")
    @Operation(summary = "导出收货单 Excel")
    @PreAuthorize("@ss.hasPermission('saas:receive-bill:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportReceiveBillExcel(@Valid ReceiveBillPageReqVO pageReqVO,
            HttpServletResponse response) throws IOException {
        // 1. 设置分页大小：设置为不分页
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        // 2. 处理查询参数：处理分页查询条件
        ReceiveBillConvert.INSTANCE.processQueryParams(pageReqVO);
        // 3. 对象转换：将VO对象转换为DTO对象
        ReceiveBillPageReqDTO pageReqDTO = ReceiveBillConvert.INSTANCE.convert2DTO(pageReqVO);
        // 4. 调用服务：获取数据列表
        List<ReceiveBillDTO> list = receiveBillService.getReceiveBillPage(pageReqDTO).getList();

        // 5. 导出Excel：将数据导出为Excel文件
        ExcelUtils.write(response, "收货单.xls", "数据", ReceiveBillRespVO.class,
                ReceiveBillConvert.INSTANCE.convert2RespVOList(list));
    }
}