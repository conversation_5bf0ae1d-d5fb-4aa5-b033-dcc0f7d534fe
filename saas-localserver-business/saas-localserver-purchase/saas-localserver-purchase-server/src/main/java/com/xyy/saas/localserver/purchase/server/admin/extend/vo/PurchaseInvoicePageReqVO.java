package com.xyy.saas.localserver.purchase.server.admin.extend.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * 管理后台 - 采购-发票信息分页 Request VO
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "管理后台 - 采购-发票信息分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class PurchaseInvoicePageReqVO extends PageParam {

    /** 采购单/收货单号 */
    @Schema(description = "采购单/收货单号")
    private String billNo;

    /** 发票号 */
    @Schema(description = "发票号")
    private String invoiceNo;

    /** 发票代码 */
    @Schema(description = "发票代码")
    private String invoiceCode;

    /** 发票类型 1：电子普通发票 2：增值税发票 3：纸质普通发票，用于委托配送 */
    @Schema(description = "发票类型 1：电子普通发票 2：增值税发票 3：纸质普通发票，用于委托配送", example = "1")
    private String invoiceType;

    /** 发票是否随货通行 0：不随行 1：随行，用于委托配送 */
    @Schema(description = "发票是否随货通行 0：不随行 1：随行，用于委托配送")
    private Boolean accompanyingShipment;

    /** 发票金额 */
    @Schema(description = "发票金额")
    private BigDecimal invoiceAmount;

    /** 发票文件名称 */
    @Schema(description = "发票文件名称", example = "李四")
    private String invoiceFileName;

    /** 发票文件地址 */
    @Schema(description = "发票文件地址", example = "https://www.iocoder.cn")
    private String invoiceFileUrl;

    /** 实际开(发)票时间 */
    @Schema(description = "实际开(发)票时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] issuedTime;

    /** 备注 */
    @Schema(description = "备注", example = "你说的对")
    private String remark;

    /** 创建时间 */
    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}