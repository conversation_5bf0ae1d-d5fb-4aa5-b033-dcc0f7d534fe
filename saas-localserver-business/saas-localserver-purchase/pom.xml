<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.xyy.saas</groupId>
        <artifactId>saas-localserver-business</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>saas-localserver-purchase</artifactId>
    <version>${revision}</version>
    <packaging>pom</packaging>
    <description>saas-药店-采购服务</description>

    <modules>
        <module>saas-localserver-purchase-api</module>
        <module>saas-localserver-purchase-server</module>
    </modules>


    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${maven-springboot-plugin.version}</version>
            </plugin>
        </plugins>
    </build>

</project>
