package com.xyy.saas.localserver.purchase.enums.stockout;

import lombok.AllArgsConstructor;
import lombok.Getter;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.RECEIVE_BILL_STATUS_NOT_EXISTS;

/**
 * 缺货单状态枚举
 */
@Getter
@AllArgsConstructor
public enum StockoutBillStatusEnum {

    STOCKOUT(1, "缺货"),
    COMPLETE(2, "已完成（已补货）");

    /**
     * 状态码
     */
    private final Integer code;

    /**
     * 状态名称
     */
    private final String name;

    /**
     * 根据状态码获取枚举
     */
    public static StockoutBillStatusEnum fromCode(Integer code) {
        for (StockoutBillStatusEnum status : values()) {
            if (status.getCode().equals(code)) {
                return status;
            }
        }
        throw exception(RECEIVE_BILL_STATUS_NOT_EXISTS);
    }
}