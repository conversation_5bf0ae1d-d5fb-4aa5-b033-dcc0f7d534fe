package com.xyy.saas.localserver.purchase.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.purchase.enums.ErrorCodeConstants.MEDICINE_TYPE_NOT_EXISTS;

/**
 * 药品类型枚举
 */
@Getter
@AllArgsConstructor
public enum MedicineTypeEnum {

    CHINESE_MEDICINE(1, "中药"),
    NON_CHINESE_MEDICINE(2, "非中药");

    private final Integer code;
    private final String description;

    /**
     * 根据code获取枚举
     *
     * @param code 编码
     * @return 枚举值
     */
    public static MedicineTypeEnum fromCode(Integer code) {
        for (MedicineTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        throw exception(MEDICINE_TYPE_NOT_EXISTS);
    }
}