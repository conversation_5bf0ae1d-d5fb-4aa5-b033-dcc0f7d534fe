package com.xyy.saas.localserver.purchase.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * System 错误码枚举类
 * <p>
 * system 系统，使用 1-003-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 供应商相关错误码 ==========
    ErrorCode SUPPLIER_NOT_EXISTS = new ErrorCode(1_001_000_000, "供应商信息不存在");
    ErrorCode SUPPLIER_BUSINESS_LICENSE_EXISTS = new ErrorCode(1_001_000_001, "供应商营业执照已存在");
    ErrorCode TENANT_SUPPLIER_RELATION_NOT_EXISTS = new ErrorCode(1_001_001_000, "租户-供应商-关联关系不存在");
    ErrorCode TENANT_SUPPLIER_RELATION_EXISTS = new ErrorCode(1_001_001_001, "供应商已开通");
    ErrorCode TENANT_SUPPLIER_SALES_NOT_EXISTS = new ErrorCode(1_001_002_000, "租户-供应商-销售人员信息不存在");

    // ========== 采购单相关错误码 ==========
    ErrorCode PURCHASE_BILL_NOT_EXISTS = new ErrorCode(1_002_000_000, "采购单不存在");
    ErrorCode PURCHASE_BILL_CREATE_PURCHASE_NOT_SUPPORT = new ErrorCode(1_002_000_001, "不支持创建采购单");
    ErrorCode PURCHASE_BILL_CREATE_REQUISITION_NOT_SUPPORT = new ErrorCode(1_002_000_002, "不支持创建要货单");
    ErrorCode PURCHASE_BILL_CREATE_ALLOCATION_NOT_SUPPORT = new ErrorCode(1_002_000_003, "不支持创建调剂单");
    ErrorCode PURCHASE_BILL_CREATE_DISTRIBUTION_NOT_SUPPORT = new ErrorCode(1_002_000_004, "不支持创建铺货单");
    ErrorCode PURCHASE_BILL_PLANNER_NOT_NULL = new ErrorCode(1_002_000_006, "采购计划员不能为空");
    ErrorCode PURCHASE_BILL_SUPPLIER_NOT_EXISTS = new ErrorCode(1_002_000_007, "采购供应商不存在");
    ErrorCode PURCHASE_BILL_OUTBOUND_TENANT_ID_NOT_NULL = new ErrorCode(1_002_000_008, "出库门店租户id不能为空");
    ErrorCode PURCHASE_BILL_INBOUND_TENANT_ID_NOT_NULL = new ErrorCode(1_002_000_09, "收货门店租户id不能为空");
    ErrorCode PURCHASE_BILL_PURCHASER_NOT_NULL = new ErrorCode(1_002_000_010, "采购员不能为空");
    ErrorCode PURCHASE_BILL_ALLOCATOR_NOT_NULL = new ErrorCode(1_002_000_010, "调剂申请员不能为空");
    ErrorCode PURCHASE_BILL_REQUISITIONER_NOT_NULL = new ErrorCode(1_002_000_010, "要货员不能为空");
    ErrorCode PURCHASE_BILL_DISTRIBUTER_NOT_NULL = new ErrorCode(1_002_000_010, "发货员不能为空");
    ErrorCode PURCHASE_BILL_TYPE_NOT_SUPPORT = new ErrorCode(1_002_000_011, "采购单类型暂不支持");
    ErrorCode PURCHASE_BILL_RECEIVER_NOT_NULL = new ErrorCode(1_002_000_012, "收货人不能为空");
    ErrorCode PURCHASE_BILL_RECEIVER_PHONE__NOT_NULL = new ErrorCode(1_002_000_013, "收货人手机号不能为空");
    ErrorCode PURCHASE_BILL_RECEIVER_AREA_NOT_NULL = new ErrorCode(1_002_000_014, "收货地址不能为空");
    ErrorCode PURCHASE_BILL_RECEIVER_ADDRESS_NOT_NULL = new ErrorCode(1_002_000_015, "收货地址不能为空");
    ErrorCode PURCHASE_BILL_UPDATE_STATUS_CONFLICT = new ErrorCode(1_002_000_016, "采购单状态更新冲突，请重试操作");
    ErrorCode REQUISITION_BILL_STATUS_NOT_SUPPORT_REVOKE = new ErrorCode(1_004_000_017, "当前要货单状态不允许撤销");
    ErrorCode PURCHASE_BILL_STATUS_NOT_SUPPORT_REVOKE = new ErrorCode(1_004_000_017, "当前采购单状态不允许撤销");
    ErrorCode DISTRIBUTION_BILL_STATUS_NOT_SUPPORT_REVOKE = new ErrorCode(1_004_000_017, "当前配送但状态不允许撤销");
    ErrorCode ALLOCATION_BILL_STATUS_NOT_SUPPORT_REVOKE = new ErrorCode(1_004_000_017, "当前调剂单状态不允许撤销");

    ErrorCode PURCHASE_BILL_DETAIL_NOT_EXISTS = new ErrorCode(1_002_001_000, "采购单明细不存在");
    ErrorCode PURCHASE_BILL_DETAIL_PRICE_NOT_NULL = new ErrorCode(1_002_001_001, "采购商品单价不能为空");
    ErrorCode PURCHASE_BILL_DETAIL_AMOUNT_NOT_NULL = new ErrorCode(1_002_001_002, "采购商品总价必须大于0");

    // ========== 收货单相关错误码 ==========
    ErrorCode RECEIVE_BILL_NOT_EXISTS = new ErrorCode(1_003_000_000, "收货单不存在");
    ErrorCode RECEIVE_BILL_PURCHASE_RECEIVE_NOT_SUPPORT = new ErrorCode(1_003_000_001, "无法操作采购收货");
    ErrorCode RECEIVE_BILL_DISTRIBUTION_NOT_SUPPORT = new ErrorCode(1_003_000_002, "无法操作总部铺货");
    ErrorCode RECEIVE_BILL_REQUISITION_NOT_SUPPORT = new ErrorCode(1_003_000_003, "无法操作门店要货出库");
    ErrorCode RECEIVE_BILL_ALLOCATION_NOT_SUPPORT = new ErrorCode(1_003_000_004, "无法操作门店调剂");
    ErrorCode RECEIVE_BILL_REJECT_NOT_SUPPORT = new ErrorCode(1_003_000_005, "无法操作门店拒收");
    ErrorCode RECEIVE_BILL_RETURN_NOT_SUPPORT = new ErrorCode(1_003_000_005, "无法操作门店退货");
    ErrorCode RECEIVE_BILL_TYPE_NOT_EXISTS = new ErrorCode(1_003_000_000, "收货单类型不存在");

    ErrorCode RECEIVE_BILL_DISTRIBUTION_NOT_EXISTS = new ErrorCode(1_003_000_004, "总部铺货单不存在");
    ErrorCode RECEIVE_BILL_REQUISITION_NOT_EXISTS = new ErrorCode(1_003_000_005, "要货单不存在");
    ErrorCode RECEIVE_BILL_ORDER_NOT_EXISTS = new ErrorCode(1_003_000_006, "采购订单不存在");
    ErrorCode RECEIVE_BILL_DELIVERER_NOT_NULL = new ErrorCode(1_003_000_007, "配送员不能为空");
    ErrorCode RECEIVE_BILL_CHECKER_NOT_NULL = new ErrorCode(1_003_000_008, "复核员不能为空");
    ErrorCode RECEIVE_BILL_RECEIVER_NOT_NULL = new ErrorCode(1_003_000_009, "收货员不能为空");
    ErrorCode RECEIVE_BILL_WAREHOUSER_NOT_NULL = new ErrorCode(1_003_000_010, "入库员不能为空");
    ErrorCode RECEIVE_BILL_TYPE_NOT_SUPPORT = new ErrorCode(1_003_000_011, "收货单类型暂不支持");
    ErrorCode RECEIVE_RECEIVER_NOT_EXISTS = new ErrorCode(1_003_000_012, "收货人不存在");
    ErrorCode RECEIVE_DETAIL_NOT_EXISTS = new ErrorCode(1_003_000_013, "收货明细不存在");
    ErrorCode RECEIVE_QUANTITY_NOT_NULL = new ErrorCode(1_003_000_014, "收货数量不能为空或小于等于0");
    ErrorCode RECEIVE_BILL_STATUS_NOT_EXISTS = new ErrorCode(1_003_000_015, "收货单状态不合法");
    ErrorCode RECEIVE_BILL_ROLLBACK_STATUS_ERROR = new ErrorCode(1_003_000_016, "当前状态不允许回退");
    ErrorCode RECEIVE_BILL_SUPPLIER_NOT_EXISTS = new ErrorCode(1_003_000_017, "收货单供应商不存在");

    ErrorCode RECEIVE_BILL_DETAIL_NOT_EXISTS = new ErrorCode(1_003_001_000, "收货单明细不存在");
    ErrorCode RECEIVE_BILL_DETAIL_RECEIVE_QUANTITY_NOT_NULL = new ErrorCode(1_003_001_001, "收货数量不能为空");
    ErrorCode RECEIVE_BILL_DETAIL_PRICE_NOT_NULL = new ErrorCode(1_003_001_002, "单价不能为空");
    ErrorCode RECEIVE_BILL_DETAIL_DISCOUNT_NOT_NULL = new ErrorCode(1_003_001_003, "折扣不能为空");
    ErrorCode RECEIVE_BILL_DETAIL_DISCOUNTED_PRICE_NOT_NULL = new ErrorCode(1_003_001_004, "折后价不能为空");
    ErrorCode RECEIVE_BILL_DETAIL_RECEIVE_AMOUNT_NOT_NULL = new ErrorCode(1_003_001_005, "收货金额不能为空");
    ErrorCode RECEIVE_BILL_DETAIL_SAMPLE_QUANTITY_NOT_NULL = new ErrorCode(1_003_001_006, "抽样数量不能为空");
    ErrorCode RECEIVE_BILL_DETAIL_QUALIFIED_QUANTITY_NOT_NULL = new ErrorCode(1_003_001_007, "合格数量不能为空");
    ErrorCode RECEIVE_BILL_DETAIL_UNQUALIFIED_QUANTITY_NOT_NULL = new ErrorCode(1_003_001_008, "不合格数量不能为空");
    ErrorCode RECEIVE_BILL_DETAIL_ACCEPT_CONCLUSION_NOT_NULL = new ErrorCode(1_003_001_009, "验收结论不能为空");
    ErrorCode RECEIVE_BILL_DETAIL_UNQUALIFIED_REASON_NOT_NULL = new ErrorCode(1_003_001_010, "不合格原因不能为空");
    ErrorCode RECEIVE_BILL_DETAIL_TREATMENT_NOT_NULL = new ErrorCode(1_003_001_011, "处理措施不能为空");
    ErrorCode RECEIVE_BILL_DETAIL_QUALIFIED_POSITION_NOT_NULL = new ErrorCode(1_003_001_012, "合格品存放货位不能为空");
    ErrorCode RECEIVE_BILL_DETAIL_UNQUALIFIED_POSITION_NOT_NULL = new ErrorCode(1_003_001_013, "不合格（待处理）品存放货位不能为空");

    // ========== 退货单相关错误码 ==========
    ErrorCode RETURN_BILL_NOT_EXISTS = new ErrorCode(1_004_000_000, "退货单不存在");
    ErrorCode STORE_RETURN_NOT_SUPPORT = new ErrorCode(1_004_000_001, "不支持创建门店退货单");
    ErrorCode PURCHASE_RETURN_NOT_SUPPORT = new ErrorCode(1_004_000_002, "不支持创建采购退货单");
    ErrorCode PURCHASE_RETURN_SUPPLIER_NOT_EXISTS = new ErrorCode(1_004_000_003, "采购退货供应商不存在");
    ErrorCode PURCHASE_RETURN_BILL_TYPE_NOT_SUPPORT = new ErrorCode(1_004_000_004, "退货单类型暂不支持");
    ErrorCode STORE_RETURN_BILL_NOT_EXISTS = new ErrorCode(1_004_000_005, "门店退货单不存在");
    ErrorCode RETURN_BILL_STATUS_NOT_SUPPORT_REVOKE = new ErrorCode(1_004_000_006, "当前退货单状态不允许撤销");
    ErrorCode RETURN_BILL_DETAIL_NOT_EXISTS = new ErrorCode(1_004_001_000, "退货单明细不存在");
    ErrorCode RETURN_BILL_DETAIL_PRICE_NOT_NULL = new ErrorCode(1_004_001_001, "退货商品成本单价不能为空");
    ErrorCode RETURN_BILL_DETAIL_OUTBOUND_PRICE_NOT_NULL = new ErrorCode(1_004_001_001, "商品出库单价不能为空");
    ErrorCode RETURN_BILL_DETAIL_OUTBOUND_AMOUNT_NOT_NULL = new ErrorCode(1_004_001_002, "商品出库单金额不能为空");

    // ========== 要货单相关错误码 ==========
    ErrorCode PURCHASE_REQUISITION_BILL_NOT_EXISTS = new ErrorCode(1_005_000_000, "（连锁门店）要货单信息不存在");
    ErrorCode PURCHASE_REQUISITION_BILL_DETAIL_NOT_EXISTS = new ErrorCode(1_005_001_000, "（连锁门店）要货单明细不存在");

    // ========== 总部单相关错误码 ==========
    ErrorCode PURCHASE_DELIVERY_BILL_NOT_EXISTS = new ErrorCode(1_006_000_000, "（总部铺货单信息不存在");
    ErrorCode PURCHASE_DELIVERY_BILL_DETAIL_NOT_EXISTS = new ErrorCode(1_006_001_000, "总部铺货单明细不存在");

    // ========== 出库单相关错误码 ==========
    ErrorCode PURCHASE_OUTBOUND_BILL_NOT_EXISTS = new ErrorCode(1_007_000_000, "（连锁总部）出库单信息不存在");
    ErrorCode PURCHASE_OUTBOUND_BILL_DETAIL_NOT_EXISTS = new ErrorCode(1_007_001_000, "（连锁总部）出库单信息不存在");

    // ========== 缺货单相关错误码 ==========
    ErrorCode STOCKOUT_BILL_NOT_EXISTS = new ErrorCode(1_008_000_000, "（连锁总部）缺货单信息不存在");
    ErrorCode STOCKOUT_BILL_DETAIL_NOT_EXISTS = new ErrorCode(1_008_001_000, "（连锁总部）缺货单明细不存在");

    // ========== 其他相关错误码 ==========
    ErrorCode PURCHASE_SUPPLIER_PRODUCT_RECORD_NOT_EXISTS = new ErrorCode(1_010_000_000, "采购记录不存在");
    ErrorCode PURCHASE_ERP_BILL_NOT_EXISTS = new ErrorCode(1_010_000_001, "采购-三方erp单据信息不存在");
    ErrorCode PURCHASE_TRANSPORT_NOT_EXISTS = new ErrorCode(1_010_000_002, "采购-运输信息不存在");
    ErrorCode PURCHASE_INVOICE_NOT_EXISTS = new ErrorCode(1_010_000_003, "采购-发票信息不存在");
    ErrorCode MEDICINE_TYPE_NOT_EXISTS = new ErrorCode(1_010_000_004, "药品类型不存在");
    ErrorCode PURCHASE_BILL_STATUS_NOT_EXISTS = new ErrorCode(1_010_000_005, "采购单状态不存在");
    ErrorCode CLOUD_SERVICE_NOT_EXISTS = new ErrorCode(1_010_000_006, "云端接口不存在");
    ErrorCode PRODUCT_INFO_NOT_EXISTS = new ErrorCode(1_010_000_007, "药品信息不存在");

    /**
     * 格式化错误信息 统一错误信息的格式
     * <p>
     * 格式规则： 1. 包含错误码描述 2. 包含具体异常信息
     *
     * @param errorCode 错误码
     * @param e         异常对象
     * @return 格式化后的错误信息
     */
    static String formatErrorMsg(ErrorCode errorCode, Exception e) {
        if (e == null) {
            return errorCode.getMsg();
        }
        return String.format("%s: %s", errorCode.getMsg(), e.getMessage());
    }

    /**
     * 格式化错误信息 统一错误信息的格式
     * <p>
     * 格式规则： 1. 包含错误码描述 2. 包含具体异常信息
     *
     * @param errorCode 错误码
     * @param e         异常对象
     * @return 格式化后的错误信息
     */
    static ErrorCode formatError(ErrorCode errorCode, Exception e) {
        return new ErrorCode(errorCode.getCode(), formatErrorMsg(errorCode, e));
    }
}
