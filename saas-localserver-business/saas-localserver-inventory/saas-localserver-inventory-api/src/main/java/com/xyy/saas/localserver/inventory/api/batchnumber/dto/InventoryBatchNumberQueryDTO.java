package com.xyy.saas.localserver.inventory.api.batchnumber.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 商品批次库存 DO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryBatchNumberQueryDTO implements Serializable {

    private static final long serialVersionUID = 1512404549420427244L;

    /* 租户Id */
    private Long tenantId;

    /* 单据类型 */
    private Integer billType;

    /* 单据编号 */
    private String billNo;

    /* 助记码（混合查询条件） */
    private String mnemonicCode;

    /* 是否停售: 0--未停售 1--已停售 */
    private Boolean stopSale;

    /* 商品分类(1:普通药品, 2:中药饮片, 3:医疗器械, 4:非药品, 5:赠品) */
    private Integer spuCategory;

    /* 商品编号 */
    private String productPref;

    /* 商品编号列表 */
    private List<String> productPrefList;

    /* 批号 */
    private String lotNo;

    /* 库存类型 0-全部 1-大于零 2-等于0 */
    private Integer stockType;

    /* 货位guid */
    private String positionGuid;

    /* 货位guid列表 */
    private List<String> positionGuidList;

    /* 近效期类型 0-全部 1-近效期 2-已过期 */
    private int nearlyEffectiveType;

    /* 距过期天数 */
    private Integer nearlyEffectiveDay;

    /* 供应商guid */
    private String supplierGuid;

    /* 批次guid */
    private String batchGuid;

    /* 追踪维度pref */
    private String tracePref;
    
    /* 来源追踪维度 */
    private String sourceTracePref;

    /* 总部追踪维度 */
    private String rootTracePref;

    /* 入库开始时间 */
    private Date billTimeStart;

    /* 入库结束时间 */
    private Date billTimeEnd;

}