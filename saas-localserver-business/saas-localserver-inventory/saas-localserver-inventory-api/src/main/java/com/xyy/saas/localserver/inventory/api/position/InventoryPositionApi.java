package com.xyy.saas.localserver.inventory.api.position;

import com.xyy.saas.localserver.inventory.api.position.dto.InventoryPositionDTO;
import com.xyy.saas.localserver.inventory.api.position.dto.InventoryPositionQueryDTO;

import java.util.List;

/**
 * 货位服务Api
 */
public interface InventoryPositionApi {

    /**
     * 获取货位列表
     *
     * @param queryDTO 列表查询
     * @return 货位列表
     */
    List<InventoryPositionDTO> getInventoryPositionList(InventoryPositionQueryDTO queryDTO);

    /**
     * 初始化货位
     *
     * @param tenantId
     */
    void initInventoryPosition(Long tenantId);

}
