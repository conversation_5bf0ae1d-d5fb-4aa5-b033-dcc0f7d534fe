package com.xyy.saas.localserver.inventory.enums;

public enum BillStatusEnum {

    TEMPORARY(1, "暂存"),

    PENDING_VERIFICATION(2, "待检验"),

    PENDING_APPROVAL(3, "审批中"),

    REVOKED(4, "已撤销"),

    REJECTED(5, "已驳回"),

    COMPLETED(6, "已完成"),

    ;

    private Integer status;
    private String statusName;

    public Integer getStatus() {
        return status;
    }

    public String getStatusName() {
        return statusName;
    }

    BillStatusEnum(int status, String statusName) {
        this.status = status;
        this.statusName = statusName;
    }

    public static String getNameByStatus(Integer status) {
        for (BillStatusEnum statusEnum : BillStatusEnum.values()) {
            if (statusEnum.getStatus().equals(status)) {
                return statusEnum.getStatusName();
            }
        }
        return null;
    }

}
