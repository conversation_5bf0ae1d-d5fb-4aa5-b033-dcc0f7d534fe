package com.xyy.saas.localserver.inventory.api.batchnumber;

import com.xyy.saas.localserver.inventory.api.batchnumber.dto.InventoryBatchNumberDTO;
import com.xyy.saas.localserver.inventory.api.batchnumber.dto.InventoryBatchNumberQueryDTO;

import java.util.List;

/**
 * 批次库存查询服务Api
 */
public interface InventoryBatchNumberApi {

    /**
     * 获取批次库存列表
     * @param queryDTO
     * @return
     */
    List<InventoryBatchNumberDTO> getInventory(InventoryBatchNumberQueryDTO queryDTO);

}
