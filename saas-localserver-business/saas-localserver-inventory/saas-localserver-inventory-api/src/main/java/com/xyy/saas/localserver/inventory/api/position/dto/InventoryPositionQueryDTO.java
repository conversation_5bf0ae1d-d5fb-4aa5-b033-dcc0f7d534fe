package com.xyy.saas.localserver.inventory.api.position.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 货位 DO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryPositionQueryDTO implements Serializable {

    private static final long serialVersionUID = -262870675333917190L;

    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 编号
     */
    private String guid;
    /**
     * 货区名称
     */
    private String areaName;
    /**
     * 货区类型：1-合格，2-不合格，3-待验区/待处理，4-退货区/待退货
     */
    private Integer areaType;
    /**
     * 存储条件：1--常温,2--阴凉,3--冷藏,4--其他
     */
    private Integer positionCondition;
    /**
     * 货位名称
     */
    private String positionName;
    /**
     * 启用状态: 0--不禁用 1--禁用
     */
    private Boolean disable;
    /**
     * 是否默认：0--非默认 1--默认
     */
    private Boolean systemDefault;

}