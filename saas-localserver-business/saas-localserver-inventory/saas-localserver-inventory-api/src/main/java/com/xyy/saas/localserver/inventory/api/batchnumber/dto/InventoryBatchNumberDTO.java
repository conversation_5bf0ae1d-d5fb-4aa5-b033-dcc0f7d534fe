package com.xyy.saas.localserver.inventory.api.batchnumber.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品批号库存 DO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryBatchNumberDTO implements Serializable {

    private static final long serialVersionUID = -8436728355672980811L;

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 批次号guid
     */
    private String guid;
    /**
     * 商品编号
     */
    private String productPref;
    /**
     * 批号
     */
    private String lotNo;
    /**
     * 货位guid
     */
    private String positionGuid;
    /**
     * 追踪维度
     */
    private String tracePref;
    /**
     * 来源追踪维度
     */
    private String sourceTracePref;
    /**
     * 总部追踪维度
     */
    private String rootTracePref;
    /**
     * 库存数量
     */
    private BigDecimal stockNumber;
    /**
     * 出库预占
     */
    private BigDecimal campOnNumber;
    /**
     * 单据类型
     */
    private Integer billType;
    /**
     * 单据编号
     */
    private String billNo;
    /**
     * 供应商guid
     */
    private String supplierGuid;
    /**
     * 入库含税价
     */
    private BigDecimal inTaxPrice;
    /**
     * 入库税率
     */
    private BigDecimal taxRate;
    /**
     * 灭菌批号
     */
    private String sterilizationBatchNo;
    /**
     * 来源guid
     */
    private String sourceGuid;
    /**
     * 总部guid
     */
    private String rootGuid;
    /**
     * 入库时间
     */
    private LocalDateTime inTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 版本号
     */
    private Long version;

}