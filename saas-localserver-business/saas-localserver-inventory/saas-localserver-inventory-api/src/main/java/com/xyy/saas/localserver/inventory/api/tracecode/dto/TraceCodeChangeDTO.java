package com.xyy.saas.localserver.inventory.api.tracecode.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TraceCodeChangeDTO implements Serializable {

    private static final long serialVersionUID = -8909131677368165703L;

    /* 变动数量 */
    private BigDecimal changeNumber;

    /* 追溯码/UDI码  */
    private String traceCode;

    /* 追溯码状态 1-已收货；2-已入库；3-已占用；4-已销售；5-已销毁；6-已采退；7-已销退 */
    private Integer status;

    /* 包装规格 1-小包；2-中包；3-大包 */
    private Integer packageLevel;

    /* 追踪维度 */
    private String tracePref;

    /* 入库追踪维度 */
    private String rootTracePref;

}
