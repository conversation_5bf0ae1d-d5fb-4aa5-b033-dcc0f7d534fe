package com.xyy.saas.localserver.inventory.api.lotnumber.dto;

import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品批号库存 DO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryLotNumberDTO implements Serializable {

    private static final long serialVersionUID = -2264707295256687092L;

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 商品编号
     */
    private String productPref;
    /**
     * 批号
     */
    private String lotNo;
    /**
     * 货位guid
     */
    private String positionGuid;
    /**
     * 生产日期
     */
    private LocalDateTime productionDate;
    /**
     * 到期时间
     */
    private LocalDateTime expiryDate;
    /**
     * 库存数量
     */
    private BigDecimal stockNumber;
    /**
     * 预占数量
     */
    private BigDecimal campOnNumber;
    /**
     * 成本均价
     */
    private BigDecimal costPrice;
    /**
     * 库存总金额
     */
    private BigDecimal stockAmount;
    /**
     * 是否停售: 0--未停售 1--已停售
     */
    private Boolean stopSale;
    /**
     * 最后一次采购入库价
     */
    private BigDecimal lastInPrice;
    /**
     * 最后入库时间
     */
    private LocalDateTime lastInTime;
    /**
     * 最后一次供应商
     */
    private String lastSupplierGuid;
    /**
     * 备注
     */
    private String remark;
    /**
     * 版本号
     */
    private Long version;

}