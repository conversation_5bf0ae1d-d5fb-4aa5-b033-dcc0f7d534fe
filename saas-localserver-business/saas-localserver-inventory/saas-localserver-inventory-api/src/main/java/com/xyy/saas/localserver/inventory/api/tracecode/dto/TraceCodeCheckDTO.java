package com.xyy.saas.localserver.inventory.api.tracecode.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TraceCodeCheckDTO implements Serializable {

    private static final long serialVersionUID = -1080532068396009240L;

    /* 商品编码 */
    private String productPref;

    /* 批号 */
    private String lotNo;

    /* 追溯码/UDI码  */
    private String traceCde;

    /* 父码 */
    private String parentCode;

}
