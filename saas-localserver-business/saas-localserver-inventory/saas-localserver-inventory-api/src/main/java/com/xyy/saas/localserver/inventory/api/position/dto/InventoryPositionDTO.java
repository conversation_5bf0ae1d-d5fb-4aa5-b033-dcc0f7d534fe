package com.xyy.saas.localserver.inventory.api.position.dto;

import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 货位 DO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryPositionDTO implements Serializable {

    private static final long serialVersionUID = -2541401435825221242L;

    /**
     * 主键id
     */
    private Long id;
    /**
     * 编号
     */
    private String guid;
    /**
     * 货区名称
     */
    private String areaName;
    /**
     * 货区类型：1-合格，2-不合格，3-待验区/待处理，4-退货区/待退货
     */
    private Integer areaType;
    /**
     * 货位名称
     */
    private String positionName;
    /**
     * 存储条件：1--常温,2--阴凉,3--冷藏,4--其他
     */
    private Integer positionCondition;
    /**
     * 最低温度
     */
    private BigDecimal temperatureMin;
    /**
     * 最高温度
     */
    private BigDecimal temperatureMax;
    /**
     * 最低湿度
     */
    private BigDecimal humidityMin;
    /**
     * 最高湿度
     */
    private BigDecimal humidityMax;
    /**
     * 启用状态: 0--不禁用 1--禁用
     */
    private Boolean disable;
    /**
     * 是否默认：0--非默认 1--默认
     */
    private Boolean systemDefault;
    /**
     * 备注
     */
    private String remark;
    /**
     * 版本号
     */
    private Long version;

}