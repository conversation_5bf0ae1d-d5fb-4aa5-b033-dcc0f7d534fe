package com.xyy.saas.localserver.inventory.api.inventory.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * 商品库存 DO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryQueryDTO implements Serializable {

    private static final long serialVersionUID = 4726185407485635320L;

    /* 租户Id */
    private Long tenantId;

    /* 助记码（混合查询条件） */
    private String mnemonicCode;

    /* 是否停售: 0--未停售 1--已停售 */
    private Boolean stopSale;

    /* 商品分类(1:普通药品, 2:中药饮片, 3:医疗器械, 4:非药品, 5:赠品) */
    private Integer spuCategory;

    /* 商品编号 */
    private String productPref;

    /* 商品编号列表 */
    private List<String> productPrefList;

    /* 库存类型 0-全部 1-大于零 2-等于0 */
    private Integer stockType;

}