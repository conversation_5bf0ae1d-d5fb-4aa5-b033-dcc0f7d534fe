package com.xyy.saas.localserver.inventory.enums;

/**
 * 预占枚举
 */
public enum CampOnEnum {

    CAMP_ON(1, "预占中"),

    RELEASE(2, "已释放");


    private Integer type;
    private String name;

    CampOnEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static CampOnEnum getEnumByType(Integer type) {
        for (CampOnEnum currEnum : CampOnEnum.values()) {
            if (currEnum.getType().equals(type)) {
                return currEnum;
            }
        }
        return null;
    }
}
