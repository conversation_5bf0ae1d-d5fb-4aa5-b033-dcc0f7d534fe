package com.xyy.saas.localserver.inventory.api.inventory.dto;

import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品库存 DO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryDTO implements Serializable {

    private static final long serialVersionUID = 5958540041979398457L;

    /**
     * 主键ID
     */
    private Long id;
    /**
     * 商品编号
     */
    private String productPref;
    /**
     * 库存数量
     */
    private BigDecimal stockNumber;
    /**
     * 成本均价
     */
    private BigDecimal costPrice;
    /**
     * 库存总金额
     */
    private BigDecimal stockAmount;
    /**
     * 最后一次采购入库价
     */
    private BigDecimal lastInPrice;
    /**
     * 最后一次供应商
     */
    private String lastSupplierGuid;
    /**
     * 最后一次入库时间
     */
    private LocalDateTime lastInTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 库存上限
     */
    private BigDecimal storeMaxLimit;
    /**
     * 库存下限
     */
    private BigDecimal storeMinLimit;
    /**
     * 版本号
     */
    private Long version;

}