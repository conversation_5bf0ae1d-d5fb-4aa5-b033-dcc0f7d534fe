package com.xyy.saas.localserver.inventory.enums;

/**
 * 出入库类型
 */
public enum StockTypeEnum {

    STOCK_IN(1, "入库", "inventoryStockInServiceImpl"),

    STOCK_OUT(2, "出库", "inventoryStockOutServiceImpl"),

    STOCK_IN_AND_OUT(3, "一入一出", "inventoryStockInAndOutServiceImpl");

    private Integer type;
    private String name;
    private String bean;

    StockTypeEnum(Integer type, String name, String bean) {
        this.type = type;
        this.name = name;
        this.bean = bean;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public String getBean() {
        return bean;
    }

    public static StockTypeEnum getEnumByType(Integer type) {
        for (StockTypeEnum currEnum : StockTypeEnum.values()) {
            if (currEnum.getType().equals(type)) {
                return currEnum;
            }
        }
        return null;
    }
}
