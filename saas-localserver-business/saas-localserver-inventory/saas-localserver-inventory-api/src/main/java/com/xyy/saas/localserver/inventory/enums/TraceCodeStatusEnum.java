package com.xyy.saas.localserver.inventory.enums;

public enum TraceCodeStatusEnum {

    RECEIVED(1, "已收货"),

    WAREHOUSING(2, "已入库"),

    CAMP_ON(3, "已占用"),

    SALE(4, "已销售"),

    DESTROYED(5, "已销毁"),

    PURCHASE_RETURN(6, "已采退"),

    SALE_RETURN(7, "已销退"),
    ;

    private Integer status;
    private String statusName;

    public Integer getStatus() {
        return status;
    }

    public String getStatusName() {
        return statusName;
    }

    TraceCodeStatusEnum(int status, String statusName) {
        this.status = status;
        this.statusName = statusName;
    }

    public static String getNameByStatus(Integer status) {
        for (TraceCodeStatusEnum statusEnum : TraceCodeStatusEnum.values()) {
            if (statusEnum.getStatus().equals(status)) {
                return statusEnum.getStatusName();
            }
        }
        return null;
    }

}
