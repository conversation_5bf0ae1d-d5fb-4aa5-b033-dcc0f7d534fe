package com.xyy.saas.localserver.inventory.api.lotnumber;

import com.xyy.saas.localserver.inventory.api.lotnumber.dto.InventoryLotNumberDTO;
import com.xyy.saas.localserver.inventory.api.lotnumber.dto.InventoryLotNumberQueryDTO;

import java.util.List;

/**
 * 批号库存查询服务Api
 */
public interface InventoryLotNumberApi {

    /**
     * 获取批号库存列表
     * @param queryDTO
     * @return
     */
    List<InventoryLotNumberDTO> getInventory(InventoryLotNumberQueryDTO queryDTO);

}
