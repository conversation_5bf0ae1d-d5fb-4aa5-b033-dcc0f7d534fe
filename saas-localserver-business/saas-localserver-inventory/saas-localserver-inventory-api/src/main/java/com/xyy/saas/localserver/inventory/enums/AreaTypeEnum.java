package com.xyy.saas.localserver.inventory.enums;

/**
 * 货区类型枚举
 */
public enum AreaTypeEnum {

    QUALIFIED(1, "合格品区"),

    NO_QUALIFIED(2, "不合格品区"),

    ON_HAND(3, "待验区"),

    RETURNED(4, "退货区");

    private Integer type;
    private String name;

    AreaTypeEnum(Integer type, String name) {
        this.type = type;
        this.name = name;
    }

    public Integer getType() {
        return type;
    }

    public String getName() {
        return name;
    }

    public static AreaTypeEnum getEnumByType(Integer type) {
        for (AreaTypeEnum currEnum : AreaTypeEnum.values()) {
            if (currEnum.getType().equals(type)) {
                return currEnum;
            }
        }
        return null;
    }
}
