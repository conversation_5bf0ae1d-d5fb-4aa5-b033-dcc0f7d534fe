package com.xyy.saas.localserver.inventory.api.tracecode.dto;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class TraceCodeDTO extends TraceCodeChangeDTO implements Serializable {

    private static final long serialVersionUID = 3586422809728978008L;

    /* id */
    private Long id;

    /* 单据类型/摘要 */
    private Integer billType;

    /* 单据编号 */
    private String billNo;

    /* 领域单据时间 */
    private LocalDateTime billTime;

    /* 商品编码 */
    private String productPref;

    /* 批号 */
    private String lotNo;

    /* 供应商编码 */
    private String supplierPref;

    /* 上一状态 */
    private Integer previousStatus;

    /* 父码 */
    private String parentCode;

    /* 变动数量 */
    private BigDecimal changeNumber;

    /* 入库数量 */
    private BigDecimal inNumber;

    /* 出库数量 */
    private BigDecimal outNumber;

    /* 剩余数量 */
    private BigDecimal remainNumber;

}
