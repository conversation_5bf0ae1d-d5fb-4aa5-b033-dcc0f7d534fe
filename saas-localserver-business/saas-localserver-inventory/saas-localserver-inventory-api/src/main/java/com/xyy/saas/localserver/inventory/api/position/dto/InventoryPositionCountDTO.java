package com.xyy.saas.localserver.inventory.api.position.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 货位数量 DTO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryPositionCountDTO implements Serializable {

    private static final long serialVersionUID = -2097268413953019621L;

    /**
     * 货区名称
     */
    private String areaName;
    /**
     * 货区数量
     */
    private Integer areaCount;
    /**
     * 货位数量
     */
    private Integer positionCount;

}