package com.xyy.saas.localserver.inventory.enums;

/**
 * @Desc 库存选择策略
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2025/4/23 上午11:36
 */
public enum InventorySelectEnum {

    // 后进先出
    LIFO("lifo", "LIFO"),
    // 先进先出
    FIFO("fifo", "FIFO"),
    // 后进先入
    LIFI("lifi", "LIFI"),
    // 先进先入
    FIFI("fifi", "FIFI"),
    // 指定
    DIRECT("direct", "DIRECT");

    private String code;
    private String desc;

    InventorySelectEnum(String code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public String getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }
}

