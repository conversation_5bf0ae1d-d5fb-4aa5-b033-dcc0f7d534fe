package com.xyy.saas.localserver.inventory.api.position.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;
import java.util.Map;

/**
 * 货位 DO
 *
 * <AUTHOR>
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryPositionTreeDTO implements Serializable {

    private static final long serialVersionUID = 3477088177208698684L;

    private Map<String, List<InventoryPositionTreeDTO>> items;

}