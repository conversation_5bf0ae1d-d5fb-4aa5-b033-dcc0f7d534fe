package com.xyy.saas.localserver.inventory.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

public interface ErrorCodeConstants {

    ErrorCode INVENTORY_POSITION_NOT_EXISTS = new ErrorCode(2_004_001_001, "货位不存在");

    ErrorCode INVENTORY_POSITION_NAME_EXISTS = new ErrorCode(2_004_001_002, "货位名称重复");

    ErrorCode INVENTORY_POSITION_SIZE_ERROR = new ErrorCode(2_004_001_003, "已超出最大限制");

    ErrorCode INVENTORY_NOT_EXISTS = new ErrorCode(2_004_002_001, "商品库存不存在");

    ErrorCode INVENTORY_LOT_NUMBER_NOT_EXISTS = new ErrorCode(2_004_003_001, "商品批号库存不存在");

    ErrorCode INVENTORY_BATCH_NUMBER_NOT_EXISTS = new ErrorCode(2_004_004_001, "商品批次库存不存在");

    ErrorCode INVENTORY_CHANGE_DETAIL_NOT_EXISTS = new ErrorCode(2_004_005_001, "库存变动明细不存在");

    ErrorCode INVENTORY_CAMP_ON_BILL_NOT_EXISTS = new ErrorCode(2_004_006_001, "预占单不存在");

    ErrorCode INVENTORY_CAMP_ON_BILL_DETAIL_NOT_EXISTS = new ErrorCode(2_004_007_001, "预占单详情不存在");

    ErrorCode INVENTORY_BILL_NOT_EXISTS = new ErrorCode(2_004_008_001, "库存单据不存在");

    ErrorCode INVENTORY_BILL_DETAIL_NOT_EXISTS = new ErrorCode(2_004_009_001, "库存变动详情不存在");

    ErrorCode INVENTORY_SELECT_BATCH_NOT_MATCH = new ErrorCode(2_004_010_001, "库存选取批次不匹配");

    ErrorCode INVENTORY_BATCH_NOT_EXISTS = new ErrorCode(2_004_010_002, "批次不存在");

    ErrorCode INVENTORY_BATCH_NOT_ENOUGH = new ErrorCode(2_004_010_003, "库存不足");

    ErrorCode INVENTORY_CAMP_ON_BILL_RELEASED = new ErrorCode(2_004_010_004, "预占单已释放");

    ErrorCode INVENTORY_TRACE_CODE_NOT_EXISTS = new ErrorCode(2_004_011_001, "追溯码不存在");

    ErrorCode INVENTORY_CHANGE_NUMBER_NOT_EXISTS = new ErrorCode(2_004_012_001, "变动数量不能为空");

    ErrorCode INVENTORY_BEFORE_QUANTITY_NOT_EXISTS = new ErrorCode(2_004_012_002, "变动前数量不能为空");

    ErrorCode INVENTORY_ACTUAL_QUANTITY_NOT_EXISTS = new ErrorCode(2_004_012_003, "实际数量不能为空");

    ErrorCode INVENTORY_REGISTER_QUANTITY_NOT_EXISTS = new ErrorCode(2_004_012_003, "登记数量不能为空");

}
