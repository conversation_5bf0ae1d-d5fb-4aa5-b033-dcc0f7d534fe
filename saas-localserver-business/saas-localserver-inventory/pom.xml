<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
	xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>
	<parent>
		<artifactId>saas-localserver-business</artifactId>
		<groupId>com.xyy.saas</groupId>
		<version>${revision}</version>
	</parent>

	<artifactId>saas-localserver-inventory</artifactId>
	<description>saas-药店-库存服务</description>
	<packaging>pom</packaging>

	<modules>
		<module>saas-localserver-inventory-api</module>
		<module>saas-localserver-inventory-server</module>
	</modules>


	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<version>${maven-springboot-plugin.version}</version>
			</plugin>
		</plugins>
	</build>

</project>
