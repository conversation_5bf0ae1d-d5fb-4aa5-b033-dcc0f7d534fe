
#货位表
DROP TABLE IF EXISTS saas_inventory_position;
create table saas_inventory_position
(
    id                             bigint unsigned primary key comment '主键id',
    guid                           varchar(32)    not null        default ''       comment '编号',
    tenant_id                      bigint         not null        default 0        comment '租户编号',
    area_name                      varchar(32)    not null        default ''       comment '货区名称',
    area_type                      tinyint(4)     not null        default '1'      comment '货区类型：1-合格，2-不合格，3-待验区/待处理，4-退货区/待退货',
    position_name                  varchar(32)    not null        default ''       comment '货位名称',
    position_condition             tinyint(4)     not null        default '1'      comment '存储条件：1--常温,2--阴凉,3--冷藏,4--其他',
    temperature_min                decimal(6,3)   null            default null     comment '最低温度',
    temperature_max                decimal(6,3)   null            default null     comment '最高温度',
    humidity_min                   decimal(6,3)   null            default null     comment '最低湿度',
    humidity_max                   decimal(6,3)   null            default null     comment '最高湿度',
    disable                        bit            not null        default b'0'     comment '启用状态: 0--不禁用 1--禁用',
    system_default                 bit            not null        default b'0'     comment '是否默认：0--非默认 1--默认',
    remark                         varchar(200)   null            default ''       comment '备注',
    version                        bigint         not null        default 0        comment '版本号',

    creator                        varchar(64)    not null        default ''                comment '创建者',
    create_time                    datetime       not null        default CURRENT_TIMESTAMP comment '创建时间',
    updater                        varchar(64)    null            default ''                comment '更新者',
    update_time                    datetime       not null        default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                        bit            not null        default b'0'              comment '是否删除',
    unique key uk_tenant_id_area_name_position_name (tenant_id, area_name, position_name)
) comment '货位表';

#追踪维度表
DROP TABLE IF EXISTS saas_inventory_trace;
create table saas_inventory_trace
(
    id                             bigint unsigned primary key comment '主键id',
    pref                           varchar(32)    not null        default ''         comment '编号',
    tenant_id                      bigint         not null        default 0          comment '租户编号',
    source_type                    varchar(20)    not null        default ''         comment '来源类型',
    source_no                      varchar(32)    not null        default ''         comment '来源单号',
    source_trace_pref              varchar(35)    not null        default ''         comment '来源跟踪编号',
    product_pref                   varchar(32)    not null        default ''         comment '商品编号',
    lot_number                     varchar(100)   not null        default ''         comment '批号',
    produced_date                  datetime       not null        default current_timestamp comment '生产日期',
    expiration_date                datetime       not null        default current_timestamp comment '到期时间',
    provide_pref                   varchar(32)    null            default ''         comment '供应商',
    in_tax_rate                    decimal(12,2)  null            default null       comment 'root入库税率',
    in_tax_price                   decimal(12,6)  not null        default '0.000000' comment '入库含税价',
    in_time                        datetime       not null        default current_timestamp comment '入库时间',
    sterilization_batch_no         varchar(128)   not null        default ''         comment '灭菌批号',
    root_source_no                 varchar(32)    null            default null       comment 'root来源单号',
    root_trace_pref                varchar(35)    null            default null       comment 'root来源跟踪编号',
    version                        bigint         not null        default 0          comment '版本号',

    creator                        varchar(64)    not null        default ''                comment '创建者',
    create_time                    datetime       not null        default CURRENT_TIMESTAMP comment '创建时间',
    updater                        varchar(64)    null            default ''                comment '更新者',
    update_time                    datetime       not null        default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                        bit            not null        default b'0'              comment '是否删除',
    unique key uk_pref_tenant_id (pref, tenant_id),
    unique key uk_source_no_tenant_id_product_pref_lot_number (source_no, tenant_id, product_pref, lot_number)
) comment '追踪维度表';

#商品库存表
DROP TABLE IF EXISTS saas_inventory;
create table saas_inventory
(
    id                             bigint     auto_increment primary key comment '主键ID',
    --guid                           varchar(32)    not null        default ''         comment '编号',
    tenant_id                      bigint         not null        default 0          comment '租户编号',
    product_pref                   varchar(32)    not null        default ''         comment '商品编号',
    stock_number                   decimal(12,3)  not null        default '0.000'    comment '库存数量',
    camp_on_number                 decimal(12,3)  null            default '0.000'    comment '预占数量',
    cost_price                     decimal(10,4)  null            default '0.0000'   comment '成本均价',
    stock_amount                   decimal(14,2)  null            default '0.00'     comment '库存总金额',
    last_in_price                  decimal(10,4)  not null        default '0.0000'   comment '最后一次采购入库价',
    last_supplier_guid             varchar(32)    null            default ''         comment '最后一次供应商',
    last_in_time                   datetime       null            default null       comment '最后一次入库时间',
    remark                         varchar(256)   null            default ''         comment '备注',
    store_max_limit                decimal(12,3)  null            default '0.00'     comment '库存上限',
    store_min_limit                decimal(12,3)  null            default '0.00'     comment '库存下限',
    version                        bigint         not null        default 0          comment '版本号',

    creator                        varchar(64)    not null        default ''                comment '创建者',
    create_time                    datetime       not null        default CURRENT_TIMESTAMP comment '创建时间',
    updater                        varchar(64)    null            default ''                comment '更新者',
    update_time                    datetime       not null        default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                        bit            not null        default b'0'              comment '是否删除',
    unique key uk_product_pref_tenant_id (product_pref, tenant_id)
) comment '商品库存表';

#商品批号库存表
DROP TABLE IF EXISTS saas_inventory_lot_number;
create table saas_inventory_lot_number
(
    id                             bigint unsigned primary key comment '主键ID',
    --guid                           varchar(32)    not null        default ''         comment '编号',
    tenant_id                      bigint         not null        default 0          comment '租户编号',
    product_pref                   varchar(32)    not null        default ''         comment '商品编号',
    lot_no                         varchar(30)    not null        default ''         comment '批号',
    position_guid                  varchar(32)    not null        default ''         comment '货位guid',
    production_date                datetime       null            default null       comment '生产日期',
    expiry_date                    datetime       null            default null       comment '到期时间',
    stock_number                   decimal(12,3)  not null        default '0.000'    comment '库存数量',
    camp_on_number                 decimal(12,3)  not null        default '0.000'    comment '预占数量',
    cost_price                     decimal(10,4)  not null        default '0.0000'   comment '成本均价',
    stock_amount                   decimal(14,2)  not null        default '0.00'     comment '库存总金额',
    stop_sale                      bit            not null        default b'0'       comment '是否停售: 0--未停售 1--已停售',
    last_in_price                  decimal(10,4)  not null        default '0.0000'   comment '最后一次采购入库价',
    last_in_time                   datetime       null            default null       comment '最后入库时间',
    last_supplier_guid             varchar(32)    null            default ''         comment '最后一次供应商',
    remark                         varchar(256)   null            default ''         comment '备注',
    version                        bigint         not null        default 0          comment '版本号',

    creator                        varchar(64)    not null        default ''                comment '创建者',
    create_time                    datetime       not null        default CURRENT_TIMESTAMP comment '创建时间',
    updater                        varchar(64)    null            default ''                comment '更新者',
    update_time                    datetime       not null        default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                        bit            not null        default b'0'              comment '是否删除',
    unique key uk_tenant_product_lotNum_position (tenant_id, product_pref, lot_no, position_guid)
) comment '商品批号库存表';

#商品批次库存表
DROP TABLE IF EXISTS saas_inventory_batch_number;
create table saas_inventory_batch_number
(
    id                             bigint unsigned primary key comment '主键ID',
    guid                           varchar(32)    not null        default ''       comment '批次号guid',
    tenant_id                      bigint         not null        default 0        comment '租户编号',
    product_pref                   varchar(32)    not null        default ''       comment '商品编号',
    lot_no                         varchar(30)    not null        default ''       comment '批号',
    position_guid                  varchar(32)    not null        default ''       comment '货位guid',
    trace_pref                     varchar(32)    not null        default ''       comment '追踪维度',
    stock_number                   decimal(12,3)  not null        default '0.000'  comment '库存数量',
    camp_on_number                 decimal(12,3)  not null        default '0.000'  comment '预占数量',

    bill_type                      tinyint(4)     not null        default ''       comment '单据类型',
    bill_no                        varchar(32)    not null        default ''       comment '单据编号',
    tax_price                      decimal(10,4)  not null        default '0.0000' comment '入库含税价',
    tax_rate                       decimal(4,2)   not null        default '0.00'   comment '入库税率',
    source_trace_pref              varchar(32)    not null        default ''       comment '来源追踪维度',
    root_trace_pref                varchar(32)    not null        default ''       comment '总部追踪维度',

    remark                         varchar(256)   null            default ''       comment '备注',
    version                        bigint         not null        default 0        comment '版本号',

    creator                        varchar(64)    not null        default ''                comment '创建者',
    create_time                    datetime       not null        default CURRENT_TIMESTAMP comment '创建时间',
    updater                        varchar(64)    null            default ''                comment '更新者',
    update_time                    datetime       not null        default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                        bit            not null        default b'0'              comment '是否删除',
    unique key uk_tenant_product_lotNum_position_bill_type_bill_no (tenant_id, product_pref, lot_no, position_guid, bill_type, bill_no)
) comment '商品批次库存表';

#库存变动明细表
DROP TABLE IF EXISTS saas_inventory_change_detail;
create table saas_inventory_change_detail
(
    id                             bigint unsigned primary key comment '主键id',
    bill_type                      tinyint(4)     null            default 1          comment '单据类型/摘要',
    bill_no                        varchar(32)    not null        default ''         comment '单据编号',
    bill_time                      datetime       not null        default current_timestamp comment '领域单据时间',
    tenant_id                      bigint         not null        default 0          comment '租户编号',
    product_pref                   varchar(32)    not null        default ''         comment '商品编号',
    lot_no                         varchar(30)    not null        default ''         comment '批号',
    position_guid                  varchar(32)    not null        default ''         comment '货位guid',
    batch_guid                     varchar(32)    not null        default ''         comment '批次库存guid',
    trace_pref                     varchar(32)    not null        default ''         comment '跟踪维度',
    supplier_guid                  varchar(32)    null            default ''         comment '供应商guid',
    in_number                      decimal(12,3)  null            default '0.000'    comment '入库数量',
    in_price                       decimal(10,4)  null            default '0.0000'   comment '入库单价',
    in_amount                      decimal(14,2)  null            default '0.00'     comment '入库总金额',
    out_number                     decimal(12,3)  null            default '0.000'    comment '出库数量',
    out_price                      decimal(10,4)  null            default '0.0000'   comment '出库单价',
    out_amount                     decimal(14,2)  null            default '0.00'     comment '出库总金额',
    batch_number                   decimal(12,3)  not null        default '0.000'    comment '批次库存结存数量',
    batch_amount                   decimal(14,2)  not null        default '0.00'     comment '批次库存结存金额',
    lot_number                     decimal(12,3)  not null        default '0.000'    comment '批号库存结存数量',
    lot_amount                     decimal(14,2)  not null        default '0.00'     comment '批号库存结存金额',
    stock_number                   decimal(12,3)  not null        default '0.000'    comment '商品结存库存数量',
    stock_amount                   decimal(14,2)  not null        default '0.00'     comment '商品结存库存金额',
    trace_codes                    text           null            default ''         comment '追溯码变动明细',
    source_trace_pref              varchar(32)    not null        default ''         comment '来源追踪维度',
    root_trace_pref                varchar(32)    not null        default ''         comment '总部追踪维度',

    cost_price                     decimal(10,4)  not null        default '0.0000'   comment '商品成本价',
    tax_rate                       decimal(4,2)   null            default '0.00'     comment '税率',
    remark                         varchar(256)   null            default ''         comment '备注',
    in_tax_price                   decimal(10,4)  not null        default '0.0000'   comment '入库含税价',
    sterilization_batch_no         varchar(32)    null            default ''         comment '灭菌批号',
    root_id                        bigint         null                               comment '总部id',
    in_time                        datetime       null                               comment '入库时间',
    ext                            text           null                               comment '扩展信息',

    creator                        varchar(64)    not null        default ''                comment '创建者',
    create_time                    datetime       not null        default CURRENT_TIMESTAMP comment '创建时间',
    updater                        varchar(64)    null            default ''                comment '更新者',
    update_time                    datetime       not null        default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                        bit            not null        default b'0'              comment '是否删除'
) comment '库存变动明细表';

# 追溯码表
DROP TABLE IF EXISTS saas_inventory_trace_code;
create table saas_inventory_trace_code
(
    id                             bigint unsigned primary key comment '主键id',
    tenant_id                      bigint         not null        default 0                 comment '租户编号',
    product_pref                   varchar(32)    not null        default ''                comment '商品编码',
    lot_no                         varchar(32)    not null        default ''                comment '批号',
    trace_pref                     varchar(32)    not null        default ''                comment '追踪维度',
    supplier_pref                  varchar(32)    null            default ''                comment '供应商编码',
    trace_code                     varchar(128)   not null        default ''                comment '追溯码/UDI码',
    in_number                      decimal(12,3)  null            default '0.000'           comment '入库数量',
    out_number                     decimal(12,3)  null            default '0.000'           comment '出库数量',
    remain_number                  decimal(12,3)  null            default '0.000'           comment '剩余数量',
    status                         tinyint(4)     null                                      comment '追溯码状态 1-已收货；2-已入库；3-已占用；4-已销售；5-已销毁；6-已采退；7-已销退',
    package_level                  tinyint(4)     not null                                  comment '包装规格 1-小包；2-中包；3-大包',
    parent_code                    varchar(128)   null            default ''                comment '父码',
    version                        bigint         not null        default 0                 comment '版本号',

    creator                        varchar(64)    not null        default ''                comment '创建者',
    create_time                    datetime       not null        default CURRENT_TIMESTAMP comment '创建时间',
    updater                        varchar(64)    null            default ''                comment '更新者',
    update_time                    datetime       not null        default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                        bit            not null        default b'0'              comment '是否删除',
    unique key uk_trace_code_tenant_id (trace_code, tenant_id)
) comment '追溯码表';

# 追溯码帐页表
DROP TABLE IF EXISTS saas_inventory_trace_code_stock_detail;
create table saas_inventory_trace_code_stock_detail
(
    id                             bigint unsigned primary key comment '主键id',
    tenant_id                      bigint         not null        default 0                 comment '租户编号',
    bill_type                      tinyint(4)     null            default 1                 comment '单据类型/摘要',
    bill_no                        varchar(32)    not null        default ''                comment '单据编号',
    bill_time                      datetime       not null        default current_timestamp comment '领域单据时间',
    product_pref                   varchar(32)    not null        default ''                comment '商品编码',
    lot_no                         varchar(32)    not null        default ''                comment '批号',
    trace_pref                     varchar(32)    not null        default ''                comment '追踪维度',
    supplier_pref                  varchar(32)    null            default ''                comment '供应商编码',
    trace_code                     varchar(128)   not null        default ''                comment '追溯码/UDI码',
    in_number                      decimal(12,3)  null            default '0.000'           comment '入库数量',
    out_number                     decimal(12,3)  null            default '0.000'           comment '出库数量',
    remain_number                  decimal(12,3)  null            default '0.000'           comment '剩余数量',
    status                         tinyint(4)     null                                      comment '追溯码状态 1-已收货；2-已入库；3-已占用；4-已销售；5-已销毁；6-已采退；7-已销退',
    package_level                  tinyint(4)     not null                                  comment '包装规格 1-小包；2-中包；3-大包',
    parent_code                    varchar(128)   null            default ''                comment '父码',
    version                        bigint         not null        default 0                 comment '版本号',

    creator                        varchar(64)    not null        default ''                comment '创建者',
    create_time                    datetime       not null        default CURRENT_TIMESTAMP comment '创建时间',
    updater                        varchar(64)    null            default ''                comment '更新者',
    update_time                    datetime       not null        default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                        bit            not null        default b'0'              comment '是否删除',
    unique key uk_trace_code_tenant_id (trace_code, tenant_id)
) comment '追溯码帐页表';

# 预占单表
DROP TABLE IF EXISTS saas_inventory_camp_on_bill;
create table saas_inventory_camp_on_bill
(
    id                             bigint unsigned primary key comment '主键id',
    tenant_id                      bigint         not null        default 0        comment '租户编号',
    bill_no                        varchar(32)    not null        default ''       comment '编号',
    source_type                    tinyint(4)     not null        default ''       comment '来源类型',
    source_no                      varchar(32)    not null        default ''       comment '来源单号',
    parent_no                      varchar(64)    not null        default ''       comment '上级预占单编号',
    status                         tinyint(4)     not null        default '1'      comment '状态: 1:预占中; 2:预占释放',
    remark                         varchar(256)   not null        default ''       comment '备注',
    version                        bigint         not null        default 0        comment '版本号',

    creator                        varchar(64)    not null        default ''                comment '创建者',
    create_time                    datetime       not null        default CURRENT_TIMESTAMP comment '创建时间',
    updater                        varchar(64)    null            default ''                comment '更新者',
    update_time                    datetime       not null        default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                        bit            not null        default b'0'              comment '是否删除',
    unique key uk_bill_no_tenant_id (bill_no, tenant_id)
) comment '预占单表';

# 预占单详情表
DROP TABLE IF EXISTS saas_inventory_camp_on_bill_detail;
create table saas_inventory_camp_on_bill_detail
(
    id                             bigint unsigned primary key comment '主键id',
    tenant_id                      bigint         not null        default 0        comment '租户编号',
    bill_no                        varchar(32)    not null        default ''       comment '预占单编号',
    product_pref                   varchar(32)    not null        default ''       comment '商品编号',
    lot_no                         varchar(30)    not null        default ''       comment '批号',
    position_guid                  varchar(32)    not null        default ''       comment '货位guid',
    batch_guid                     varchar(32)    not null        default ''       comment '批次库存guid',
    trace_pref                     varchar(32)    not null        default ''       comment '追踪维度',
    camp_on_number                 decimal(12,3)  not null        default '0.000'  comment '预占数量',
    release_number                 decimal(12,3)  not null        default '0.000'  comment '释放数量',
    total_camp_on_number           decimal(12,3)  not null        default '0.000'  comment '总预占数量',
    remark                         varchar(256)   null            default ''       comment '备注',
    ext                            text           null                             comment '扩展信息',

    creator                        varchar(64)    not null        default ''                comment '创建者',
    create_time                    datetime       not null        default CURRENT_TIMESTAMP comment '创建时间',
    updater                        varchar(64)    null            default ''                comment '更新者',
    update_time                    datetime       not null        default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                        bit            not null        default b'0'              comment '是否删除'
) comment '预占单详情表';

# 库存单据表
DROP TABLE IF EXISTS saas_inventory_bill;
create table saas_inventory_bill
(
    id                             bigint unsigned primary key comment '主键id',
    tenant_id                      bigint         not null        default 0        comment '租户编号',
    bill_type                      tinyint(4)     not null        default 1        comment '单据类型：101-货位移动 102-盘点 103-报损报溢 104-库存拆零 105-质量复查',
    bill_no                        varchar(32)    not null        default ''       comment '单据编号',
    source_type                    tinyint(4)     not null        default '0'      comment '来源类型',
    source_no                      varchar(32)    not null        default ''       comment '来源编号',
    source_description             varchar(200)   not null        default ''       comment '来源描述',
    status                         tinyint(4)     not null        default '1'      comment '审批状态: 1-暂存 2-待检验 3-审批中 5-已驳回 6-已完成',
    operator                       varchar(32)    not null        default ''       comment '操作员',
    operate_time                   datetime       null                             comment '操作时间',
    remark                         varchar(256)   null            default ''       comment '备注',
    ext                            text           null                             comment '扩展信息',
    version                        bigint         not null        default 0        comment '版本号',

    creator                        varchar(64)    not null        default ''                comment '创建者',
    create_time                    datetime       not null        default CURRENT_TIMESTAMP comment '创建时间',
    updater                        varchar(64)    null            default ''                comment '更新者',
    update_time                    datetime       not null        default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                        bit            not null        default b'0'              comment '是否删除',
    unique key uk_bill_no_tenant_id (bill_no, tenant_id)
) comment '库存单据表';

# 库存单据详情表
DROP TABLE IF EXISTS saas_inventory_bill_detail;
create table saas_inventory_bill_detail
(
    id                             bigint unsigned primary key comment '主键id',
    tenant_id                      bigint         not null        default 0        comment '租户编号',
    bill_no                        varchar(32)    not null        default ''       comment '单据编号',
    product_pref                   varchar(32)    not null        default ''       comment '商品编号',
    lot_no                         varchar(32)    not null        default ''       comment '批号',
    position_guid                  varchar(32)    not null        default ''       comment '货位guid',
    register_quantity              decimal(12,3)  null                             comment '登记数量',
    change_quantity                decimal(12,3)  null                             comment '变动数量',
    before_quantity                decimal(12,3)  not null        default '0.000'  comment '变动前数量',
    actual_quantity                decimal(12,3)  null                             comment '实际数量',
    cost_price                     decimal(10,4)  null            default '0.0000' comment '成本价',
    retail_price                   decimal(10,2)  null            default '0.00'   comment '零售价',
    stock_amount                   decimal(14,2)  not null        default '0.00'   comment '库存金额',
    remark                         varchar(256)   not null        default ''       comment '备注',
    ext                            text           null                             comment '扩展信息',

    creator                        varchar(64)    not null        default ''                comment '创建者',
    create_time                    datetime       not null        default CURRENT_TIMESTAMP comment '创建时间',
    updater                        varchar(64)    null            default ''                comment '更新者',
    update_time                    datetime       not null        default CURRENT_TIMESTAMP on update CURRENT_TIMESTAMP comment '更新时间',
    deleted                        bit            not null        default b'0'              comment '是否删除'
) comment '库存单据详情表';
