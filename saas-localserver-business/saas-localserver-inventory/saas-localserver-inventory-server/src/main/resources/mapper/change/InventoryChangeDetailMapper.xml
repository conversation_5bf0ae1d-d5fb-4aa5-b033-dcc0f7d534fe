<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.localserver.inventory.server.dal.mysql.change.InventoryChangeDetailMapper">

    <!--
        一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
        无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
        代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
        文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
     -->

    <insert id="insertBatch">
        INSERT INTO saas_inventory_change_detail (
            bill_type, bill_no, bill_time, product_pref, lot_no, position_guid, batch_guid, supplier_guid, in_number, in_price, in_amount,
            out_number, out_price, out_amount, batch_number, batch_amount, lot_number, lot_amount, stock_number, stock_amount, cost_price,
            tax_rate, remark, in_tax_price, sterilization_batch_no, root_id, in_time, ext
        ) VALUES
        <foreach collection="items" item="item" separator=",">
            (
            #{item.billType,jdbcType=INTEGER}, #{item.billNo,jdbcType=VARCHAR}, #{item.billTime,jdbcType=TIMESTAMP}, #{item.productPref,jdbcType=VARCHAR},
            #{item.lotNo,jdbcType=VARCHAR}, #{item.positionGuid,jdbcType=VARCHAR}, #{item.batchGuid,jdbcType=VARCHAR}, #{item.supplierGuid,jdbcType=VARCHAR},
            #{item.inNumber,jdbcType=DECIMAL}, #{item.inPrice,jdbcType=DECIMAL}, #{item.inAmount,jdbcType=DECIMAL}, #{item.outNumber,jdbcType=DECIMAL},
            #{item.outPrice,jdbcType=DECIMAL}, #{item.outAmount,jdbcType=DECIMAL}, #{item.batchNumber,jdbcType=DECIMAL}, #{item.batchAmount,jdbcType=DECIMAL},
            #{item.lotNumber,jdbcType=DECIMAL}, #{item.lotAmount,jdbcType=DECIMAL}, #{item.stockNumber,jdbcType=DECIMAL}, #{item.stockAmount,jdbcType=DECIMAL},
            #{item.costPrice,jdbcType=DECIMAL}, #{item.taxRate,jdbcType=DECIMAL}, #{item.remark,jdbcType=VARCHAR}, #{item.inTaxPrice,jdbcType=DECIMAL},
            #{item.sterilizationBatchNo,jdbcType=VARCHAR}, #{item.rootId,jdbcType=BIGINT}, #{item.inTime,jdbcType=TIMESTAMP}, #{item.ext,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

</mapper>