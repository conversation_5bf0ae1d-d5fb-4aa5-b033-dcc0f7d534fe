package com.xyy.saas.localserver.inventory.server.service.campon.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.xyy.saas.localserver.inventory.api.campon.dto.*;
import com.xyy.saas.localserver.inventory.api.change.dto.InventoryChangeDetailDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySelectBatchItemDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventoryStockDTO;
import com.xyy.saas.localserver.inventory.enums.CampOnEnum;
import com.xyy.saas.localserver.inventory.enums.TraceCodeStatusEnum;
import com.xyy.saas.localserver.inventory.server.convert.campon.CampOnConvert;
import com.xyy.saas.localserver.inventory.server.convert.stock.StockConvert;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.campon.InventoryCampOnBillDO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.campon.InventoryCampOnBillDetailDO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.change.InventoryChangeDetailDO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.inventory.InventoryDO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.lotnumber.InventoryLotNumberDO;
import com.xyy.saas.localserver.inventory.server.dal.mysql.batchnumber.InventoryBatchNumberMapper;
import com.xyy.saas.localserver.inventory.server.dal.mysql.campon.InventoryCampOnBillDetailMapper;
import com.xyy.saas.localserver.inventory.server.dal.mysql.campon.InventoryCampOnBillMapper;
import com.xyy.saas.localserver.inventory.server.dal.mysql.change.InventoryChangeDetailMapper;
import com.xyy.saas.localserver.inventory.server.dal.mysql.inventory.InventoryMapper;
import com.xyy.saas.localserver.inventory.server.dal.mysql.lotnumber.InventoryLotNumberMapper;
import com.xyy.saas.localserver.inventory.server.service.campon.InventoryCampOnService;
import com.xyy.saas.localserver.inventory.server.service.stock.InventorySelectService;
import com.xyy.saas.localserver.inventory.server.service.tracecode.InventoryTraceCodeService;
import com.xyy.saas.localserver.inventory.server.support.InventoryBaseSupport;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.inventory.enums.ErrorCodeConstants.INVENTORY_CAMP_ON_BILL_RELEASED;

/**
 * 库存预占
 */
@Service
public class InventoryCampOnServiceImpl implements InventoryCampOnService {

    @Resource
    private InventorySelectService inventorySelectService;
    @Resource
    private InventoryBaseSupport inventoryBaseSupport;
    @Resource
    private InventoryTraceCodeService inventoryTraceCodeService;
    @Resource
    private InventoryBatchNumberMapper batchNumberMapper;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private InventoryLotNumberMapper lotNumberMapper;
    @Resource
    private InventoryCampOnBillMapper inventoryCampOnBillMapper;
    @Resource
    private InventoryCampOnBillDetailMapper inventoryCampOnBillDetailMapper;
    @Resource
    private InventoryChangeDetailMapper changeDetailMapper;

    /**
     * 对指定库存进行预占操作（即锁定库存）。
     *
     * @param inventoryCampOn 预占请求参数，包含商品信息、数量、批号等
     * @return 返回预占结果信息，如预占单据 ID、实际预占数量等
     */
    @Override
    public InventoryCampOnResultDTO campOn(InventoryCampOnDTO inventoryCampOn) {
        // 选取批次
        List<InventorySelectBatchItemDTO> batchList = inventoryCampOn.getSelectMap().entrySet().stream()
                .map(entry -> inventorySelectService.select(StockConvert.INSTANCE.convert(StockConvert.INSTANCE.convert(inventoryCampOn, entry.getKey(), entry.getValue()))))
                .flatMap(List::stream)
                .collect(Collectors.toList());
        InventoryStockDTO inventoryStock = StockConvert.INSTANCE.convert(inventoryCampOn); // 根据需要构造最终对象
        inventoryStock.setBatchList(batchList);

        Tuple2<Map<String, InventoryLotNumberDO>, Map<String, InventoryDO>> tuple = getInventoryInfo(inventoryStock, false, BigDecimal.ONE.negate());
        // 执行预占
        return handleCampOn(inventoryCampOn, inventoryStock, tuple);
    }

    /**
     * 对指定库存进行批量预占操作（即锁定库存）。
     *
     * @param inventoryCampOnList 预占请求参数，包含商品信息、数量、批号等
     * @return 返回预占结果信息，如预占单据 ID、实际预占数量等
     */
    @Override
    public List<InventoryCampOnResultDTO> batchCampOn(List<InventoryCampOnDTO> inventoryCampOnList) {
        return inventoryCampOnList.stream().map(inventoryCampOn -> campOn(inventoryCampOn)).collect(Collectors.toList());
    }

    /**
     * 释放已预占的库存（仅释放，不触发出库）。
     *
     * @param inventorySimpleRelease 释放预占请求参数
     * @return 返回释放结果信息
     */
    @Override
    public InventoryCampOnResultDTO releaseCampOn(InventorySimpleReleaseDTO inventorySimpleRelease) {
        // 选取批次
        Tuple2<InventoryCampOnBillDO, List<InventoryCampOnBillDetailDO>> billTuple = getCampOnBillAndDetails(inventorySimpleRelease);
        InventoryStockDTO inventoryStock = StockConvert.INSTANCE.convert(billTuple.getT1(), billTuple.getT2());
        inventoryStock.setBatchList(inventorySelectService.select(StockConvert.INSTANCE.convert(inventoryStock)));
        Tuple2<Map<String, InventoryLotNumberDO>, Map<String, InventoryDO>> tuple = getInventoryInfo(inventoryStock, false, BigDecimal.ONE);
        // 执行释放
        return handleReleaseCampOn(inventoryStock, billTuple, tuple);
    }

    /**
     * 根据释放请求参数，释放预占库存并完成出库操作。
     *
     * <p>适用于需要额外释放参数（如目标商品、指定数量等）的场景。</p>
     *
     * @param inventorySimpleRelease 出库释放请求参数
     * @return 返回库存变动明细列表，记录此次出库账页信息
     */
    @Override
    public List<InventoryChangeDetailDTO> releaseStock(InventorySimpleReleaseDTO inventorySimpleRelease) {
        Tuple2<InventoryCampOnBillDO, List<InventoryCampOnBillDetailDO>> tuple = getCampOnBillAndDetails(inventorySimpleRelease);
        return handleReleaseStock(tuple.getT1(), tuple.getT2(), CampOnConvert.INSTANCE.convertReleaseDTO(tuple.getT1(), tuple.getT2()));
    }

    /**
     * 根据释放请求参数，释放预占库存并完成出库操作。
     *
     * <p>适用于需要额外释放参数（如目标商品、指定数量等）的场景。</p>
     *
     * @param inventoryRelease 出库释放请求参数
     * @return 返回库存变动明细列表，记录此次出库账页信息
     */
    @Override
    public List<InventoryChangeDetailDTO> releaseStock(InventoryReleaseDTO inventoryRelease) {
        Tuple2<InventoryCampOnBillDO, List<InventoryCampOnBillDetailDO>> tuple = getCampOnBillAndDetails(CampOnConvert.INSTANCE.convert(inventoryRelease));
        return handleReleaseStock(tuple.getT1(), tuple.getT2(), inventoryRelease);
    }

    @Override
    public InventoryCampOnResultDTO queryCampOnBill(InventoryCampOnQueryDTO inventoryCampOnQuery) {
        InventoryCampOnBillDO campOnBillDO = inventoryCampOnBillMapper.selectOne(CampOnConvert.INSTANCE.convert(inventoryCampOnQuery));
        if(campOnBillDO != null) {
            List<InventoryCampOnBillDetailDO> billDetailDOList = inventoryCampOnBillDetailMapper.selectListByBillNo(campOnBillDO.getBillNo());
            return CampOnConvert.INSTANCE.convertResultDTO(campOnBillDO, billDetailDOList);
        }
        return InventoryCampOnResultDTO.builder().inventoryCampOnBill(CampOnConvert.INSTANCE.convert(campOnBillDO)).build();
    }

    /**
     * 执行库存预占的具体逻辑。
     *
     * @param inventoryCampOn 预占请求参数
     * @param inventoryStock  库存信息
     * @param tuple           批号和库存对象映射
     * @return 返回预占结果 DTO
     */
    @Transactional
    public InventoryCampOnResultDTO handleCampOn(InventoryCampOnDTO inventoryCampOn, InventoryStockDTO inventoryStock,
                                               Tuple2<Map<String, InventoryLotNumberDO>, Map<String, InventoryDO>> tuple) {
        // 批量更新库存
        updateInventoryNumbers(inventoryStock, tuple, true);

        // 生成预占单
        InventoryCampOnBillDO inventoryCampOnBill = CampOnConvert.INSTANCE.convertDO(inventoryCampOn);
        inventoryCampOnBillMapper.insert(inventoryCampOnBill);
        List<InventoryCampOnBillDetailDO> inventoryCampOnBillDetailDOList = CampOnConvert.INSTANCE.convertDetailDO(inventoryCampOnBill, inventoryStock.getBatchList());
        inventoryCampOnBillDetailMapper.insertBatch(inventoryCampOnBillDetailDOList);
        return CampOnConvert.INSTANCE.convertResultDTO(inventoryCampOnBill, inventoryCampOnBillDetailDOList);
    }

    /**
     * 执行释放预占库存的具体逻辑。
     *
     * @param inventoryStock 库存信息
     * @param billTuple      预占单据及明细
     * @param tuple          批号和库存对象映射
     * @return 返回释放结果 DTO
     */
    @Transactional
    public InventoryCampOnResultDTO handleReleaseCampOn(InventoryStockDTO inventoryStock, Tuple2<InventoryCampOnBillDO,
            List<InventoryCampOnBillDetailDO>> billTuple, Tuple2<Map<String, InventoryLotNumberDO>, Map<String, InventoryDO>> tuple) {
        // 批量更新库存
        updateInventoryNumbers(inventoryStock, tuple, false);

        // 更新预占状态
        InventoryCampOnBillDO campOnBillDO = billTuple.getT1();
        campOnBillDO.setStatus(CampOnEnum.RELEASE.getType());
        inventoryCampOnBillMapper.updateStatus(campOnBillDO);
        return CampOnConvert.INSTANCE.convertResultDTO(campOnBillDO, billTuple.getT2());
    }

    /**
     * 执行释放预占并出库的具体逻辑。
     *
     * @param campOnBillDO        预占单据
     * @param billDetailDOList    预占明细列表
     * @param inventoryRelease    出库请求参数
     * @return 返回库存变动明细 DTO 列表
     */
    @Transactional
    public List<InventoryChangeDetailDTO> handleReleaseStock(InventoryCampOnBillDO campOnBillDO, List<InventoryCampOnBillDetailDO> billDetailDOList, InventoryReleaseDTO inventoryRelease) {
        // 选取批次
        InventoryStockDTO inventoryStock = StockConvert.INSTANCE.convert(inventoryRelease);
        inventoryStock.setBatchList(inventorySelectService.select(StockConvert.INSTANCE.convert(inventoryStock)));
        Tuple2<Map<String, InventoryLotNumberDO>, Map<String, InventoryDO>> tuple = getInventoryInfo(inventoryStock, true, BigDecimal.ONE.negate());

        // 批量更新库存
        updateInventoryNumbers(inventoryStock, tuple, false);

        // 更新预占状态
        campOnBillDO.setStatus(CampOnEnum.RELEASE.getType());
        inventoryCampOnBillMapper.updateStatus(campOnBillDO);

        // 生成货品账页
        List<InventoryChangeDetailDTO> changeDetailDTOList = inventoryBaseSupport.generateChangeDetailList(inventoryStock, tuple.getT1(), tuple.getT2(), false);
        changeDetailMapper.insertBatch(changeDetailDTOList);

        // 更新追溯码状态
        List<InventoryChangeDetailDTO> changeTraceCodeDetailDTOList = changeDetailDTOList.stream().filter(changeDetailDTO -> CollectionUtil.isNotEmpty(changeDetailDTO.getTraceCodes())).collect(Collectors.toList());
        inventoryTraceCodeService.updateTraceCodeStatus(changeTraceCodeDetailDTOList, TraceCodeStatusEnum.CAMP_ON);

        return changeDetailDTOList;
    }

    /**
     * 获取库存信息（批号 + 库存），并更新预占数量。
     *
     * @param inventoryStock   库存信息
     * @param updateStockNumber 是否更新库存总数
     * @param alpha            增量系数（+1 或 -1）
     * @return 返回批号和库存对象映射
     */
    public Tuple2<Map<String, InventoryLotNumberDO>, Map<String, InventoryDO>> getInventoryInfo(InventoryStockDTO inventoryStock, boolean updateStockNumber, BigDecimal alpha) {
        Tuple2<Map<String, InventoryLotNumberDO>, Map<String, InventoryDO>> inventoryTuple = inventoryBaseSupport.getInventoryInfoByBatch(inventoryStock.getBatchList());
        Tuple2<Map<String, BigDecimal>, Map<String, BigDecimal>> numberTuple = inventoryBaseSupport.getNumberInfoByBatch(inventoryStock.getBatchList());
        // 匹配和更新库存
        inventoryBaseSupport.updateCampOnNumber(numberTuple, inventoryTuple, updateStockNumber, alpha);
        return inventoryTuple;
    }

    /**
     * 批量更新批次号、批号、库存的预占数量。
     *
     * @param inventoryStock 库存信息
     * @param tuple          批号和库存对象映射
     * @param isCampOn       是否为预占操作（true: 增加预占；false: 释放预占）
     */
    @Transactional
    public void updateInventoryNumbers(InventoryStockDTO inventoryStock, Tuple2<Map<String, InventoryLotNumberDO>, Map<String, InventoryDO>> tuple, boolean isCampOn) {
        if(isCampOn) {
            batchNumberMapper.increaseCampOnNumber(inventoryStock.getBatchList());
        } else {
            batchNumberMapper.releaseCampOnNumber(inventoryStock.getBatchList());
        }
        lotNumberMapper.updateCampOnNumber(new ArrayList<>(tuple.getT1().values()));
        inventoryMapper.updateCampOnNumber(new ArrayList<>(tuple.getT2().values()));
    }

    /**
     * 根据单据 ID 获取预占单据，并校验状态是否为“已释放”。
     *
     * @param campOnBillId 单据 ID
     * @return 返回有效的预占单据 DO
     */
    private InventoryCampOnBillDO getInventoryCampOnBillDO(Long campOnBillId) {
        return Optional.ofNullable(inventoryCampOnBillMapper.selectById(campOnBillId))
                .filter(bill -> !CampOnEnum.RELEASE.getType().equals(bill.getStatus()))
                .orElseThrow(() -> exception(INVENTORY_CAMP_ON_BILL_RELEASED));
    }

    /**
     * 根据单据编号获取预占单据，并校验状态是否为“已释放”。
     *
     * @param simpleReleaseDTO 简单释放DTO
     * @return 返回有效的预占单据 DO
     */
    private InventoryCampOnBillDO getInventoryCampOnBillDO(InventorySimpleReleaseDTO simpleReleaseDTO) {
        return Optional.ofNullable(inventoryCampOnBillMapper.selectOne(simpleReleaseDTO))
                .filter(bill -> !CampOnEnum.RELEASE.getType().equals(bill.getStatus()))
                .orElseThrow(() -> exception(INVENTORY_CAMP_ON_BILL_RELEASED));
    }

    /**
     * 获取预占单据及其明细。
     *
     * @param simpleReleaseDTO 简单释放DTO
     * @return 返回预占单据及明细的元组
     */
    private Tuple2<InventoryCampOnBillDO, List<InventoryCampOnBillDetailDO>> getCampOnBillAndDetails(InventorySimpleReleaseDTO simpleReleaseDTO) {
        InventoryCampOnBillDO campOnBillDO = getInventoryCampOnBillDO(simpleReleaseDTO);
        List<InventoryCampOnBillDetailDO> billDetailDOList = inventoryCampOnBillDetailMapper.selectListByBillNo(campOnBillDO.getBillNo());
        return Tuples.of(campOnBillDO, billDetailDOList);
    }

}
