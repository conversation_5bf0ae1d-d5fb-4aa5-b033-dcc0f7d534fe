package com.xyy.saas.localserver.inventory.server.convert.campon;

import com.xyy.saas.localserver.inventory.api.campon.dto.*;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySelectBatchItemDTO;
import com.xyy.saas.localserver.inventory.enums.CampOnEnum;
import com.xyy.saas.localserver.inventory.enums.InventorySelectEnum;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.campon.InventoryCampOnBillDO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.campon.InventoryCampOnBillDetailDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;

/**
 * @Desc 预占转换器
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2025/4/22 下午9:35
 */
@Mapper
public interface CampOnConvert {

    CampOnConvert INSTANCE = Mappers.getMapper(CampOnConvert.class);

    @Mapping(source = "billType", target = "sourceType")
    @Mapping(source = "billNo", target = "sourceNo")
    InventoryCampOnBillDO convert(InventoryCampOnDTO inventoryCampOnDTO);

    @Mapping(target = "id", ignore = true)
    @Mapping(source = "guid", target = "batchGuid")
    @Mapping(source = "changeNumber", target = "campOnNumber")
    @Mapping(source = "changeNumber", target = "totalCampOnNumber")
    InventoryCampOnBillDetailDO convert(InventorySelectBatchItemDTO inventoryProductBatchItemDTO);

    InventoryCampOnBillDTO convert(InventoryCampOnBillDO inventoryCampOnBillDO);

    List<InventoryCampOnBillDetailDTO> convert(List<InventoryCampOnBillDetailDO> list);

    InventoryCampOnLotItemDTO convert(InventoryCampOnBillDetailDO detailDO);

    InventorySimpleReleaseDTO convert(InventoryReleaseDTO inventoryRelease);

    InventorySimpleReleaseDTO convert(InventoryCampOnQueryDTO inventoryCampOnQuery);

    default InventoryCampOnBillDO convertDO(InventoryCampOnDTO inventoryCampOnDTO) {
        InventoryCampOnBillDO campOnBillDO = convert(inventoryCampOnDTO);
        campOnBillDO.setStatus(CampOnEnum.CAMP_ON.getType());
        return campOnBillDO;
    }

    default List<InventoryCampOnBillDetailDO> convertDetailDO(InventoryCampOnBillDO inventoryCampOnBillDO, List<InventorySelectBatchItemDTO> batchList) {
        List<InventoryCampOnBillDetailDO> list = new ArrayList<>();
        for(InventorySelectBatchItemDTO item : batchList) {
            InventoryCampOnBillDetailDO detailDO = convert(item);
            detailDO.setBillNo(inventoryCampOnBillDO.getBillNo());
            list.add(detailDO);
        }
        return list;
    }

    default InventoryCampOnResultDTO convertResultDTO(InventoryCampOnBillDO inventoryCampOnBillDO, List<InventoryCampOnBillDetailDO> detailList) {
        InventoryCampOnResultDTO resultDTO = new InventoryCampOnResultDTO();
        resultDTO.setInventoryCampOnBill(convert(inventoryCampOnBillDO));
        resultDTO.setInventoryCampOnBillDetailList(convert(detailList));
        return resultDTO;
    }

    default InventoryReleaseDTO convertReleaseDTO(InventoryCampOnBillDO campOnBillDO, List<InventoryCampOnBillDetailDO> detailDOList) {
        InventoryReleaseDTO inventoryReleaseDTO = new InventoryReleaseDTO();
        inventoryReleaseDTO.setBillNo(campOnBillDO.getBillNo());
        inventoryReleaseDTO.setSourceType(campOnBillDO.getSourceType());
        inventoryReleaseDTO.setSourceNo(campOnBillDO.getSourceNo());
        inventoryReleaseDTO.setSelectStrategy(InventorySelectEnum.DIRECT);
        List<InventoryCampOnLotItemDTO> list = new ArrayList<>();
        for (InventoryCampOnBillDetailDO detailDO : detailDOList) {
            InventoryCampOnLotItemDTO item = convert(detailDO);
            item.setDetailId(detailDO.getId());
            item.setChangeNumber(detailDO.getCampOnNumber().subtract(detailDO.getReleaseNumber()));
            list.add(item);
        }
        inventoryReleaseDTO.setItems(list);
        return inventoryReleaseDTO;
     }

}
