package com.xyy.saas.localserver.inventory.server.service.stock;

import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySelectBatchItemDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySelectItemDTO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.batchnumber.InventoryBatchNumberDO;

import java.util.List;

@FunctionalInterface
public interface NormalSelectStrategy {

    void selectBatch(InventorySelectItemDTO item, List<InventoryBatchNumberDO> batches, List<InventorySelectBatchItemDTO> result, boolean supportPartialSelect);

}
