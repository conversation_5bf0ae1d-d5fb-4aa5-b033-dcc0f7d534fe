package com.xyy.saas.localserver.inventory.server.dal.dataobject.inventory;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 商品库存 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inventory")
@KeySequence("saas_inventory_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 商品编号
     */
    private String productPref;
    /**
     * 库存数量
     */
    private BigDecimal stockNumber;
    /**
     * 预占数量
     */
    private BigDecimal campOnNumber;
    /**
     * 成本均价
     */
    private BigDecimal costPrice;
    /**
     * 库存总金额
     */
    private BigDecimal stockAmount;
    /**
     * 最后一次采购入库价
     */
    private BigDecimal lastInPrice;
    /**
     * 最后一次供应商
     */
    private String lastSupplierGuid;
    /**
     * 最后一次入库时间
     */
    private LocalDateTime lastInTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 库存上限
     */
    private BigDecimal storeMaxLimit;
    /**
     * 库存下限
     */
    private BigDecimal storeMinLimit;
    /**
     * 版本号
     */
    private Long version;

}