package com.xyy.saas.localserver.inventory.server.api.position;

import com.xyy.saas.localserver.inventory.api.position.InventoryPositionApi;
import com.xyy.saas.localserver.inventory.api.position.dto.InventoryPositionDTO;
import com.xyy.saas.localserver.inventory.api.position.dto.InventoryPositionQueryDTO;
import com.xyy.saas.localserver.inventory.server.service.position.InventoryPositionService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 货位服务Api
 */
@Service
public class InventoryPositionApiImpl implements InventoryPositionApi {

    @Resource
    private InventoryPositionService inventoryPositionService;

    @Override
    public List<InventoryPositionDTO> getInventoryPositionList(InventoryPositionQueryDTO positionDTO) {
        return inventoryPositionService.getInventoryPositionList(positionDTO);
    }

    @Override
    public void initInventoryPosition(Long tenantId) {
        inventoryPositionService.initInventoryPosition(tenantId);
    }

}
