package com.xyy.saas.localserver.inventory.server.dal.mysql.position;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.localserver.inventory.api.position.dto.InventoryPositionCountDTO;
import com.xyy.saas.localserver.inventory.api.position.dto.InventoryPositionQueryDTO;
import com.xyy.saas.localserver.inventory.server.controller.admin.position.vo.InventoryPositionPageReqVO;
import com.xyy.saas.localserver.inventory.server.controller.admin.position.vo.InventoryPositionSaveReqVO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.position.InventoryPositionDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 货位 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InventoryPositionMapper extends BaseMapperX<InventoryPositionDO> {

    default PageResult<InventoryPositionDO> selectPage(InventoryPositionPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InventoryPositionDO>()
                .eqIfPresent(InventoryPositionDO::getGuid, reqVO.getGuid())
                .likeIfPresent(InventoryPositionDO::getAreaName, reqVO.getAreaName())
                .eqIfPresent(InventoryPositionDO::getAreaType, reqVO.getAreaType())
                .likeIfPresent(InventoryPositionDO::getPositionName, reqVO.getPositionName())
                .eqIfPresent(InventoryPositionDO::getPositionCondition, reqVO.getPositionCondition())
                .eqIfPresent(InventoryPositionDO::getTemperatureMin, reqVO.getTemperatureMin())
                .eqIfPresent(InventoryPositionDO::getTemperatureMax, reqVO.getTemperatureMax())
                .eqIfPresent(InventoryPositionDO::getHumidityMin, reqVO.getHumidityMin())
                .eqIfPresent(InventoryPositionDO::getHumidityMax, reqVO.getHumidityMax())
                .eqIfPresent(InventoryPositionDO::getDisable, reqVO.getDisable())
                .eqIfPresent(InventoryPositionDO::getSystemDefault, reqVO.getSystemDefault())
                .eqIfPresent(InventoryPositionDO::getRemark, reqVO.getRemark())
                .eqIfPresent(InventoryPositionDO::getVersion, reqVO.getVersion())
                .betweenIfPresent(InventoryPositionDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InventoryPositionDO::getId));
    }

    default Integer checkList(InventoryPositionSaveReqVO reqVO) {
        return selectList(new LambdaQueryWrapperX<InventoryPositionDO>()
                .neIfPresent(InventoryPositionDO::getId, reqVO.getId())
                .eqIfPresent(InventoryPositionDO::getAreaName, reqVO.getAreaName())
                .eqIfPresent(InventoryPositionDO::getPositionName, reqVO.getPositionName()))
                .size();
    }

    List<InventoryPositionCountDTO> getAreaAndPositionNum(InventoryPositionSaveReqVO reqVO);

    List<String> getAreaList(Long tenantId);

    default List<InventoryPositionDO> selectList(InventoryPositionQueryDTO positionDTO) {
        return selectList(new LambdaQueryWrapperX<InventoryPositionDO>()
                .eqIfPresent(InventoryPositionDO::getTenantId, positionDTO.getTenantId())
                .eqIfPresent(InventoryPositionDO::getGuid, positionDTO.getGuid())
                .likeIfPresent(InventoryPositionDO::getAreaName, positionDTO.getAreaName())
                .eqIfPresent(InventoryPositionDO::getAreaType, positionDTO.getAreaType())
                .likeIfPresent(InventoryPositionDO::getPositionName, positionDTO.getPositionName())
                .eqIfPresent(InventoryPositionDO::getPositionCondition, positionDTO.getPositionCondition())
                .eqIfPresent(InventoryPositionDO::getDisable, positionDTO.getDisable())
                .eqIfPresent(InventoryPositionDO::getSystemDefault, positionDTO.getSystemDefault()));
    }

}