package com.xyy.saas.localserver.inventory.server.service.stock.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.xyy.saas.localserver.inventory.api.change.dto.InventoryChangeDetailDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.*;
import com.xyy.saas.localserver.inventory.enums.TraceCodeStatusEnum;
import com.xyy.saas.localserver.inventory.server.convert.inventory.InventoryConvert;
import com.xyy.saas.localserver.inventory.server.convert.lotnumber.LotNumberConvert;
import com.xyy.saas.localserver.inventory.server.convert.stock.StockConvert;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.inventory.InventoryDO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.lotnumber.InventoryLotNumberDO;
import com.xyy.saas.localserver.inventory.server.dal.mysql.batchnumber.InventoryBatchNumberMapper;
import com.xyy.saas.localserver.inventory.server.dal.mysql.change.InventoryChangeDetailMapper;
import com.xyy.saas.localserver.inventory.server.dal.mysql.inventory.InventoryMapper;
import com.xyy.saas.localserver.inventory.server.dal.mysql.lotnumber.InventoryLotNumberMapper;
import com.xyy.saas.localserver.inventory.server.service.stock.InventorySelectService;
import com.xyy.saas.localserver.inventory.server.service.stock.InventoryStockService;
import com.xyy.saas.localserver.inventory.server.service.tracecode.InventoryTraceCodeService;
import com.xyy.saas.localserver.inventory.server.support.InventoryBaseSupport;
import com.xyy.saas.localserver.inventory.server.utils.StockUtil;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import reactor.util.function.Tuple2;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Desc 库存出入库
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2025/4/22 下午8:46
 */
@Service
public class InventoryStockServiceImpl implements InventoryStockService {

    @Resource
    private InventorySelectService inventorySelectService;
    @Resource
    private InventoryBaseSupport inventoryBaseSupport;
    @Resource
    private InventoryTraceCodeService inventoryTraceCodeService;
    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private InventoryLotNumberMapper lotNumberMapper;
    @Resource
    private InventoryBatchNumberMapper batchNumberMapper;
    @Resource
    private InventoryChangeDetailMapper changeDetailMapper;

    /**
     * 执行库存入库操作，增加指定仓库、批次、货品的库存数量。
     *
     * @param inventoryIncrease 入库参数 DTO，必须非空且通过校验
     * @return 返回生成的库存变动明细记录列表
     */
    @Override
    public List<InventoryChangeDetailDTO> increaseStock(InventoryIncreaseDTO inventoryIncrease) {
        return inventoryIncrease.getSelectMap().entrySet()
                .stream()
                .flatMap(entry -> handleStockChange(StockConvert.INSTANCE.convert(inventoryIncrease, entry.getKey(), entry.getValue()), true, TraceCodeStatusEnum.WAREHOUSING).stream())
                .collect(Collectors.toList());
    }

    /**
     * 批量执行库存入库操作，增加指定仓库、批次、货品的库存数量。
     *
     * @param inventoryIncreaseList 入库参数 DTO 集合，必须非空且通过校验
     * @return 返回生成的库存变动明细记录列表
     */
    @Override
    public Map<String, List<InventoryChangeDetailDTO>> batchIncreaseStock(@Valid List<InventoryIncreaseDTO> inventoryIncreaseList) {
        Map<String, List<InventoryChangeDetailDTO>> result = new HashMap<>();
        inventoryIncreaseList.forEach(inventoryIncrease -> {
            result.put(inventoryIncrease.getBillNo(), inventoryIncrease.getSelectMap().entrySet()
                    .stream()
                    .flatMap(entry -> handleStockChange(StockConvert.INSTANCE.convert(inventoryIncrease, entry.getKey(), entry.getValue()), true, TraceCodeStatusEnum.WAREHOUSING).stream())
                    .collect(Collectors.toList()));
        });
        return result;
    }

    /**
     * 执行库存出库操作，减少指定仓库、批次、货品的库存数量。
     *
     * @param inventoryReduce 出库参数 DTO，必须非空且通过校验
     * @return 返回生成的库存变动明细记录列表
     */
    @Override
    public List<InventoryChangeDetailDTO> reduceStock(InventoryReduceDTO inventoryReduce) {
        return inventoryReduce.getSelectMap().entrySet()
                .stream()
                .flatMap(entry -> handleStockChange(StockConvert.INSTANCE.convert(inventoryReduce, entry.getKey(), entry.getValue()), false, TraceCodeStatusEnum.PURCHASE_RETURN).stream())
                .collect(Collectors.toList());
    }

    /**
     * 在不同仓库或库位之间移动库存。
     *
     * @param inventoryMove 移动参数 DTO，必须非空且通过校验
     * @return 操作是否成功
     */
    @Override
    public boolean moveStock(InventoryMoveDTO inventoryMove) {
        return handleStockMove(inventoryMove);
    }

    /**
     * 执行一入一出操作，库存总量不变。
     *
     * @param InventorySwap 参数 DTO，必须非空且通过校验
     * @return 操作是否成功
     */
    @Override
    public boolean swapStock(InventorySwapDTO InventorySwap) {
        return handleStockSwap(StockConvert.INSTANCE.convertIn(InventorySwap));
    }

    /**
     * 处理库存增减操作，包含库存更新与账页记录。
     *
     * @param inventoryStock 库存操作参数
     * @param isIncrease 是否为增加库存
     * @return 返回库存变动明细列表
     */
    @Transactional
    public List<InventoryChangeDetailDTO> handleStockChange(InventoryStockDTO inventoryStock, boolean isIncrease, TraceCodeStatusEnum traceCodeStatusEnum) {
        inventoryStock.setBatchList(inventorySelectService.select(StockConvert.INSTANCE.convert(inventoryStock)));
        BigDecimal alpha = isIncrease ? BigDecimal.ONE.negate() : BigDecimal.ONE;
        Tuple2<Map<String, InventoryLotNumberDO>, Map<String, InventoryDO>> tuple = getInventoryInfo(inventoryStock, alpha);

        // 批量更新库存
        if (isIncrease) {
            batchNumberMapper.increaseStockNumber(inventoryStock.getBatchList());
        } else {
            batchNumberMapper.reduceStockNumber(inventoryStock.getBatchList());
        }
        lotNumberMapper.updateStockNumber(new ArrayList<>(tuple.getT1().values()));
        inventoryMapper.updateStockNumber(new ArrayList<>(tuple.getT2().values()));

        // 生成账页
        List<InventoryChangeDetailDTO> changeDetailDTOList = inventoryBaseSupport.generateChangeDetailList(inventoryStock, tuple.getT1(), tuple.getT2(), isIncrease);
        changeDetailMapper.insertBatch(changeDetailDTOList);

        // 更新追溯码状态
        List<InventoryChangeDetailDTO> changeTraceCodeDetailDTOList = changeDetailDTOList.stream().filter(changeDetailDTO -> CollectionUtil.isNotEmpty(changeDetailDTO.getTraceCodes())).collect(Collectors.toList());
        inventoryTraceCodeService.updateTraceCodeStatus(changeTraceCodeDetailDTOList, traceCodeStatusEnum);

        return changeDetailDTOList;
    }

    /**
     * 在不同仓库或库位之间移动库存。
     *
     * @param inventoryMove 移动参数 DTO，必须非空且通过校验
     * @return 操作是否成功
     */
    public boolean handleStockMove(InventoryMoveDTO inventoryMove) {
        // 出库并入库
        reduceStock(inventoryMove.getInventoryReduce());
        increaseStock(inventoryMove.getInventoryIncrease());
        return true;
    }

    /**
     * 处理库存一入一出操作，分别生成出库和入库的账页记录。
     *
     * @param inventoryStock 置换操作参数
     * @return 操作结果
     */
    @Transactional
    public boolean handleStockSwap(InventoryStockDTO inventoryStock) {
        inventoryStock.setBatchList(inventorySelectService.select(StockConvert.INSTANCE.convert(inventoryStock)));

        // 生成入库货品账页
        Tuple2<Map<String, InventoryLotNumberDO>, Map<String, InventoryDO>> tuple = getInventoryInfo(inventoryStock, BigDecimal.ONE.negate());
        changeDetailMapper.insertBatch(inventoryBaseSupport.generateChangeDetailList(inventoryStock, tuple.getT1(), tuple.getT2(), true));

        // 生成出库货品账页
        tuple = getInventoryInfo(inventoryStock, tuple, BigDecimal.ONE);
        changeDetailMapper.insertBatch(inventoryBaseSupport.generateChangeDetailList(inventoryStock, tuple.getT1(), tuple.getT2(), false));
        return true;
    }

    /**
     * 获取库存信息
     *
     * @param inventoryStock 库存操作参数
     * @param alpha 变化系数（正负表示增减）
     * @return 包含批次号库存和商品库存的元组
     */
    public Tuple2<Map<String, InventoryLotNumberDO>, Map<String, InventoryDO>> getInventoryInfo(InventoryStockDTO inventoryStock, BigDecimal alpha) {
        Tuple2<Map<String, InventoryLotNumberDO>, Map<String, InventoryDO>> inventoryTuple = inventoryBaseSupport.getInventoryInfoByBatch(inventoryStock.getBatchList());
        return getInventoryInfo(inventoryStock, inventoryTuple, alpha);
    }

    /**
     * 获取库存信息
     *
     * @param inventoryStock 库存操作参数
     * @param inventoryTuple 已有的库存信息元组
     * @param alpha 变化系数
     * @return 更新后的库存信息元组
     */
    @Transactional
    public Tuple2<Map<String, InventoryLotNumberDO>, Map<String, InventoryDO>> getInventoryInfo(InventoryStockDTO inventoryStock,
                                                                                                Tuple2<Map<String, InventoryLotNumberDO>, Map<String, InventoryDO>> inventoryTuple,
                                                                                                BigDecimal alpha) {
        Tuple2<Map<String, BigDecimal>, Map<String, BigDecimal>> numberTuple = inventoryBaseSupport.getNumberInfoByBatch(inventoryStock.getBatchList());

        // 新增库存
        addBatch(inventoryStock, inventoryStock.getBatchList(), inventoryTuple.getT1(), inventoryTuple.getT2());

        // 匹配和更新库存
        inventoryBaseSupport.updateStockNumber(numberTuple, inventoryTuple, alpha);
        return inventoryTuple;
    }

    /**
     * 新增批次库存记录，若对应批次或商品不存在则插入新记录。
     *
     * @param inventoryStock 库存操作参数
     * @param items 批次项列表
     * @param lotNumberDOMap 当前批次号库存映射
     * @param inventoryDOMap 当前商品库存映射
     */
    public void addBatch(InventoryStockDTO inventoryStock, List<InventorySelectBatchItemDTO> items,
                         Map<String, InventoryLotNumberDO> lotNumberDOMap, Map<String, InventoryDO> inventoryDOMap) {
        if (inventoryStock.getSelectStrategy() != null) {
            return;
        }
        doAddBatch(items, lotNumberDOMap, inventoryDOMap);
    }

    /**
     * 实际执行批次库存插入操作，由 addBatch 调用。
     *
     * @param items 批次项列表
     * @param lotNumberDOMap 当前批次号库存映射
     * @param inventoryDOMap 当前商品库存映射
     */
    @Transactional
    public void doAddBatch(List<InventorySelectBatchItemDTO> items, Map<String, InventoryLotNumberDO> lotNumberDOMap, Map<String, InventoryDO> inventoryDOMap) {
        List<InventoryLotNumberDO> insertLotNumberList = new ArrayList<>();
        List<InventoryDO> insertInventoryList = new ArrayList<>();
        for (InventorySelectBatchItemDTO item : items) {
            String lotKey = StockUtil.getLotNoKey(item);
            String productPref = item.getProductPref();

            if (!lotNumberDOMap.containsKey(lotKey)) {
                InventoryLotNumberDO lotNumberDO = LotNumberConvert.INSTANCE.convert(item);
                insertLotNumberList.add(lotNumberDO);
                lotNumberDOMap.put(lotKey, lotNumberDO);
            }

            if (!inventoryDOMap.containsKey(productPref)) {
                InventoryDO inventoryDO = InventoryConvert.INSTANCE.convert(item);
                insertInventoryList.add(inventoryDO);
                inventoryDOMap.put(productPref, inventoryDO);
            }
        }

        if (CollectionUtils.isNotEmpty(insertLotNumberList)) {
            lotNumberMapper.insertBatch(insertLotNumberList);
        }
        if (CollectionUtils.isNotEmpty(insertInventoryList)) {
            inventoryMapper.insertBatch(insertInventoryList);
        }
    }

}
