package com.xyy.saas.localserver.inventory.server.dal.mysql.bill;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.bill.InventoryBillDetailDO;
import org.apache.ibatis.annotations.Mapper;
import com.xyy.saas.localserver.inventory.server.controller.admin.bill.vo.*;

/**
 * 库存单据详情 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InventoryBillDetailMapper extends BaseMapperX<InventoryBillDetailDO> {

    default PageResult<InventoryBillDetailDO> selectPage(InventoryBillDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InventoryBillDetailDO>()
                .eqIfPresent(InventoryBillDetailDO::getBillNo, reqVO.getBillNo())
                .eqIfPresent(InventoryBillDetailDO::getProductPref, reqVO.getProductPref())
                .eqIfPresent(InventoryBillDetailDO::getLotNo, reqVO.getLotNo())
                .eqIfPresent(InventoryBillDetailDO::getPositionGuid, reqVO.getPositionGuid())
                .eqIfPresent(InventoryBillDetailDO::getRegisterQuantity, reqVO.getRegisterQuantity())
                .eqIfPresent(InventoryBillDetailDO::getChangeQuantity, reqVO.getChangeQuantity())
                .eqIfPresent(InventoryBillDetailDO::getBeforeQuantity, reqVO.getBeforeQuantity())
                .eqIfPresent(InventoryBillDetailDO::getActualQuantity, reqVO.getActualQuantity())
                .eqIfPresent(InventoryBillDetailDO::getCostPrice, reqVO.getCostPrice())
                .eqIfPresent(InventoryBillDetailDO::getRetailPrice, reqVO.getRetailPrice())
                .eqIfPresent(InventoryBillDetailDO::getStockAmount, reqVO.getStockAmount())
                .eqIfPresent(InventoryBillDetailDO::getRemark, reqVO.getRemark())
                .eqIfPresent(InventoryBillDetailDO::getExt, reqVO.getExt())
                .betweenIfPresent(InventoryBillDetailDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InventoryBillDetailDO::getId));
    }

    default List<InventoryBillDetailDO> getDetailByBillNo(String billNo) {
        return selectList(new LambdaQueryWrapperX<InventoryBillDetailDO>().eq(InventoryBillDetailDO::getBillNo, billNo));
    }
}