package com.xyy.saas.localserver.inventory.server.controller.admin.lotnumber.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商品批号库存 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InventoryLotNumberRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3971")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "商品编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编号")
    private String productPref;

    @Schema(description = "批号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("批号")
    private String lotNo;

    @Schema(description = "货位guid", requiredMode = Schema.RequiredMode.REQUIRED, example = "22319")
    @ExcelProperty("货位guid")
    private String positionGuid;

    @Schema(description = "生产日期")
    @ExcelProperty("生产日期")
    private LocalDateTime productionDate;

    @Schema(description = "到期时间")
    @ExcelProperty("到期时间")
    private LocalDateTime expiryDate;

    @Schema(description = "库存数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("库存数量")
    private BigDecimal stockNumber;

    @Schema(description = "预占数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("预占数量")
    private BigDecimal campOnNumber;

    @Schema(description = "成本均价", requiredMode = Schema.RequiredMode.REQUIRED, example = "21070")
    @ExcelProperty("成本均价")
    private BigDecimal costPrice;

    @Schema(description = "库存总金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("库存总金额")
    private BigDecimal stockAmount;

    @Schema(description = "是否停售: 0--未停售 1--已停售", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否停售: 0--未停售 1--已停售")
    private Boolean stopSale;

    @Schema(description = "最后一次采购入库价", requiredMode = Schema.RequiredMode.REQUIRED, example = "10001")
    @ExcelProperty("最后一次采购入库价")
    private BigDecimal lastInPrice;

    @Schema(description = "最后入库时间")
    @ExcelProperty("最后入库时间")
    private LocalDateTime lastInTime;

    @Schema(description = "最后一次供应商", example = "17901")
    @ExcelProperty("最后一次供应商")
    private String lastSupplierGuid;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本号")
    private Long version;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}