package com.xyy.saas.localserver.inventory.server.controller.admin.lotnumber.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商品批号库存新增/修改 Request VO")
@Data
public class InventoryLotNumberSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "3971")
    private Long id;

    @Schema(description = "商品编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品编号不能为空")
    private String productPref;

    @Schema(description = "批号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "批号不能为空")
    private String lotNo;

    @Schema(description = "货位guid", requiredMode = Schema.RequiredMode.REQUIRED, example = "22319")
    @NotEmpty(message = "货位guid不能为空")
    private String positionGuid;

    @Schema(description = "生产日期")
    private LocalDateTime productionDate;

    @Schema(description = "到期时间")
    private LocalDateTime expiryDate;

    @Schema(description = "库存数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "库存数量不能为空")
    private BigDecimal stockNumber;

    @Schema(description = "预占数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "预占数量不能为空")
    private BigDecimal campOnNumber;

    @Schema(description = "成本均价", requiredMode = Schema.RequiredMode.REQUIRED, example = "21070")
    @NotNull(message = "成本均价不能为空")
    private BigDecimal costPrice;

    @Schema(description = "库存总金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "库存总金额不能为空")
    private BigDecimal stockAmount;

    @Schema(description = "是否停售: 0--未停售 1--已停售", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "是否停售: 0--未停售 1--已停售不能为空")
    private Boolean stopSale;

    @Schema(description = "最后一次采购入库价", requiredMode = Schema.RequiredMode.REQUIRED, example = "10001")
    @NotNull(message = "最后一次采购入库价不能为空")
    private BigDecimal lastInPrice;

    @Schema(description = "最后入库时间")
    private LocalDateTime lastInTime;

    @Schema(description = "最后一次供应商", example = "17901")
    private String lastSupplierGuid;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "版本号不能为空")
    private Long version;

}