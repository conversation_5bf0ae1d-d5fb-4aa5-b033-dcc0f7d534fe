package com.xyy.saas.localserver.inventory.server.controller.admin.bill.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 库存单据新增/修改 Request VO")
@Data
public class InventoryBillSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "27254")
    private Long id;

    @Schema(description = "单据类型 ：101-货位移动 102-盘点 103-报损报溢 104-库存拆零 105-质量复查", requiredMode = Schema.RequiredMode.REQUIRED, example = "102")
    @NotNull(message = "单据类型不能为空")
    private Integer billType;

    @Schema(description = "单据编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "单据编号不能为空")
    private String billNo;

    @Schema(description = "来源类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "来源类型不能为空")
    private Integer sourceType;

    @Schema(description = "来源编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "来源编号不能为空")
    private String sourceNo;

    @Schema(description = "来源描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @NotEmpty(message = "来源描述不能为空")
    private String sourceDescription;

    @Schema(description = "审批状态: 1-暂存 2-待检验 3-审批中 5-已驳回 6-已完成", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "审批状态: 1-暂存 2-待检验 3-审批中 5-已驳回 6-已完成 不能为空")
    private Integer status;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "扩展信息")
    private String ext;

    @Schema(description = "详情信息")
    private List<InventoryBillDetailSaveReqVO> list;

}