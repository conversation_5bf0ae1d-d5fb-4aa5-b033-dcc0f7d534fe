package com.xyy.saas.localserver.inventory.server.dal.mysql.change;

import cn.hutool.core.collection.CollectionUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.localserver.inventory.api.change.dto.InventoryChangeDetailDTO;
import com.xyy.saas.localserver.inventory.server.controller.admin.change.vo.InventoryChangeDetailPageReqVO;
import com.xyy.saas.localserver.inventory.server.convert.stock.StockConvert;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.change.InventoryChangeDetailDO;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;
import java.util.Map;

/**
 * 库存变动明细 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InventoryChangeDetailMapper extends BaseMapperX<InventoryChangeDetailDO> {

    default PageResult<InventoryChangeDetailDO> selectPage(InventoryChangeDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InventoryChangeDetailDO>()
                .eqIfPresent(InventoryChangeDetailDO::getBillType, reqVO.getBillType())
                .eqIfPresent(InventoryChangeDetailDO::getBillNo, reqVO.getBillNo())
                .betweenIfPresent(InventoryChangeDetailDO::getBillTime, reqVO.getBillTime())
                .eqIfPresent(InventoryChangeDetailDO::getProductPref, reqVO.getProductPref())
                .eqIfPresent(InventoryChangeDetailDO::getLotNo, reqVO.getLotNo())
                .eqIfPresent(InventoryChangeDetailDO::getPositionGuid, reqVO.getPositionGuid())
                .eqIfPresent(InventoryChangeDetailDO::getBatchGuid, reqVO.getBatchGuid())
                .eqIfPresent(InventoryChangeDetailDO::getSupplierGuid, reqVO.getSupplierGuid())
                .eqIfPresent(InventoryChangeDetailDO::getInNumber, reqVO.getInNumber())
                .eqIfPresent(InventoryChangeDetailDO::getInPrice, reqVO.getInPrice())
                .eqIfPresent(InventoryChangeDetailDO::getInAmount, reqVO.getInAmount())
                .eqIfPresent(InventoryChangeDetailDO::getOutNumber, reqVO.getOutNumber())
                .eqIfPresent(InventoryChangeDetailDO::getOutPrice, reqVO.getOutPrice())
                .eqIfPresent(InventoryChangeDetailDO::getOutAmount, reqVO.getOutAmount())
                .eqIfPresent(InventoryChangeDetailDO::getBatchNumber, reqVO.getBatchNumber())
                .eqIfPresent(InventoryChangeDetailDO::getBatchAmount, reqVO.getBatchAmount())
                .eqIfPresent(InventoryChangeDetailDO::getStockNumber, reqVO.getStockNumber())
                .eqIfPresent(InventoryChangeDetailDO::getStockAmount, reqVO.getStockAmount())
                .eqIfPresent(InventoryChangeDetailDO::getCostPrice, reqVO.getCostPrice())
                .eqIfPresent(InventoryChangeDetailDO::getTaxRate, reqVO.getTaxRate())
                .eqIfPresent(InventoryChangeDetailDO::getRemark, reqVO.getRemark())
                .eqIfPresent(InventoryChangeDetailDO::getExt, reqVO.getExt())
                .eqIfPresent(InventoryChangeDetailDO::getSourceGuid, reqVO.getSourceGuid())
                .eqIfPresent(InventoryChangeDetailDO::getRootGuid, reqVO.getRootGuid())
                .betweenIfPresent(InventoryChangeDetailDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InventoryChangeDetailDO::getId));
    }

    default Boolean insertBatch(List<InventoryChangeDetailDTO> items) {
        List<InventoryChangeDetailDO> list = StockConvert.INSTANCE.convert(items);
        if(CollectionUtil.isNotEmpty(list)) {
            return insertBatch(list);
        }
        return false;
    }

}