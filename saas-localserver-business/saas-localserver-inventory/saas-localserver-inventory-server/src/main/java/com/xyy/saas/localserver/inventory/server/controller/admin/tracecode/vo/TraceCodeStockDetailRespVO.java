package com.xyy.saas.localserver.inventory.server.controller.admin.tracecode.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 追溯码帐页 Response VO")
@Data
@ExcelIgnoreUnannotated
public class TraceCodeStockDetailRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "4294")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "单据类型/摘要", example = "2")
    @ExcelProperty("单据类型/摘要")
    private Integer billType;

    @Schema(description = "单据编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单据编号")
    private String billNo;

    @Schema(description = "领域单据时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("领域单据时间")
    private LocalDateTime billTime;

    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编码")
    private String productPref;

    @Schema(description = "批号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("批号")
    private String lotNo;

    @Schema(description = "批次库存guid", requiredMode = Schema.RequiredMode.REQUIRED, example = "15999")
    @ExcelProperty("批次库存guid")
    private String batchGuid;

    @Schema(description = "供应商编码")
    @ExcelProperty("供应商编码")
    private String supplierPref;

    @Schema(description = "追溯码/UDI码", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("追溯码/UDI码")
    private String traceCode;

    @Schema(description = "入库数量")
    @ExcelProperty("入库数量")
    private BigDecimal inNumber;

    @Schema(description = "出库数量")
    @ExcelProperty("出库数量")
    private BigDecimal outNumber;

    @Schema(description = "剩余数量")
    @ExcelProperty("剩余数量")
    private BigDecimal remainNumber;

    @Schema(description = "追溯码状态 1-已收货；2-已入库；3-已占用；4-已销售；5-已销毁；6-已采退；7-已销退", example = "1")
    @ExcelProperty("追溯码状态 1-已收货；2-已入库；3-已占用；4-已销售；5-已销毁；6-已采退；7-已销退")
    private Integer status;

    @Schema(description = "包装规格 1-小包；2-中包；3-大包", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("包装规格 1-小包；2-中包；3-大包")
    private Integer packageLevel;

    @Schema(description = "父码")
    @ExcelProperty("父码")
    private String parentCode;

    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本号")
    private Long version;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}