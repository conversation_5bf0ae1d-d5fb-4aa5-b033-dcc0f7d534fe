package com.xyy.saas.localserver.inventory.server.controller.admin.tracecode.vo;

import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 追溯码分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class TraceCodePageReqVO extends PageParam {

    @Schema(description = "商品编码")
    private String productPref;

    @Schema(description = "批号")
    private String lotNo;

    @Schema(description = "批次库存guid", example = "3919")
    private String batchGuid;

    @Schema(description = "供应商编码")
    private String supplierPref;

    @Schema(description = "追溯码/UDI码")
    private String traceCode;

    @Schema(description = "追溯码状态 1-已收货；2-已入库；3-已占用；4-已销售；5-已销毁；6-已采退；7-已销退", example = "2")
    private Integer status;

    @Schema(description = "追溯码上一次状态", example = "1")
    private Integer previousStatus;

    @Schema(description = "包装规格 1-小包；2-中包；3-大包")
    private Integer packageLevel;

    @Schema(description = "父码")
    private String parentCode;

    @Schema(description = "版本号")
    private Long version;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}