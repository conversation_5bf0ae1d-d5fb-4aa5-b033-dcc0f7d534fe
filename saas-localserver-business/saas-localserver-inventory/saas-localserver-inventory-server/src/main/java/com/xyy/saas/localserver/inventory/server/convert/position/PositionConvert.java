package com.xyy.saas.localserver.inventory.server.convert.position;

import com.xyy.saas.localserver.inventory.api.position.dto.InventoryPositionDTO;
import com.xyy.saas.localserver.inventory.api.position.dto.InventoryPositionTreeDTO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.position.InventoryPositionDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Mapper
public interface PositionConvert {

    PositionConvert INSTANCE = Mappers.getMapper(PositionConvert.class);

    List<InventoryPositionTreeDTO> convert(List<InventoryPositionDO> positionDOList);

    List<InventoryPositionDTO> convertToList(List<InventoryPositionDO> positionDOList);

    default InventoryPositionTreeDTO convertToTree(List<InventoryPositionDO> positionDOList) {
        InventoryPositionTreeDTO dto = new InventoryPositionTreeDTO();
        dto.setItems(new HashMap<>());
        Map<String, List<InventoryPositionDO>> map = positionDOList.stream().collect(Collectors.groupingBy(
                InventoryPositionDO::getAreaName, LinkedHashMap::new, Collectors.toList()));
        for (Map.Entry<String, List<InventoryPositionDO>> entry : map.entrySet()) {
            dto.getItems().put(entry.getKey(), convert(entry.getValue()));
        }
        return dto;
    }

}
