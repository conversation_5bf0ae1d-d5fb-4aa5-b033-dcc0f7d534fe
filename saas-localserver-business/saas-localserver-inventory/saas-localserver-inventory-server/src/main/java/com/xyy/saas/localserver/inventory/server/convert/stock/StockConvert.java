package com.xyy.saas.localserver.inventory.server.convert.stock;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.localserver.inventory.api.campon.dto.InventoryCampOnDTO;
import com.xyy.saas.localserver.inventory.api.campon.dto.InventoryReleaseDTO;
import com.xyy.saas.localserver.inventory.api.change.dto.InventoryChangeDetailDTO;
import com.xyy.saas.localserver.inventory.api.constant.InventoryConstant;
import com.xyy.saas.localserver.inventory.api.stock.dto.*;
import com.xyy.saas.localserver.inventory.enums.InventorySelectEnum;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.batchnumber.InventoryBatchNumberDO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.campon.InventoryCampOnBillDO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.campon.InventoryCampOnBillDetailDO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.change.InventoryChangeDetailDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Mapper
public interface StockConvert {

    StockConvert INSTANCE = Mappers.getMapper(StockConvert.class);

    InventorySelectDTO convert(InventoryStockDTO inventoryStockDTO);

    @Mapping(source = "inTime", target = "stockTime")
    InventoryStockDTO convert(InventoryIncreaseDTO inventoryIncreaseDTO);

    default InventoryStockDTO convert(InventoryIncreaseDTO inventoryIncreaseDTO, InventorySelectEnum selectStrategy, List<InventorySelectItemDTO> items) {
        InventoryStockDTO convert = convert(inventoryIncreaseDTO);
        convert.setSelectStrategy(selectStrategy);
        convert.setItems(items);
        return convert;
    }

    @Mapping(source = "outTime", target = "stockTime")
    InventoryStockDTO convert(InventoryReduceDTO inventoryReduceDTO);

    default InventoryStockDTO convert(InventoryReduceDTO inventoryReduceDTO, InventorySelectEnum selectStrategy, List<InventorySelectItemDTO> items) {
        InventoryStockDTO convert = convert(inventoryReduceDTO);
        convert.setSelectStrategy(selectStrategy);
        convert.setItems(items);
        return convert;
    }

    @Mapping(source = "inBillType", target = "billType")
    @Mapping(source = "inBillNo", target = "billNo")
    InventoryStockDTO convertIn(InventorySwapDTO inventorySwapDTO);

    @Mapping(source = "outBillType", target = "billType")
    @Mapping(source = "outBillNo", target = "billNo")
    InventoryStockDTO convertOut(InventorySwapDTO inventoryUnChangeDTO);

    InventoryStockDTO convert(InventoryCampOnDTO inventoryCampOnDTO);

    default InventoryStockDTO convert(InventoryCampOnDTO inventoryCampOnDTO, InventorySelectEnum selectStrategy, List<InventorySelectItemDTO> items) {
        InventoryStockDTO convert = convert(inventoryCampOnDTO);
        convert.setSelectStrategy(selectStrategy);
        convert.setItems(items);
        return convert;
    }

    @Mapping(source = "sourceType", target = "billType")
    @Mapping(source = "sourceNo", target = "billNo")
    InventoryStockDTO convert(InventoryReleaseDTO inventoryRelease);

    InventoryBatchNumberDO convertDO(InventorySelectItemDTO lotItemDTO);

    InventorySelectBatchItemDTO convert(InventoryBatchNumberDO batchNumberDO);

    List<InventorySelectBatchItemDTO> convertDTOList(List<InventoryBatchNumberDO> batchNumberDOList);

    InventorySelectItemDTO convert(InventoryCampOnBillDetailDO detailDO);

    InventoryChangeDetailDO convert(InventoryChangeDetailDTO detailDTO);

    default List<InventoryChangeDetailDO> convert(List<InventoryChangeDetailDTO> detailDOList) {
        return detailDOList.stream().map(item -> {
            InventoryChangeDetailDO convert = convert(item);
            if (CollectionUtil.isNotEmpty(item.getTraceCodes())) {
                convert.setExt(JSON.toJSONString(Map.of(InventoryConstant.TRACE_CODES, JSON.toJSONString(item.getTraceCodes()))));
            }
            return convert;
        }).collect(Collectors.toList());
    }

    default InventoryStockDTO convert(InventoryCampOnBillDO campOnBillDO, List<InventoryCampOnBillDetailDO> detailDOList) {
        InventoryStockDTO inventoryStockDTO = new InventoryStockDTO();
        inventoryStockDTO.setBillType(campOnBillDO.getSourceType());
        inventoryStockDTO.setBillNo(campOnBillDO.getSourceNo());
        inventoryStockDTO.setSelectStrategy(InventorySelectEnum.DIRECT);
        List<InventorySelectItemDTO> list = new ArrayList<>();
        for (InventoryCampOnBillDetailDO detailDO : detailDOList) {
            InventorySelectItemDTO item = convert(detailDO);
            item.setChangeNumber(detailDO.getCampOnNumber().subtract(detailDO.getReleaseNumber()));
            list.add(item);
        }
        inventoryStockDTO.setItems(list);
        return inventoryStockDTO;
    }

}
