package com.xyy.saas.localserver.inventory.server.controller.admin.position.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 货位 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InventoryPositionRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "19054")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21525")
    @ExcelProperty("编号")
    private String guid;

    @Schema(description = "货区名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("货区名称")
    private String areaName;

    @Schema(description = "货区类型：1-合格，2-不合格，3-待验区/待处理，4-退货区/待退货", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("货区类型：1-合格，2-不合格，3-待验区/待处理，4-退货区/待退货")
    private Integer areaType;

    @Schema(description = "货位名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @ExcelProperty("货位名称")
    private String positionName;

    @Schema(description = "存储条件：1--常温,2--阴凉,3--冷藏,4--其他", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("存储条件：1--常温,2--阴凉,3--冷藏,4--其他")
    private Integer positionCondition;

    @Schema(description = "最低温度")
    @ExcelProperty("最低温度")
    private BigDecimal temperatureMin;

    @Schema(description = "最高温度")
    @ExcelProperty("最高温度")
    private BigDecimal temperatureMax;

    @Schema(description = "最低湿度")
    @ExcelProperty("最低湿度")
    private BigDecimal humidityMin;

    @Schema(description = "最高湿度")
    @ExcelProperty("最高湿度")
    private BigDecimal humidityMax;

    @Schema(description = "启用状态: 0--不禁用 1--禁用", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("启用状态: 0--不禁用 1--禁用")
    private Boolean disable;

    @Schema(description = "是否默认：0--非默认 1--默认", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("是否默认：0--非默认 1--默认")
    private Boolean systemDefault;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本号")
    private Long version;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}