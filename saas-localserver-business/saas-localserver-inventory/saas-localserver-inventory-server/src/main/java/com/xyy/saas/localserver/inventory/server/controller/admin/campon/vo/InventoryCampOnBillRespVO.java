package com.xyy.saas.localserver.inventory.server.controller.admin.campon.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 预占单 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InventoryCampOnBillRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "20384")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "单据类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("单据类型")
    private Integer billType;

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("编号")
    private String billNo;

    @Schema(description = "来源单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("来源单号")
    private String sourceNo;

    @Schema(description = "上级预占单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("上级预占单编号")
    private String parentNo;

    @Schema(description = "状态: 1:预占中; 2:预占释放", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("状态: 1:预占中; 2:预占释放")
    private Integer status;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本号")
    private Long version;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}