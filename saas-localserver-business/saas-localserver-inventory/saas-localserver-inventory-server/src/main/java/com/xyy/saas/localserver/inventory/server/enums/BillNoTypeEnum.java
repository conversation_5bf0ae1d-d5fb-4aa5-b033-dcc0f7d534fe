package com.xyy.saas.localserver.inventory.server.enums;

import com.xyy.saas.localserver.entity.config.db.BillNoGenerator;
import lombok.Getter;

import java.time.LocalDateTime;

/**
 * 单据编号类型枚举
 */
@Getter
public enum BillNoTypeEnum {

//    INVENTORY_TRACE("ITP", "库存追踪维度表", InventoryTraceDO.class, "pref"),
    ;

    private final String prefix;
    private final String description;
    private final Class<?> entityClass;
    private final String columnName;

    BillNoTypeEnum(String prefix, String description, Class<?> entityClass, String columnName) {
        this.prefix = prefix;
        this.description = description;
        this.entityClass = entityClass;
        this.columnName = columnName;
    }

    /**
     * 获取单据编号
     */
    public String getBillNo(LocalDateTime localDateTime) {
        return BillNoGenerator.generateBillNoByDate(prefix, entityClass, columnName, localDateTime);
    }

}