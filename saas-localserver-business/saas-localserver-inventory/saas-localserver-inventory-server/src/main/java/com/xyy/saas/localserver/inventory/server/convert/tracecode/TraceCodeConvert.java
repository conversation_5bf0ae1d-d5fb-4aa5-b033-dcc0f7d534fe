package com.xyy.saas.localserver.inventory.server.convert.tracecode;

import com.xyy.saas.localserver.inventory.api.change.dto.InventoryChangeDetailDTO;
import com.xyy.saas.localserver.inventory.api.tracecode.dto.TraceCodeChangeDTO;
import com.xyy.saas.localserver.inventory.api.tracecode.dto.TraceCodeDTO;
import com.xyy.saas.localserver.inventory.api.tracecode.dto.TraceCodeStockDetailDTO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.tracecode.TraceCodeDO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.tracecode.TraceCodeStockDetailDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface TraceCodeConvert {

    TraceCodeConvert INSTANCE = Mappers.getMapper(TraceCodeConvert.class);

    TraceCodeDTO convert(InventoryChangeDetailDTO inventoryChangeDetailDTO);

    List<TraceCodeDO> convert(List<TraceCodeDTO> list);

    TraceCodeStockDetailDO convert2Detail(TraceCodeStockDetailDTO traceCodeStockDetailDTO);

    TraceCodeStockDetailDTO convert(TraceCodeDTO item);

    default TraceCodeDTO convert(InventoryChangeDetailDTO inventoryChangeDetailDTO, TraceCodeChangeDTO traceCodeChangeDTO) {
        TraceCodeDTO dto = convert(inventoryChangeDetailDTO);
        dto.setTraceCode(traceCodeChangeDTO.getTraceCode());
        dto.setPackageLevel(traceCodeChangeDTO.getPackageLevel());
        dto.setChangeNumber(traceCodeChangeDTO.getChangeNumber());
        dto.setInNumber(null);
        dto.setOutNumber(null);
        return dto;
    }

}
