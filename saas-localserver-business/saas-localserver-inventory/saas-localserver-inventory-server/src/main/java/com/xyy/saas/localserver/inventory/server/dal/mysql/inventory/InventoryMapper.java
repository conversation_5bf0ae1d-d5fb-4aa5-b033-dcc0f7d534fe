package com.xyy.saas.localserver.inventory.server.dal.mysql.inventory;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.localserver.inventory.api.inventory.dto.InventoryQueryDTO;
import com.xyy.saas.localserver.inventory.server.controller.admin.inventory.vo.*;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.inventory.InventoryDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品库存 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InventoryMapper extends BaseMapperX<InventoryDO> {

    default PageResult<InventoryDO> selectPage(InventoryPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InventoryDO>()
                .eqIfPresent(InventoryDO::getProductPref, reqVO.getProductPref())
                .eqIfPresent(InventoryDO::getStockNumber, reqVO.getStockNumber())
                .eqIfPresent(InventoryDO::getCampOnNumber, reqVO.getCampOnNumber())
                .eqIfPresent(InventoryDO::getCostPrice, reqVO.getCostPrice())
                .eqIfPresent(InventoryDO::getStockAmount, reqVO.getStockAmount())
                .eqIfPresent(InventoryDO::getLastInPrice, reqVO.getLastInPrice())
                .eqIfPresent(InventoryDO::getLastSupplierGuid, reqVO.getLastSupplierGuid())
                .betweenIfPresent(InventoryDO::getLastInTime, reqVO.getLastInTime())
                .eqIfPresent(InventoryDO::getRemark, reqVO.getRemark())
                .eqIfPresent(InventoryDO::getStoreMaxLimit, reqVO.getStoreMaxLimit())
                .eqIfPresent(InventoryDO::getStoreMinLimit, reqVO.getStoreMinLimit())
                .eqIfPresent(InventoryDO::getVersion, reqVO.getVersion())
                .betweenIfPresent(InventoryDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InventoryDO::getId));
    }

    default List<InventoryDO> selectByProductPref(List<String> productPrefList) {
        return selectList(new LambdaQueryWrapperX<InventoryDO>()
                .inIfPresent(InventoryDO::getProductPref, productPrefList));
    }

    int updateStockNumber(@Param("items") List<InventoryDO> items);

    int updateCampOnNumber(@Param("items") List<InventoryDO> items);

    default List<InventoryDO> selectList(InventoryQueryDTO queryDTO) {
        return selectList(new LambdaQueryWrapperX<InventoryDO>()
                .eqIfPresent(InventoryDO::getProductPref, queryDTO.getProductPref())
        );
    }

}