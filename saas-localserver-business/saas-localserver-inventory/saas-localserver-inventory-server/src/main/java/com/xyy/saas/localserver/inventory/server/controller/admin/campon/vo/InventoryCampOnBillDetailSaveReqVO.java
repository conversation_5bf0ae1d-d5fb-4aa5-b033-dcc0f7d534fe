package com.xyy.saas.localserver.inventory.server.controller.admin.campon.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 预占单详情新增/修改 Request VO")
@Data
public class InventoryCampOnBillDetailSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "29804")
    private Long id;

    @Schema(description = "预占单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "预占单编号不能为空")
    private String billNo;

    @Schema(description = "商品编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品编号不能为空")
    private String productPref;

    @Schema(description = "批号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "批号不能为空")
    private String lotNo;

    @Schema(description = "货位guid", requiredMode = Schema.RequiredMode.REQUIRED, example = "18617")
    @NotEmpty(message = "货位guid不能为空")
    private String positionGuid;

    @Schema(description = "批次库存guid", requiredMode = Schema.RequiredMode.REQUIRED, example = "17898")
    @NotEmpty(message = "批次库存guid不能为空")
    private String batchGuid;

    @Schema(description = "预占数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "预占数量不能为空")
    private BigDecimal campOnNumber;

    @Schema(description = "释放数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "释放数量不能为空")
    private BigDecimal releaseNumber;

    @Schema(description = "总预占数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "总预占数量不能为空")
    private BigDecimal totalCampOnNumber;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

}