package com.xyy.saas.localserver.inventory.server.dal.dataobject.bill;

import lombok.*;
import java.math.BigDecimal;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 库存单据详情 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inventory_bill_detail")
@KeySequence("saas_inventory_bill_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryBillDetailDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 单据编号
     */
    private String billNo;
    /**
     * 商品编号
     */
    private String productPref;
    /**
     * 批号
     */
    private String lotNo;
    /**
     * 货位guid
     */
    private String positionGuid;
    /**
     * 登记数量
     */
    private BigDecimal registerQuantity;
    /**
     * 变动数量
     */
    private BigDecimal changeQuantity;
    /**
     * 变动前数量
     */
    private BigDecimal beforeQuantity;
    /**
     * 实际数量
     */
    private BigDecimal actualQuantity;
    /**
     * 成本价
     */
    private BigDecimal costPrice;
    /**
     * 零售价
     */
    private BigDecimal retailPrice;
    /**
     * 库存金额
     */
    private BigDecimal stockAmount;
    /**
     * 备注
     */
    private String remark;
    /**
     * 扩展信息
     */
    private String ext;

}