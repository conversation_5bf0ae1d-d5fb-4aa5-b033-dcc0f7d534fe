package com.xyy.saas.localserver.inventory.server.dal.mysql.lotnumber;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.localserver.inventory.api.lotnumber.dto.InventoryLotNumberQueryDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySelectBatchItemDTO;
import com.xyy.saas.localserver.inventory.server.controller.admin.lotnumber.vo.*;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.lotnumber.InventoryLotNumberDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 商品批号库存 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InventoryLotNumberMapper extends BaseMapperX<InventoryLotNumberDO> {

    default PageResult<InventoryLotNumberDO> selectPage(InventoryLotNumberPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InventoryLotNumberDO>()
                .eqIfPresent(InventoryLotNumberDO::getProductPref, reqVO.getProductPref())
                .eqIfPresent(InventoryLotNumberDO::getLotNo, reqVO.getLotNo())
                .eqIfPresent(InventoryLotNumberDO::getPositionGuid, reqVO.getPositionGuid())
                .betweenIfPresent(InventoryLotNumberDO::getProductionDate, reqVO.getProductionDate())
                .betweenIfPresent(InventoryLotNumberDO::getExpiryDate, reqVO.getExpiryDate())
                .eqIfPresent(InventoryLotNumberDO::getStockNumber, reqVO.getStockNumber())
                .eqIfPresent(InventoryLotNumberDO::getCampOnNumber, reqVO.getCampOnNumber())
                .eqIfPresent(InventoryLotNumberDO::getCostPrice, reqVO.getCostPrice())
                .eqIfPresent(InventoryLotNumberDO::getStockAmount, reqVO.getStockAmount())
                .eqIfPresent(InventoryLotNumberDO::getStopSale, reqVO.getStopSale())
                .eqIfPresent(InventoryLotNumberDO::getLastInPrice, reqVO.getLastInPrice())
                .betweenIfPresent(InventoryLotNumberDO::getLastInTime, reqVO.getLastInTime())
                .eqIfPresent(InventoryLotNumberDO::getLastSupplierGuid, reqVO.getLastSupplierGuid())
                .eqIfPresent(InventoryLotNumberDO::getRemark, reqVO.getRemark())
                .eqIfPresent(InventoryLotNumberDO::getVersion, reqVO.getVersion())
                .betweenIfPresent(InventoryLotNumberDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InventoryLotNumberDO::getId));
    }

    List<InventoryLotNumberDO> selectListByBatchNumber(@Param("items") List<InventorySelectBatchItemDTO> items);

    int updateStockNumber(@Param("items") List<InventoryLotNumberDO> items);

    int updateCampOnNumber(@Param("items") List<InventoryLotNumberDO> items);

    default List<InventoryLotNumberDO> selectList(InventoryLotNumberQueryDTO queryDTO) {
        return selectList(new LambdaQueryWrapperX<InventoryLotNumberDO>()
                .eqIfPresent(InventoryLotNumberDO::getProductPref, queryDTO.getProductPref())
                .eqIfPresent(InventoryLotNumberDO::getLotNo, queryDTO.getLotNo())
                .eqIfPresent(InventoryLotNumberDO::getPositionGuid, queryDTO.getPositionGuid())
        );
    }

}