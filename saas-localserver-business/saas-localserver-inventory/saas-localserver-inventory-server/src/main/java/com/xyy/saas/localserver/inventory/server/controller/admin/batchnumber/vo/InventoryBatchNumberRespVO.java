package com.xyy.saas.localserver.inventory.server.controller.admin.batchnumber.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 商品批次库存 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InventoryBatchNumberRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18487")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "批次号guid", requiredMode = Schema.RequiredMode.REQUIRED, example = "7546")
    @ExcelProperty("批次号guid")
    private String guid;

    @Schema(description = "商品编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编号")
    private String productPref;

    @Schema(description = "批号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("批号")
    private String lotNo;

    @Schema(description = "货位guid", requiredMode = Schema.RequiredMode.REQUIRED, example = "19216")
    @ExcelProperty("货位guid")
    private String positionGuid;

    @Schema(description = "库存数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("库存数量")
    private BigDecimal stockNumber;

    @Schema(description = "出库预占", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("出库预占")
    private BigDecimal campOnNumber;

    @Schema(description = "单据类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("单据类型")
    private Integer billType;

    @Schema(description = "单据编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单据编号")
    private String billNo;

    @Schema(description = "供应商guid", requiredMode = Schema.RequiredMode.REQUIRED, example = "4269")
    @ExcelProperty("供应商guid")
    private String supplierGuid;

    @Schema(description = "入库含税价", requiredMode = Schema.RequiredMode.REQUIRED, example = "31417")
    @ExcelProperty("入库含税价")
    private BigDecimal inTaxPrice;

    @Schema(description = "入库税率", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("入库税率")
    private BigDecimal taxRate;

    @Schema(description = "灭菌批号")
    @ExcelProperty("灭菌批号")
    private String sterilizationBatchNo;

    @Schema(description = "总部guid", requiredMode = Schema.RequiredMode.REQUIRED, example = "19654")
    @ExcelProperty("总部guid")
    private String rootGuid;

    @Schema(description = "入库时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("入库时间")
    private LocalDateTime inTime;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本号")
    private Long version;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}