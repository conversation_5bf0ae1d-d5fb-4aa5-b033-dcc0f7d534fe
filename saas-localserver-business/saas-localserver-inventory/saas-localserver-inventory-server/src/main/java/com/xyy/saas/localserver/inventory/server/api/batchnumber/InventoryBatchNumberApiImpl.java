package com.xyy.saas.localserver.inventory.server.api.batchnumber;

import com.xyy.saas.localserver.inventory.api.batchnumber.InventoryBatchNumberApi;
import com.xyy.saas.localserver.inventory.api.batchnumber.dto.InventoryBatchNumberDTO;
import com.xyy.saas.localserver.inventory.api.batchnumber.dto.InventoryBatchNumberQueryDTO;
import com.xyy.saas.localserver.inventory.server.service.batchnumber.InventoryBatchNumberService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class InventoryBatchNumberApiImpl implements InventoryBatchNumberApi {

    @Resource
    private InventoryBatchNumberService inventoryBatchNumberService;

    @Override
    public List<InventoryBatchNumberDTO> getInventory(InventoryBatchNumberQueryDTO queryDTO) {
        return inventoryBatchNumberService.getInventoryBatchNumberList(queryDTO);
    }

}
