package com.xyy.saas.localserver.inventory.server.controller.admin.bill.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 库存单据详情分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InventoryBillDetailPageReqVO extends PageParam {

    @Schema(description = "单据编号")
    private String billNo;

    @Schema(description = "商品编号")
    private String productPref;

    @Schema(description = "批号")
    private String lotNo;

    @Schema(description = "货位guid", example = "3079")
    private String positionGuid;

    @Schema(description = "登记数量")
    private BigDecimal registerQuantity;

    @Schema(description = "变动数量")
    private BigDecimal changeQuantity;

    @Schema(description = "变动前数量")
    private BigDecimal beforeQuantity;

    @Schema(description = "实际数量")
    private BigDecimal actualQuantity;

    @Schema(description = "成本价", example = "4550")
    private BigDecimal costPrice;

    @Schema(description = "零售价", example = "32052")
    private BigDecimal retailPrice;

    @Schema(description = "库存金额")
    private BigDecimal stockAmount;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "扩展信息")
    private String ext;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}