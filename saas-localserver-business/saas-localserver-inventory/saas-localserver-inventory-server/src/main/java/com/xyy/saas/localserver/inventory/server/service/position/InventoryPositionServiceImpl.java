package com.xyy.saas.localserver.inventory.server.service.position;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.localserver.inventory.api.constant.InventoryConstant;
import com.xyy.saas.localserver.inventory.api.position.dto.InventoryPositionCountDTO;
import com.xyy.saas.localserver.inventory.api.position.dto.InventoryPositionDTO;
import com.xyy.saas.localserver.inventory.api.position.dto.InventoryPositionQueryDTO;
import com.xyy.saas.localserver.inventory.api.position.dto.InventoryPositionTreeDTO;
import com.xyy.saas.localserver.inventory.enums.AreaTypeEnum;
import com.xyy.saas.localserver.inventory.server.controller.admin.position.vo.InventoryPositionPageReqVO;
import com.xyy.saas.localserver.inventory.server.controller.admin.position.vo.InventoryPositionSaveReqVO;
import com.xyy.saas.localserver.inventory.server.convert.position.PositionConvert;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.position.InventoryPositionDO;
import com.xyy.saas.localserver.inventory.server.dal.mysql.position.InventoryPositionMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

import java.util.ArrayList;
import java.util.List;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.inventory.enums.ErrorCodeConstants.*;

/**
 * 货位 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InventoryPositionServiceImpl implements InventoryPositionService {

    @Resource
    private InventoryPositionMapper inventoryPositionMapper;

    @Override
    public Long createInventoryPosition(InventoryPositionSaveReqVO createReqVO) {
        checkName(createReqVO);
        List<InventoryPositionCountDTO> list = inventoryPositionMapper.getAreaAndPositionNum(createReqVO);
        if(list.size() >= getMaxAreaCount() || list.get(0).getPositionCount() >= getMaxPositionCount()) {
            throw exception(INVENTORY_POSITION_SIZE_ERROR);
        }

        // 插入
        InventoryPositionDO inventoryPosition = BeanUtils.toBean(createReqVO, InventoryPositionDO.class);
        inventoryPositionMapper.insert(inventoryPosition);
        // 返回
        return inventoryPosition.getId();
    }

    @Override
    public void updateInventoryPosition(InventoryPositionSaveReqVO updateReqVO) {
        // 校验存在
        validateInventoryPositionExists(updateReqVO.getId());
        checkName(updateReqVO);
        // 更新
        InventoryPositionDO updateObj = BeanUtils.toBean(updateReqVO, InventoryPositionDO.class);
        inventoryPositionMapper.updateById(updateObj);
    }

    @Override
    public void deleteInventoryPosition(Long id) {
        // 校验存在
        validateInventoryPositionExists(id);
        // 删除
        inventoryPositionMapper.deleteById(id);
    }

    private void validateInventoryPositionExists(Long id) {
        if (inventoryPositionMapper.selectById(id) == null) {
            throw exception(INVENTORY_POSITION_NOT_EXISTS);
        }
    }

    @Override
    public InventoryPositionDO getInventoryPosition(Long id) {
        return inventoryPositionMapper.selectById(id);
    }

    @Override
    public void initInventoryPosition(Long tenantId) {
        List<InventoryPositionDO> list = new ArrayList<>();
        for (AreaTypeEnum value : AreaTypeEnum.values()) {
            list.add(InventoryPositionDO.builder()
                    .tenantId(tenantId)
                    .areaName(value.getName())
                    .areaType(value.getType())
                    .positionName(InventoryConstant.DEFAULT_POSITION_NAME)
                    .systemDefault(true)
                    .build());
        }
        inventoryPositionMapper.insertBatch(list);
    }

    @Override
    public InventoryPositionTreeDTO getInventoryPositionTree(InventoryPositionQueryDTO queryDTO) {
        List<InventoryPositionDO> positionDOList = inventoryPositionMapper.selectList(queryDTO);
        return PositionConvert.INSTANCE.convertToTree(positionDOList);
    }

    @Override
    public PageResult<InventoryPositionDO> getInventoryPositionPage(InventoryPositionPageReqVO pageReqVO) {
        return inventoryPositionMapper.selectPage(pageReqVO);
    }

    @Override
    public List<InventoryPositionDTO> getInventoryPositionList(InventoryPositionQueryDTO queryDTO) {
        List<InventoryPositionDO> positionDOList = inventoryPositionMapper.selectList(queryDTO);
        return PositionConvert.INSTANCE.convertToList(positionDOList);
    }

    @Override
    public List<String> getAreaList(Long tenantId) {
        return inventoryPositionMapper.getAreaList(tenantId);
    }

    private void checkName(InventoryPositionSaveReqVO createReqVO) {
        Integer count = inventoryPositionMapper.checkList(createReqVO);
        if(count > 0) {
            throw exception(INVENTORY_POSITION_NAME_EXISTS);
        }
    }

    private Integer getMaxAreaCount() {
        return 20;
    }

    private Integer getMaxPositionCount() {
        return 50;
    }

}