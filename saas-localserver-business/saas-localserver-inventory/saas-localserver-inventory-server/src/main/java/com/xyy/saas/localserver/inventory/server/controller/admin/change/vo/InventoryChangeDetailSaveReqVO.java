package com.xyy.saas.localserver.inventory.server.controller.admin.change.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 库存变动明细新增/修改 Request VO")
@Data
public class InventoryChangeDetailSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31274")
    private Long id;

    @Schema(description = "单据来源", example = "1")
    private Integer billType;

    @Schema(description = "单据编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "单据编号不能为空")
    private String billNo;

    @Schema(description = "领域单据时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "领域单据时间不能为空")
    private LocalDateTime billTime;

    @Schema(description = "商品编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品编号不能为空")
    private String productPref;

    @Schema(description = "批号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "批号不能为空")
    private String lotNo;

    @Schema(description = "货位guid", requiredMode = Schema.RequiredMode.REQUIRED, example = "26194")
    @NotEmpty(message = "货位guid不能为空")
    private String positionGuid;

    @Schema(description = "批次库存guid", requiredMode = Schema.RequiredMode.REQUIRED, example = "27812")
    @NotEmpty(message = "批次库存guid不能为空")
    private String batchGuid;

    @Schema(description = "供应商编号", example = "28869")
    private String supplierGuid;

    @Schema(description = "入库数量")
    private BigDecimal inNumber;

    @Schema(description = "入库单价", example = "5864")
    private BigDecimal inPrice;

    @Schema(description = "入库总金额")
    private BigDecimal inAmount;

    @Schema(description = "出库数量")
    private BigDecimal outNumber;

    @Schema(description = "出库单价", example = "15400")
    private BigDecimal outPrice;

    @Schema(description = "出库总金额")
    private BigDecimal outAmount;

    @Schema(description = "批次库存结存数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "批次库存结存数量不能为空")
    private BigDecimal batchNumber;

    @Schema(description = "批次库存结存金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "批次库存结存金额不能为空")
    private BigDecimal batchAmount;

    @Schema(description = "商品结存库存数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "商品结存库存数量不能为空")
    private BigDecimal stockNumber;

    @Schema(description = "商品结存库存金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "商品结存库存金额不能为空")
    private BigDecimal stockAmount;

    @Schema(description = "商品成本价", requiredMode = Schema.RequiredMode.REQUIRED, example = "22099")
    @NotNull(message = "商品成本价不能为空")
    private BigDecimal costPrice;

    @Schema(description = "税率")
    private BigDecimal taxRate;

    @Schema(description = "备注", example = "你猜")
    private String remark;

    @Schema(description = "扩展信息")
    private String ext;

    @Schema(description = "总部guid")
    private String rootGuid;

}