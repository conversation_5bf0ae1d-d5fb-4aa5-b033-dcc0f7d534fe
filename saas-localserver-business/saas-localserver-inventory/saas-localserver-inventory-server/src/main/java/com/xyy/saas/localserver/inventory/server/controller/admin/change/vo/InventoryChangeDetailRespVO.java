package com.xyy.saas.localserver.inventory.server.controller.admin.change.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 库存变动明细 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InventoryChangeDetailRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "31274")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "单据来源", example = "1")
    @ExcelProperty("单据来源")
    private Integer billType;

    @Schema(description = "单据编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单据编号")
    private String billNo;

    @Schema(description = "领域单据时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("领域单据时间")
    private LocalDateTime billTime;

    @Schema(description = "商品编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编号")
    private String productPref;

    @Schema(description = "批号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("批号")
    private String lotNo;

    @Schema(description = "货位guid", requiredMode = Schema.RequiredMode.REQUIRED, example = "26194")
    @ExcelProperty("货位guid")
    private String positionGuid;

    @Schema(description = "批次库存guid", requiredMode = Schema.RequiredMode.REQUIRED, example = "27812")
    @ExcelProperty("批次库存guid")
    private String batchGuid;

    @Schema(description = "供应商编号", example = "28869")
    @ExcelProperty("供应商编号")
    private String supplierGuid;

    @Schema(description = "入库数量")
    @ExcelProperty("入库数量")
    private BigDecimal inNumber;

    @Schema(description = "入库单价", example = "5864")
    @ExcelProperty("入库单价")
    private BigDecimal inPrice;

    @Schema(description = "入库总金额")
    @ExcelProperty("入库总金额")
    private BigDecimal inAmount;

    @Schema(description = "出库数量")
    @ExcelProperty("出库数量")
    private BigDecimal outNumber;

    @Schema(description = "出库单价", example = "15400")
    @ExcelProperty("出库单价")
    private BigDecimal outPrice;

    @Schema(description = "出库总金额")
    @ExcelProperty("出库总金额")
    private BigDecimal outAmount;

    @Schema(description = "批次库存结存数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("批次库存结存数量")
    private BigDecimal batchNumber;

    @Schema(description = "批次库存结存金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("批次库存结存金额")
    private BigDecimal batchAmount;

    @Schema(description = "商品结存库存数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品结存库存数量")
    private BigDecimal stockNumber;

    @Schema(description = "商品结存库存金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品结存库存金额")
    private BigDecimal stockAmount;

    @Schema(description = "商品成本价", requiredMode = Schema.RequiredMode.REQUIRED, example = "22099")
    @ExcelProperty("商品成本价")
    private BigDecimal costPrice;

    @Schema(description = "税率")
    @ExcelProperty("税率")
    private BigDecimal taxRate;

    @Schema(description = "备注", example = "你猜")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "扩展信息")
    @ExcelProperty("扩展信息")
    private String ext;

    @Schema(description = "总部guid")
    private String rootGuid;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}