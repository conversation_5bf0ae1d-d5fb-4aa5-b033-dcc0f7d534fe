package com.xyy.saas.localserver.inventory.server.controller.admin.lotnumber.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 商品批号库存分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InventoryLotNumberPageReqVO extends PageParam {

    @Schema(description = "商品编号")
    private String productPref;

    @Schema(description = "批号")
    private String lotNo;

    @Schema(description = "货位guid", example = "22319")
    private String positionGuid;

    @Schema(description = "生产日期")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] productionDate;

    @Schema(description = "到期时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] expiryDate;

    @Schema(description = "库存数量")
    private BigDecimal stockNumber;

    @Schema(description = "预占数量")
    private BigDecimal campOnNumber;

    @Schema(description = "成本均价", example = "21070")
    private BigDecimal costPrice;

    @Schema(description = "库存总金额")
    private BigDecimal stockAmount;

    @Schema(description = "是否停售: 0--未停售 1--已停售")
    private Boolean stopSale;

    @Schema(description = "最后一次采购入库价", example = "10001")
    private BigDecimal lastInPrice;

    @Schema(description = "最后入库时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] lastInTime;

    @Schema(description = "最后一次供应商", example = "17901")
    private String lastSupplierGuid;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "版本号")
    private Long version;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}