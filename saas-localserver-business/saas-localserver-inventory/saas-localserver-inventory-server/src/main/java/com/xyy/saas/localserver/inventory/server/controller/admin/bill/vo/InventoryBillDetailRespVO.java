package com.xyy.saas.localserver.inventory.server.controller.admin.bill.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 库存单据详情 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InventoryBillDetailRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13371")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "单据编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单据编号")
    private String billNo;

    @Schema(description = "商品编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编号")
    private String productPref;

    @Schema(description = "批号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("批号")
    private String lotNo;

    @Schema(description = "货位guid", requiredMode = Schema.RequiredMode.REQUIRED, example = "3079")
    @ExcelProperty("货位guid")
    private String positionGuid;

    @Schema(description = "登记数量")
    @ExcelProperty("登记数量")
    private BigDecimal registerQuantity;

    @Schema(description = "变动数量")
    @ExcelProperty("变动数量")
    private BigDecimal changeQuantity;

    @Schema(description = "变动前数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("变动前数量")
    private BigDecimal beforeQuantity;

    @Schema(description = "实际数量")
    @ExcelProperty("实际数量")
    private BigDecimal actualQuantity;

    @Schema(description = "成本价", example = "4550")
    @ExcelProperty("成本价")
    private BigDecimal costPrice;

    @Schema(description = "零售价", example = "32052")
    @ExcelProperty("零售价")
    private BigDecimal retailPrice;

    @Schema(description = "库存金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("库存金额")
    private BigDecimal stockAmount;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "扩展信息")
    @ExcelProperty("扩展信息")
    private String ext;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}