package com.xyy.saas.localserver.inventory.server.dal.dataobject.bill;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 库存单据 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inventory_bill")
@KeySequence("saas_inventory_bill_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryBillDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 单据类型：101-货位移动 102-盘点 103-报损报溢 104-库存拆零 105-质量复查
     */
    private Integer billType;
    /**
     * 单据编号
     */
    private String billNo;
    /**
     * 来源类型
     */
    private Integer sourceType;
    /**
     * 来源编号
     */
    private String sourceNo;
    /**
     * 来源描述
     */
    private String sourceDescription;
    /**
     * 审批状态: 1-暂存 2-待检验 3-审批中 5-已驳回 6-已完成
     */
    private Integer status;
    /**
     * 操作员
     */
    private String operator;
    /**
     * 操作时间
     */
    private LocalDateTime operateTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 扩展信息
     */
    private String ext;
    /**
     * 版本号
     */
    private Long version;

}