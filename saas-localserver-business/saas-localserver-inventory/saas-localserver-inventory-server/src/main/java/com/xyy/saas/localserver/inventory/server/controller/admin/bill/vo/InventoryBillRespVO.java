package com.xyy.saas.localserver.inventory.server.controller.admin.bill.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 库存单据 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InventoryBillRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "27254")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "单据类型：101-货位移动 102-盘点 103-报损报溢 104-库存拆零 105-质量复查", requiredMode = Schema.RequiredMode.REQUIRED, example = "101")
    @ExcelProperty("单据类型：101-货位移动 102-盘点 103-报损报溢 104-库存拆零 105-质量复查")
    private Integer billType;

    @Schema(description = "单据编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("单据编号")
    private String billNo;

    @Schema(description = "来源类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("来源类型")
    private Integer sourceType;

    @Schema(description = "来源编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("来源编号")
    private String sourceNo;

    @Schema(description = "来源描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @ExcelProperty("来源描述")
    private String sourceDescription;

    @Schema(description = "审批状态: 1-暂存 2-待检验 3-审批中 5-已驳回 6-已完成", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @ExcelProperty("审批状态: 1-暂存 2-待检验 3-审批中 5-已驳回 6-已完成")
    private Integer status;

    @Schema(description = "操作员", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("操作员")
    private String operator;

    @Schema(description = "操作时间")
    @ExcelProperty("操作时间")
    private LocalDateTime operateTime;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "扩展信息")
    @ExcelProperty("扩展信息")
    private String ext;

    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本号")
    private Long version;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}