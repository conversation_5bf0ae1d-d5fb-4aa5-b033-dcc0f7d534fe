package com.xyy.saas.localserver.inventory.server.controller.admin.tracecode;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import com.xyy.saas.localserver.inventory.server.controller.admin.tracecode.vo.*;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.tracecode.TraceCodeDO;
import com.xyy.saas.localserver.inventory.server.service.tracecode.InventoryTraceCodeService;

@Tag(name = "管理后台 - 追溯码")
@RestController
@RequestMapping("/saas/inventory-trace-code")
@Validated
public class InventoryTraceCodeController {

    @Resource
    private InventoryTraceCodeService inventoryTraceCodeService;

    @PostMapping("/create")
    @Operation(summary = "创建追溯码")
    @PreAuthorize("@ss.hasPermission('saas:inventory-trace-code:create')")
    public CommonResult<Long> createInventoryTraceCode(@Valid @RequestBody TraceCodeSaveReqVO createReqVO) {
        return success(inventoryTraceCodeService.createInventoryTraceCode(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新追溯码")
    @PreAuthorize("@ss.hasPermission('saas:inventory-trace-code:update')")
    public CommonResult<Boolean> updateInventoryTraceCode(@Valid @RequestBody TraceCodeSaveReqVO updateReqVO) {
        inventoryTraceCodeService.updateInventoryTraceCode(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除追溯码")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:inventory-trace-code:delete')")
    public CommonResult<Boolean> deleteInventoryTraceCode(@RequestParam("id") Long id) {
        inventoryTraceCodeService.deleteInventoryTraceCode(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得追溯码")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:inventory-trace-code:query')")
    public CommonResult<TraceCodeRespVO> getInventoryTraceCode(@RequestParam("id") Long id) {
        TraceCodeDO inventoryTraceCode = inventoryTraceCodeService.getInventoryTraceCode(id);
        return success(BeanUtils.toBean(inventoryTraceCode, TraceCodeRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得追溯码分页")
    @PreAuthorize("@ss.hasPermission('saas:inventory-trace-code:query')")
    public CommonResult<PageResult<TraceCodeRespVO>> getInventoryTraceCodePage(@Valid TraceCodePageReqVO pageReqVO) {
        PageResult<TraceCodeDO> pageResult = inventoryTraceCodeService.getInventoryTraceCodePage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, TraceCodeRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出追溯码 Excel")
    @PreAuthorize("@ss.hasPermission('saas:inventory-trace-code:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInventoryTraceCodeExcel(@Valid TraceCodePageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<TraceCodeDO> list = inventoryTraceCodeService.getInventoryTraceCodePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "追溯码.xls", "数据", TraceCodeRespVO.class,
                        BeanUtils.toBean(list, TraceCodeRespVO.class));
    }

}