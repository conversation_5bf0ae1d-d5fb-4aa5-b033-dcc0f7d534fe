package com.xyy.saas.localserver.inventory.server.dal.mysql.campon;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xyy.saas.localserver.inventory.api.campon.dto.InventorySimpleReleaseDTO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.campon.InventoryCampOnBillDO;
import org.apache.ibatis.annotations.Mapper;
import com.xyy.saas.localserver.inventory.server.controller.admin.campon.vo.*;

/**
 * 预占单 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InventoryCampOnBillMapper extends BaseMapperX<InventoryCampOnBillDO> {

    default PageResult<InventoryCampOnBillDO> selectPage(InventoryCampOnBillPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InventoryCampOnBillDO>()
                .eqIfPresent(InventoryCampOnBillDO::getBillNo, reqVO.getBillNo())
                .eqIfPresent(InventoryCampOnBillDO::getSourceType, reqVO.getSourceType())
                .eqIfPresent(InventoryCampOnBillDO::getSourceNo, reqVO.getSourceNo())
                .eqIfPresent(InventoryCampOnBillDO::getParentNo, reqVO.getParentNo())
                .eqIfPresent(InventoryCampOnBillDO::getStatus, reqVO.getStatus())
                .eqIfPresent(InventoryCampOnBillDO::getRemark, reqVO.getRemark())
                .eqIfPresent(InventoryCampOnBillDO::getVersion, reqVO.getVersion())
                .betweenIfPresent(InventoryCampOnBillDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InventoryCampOnBillDO::getId));
    }

    default void updateStatus(InventoryCampOnBillDO campOnBillDO) {
        update(new LambdaUpdateWrapper<InventoryCampOnBillDO>()
                .set(InventoryCampOnBillDO::getStatus, campOnBillDO.getStatus())
                .set(InventoryCampOnBillDO::getVersion, campOnBillDO.getVersion() + 1)
                .eq(InventoryCampOnBillDO::getId, campOnBillDO.getId()));
    }

    default InventoryCampOnBillDO selectByBillNo(String campOnBillNo) {
        return selectOne(InventoryCampOnBillDO::getBillNo, campOnBillNo);
    }

    default InventoryCampOnBillDO selectOne(InventorySimpleReleaseDTO simpleReleaseDTO) {
        return selectOne(new LambdaQueryWrapperX<InventoryCampOnBillDO>()
                .eq(InventoryCampOnBillDO::getTenantId, simpleReleaseDTO.getTenantId())
                .eqIfPresent(InventoryCampOnBillDO::getBillNo, simpleReleaseDTO.getCampOnNo())
                .eqIfPresent(InventoryCampOnBillDO::getSourceNo, simpleReleaseDTO.getSourceNo())
        );
    }
}