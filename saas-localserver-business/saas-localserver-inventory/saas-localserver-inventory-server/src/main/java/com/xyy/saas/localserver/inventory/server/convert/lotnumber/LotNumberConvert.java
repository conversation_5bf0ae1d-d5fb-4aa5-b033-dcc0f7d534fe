package com.xyy.saas.localserver.inventory.server.convert.lotnumber;

import com.xyy.saas.localserver.inventory.api.lotnumber.dto.InventoryLotNumberDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySelectBatchItemDTO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.lotnumber.InventoryLotNumberDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface LotNumberConvert {

    LotNumberConvert INSTANCE = Mappers.getMapper(LotNumberConvert.class);

    InventoryLotNumberDO convert(InventorySelectBatchItemDTO batchItemDTO);

    List<InventoryLotNumberDTO> convert(List<InventoryLotNumberDO> list);

}
