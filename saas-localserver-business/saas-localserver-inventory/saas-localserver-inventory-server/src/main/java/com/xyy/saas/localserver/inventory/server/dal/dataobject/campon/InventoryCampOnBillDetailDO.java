package com.xyy.saas.localserver.inventory.server.dal.dataobject.campon;

import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 预占单详情 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inventory_camp_on_bill_detail")
@KeySequence("saas_inventory_camp_on_bill_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryCampOnBillDetailDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 预占单编号
     */
    private String billNo;
    /**
     * 商品编号
     */
    private String productPref;
    /**
     * 批号
     */
    private String lotNo;
    /**
     * 货位guid
     */
    private String positionGuid;
    /**
     * 批次库存guid
     */
    private String batchGuid;
    /**
     * 追踪维度
     */
    private String tracePref;
    /**
     * 预占数量
     */
    private BigDecimal campOnNumber;
    /**
     * 释放数量
     */
    private BigDecimal releaseNumber;
    /**
     * 总预占数量
     */
    private BigDecimal totalCampOnNumber;
    /**
     * 备注
     */
    private String remark;
    /**
     * 扩展信息
     */
    private String ext;

}