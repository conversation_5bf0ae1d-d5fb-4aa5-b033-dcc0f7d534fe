package com.xyy.saas.localserver.inventory.server.controller.admin.position.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.math.BigDecimal;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 货位分页 Request VO")
@Data
@ToString(callSuper = true)
public class InventoryPositionListReqVO {

    @Schema(description = "编号", example = "21525")
    private String guid;

    @Schema(description = "货区名称", example = "张三")
    private String areaName;

    @Schema(description = "货区类型：1-合格，2-不合格，3-待验区/待处理，4-退货区/待退货", example = "2")
    private Integer areaType;

    @Schema(description = "货位名称", example = "张三")
    private String positionName;

    @Schema(description = "存储条件：1--常温,2--阴凉,3--冷藏,4--其他")
    private Integer positionCondition;

    @Schema(description = "最低温度")
    private BigDecimal temperatureMin;

    @Schema(description = "最高温度")
    private BigDecimal temperatureMax;

    @Schema(description = "最低湿度")
    private BigDecimal humidityMin;

    @Schema(description = "最高湿度")
    private BigDecimal humidityMax;

    @Schema(description = "启用状态: 0--不禁用 1--禁用")
    private Boolean disable;

    @Schema(description = "是否默认：0--非默认 1--默认")
    private Boolean systemDefault;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "版本号")
    private Long version;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}