package com.xyy.saas.localserver.inventory.server.service.stock;

import com.xyy.saas.localserver.inventory.api.stock.dto.*;

import java.util.List;

/**
 * 库存选取服务接口，用于根据条件选取合适的库存批次。
 */
public interface InventorySelectService {

    /**
     * 根据库存操作需求选取合适的库存批次列表。
     *
     * @param inventorySelect 选取条件 DTO，包含货品、批号、仓库等信息
     * @return 返回匹配的库存批次列表
     */
    List<InventorySelectBatchItemDTO> select(InventorySelectDTO inventorySelect);

}
