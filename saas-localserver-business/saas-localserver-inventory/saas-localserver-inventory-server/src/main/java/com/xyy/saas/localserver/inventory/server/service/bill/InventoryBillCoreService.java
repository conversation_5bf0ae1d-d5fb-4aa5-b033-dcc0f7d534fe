package com.xyy.saas.localserver.inventory.server.service.bill;

import com.xyy.saas.localserver.inventory.api.bill.dto.InventoryBillDTO;
import com.xyy.saas.localserver.inventory.server.controller.admin.bill.vo.InventoryBillSaveReqVO;
import jakarta.validation.Valid;

public interface InventoryBillCoreService {

    /**
     * 保存库存单据
     *
     * @param saveReqVO 保存信息
     * @return 编号
     */
    InventoryBillDTO saveInventoryBill(@Valid InventoryBillSaveReqVO saveReqVO);

    /**
     * 执行货位移动单据
     *
     * @param inventoryBill
     */
    InventoryBillDTO executePositionMoveBill(InventoryBillDTO inventoryBill);

    /**
     * 执行盘点单据
     *
     * @param inventoryBill
     */
    InventoryBillDTO executeInventoryPlanBill(InventoryBillDTO inventoryBill);

    /**
     * 执行报损报溢单据
     *
     * @param inventoryBill
     */
    InventoryBillDTO executeProfitLossBill(InventoryBillDTO inventoryBill);

    /**
     * 执行质量复查单据
     *
     * @param inventoryBill
     */
    InventoryBillDTO executeQualityReviewBill(InventoryBillDTO inventoryBill);

    /**
     * 执行库存拆零单据
     *
     * @param inventoryBill
     */
    InventoryBillDTO executeInventoryUnBundledBill(InventoryBillDTO inventoryBill);

}
