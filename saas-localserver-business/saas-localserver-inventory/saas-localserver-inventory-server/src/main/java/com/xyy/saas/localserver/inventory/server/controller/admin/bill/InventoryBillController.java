package com.xyy.saas.localserver.inventory.server.controller.admin.bill;

import org.springframework.web.bind.annotation.*;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.security.access.prepost.PreAuthorize;
import io.swagger.v3.oas.annotations.tags.Tag;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.Operation;

import jakarta.validation.*;
import jakarta.servlet.http.*;
import java.util.*;
import java.io.IOException;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.*;

import com.xyy.saas.localserver.inventory.server.controller.admin.bill.vo.*;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.bill.InventoryBillDO;
import com.xyy.saas.localserver.inventory.server.service.bill.InventoryBillService;

@Tag(name = "管理后台 - 库存单据")
@RestController
@RequestMapping("/saas/inventory/bill")
@Validated
public class InventoryBillController {

    @Resource
    private InventoryBillService inventoryBillService;

    @PostMapping("/save")
    @Operation(summary = "创建库存单据")
    @PreAuthorize("@ss.hasPermission('saas:inventory:bill:save')")
    public CommonResult<Long> saveInventoryBill(@Valid @RequestBody InventoryBillSaveReqVO saveReqVO) {
//        return success(ServiceAdapter.getBillBusinessService(saveReqVO.getBusinessType()).saveInventoryBill(saveReqVO));
        return null;
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除库存单据")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:inventory:bill:delete')")
    public CommonResult<Boolean> deleteInventoryBill(@RequestParam("id") Long id) {
        inventoryBillService.deleteInventoryBill(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得库存单据")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:inventory:bill:query')")
    public CommonResult<InventoryBillRespVO> getInventoryBill(@RequestParam("id") Long id) {
        InventoryBillDO inventoryBill = inventoryBillService.getInventoryBill(id);
        return success(BeanUtils.toBean(inventoryBill, InventoryBillRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得库存单据分页")
    @PreAuthorize("@ss.hasPermission('saas:inventory:bill:query')")
    public CommonResult<PageResult<InventoryBillRespVO>> getInventoryBillPage(@Valid InventoryBillPageReqVO pageReqVO) {
        PageResult<InventoryBillDO> pageResult = inventoryBillService.getInventoryBillPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InventoryBillRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出库存单据 Excel")
    @PreAuthorize("@ss.hasPermission('saas:inventory:bill:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInventoryBillExcel(@Valid InventoryBillPageReqVO pageReqVO,
              HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InventoryBillDO> list = inventoryBillService.getInventoryBillPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "库存单据.xls", "数据", InventoryBillRespVO.class,
                        BeanUtils.toBean(list, InventoryBillRespVO.class));
    }

}