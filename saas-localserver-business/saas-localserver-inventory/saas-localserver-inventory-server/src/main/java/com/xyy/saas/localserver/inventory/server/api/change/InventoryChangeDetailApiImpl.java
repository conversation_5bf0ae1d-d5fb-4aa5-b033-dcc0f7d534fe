package com.xyy.saas.localserver.inventory.server.api.change;

import com.xyy.saas.localserver.inventory.api.change.InventoryChangeDetailApi;
import com.xyy.saas.localserver.inventory.api.change.dto.InventoryChangeDetailDTO;
import com.xyy.saas.localserver.inventory.api.change.dto.InventoryChangeDetailQueryDTO;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class InventoryChangeDetailApiImpl implements InventoryChangeDetailApi {

    @Override
    public List<InventoryChangeDetailDTO> getChangeDetail(InventoryChangeDetailQueryDTO dto) {
        return List.of();
    }

}
