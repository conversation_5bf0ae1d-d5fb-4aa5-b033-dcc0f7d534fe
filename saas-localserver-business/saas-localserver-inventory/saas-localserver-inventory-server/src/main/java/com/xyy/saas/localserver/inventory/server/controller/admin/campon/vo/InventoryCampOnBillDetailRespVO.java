package com.xyy.saas.localserver.inventory.server.controller.admin.campon.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import com.alibaba.excel.annotation.*;

@Schema(description = "管理后台 - 预占单详情 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InventoryCampOnBillDetailRespVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "29804")
    @ExcelProperty("主键id")
    private Long id;

    @Schema(description = "预占单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("预占单编号")
    private String billNo;

    @Schema(description = "商品编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编号")
    private String productPref;

    @Schema(description = "批号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("批号")
    private String lotNo;

    @Schema(description = "货位guid", requiredMode = Schema.RequiredMode.REQUIRED, example = "18617")
    @ExcelProperty("货位guid")
    private String positionGuid;

    @Schema(description = "批次库存guid", requiredMode = Schema.RequiredMode.REQUIRED, example = "17898")
    @ExcelProperty("批次库存guid")
    private String batchGuid;

    @Schema(description = "预占数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("预占数量")
    private BigDecimal campOnNumber;

    @Schema(description = "释放数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("释放数量")
    private BigDecimal releaseNumber;

    @Schema(description = "总预占数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("总预占数量")
    private BigDecimal totalCampOnNumber;

    @Schema(description = "备注", example = "你说的对")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}