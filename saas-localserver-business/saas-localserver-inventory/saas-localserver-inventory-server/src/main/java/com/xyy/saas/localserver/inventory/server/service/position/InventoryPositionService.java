package com.xyy.saas.localserver.inventory.server.service.position;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.localserver.inventory.api.position.dto.InventoryPositionDTO;
import com.xyy.saas.localserver.inventory.api.position.dto.InventoryPositionQueryDTO;
import com.xyy.saas.localserver.inventory.api.position.dto.InventoryPositionTreeDTO;
import com.xyy.saas.localserver.inventory.server.controller.admin.position.vo.InventoryPositionPageReqVO;
import com.xyy.saas.localserver.inventory.server.controller.admin.position.vo.InventoryPositionSaveReqVO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.position.InventoryPositionDO;
import jakarta.validation.Valid;

import java.util.List;

/**
 * 货位 Service 接口
 *
 * <AUTHOR>
 */
public interface InventoryPositionService {

    /**
     * 创建货位
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInventoryPosition(@Valid InventoryPositionSaveReqVO createReqVO);

    /**
     * 更新货位
     *
     * @param updateReqVO 更新信息
     */
    void updateInventoryPosition(@Valid InventoryPositionSaveReqVO updateReqVO);

    /**
     * 删除货位
     *
     * @param id 编号
     */
    void deleteInventoryPosition(Long id);

    /**
     * 获得货位
     *
     * @param id 编号
     * @return 货位
     */
    InventoryPositionDO getInventoryPosition(Long id);

    /**
     * 获取货位分页
     *
     * @param pageReqVO 分页查询
     * @return 货位分页
     */
    PageResult<InventoryPositionDO> getInventoryPositionPage(InventoryPositionPageReqVO pageReqVO);

    /**
     * 获取货位列表
     *
     * @param queryDTO 列表查询
     * @return 货位列表
     */
    List<InventoryPositionDTO> getInventoryPositionList(InventoryPositionQueryDTO queryDTO);

    /**
     * 获取货区列表
     *
     * @param tenantId
     * @return
     */
    List<String> getAreaList(Long tenantId);

    /**
     * 初始化货位
     *
     * @param tenantId
     */
    void initInventoryPosition(Long tenantId);

    /**
     * 获取货位树形目录
     *
     * @param queryDTO
     * @return
     */
    InventoryPositionTreeDTO getInventoryPositionTree(InventoryPositionQueryDTO queryDTO);

}