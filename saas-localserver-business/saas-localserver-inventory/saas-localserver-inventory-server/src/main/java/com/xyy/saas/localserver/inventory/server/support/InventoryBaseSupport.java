package com.xyy.saas.localserver.inventory.server.support;

import com.xyy.saas.localserver.inventory.api.change.dto.InventoryChangeDetailDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySelectBatchItemDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventoryStockDTO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.inventory.InventoryDO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.lotnumber.InventoryLotNumberDO;
import com.xyy.saas.localserver.inventory.server.dal.mysql.inventory.InventoryMapper;
import com.xyy.saas.localserver.inventory.server.dal.mysql.lotnumber.InventoryLotNumberMapper;
import com.xyy.saas.localserver.inventory.server.utils.StockUtil;
import jakarta.annotation.Resource;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class InventoryBaseSupport {

    @Resource
    private InventoryMapper inventoryMapper;
    @Resource
    private InventoryLotNumberMapper lotNumberMapper;

    public Tuple2<Map<String, InventoryLotNumberDO>, Map<String, InventoryDO>> getInventoryInfoByBatch(List<InventorySelectBatchItemDTO> batchList) {
        // 获取库存
        List<InventoryLotNumberDO> inventoryLotNumberDOList = lotNumberMapper.selectListByBatchNumber(batchList);
        List<InventoryDO> inventoryDOList = inventoryMapper.selectByProductPref(inventoryLotNumberDOList.stream().map(InventoryLotNumberDO::getProductPref).collect(Collectors.toList()));

        // 创建Map以便快速查找
        Map<String, InventoryLotNumberDO> lotNumberDOMap = inventoryLotNumberDOList.stream()
                .collect(Collectors.toMap(
                        lotNumberDO -> StockUtil.getLotNoKey(lotNumberDO.getProductPref(), lotNumberDO.getLotNo(), lotNumberDO.getPositionGuid()),
                        lotNumberDO -> lotNumberDO
                ));
        Map<String, InventoryDO> inventoryDOMap = inventoryDOList.stream()
                .collect(Collectors.toMap(
                        InventoryDO::getProductPref,
                        inventoryDO -> inventoryDO
                ));

        return Tuples.of(lotNumberDOMap, inventoryDOMap);
    }

    public Tuple2<Map<String, BigDecimal>, Map<String, BigDecimal>> getNumberInfoByBatch(List<InventorySelectBatchItemDTO> batchList) {
        // 分组
        Map<String, BigDecimal> itemMap = batchList.stream()
                .collect(Collectors.groupingBy(
                        item -> StockUtil.getLotNoKey(item),
                        Collectors.reducing(BigDecimal.ZERO, InventorySelectBatchItemDTO::getChangeNumber, BigDecimal::add)
                ));
        Map<String, BigDecimal> productPrefMap = batchList.stream()
                .collect(Collectors.groupingBy(
                        InventorySelectBatchItemDTO::getProductPref,
                        Collectors.reducing(BigDecimal.ZERO, InventorySelectBatchItemDTO::getChangeNumber, BigDecimal::add)
                ));

        return Tuples.of(itemMap, productPrefMap);
    }

    public Tuple2<Map<String, InventoryLotNumberDO>, Map<String, InventoryDO>> updateStockNumber(Tuple2<Map<String, BigDecimal>, Map<String, BigDecimal>> numberTuple,
                                                                                                 Tuple2<Map<String, InventoryLotNumberDO>, Map<String, InventoryDO>> inventoryTuple,
                                                                                                 BigDecimal alpha) {
        for (Map.Entry<String, BigDecimal> entry : numberTuple.getT1().entrySet()) {
            InventoryLotNumberDO lotNumberDO = inventoryTuple.getT1().get(entry.getKey());
            if (lotNumberDO != null) {
                lotNumberDO.setStockNumber(lotNumberDO.getStockNumber().subtract(entry.getValue().multiply(alpha)));
            }
        }
        for (Map.Entry<String, BigDecimal> entry : numberTuple.getT2().entrySet()) {
            InventoryDO inventoryDO = inventoryTuple.getT2().get(entry.getKey());
            if (inventoryDO != null) {
                inventoryDO.setStockNumber(inventoryDO.getStockNumber().subtract(entry.getValue().multiply(alpha)));
            }
        }
        return inventoryTuple;
    }

    public Tuple2<Map<String, InventoryLotNumberDO>, Map<String, InventoryDO>> updateCampOnNumber(Tuple2<Map<String, BigDecimal>, Map<String, BigDecimal>> numberTuple,
                                                                                                 Tuple2<Map<String, InventoryLotNumberDO>, Map<String, InventoryDO>> inventoryTuple,
                                                                                                  boolean updateStockNumber,
                                                                                                 BigDecimal alpha) {
        for (Map.Entry<String, BigDecimal> entry : numberTuple.getT1().entrySet()) {
            InventoryLotNumberDO lotNumberDO = inventoryTuple.getT1().get(entry.getKey());
            if (lotNumberDO != null) {
                lotNumberDO.setCampOnNumber(lotNumberDO.getCampOnNumber().subtract(entry.getValue().multiply(alpha)));
                if(updateStockNumber) {
                    lotNumberDO.setStockNumber(lotNumberDO.getStockNumber().subtract(entry.getValue().multiply(alpha)));
                }
            }
        }
        for (Map.Entry<String, BigDecimal> entry : numberTuple.getT2().entrySet()) {
            InventoryDO inventoryDO = inventoryTuple.getT2().get(entry.getKey());
            if (inventoryDO != null) {
                inventoryDO.setCampOnNumber(inventoryDO.getCampOnNumber().subtract(entry.getValue().multiply(alpha)));
                if(updateStockNumber) {
                    inventoryDO.setStockNumber(inventoryDO.getStockNumber().subtract(entry.getValue().multiply(alpha)));
                }
            }
        }
        return inventoryTuple;
    }

    public Tuple2<Map<String, InventoryLotNumberDO>, Map<String, InventoryDO>> updateCampOnAndStockNumber(Tuple2<Map<String, BigDecimal>, Map<String, BigDecimal>> numberTuple,
                                                                                                 Tuple2<Map<String, InventoryLotNumberDO>, Map<String, InventoryDO>> inventoryTuple,
                                                                                                 BigDecimal alpha) {
        for (Map.Entry<String, BigDecimal> entry : numberTuple.getT1().entrySet()) {
            InventoryLotNumberDO lotNumberDO = inventoryTuple.getT1().get(entry.getKey());
            if (lotNumberDO != null) {
                lotNumberDO.setStockNumber(lotNumberDO.getStockNumber().subtract(entry.getValue().multiply(alpha)));
                lotNumberDO.setCampOnNumber(lotNumberDO.getCampOnNumber().subtract(entry.getValue().multiply(alpha)));
            }
        }
        for (Map.Entry<String, BigDecimal> entry : numberTuple.getT2().entrySet()) {
            InventoryDO inventoryDO = inventoryTuple.getT2().get(entry.getKey());
            if (inventoryDO != null) {
                inventoryDO.setStockNumber(inventoryDO.getStockNumber().subtract(entry.getValue().multiply(alpha)));
                inventoryDO.setCampOnNumber(inventoryDO.getCampOnNumber().subtract(entry.getValue().multiply(alpha)));
            }
        }
        return inventoryTuple;
    }

    public List<InventoryChangeDetailDTO> generateChangeDetailList(InventoryStockDTO inventoryStock, Map<String, InventoryLotNumberDO> lotNumberDOMap, Map<String, InventoryDO> inventoryDOMap, boolean inStock) {
        List<InventoryChangeDetailDTO> list = new ArrayList<>();
        for (InventorySelectBatchItemDTO item : inventoryStock.getBatchList()) {
            InventoryLotNumberDO inventoryLotNumber = lotNumberDOMap.get(StockUtil.getLotNoKey(item));
            InventoryDO inventory = inventoryDOMap.get(item.getProductPref());
            InventoryChangeDetailDTO inventoryChangeDetail = generateChangeDetail(item, inventoryStock, inventoryLotNumber, inventory, inStock);
            list.add(inventoryChangeDetail);
        }
        return list;
    }

    public InventoryChangeDetailDTO generateChangeDetail(InventorySelectBatchItemDTO item, InventoryStockDTO inventoryStock, InventoryLotNumberDO inventoryLotNumber, InventoryDO inventory, boolean inStock) {
        InventoryChangeDetailDTO inventoryChangeDetail = new InventoryChangeDetailDTO();
        BeanUtils.copyProperties(item, inventoryChangeDetail);

        inventoryChangeDetail.setBillType(inventoryStock.getBillType());
        inventoryChangeDetail.setBillNo(inventoryStock.getBillNo());
        inventoryChangeDetail.setBatchNumber(item.getStockNumber());
        inventoryChangeDetail.setBatchAmount(item.getStockNumber().multiply(inventory.getCostPrice()));
        inventoryChangeDetail.setLotNumber(inventoryLotNumber.getStockNumber());
        inventoryChangeDetail.setLotAmount(inventoryLotNumber.getStockAmount());
        inventoryChangeDetail.setStockNumber(inventory.getStockNumber());
        inventoryChangeDetail.setStockAmount(inventory.getStockAmount());
        inventoryChangeDetail.setCostPrice(inventory.getCostPrice());
        inventoryChangeDetail.setBatchGuid(item.getGuid());
        inventoryChangeDetail.setTaxRate(item.getTaxRate());
        // 设置扩展字段等信息
        setExtInfo(item, inventoryStock, inStock, inventoryChangeDetail, inventory);
        return inventoryChangeDetail;
    }

    private void setExtInfo(InventorySelectBatchItemDTO item, InventoryStockDTO inventoryStock, boolean inStock, InventoryChangeDetailDTO inventoryChangeDetail, InventoryDO inventory) {
        BigDecimal price = Optional.ofNullable(item.getCostPrice()).orElse(Optional.ofNullable(item.getInTaxPrice()).orElse(inventory.getCostPrice()));
        if (inStock) {
            inventoryChangeDetail.setInNumber(item.getChangeNumber());
            inventoryChangeDetail.setInPrice(price);
            inventoryChangeDetail.setInAmount(inventoryChangeDetail.getInNumber().multiply(inventoryChangeDetail.getInPrice()));
        } else {
            inventoryChangeDetail.setOutNumber(item.getChangeNumber());
            inventoryChangeDetail.setOutPrice(price);
            inventoryChangeDetail.setOutAmount(inventoryChangeDetail.getOutNumber().multiply(inventoryChangeDetail.getOutPrice()));
        }
        inventoryChangeDetail.setTraceCodes(item.getTraceCodes());
    }

    public String getGuid() {
        return UUID.randomUUID().toString().replace("-", "");
    }

}
