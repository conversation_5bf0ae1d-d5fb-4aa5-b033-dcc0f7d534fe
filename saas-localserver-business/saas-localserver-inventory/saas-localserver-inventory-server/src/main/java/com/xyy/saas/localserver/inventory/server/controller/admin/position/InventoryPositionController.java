package com.xyy.saas.localserver.inventory.server.controller.admin.position;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.localserver.inventory.api.position.dto.InventoryPositionQueryDTO;
import com.xyy.saas.localserver.inventory.api.position.dto.InventoryPositionTreeDTO;
import com.xyy.saas.localserver.inventory.server.controller.admin.position.vo.InventoryPositionPageReqVO;
import com.xyy.saas.localserver.inventory.server.controller.admin.position.vo.InventoryPositionRespVO;
import com.xyy.saas.localserver.inventory.server.controller.admin.position.vo.InventoryPositionSaveReqVO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.position.InventoryPositionDO;
import com.xyy.saas.localserver.inventory.server.service.position.InventoryPositionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

@Tag(name = "管理后台 - 货位")
@RestController
@RequestMapping("/saas/inventory-position")
@Validated
public class InventoryPositionController {

    @Resource
    private InventoryPositionService inventoryPositionService;

    @PostMapping("/create")
    @Operation(summary = "创建货位")
    @PreAuthorize("@ss.hasPermission('saas:inventory-position:create')")
    public CommonResult<Long> createInventoryPosition(@Valid @RequestBody InventoryPositionSaveReqVO createReqVO) {
        return success(inventoryPositionService.createInventoryPosition(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新货位")
    @PreAuthorize("@ss.hasPermission('saas:inventory-position:update')")
    public CommonResult<Boolean> updateInventoryPosition(@Valid @RequestBody InventoryPositionSaveReqVO updateReqVO) {
        inventoryPositionService.updateInventoryPosition(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除货位")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('saas:inventory-position:delete')")
    public CommonResult<Boolean> deleteInventoryPosition(@RequestParam("id") Long id) {
        inventoryPositionService.deleteInventoryPosition(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得货位")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('saas:inventory-position:query')")
    public CommonResult<InventoryPositionRespVO> getInventoryPosition(@RequestParam("id") Long id) {
        InventoryPositionDO inventoryPosition = inventoryPositionService.getInventoryPosition(id);
        return success(BeanUtils.toBean(inventoryPosition, InventoryPositionRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得货位分页")
    @PreAuthorize("@ss.hasPermission('saas:inventory-position:query')")
    public CommonResult<PageResult<InventoryPositionRespVO>> getInventoryPositionPage(@Valid InventoryPositionPageReqVO pageReqVO) {
        PageResult<InventoryPositionDO> pageResult = inventoryPositionService.getInventoryPositionPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InventoryPositionRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出货位 Excel")
    @PreAuthorize("@ss.hasPermission('saas:inventory-position:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInventoryPositionExcel(@Valid InventoryPositionPageReqVO pageReqVO,
                                             HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InventoryPositionDO> list = inventoryPositionService.getInventoryPositionPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "货位.xls", "数据", InventoryPositionRespVO.class,
                BeanUtils.toBean(list, InventoryPositionRespVO.class));
    }

    @GetMapping("/tree")
    @Operation(summary = "获得货位目录")
    @PreAuthorize("@ss.hasPermission('saas:inventory-position:tree')")
    public CommonResult<InventoryPositionTreeDTO> getInventoryPositionTree(@Valid InventoryPositionQueryDTO queryDTO) {
        InventoryPositionTreeDTO result = inventoryPositionService.getInventoryPositionTree(queryDTO);
        return success(result);
    }

}