package com.xyy.saas.localserver.inventory.server.service.bill;

import com.xyy.saas.localserver.inventory.api.bill.dto.InventoryBillDTO;
import com.xyy.saas.localserver.inventory.api.bill.dto.InventoryBillDetailDTO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.bill.InventoryBillDetailDO;
import jakarta.validation.*;
import com.xyy.saas.localserver.inventory.server.controller.admin.bill.vo.*;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.bill.InventoryBillDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import reactor.util.function.Tuple3;

import java.util.List;

/**
 * 库存单据 Service 接口
 *
 * <AUTHOR>
 */
public interface InventoryBillService {

    /**
     * 保存库存单据
     *
     * @param inventoryBill 保存信息
     * @return 编号
     */
    Long saveInventoryBill(@Valid InventoryBillDTO inventoryBill, Tuple3<List<InventoryBillDetailDTO>, List<InventoryBillDetailDTO>, List<InventoryBillDetailDTO>> tuple);

    /**
     * 创建库存单据
     *
     * @param inventoryBill 创建信息
     * @return 编号
     */
    Long createInventoryBill(@Valid InventoryBillDO inventoryBill);

    /**
     * 更新库存单据
     *
     * @param inventoryBill 更新信息
     */
    void updateInventoryBill(@Valid InventoryBillDO inventoryBill);

    /**
     * 删除库存单据
     *
     * @param id 编号
     */
    void deleteInventoryBill(Long id);

    /**
     * 获得库存单据
     *
     * @param id 编号
     * @return 库存单据
     */
    InventoryBillDO getInventoryBill(Long id);

    /**
     * 获得库存单据分页
     *
     * @param pageReqVO 分页查询
     * @return 库存单据分页
     */
    PageResult<InventoryBillDO> getInventoryBillPage(InventoryBillPageReqVO pageReqVO);

    /**
     * 创建库存单据详情
     *
     * @param tuple 创建信息
     * @return 编号
     */
    void saveInventoryBillDetail(@Valid Tuple3<List<InventoryBillDetailDTO>, List<InventoryBillDetailDTO>, List<InventoryBillDetailDTO>> tuple);
    /**
     * 创建库存单据详情
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInventoryBillDetail(@Valid InventoryBillDetailSaveReqVO createReqVO);

    /**
     * 更新库存单据详情
     *
     * @param updateReqVO 更新信息
     */
    void updateInventoryBillDetail(@Valid InventoryBillDetailSaveReqVO updateReqVO);

    /**
     * 删除库存单据详情
     *
     * @param id 编号
     */
    void deleteInventoryBillDetail(Long id);

    /**
     * 获得库存单据详情
     *
     * @param id 编号
     * @return 库存单据详情
     */
    InventoryBillDetailDO getInventoryBillDetail(Long id);

    /**
     * 获得库存单据详情分页
     *
     * @param pageReqVO 分页查询
     * @return 库存单据详情分页
     */
    PageResult<InventoryBillDetailDO> getInventoryBillDetailPage(InventoryBillDetailPageReqVO pageReqVO);

    /**
     * 获得库存单据详情
     *
     * @param billNo
     * @return
     */
    List<InventoryBillDetailDO> getDetailByBillNo(String billNo);

}