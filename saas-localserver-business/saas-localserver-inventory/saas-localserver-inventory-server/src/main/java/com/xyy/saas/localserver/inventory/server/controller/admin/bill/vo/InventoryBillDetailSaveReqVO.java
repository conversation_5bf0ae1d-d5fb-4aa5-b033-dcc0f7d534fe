package com.xyy.saas.localserver.inventory.server.controller.admin.bill.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;

@Schema(description = "管理后台 - 库存单据详情新增/修改 Request VO")
@Data
public class InventoryBillDetailSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "13371")
    private Long id;

    @Schema(description = "单据编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "单据编号不能为空")
    private String billNo;

    @Schema(description = "商品编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品编号不能为空")
    private String productPref;

    @Schema(description = "批号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "批号不能为空")
    private String lotNo;

    @Schema(description = "货位guid", requiredMode = Schema.RequiredMode.REQUIRED, example = "3079")
    @NotEmpty(message = "货位guid不能为空")
    private String positionGuid;

    @Schema(description = "登记数量")
    private BigDecimal registerQuantity;

    @Schema(description = "变动数量")
    private BigDecimal changeQuantity;

    @Schema(description = "变动前数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "变动前数量不能为空")
    private BigDecimal beforeQuantity;

    @Schema(description = "实际数量")
    private BigDecimal actualQuantity;

    @Schema(description = "成本价", example = "4550")
    private BigDecimal costPrice;

    @Schema(description = "零售价", example = "32052")
    private BigDecimal retailPrice;

    @Schema(description = "库存金额", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "库存金额不能为空")
    private BigDecimal stockAmount;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "你说的对")
    @NotEmpty(message = "备注不能为空")
    private String remark;

    @Schema(description = "扩展信息")
    private String ext;

}