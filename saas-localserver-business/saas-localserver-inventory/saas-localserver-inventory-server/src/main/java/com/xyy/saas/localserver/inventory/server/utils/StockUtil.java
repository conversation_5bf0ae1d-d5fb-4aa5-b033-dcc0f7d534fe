package com.xyy.saas.localserver.inventory.server.utils;

import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySelectBatchItemDTO;

public class StockUtil {

    public static String getLotNoKey(InventorySelectBatchItemDTO item) {
        return getLotNoKey(item.getProductPref(), item.getLotNo(), item.getPositionGuid());
    }

    public static String getLotNoKey(String productPref, String lotNo, String positionGuid) {
        return productPref + "_" + lotNo + "_" + positionGuid;
    }

}
