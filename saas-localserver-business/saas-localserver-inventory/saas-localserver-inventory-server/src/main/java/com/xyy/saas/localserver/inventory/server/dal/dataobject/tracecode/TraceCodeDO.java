package com.xyy.saas.localserver.inventory.server.dal.dataobject.tracecode;

import lombok.*;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

import java.math.BigDecimal;

/**
 * 追溯码 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inventory_trace_code")
@KeySequence("saas_inventory_trace_code_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TraceCodeDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 商品编码
     */
    private String productPref;
    /**
     * 批号
     */
    private String lotNo;
    /**
     * 追踪维度
     */
    private String tracePref;
    /**
     * 供应商编码
     */
    private String supplierPref;
    /**
     * 追溯码/UDI码
     */
    private String traceCode;
    /**
     * 入库数量
     */
    private BigDecimal inNumber;
    /**
     * 出库数量
     */
    private BigDecimal outNumber;
    /**
     * 剩余数量
     */
    private BigDecimal remainNumber;
    /**
     * 追溯码状态 1-已收货；2-已入库；3-已占用；4-已销售；5-已销毁；6-已采退；7-已销退
     */
    private Integer status;
    /**
     * 包装规格 1-小包；2-中包；3-大包
     */
    private Integer packageLevel;
    /**
     * 父码
     */
    private String parentCode;
    /**
     * 版本号
     */
    private Long version;

}