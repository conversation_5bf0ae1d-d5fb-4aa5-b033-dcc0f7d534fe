package com.xyy.saas.localserver.inventory.server.dal.dataobject.change;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 库存变动明细 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inventory_change_detail")
@KeySequence("saas_inventory_change_detail_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryChangeDetailDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 单据类型/摘要
     */
    private Integer billType;
    /**
     * 单据编号
     */
    private String billNo;
    /**
     * 领域单据时间
     */
    private LocalDateTime billTime;
    /**
     * 商品编号
     */
    private String productPref;
    /**
     * 批号
     */
    private String lotNo;
    /**
     * 货位guid
     */
    private String positionGuid;
    /**
     * 批次库存guid
     */
    private String batchGuid;
    /**
     * 追踪维度
     */
    private String tracePref;
    /**
     * 来源追踪维度
     */
    private String sourceTracePref;
    /**
     * 总部追踪维度
     */
    private String rootTracePref;
    /**
     * 供应商编号
     */
    private String supplierGuid;
    /**
     * 入库数量
     */
    private BigDecimal inNumber;
    /**
     * 入库单价
     */
    private BigDecimal inPrice;
    /**
     * 入库总金额
     */
    private BigDecimal inAmount;
    /**
     * 出库数量
     */
    private BigDecimal outNumber;
    /**
     * 出库单价
     */
    private BigDecimal outPrice;
    /**
     * 出库总金额
     */
    private BigDecimal outAmount;
    /**
     * 批次库存结存数量
     */
    private BigDecimal batchNumber;
    /**
     * 批次库存结存金额
     */
    private BigDecimal batchAmount;
    /**
     * 批号库存结存数量
     */
    private BigDecimal lotNumber;
    /**
     * 批号库存结存金额
     */
    private BigDecimal lotAmount;
    /**
     * 商品结存库存数量
     */
    private BigDecimal stockNumber;
    /**
     * 商品结存库存金额
     */
    private BigDecimal stockAmount;
    /**
     * 追溯码变动明细二进制
     */
    private Byte[] codeBlob;
    /**
     * 商品成本价
     */
    private BigDecimal costPrice;
    /**
     * 税率
     */
    private BigDecimal taxRate;
    /**
     * 备注
     */
    private String remark;
    /**
     * 入库含税价
     */
    private BigDecimal inTaxPrice;
    /**
     * 灭菌批次号
     */
    private String sterilizationBatchNo;
    /**
     * 入库时间
     */
    private LocalDateTime inTime;
    /**
     * 扩展信息
     */
    private String ext;
    /**
     * 来源guid
     */
    private String sourceGuid;
    /**
     * 总部guid
     */
    private String rootGuid;

}