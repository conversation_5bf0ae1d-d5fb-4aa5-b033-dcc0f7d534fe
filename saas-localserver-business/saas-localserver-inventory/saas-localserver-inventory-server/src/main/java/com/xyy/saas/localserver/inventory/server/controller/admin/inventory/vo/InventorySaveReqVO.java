package com.xyy.saas.localserver.inventory.server.controller.admin.inventory.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商品库存新增/修改 Request VO")
@Data
public class InventorySaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5048")
    private Long id;

    @Schema(description = "商品编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品编号不能为空")
    private String productPref;

    @Schema(description = "库存数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "库存数量不能为空")
    private BigDecimal stockNumber;

    @Schema(description = "成本均价", example = "32481")
    private BigDecimal costPrice;

    @Schema(description = "库存总金额")
    private BigDecimal stockAmount;

    @Schema(description = "最后一次采购入库价", requiredMode = Schema.RequiredMode.REQUIRED, example = "16933")
    @NotNull(message = "最后一次采购入库价不能为空")
    private BigDecimal lastInPrice;

    @Schema(description = "最后一次供应商", example = "24016")
    private String lastSupplierGuid;

    @Schema(description = "最后一次入库时间")
    private LocalDateTime lastInTime;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "库存上限")
    private BigDecimal storeMaxLimit;

    @Schema(description = "库存下限")
    private BigDecimal storeMinLimit;

    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "版本号不能为空")
    private Long version;

}