package com.xyy.saas.localserver.inventory.server.convert.batchnumber;

import com.xyy.saas.localserver.inventory.api.batchnumber.dto.InventoryBatchNumberDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySelectBatchItemDTO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.batchnumber.InventoryBatchNumberDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface BatchNumberConvert {

    BatchNumberConvert INSTANCE = Mappers.getMapper(BatchNumberConvert.class);

    InventorySelectBatchItemDTO convert(InventoryBatchNumberDO inventoryBatchNumberDO);

    List<InventoryBatchNumberDTO> convert(List<InventoryBatchNumberDO> list);
}
