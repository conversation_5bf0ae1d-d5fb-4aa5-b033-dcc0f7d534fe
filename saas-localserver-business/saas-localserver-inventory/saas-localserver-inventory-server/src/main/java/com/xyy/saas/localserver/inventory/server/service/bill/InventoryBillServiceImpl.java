package com.xyy.saas.localserver.inventory.server.service.bill;

import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.localserver.entity.enums.bill.BillTypeEnum;
import com.xyy.saas.localserver.inventory.api.bill.dto.InventoryBillDTO;
import com.xyy.saas.localserver.inventory.api.bill.dto.InventoryBillDetailDTO;
import com.xyy.saas.localserver.inventory.server.convert.bill.BillConvert;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.bill.InventoryBillDetailDO;
import com.xyy.saas.localserver.inventory.server.dal.mysql.bill.InventoryBillDetailMapper;
import jakarta.annotation.PostConstruct;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.transaction.annotation.Transactional;

import com.xyy.saas.localserver.inventory.server.controller.admin.bill.vo.*;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.bill.InventoryBillDO;
import cn.iocoder.yudao.framework.common.pojo.PageResult;

import com.xyy.saas.localserver.inventory.server.dal.mysql.bill.InventoryBillMapper;
import reactor.util.function.Tuple3;

import java.util.List;
import java.util.stream.Collectors;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.localserver.inventory.enums.ErrorCodeConstants.*;

/**
 * 库存单据 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InventoryBillServiceImpl implements InventoryBillService {

    @Resource
    private InventoryBillMapper inventoryBillMapper;
    @Resource
    private InventoryBillDetailMapper inventoryBillDetailMapper;

    @Transactional
    @Override
    public Long saveInventoryBill(InventoryBillDTO inventoryBillDTO, Tuple3<List<InventoryBillDetailDTO>, List<InventoryBillDetailDTO>, List<InventoryBillDetailDTO>> tuple) {
        InventoryBillDO inventoryBill = BillConvert.INSTANCE.convert2DO(inventoryBillDTO);
        if(inventoryBill.getId() == null) {
            // 插入
            inventoryBillMapper.insert(inventoryBill);
        } else {
            // 更新
            inventoryBillMapper.updateById(inventoryBill);
        }
        // 保存详情
        this.saveInventoryBillDetail(tuple);
        // 返回
        return inventoryBill.getId();
    }

    @Override
    public Long createInventoryBill(InventoryBillDO inventoryBill) {
        // 插入
        inventoryBillMapper.insert(inventoryBill);
        // 返回
        return inventoryBill.getId();
    }

    @Override
    public void updateInventoryBill(InventoryBillDO inventoryBill) {
        inventoryBillMapper.updateById(inventoryBill);
    }

    @Override
    public void deleteInventoryBill(Long id) {
        // 校验存在
        validateInventoryBillExists(id);
        // 删除
        inventoryBillMapper.deleteById(id);
    }

    private void validateInventoryBillExists(Long id) {
        if (inventoryBillMapper.selectById(id) == null) {
            throw exception(INVENTORY_BILL_NOT_EXISTS);
        }
    }

    @Override
    public InventoryBillDO getInventoryBill(Long id) {
        return inventoryBillMapper.selectById(id);
    }

    @Override
    public PageResult<InventoryBillDO> getInventoryBillPage(InventoryBillPageReqVO pageReqVO) {
        return inventoryBillMapper.selectPage(pageReqVO);
    }

    @Override
    public void saveInventoryBillDetail(Tuple3<List<InventoryBillDetailDTO>, List<InventoryBillDetailDTO>, List<InventoryBillDetailDTO>> tuple) {
        if(!CollectionUtils.isAnyEmpty(tuple.getT1())) {
            // 批量插入
            inventoryBillDetailMapper.insertBatch(BillConvert.INSTANCE.convert(tuple.getT1()));
        }
        if(!CollectionUtils.isAnyEmpty(tuple.getT2())) {
            // 批量更新
            inventoryBillDetailMapper.updateBatch(BillConvert.INSTANCE.convert(tuple.getT2()));
        }
        if(!CollectionUtils.isAnyEmpty(tuple.getT3())) {
            // 批量删除
            inventoryBillDetailMapper.deleteBatchIds(tuple.getT3().stream().map(InventoryBillDetailDTO::getId).collect(Collectors.toList()));
        }
    }

    @Override
    public Long createInventoryBillDetail(InventoryBillDetailSaveReqVO createReqVO) {
        // 插入
        InventoryBillDetailDO inventoryBillDetail = BeanUtils.toBean(createReqVO, InventoryBillDetailDO.class);
        inventoryBillDetailMapper.insert(inventoryBillDetail);
        // 返回
        return inventoryBillDetail.getId();
    }

    @Override
    public void updateInventoryBillDetail(InventoryBillDetailSaveReqVO updateReqVO) {
        // 校验存在
        validateInventoryBillDetailExists(updateReqVO.getId());
        // 更新
        InventoryBillDetailDO updateObj = BeanUtils.toBean(updateReqVO, InventoryBillDetailDO.class);
        inventoryBillDetailMapper.updateById(updateObj);
    }

    @Override
    public void deleteInventoryBillDetail(Long id) {
        // 校验存在
        validateInventoryBillDetailExists(id);
        // 删除
        inventoryBillDetailMapper.deleteById(id);
    }

    private void validateInventoryBillDetailExists(Long id) {
        if (inventoryBillDetailMapper.selectById(id) == null) {
            throw exception(INVENTORY_BILL_DETAIL_NOT_EXISTS);
        }
    }

    @Override
    public InventoryBillDetailDO getInventoryBillDetail(Long id) {
        return inventoryBillDetailMapper.selectById(id);
    }

    @Override
    public PageResult<InventoryBillDetailDO> getInventoryBillDetailPage(InventoryBillDetailPageReqVO pageReqVO) {
        return inventoryBillDetailMapper.selectPage(pageReqVO);
    }

    @Override
    public List<InventoryBillDetailDO> getDetailByBillNo(String billNo) {
        return inventoryBillDetailMapper.getDetailByBillNo(billNo);
    }

}