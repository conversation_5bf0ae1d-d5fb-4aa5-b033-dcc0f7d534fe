package com.xyy.saas.localserver.inventory.server.api.campon;

import com.xyy.saas.localserver.inventory.api.campon.InventoryCampOnApi;
import com.xyy.saas.localserver.inventory.api.campon.dto.*;
import com.xyy.saas.localserver.inventory.api.change.dto.InventoryChangeDetailDTO;
import com.xyy.saas.localserver.inventory.server.service.campon.InventoryCampOnService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class InventoryCampOnApiImpl implements InventoryCampOnApi {

    @Resource
    private InventoryCampOnService inventoryCampOnService;

    @Override
    public InventoryCampOnResultDTO campOn(InventoryCampOnDTO inventoryCampOn) {
        return inventoryCampOnService.campOn(inventoryCampOn);
    }

    @Override
    public List<InventoryCampOnResultDTO> batchCampOn(List<InventoryCampOnDTO> inventoryCampOnList) {
        return inventoryCampOnService.batchCampOn(inventoryCampOnList);
    }

    @Override
    public InventoryCampOnResultDTO releaseCampOn(InventorySimpleReleaseDTO inventorySimpleRelease) {
        return inventoryCampOnService.releaseCampOn(inventorySimpleRelease);
    }

    @Override
    public List<InventoryChangeDetailDTO> releaseStock(InventorySimpleReleaseDTO inventorySimpleRelease) {
        return inventoryCampOnService.releaseStock(inventorySimpleRelease);
    }

    @Override
    public List<InventoryChangeDetailDTO> releaseStock(InventoryReleaseDTO inventoryRelease) {
        return inventoryCampOnService.releaseStock(inventoryRelease);
    }

    @Override
    public InventoryCampOnResultDTO queryCampOnBill(InventoryCampOnQueryDTO inventoryCampOnQuery) {
        return inventoryCampOnService.queryCampOnBill(inventoryCampOnQuery);
    }
}
