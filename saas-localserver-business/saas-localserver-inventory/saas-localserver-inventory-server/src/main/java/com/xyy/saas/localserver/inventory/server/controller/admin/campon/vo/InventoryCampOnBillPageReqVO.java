package com.xyy.saas.localserver.inventory.server.controller.admin.campon.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 预占单分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InventoryCampOnBillPageReqVO extends PageParam {

    @Schema(description = "编号")
    private String billNo;

    @Schema(description = "来源类型", example = "2")
    private Integer sourceType;

    @Schema(description = "来源单号")
    private String sourceNo;

    @Schema(description = "上级预占单编号")
    private String parentNo;

    @Schema(description = "状态: 1:预占中; 2:预占释放", example = "2")
    private Integer status;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "版本号")
    private Long version;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}