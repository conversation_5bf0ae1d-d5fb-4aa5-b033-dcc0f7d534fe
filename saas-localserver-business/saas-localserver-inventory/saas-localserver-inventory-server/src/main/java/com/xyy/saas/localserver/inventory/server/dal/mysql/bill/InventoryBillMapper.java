package com.xyy.saas.localserver.inventory.server.dal.mysql.bill;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.bill.InventoryBillDO;
import org.apache.ibatis.annotations.Mapper;
import com.xyy.saas.localserver.inventory.server.controller.admin.bill.vo.*;

/**
 * 库存单据 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InventoryBillMapper extends BaseMapperX<InventoryBillDO> {

    default PageResult<InventoryBillDO> selectPage(InventoryBillPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InventoryBillDO>()
                .eqIfPresent(InventoryBillDO::getBillType, reqVO.getBillType())
                .eqIfPresent(InventoryBillDO::getBillNo, reqVO.getBillNo())
                .eqIfPresent(InventoryBillDO::getSourceType, reqVO.getSourceType())
                .eqIfPresent(InventoryBillDO::getSourceNo, reqVO.getSourceNo())
                .eqIfPresent(InventoryBillDO::getSourceDescription, reqVO.getSourceDescription())
                .eqIfPresent(InventoryBillDO::getStatus, reqVO.getStatus())
                .eqIfPresent(InventoryBillDO::getOperator, reqVO.getOperator())
                .betweenIfPresent(InventoryBillDO::getOperateTime, reqVO.getOperateTime())
                .eqIfPresent(InventoryBillDO::getRemark, reqVO.getRemark())
                .eqIfPresent(InventoryBillDO::getExt, reqVO.getExt())
                .eqIfPresent(InventoryBillDO::getVersion, reqVO.getVersion())
                .betweenIfPresent(InventoryBillDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InventoryBillDO::getId));
    }

}