package com.xyy.saas.localserver.inventory.server.controller.admin.campon.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 预占单详情分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InventoryCampOnBillDetailPageReqVO extends PageParam {

    @Schema(description = "预占单编号")
    private String billNo;

    @Schema(description = "商品编号")
    private String productPref;

    @Schema(description = "批号")
    private String lotNo;

    @Schema(description = "货位guid", example = "18617")
    private String positionGuid;

    @Schema(description = "批次库存guid", example = "17898")
    private String batchGuid;

    @Schema(description = "预占数量")
    private BigDecimal campOnNumber;

    @Schema(description = "释放数量")
    private BigDecimal releaseNumber;

    @Schema(description = "总预占数量")
    private BigDecimal totalCampOnNumber;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}