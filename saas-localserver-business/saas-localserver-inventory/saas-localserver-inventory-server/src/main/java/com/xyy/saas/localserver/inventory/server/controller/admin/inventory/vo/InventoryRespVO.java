package com.xyy.saas.localserver.inventory.server.controller.admin.inventory.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商品库存 Response VO")
@Data
@ExcelIgnoreUnannotated
public class InventoryRespVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "5048")
    @ExcelProperty("主键ID")
    private Long id;

    @Schema(description = "商品编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("商品编号")
    private String productPref;

    @Schema(description = "库存数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("库存数量")
    private BigDecimal stockNumber;

    @Schema(description = "成本均价", example = "32481")
    @ExcelProperty("成本均价")
    private BigDecimal costPrice;

    @Schema(description = "库存总金额")
    @ExcelProperty("库存总金额")
    private BigDecimal stockAmount;

    @Schema(description = "最后一次采购入库价", requiredMode = Schema.RequiredMode.REQUIRED, example = "16933")
    @ExcelProperty("最后一次采购入库价")
    private BigDecimal lastInPrice;

    @Schema(description = "最后一次供应商", example = "24016")
    @ExcelProperty("最后一次供应商")
    private String lastSupplierGuid;

    @Schema(description = "最后一次入库时间")
    @ExcelProperty("最后一次入库时间")
    private LocalDateTime lastInTime;

    @Schema(description = "备注", example = "随便")
    @ExcelProperty("备注")
    private String remark;

    @Schema(description = "库存上限")
    @ExcelProperty("库存上限")
    private BigDecimal storeMaxLimit;

    @Schema(description = "库存下限")
    @ExcelProperty("库存下限")
    private BigDecimal storeMinLimit;

    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("版本号")
    private Long version;

    @Schema(description = "创建时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @ExcelProperty("创建时间")
    private LocalDateTime createTime;

}