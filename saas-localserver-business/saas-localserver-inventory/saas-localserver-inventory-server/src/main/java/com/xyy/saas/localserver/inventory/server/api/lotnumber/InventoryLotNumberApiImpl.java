package com.xyy.saas.localserver.inventory.server.api.lotnumber;

import com.xyy.saas.localserver.inventory.api.lotnumber.InventoryLotNumberApi;
import com.xyy.saas.localserver.inventory.api.lotnumber.dto.InventoryLotNumberDTO;
import com.xyy.saas.localserver.inventory.api.lotnumber.dto.InventoryLotNumberQueryDTO;
import com.xyy.saas.localserver.inventory.server.service.lotnumber.InventoryLotNumberService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class InventoryLotNumberApiImpl implements InventoryLotNumberApi {

    @Resource
    private InventoryLotNumberService inventoryLotNumberService;

    @Override
    public List<InventoryLotNumberDTO> getInventory(InventoryLotNumberQueryDTO queryDTO) {
        return inventoryLotNumberService.getInventoryLotNumberList(queryDTO);
    }

}
