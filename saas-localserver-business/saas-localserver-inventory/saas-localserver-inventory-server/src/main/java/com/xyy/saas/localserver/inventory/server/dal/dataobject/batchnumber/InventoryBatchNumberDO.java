package com.xyy.saas.localserver.inventory.server.dal.dataobject.batchnumber;

import lombok.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 商品批次库存 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inventory_batch_number")
@KeySequence("saas_inventory_batch_number_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryBatchNumberDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;
    /**
     * 批次号guid
     */
    private String guid;
    /**
     * 商品编号
     */
    private String productPref;
    /**
     * 批号
     */
    private String lotNo;
    /**
     * 货位guid
     */
    private String positionGuid;
    /**
     * 库存数量
     */
    private BigDecimal stockNumber;
    /**
     * 出库预占
     */
    private BigDecimal campOnNumber;
    /**
     * 单据类型
     */
    private Integer billType;
    /**
     * 单据编号
     */
    private String billNo;
    /**
     * 供应商guid
     */
    private String supplierGuid;
    /**
     * 入库含税价
     */
    private BigDecimal inTaxPrice;
    /**
     * 入库税率
     */
    private BigDecimal taxRate;
    /**
     * 灭菌批号
     */
    private String sterilizationBatchNo;
    /**
     * 追踪维度
     */
    private String tracePref;
    /**
     * 来源追踪维度
     */
    private String sourceTracePref;
    /**
     * 总部追踪维度
     */
    private String rootTracePref;
    /**
     * 入库时间
     */
    private LocalDateTime inTime;
    /**
     * 备注
     */
    private String remark;
    /**
     * 版本号
     */
    private Long version;

}