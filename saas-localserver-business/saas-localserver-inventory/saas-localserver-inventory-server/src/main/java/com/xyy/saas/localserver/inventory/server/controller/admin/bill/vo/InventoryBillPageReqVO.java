package com.xyy.saas.localserver.inventory.server.controller.admin.bill.vo;

import lombok.*;
import java.util.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 库存单据分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InventoryBillPageReqVO extends PageParam {

    @Schema(description = "单据类型：101-货位移动 102-盘点 103-报损报溢 104-库存拆零 105-质量复查", example = "101")
    private Integer billType;

    @Schema(description = "单据编号")
    private String billNo;

    @Schema(description = "来源类型", example = "2")
    private Integer sourceType;

    @Schema(description = "来源编号")
    private String sourceNo;

    @Schema(description = "来源描述", example = "你说的对")
    private String sourceDescription;

    @Schema(description = "审批状态: 1-暂存 2-待检验 3-审批中 5-已驳回 6-已完成", example = "2")
    private Integer status;

    @Schema(description = "操作员")
    private String operator;

    @Schema(description = "操作时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] operateTime;

    @Schema(description = "备注", example = "随便")
    private String remark;

    @Schema(description = "扩展信息")
    private String ext;

    @Schema(description = "版本号")
    private Long version;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}