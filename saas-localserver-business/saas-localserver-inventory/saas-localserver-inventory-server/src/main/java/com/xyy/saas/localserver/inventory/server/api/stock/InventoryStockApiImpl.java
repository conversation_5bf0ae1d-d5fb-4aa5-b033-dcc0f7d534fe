package com.xyy.saas.localserver.inventory.server.api.stock;

import com.xyy.saas.localserver.inventory.api.change.dto.InventoryChangeDetailDTO;
import com.xyy.saas.localserver.inventory.api.stock.InventoryStockApi;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventoryIncreaseDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventoryMoveDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventoryReduceDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySwapDTO;
import com.xyy.saas.localserver.inventory.server.service.stock.InventoryStockService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class InventoryStockApiImpl implements InventoryStockApi {

    @Resource
    private InventoryStockService inventoryStockService;

    @Override
    public List<InventoryChangeDetailDTO> increaseStock(InventoryIncreaseDTO inventoryIncrease) {
        return inventoryStockService.increaseStock(inventoryIncrease);
    }

    @Override
    public Map<String, List<InventoryChangeDetailDTO>> batchIncreaseStock(List<InventoryIncreaseDTO> inventoryIncreaseList) {
        return inventoryStockService.batchIncreaseStock(inventoryIncreaseList);
    }

    @Override
    public List<InventoryChangeDetailDTO> reduceStock(InventoryReduceDTO inventoryReduce) {
        return inventoryStockService.reduceStock(inventoryReduce);
    }

    @Override
    public boolean moveStock(InventoryMoveDTO inventoryMove) {
        return inventoryStockService.moveStock(inventoryMove);
    }

    @Override
    public boolean swapStock(InventorySwapDTO inventorySwap) {
        return inventoryStockService.swapStock(inventorySwap);
    }

}
