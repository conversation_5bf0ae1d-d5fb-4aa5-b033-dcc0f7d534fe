package com.xyy.saas.localserver.inventory.server.controller.admin.campon.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 预占单新增/修改 Request VO")
@Data
public class InventoryCampOnBillSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "20384")
    private Long id;

    @Schema(description = "单据类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "单据类型不能为空")
    private Integer billType;

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "编号不能为空")
    private String billNo;

    @Schema(description = "来源单号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "来源单号不能为空")
    private String sourceNo;

    @Schema(description = "上级预占单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "上级预占单编号不能为空")
    private String parentNo;

    @Schema(description = "状态: 1:预占中; 2:预占释放", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "状态: 1:预占中; 2:预占释放不能为空")
    private Integer status;

    @Schema(description = "备注", requiredMode = Schema.RequiredMode.REQUIRED, example = "随便")
    @NotEmpty(message = "备注不能为空")
    private String remark;

    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "版本号不能为空")
    private Long version;

}