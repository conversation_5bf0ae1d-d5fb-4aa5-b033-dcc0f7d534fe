package com.xyy.saas.localserver.inventory.server.controller.admin.tracecode.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 追溯码帐页新增/修改 Request VO")
@Data
public class TraceCodeStockDetailSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "4294")
    private Long id;

    @Schema(description = "单据类型/摘要", example = "2")
    private Integer billType;

    @Schema(description = "单据编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "单据编号不能为空")
    private String billNo;

    @Schema(description = "领域单据时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "领域单据时间不能为空")
    private LocalDateTime billTime;

    @Schema(description = "商品编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品编码不能为空")
    private String productPref;

    @Schema(description = "批号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "批号不能为空")
    private String lotNo;

    @Schema(description = "批次库存guid", requiredMode = Schema.RequiredMode.REQUIRED, example = "15999")
    @NotEmpty(message = "批次库存guid不能为空")
    private String batchGuid;

    @Schema(description = "供应商编码")
    private String supplierPref;

    @Schema(description = "追溯码/UDI码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "追溯码/UDI码不能为空")
    private String traceCode;

    @Schema(description = "入库数量")
    private BigDecimal inNumber;

    @Schema(description = "出库数量")
    private BigDecimal outNumber;

    @Schema(description = "剩余数量")
    private BigDecimal remainNumber;

    @Schema(description = "追溯码状态 1-已收货；2-已入库；3-已占用；4-已销售；5-已销毁；6-已采退；7-已销退", example = "1")
    private Integer status;

    @Schema(description = "包装规格 1-小包；2-中包；3-大包", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "包装规格 1-小包；2-中包；3-大包不能为空")
    private Integer packageLevel;

    @Schema(description = "父码")
    private String parentCode;

    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "版本号不能为空")
    private Long version;

}