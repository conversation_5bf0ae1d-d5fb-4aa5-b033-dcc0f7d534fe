package com.xyy.saas.localserver.inventory.server.controller.admin.position.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;

import java.math.BigDecimal;

@Schema(description = "管理后台 - 货位新增/修改 Request VO")
@Data
public class InventoryPositionSaveReqVO {

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED, example = "19054")
    private Long id;

    @Schema(description = "主键id", requiredMode = Schema.RequiredMode.REQUIRED)
    private Long tenantId;

    @Schema(description = "编号", requiredMode = Schema.RequiredMode.REQUIRED, example = "21525")
    private String guid;

    @Schema(description = "货区名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "货区名称不能为空")
    @Size(max = 10, message = "货区名称长度不能超过10")
    private String areaName;

    @Schema(description = "货区类型：1-合格，2-不合格，3-待验区/待处理，4-退货区/待退货", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "货区类型：1-合格，2-不合格，3-待验区/待处理，4-退货区/待退货不能为空")
    private Integer areaType;

    @Schema(description = "货位名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "货位名称不能为空")
    @Size(max = 10, message = "货位名称长度不能超过10")
    private String positionName;

    @Schema(description = "存储条件：1--常温,2--阴凉,3--冷藏,4--其他", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "存储条件：1--常温,2--阴凉,3--冷藏,4--其他不能为空")
    private Integer positionCondition;

    @Schema(description = "最低温度")
    private BigDecimal temperatureMin;

    @Schema(description = "最高温度")
    private BigDecimal temperatureMax;

    @Schema(description = "最低湿度")
    private BigDecimal humidityMin;

    @Schema(description = "最高湿度")
    private BigDecimal humidityMax;

    @Schema(description = "启用状态: 0--不禁用 1--禁用")
    private Boolean disable;

    @Schema(description = "备注", example = "随便")
    private String remark;

}