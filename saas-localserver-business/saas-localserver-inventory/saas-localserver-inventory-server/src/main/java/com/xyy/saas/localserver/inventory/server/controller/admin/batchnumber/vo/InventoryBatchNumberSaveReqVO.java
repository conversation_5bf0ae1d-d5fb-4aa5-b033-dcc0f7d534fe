package com.xyy.saas.localserver.inventory.server.controller.admin.batchnumber.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;
import java.math.BigDecimal;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;

@Schema(description = "管理后台 - 商品批次库存新增/修改 Request VO")
@Data
public class InventoryBatchNumberSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "18487")
    private Long id;

    @Schema(description = "批次号guid", requiredMode = Schema.RequiredMode.REQUIRED, example = "7546")
    @NotEmpty(message = "批次号guid不能为空")
    private String guid;

    @Schema(description = "商品编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "商品编号不能为空")
    private String productPref;

    @Schema(description = "批号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "批号不能为空")
    private String lotNo;

    @Schema(description = "货位guid", requiredMode = Schema.RequiredMode.REQUIRED, example = "19216")
    @NotEmpty(message = "货位guid不能为空")
    private String positionGuid;

    @Schema(description = "库存数量", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "库存数量不能为空")
    private BigDecimal stockNumber;

    @Schema(description = "出库预占", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "出库预占不能为空")
    private BigDecimal campOnNumber;

    @Schema(description = "单据类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotEmpty(message = "单据类型")
    private Integer billType;

    @Schema(description = "单据编号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "单据编号")
    private String billNo;

    @Schema(description = "供应商guid", requiredMode = Schema.RequiredMode.REQUIRED, example = "4269")
    @NotEmpty(message = "供应商guid不能为空")
    private String supplierGuid;

    @Schema(description = "入库含税价", requiredMode = Schema.RequiredMode.REQUIRED, example = "31417")
    @NotNull(message = "入库含税价不能为空")
    private BigDecimal inTaxPrice;

    @Schema(description = "入库税率", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "入库税率不能为空")
    private BigDecimal taxRate;

    @Schema(description = "灭菌批号")
    private String sterilizationBatchNo;

    @Schema(description = "总部guid", requiredMode = Schema.RequiredMode.REQUIRED, example = "19654")
    @NotEmpty(message = "总部guid不能为空")
    private String rootGuid;

    @Schema(description = "入库时间", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "入库时间不能为空")
    private LocalDateTime inTime;

    @Schema(description = "备注", example = "你说的对")
    private String remark;

    @Schema(description = "版本号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "版本号不能为空")
    private Long version;

}