package com.xyy.saas.localserver.inventory.server.convert.bill;

import com.xyy.saas.localserver.inventory.api.bill.dto.InventoryBillDTO;
import com.xyy.saas.localserver.inventory.api.bill.dto.InventoryBillDetailDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventoryIncreaseDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventoryMoveDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventoryReduceDTO;
import com.xyy.saas.localserver.inventory.server.controller.admin.bill.vo.InventoryBillDetailSaveReqVO;
import com.xyy.saas.localserver.inventory.server.controller.admin.bill.vo.InventoryBillSaveReqVO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.bill.InventoryBillDO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.bill.InventoryBillDetailDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import reactor.util.function.Tuple3;
import reactor.util.function.Tuples;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Mapper
public interface BillConvert {

    BillConvert INSTANCE = Mappers.getMapper(BillConvert.class);

    InventoryBillDTO convert(InventoryBillSaveReqVO saveReqVO);

    InventoryBillDTO convert(InventoryBillDO inventoryBill);

    InventoryBillDO convert2DO(InventoryBillDTO inventoryBill);

    List<InventoryBillDetailDO> convert(List<InventoryBillDetailDTO> list);

    default InventoryMoveDTO convert(InventoryBillDTO execute) {
        InventoryMoveDTO dto = new InventoryMoveDTO();
        dto.setInventoryReduce(InventoryReduceDTO.builder().billType(execute.getBillType()).billNo(execute.getBillNo()).build());
        dto.setInventoryIncrease(InventoryIncreaseDTO.builder().billType(execute.getBillType()).billNo(execute.getBillNo()).build());
        for (InventoryBillDetailDTO inventoryBillDetailDTO : execute.getList()) {
            // TODO 追溯码与出入库对接（方案需要修改）
        }
        return dto;
    }

    List<InventoryBillDetailDTO> convert2DTO(List<InventoryBillDetailSaveReqVO> List);

    default InventoryBillDTO convert2DTO(InventoryBillSaveReqVO saveReqVO) {
        InventoryBillDTO inventoryBill = convert(saveReqVO);
        List<InventoryBillDetailDTO> list = convert2DTO(saveReqVO.getList());
        inventoryBill.setList(list);
        return inventoryBill;
    }

    default Tuple3<List<InventoryBillDetailDTO>, List<InventoryBillDetailDTO>, List<InventoryBillDetailDTO>> convert(InventoryBillDTO inventoryBill, List<InventoryBillDetailDO> existingDetails) {
        // 转换为 Set 便于查找
        Map<Long, InventoryBillDetailDTO> detailMap = inventoryBill.getList().stream()
                .filter(detail -> detail.getId() != null)
                .collect(Collectors.toMap(InventoryBillDetailDTO::getId, Function.identity()));

        List<InventoryBillDetailDTO> createList = inventoryBill.getList().stream()
                .filter(detail -> detail.getId() == null)
                .collect(Collectors.toList());

        List<InventoryBillDetailDTO> updateList = existingDetails.stream()
                .filter(detail -> detailMap.containsKey(detail.getId()))
                .map(detail -> {
                    InventoryBillDetailDTO dto = detailMap.get(detail.getId());
                    dto.setId(detail.getId()); // 确保 ID 一致
                    return dto;
                })
                .collect(Collectors.toList());

        List<InventoryBillDetailDTO> deleteList = existingDetails.stream()
                .filter(detail -> !detailMap.containsKey(detail.getId()))
                .map(detail -> InventoryBillDetailDTO.builder().id(detail.getId()).build())
                .collect(Collectors.toList());

        return Tuples.of(createList, updateList, deleteList);
    }

}
