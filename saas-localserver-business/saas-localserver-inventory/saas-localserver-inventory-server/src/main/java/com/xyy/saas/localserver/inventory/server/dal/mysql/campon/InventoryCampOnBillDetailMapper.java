package com.xyy.saas.localserver.inventory.server.dal.mysql.campon;

import java.util.*;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.campon.InventoryCampOnBillDetailDO;
import org.apache.ibatis.annotations.Mapper;
import com.xyy.saas.localserver.inventory.server.controller.admin.campon.vo.*;

/**
 * 预占单详情 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InventoryCampOnBillDetailMapper extends BaseMapperX<InventoryCampOnBillDetailDO> {

    default PageResult<InventoryCampOnBillDetailDO> selectPage(InventoryCampOnBillDetailPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InventoryCampOnBillDetailDO>()
                .eqIfPresent(InventoryCampOnBillDetailDO::getBillNo, reqVO.getBillNo())
                .eqIfPresent(InventoryCampOnBillDetailDO::getProductPref, reqVO.getProductPref())
                .eqIfPresent(InventoryCampOnBillDetailDO::getLotNo, reqVO.getLotNo())
                .eqIfPresent(InventoryCampOnBillDetailDO::getPositionGuid, reqVO.getPositionGuid())
                .eqIfPresent(InventoryCampOnBillDetailDO::getBatchGuid, reqVO.getBatchGuid())
                .eqIfPresent(InventoryCampOnBillDetailDO::getCampOnNumber, reqVO.getCampOnNumber())
                .eqIfPresent(InventoryCampOnBillDetailDO::getReleaseNumber, reqVO.getReleaseNumber())
                .eqIfPresent(InventoryCampOnBillDetailDO::getTotalCampOnNumber, reqVO.getTotalCampOnNumber())
                .eqIfPresent(InventoryCampOnBillDetailDO::getRemark, reqVO.getRemark())
                .betweenIfPresent(InventoryCampOnBillDetailDO::getCreateTime, reqVO.getCreateTime())
                .orderByDesc(InventoryCampOnBillDetailDO::getId));
    }

    default List<InventoryCampOnBillDetailDO> selectListByBillNo(String billNo) {
        return selectList(new LambdaQueryWrapperX<InventoryCampOnBillDetailDO>()
                .eqIfPresent(InventoryCampOnBillDetailDO::getBillNo, billNo));
    }

}