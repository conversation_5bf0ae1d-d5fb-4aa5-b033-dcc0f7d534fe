package com.xyy.saas.localserver.inventory.server.dal.dataobject.campon;

import lombok.*;
import java.util.*;
import java.time.LocalDateTime;
import java.time.LocalDateTime;
import com.baomidou.mybatisplus.annotation.*;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;

/**
 * 预占单 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inventory_camp_on_bill")
@KeySequence("saas_inventory_camp_on_bill_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InventoryCampOnBillDO extends BaseDO {

    /**
     * 主键id
     */
    @TableId
    private Long id;
    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 编号
     */
    private String billNo;
    /**
     * 来源类型
     */
    private Integer sourceType;
    /**
     * 来源单号
     */
    private String sourceNo;
    /**
     * 上级预占单编号
     */
    private String parentNo;
    /**
     * 状态: 1:预占中; 2:预占释放
     */
    private Integer status;
    /**
     * 备注
     */
    private String remark;
    /**
     * 版本号
     */
    private Long version;

}