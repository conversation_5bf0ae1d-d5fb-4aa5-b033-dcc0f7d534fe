package com.xyy.saas.localserver.inventory.server.api.inventory;

import com.xyy.saas.localserver.inventory.api.inventory.InventoryApi;
import com.xyy.saas.localserver.inventory.api.inventory.dto.InventoryDTO;
import com.xyy.saas.localserver.inventory.api.inventory.dto.InventoryQueryDTO;
import com.xyy.saas.localserver.inventory.server.service.inventory.InventoryService;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class InventoryApiImpl implements InventoryApi {

    @Resource
    private InventoryService inventoryService;

    @Override
    public List<InventoryDTO> getInventory(InventoryQueryDTO queryDTO) {
        return inventoryService.getInventoryList(queryDTO);
    }

}
