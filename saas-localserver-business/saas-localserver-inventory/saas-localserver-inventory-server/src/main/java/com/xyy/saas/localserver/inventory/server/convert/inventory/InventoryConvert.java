package com.xyy.saas.localserver.inventory.server.convert.inventory;

import com.xyy.saas.localserver.inventory.api.inventory.dto.InventoryDTO;
import com.xyy.saas.localserver.inventory.api.stock.dto.InventorySelectBatchItemDTO;
import com.xyy.saas.localserver.inventory.server.dal.dataobject.inventory.InventoryDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

import java.util.List;

@Mapper
public interface InventoryConvert {

    InventoryConvert INSTANCE = Mappers.getMapper(InventoryConvert.class);

    InventoryDO convert(InventorySelectBatchItemDTO batchItemDTO);

    List<InventoryDTO> convert(List<InventoryDO> list);

}
