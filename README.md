# saas-inquiry-kernel

# 状态

[![警报](http://172.20.11.80:9000/api/project_badges/measure?project=saas_saas-inquiry-kernel_af9740f0-00f5-4ce4-915d-9ab068dfa387&metric=alert_status&token=sqb_37c77dcd5e2ff753e95abcb32c49f90c04d4f6f4)](http://172.20.11.80:9000/dashboard?id=saas_saas-inquiry-kernel_af9740f0-00f5-4ce4-915d-9ab068dfa387)
[![代码行数](http://172.20.11.80:9000/api/project_badges/measure?project=saas_saas-inquiry-kernel_af9740f0-00f5-4ce4-915d-9ab068dfa387&metric=ncloc&token=sqb_37c77dcd5e2ff753e95abcb32c49f90c04d4f6f4)](http://172.20.11.80:9000/dashboard?id=saas_saas-inquiry-kernel_af9740f0-00f5-4ce4-915d-9ab068dfa387)
[![安全热点](http://172.20.11.80:9000/api/project_badges/measure?project=saas_saas-inquiry-kernel_af9740f0-00f5-4ce4-915d-9ab068dfa387&metric=security_hotspots&token=sqb_37c77dcd5e2ff753e95abcb32c49f90c04d4f6f4)](http://172.20.11.80:9000/dashboard?id=saas_saas-inquiry-kernel_af9740f0-00f5-4ce4-915d-9ab068dfa387)
[![覆盖率](http://172.20.11.80:9000/api/project_badges/measure?project=saas_saas-inquiry-kernel_af9740f0-00f5-4ce4-915d-9ab068dfa387&metric=coverage&token=sqb_37c77dcd5e2ff753e95abcb32c49f90c04d4f6f4)](http://172.20.11.80:9000/dashboard?id=saas_saas-inquiry-kernel_af9740f0-00f5-4ce4-915d-9ab068dfa387)
[![重复行 (%)](http://172.20.11.80:9000/api/project_badges/measure?project=saas_saas-inquiry-kernel_af9740f0-00f5-4ce4-915d-9ab068dfa387&metric=duplicated_lines_density&token=sqb_37c77dcd5e2ff753e95abcb32c49f90c04d4f6f4)](http://172.20.11.80:9000/dashboard?id=saas_saas-inquiry-kernel_af9740f0-00f5-4ce4-915d-9ab068dfa387)

# saas-inquiry-drugstore(药店服务)

1. 药店信息
2. 药店问诊配置（策略）

# saas-inquiry-patient(患者服务)

1. 患者记录
2. 患者病历
3. 患者问诊记录
4. 患者收发消息

# saas-inquiry-hospital(医院服务)

1. 医院信息
2. 医院和医生的关系
3. 医院问诊配置（策略）
4. 处方笺

# saas-inquiry-physician(医生服务)

1. 开处方场景
   基础信息-药人信息、病情描述/症状、过敏史、肝肾功能是否异常、是否复诊、上传病历
    - 0). 快速[线上]问诊(填用药人信息、病情描述/症状、选科室)必填 基础信息
    - 1). 药店[线上]问诊(填用药人信息、选药品、诊断/病型、基础信息)必填
    - 2). 挂号[线下]问诊(填用药人信息、选医院-科室-医生-号段、诊断/病型、是否复诊)   基础信息
    - 3). 专家[线上]问诊(填用药人信息、选专家、病情描述/症状)  基础信息
2. 队列模型
    - 1). 患者问诊等待队列
    - 2). 医生的患者等待队列
    - 3). 医生空闲待接诊等待队列
3. 问诊流程
    - 1). 问诊流程图

   填用药人信息(基础信息)->选医生(选院-科室-医生-号段)/调度医生(自动/手动)->医生接诊（开处方）->问诊结束

3. 医生信息

    1. 医生信息(CA认证)
    2. 问诊医生选取(自动/真人)
    3. 医生派单+抢单
    4. 医生开方

# saas-inquiry-pharmacist(药师服务)

1. 药师信息(CA认证)
2. 药师和门店关系
3. 药师审方

# saas-inquiry-im(即时通讯服务)

1. 用户IM账号创建
2. 消息发送
3. 腾讯回调(聊天记录)

# saas-inquiry-signture(签章服务)

1. 用户认证
2. 签名创建
3. 创建合同(填充数据+提交)
4. 追加合同参与方
5. 签章回调
6. 下载处方笺

# saas-inquiry-kernel-all(启动服务、依赖全部的业务)

