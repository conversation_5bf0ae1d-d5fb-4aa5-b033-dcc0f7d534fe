# saas-localserver

## 项目结构
saas-localserver/
├── saas-localserver-application    # 应用程序入口模块
├── saas-localserver-business       # 业务逻辑模块
│   └── saas-localserver-purchase   # 采购相关业务子模块
├── saas-localserver-dsl            # DSL(领域特定语言)模块，支持医保相关配置
├── saas-localserver-resource       # 资源服务模块
└── saas-localserver-entity         # 实体类模块


## 功能说明
### 1. 概述
saas-localserver 是一个药店系统的本地服务组件，主要提供药店本地业务处理能力，包括医保处理、采购管理、资源管理等功能。该项目是整个 SaaS 药店系统的重要组成部分。

### 2. 核心模块
#### 2.1 应用程序模块 (saas-localserver-application)
- 提供系统启动入口
- 集成各业务模块
- 配置系统环境和参数

#### 2.2 业务模块 (saas-localserver-business)
- 采购管理 (saas-localserver-purchase)
  - 库存入库处理
  - 采购订单收货
  - 退货收货
  - 拒收处理
  - 总部出库收货
#### 2.3 DSL模块 (saas-localserver-dsl)
- 支持医保相关的动态配置
- 基于YAML格式的配置解析
- 支持多种协议客户端(HTTP、DLL、WebService)
- 提供参数解析和执行流程
#### 2.4 资源模块 (saas-localserver-resource)
- 管理系统资源
- 提供资源访问接口
#### 2.5 实体模块 (saas-localserver-entity)
- 定义系统数据实体
- 提供数据访问对象
### 3. 技术栈
- 框架: Spring Boot
- 构建工具: Maven
- 数据库: MySQL, SQLite(本地)
- 其他: Jackson-YAML, GraalVM Native Image支持
### 4. 部署说明
安装 (排除 saas-localserver-application)
```bash
mvn clean install -DskipTests -pl '!saas-localserver-application'
```

部署 (排除 saas-localserver-application)
```bash
mvn clean deploy -P test -DskipTests -pl '!saas-localserver-application'
```

### 5. 与云端服务关系
本项目是整个药店系统的本地服务部分，与云端服务(saas-localserver-cloud)协同工作，形成完整的端到云解决方案。本地服务主要处理需要在药店本地执行的业务逻辑，如医保处理、库存管理等。

### 6. 特色功能
- 医保DSL配置：通过YAML配置文件动态调用第三方医保引擎
- 库存入库处理：采用模板方法模式的库存入库处理器架构
- 数据同步：支持本地与云端的数据同步机制
### 7. 开发规范
- 遵循《阿里巴巴Java开发手册》规范
- 代码注释比例不低于1/5
- 重要功能通过单元测试保证质量
