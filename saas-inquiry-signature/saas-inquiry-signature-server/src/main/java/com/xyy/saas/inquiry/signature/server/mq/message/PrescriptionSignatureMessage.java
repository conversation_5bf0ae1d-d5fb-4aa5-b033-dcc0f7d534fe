package com.xyy.saas.inquiry.signature.server.mq.message;

import com.xyy.saas.inquiry.pojo.prescription.ParticipantItem;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 处方签章消息Dto
 *
 * <AUTHOR>
 */
@Builder
@Data
@Accessors(chain = true)
public class PrescriptionSignatureMessage implements Serializable {

    /**
     * 处方合同Pref
     */
    private String contractPref;

    /**
     * 当前参与方
     */
    private ParticipantItem participantItem;

    /**
     * 处方pdfUrl-自绘时发送mq前自己绘制, 存在就不再获取(三方回调需要获取)
     */
    private String pdfUrl;

    private String imgUrl;

    /**
     * 签章平台完结pdfUrl
     */
    private String platformPdfUrl;

    /**
     * 回调时间
     */
    private LocalDateTime callBackTime;

    /**
     * 降级绘制处方笺
     */
    // private Integer selfDrawn;

}
