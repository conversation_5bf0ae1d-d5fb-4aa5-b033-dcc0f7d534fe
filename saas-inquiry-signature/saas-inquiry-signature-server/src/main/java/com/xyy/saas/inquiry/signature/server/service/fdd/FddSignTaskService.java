package com.xyy.saas.inquiry.signature.server.service.fdd;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.fasc.open.api.v5_1.client.SignTaskClient;
import com.fasc.open.api.v5_1.req.signtask.AddActorsReq;
import com.fasc.open.api.v5_1.req.signtask.AddFieldReq;
import com.fasc.open.api.v5_1.req.signtask.CreateSignTaskReq;
import com.fasc.open.api.v5_1.req.signtask.CreateWithTemplateReq;
import com.fasc.open.api.v5_1.req.signtask.DeleteActorReq;
import com.fasc.open.api.v5_1.req.signtask.DeleteFieldReq;
import com.fasc.open.api.v5_1.req.signtask.FillFieldValuesReq;
import com.fasc.open.api.v5_1.req.signtask.FinishSignTaskReq;
import com.fasc.open.api.v5_1.req.signtask.GetOwnerDownloadUrlReq;
import com.fasc.open.api.v5_1.req.signtask.GetSignTaskUrlReq;
import com.fasc.open.api.v5_1.req.signtask.ListSignTaskActorReq;
import com.fasc.open.api.v5_1.req.signtask.SignTaskActorGetUrlReq;
import com.fasc.open.api.v5_1.req.signtask.SignTaskBaseReq;
import com.fasc.open.api.v5_1.req.signtask.SignTaskCancelReq;
import com.fasc.open.api.v5_1.req.signtask.SignTaskGetCerInfoReq;
import com.fasc.open.api.v5_1.res.signtask.CreateSignTaskRes;
import com.fasc.open.api.v5_1.res.signtask.GetSignTaskPreviewUrlRes;
import com.fasc.open.api.v5_1.res.signtask.ListSignTaskActorRes;
import com.fasc.open.api.v5_1.res.signtask.OwnerDownloadUrlRes;
import com.fasc.open.api.v5_1.res.signtask.SignTaskActorGetUrlRes;
import com.fasc.open.api.v5_1.res.signtask.SignTaskDetailRes;
import com.fasc.open.api.v5_1.res.signtask.SignTaskGetCerInfoRes;
import com.xyy.saas.inquiry.signature.server.service.fdd.bean.FddBaseReqDto;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 法大大 签署任务service
 *
 * @Author:chenxiaoyi
 * @Date:2024/02/23 18:36
 */
@Component
@Slf4j
public class FddSignTaskService extends FddBaseService {

    private static final Map<Integer, SignTaskClient> signTaskClientMap = new HashMap<>();

    private SignTaskClient getSignTaskClient(Integer configId) {
        if (configId == null) {
            configId = 0;
        }
        if (signTaskClientMap.get(configId) == null) {
            synchronized (FddSignTaskService.class) {
                if (signTaskClientMap.get(configId) == null) {
                    signTaskClientMap.put(configId, new SignTaskClient(getOpenApiClient(configId)));
                }
            }
        }
        return signTaskClientMap.get(configId);
    }

    /**
     * 创建签署任务 (基于文档)
     *
     * @param createSignTaskReq
     * @return
     */
    public CommonResult<CreateSignTaskRes> createByFile(FddBaseReqDto<CreateSignTaskReq> fddBaseReqDto) {
        return execute(getSignTaskClient(fddBaseReqDto.getConfigId())::create, fddBaseReqDto, "基于文档创建签署任务");
    }

    /**
     * 创建签署任务 (基于签署模板)
     *
     * @param createWithTemplateReq
     * @return
     */
    public CommonResult<CreateSignTaskRes> createWithTemplate(FddBaseReqDto<CreateWithTemplateReq> fddBaseReqDto) {
        return execute((t) -> getSignTaskClient(fddBaseReqDto.getConfigId()).createWithTemplate(t), fddBaseReqDto, "基于签署模板创建签署任务");
    }

    /**
     * 填写签署任务控件内容 需要查询文档id  docId
     *
     * @param fillFieldValuesReq
     */
    public CommonResult<Void> signTaskFillFields(FddBaseReqDto<FillFieldValuesReq> fddBaseReqDto) {
        return executeVoid((t) -> getSignTaskClient(fddBaseReqDto.getConfigId()).fillFieldValues(t), fddBaseReqDto, "填写签署任务控件内容");
    }

    /**
     * 查询签署任务详情
     *
     * @param signTaskBaseReq
     * @return
     */
    public CommonResult<SignTaskDetailRes> getDetail(FddBaseReqDto<SignTaskBaseReq> fddBaseReqDto) {
        return execute((t) -> getSignTaskClient(fddBaseReqDto.getConfigId()).getDetail(t), fddBaseReqDto, "查询签署任务详情");
    }


    /**
     * 定稿签署任务
     */
    public CommonResult<Void> docFinalize(FddBaseReqDto<SignTaskBaseReq> fddBaseReqDto) {
        return executeVoid((t) -> getSignTaskClient(fddBaseReqDto.getConfigId()).finalizeDoc(t), fddBaseReqDto, "定稿签署任务");
    }

    /**
     * 提交签署任务
     */
    public CommonResult<Void> start(FddBaseReqDto<SignTaskBaseReq> fddBaseReqDto) {
        return executeVoid((t) -> getSignTaskClient(fddBaseReqDto.getConfigId()).start(t), fddBaseReqDto, "提交签署任务");
    }

    /**
     * 获取参与方签署链接
     */
    public CommonResult<SignTaskActorGetUrlRes> signTaskActorGetUrl(FddBaseReqDto<SignTaskActorGetUrlReq> fddBaseReqDto) {
        return execute((t) -> getSignTaskClient(fddBaseReqDto.getConfigId()).signTaskActorGetUrl(t), fddBaseReqDto, "获取参与方签署链接");
    }

    /**
     * 获取签署任务预览链接
     */
    public CommonResult<GetSignTaskPreviewUrlRes> getSignTaskPreviewUrl(FddBaseReqDto<GetSignTaskUrlReq> fddBaseReqDto) {
        try {
            fddBaseReqDto.getData().setRedirectUrl(URLEncoder.encode(fddBaseReqDto.getData().getRedirectUrl(), StandardCharsets.UTF_8));
        } catch (Exception ignore) {
        }
        return execute((t) -> getSignTaskClient(fddBaseReqDto.getConfigId()).getSignTaskPreviewUrl(t), fddBaseReqDto, "获取签署任务预览链接");
    }

    /**
     * 获取签署文档下载地址
     */
    public CommonResult<OwnerDownloadUrlRes> getOwnerDownloadUrl(FddBaseReqDto<GetOwnerDownloadUrlReq> fddBaseReqDto) {
        return execute((t) -> getSignTaskClient(fddBaseReqDto.getConfigId()).getOwnerDownloadUrl(t), fddBaseReqDto, "获取签署文档下载地址");
    }

    public CommonResult<Void> cancel(FddBaseReqDto<SignTaskCancelReq> fddBaseReqDto) {
        return executeVoid((t) -> getSignTaskClient(fddBaseReqDto.getConfigId()).cancel(t), fddBaseReqDto, "撤销签署任务");
    }

    /**
     * 结束签署任务
     *
     * @param finishSignTaskReq
     * @return
     */
    public CommonResult<Void> signTaskFinish(FddBaseReqDto<FinishSignTaskReq> fddBaseReqDto) {
        return executeVoid((t) -> getSignTaskClient(fddBaseReqDto.getConfigId()).signTaskFinish(t), fddBaseReqDto, "结束签署任务");
    }

    /**
     * 添加控件
     *
     * @param fieldReq
     * @return
     */
    public CommonResult<Void> addField(FddBaseReqDto<AddFieldReq> fddBaseReqDto) {
        return executeVoid((t) -> getSignTaskClient(fddBaseReqDto.getConfigId()).addField(t), fddBaseReqDto, "添加控件");
    }

    /**
     * 添加参与方
     *
     * @param actorsReq
     * @return
     */
    public CommonResult<Void> addActor(FddBaseReqDto<AddActorsReq> fddBaseReqDto) {
        return executeVoid((t) -> getSignTaskClient(fddBaseReqDto.getConfigId()).addActor(t), fddBaseReqDto, "添加参与方");
    }

    /**
     * 移除控件
     *
     * @param deleteFieldReq
     * @return
     */
    public CommonResult<Void> deleteField(FddBaseReqDto<DeleteFieldReq> fddBaseReqDto) {
        return executeVoid((t) -> getSignTaskClient(fddBaseReqDto.getConfigId()).deleteField(t), fddBaseReqDto, "移除签署任务控件");
    }

    /**
     * 移除签署任务参与方
     *
     * @param deleteActorReq
     * @return
     */
    public CommonResult<Void> deleteActor(FddBaseReqDto<DeleteActorReq> fddBaseReqDto) {
        return executeVoid((t) -> getSignTaskClient(fddBaseReqDto.getConfigId()).deleteActor(t), fddBaseReqDto, "移除签署任务参与方");
    }

    /**
     * 查询参与方证书文件
     *
     * @param signTaskGetCerInfoReq
     * @return
     */
    public CommonResult<SignTaskGetCerInfoRes> getCerInfo(FddBaseReqDto<SignTaskGetCerInfoReq> fddBaseReqDto) {
        return execute((t) -> getSignTaskClient(fddBaseReqDto.getConfigId()).getCerInfo(t), fddBaseReqDto, "查询参与方证书文件");
    }

    /**
     * 查询参与方身份信息
     *
     * @param listSignTaskActorReq
     * @return
     */
    public CommonResult<List<ListSignTaskActorRes>> listSignTaskActor(FddBaseReqDto<ListSignTaskActorReq> fddBaseReqDto) {
        return execute((t) -> getSignTaskClient(fddBaseReqDto.getConfigId()).listSignTaskActor(t), fddBaseReqDto, "查询参与方身份信息");
    }

}
