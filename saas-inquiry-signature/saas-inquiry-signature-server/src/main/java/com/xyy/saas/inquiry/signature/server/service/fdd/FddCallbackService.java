package com.xyy.saas.inquiry.signature.server.service.fdd;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.signature.server.service.fdd.bean.FddCallbackHandlerCode;
import com.xyy.saas.inquiry.signature.server.service.fdd.enums.FddCallbackEvent;
import com.xyy.saas.inquiry.signature.server.service.fdd.handler.FddCallbackHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Map;

/**
 * @Author:chenxiaoyi
 * @Date:2024/11/18 13:41
 */
@Slf4j
@Service
public class FddCallbackService {

    @Autowired
    private Map<String, FddCallbackHandler> fddCallbackHandlerMap;

    /**
     * 法大大处理回调 事件入口
     */
    public CommonResult<Object> eventCallback(String event, String appId, String bizContent) {
        if (StringUtils.isAnyBlank(event, bizContent)) {
            return CommonResult.error(500, "参数异常,不可为空");
        }

        FddCallbackEvent eventEnum = FddCallbackEvent.getCallbackEventEnum(event);
        // 判断事件是否需要处理 不需要处理的回调 默认处理记录日志
        String handlerCode = eventEnum == null ? FddCallbackHandlerCode.DEFAULT : eventEnum.handlerCode;

        // 根据事件分发不同的处理器
        FddCallbackHandler fddCallbackHandler = fddCallbackHandlerMap.get(handlerCode);
        if (fddCallbackHandler == null) {
            log.error("event: {}, has no callback handler {}", event, handlerCode);
            return CommonResult.success("no callback handler for event: " + event);
        }
        return fddCallbackHandler.handle(eventEnum, appId, bizContent);

    }
}
