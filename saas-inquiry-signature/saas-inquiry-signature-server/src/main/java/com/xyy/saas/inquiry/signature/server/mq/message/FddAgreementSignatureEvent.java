package com.xyy.saas.inquiry.signature.server.mq.message;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import com.xyy.saas.inquiry.signature.server.service.fdd.dto.SignTaskCallbackDto;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 法大大授权合同事件
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class FddAgreementSignatureEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "FDD_SIGN_TASK_AGREEMENT";

    private SignTaskCallbackDto msg;


    @JsonCreator
    public FddAgreementSignatureEvent(@JsonProperty("msg") SignTaskCallbackDto msg) {
        this.msg = msg;
    }


    @Override
    public String getTag() {
        return "";
    }

}
