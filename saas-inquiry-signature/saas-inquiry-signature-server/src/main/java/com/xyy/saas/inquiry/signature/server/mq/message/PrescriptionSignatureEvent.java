package com.xyy.saas.inquiry.signature.server.mq.message;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 处方签章Event
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class PrescriptionSignatureEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "PRESCRIPTION_SIGNATURE";

    private PrescriptionSignatureMessage msg;


    @JsonCreator
    public PrescriptionSignatureEvent(@JsonProperty("msg") PrescriptionSignatureMessage msg) {
        this.msg = msg;
    }


    @Override
    public String getTag() {
        return "";
    }

}
