package com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo;

import com.xyy.saas.inquiry.enums.prescription.template.TemplateTypeEnum;
import lombok.*;
import io.swagger.v3.oas.annotations.media.Schema;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import org.springframework.format.annotation.DateTimeFormat;
import java.time.LocalDateTime;
import java.util.List;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 处方笺模板分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InquiryPrescriptionTemplatePageReqVO extends PageParam {

    @Schema(description = "处方笺模板id", example = "19956")
    private Long id;
    @Schema(description = "处方笺模板id", example = "19956")
    private List<Long> idList;

    @Schema(description = "关联医院编码（使用了处方笺模板）", requiredMode = Schema.RequiredMode.REQUIRED, example = "25982")
    private String usedRelatedHospitalPref;

    @Schema(description = "处方笺模板名称", example = "张三")
    private String name;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "false")
    private Boolean disable;

    /**
     * 处方笺模板类型 {@link TemplateTypeEnum}
     */
    @Schema(description = "处方笺模板类型", example = "2")
    private Integer type;
    @Schema(description = "处方笺模板类型", example = "2")
    private List<Integer> typeList;

    @Schema(description = "文件 URL", example = "https://www.iocoder.cn")
    private String url;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}