package com.xyy.saas.inquiry.signature.server.convert.person;

import com.fasc.open.api.v5_1.req.user.GetUserAuthUrlReq;
import com.xyy.saas.inquiry.pojo.forward.ForwardPersonInfo;
import com.xyy.saas.inquiry.signature.server.controller.admin.person.vo.InquirySignaturePersonSaveReqVO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.person.InquirySignaturePersonDO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author:chenxiaoyi
 * @Date:2024/10/12 17:17
 */
@Mapper
public interface InquirySignaturePersonConvert {

    InquirySignaturePersonConvert INSTANCE = Mappers.getMapper(InquirySignaturePersonConvert.class);


    @Mapping(target = "userId", source = "clientUserId")
    @Mapping(target = "accountName", source = "accountName")
    @Mapping(target = "userName", expression = "java(org.apache.commons.lang3.StringUtils.defaultString(req.getUserName(), req.getUserIdentInfo() == null ? null : req.getUserIdentInfo().getUserName()))")
    @Mapping(target = "userIdentType", expression = "java(org.apache.commons.lang3.StringUtils.defaultString(req.getUserIdentType(), req.getUserIdentInfo() == null ? null : req.getUserIdentInfo().getUserIdentType()))")
    @Mapping(target = "userIdentNo", expression = "java(org.apache.commons.lang3.StringUtils.defaultString(req.getUserIdentNo(), req.getUserIdentInfo() == null ? null : req.getUserIdentInfo().getUserIdentNo()))")
    @Mapping(target = "mobile", source = "userIdentInfo.mobile")
    @Mapping(target = "bankAccountNo", source = "userIdentInfo.bankAccountNo")
    InquirySignaturePersonDO getUserAuthUrlReq2Po(GetUserAuthUrlReq req);

    InquirySignaturePersonDO convertDo(InquirySignaturePersonSaveReqVO createReqVO);


    @Mapping(target = "id", ignore = true)
    @Mapping(target = "userId", source = "userId")
    @Mapping(target = "signaturePlatform", expression = "java(com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum.FDD.getCode())")
    InquirySignaturePersonSaveReqVO convertSync(ForwardPersonInfo personInfo, Long userId);
}
