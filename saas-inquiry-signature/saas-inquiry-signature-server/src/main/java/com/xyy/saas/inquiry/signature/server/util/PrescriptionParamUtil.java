package com.xyy.saas.inquiry.signature.server.util;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import com.alibaba.fastjson2.JSON;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDetailDto;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDto;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionParamDto;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;

/**
 * 处方参数工具类
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/03 14:06
 */
public class PrescriptionParamUtil {


    public static void main(String[] args) {

        InquiryProductDto productDto = new InquiryProductDto();

        List<InquiryProductDetailDto> list = new ArrayList<>();

        for (int i = 0; i < 2; i++) {
            InquiryProductDetailDto detailDto = new InquiryProductDetailDto();
            detailDto.setCommonName("人参" + i).setQuantity(BigDecimal.TEN);
            list.add(detailDto);
        }
        productDto.setInquiryProductInfos(list);

        System.out.println(getProductParamStr(productDto, MedicineTypeEnum.CHINESE_MEDICINE));

    }

    /**
     * 获取处方商品参数Str
     *
     * @param inquiryProductDto 商品信息
     * @param medicineTypeEnum  药品类型
     * @return
     */
    public static String getProductParamStr(InquiryProductDto inquiryProductDto, MedicineTypeEnum medicineTypeEnum) {
        /**
         * 中草药
         * 通用名 数量 g  \t\t\t  通用名 数量 g  \n
         * eg:
         * 决明子 10g      人参  90g
         * 通草   100g
         */
        if (MedicineTypeEnum.CHINESE_MEDICINE.equals(medicineTypeEnum)) {
            List<String> list = inquiryProductDto.getInquiryProductInfos().stream().map(p -> p.getCommonName() + "  " + p.getQuantity().stripTrailingZeros().toPlainString() + "g")
                .collect(Collectors.toList());
            return JSON.toJSONString(list);
        }
        /**
         *  西药药品信息
         *  通用名 (规格) 【数量 包装单位】
         *  用法用量 : x ,一次 剂量 片 ,频次 \n
         *  eg：
         *  辅酶XX片（10mg*10s*3 板 糖衣）【3 盒】;
         *  用法用量：口服，一次 1 片，3 次/天1; \n
         *  -------------以下空白----------------
         */
        List<String> productList = new ArrayList<>();
        for (InquiryProductDetailDto productInfo : inquiryProductDto.getInquiryProductInfos()) {
            productList.add(productInfo.getCommonName() + "（" + productInfo.getAttributeSpecification() + "）【" + removeTrailingZeros(productInfo.getQuantity()) + productInfo.getUnitName() + "】");
            productList.add("用法用量：" + productInfo.getDirections() + "，一次" + productInfo.getSingleDose() + productInfo.getSingleUnit() + "，" + productInfo.getUseFrequency());
        }
        productList.add("---------------------------------------以下空白-------------------------------------");
        return String.join("\n", productList);
    }


    public static String getInstructionStr(InquiryProductDto inquiryProductDto, MedicineTypeEnum medicineTypeEnum) {
        if (MedicineTypeEnum.ASIAN_MEDICINE.equals(medicineTypeEnum)) {
            return "";
        }
        return String.format("剂数：共 %s 剂  每 %s 日 %s 剂   每剂分 %s 次用药  加工方式：%s   用法：%s",
            inquiryProductDto.getTcmTotalDosage() != null ? inquiryProductDto.getTcmTotalDosage() : "",
            inquiryProductDto.getTcmDaily() != null ? inquiryProductDto.getTcmDaily() : "",
            inquiryProductDto.getTcmDailyDosage() != null ? inquiryProductDto.getTcmDailyDosage() : "",
            inquiryProductDto.getTcmUsage() != null ? inquiryProductDto.getTcmUsage() : "",
            inquiryProductDto.getTcmProcessingMethod() != null ? inquiryProductDto.getTcmProcessingMethod() : "",
            inquiryProductDto.getTcmDirections() != null ? inquiryProductDto.getTcmDirections() : "");
    }


    public static String getWarningStr(InquiryProductDto inquiryProductDto, MedicineTypeEnum medicineTypeEnum) {
        if (MedicineTypeEnum.ASIAN_MEDICINE.equals(medicineTypeEnum)) {
            return "";
        }
        return "注：该处方为复诊处方，沿用原治疗方案";
    }

    public static String getRemarkStr(TenantDto tenantDto, PrescriptionParamDto paramDto) {
        String remark = "本处方仅限{drugstoreName}使用(非本门店无效) ".replace("{drugstoreName}", StringUtils.defaultIfBlank(tenantDto.getBusinessLicenseName(), tenantDto.getName()));
        if (CommonStatusEnum.isEnable(paramDto.getSlowDisease())) {
            // return remark + " 慢病病情需要";
        }
        // return remark + " 本处方3日内有效";
        return remark;

    }

    public static String removeTrailingZeros(BigDecimal number) {
        if (number == null) {
            return "";
        }
        // 移除尾数0
        number = number.stripTrailingZeros();
        // 转换为普通字符串表示
        String result = number.toPlainString();

        // 处理科学计数法的情况（如1E+2变成100）
        if (result.contains("E")) {
            return new BigDecimal(result).toPlainString();
        }
        return result;
    }
}
