package com.xyy.saas.inquiry.signature.server.controller.admin.platform.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Schema(description = "管理后台 - 签章回调日志新增/修改 Request VO")
@Data
public class InquirySignatureCallbackLogSaveReqVO {

    @Schema(description = "自增主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "2027")
    private Long id;

    @Schema(description = "签章平台 0-自签署 1-法大大", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "签章平台 0-自签署 1-法大大不能为空")
    private Integer signaturePlatform;

    @Schema(description = "类型：事件ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotEmpty(message = "类型：事件ID不能为空")
    private String type;

    @Schema(description = "业务id", requiredMode = Schema.RequiredMode.REQUIRED, example = "9246")
    @NotEmpty(message = "业务id不能为空")
    private String bizId;

    @Schema(description = "具体事件的请求参数，json字符串")
    private String bizContent;

}