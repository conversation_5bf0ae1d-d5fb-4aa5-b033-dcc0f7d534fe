package com.xyy.saas.inquiry.signature.server.service.prescription;

import static com.xyy.saas.inquiry.signature.enums.ErrorCodeConstants.INQUIRY_SIGNATURE_CA_AUTH_NOT_SIGN_URL;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureAuditDto;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.InquirySignatureCaAuthRespVO;
import com.xyy.saas.inquiry.signature.server.convert.signature.InquirySignatureContractConvert;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquirySignatureContractDO;
import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionExecutionSignatureEvent;
import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionExecutionSignatureMessage;
import com.xyy.saas.inquiry.signature.server.mq.producer.PrescriptionExecutionSignatureProducer;
import com.xyy.saas.inquiry.signature.server.service.ca.InquirySignatureCaAuthService;
import com.xyy.saas.inquiry.signature.server.service.prescriptiontemplate.dto.TemplateSignCheckedDto;
import com.xyy.saas.inquiry.signature.server.service.signature.InquirySignatureContractService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

/**
 * 问诊远程处方签章service
 *
 * @Author:chenxiaoyi
 * @Date:2024/11/26 17:47
 */
@Service
@Slf4j
public class InquirySignatureRemotePrescriptionServiceImpl implements InquirySignatureRemotePrescriptionService {

    @Resource
    protected InquirySignatureContractService inquirySignatureContractService;

    @Resource
    @Lazy
    private InquirySignatureCaAuthService inquirySignatureCaAuthService;

    // 处方执行签章事件Producer
    @Resource
    private PrescriptionExecutionSignatureProducer prescriptionExecutionSignatureProducer;

    @Override
    public CommonResult<?> auditRemotePrescription(PrescriptionSignatureAuditDto psAuditDto) {

        // 1.校验用户CA及降级签名参数
        InquirySignatureCaAuthRespVO caAuthRespVO = inquirySignatureCaAuthService.getInquirySignatureCaInfo(new TemplateSignCheckedDto().setUserId(psAuditDto.getParticipantItem().getUserId()), SignaturePlatformEnum.FDD);
        if (!caAuthRespVO.isDrawnSignCompleted() && !caAuthRespVO.isCaAuthCompleted()) {
            return CommonResult.error(INQUIRY_SIGNATURE_CA_AUTH_NOT_SIGN_URL);
        }

        // 2.设置签名+参数 固定推送门店药师  PrescriptionTemplateFieldEnum.PHARMACIST.getField()
        InquirySignatureContractConvert.INSTANCE.convertFillRemoteParticipant(psAuditDto, caAuthRespVO);

        // 3.获取或创建处方合同
        InquirySignatureContractDO signatureContractDO = inquirySignatureContractService.saveOrGetSignatureContractByCondition(
            InquirySignatureContractConvert.INSTANCE.convertVoByRemotePrescription(psAuditDto));
        log.info("【Remote-Signature】远程审方-创建合同, prescriptionPref:{},contractPref:{},Participant:{}", psAuditDto.getPrescriptionPref(), signatureContractDO.getPref(), psAuditDto.getParticipantItem());

        // 4.发送审核签章MQ
        prescriptionExecutionSignatureProducer.sendMessage(PrescriptionExecutionSignatureEvent.builder().msg(PrescriptionExecutionSignatureMessage.builder().psAuditDto(psAuditDto).build()).build());
        return CommonResult.success(null);
    }
}
