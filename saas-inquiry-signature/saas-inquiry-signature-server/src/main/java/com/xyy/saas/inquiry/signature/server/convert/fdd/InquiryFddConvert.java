package com.xyy.saas.inquiry.signature.server.convert.fdd;

import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.fasc.open.api.bean.common.Actor;
import com.fasc.open.api.bean.common.ActorCorpMember;
import com.fasc.open.api.bean.common.Field;
import com.fasc.open.api.bean.common.FieldPosition;
import com.fasc.open.api.bean.common.Notification;
import com.fasc.open.api.bean.common.OpenId;
import com.fasc.open.api.enums.common.IdTypeEnum;
import com.fasc.open.api.enums.common.PositionModeEnum;
import com.fasc.open.api.enums.user.UserIdentTypeEnum;
import com.fasc.open.api.v5_1.req.seal.GetPersonalSealFreeSignUrlReq;
import com.fasc.open.api.v5_1.req.signtask.AddActorsInfo;
import com.fasc.open.api.v5_1.req.signtask.AddActorsReq;
import com.fasc.open.api.v5_1.req.signtask.AddActorsTempInfo;
import com.fasc.open.api.v5_1.req.signtask.AddDocInfo;
import com.fasc.open.api.v5_1.req.signtask.AddFieldInfo;
import com.fasc.open.api.v5_1.req.signtask.AddFieldReq;
import com.fasc.open.api.v5_1.req.signtask.AddSignConfigInfo;
import com.fasc.open.api.v5_1.req.signtask.AddSignFieldInfo;
import com.fasc.open.api.v5_1.req.signtask.CreateSignTaskReq;
import com.fasc.open.api.v5_1.req.signtask.CreateWithTemplateReq;
import com.fasc.open.api.v5_1.req.signtask.TemplateSignConfigInfoReq;
import com.fasc.open.api.v5_1.req.user.GetUserAuthUrlReq;
import com.fasc.open.api.v5_1.req.user.UserIdentInfoReq;
import com.fasc.open.api.v5_1.res.signtask.SignTaskDetailRes;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.signature.ContractTypeEnum;
import com.xyy.saas.inquiry.pojo.prescription.ParticipantItem;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureInitDto;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.InquirySignatureCaAuthRespVO;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.UserCaAuthCallbackVo;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.ca.InquirySignatureCaAuthDO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.person.InquirySignaturePersonDO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquirySignatureContractDO;
import com.xyy.saas.inquiry.signature.server.service.fdd.dto.AuthCallback2RedirectDto;
import com.xyy.saas.inquiry.signature.server.service.fdd.dto.FddSignTaskCreateDto;
import jakarta.annotation.Nonnull;
import java.time.ZoneOffset;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author:chenxiaoyi
 * @Date:2024/10/12 17:17
 */
@Mapper
public interface InquiryFddConvert {

    InquiryFddConvert INSTANCE = Mappers.getMapper(InquiryFddConvert.class);


    InquirySignatureCaAuthRespVO convert(InquirySignatureCaAuthDO caAuthDO);

    /**
     * 个人用户在法大大平台页面操作授权(允许授权或拒绝)之后，重定向到redirectUrl上，重定向地址会带上以下参数。  redirectUrl前端必传-h5, 跳转后将授权回调参数再传给后台 如clientUserId已经绑定了法大大帐号，或绑定的实名信息与本次认证实名信息不一致， 用户打开链接后是否默认解绑之前的帐号并以本次登录的帐号更新实名信息，默认为false  false：不解绑 true：解绑
     *
     * @param user
     * @param redirectUrl      重定向地址。即用户在页面上完成操作后重定向跳转到该地址，并且附带上参数。长度最大1000个字符。redirectUrl需要进行编码。例： URLEncoder.encode("http://www.baidu.com", "UTF-8")。若未设置redirectUrl，则授权完成的参数通过回调事件通知应用系统服务端。
     * @param userIdentMethods
     * @return
     */
    default GetUserAuthUrlReq convertFddAuth(AdminUserRespDTO user, String redirectUrl, List<String> userIdentMethods) {
        GetUserAuthUrlReq getUserAuthUrlReq = new GetUserAuthUrlReq();
        getUserAuthUrlReq.setClientUserId(user.getId().toString());
        getUserAuthUrlReq.setAccountName(user.getMobile());
        getUserAuthUrlReq.setRedirectUrl(redirectUrl);
        getUserAuthUrlReq.setUnbindAccount(true);
        UserIdentInfoReq userIdentInfo = new UserIdentInfoReq();
        userIdentInfo.setUserName(user.getNickname());
        userIdentInfo.setUserIdentType(UserIdentTypeEnum.ID_CARD.getCode());
        userIdentInfo.setUserIdentNo(user.getIdCard());
        userIdentInfo.setMobile(user.getMobile());
        userIdentInfo.setIdentMethod(userIdentMethods);
        getUserAuthUrlReq.setUserIdentInfo(userIdentInfo);
        return getUserAuthUrlReq;
    }

    /**
     * 转换用户免签url
     *
     * @param signaturePerson
     * @param redirectUrl
     * @return
     */
    default GetPersonalSealFreeSignUrlReq convertFddFreeSign(InquirySignaturePersonDO signaturePerson, String redirectUrl) {
        GetPersonalSealFreeSignUrlReq freeSignUrlReq = new GetPersonalSealFreeSignUrlReq();
        freeSignUrlReq.setOpenUserId(signaturePerson.getOpenUserId());
        freeSignUrlReq.setSealIds(Collections.singletonList(NumberUtil.parseLong(signaturePerson.getSealId())));
        freeSignUrlReq.setRedirectUrl(redirectUrl);
        return freeSignUrlReq;
    }

    /**
     * 法大大回调
     *
     * @param caAuthCallbackVo
     * @return
     */
    AuthCallback2RedirectDto convertCallback(UserCaAuthCallbackVo caAuthCallbackVo);


    /**
     * 法大大基于文档签署任务
     */
    default CreateSignTaskReq convertCreateWithFile(FddSignTaskCreateDto taskCreateDto) {
        // 1.创建任务
        CreateSignTaskReq signTaskReq = new CreateSignTaskReq();
        signTaskReq.setInitiator(OpenId.getInstance(IdTypeEnum.CORP.getCode(), taskCreateDto.getPlatformConfig().getMainOpenCorpId()));
        signTaskReq.setSignTaskSubject(taskCreateDto.getBizId() + taskCreateDto.getContractType().getDesc());
        signTaskReq.setBusinessId(taskCreateDto.getPlatformConfig().getUnSignBusinessId()); //// 免验证签场景码
        signTaskReq.setExpiresTime(taskCreateDto.getExpiresTime() == null ? null : taskCreateDto.getExpiresTime().getNano() + ""); // 过期时间,为空不设置
        signTaskReq.setAutoFillFinalize(true); // 自动定稿
        signTaskReq.setAutoStart(true); // 自动提交
        signTaskReq.setAutoFinish(false); // 签署任务自动完结
        signTaskReq.setTransReferenceId(taskCreateDto.getBizId()); // 回调透传的 业务id
        // signTaskReq.setCertCAOrg(taskCreateDto.getCertCAOrg()); // 证书机构 - 不传默认无要求,重庆地区要求 EZCA：东方中讯CA

        // 2.构建 参与方信息
        AddActorsInfo addActorsTempInfo = new AddActorsInfo();
        addActorsTempInfo.setActor(getActor(taskCreateDto.getActorId(), IdTypeEnum.PERSON.getCode(), taskCreateDto.getNickname(), new String[]{"sign"}, taskCreateDto.getOpenUserId()
            , taskCreateDto.getMobile(), null, null, null, null, null));
        AddSignConfigInfo signConfigInfoReq = new AddSignConfigInfo();
        signConfigInfoReq.setRequestVerifyFree(true);
        // signConfigInfoReq.setFreeLogin(true); // 免登录
        signConfigInfoReq.setIdentifiedView(false); // 无需实名即可查看
        addActorsTempInfo.setSignConfigInfo(signConfigInfoReq);
        if (taskCreateDto.isAddDoc()) {
            String docId = IdUtil.simpleUUID();
            // 2.1关联签署控件
            AddSignFieldInfo addSignFieldInfo = new AddSignFieldInfo();
            addSignFieldInfo.setFieldDocId(docId);
            addSignFieldInfo.setFieldId(taskCreateDto.getFileId());
            addSignFieldInfo.setSealId(NumberUtil.parseLong(taskCreateDto.getSealId(), null));

            addActorsTempInfo.setSignFields(Collections.singletonList(addSignFieldInfo));
            signTaskReq.setActors(Collections.singletonList(addActorsTempInfo));
            // 2.2添加签署任务控件 基于关键字
            AddDocInfo addDocInfo = new AddDocInfo();
            addDocInfo.setDocId(docId);
            addDocInfo.setDocName(docId);
            addDocInfo.setDocFileId(taskCreateDto.getFileId());
            final Field field = getField(taskCreateDto);
            addDocInfo.setDocFields(Collections.singletonList(field));
            signTaskReq.setDocs(Collections.singletonList(addDocInfo));
        }
        return signTaskReq;
    }

    /**
     * 法大大基于模板创建签署任务
     *
     * @param taskCreateDto
     * @return
     */
    default CreateWithTemplateReq convertCreateWithTemplate(FddSignTaskCreateDto taskCreateDto) {
        // 1.创建签署任务链接
        CreateWithTemplateReq createWithTemplateReq = new CreateWithTemplateReq();
        createWithTemplateReq.setInitiator(OpenId.getInstance(IdTypeEnum.CORP.getCode(), taskCreateDto.getPlatformConfig().getMainOpenCorpId()));
        createWithTemplateReq.setSignTaskSubject(taskCreateDto.getBizId() + taskCreateDto.getContractType().getDesc());
        createWithTemplateReq.setSignTemplateId(taskCreateDto.getSignTemplateId()); // 签署任务模板
        createWithTemplateReq.setBusinessId(taskCreateDto.getPlatformConfig().getUnSignBusinessId()); // 免验证签场景码
        createWithTemplateReq.setExpiresTime(taskCreateDto.getExpiresTime() == null ? null : taskCreateDto.getExpiresTime().toInstant(ZoneOffset.ofHours(8)).toEpochMilli() + ""); // 过期时间,为空不设置
        createWithTemplateReq.setAutoFillFinalize(true);   // 自动定稿
        createWithTemplateReq.setAutoStart(MapUtils.isEmpty(taskCreateDto.getParamMap())); // 填充参数为空 默认自动提交
        createWithTemplateReq.setTransReferenceId(taskCreateDto.getBizId()); // 回调透传的 业务id
        // 2.构建 参与方信息
        AddActorsTempInfo addActorsTempInfo = new AddActorsTempInfo();
        addActorsTempInfo.setActor(getActor(taskCreateDto.getActorId(), IdTypeEnum.PERSON.getCode(), taskCreateDto.getNickname(), new String[]{"sign"}, taskCreateDto.getOpenUserId()
            , taskCreateDto.getFreeLogin() ? null : taskCreateDto.getMobile(), null, null, null, null, null));
        TemplateSignConfigInfoReq signConfigInfoReq = new TemplateSignConfigInfoReq();
        signConfigInfoReq.setFreeLogin(taskCreateDto.getFreeLogin()); // 免登录 - 当新系统的手机号和旧系统的不一致，但是CA的时候又是直接拿旧的信息过来的时候
        signConfigInfoReq.setIdentifiedView(false); // 无需实名即可查看
        addActorsTempInfo.setSignConfigInfo(signConfigInfoReq);
        createWithTemplateReq.setActors(Collections.singletonList(addActorsTempInfo));
        return createWithTemplateReq;
    }

    /**
     * 添加签署任务参与方
     *
     * @param taskCreateDto 需要设置 docId ,FileId
     * @return
     */
    default AddActorsReq convertAddActor(FddSignTaskCreateDto taskCreateDto) {
        AddActorsReq actorsReq = new AddActorsReq();
        actorsReq.setSignTaskId(taskCreateDto.getSignTaskId());

        Actor actor = getActor(taskCreateDto.getActorId(), IdTypeEnum.PERSON.getCode(), taskCreateDto.getNickname(), new String[]{"sign"}, taskCreateDto.getOpenUserId()
            , taskCreateDto.getMobile(), null, null, null, null, null);

        // 2.构建 参与方信息
        AddActorsInfo addActorsTempInfo = new AddActorsInfo();
        addActorsTempInfo.setActor(actor);
        AddSignConfigInfo signConfigInfoReq = new AddSignConfigInfo();
        signConfigInfoReq.setRequestVerifyFree(true); // 免签
        // signConfigInfoReq.setFreeLogin(true); // 免登录
        signConfigInfoReq.setIdentifiedView(false); // 无需实名即可查看
//        signConfigInfoReq.setFreeDragSealId(1724033412150133182L);
        addActorsTempInfo.setSignConfigInfo(signConfigInfoReq);

        AddSignFieldInfo addSignFieldInfo = new AddSignFieldInfo();
        addSignFieldInfo.setFieldDocId(taskCreateDto.getDocId());
        addSignFieldInfo.setFieldId(taskCreateDto.getFileId());
        addSignFieldInfo.setSealId(NumberUtil.parseLong(taskCreateDto.getSealId(), null));
        addActorsTempInfo.setSignFields(Collections.singletonList(addSignFieldInfo));
        actorsReq.setActors(Collections.singletonList(addActorsTempInfo));

        return actorsReq;
    }

    /**
     * 添加签署任务控件
     *
     * @param taskCreateDto
     * @return
     */
    default AddFieldReq convertAddField(FddSignTaskCreateDto taskCreateDto) {
        AddFieldReq fieldReq = new AddFieldReq();
        fieldReq.setSignTaskId(taskCreateDto.getSignTaskId());
        AddFieldInfo addFieldInfo = new AddFieldInfo();
        addFieldInfo.setDocId(taskCreateDto.getDocId());
        final Field field = getField(taskCreateDto);
        addFieldInfo.setDocFields(Collections.singletonList(field));
        fieldReq.setFields(Collections.singletonList(addFieldInfo));
        return fieldReq;
    }

    private static @Nonnull Field getField(FddSignTaskCreateDto taskCreateDto) {
        Field field = new Field();
        field.setFieldId(taskCreateDto.getFileId());
        field.setFieldName(taskCreateDto.getFileId());
        field.setFieldType("person_sign");
        FieldPosition fieldPosition = new FieldPosition();
        if (StringUtils.equals(taskCreateDto.getPositionMode(), PositionModeEnum.PIXEL.getCode())) {
            fieldPosition.setPositionMode(PositionModeEnum.PIXEL.getCode());
            fieldPosition.setPositionPageNo(1); //  定位页码。首页从1开始。
            fieldPosition.setPositionX(taskCreateDto.getPositionX());
            fieldPosition.setPositionY(taskCreateDto.getPositionY());
        } else {
            fieldPosition.setPositionMode(PositionModeEnum.KEYWORD.getCode());
            fieldPosition.setPositionKeyword(taskCreateDto.getPositionKeyword());
            fieldPosition.setKeywordOffsetX(StringUtils.defaultIfBlank(taskCreateDto.getKeywordOffsetX(), "100"));
            fieldPosition.setKeywordOffsetY(taskCreateDto.getKeywordOffsetY());
        }

        field.setPosition(fieldPosition);
        return field;
    }


    /**
     * 构建参与方
     *
     * @param actorId 模板上的参与方 标识
     * @return
     */
    default Actor getActor(String actorId, String actorType, String actorName, String[] permissions,
        String actorOpenId, String accountName, String actorFDDId, ActorCorpMember[] actorCorpMembers,
        String identNameForMatch, String certNoForMatch, Notification notification) {
        Actor actor = new Actor();
        actor.setActorId(actorId);
//        actor.setClientUserId(clientUserId);
        actor.setActorType(actorType);
        actor.setActorName(actorName);
        actor.setAccountName(accountName);
        if (permissions != null) {
            actor.setPermissions(Arrays.asList(permissions));
        }
        actor.setActorOpenId(actorOpenId);
        actor.setActorFDDId(actorFDDId);
        if (actorCorpMembers != null) {
            actor.setActorCorpMembers(Arrays.asList(actorCorpMembers));
        }
        actor.setIdentNameForMatch(identNameForMatch);
        actor.setCertNoForMatch(certNoForMatch);
        actor.setNotification(notification);
        return actor;
    }

    /**
     * 转换法大大签章处方dto
     *
     * @param psDto       签章initDto
     * @param fddPersonDO fdd用户
     * @param fileUrl     文件url
     * @return
     */
    default FddSignTaskCreateDto convertByIssuePs(PrescriptionSignatureInitDto psDto, InquirySignaturePersonDO fddPersonDO, String fileUrl) {
        return FddSignTaskCreateDto.builder()
            .bizId(psDto.getPrescriptionPref())
            .nickname(fddPersonDO.getUserName())
            .mobile(fddPersonDO.getMobile())
            .openUserId(fddPersonDO.getOpenUserId())
            .userId(fddPersonDO.getUserId())
            .sealId(fddPersonDO.getSealId())
            .actorId(psDto.getParticipantItem().getActorField())
            .positionKeyword(psDto.getParticipantItem().getActorFieldName())
            .contractType(ContractTypeEnum.PRESCRIPTION)
            .configId(psDto.getSignaturePlatformConfigId())
            .fileUrl(fileUrl)
            .build();
    }

    /**
     * 转换法大大审核处方dto
     *
     * @param signatureContract 合同
     * @param fddPersonDO       fdd用户
     * @param signTaskDetailRes 签章任务详情
     * @return
     */
    default FddSignTaskCreateDto convertByAuditPs(InquirySignatureContractDO signatureContract, ParticipantItem participantItem, InquirySignaturePersonDO fddPersonDO, SignTaskDetailRes signTaskDetailRes) {
        return FddSignTaskCreateDto.builder()
            .signTaskId(signatureContract.getThirdId())
            .configId(signatureContract.extGet().getPlatformConfigId())
            .docId(signTaskDetailRes.getDocs().getFirst().getDocId())
            // .fileId(signTaskDetailRes.getDocs().getFirst().getDocFileId()) //  控件编码【1740626173748132166】不允许重复
            .fileId(participantItem.getActorField())
            .actorId(participantItem.getActorField())
            .positionKeyword(participantItem.getActorFieldName())
            .nickname(fddPersonDO.getUserName())
            .mobile(fddPersonDO.getMobile())
            .openUserId(fddPersonDO.getOpenUserId())
            .sealId(fddPersonDO.getSealId())
            .build();
    }

    /**
     * 转换合同同步
     *
     * @param signatureContract
     * @param initiator
     * @param fileUrl
     * @return
     */
    default FddSignTaskCreateDto convertByContractSync(InquirySignatureContractDO signatureContract, ParticipantItem initiator, InquirySignaturePersonDO personDO, String fileUrl) {
        return FddSignTaskCreateDto.builder()
            .userId(initiator.getUserId())
            .nickname(initiator.getName())
            .mobile(initiator.getMobile())
            .openUserId(personDO.getOpenUserId())
            .sealId(personDO.getSealId())
            .bizId(signatureContract.getBizId())
            .actorId(initiator.getActorField())
            .positionKeyword(initiator.getActorFieldName())
            .fileUrl(fileUrl)
            .configId(signatureContract.extGet().getPlatformConfigId())
            .contractType(ContractTypeEnum.PRESCRIPTION)
            .positionMode(Objects.equals(signatureContract.extGet().getInquiryBizType(), InquiryBizTypeEnum.REMOTE_INQUIRY.getCode()) ? PositionModeEnum.PIXEL.getCode() : PositionModeEnum.KEYWORD.getCode())
            .positionX(Optional.ofNullable(signatureContract.extGet().getCoordinateX()).orElse(0).toString())
            .positionY(Optional.ofNullable(signatureContract.extGet().getCoordinateY()).orElse(0).toString())
            .build();
    }

    default FddSignTaskCreateDto convertByContractAddSync(InquirySignatureContractDO signatureContract, ParticipantItem participantItem, CommonResult<SignTaskDetailRes> signTaskDetail, InquirySignaturePersonDO personDO) {
        return FddSignTaskCreateDto.builder()
            .docId(signTaskDetail.getData().getDocs().getFirst().getDocId())
            .configId(signatureContract.extGet().getPlatformConfigId())
            // .fileId(signTaskDetail.getData().getDocs().getFirst().getDocFileId())
            .fileId(participantItem.getActorField())
            .actorId(participantItem.getActorField())
            .positionKeyword(participantItem.getActorFieldName())
            .nickname(participantItem.getName())
            .mobile(participantItem.getMobile())
            .openUserId(personDO.getOpenUserId())
            .sealId(personDO.getSealId())
            .build();
    }
}
