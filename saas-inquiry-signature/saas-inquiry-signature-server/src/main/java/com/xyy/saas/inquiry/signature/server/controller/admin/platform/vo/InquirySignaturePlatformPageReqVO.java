package com.xyy.saas.inquiry.signature.server.controller.admin.platform.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

@Schema(description = "管理后台 - 签章平台配置分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InquirySignaturePlatformPageReqVO extends PageParam {

    @Schema(description = "签章平台 0-自签署 1-法大大")
    private Integer signaturePlatform;

    @Schema(description = "属性名 eg:私有云地址 对应枚举", example = "张三")
    private String paramName;

    @Schema(description = "属性值 eg:8000809")
    private String paramValue;

    @Schema(description = "描述", example = "你猜")
    private String description;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}