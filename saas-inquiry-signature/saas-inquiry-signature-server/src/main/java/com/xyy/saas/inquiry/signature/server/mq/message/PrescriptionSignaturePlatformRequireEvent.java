package com.xyy.saas.inquiry.signature.server.mq.message;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 处方签章平台确认Event
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class PrescriptionSignaturePlatformRequireEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "PRESCRIPTION_SIGNATURE_PLATFORM_REQUIRE";

    private PrescriptionSignaturePlatformRequireMessage msg;


    @JsonCreator
    public PrescriptionSignaturePlatformRequireEvent(@JsonProperty("msg") PrescriptionSignaturePlatformRequireMessage msg) {
        this.msg = msg;
    }


    @Override
    public String getTag() {
        return "";
    }

}
