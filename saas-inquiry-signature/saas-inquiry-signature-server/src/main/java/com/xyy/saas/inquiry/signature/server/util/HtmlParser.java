package com.xyy.saas.inquiry.signature.server.util;

import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.select.Elements;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author: xucao
 * @DateTime: 2025/4/15 19:06
 * @Description: html解析器
 **/
public class HtmlParser {

    /**
     * 根据指定标签解析html
     * @param html 文本
     * @param lable 标签
     * @return
     */
    public static List<String> parseHtmlParagraphs(String html, String lable) {
        final List<String> result = new ArrayList<>();
        // 使用 Jsoup 解析 HTML
        Document doc = Jsoup.parse(html);
        // 选择所有 传入lable 标签（包含空标签处理）
        Elements paragraphs = doc.select(lable);
        paragraphs.forEach(p -> {
            String text = p.text();
            if (!text.isBlank()) {
                result.add(text.trim());
            }
        });
        return result;
    }
}
