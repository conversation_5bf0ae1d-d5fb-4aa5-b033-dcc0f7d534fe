package com.xyy.saas.inquiry.signature.server.service.fdd.handler;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.signature.server.mq.message.FddCaAuthEvent;
import com.xyy.saas.inquiry.signature.server.mq.producer.FddCaAuthProducer;
import com.xyy.saas.inquiry.signature.server.service.fdd.bean.FddCallbackHandlerCode;
import com.xyy.saas.inquiry.signature.server.service.fdd.dto.AuthCallbackDto;
import com.xyy.saas.inquiry.signature.server.service.fdd.enums.FddCallbackEvent;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * 法大大 授权认证相关回调处理器
 *
 * @Author:chenxiaoyi
 * @Date:2024/02/29 10:48
 */
@Component(FddCallbackHandlerCode.AUTHORIZE)
public class FddCallbackAuthorizeHandler extends AbstractFddCallbackHandler {

    @Resource
    private FddCaAuthProducer fddCaAuthProducer;

    @Override
    public CommonResult<Object> handle(FddCallbackEvent eventEnum, String appId, String bizContent) {
        AuthCallbackDto authCallbackDto = JSON.parseObject(bizContent, AuthCallbackDto.class);
        authCallbackDto.setEventId(eventEnum.eventCode);
        authCallbackDto.setAppId(appId);
        // if (!NumberUtil.isNumber(authCallbackDto.getClientUserId())) {
        //     return CommonResult.success("非inquiry项目忽略");
        // }
        // todo 判断个人还是企业
        boolean isCompany = false;
        String bizId = isCompany ? authCallbackDto.getClientCorpId() : authCallbackDto.getClientUserId();
        // 记录相关回调日志
        insertLogAsync(eventEnum, bizId, bizContent);

        fddCaAuthProducer.sendMessage(FddCaAuthEvent.builder().msg(authCallbackDto).build());

        return CommonResult.success(authCallbackDto);
    }


}
