package com.xyy.saas.inquiry.signature.server.service.signature.sync;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception0;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import com.baomidou.lock.annotation.Lock4j;
import com.fasc.open.api.v5_1.res.signtask.CreateSignTaskRes;
import com.fasc.open.api.v5_1.res.signtask.SignTaskDetailRes;
import com.xyy.saas.inquiry.enums.signature.FddCaConstantEnum;
import com.xyy.saas.inquiry.enums.signature.SignatureAppConfigIdEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.pojo.prescription.ParticipantItem;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionTemplateField;
import com.xyy.saas.inquiry.signature.enums.SignatureSyncPlatformStatusEnum;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractStatusVO;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.InquirySignatureCaAuthRespVO;
import com.xyy.saas.inquiry.signature.server.convert.fdd.InquiryFddConvert;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.person.InquirySignaturePersonDO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquirySignatureContractDO;
import com.xyy.saas.inquiry.signature.server.dal.redis.RedisKeyConstants;
import com.xyy.saas.inquiry.signature.server.mq.consumer.signature.dto.SyncSignaturePlatformResultDto;
import com.xyy.saas.inquiry.signature.server.service.ca.InquirySignatureCaAuthService;
import com.xyy.saas.inquiry.signature.server.service.fdd.FddSignTaskBussService;
import com.xyy.saas.inquiry.signature.server.service.fdd.dto.FddSignTaskCreateDto;
import com.xyy.saas.inquiry.signature.server.service.person.InquirySignaturePersonService;
import com.xyy.saas.inquiry.signature.server.service.prescription.InquirySignaturePrescriptionService;
import com.xyy.saas.inquiry.signature.server.service.prescriptiontemplate.InquiryPrescriptionTemplateService;
import com.xyy.saas.inquiry.signature.server.service.signature.InquirySignatureContractService;
import jakarta.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author:chenxiaoyi
 * @Date:2025/02/20 11:07
 */
@Component
@Slf4j
public class FddSyncSignaturePlatformStrategy implements SyncSignaturePlatformStrategy {

    public SignaturePlatformEnum getPlatform() {
        return SignaturePlatformEnum.FDD;
    }

    @Resource
    private InquirySignatureContractService inquirySignatureContractService;

    @Resource
    private InquirySignaturePrescriptionService inquirySignaturePrescriptionService;

    @Resource
    private InquirySignaturePersonService inquirySignaturePersonService;

    @Resource
    private InquirySignatureCaAuthService inquirySignatureCaAuthService;

    @Resource
    private InquiryPrescriptionTemplateService inquiryPrescriptionTemplateService;

    @Autowired
    private FddSignTaskBussService fddSignTaskBussService;

    /**
     * 同步签章平台
     *
     * @param signatureContract
     */
    @Lock4j(keys = "'" + RedisKeyConstants.SIGNATURE_PRESCRIPTION_SYNC_PLATFORM_LOCK + "'.concat(#signatureContract.pref)")
    public void syncSignaturePlatform(InquirySignatureContractDO signatureContract) {
        // 同步签章平台
        SyncSignaturePlatformResultDto resultDto = syncSignature(signatureContract);

        inquirySignatureContractService.updateSignatureContractStatus(InquirySignatureContractStatusVO.builder()
            .pref(signatureContract.getPref()).thirdId(resultDto.getThirdId())
            .syncPlatformStatus(resultDto.getSyncPlatformStatus())
            .syncPlatform(getPlatform().getCode())
            .ext(signatureContract.extGet().setRemark(resultDto.getRemark()))
            .build());
    }

    private SyncSignaturePlatformResultDto syncSignature(InquirySignatureContractDO signatureContract) {
        SyncSignaturePlatformResultDto resultDto = SyncSignaturePlatformResultDto.builder().syncPlatform(SignaturePlatformEnum.FDD.getCode()).build();
        // 筛选免签user
        Set<ParticipantItem> participantItems = signatureContract.getParticipants().stream().filter(p -> CommonStatusEnum.isEnable(p.getAccessPlatform())).collect(Collectors.toSet());

        List<Long> caUserIds = inquirySignatureCaAuthService.queryByUserIds(CollectionUtils.convertList(participantItems, ParticipantItem::getUserId), getPlatform())
            .stream().filter(c -> FddCaConstantEnum.isFreeSignAndValid(c.getAuthorizeFreeSignStatus(), c.getAuthorizeFreeSignDdl())).map(InquirySignatureCaAuthRespVO::getUserId).toList();
        if (CollUtil.isEmpty(caUserIds)) {
            return resultDto.setSyncPlatformStatus(SignatureSyncPlatformStatusEnum.SYNC_FAIL.getCode()).setRemark("需要同步的参与方未免签");
        }

        Map<Long, InquirySignaturePersonDO> personMap = inquirySignaturePersonService.queryPersonByUserIds(caUserIds, getPlatform(),
                Optional.ofNullable(signatureContract.extGet().getPlatformConfigId()).orElse(SignatureAppConfigIdEnum.DEFAULT.getCode()))
            .stream().collect(Collectors.toMap(InquirySignaturePersonDO::getUserId, Function.identity(), (a, b) -> b));
        if (CollUtil.isEmpty(personMap)) {
            return resultDto.setSyncPlatformStatus(SignatureSyncPlatformStatusEnum.SYNC_FAIL.getCode()).setRemark("参与方三方用户不存在");
        }

        // 需要追加的参与方Participant
        List<ParticipantItem> items = participantItems.stream()
            .filter(p -> caUserIds.contains(p.getUserId()) && personMap.containsKey(p.getUserId()))
            .sorted(Comparator.comparing(ParticipantItem::getSort)).toList();
        if (CollUtil.isEmpty(items) || !Objects.equals(items.getFirst().getUserId(), signatureContract.getInitiatorUserId())) {
            return resultDto.setSyncPlatformStatus(SignatureSyncPlatformStatusEnum.SYNC_FAIL.getCode()).setRemark("发起方CA无效或没有可同步的参与方");
        }

        // 同步发起方
        syncInitiator(signatureContract, items.getFirst(), personMap.get(items.getFirst().getUserId()));

        // 同步参与方
        String msg = syncParticipant(signatureContract, items, personMap);
        if (StringUtils.isNotBlank(msg)) {
            return resultDto.setSyncPlatformStatus(SignatureSyncPlatformStatusEnum.SYNC_FAIL.getCode()).setRemark(msg);
        }

        // 判断是否同步完参与方节点
        List<String> parts = signatureContract.getParticipants().stream().map(ParticipantItem::getActorField).toList();
        List<PrescriptionTemplateField> platformFields = inquiryPrescriptionTemplateService.getTemplateAccessPlatformFields(signatureContract.getTemplateIdLong());
        boolean signFinished = platformFields.stream().allMatch(f -> parts.contains(f.getField()));

        log.info("【处方同步FDD平台】,处方号:{},parts:{},platformFields:{},signFinished:{}", signatureContract.getBizId(), parts, platformFields, signFinished);

        resultDto.setSyncPlatformStatus(signFinished ? SignatureSyncPlatformStatusEnum.SYNCED.getCode() : SignatureSyncPlatformStatusEnum.SYNCING.getCode());
        return resultDto;
    }

    private String syncParticipant(InquirySignatureContractDO signatureContract, List<ParticipantItem> items, Map<Long, InquirySignaturePersonDO> personMap) {
        if (StringUtils.isBlank(signatureContract.getThirdId())) {
            return "处方合同创建失败,不予同步其他";
        }

        if (CollUtil.size(items) > 1) {
            CommonResult<SignTaskDetailRes> signTaskDetail = fddSignTaskBussService.getSignTaskDetail(signatureContract.extGet().getPlatformConfigId(), signatureContract.getThirdId());
            if (signTaskDetail.isError()) {
                throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), signTaskDetail.getMsg());
            }
            for (ParticipantItem participantItem : items) {
                // 参与方存在直接跳过
                if (signTaskDetail.getData().getActors().stream().anyMatch(a -> a.getActorInfo() != null &&
                    StringUtils.equalsIgnoreCase(a.getActorInfo().getActorId(), participantItem.getActorField()))) {
                    log.info("【处方同步FDD平台】同步追加处方合同参与方跳过:处方号:{},participantItem:{}", signatureContract.getBizId(), participantItem);
                    continue;
                }
                FddSignTaskCreateDto taskCreateDto = InquiryFddConvert.INSTANCE.convertByContractAddSync(signatureContract, participantItem, signTaskDetail, personMap.get(participantItem.getUserId()));
                // 添加控件 并追加参与方
                fddSignTaskBussService.addField(taskCreateDto.setSignTaskId(signatureContract.getThirdId()));
                CommonResult<?> addActorRes = fddSignTaskBussService.addActor(taskCreateDto.setSignTaskId(signatureContract.getThirdId()));
                log.info("【处方同步FDD平台】同步追加处方合同参与方结果:处方号:{},res:{}", signatureContract.getBizId(), addActorRes);
                if (addActorRes.isError()) {
                    return "追加参与方失败:" + addActorRes.getMsg();
                }
            }
        }
        return "";
    }

    private void syncInitiator(InquirySignatureContractDO signatureContract, ParticipantItem item, InquirySignaturePersonDO personDO) {
        // 存在平台id不处理
        if (StringUtils.isNotBlank(signatureContract.getThirdId()) || personDO == null || StringUtils.isBlank(personDO.getOpenUserId())) {
            return;
        }
        String pdfUrl = inquirySignaturePrescriptionService.drawnPrescriptionSelf(signatureContract.getPref(), false);
        if (StringUtils.isBlank(pdfUrl)) {
            throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), "处方笺自绘图片为空");
        }
        FddSignTaskCreateDto taskCreateDto = InquiryFddConvert.INSTANCE.convertByContractSync(signatureContract, item, personDO, pdfUrl);
        CommonResult<CreateSignTaskRes> cstResult = fddSignTaskBussService.createWithFile(taskCreateDto);
        log.info("【处方同步FDD平台】处方签章,createWithFile:处方号:{},res:{}", signatureContract.getBizId(), cstResult.getMsg());
        if (cstResult.isError()) {
            throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), cstResult.getMsg());
        }

        inquirySignatureContractService.updateSignatureContractStatus(InquirySignatureContractStatusVO.builder()
            .pref(signatureContract.getPref()).thirdId(cstResult.getData().getSignTaskId())
            .syncPlatformStatus(SignatureSyncPlatformStatusEnum.SYNCING.getCode())
            .syncPlatform(getPlatform().getCode()).build());

        signatureContract.setThirdId(cstResult.getData().getSignTaskId());
    }

}
