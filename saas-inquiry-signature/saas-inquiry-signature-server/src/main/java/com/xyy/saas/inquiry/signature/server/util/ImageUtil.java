package com.xyy.saas.inquiry.signature.server.util;

import cn.hutool.core.img.Img;
import cn.hutool.core.img.ImgUtil;
import cn.hutool.core.util.NumberUtil;
import java.awt.AlphaComposite;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.RenderingHints;
import java.awt.Transparency;
import java.awt.geom.AffineTransform;
import java.awt.image.AffineTransformOp;
import java.awt.image.BufferedImage;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;

/**
 * desc
 *
 * <AUTHOR>
 */
@Slf4j
public class ImageUtil {


    /**
     * 合并图片（完整参数版）
     *
     * @param destFile      目标文件，合并后的图片保存路径
     * @param sourceUrl     原图URL
     * @param mergeImageUrl 需要合并的图片URL
     * @param alpha         透明度 (0.0-1.0)
     * @param x             合并图片的x坐标（左上角为原点），null表示居中
     * @param y             合并图片的y坐标（左上角为原点），null表示居中
     * @param offset        垂直偏移比例 (-1.0到1.0)，仅当x/y为null时有效
     * @param width         合并图片的缩放宽度，null表示不缩放
     * @param height        合并图片的缩放高度，null表示不缩放
     * @return 合并后的图片文件，失败返回null
     */
    public static File mergeImage(File destFile, String sourceUrl, String mergeImageUrl,
        float alpha, Integer x, Integer y,
        Double offset, Integer width, Integer height, Double scaleRatio) {
        return mergeImageInternal(destFile, sourceUrl, mergeImageUrl,
            alpha, x, y, offset, width, height, scaleRatio);
    }

    /**
     * 合并图片（简化版，不带偏移参数）
     *
     * @param destFile      目标文件
     * @param sourceUrl     原图URL
     * @param mergeImageUrl 需要合并的图片URL
     * @param alpha         透明度 (0.0-1.0)
     * @param x             合并图片的x坐标（左上角为原点）
     * @param y             合并图片的y坐标（左上角为原点）
     * @param width         合并图片的缩放宽度
     * @param height        合并图片的缩放高度
     * @return 合并后的图片文件，失败返回null
     */
    public static File mergeImage(File destFile, String sourceUrl, String mergeImageUrl,
        float alpha, Integer x, Integer y,
        Integer width, Integer height) {
        return mergeImageInternal(destFile, sourceUrl, mergeImageUrl,
            alpha, x, y, null, width, height, null);
    }

    /**
     * 合并图片（居中版，带偏移参数）
     *
     * @param destFile      目标文件
     * @param sourceUrl     原图URL
     * @param mergeImageUrl 需要合并的图片URL
     * @param alpha         透明度 (0.0-1.0)
     * @param offset        垂直偏移比例 (-1.0到1.0)
     * @param width         合并图片的缩放宽度
     * @param height        合并图片的缩放高度
     * @return 合并后的图片文件，失败返回null
     */
    public static File mergeImage(File destFile, String sourceUrl, String mergeImageUrl,
        float alpha, Double offset,
        Integer width, Integer height) {
        return mergeImageInternal(destFile, sourceUrl, mergeImageUrl,
            alpha, null, null, offset, width, height, null);
    }

    /**
     * 合并图片（最简单版，仅指定位置）
     *
     * @param destFile      目标文件
     * @param sourceUrl     原图URL
     * @param mergeImageUrl 需要合并的图片URL
     * @param alpha         透明度 (0.0-1.0)
     * @param x             合并图片的x坐标
     * @param y             合并图片的y坐标
     * @return 合并后的图片文件，失败返回null
     */
    public static File mergeImage(File destFile, String sourceUrl, String mergeImageUrl,
        float alpha, Integer x, Integer y) {
        return mergeImageInternal(destFile, sourceUrl, mergeImageUrl,
            alpha, x, y, null, null, null, null);
    }

    /**
     * 合并图片（默认居中版）
     *
     * @param destFile      目标文件
     * @param sourceUrl     原图URL
     * @param mergeImageUrl 需要合并的图片URL
     * @param alpha         透明度 (0.0-1.0)
     * @return 合并后的图片文件，失败返回null
     */
    public static File mergeImage(File destFile, String sourceUrl, String mergeImageUrl,
        float alpha) {
        return mergeImageInternal(destFile, sourceUrl, mergeImageUrl,
            alpha, null, null, null, null, null, null);
    }


    /**
     * 合并图片
     *
     * @param destFile      目标文件
     * @param sourceUrl     原图
     * @param mergeImageUrl 合并图片
     * @param alpha         透明度
     * @param x             坐标x
     * @param y             坐标y
     * @param offset        针对 图片中心点 0,0的 偏移量
     * @param width         缩放宽
     * @param height        缩放高
     * @return 图片地址
     */
    private static File mergeImageInternal(File destFile, String sourceUrl, String mergeImageUrl, float alpha, Integer x, Integer y, Double offset, Integer width, Integer height, Double scaleRatio) {
        // x 和 y坐标都传 null 时,绘制在中心点 0,0
        boolean isCenter = x == null && y == null;

        x = Optional.ofNullable(x).orElse(0);
        y = Optional.ofNullable(y).orElse(0);

        // 1. 并行下载图片
        try (InputStream sourceStream = cn.hutool.http.HttpUtil.createGet(sourceUrl).executeAsync().bodyStream();
            InputStream mergeImageStream = cn.hutool.http.HttpUtil.createGet(mergeImageUrl).executeAsync().bodyStream();
        ) {
            // 2. 读取图片
            BufferedImage sourceImage = ImgUtil.read(sourceStream);
            int sourceWidth = sourceImage.getWidth();
            int sourceHeight = sourceImage.getHeight();

            BufferedImage mergeImage = ImgUtil.read(mergeImageStream);
            Image image = Img.from(mergeImage).getImg();

            if (width != null && height != null) {
                // 缩放签名图片
                image = Img.from(mergeImage).scale(width, height).getImg();
            } else if (height != null) {
                // 根据高度自动计算宽度，保持原图比例
                int originalWidth = mergeImage.getWidth();
                int originalHeight = mergeImage.getHeight();
                double ratio = (double) originalWidth / originalHeight;
                width = (int) (height * ratio);  // 根据高度计算宽度
                image = Img.from(mergeImage).scale(width, height).getImg();
            } else if (width != null) {
                // 或者根据宽度自动计算高度，保持原图比例
                int originalWidth = mergeImage.getWidth();
                int originalHeight = mergeImage.getHeight();
                double ratio = (double) originalHeight / originalWidth;
                height = (int) (width * ratio);  // 根据宽度计算高度
                image = Img.from(mergeImage).scale(width, height).getImg();
            } else if (scaleRatio != null) {
                // 新增：当width和height都为空时，按比例缩放
                scaleRatio = Math.max(1.0, Math.min(100.0, scaleRatio)); // 限制范围1-100
                double actualRatio = scaleRatio / 100.0; // 转换为0.01-1.0的实际比例

                int targetHeight = (int) (sourceHeight * actualRatio);
                int originalWidth = mergeImage.getWidth();
                int originalHeight = mergeImage.getHeight();
                double imageRatio = (double) originalWidth / originalHeight;
                int targetWidth = (int) (targetHeight * imageRatio);

                // 边界保护：确保宽度不超出sourceImage的宽度
                if (targetWidth > sourceWidth) {
                    // 如果计算出的宽度超出边界，以sourceWidth为准重新计算高度
                    targetWidth = sourceWidth;
                    targetHeight = (int) (targetWidth / imageRatio);
                }

                image = Img.from(mergeImage).scale(targetWidth, targetHeight).getImg();
            }

            int mergeWidth = image.getWidth(null);
            int mergeHeight = image.getHeight(null);

            if (isCenter) {
                if (offset != null) {
                    // 限制offset范围在 [-1, 1] 之间
                    offset = NumberUtil.max(NumberUtil.min(offset, 1.0), -1.0);

                    // 关键修改点：计算最大可偏移量时考虑图片B的高度
                    // 确保当 offset=-1 时，图片B的下边缘与A的下边缘完全对齐
                    int maxYOffset = (sourceHeight - mergeHeight) / 2;

                    // 计算实际Y偏移量（Hutool坐标系Y轴向下为正）
                    y = (int) (-offset * maxYOffset);

                    // 边界保护：若计算结果导致图片B超出A的范围，强制对齐边缘
                    if (maxYOffset < 0) {
                        // 如果图片B高度大于A，直接贴底（特殊场景处理）
                        y = sourceHeight - mergeHeight;
                    }
                }
            } else {
                // 兼容 非中心点坐标,换算从sourceImage的左上角计算0,0 计算基于中心点的偏移量
                int centerXA = sourceWidth / 2;
                int centerYA = sourceHeight / 2;

                // 边界保护：确保B图中心点不会导致图片越界
                int minX = mergeWidth / 2 - centerXA;
                int maxX = centerXA - mergeWidth / 2; // 原图一半 - B图一半

                int minY = mergeHeight / 2 - centerYA;
                int maxY = centerYA - mergeHeight / 2; // 原图一半 - B图一半

                // 计算图片B中心点在图片A中的坐标
                x = Math.max(minX, Math.min(x - centerXA, maxX));
                y = Math.max(minY, Math.min(y - centerYA, maxY));
            }

            // 3. 创建处理对象
            Img img = Img.from(sourceImage);

            // 4. 添加合并图片
            img.pressImage(image, x, y, alpha);

            // 5. 添加用户签名
            img.write(destFile);

            return destFile;

        } catch (IOException e) {
            log.error("mergeImage处理失败,sourceUrl:{}", sourceUrl, e);
        }
        return null;

    }

    /**
     * 自己绘制 - 传入的所有坐标均以原图左上角为原点计算
     */
    // public static File mergeImageInternal1(File destFile, String sourceUrl, String mergeImageUrl, float alpha, Integer x, Integer y, Double offset, Integer width, Integer height) {
    //     // 1. 并行下载图片
    //     try (InputStream sourceStream = HttpUtil.createGet(sourceUrl).executeAsync().bodyStream();
    //         InputStream mergeImageStream = HttpUtil.createGet(mergeImageUrl).executeAsync().bodyStream()) {
    //
    //         // 2. 读取图片
    //         BufferedImage sourceImage = ImgUtil.read(sourceStream);
    //         BufferedImage mergeImage = ImgUtil.read(mergeImageStream);
    //
    //         // 3. 缩放处理（保持原比例）
    //         Image scaledImage = mergeImage;
    //         int originalMergeWidth = mergeImage.getWidth();
    //         int originalMergeHeight = mergeImage.getHeight();
    //
    //         if (width != null || height != null) {
    //             if (width != null && height != null) {
    //                 scaledImage = Img.from(mergeImage).scale(width, height).getImg();
    //             } else if (height != null) {
    //                 double ratio = (double) originalMergeWidth / originalMergeHeight;
    //                 width = (int) (height * ratio);
    //                 scaledImage = Img.from(mergeImage).scale(width, height).getImg();
    //             } else {
    //                 double ratio = (double) originalMergeHeight / originalMergeWidth;
    //                 height = (int) (width * ratio);
    //                 scaledImage = Img.from(mergeImage).scale(width, height).getImg();
    //             }
    //         }
    //
    //         // 获取缩放后的尺寸
    //         int mergeWidth = (scaledImage instanceof BufferedImage) ?
    //             ((BufferedImage) scaledImage).getWidth() : scaledImage.getWidth(null);
    //         int mergeHeight = (scaledImage instanceof BufferedImage) ?
    //             ((BufferedImage) scaledImage).getHeight() : scaledImage.getHeight(null);
    //
    //         int sourceWidth = sourceImage.getWidth();
    //         int sourceHeight = sourceImage.getHeight();
    //
    //         // 4. 坐标处理
    //         int drawX = 0;
    //         int drawY = 0;
    //
    //         if (x == null && y == null) {
    //             // 居中处理 - B图中心点对齐A图中心点
    //             int centerX = sourceWidth / 2;
    //             int centerY = sourceHeight / 2;
    //
    //             // 计算B图左上角坐标
    //             drawX = centerX - mergeWidth / 2;
    //             drawY = centerY - mergeHeight / 2;
    //
    //             // 偏移处理
    //             if (offset != null) {
    //                 // 限制偏移量在[-1,1]范围内
    //                 offset = Math.max(-1.0, Math.min(1.0, offset));
    //
    //                 // 计算最大可偏移量（上下各一半空间）
    //                 int maxYOffset = (sourceHeight - mergeHeight) / 2;
    //
    //                 // 应用偏移（向下偏移为正）
    //                 drawY += (int) (offset * maxYOffset);
    //             }
    //         } else {
    //             // 使用传入坐标作为B图中心点
    //             int centerX = Optional.ofNullable(x).orElse(0);
    //             int centerY = Optional.ofNullable(y).orElse(0);
    //
    //             // 计算B图左上角坐标
    //             drawX = centerX - mergeWidth / 2;
    //             drawY = centerY - mergeHeight / 2;
    //         }
    //
    //         // 5. 边界保护（确保不会绘制到可见区域外）
    //         drawX = Math.max(-mergeWidth + 1, Math.min(drawX, sourceWidth - 1));
    //         drawY = Math.max(-mergeHeight + 1, Math.min(drawY, sourceHeight - 1));
    //
    //         // 6. 创建目标图像
    //         BufferedImage target = new BufferedImage(
    //             sourceWidth,
    //             sourceHeight,
    //             BufferedImage.TYPE_INT_ARGB
    //         );
    //
    //         // 7. 绘制基础图像
    //         Graphics2D g = target.createGraphics();
    //
    //         // 设置渲染质量
    //         g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
    //         g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BILINEAR);
    //
    //         g.drawImage(sourceImage, 0, 0, null);
    //
    //         // 8. 设置透明度
    //         AlphaComposite alphaComposite = AlphaComposite.getInstance(AlphaComposite.SRC_OVER, alpha);
    //         g.setComposite(alphaComposite);
    //
    //         // 9. 绘制叠加图像（以中心点对齐）
    //         g.drawImage(scaledImage, drawX, drawY, null);
    //
    //         // 10. 释放资源
    //         g.dispose();
    //
    //         // 11. 保存结果
    //         ImgUtil.write(target, destFile);
    //         return destFile;
    //
    //     } catch (Exception e) {
    //         log.error("图片合成失败: sourceUrl={}, mergeImageUrl={}", sourceUrl, mergeImageUrl, e);
    //     }
    //     return null;
    // }


    /**
     * 拼接A B图片 以A高度为基准
     *
     * @param destFile
     * @param urlA
     * @param urlB
     * @return
     */
    public static File combinedABImage(File destFile, String urlA, String urlB) {

        try (InputStream sourceStream = cn.hutool.http.HttpUtil.createGet(urlA).executeAsync().bodyStream();
            InputStream mergeImageStream = cn.hutool.http.HttpUtil.createGet(urlB).executeAsync().bodyStream();
        ) {
            // 1. 读取图片
            BufferedImage imageA = ImgUtil.read(sourceStream);
            BufferedImage imageB = ImgUtil.read(mergeImageStream);

            // 2. 获取A图片高度作为基准
            int baseHeight = imageA.getHeight();

            // 3. 高质量缩放图片B（使用双三次插值算法）
            double ratio = (double) baseHeight / imageB.getHeight();
            int scaledWidth = (int) (imageB.getWidth() * ratio);

            // 4. 使用高质量缩放方法
            BufferedImage scaledImageB = scaleHighQuality(imageB, scaledWidth, baseHeight);

            // 5. 计算新图片尺寸
            int totalWidth = imageA.getWidth() + scaledImageB.getWidth();

            // 6. 创建透明背景的PNG图片
            BufferedImage combined = new BufferedImage(totalWidth, baseHeight, BufferedImage.TYPE_INT_ARGB);

            // 7. 高质量绘制图片
            Graphics2D g = combined.createGraphics();
            try {
                // 设置高质量渲染参数
                g.setRenderingHint(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);
                g.setRenderingHint(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);
                g.setRenderingHint(RenderingHints.KEY_INTERPOLATION, RenderingHints.VALUE_INTERPOLATION_BICUBIC);

                // 透明背景（PNG默认透明）
                g.setComposite(AlphaComposite.Src);

                // 绘制图片A（左侧）
                g.drawImage(imageA, 0, 0, null);

                // 绘制缩放后的图片B（右侧）
                g.drawImage(scaledImageB, imageA.getWidth(), 0, null);
            } finally {
                g.dispose(); // 确保释放图形资源
            }
            // 8. 保存为PNG格式
            ImgUtil.write(combined, destFile);

            log.info("图片拼接完成---------------");
        } catch (Exception e) {
            log.error("mergeImage处理失败,urlB:{}", urlB, e);
        }
        return destFile;
    }

    /**
     * 高性能高质量图片缩放方法（使用双三次插值）
     *
     * @param source 原始图片
     * @param width  目标宽度
     * @param height 目标高度
     * @return 缩放后的高清图片
     */
    private static BufferedImage scaleHighQuality(BufferedImage source, int width, int height) {
        // 1. 创建目标尺寸的图片（使用原始类型保持透明通道）
        int imageType = source.getTransparency() == Transparency.OPAQUE ?
            BufferedImage.TYPE_INT_RGB : BufferedImage.TYPE_INT_ARGB;

        BufferedImage dest = new BufferedImage(width, height, imageType);

        // 2. 创建双三次插值操作
        AffineTransform transform = AffineTransform.getScaleInstance(
            (double) width / source.getWidth(),
            (double) height / source.getHeight()
        );

        AffineTransformOp op = new AffineTransformOp(
            transform,
            AffineTransformOp.TYPE_BICUBIC // 使用双三次插值
        );

        // 3. 执行高质量缩放
        return op.filter(source, dest);
    }

    /**
     * 可选：快速缩放方法（在速度优先时使用）
     * <p>
     * 比双三次插值快2-3倍，质量稍低但可接受
     */
    private static BufferedImage scaleFast(BufferedImage source, int width, int height) {
        // 使用双线性插值平衡速度和质量
        BufferedImage dest = new BufferedImage(width, height, source.getType());
        Graphics2D g = dest.createGraphics();
        try {
            g.setRenderingHint(RenderingHints.KEY_INTERPOLATION,
                RenderingHints.VALUE_INTERPOLATION_BILINEAR);
            g.drawImage(source, 0, 0, width, height, null);
        } finally {
            g.dispose();
        }
        return dest;
    }

}
