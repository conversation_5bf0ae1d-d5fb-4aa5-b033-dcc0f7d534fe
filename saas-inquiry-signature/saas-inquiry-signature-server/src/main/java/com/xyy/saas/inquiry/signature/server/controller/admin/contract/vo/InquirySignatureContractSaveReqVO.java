package com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.enums.signature.ContractStatusEnum;
import com.xyy.saas.inquiry.pojo.prescription.ParticipantItem;
import com.xyy.saas.inquiry.pojo.signature.SignatureContractExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

@Schema(description = "管理后台 - 签章合同新增/修改 Request VO")
@Data
@Builder
@Accessors(chain = true)
public class InquirySignatureContractSaveReqVO {

    @Schema(description = "主键", example = "4478")
    private Long id;

    @Schema(description = "合同编号,系统生成")
    private String pref;

    @Schema(description = "签章平台  1-法大大")
    @NotNull(message = "签章平台  1-法大大不能为空")
    private Integer signaturePlatform;

    /**
     * {@link ContractStatusEnum}
     */
    @Schema(description = "合同状态", example = "2")
    private Integer contractStatus;

    /**
     * 自绘合同标识  0-是,1-否 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    @Schema(description = "自绘合同标识  0-是,1-否")
    private Integer selfDrawn;

    /**
     * {@link com.xyy.saas.inquiry.enums.signature.ContractTypeEnum}
     */
    @Schema(description = "合同业务类型", example = "2")
    private Integer contractType;

    @Schema(description = "业务方唯一标识", example = "2369")
    private String bizId;

    @Schema(description = "三方签署任务id signTaskId", example = "10496")
    private String thirdId;

    @Schema(description = "模板id", example = "10496")
    private String templateId;

    @Schema(description = "发起方uerId", example = "10496")
    private Long initiatorUserId;

    @Schema(description = "发起方姓名", example = "芋艿")
    private String initiatorName;

    @Schema(description = "发起方联系方式")
    private String initiatorMobile;


    @Schema(description = "参与方集合")
    private List<ParticipantItem> participants;

    @Schema(description = "合同参数详情")
    private String paramDetail;

    @Schema(description = "合同图片")
    private String imgUrl;

    @Schema(description = "合同PDF文件")
    private String pdfUrl;

    @Schema(description = "ext")
    @TableField(typeHandler = JsonTypeHandler.class)
    private SignatureContractExtDto ext;
}