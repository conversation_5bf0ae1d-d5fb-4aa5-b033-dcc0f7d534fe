package com.xyy.saas.inquiry.signature.server.controller.admin.platform.vo;

import com.xyy.saas.inquiry.pojo.signature.PlatformConfigExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Schema(description = "管理后台 - 签章平台配置新增/修改 Request VO")
@Data
public class InquirySignaturePlatformSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "19776")
    private Long id;

    @Schema(description = "签章平台 0-自签署 1-法大大", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "签章平台 0-自签署 1-法大大不能为空")
    private Integer signaturePlatform;

    @Schema(description = "属性名 eg:私有云地址 对应枚举", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String paramName;

    @Schema(description = "属性值 eg:8000809", requiredMode = Schema.RequiredMode.REQUIRED)
    private String paramValue;

    @Schema(description = "描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "你猜")
    @NotEmpty(message = "描述不能为空")
    private String description;

    /**
     * 平台应用扩展信息
     */
    private List<PlatformConfigExtDto> ext;

}