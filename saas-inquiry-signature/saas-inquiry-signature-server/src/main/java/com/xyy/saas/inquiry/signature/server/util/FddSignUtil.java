package com.xyy.saas.inquiry.signature.server.util;

import com.fasc.open.api.utils.crypt.FddCryptUtil;
import com.xyy.saas.inquiry.signature.server.service.fdd.dto.AuthCallback2RedirectDto;
import com.xyy.saas.inquiry.signature.server.service.fdd.dto.EventCallbackHeaderDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Slf4j
public class FddSignUtil {

    /**
     * 授权redirectUrl回调-签名验证
     *
     * @param appSecret
     * @param dto
     * @return
     */
    public static boolean isSignatureValid(String appSecret, AuthCallback2RedirectDto dto) {
        if (StringUtils.isBlank(appSecret) || dto == null) {
            return false;
        }
        // Unix标准时间戳，该值需要拼接到重定向redirectUrl上
        String timestamp = dto.getTimestamp();
        // 参与签名计算的Key-Value列表
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("timestamp", timestamp);
        paramMap.put("clientUserId", dto.getClientUserId());
        paramMap.put("openUserId", dto.getOpenUserId());
        paramMap.put("authResult", dto.getAuthResult());
        // 参数值为空，则传空串
        paramMap.put("authFailedReason", StringUtils.defaultString(dto.getAuthFailedReason()));
        paramMap.put("authScope", StringUtils.defaultString(dto.getAuthScope()));
        // 得到排序后的字符串，FddCryptUtil为法大大提供得签名工具类
        String paramToSignStr = FddCryptUtil.sortParameters(paramMap);
        // 计算之后得到签名，该签名需要拼接到重定向redirectUrl上
        try {
            String signature = FddCryptUtil.sign(paramToSignStr, timestamp, appSecret);
            return Objects.equals(signature, dto.getSignature());
        } catch (Exception e) {
            log.error("法大大签名校验异常: {}", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 事件回调
     *
     * @param appSecret
     * @param dto
     * @return
     */
    public static boolean isSignatureValid(String appSecret, EventCallbackHeaderDto dto) {
        if (StringUtils.isBlank(appSecret) || dto == null) {
            return false;
        }
        String timestamp = dto.getTimestamp();
        //验签
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("X-FASC-App-Id", dto.getAppId());
        paramMap.put("X-FASC-Sign-Type", "HMAC-SHA256");
        paramMap.put("X-FASC-Timestamp", timestamp);
        paramMap.put("X-FASC-Nonce", dto.getNonce());
        paramMap.put("X-FASC-Event", dto.getEvent());
        paramMap.put("bizContent", dto.getBizContent());
        // 得到排序后的字符串，FddCryptUtil为法大大提供得签名工具类
        String paramToSignStr = FddCryptUtil.sortParameters(paramMap);
        // 计算之后得到签名，该签名需要拼接到重定向redirectUrl上
        try {
            String signature = FddCryptUtil.sign(paramToSignStr, timestamp, appSecret);
            return Objects.equals(signature, dto.getSign());
        } catch (Exception e) {
            log.error("法大大签名校验异常: {}", e.getMessage(), e);
            return false;
        }
    }
}
