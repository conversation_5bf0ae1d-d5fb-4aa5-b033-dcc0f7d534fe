package com.xyy.saas.inquiry.signature.server.mq.consumer.user;

import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.mq.user.UserBaseInfoChangeEvent;
import com.xyy.saas.inquiry.signature.server.service.ca.InquirySignatureCaAuthService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Desc 用户信息修改, 处理CA重置三要素
 * <AUTHOR>
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_signature_server_mq_consumer_user_UserBaseInfoChangeCaMQConsumer",
    topic = UserBaseInfoChangeEvent.TOPIC)
public class UserBaseInfoChangeCaMQConsumer {

    @Resource
    private InquirySignatureCaAuthService inquirySignatureCaAuthService;

    @Resource
    private AdminUserApi adminUserApi;

    @EventBusListener
    public void caUserBaseInfoUpdateMQConsumer(UserBaseInfoChangeEvent userBaseInfoChangeEvent) {
        // UserBaseInfoDto userBaseInfoDto = userBaseInfoChangeEvent.getMsg();
        // if (userBaseInfoDto == null || userBaseInfoDto.getUserId() == null) {
        //     return;
        // }
        // Long tenantId = Optional.ofNullable(userBaseInfoDto.getTenantId()).orElse(TenantContextHolder.getTenantId());
        // AdminUserRespDTO userBaseInfo = TenantUtils.execute(tenantId, () -> adminUserApi.getUserBaseInfo(userBaseInfoDto.getUserId()));
        // // 判断三要素是否修改，修改了则重置CA认证状态
        // if (!StringUtils.equals(userBaseInfo.getNickname(), userBaseInfoDto.getNickname())
        //     || !StringUtils.equals(userBaseInfo.getIdCard(), userBaseInfoDto.getIdCard())
        //     || !StringUtils.equals(userBaseInfo.getMobile(), userBaseInfoDto.getMobile())) {
        //     log.info("用户三要素修改,重置CA认证数据,userId:{},nickName:{}", userBaseInfo.getId(), userBaseInfoDto.getNickname());
        //     inquirySignatureCaAuthService.resetCaAuth(userBaseInfo.getId());
        // }
    }

}
