package com.xyy.saas.inquiry.signature.server.service.fdd;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.fasc.open.api.v5_1.client.DocClient;
import com.fasc.open.api.v5_1.req.doc.FileProcessReq;
import com.fasc.open.api.v5_1.req.doc.GetUploadUrlReq;
import com.fasc.open.api.v5_1.req.doc.UploadFileByUrlReq;
import com.fasc.open.api.v5_1.res.doc.FileProcessRes;
import com.fasc.open.api.v5_1.res.doc.GetUploadUrlRes;
import com.fasc.open.api.v5_1.res.doc.UploadFileByUrlRes;
import com.xyy.saas.inquiry.signature.server.service.fdd.bean.FddBaseReqDto;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 法大大 文件相关service
 *
 * @Author:chenxiaoyi
 * @Date:2024/02/23 18:36
 */
@Component
@Slf4j
public class FddDocService extends FddBaseService {

    private static final Map<Integer, DocClient> docClientMap = new HashMap<>();

    private DocClient getDocClient(Integer configId) {
        if (docClientMap.get(configId) == null) {
            synchronized (FddDocService.class) {
                if (docClientMap.get(configId) == null) {
                    docClientMap.put(configId, new DocClient(getOpenApiClient(configId)));
                }
            }
        }
        return docClientMap.get(configId);
    }

    /**
     * 通过网络文件地址上传
     *
     * @param fddBaseReqDto
     * @return
     */
    public CommonResult<UploadFileByUrlRes> uploadFileByUrl(FddBaseReqDto<UploadFileByUrlReq> fddBaseReqDto) {
        return execute(getDocClient(fddBaseReqDto.getConfigId())::uploadFileByUrl, fddBaseReqDto, "通过网络文件地址上传");
    }

    /**
     * 上传本地文件
     *
     * @param fddBaseReqDto
     * @return
     */
    public CommonResult<GetUploadUrlRes> getUploadFileUrl(FddBaseReqDto<GetUploadUrlReq> fddBaseReqDto) {
        return execute(getDocClient(fddBaseReqDto.getConfigId())::getUploadFileUrl, fddBaseReqDto, "上传本地文件");
    }

    /**
     * 文件处理
     *
     * @param fddBaseReqDto
     * @return
     */
    public CommonResult<FileProcessRes> process(FddBaseReqDto<FileProcessReq> fddBaseReqDto) {
        return execute(getDocClient(fddBaseReqDto.getConfigId())::process, fddBaseReqDto, "文件处理");
    }

}
