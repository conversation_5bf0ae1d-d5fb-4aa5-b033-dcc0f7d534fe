package com.xyy.saas.inquiry.signature.server.controller.admin.person.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 签章平台用户分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquirySignaturePersonPageReqVO extends PageParam {

    @Schema(description = "用户uid", example = "10984")
    private Long userId;

    @Schema(description = "法大大平台为该用户在该应用appId范围内分配的唯一标识", example = "7810")
    private String openUserId;
    /**
     * 签章平台  0-无 1-法大大 {@link com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum}
     */
    private Integer signaturePlatform;

    /**
     * 签章平台应用标识id
     */
    private Integer signaturePlatformConfigId;

    @Schema(description = "个人用户的法大大帐号，仅限手机号或邮箱", example = "赵六")
    private String accountName;

    @Schema(description = "个人用户真实姓名", example = "李四")
    private String userName;

    @Schema(description = "个人手机号")
    private String mobile;

    @Schema(description = "个人银行账户号")
    private String bankAccountNo;

    @Schema(description = "证件类型 ：id_card: 身份证;passport: 护照;hk_macao: 港澳居民来往内地通行证;taiwan: 台湾居民来往大陆通行证 ", example = "2")
    private String userIdentType;

    @Schema(description = "证件号。跟证件类型关联")
    private String userIdentNo;

    @Schema(description = "法大大签名印章ID", example = "3985")
    private String sealId;

    @Schema(description = "0-未认证,1-已认证,3已设置签名", example = "2")
    private Integer userStatus;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}