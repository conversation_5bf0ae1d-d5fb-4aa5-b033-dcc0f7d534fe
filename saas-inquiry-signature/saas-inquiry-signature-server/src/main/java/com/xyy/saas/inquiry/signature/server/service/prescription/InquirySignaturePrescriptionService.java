package com.xyy.saas.inquiry.signature.server.service.prescription;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureAuditDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureInitDto;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquirySignatureContractDO;
import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionSignatureMessage;
import jakarta.validation.Valid;

/**
 * 问诊处方签章service
 *
 * @Author:chenxiaoyi
 * @Date:2024/11/26 17:47
 */
public interface InquirySignaturePrescriptionService {

    /**
     * 开具(签发)处方合同(基于文档)
     *
     * @param prescriptionSignatureInitDto 处方签章dto
     */
    CommonResult<?> issuePrescription(@Valid PrescriptionSignatureInitDto prescriptionSignatureInitDto);

    /**
     * 审核处方笺
     *
     * @param prescriptionSignatureAuditDto 审核dto
     */
    CommonResult<?> auditPrescription(@Valid PrescriptionSignatureAuditDto prescriptionSignatureAuditDto);

    /**
     * 处方签章callback统一入口
     *
     * @param psMessageDto 处方消息
     */
    void signaturePrescriptionCallback(PrescriptionSignatureMessage psMessageDto);


    /**
     * 根据当前合同id自绘处方笺
     *
     * @param contractPref 合同pref
     * @param drawnSign    是否绘制签名
     * @return 处方pdf图片地址
     */
    String drawnPrescriptionSelf(String contractPref, boolean drawnSign);

    /**
     * 绘制合同
     *
     * @param signatureContract
     * @param drawnSign
     * @return
     */
    String drawnContract(InquirySignatureContractDO signatureContract, boolean drawnSign);
}
