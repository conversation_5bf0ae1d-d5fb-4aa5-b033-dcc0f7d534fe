package com.xyy.saas.inquiry.signature.server.controller.app.ca.vo;

import com.xyy.saas.inquiry.enums.signature.SignatureAppConfigIdEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Schema(description = "管理后台 - CA认证新增/修改 Request VO")
@Data
public class InquirySignatureCaAuthSaveReqVO {

    @Schema(description = "主键", example = "32662")
    private Long id;

    @Schema(description = "用户id", example = "16240")
    private Long userId;

    @Schema(description = "签章平台 0-自签署 1-法大大")
    private Integer signaturePlatform;

    @Schema(description = "签名图片base64", example = "16240")
    private String imgBase64;

    @Schema(description = "前端重定向地址", example = "16240")
    private String redirectUrl;

    @Schema(description = "应用配置id", example = "16240")
    private Integer signaturePlatformConfigId = SignatureAppConfigIdEnum.DEFAULT.getCode();


}