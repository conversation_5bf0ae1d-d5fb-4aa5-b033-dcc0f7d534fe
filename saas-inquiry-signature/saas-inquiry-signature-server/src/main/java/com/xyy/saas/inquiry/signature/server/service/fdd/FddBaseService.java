package com.xyy.saas.inquiry.signature.server.service.fdd;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.alibaba.fastjson.JSON;
import com.fasc.open.api.bean.base.BaseReq;
import com.fasc.open.api.bean.base.BaseRes;
import com.fasc.open.api.exception.ApiException;
import com.fasc.open.api.utils.ResultUtil;
import com.fasc.open.api.v5_1.client.OpenApiClient;
import com.fasc.open.api.v5_1.client.ServiceClient;
import com.fasc.open.api.v5_1.res.service.AccessTokenRes;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.pojo.signature.PlatformConfigExtDto;
import com.xyy.saas.inquiry.signature.server.service.fdd.bean.FddBaseReqDto;
import com.xyy.saas.inquiry.signature.server.service.fdd.bean.FddFunction;
import com.xyy.saas.inquiry.signature.server.service.platform.InquirySignaturePlatformService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.concurrent.TimeUnit;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;


/**
 * 法大大基础service
 *
 * @Author:chenxiaoyi
 * @Date:2024/02/23 18:32
 */
@Component
@Slf4j
public class FddBaseService {

    @Resource
    private RedisTemplate<String, String> redisTemplate;


    @Resource
    private InquirySignaturePlatformService inquirySignaturePlatformService;

    private static final Map<Integer, OpenApiClient> openApiClientMap = new HashMap<>();

    public OpenApiClient getOpenApiClient(Integer configId) {
        if (openApiClientMap.get(configId) == null) {
            PlatformConfigExtDto platformConfig = inquirySignaturePlatformService.getSignaturePlatformConfig(SignaturePlatformEnum.FDD.getCode(), configId);
            openApiClientMap.put(configId, new OpenApiClient(platformConfig.getAppId(), platformConfig.getAppSecret(), platformConfig.getPrivateUrl()));
        }
        return openApiClientMap.get(configId);
    }

    @PostConstruct
    public void init() {
        if (CollUtil.isNotEmpty(openApiClientMap)) {
            return;
        }
        List<PlatformConfigExtDto> platformConfigExtDtos = inquirySignaturePlatformService.getSignaturePlatform(SignaturePlatformEnum.FDD.getCode());
        if (CollUtil.isEmpty(platformConfigExtDtos)) {
            log.error("法大大项目配置缓存未加载!!!");
            return;
        }
        for (PlatformConfigExtDto platformConfig : platformConfigExtDtos) {
            openApiClientMap.put(platformConfig.getConfigId(), new OpenApiClient(platformConfig.getAppId(), platformConfig.getAppSecret(), platformConfig.getPrivateUrl()));
        }
    }

    /**
     * token 缓存key  有前缀
     */
    private static final String ACCESS_TOKEN_KEY = "ACCESS_TOKEN";
    /**
     * accessToken的有效期默认为7200秒(2小时), 这里设置小一点
     */
    private static final int ACCESS_TOKEN_EXP_SECOND = 7000;
    private static final int ACCESS_TOKEN_DIFF_SECOND = 200;

    /**
     * 获取token redis 控制过期时间
     *
     * @return
     * @throws ApiException
     */
    public String getAccessToken(Integer configId) throws ApiException {
        // 单个应用 ???
        // 如果是多个应用, 这里的key需要带上appId
        String key = ACCESS_TOKEN_KEY.concat(Optional.ofNullable(configId).orElse(0).toString());
        String token = redisTemplate.opsForValue().get(key);
        if (StringUtils.isBlank(token)) {
            ServiceClient serviceClient = new ServiceClient(getOpenApiClient(configId));
            BaseRes<AccessTokenRes> res = serviceClient.getAccessToken();
            ResultUtil.printLog(res, getOpenApiClient(configId).getJsonStrategy());
            AccessTokenRes resData = res.getData();
            // 获取token 设置缓存
            token = resData.getAccessToken();
            // long expireIn = NumberUtils.toLong(resData.getExpiresIn(), 0L);
            // int expiresTime = (int) (expireIn - System.currentTimeMillis() / 1000);
            // if (expiresTime < ACCESS_TOKEN_EXP_SECOND) {
            //     log.warn("accessToken的有效期: {}s, 截止时间: {}, ", expiresTime, resData.getExpiresIn());
            // }
            // // 过期时间保留余量: 200秒
            // expiresTime = expiresTime - ACCESS_TOKEN_DIFF_SECOND;
            // if (expiresTime < 1) {
            //     log.error("accessToken的有效期错误: {}s, 跳过缓存设置", expiresTime);
            //     return token;
            // }
            redisTemplate.opsForValue().set(key, token, ACCESS_TOKEN_EXP_SECOND, TimeUnit.SECONDS);
            log.error("accessToken:{}的有效期: {}s, 缓存设置", key, ACCESS_TOKEN_EXP_SECOND);
        }
        return token;
    }

    /**
     * 法大大通用执行 方法
     *
     * @param function 法大大具体方法
     * @param t        具体入参 继承BaseReq 需要设置 accessToken
     * @param desc     方法说明
     * @param <T>      入参类型
     * @param <R>      出参类型 (传进来)
     * @return ResultVO<R>
     */
    public <T extends BaseReq, R> CommonResult<R> execute(FddFunction<T, BaseRes<R>> function, FddBaseReqDto<T> fddBaseReqDto, String desc) {
        log.info("法大大-{},入参:{}", desc, JSON.toJSONString(fddBaseReqDto));
        try {
            fddBaseReqDto.getData().setAccessToken(getAccessToken(fddBaseReqDto.getConfigId()));
            BaseRes<R> apply = function.apply(fddBaseReqDto.getData());
            CommonResult<R> resultVO = new CommonResult<>();
            resultVO.setData(apply.getData());
            resultVO.setMsg(apply.getMsg());
            resultVO.setCode(apply.isSuccess() ? GlobalErrorCodeConstants.SUCCESS.getCode() : GlobalErrorCodeConstants.FAIL.getCode());
            log.info("法大大-{}成功,出参:{}", desc, JSON.toJSONString(resultVO));
            // 特殊处理
            if (resultVO.getData() == null && resultVO.isSuccess()) {
                resultVO.setCode(GlobalErrorCodeConstants.FAIL.getCode());
                log.error("法大大-{}成功,出参 code 转换!", desc);
            }
            return resultVO;
        } catch (Exception e) {
            log.info("法大大-{}失败,入参:{},msg{}", desc, JSON.toJSONString(fddBaseReqDto), e.getMessage());
            return CommonResult.error(500, e.getMessage());
        }
    }

    public <T extends BaseReq> CommonResult<Void> executeVoid(FddFunction<T, BaseRes<Void>> function, FddBaseReqDto<T> fddBaseReqDto, String desc) {
        log.info("法大大Void-{},入参:{}", desc, JSON.toJSONString(fddBaseReqDto));
        try {
            fddBaseReqDto.getData().setAccessToken(getAccessToken(fddBaseReqDto.getConfigId()));
            BaseRes<Void> apply = function.apply(fddBaseReqDto.getData());
            log.info("法大大Void-{}成功,出参:{}", desc, JSON.toJSONString(apply));
            if (!apply.isSuccess()) {
                return CommonResult.error(GlobalErrorCodeConstants.FAIL.getCode(), apply.getMsg());
            }
            return CommonResult.success(apply.getData());
        } catch (Exception e) {
            log.info("法大大Void-{}失败,入参:{},msg{}", desc, JSON.toJSONString(fddBaseReqDto), e.getMessage());
            return CommonResult.error(GlobalErrorCodeConstants.FAIL.getCode(), e.getMessage());
        }
    }

}
