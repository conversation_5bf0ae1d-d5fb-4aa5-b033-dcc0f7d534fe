package com.xyy.saas.inquiry.signature.server.api.ca;

import cn.hutool.core.collection.CollUtil;
import com.xyy.saas.inquiry.enums.signature.ContractStatusEnum;
import com.xyy.saas.inquiry.enums.signature.SignatureBizTypeEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.signature.api.ca.InquirySignatureCaAuthApi;
import com.xyy.saas.inquiry.signature.api.ca.dto.InquirySignatureCaAuthRespDto;
import com.xyy.saas.inquiry.signature.api.ca.dto.SyncCreateCaDto;
import com.xyy.saas.inquiry.signature.server.controller.admin.person.vo.InquirySignaturePersonSaveReqVO;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.InquirySignatureCaAuthRespVO;
import com.xyy.saas.inquiry.signature.server.controller.app.signature.vo.InquiryUserSignatureInformationVO;
import com.xyy.saas.inquiry.signature.server.convert.ca.InquirySignatureCaAuthConvert;
import com.xyy.saas.inquiry.signature.server.convert.person.InquirySignaturePersonConvert;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.ca.InquirySignatureCaAuthDO;
import com.xyy.saas.inquiry.signature.server.service.ca.InquirySignatureCaAuthService;
import com.xyy.saas.inquiry.signature.server.service.person.InquirySignaturePersonService;
import com.xyy.saas.inquiry.signature.server.service.signature.InquiryUserSignatureInformationService;
import jakarta.annotation.Resource;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.transaction.annotation.Transactional;
import java.util.List;

/**
 * @Author:chenxiaoyi
 * @Date:2025/02/20 19:34
 */
@DubboService
public class InquirySignatureCaAuthApiImpl implements InquirySignatureCaAuthApi {

    @Resource
    private InquirySignatureCaAuthService inquirySignatureCaAuthService;

    @Resource
    private InquirySignaturePersonService inquirySignaturePersonService;

    @Resource
    private InquiryUserSignatureInformationService inquiryUserSignatureInformationService;

    @Override
    public boolean isCaAuthFreeSign(Long userId, SignaturePlatformEnum signaturePlatform) {
        return inquirySignatureCaAuthService.isCaAuthFreeSign(userId, signaturePlatform);
    }

    @Override
    public InquirySignatureCaAuthRespDto getCaAuthInfo(Long userId, SignaturePlatformEnum signaturePlatform) {
        InquirySignatureCaAuthRespVO signatureCaAuth = inquirySignatureCaAuthService.getInquirySignatureCaAuth(userId, signaturePlatform);
        return InquirySignatureCaAuthConvert.INSTANCE.convertDto(signatureCaAuth);
    }

    @Override
    public List<InquirySignatureCaAuthRespDto> getCaAuthInfo(List<Long> userIds, SignaturePlatformEnum signaturePlatform) {
        if (CollUtil.isEmpty(userIds)) {
            return List.of();
        }
        List<InquirySignatureCaAuthRespVO> caAuthRespVOS = inquirySignatureCaAuthService.queryByUserIds(userIds, signaturePlatform);
        return InquirySignatureCaAuthConvert.INSTANCE.convertDto(caAuthRespVOS);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void createSyncSignatureCaAuth(SyncCreateCaDto syncCreateCaDto) {
        if (syncCreateCaDto.getCaInfo() != null) {
            InquirySignatureCaAuthDO caAuthDO = InquirySignatureCaAuthConvert.INSTANCE.convertSync(syncCreateCaDto.getCaInfo(), syncCreateCaDto.getUserId());
            inquirySignatureCaAuthService.createOrUpdateInquirySignatureCaAuth(caAuthDO);
            // 记录签章 信息
            InquiryUserSignatureInformationVO informationVO = InquiryUserSignatureInformationVO.builder()
                .userId(syncCreateCaDto.getUserId())
                .signatureUrl(syncCreateCaDto.getCaInfo().getSignUrl())
                .signaturePlatform(SignaturePlatformEnum.FDD.getCode())
                .signatureBizType(SignatureBizTypeEnum.USER_HAND_DRAWN_SIGN.getCode())
                .signatureStatus(ContractStatusEnum.COMPLETE.getCode())
                .build();
            inquiryUserSignatureInformationService.saveOrUpdateInquiryUserSign(informationVO);
            // 记录降级签名
            if (syncCreateCaDto.getCaInfo().isDownFlag() && StringUtils.isNotBlank(syncCreateCaDto.getCaInfo().getDownSignUrl())) {
                informationVO.setSignatureUrl(syncCreateCaDto.getCaInfo().getDownSignUrl())
                    .setSignaturePlatform(SignaturePlatformEnum.SELF.getCode());
                inquiryUserSignatureInformationService.saveOrUpdateInquiryUserSign(informationVO);
            }
            // 记录医生电子签章
            if (StringUtils.isNotBlank(syncCreateCaDto.getCaInfo().getSealUrl())) {
                informationVO.setSignatureUrl(syncCreateCaDto.getCaInfo().getSealUrl())
                    .setSignatureBizType(SignatureBizTypeEnum.USER_ELE_SIGN.getCode())
                    .setSignaturePlatform(SignaturePlatformEnum.FDD.getCode());
                inquiryUserSignatureInformationService.saveOrUpdateInquiryUserSign(informationVO);
            }
        }

        if (syncCreateCaDto.getPersonInfo() != null) {
            InquirySignaturePersonSaveReqVO saveReqVO = InquirySignaturePersonConvert.INSTANCE.convertSync(syncCreateCaDto.getPersonInfo(), syncCreateCaDto.getUserId());
            inquirySignaturePersonService.saveOrUpdateInquirySignaturePerson(saveReqVO);
        }
    }

    @Override
    public void resetCaAuth(Long userId) {
        inquirySignatureCaAuthService.resetCaAuth(userId);
    }
}
