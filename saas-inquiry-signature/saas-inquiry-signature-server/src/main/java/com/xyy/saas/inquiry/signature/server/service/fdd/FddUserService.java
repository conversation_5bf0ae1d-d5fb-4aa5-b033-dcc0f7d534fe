package com.xyy.saas.inquiry.signature.server.service.fdd;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.fasc.open.api.v5_1.client.UserClient;
import com.fasc.open.api.v5_1.req.user.GetUserAuthUrlReq;
import com.fasc.open.api.v5_1.req.user.GetUserIdentityInfoReq;
import com.fasc.open.api.v5_1.req.user.GetUserReq;
import com.fasc.open.api.v5_1.req.user.UserUnbindReq;
import com.fasc.open.api.v5_1.res.common.EUrlRes;
import com.fasc.open.api.v5_1.res.user.UserIdentityInfoRes;
import com.fasc.open.api.v5_1.res.user.UserRes;
import com.xyy.saas.inquiry.signature.server.service.fdd.bean.FddBaseReqDto;
import java.util.HashMap;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 法大大 用户相关service
 *
 * @Author:chenxiaoyi
 * @Date:2024/02/23 18:36
 */
@Component
@Slf4j
public class FddUserService extends FddBaseService {

    private static final Map<Integer, UserClient> userClientMap = new HashMap<>();

    private UserClient getUserClient(Integer configId) {
        if (userClientMap.get(configId) == null) {
            synchronized (FddUserService.class) {
                if (userClientMap.get(configId) == null) {
                    userClientMap.put(configId, new UserClient(getOpenApiClient(configId)));
                }
            }
        }
        return userClientMap.get(configId);
    }


    /**
     * 查询个人认证/授权状态
     *
     * @param fddBaseReqDto
     * @return
     */
    public CommonResult<UserRes> get(FddBaseReqDto<GetUserReq> fddBaseReqDto) {
        return execute((t) -> getUserClient(fddBaseReqDto.getConfigId()).get(t), fddBaseReqDto, "查询个人认证/授权状态");
    }

    /**
     * 解除个人授权/解除免登
     *
     * @param fddBaseReqDto
     * @return
     */
    public CommonResult<Void> unbind(FddBaseReqDto<UserUnbindReq> fddBaseReqDto) {
        return executeVoid((t) -> getUserClient(fddBaseReqDto.getConfigId()).unbind(t), fddBaseReqDto, "解除个人授权/解除免登");
    }

    /**
     * 获取个人授权链接
     *
     * @param fddBaseReqDto
     * @return
     */
    public CommonResult<EUrlRes> getUserAuthUrl(FddBaseReqDto<GetUserAuthUrlReq> fddBaseReqDto) {
        return execute((t) -> getUserClient(fddBaseReqDto.getConfigId()).getUserAuthUrl(t), fddBaseReqDto, "获取个人授权链接");
    }

    /**
     * 查询个人认证身份信息
     *
     * @param fddBaseReqDto
     * @return
     */
    public CommonResult<UserIdentityInfoRes> getIdentityInfo(FddBaseReqDto<GetUserIdentityInfoReq> fddBaseReqDto) {
        return execute((t) -> getUserClient(fddBaseReqDto.getConfigId()).getIdentityInfo(t), fddBaseReqDto, "查询个人认证身份信息");
    }


}
