package com.xyy.saas.inquiry.signature.server.service.fdd.handler;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.alibaba.fastjson.JSON;
import com.fasc.open.api.enums.signtask.SignTaskStatusEnum;
import com.xyy.saas.inquiry.enums.signature.ContractStatusEnum;
import com.xyy.saas.inquiry.enums.signature.ContractTypeEnum;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractSaveReqVO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquirySignatureContractDO;
import com.xyy.saas.inquiry.signature.server.mq.message.FddAgreementSignatureEvent;
import com.xyy.saas.inquiry.signature.server.mq.message.FddPrescriptionSignatureEvent;
import com.xyy.saas.inquiry.signature.server.mq.producer.FddAgreementSignatureProducer;
import com.xyy.saas.inquiry.signature.server.mq.producer.FddPrescriptionSignatureProducer;
import com.xyy.saas.inquiry.signature.server.service.fdd.bean.FddCallbackHandlerCode;
import com.xyy.saas.inquiry.signature.server.service.fdd.dto.SignTaskCallbackDto;
import com.xyy.saas.inquiry.signature.server.service.fdd.enums.FddCallbackEvent;
import com.xyy.saas.inquiry.signature.server.service.signature.InquirySignatureContractService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * 法大大 合同签署相关回调处理器
 *
 * @Author:chenxiaoyi
 * @Date:2024/02/29 10:48
 */
@Component(FddCallbackHandlerCode.SIGN_TASK)
@Slf4j
public class FddCallbackSignTaskHandler extends AbstractFddCallbackHandler {

    @Resource
    private InquirySignatureContractService inquirySignatureContractService;

    @Resource
    FddPrescriptionSignatureProducer fddPrescriptionSignatureProducer;

    @Resource
    FddAgreementSignatureProducer fddAgreementSignatureProducer;

    @Override
    public CommonResult<Object> handle(FddCallbackEvent eventEnum, String appId, String bizContent) {
        SignTaskCallbackDto signTaskCallbackDto = JSON.parseObject(bizContent, SignTaskCallbackDto.class);
        signTaskCallbackDto.setEventId(eventEnum.eventCode);
        signTaskCallbackDto.setAppId(appId);
        String signTaskId = signTaskCallbackDto.getSignTaskId();
        if (StringUtils.isBlank(signTaskId)) {
            return CommonResult.error("签署任务Id为空");
        }
        InquirySignatureContractDO signatureContractDO = inquirySignatureContractService.querySignatureContract(InquirySignatureContractSaveReqVO.builder()
            .bizId(signTaskCallbackDto.getTransReferenceId())
            .thirdId(signTaskId).build());
        if (signatureContractDO == null) {
            return CommonResult.error("签署任务Id未查询到合同");
        }
        // 记录相关回调日志
        insertLogAsync(eventEnum, signTaskId, bizContent);
        // 转换签署状态
        ContractStatusEnum contractStatus = convertFddContractStatus(signTaskCallbackDto.getSignTaskStatus());
        if (contractStatus == null) {
            return CommonResult.success("签署任务状态忽略");
        }
        signTaskCallbackDto.setContractStatus(contractStatus);

        // 通过合同类型来处理mq
        ContractTypeEnum contractType = ContractTypeEnum.fromStatusCode(signatureContractDO.getContractType());
        switch (contractType) {
            case ContractTypeEnum.PRESCRIPTION:
                fddPrescriptionSignatureProducer.sendMessage(FddPrescriptionSignatureEvent.builder().msg(signTaskCallbackDto).build());
                break;
            case ContractTypeEnum.AUTHORIZATION_CONTRACT:
                fddAgreementSignatureProducer.sendMessage(FddAgreementSignatureEvent.builder().msg(signTaskCallbackDto).build());
                break;
            default:
                log.info("contractType: {} 合同类型不处理", contractType.getDesc());
        }
        return CommonResult.success(true);
    }

    /**
     * 转换法大大签署任务状态
     *
     * @param signTaskStatus
     * @return
     */
    private ContractStatusEnum convertFddContractStatus(String signTaskStatus) {
        // 参与方签署中 - > 签署中
        if (StringUtils.equals(signTaskStatus, SignTaskStatusEnum.SIGN_PROGRESS.getCode())) {
            return ContractStatusEnum.SIGNING;
        }
        // 签署已完成 - > 签署中
        if (StringUtils.equals(signTaskStatus, SignTaskStatusEnum.SIGN_COMPLETED.getCode())) {
            return ContractStatusEnum.SIGNING;
        }
        // 签署结束 - > 签署结束
        if (StringUtils.equals(signTaskStatus, SignTaskStatusEnum.TASK_FINISHED.getCode())) {
            return ContractStatusEnum.COMPLETE;
        }
        // 签署异常终止 - > 签署异常终止
        if (StringUtils.equals(signTaskStatus, SignTaskStatusEnum.TASK_TERMINATED.getCode())) {
            return ContractStatusEnum.ABNORMAL;
        }
        // 作废中 - > 作废  任务已作废 - >作废
        if (StringUtils.equals(signTaskStatus, SignTaskStatusEnum.TASK_ABOLISHING.getCode()) || StringUtils.equals(signTaskStatus, SignTaskStatusEnum.TASK_REVOKED.getCode())) {
            return ContractStatusEnum.TERMINATED;
        }
        // 已逾期 - > 已逾期
        if (StringUtils.equals(signTaskStatus, SignTaskStatusEnum.EXPIRED.getCode())) {
            return ContractStatusEnum.EXPIRED;
        }
        return null;
    }
}
