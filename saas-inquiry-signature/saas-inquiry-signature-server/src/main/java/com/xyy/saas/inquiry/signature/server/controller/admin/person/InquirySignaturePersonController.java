package com.xyy.saas.inquiry.signature.server.controller.admin.person;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.signature.server.controller.admin.person.vo.InquirySignaturePersonPageReqVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.person.vo.InquirySignaturePersonRespVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.person.vo.InquirySignaturePersonSaveReqVO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.person.InquirySignaturePersonDO;
import com.xyy.saas.inquiry.signature.server.service.person.InquirySignaturePersonService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理后台 - 签章平台用户")
@RestController
@RequestMapping("/signature/inquiry-signature-person")
@Validated
public class InquirySignaturePersonController {

    @Resource
    private InquirySignaturePersonService inquirySignaturePersonService;

    @PostMapping("/create")
    @Operation(summary = "创建签章平台用户")
    @PreAuthorize("@ss.hasPermission('signature:inquiry-signature-person:create')")
    public CommonResult<Long> createInquirySignaturePerson(@Valid @RequestBody InquirySignaturePersonSaveReqVO createReqVO) {
        return success(inquirySignaturePersonService.createInquirySignaturePerson(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新签章平台用户")
    @PreAuthorize("@ss.hasPermission('signature:inquiry-signature-person:update')")
    public CommonResult<Boolean> updateInquirySignaturePerson(@Valid @RequestBody InquirySignaturePersonSaveReqVO updateReqVO) {
        inquirySignaturePersonService.updateInquirySignaturePerson(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除签章平台用户")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('signature:inquiry-signature-person:delete')")
    public CommonResult<Boolean> deleteInquirySignaturePerson(@RequestParam("id") Long id) {
        inquirySignaturePersonService.deleteInquirySignaturePerson(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得签章平台用户")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('signature:inquiry-signature-person:query')")
    public CommonResult<InquirySignaturePersonRespVO> getInquirySignaturePerson(@RequestParam("id") Long id) {
        InquirySignaturePersonDO inquirySignaturePerson = inquirySignaturePersonService.getInquirySignaturePerson(id);
        return success(BeanUtils.toBean(inquirySignaturePerson, InquirySignaturePersonRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得签章平台用户分页")
    @PreAuthorize("@ss.hasPermission('signature:inquiry-signature-person:query')")
    public CommonResult<PageResult<InquirySignaturePersonRespVO>> getInquirySignaturePersonPage(@Valid InquirySignaturePersonPageReqVO pageReqVO) {
        PageResult<InquirySignaturePersonDO> pageResult = inquirySignaturePersonService.getInquirySignaturePersonPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InquirySignaturePersonRespVO.class));
    }


}