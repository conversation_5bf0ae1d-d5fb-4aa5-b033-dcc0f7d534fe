package com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo;

import com.xyy.saas.inquiry.enums.prescription.template.TemplateTypeEnum;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionTemplateField;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import jakarta.validation.constraints.*;

import java.util.List;

@Schema(description = "管理后台 - 处方笺模板新增/修改 Request VO")
@Data
public class InquiryPrescriptionTemplateSaveReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "12132")
    private Long id;

    @Schema(description = "处方笺模板名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    @NotEmpty(message = "处方笺模板名称不能为空")
    private String name;

    @Schema(description = "处方笺模板描述", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String desc;

    @Schema(description = "是否禁用", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private Boolean disable;

    /**
     * 处方笺模板类型 {@link TemplateTypeEnum}
     */
    @Schema(description = "处方笺模板类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "处方笺模板类型不能为空")
    private Integer type;

    @Schema(description = "PDF文件（底版）", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    private String url0;

    @Schema(description = "PDF文件", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    @NotEmpty(message = "PDF文件不能为空")
    private String url;


    @Schema(description = "模板字段类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "25982")
    private List<PrescriptionTemplateField> templateFields;
}