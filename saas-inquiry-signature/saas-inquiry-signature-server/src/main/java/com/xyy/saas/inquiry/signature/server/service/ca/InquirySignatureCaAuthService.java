package com.xyy.saas.inquiry.signature.server.service.ca;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.InquirySignatureCaAuthRespVO;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.InquirySignatureCaAuthSaveReqVO;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.UserCaAuthCallbackVo;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.ca.InquirySignatureCaAuthDO;
import com.xyy.saas.inquiry.signature.server.service.fdd.dto.SignTaskCallbackDto;
import com.xyy.saas.inquiry.signature.server.service.prescriptiontemplate.dto.TemplateSignCheckedDto;
import java.util.List;

/**
 * CA认证 Service 接口
 *
 * <AUTHOR>
 */
public interface InquirySignatureCaAuthService {

    /**
     * 获得CA认证状态 - 前端
     *
     * @param userId 编号
     * @return CA认证
     */
    InquirySignatureCaAuthRespVO getInquirySignatureCaAuth(Long userId, SignaturePlatformEnum signaturePlatformEnum);

    /**
     * 获取CA认证信息 - 签章
     *
     * @param userId                用户id
     * @param signaturePlatformEnum 签章平台
     * @return
     */
    InquirySignatureCaAuthRespVO getInquirySignatureCaInfo(TemplateSignCheckedDto checkedDto, SignaturePlatformEnum signaturePlatformEnum);


    /**
     * 获取CA认证链接
     *
     * @param createReqVO
     * @return
     */
    CommonResult<String> getInquirySignatureCaAuthUrl(InquirySignatureCaAuthSaveReqVO createReqVO);


    /**
     * CA认证回调
     *
     * @param caAuthCallbackVo 认证回调vo
     */
    CommonResult<InquirySignatureCaAuthRespVO> callBackInquirySignatureCaAuth(UserCaAuthCallbackVo caAuthCallbackVo);


    /**
     * 创建认证手绘签名
     *
     * @param createReqVO 签名vo
     * @return 签名地址
     */
    CommonResult<String> createSeal(InquirySignatureCaAuthSaveReqVO createReqVO);

    /**
     * 创建用户手绘签名 - 降级
     *
     * @param createReqVO
     * @return
     */
    CommonResult<String> drawnSeal(InquirySignatureCaAuthSaveReqVO createReqVO);

    /**
     * 获取签名免验证签链接
     *
     * @param createReqVO 签名vo
     * @return 签署链接
     */
    CommonResult<String> getSealFreeSignUrl(InquirySignatureCaAuthSaveReqVO createReqVO);


    /**
     * 获取签章授权协议签署链接
     *
     * @param createReqVO 用户vo
     * @return 签署链接
     */
    CommonResult<String> getAgreementSignUrl(InquirySignatureCaAuthSaveReqVO createReqVO);

    /**
     * 签章授权协议签署合同回调
     *
     * @param signTaskCallbackDto
     */
    void callbackAgreementSign(SignTaskCallbackDto signTaskCallbackDto);

    /**
     * 创建或修改CA认证
     *
     * @param caAuthDO 创建信息
     * @return 编号
     */
    Long createOrUpdateInquirySignatureCaAuth(InquirySignatureCaAuthDO caAuthDO);


    /**
     * 删除CA认证
     *
     * @param id 编号
     */
    void deleteInquirySignatureCaAuth(Long id);


    /**
     * 根据userid批量查ca认证数据
     *
     * @param userIds           userIds
     * @param signaturePlatform 平台
     * @return
     */
    List<InquirySignatureCaAuthRespVO> queryByUserIds(List<Long> userIds, SignaturePlatformEnum signaturePlatform);

    /**
     * 判断用户是否CA认证且免签
     *
     * @param userId            用户id
     * @param signaturePlatform 签章平台
     * @return true-是
     */
    boolean isCaAuthFreeSign(Long userId, SignaturePlatformEnum signaturePlatform);

    /**
     * 重置CA认证状态
     *
     * @param userId
     */
    void resetCaAuth(Long userId);


}