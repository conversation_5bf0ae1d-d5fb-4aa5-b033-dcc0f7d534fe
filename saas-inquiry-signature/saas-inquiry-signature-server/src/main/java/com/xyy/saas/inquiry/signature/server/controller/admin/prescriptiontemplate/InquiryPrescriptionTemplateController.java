package com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo.InquiryPrescriptionTemplateFieldRespVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo.InquiryPrescriptionTemplateGenerateReqVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo.InquiryPrescriptionTemplatePageReqVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo.InquiryPrescriptionTemplateRespVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo.InquiryPrescriptionTemplateSaveReqVO;
import com.xyy.saas.inquiry.signature.server.service.prescriptiontemplate.InquiryPrescriptionTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import java.io.IOException;
import java.util.List;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "管理后台 - 处方笺模板")
@RestController
@RequestMapping("/signature/prescription-template")
@Validated
public class InquiryPrescriptionTemplateController {

    @Resource
    private InquiryPrescriptionTemplateService inquiryPrescriptionTemplateService;

    @PostMapping("/create")
    @Operation(summary = "创建处方笺模板")
    @PreAuthorize("@ss.hasPermission('signature:prescription-template:create')")
    public CommonResult<Long> createInquiryPrescriptionTemplate(@Valid @RequestBody InquiryPrescriptionTemplateSaveReqVO createReqVO) {
        return success(inquiryPrescriptionTemplateService.createInquiryPrescriptionTemplate(createReqVO));
    }

    @PostMapping("/copy")
    @Operation(summary = "复制处方笺模板")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('signature:prescription-template:create')")
    public CommonResult<Boolean> copyInquiryPrescriptionTemplate(@RequestParam("id") Long id) {
        inquiryPrescriptionTemplateService.copyInquiryPrescriptionTemplate(id);
        return success(true);
    }

    @PutMapping("/update")
    @Operation(summary = "更新处方笺模板")
    @PreAuthorize("@ss.hasPermission('signature:prescription-template:update')")
    public CommonResult<Boolean> updateInquiryPrescriptionTemplate(@Valid @RequestBody InquiryPrescriptionTemplateSaveReqVO updateReqVO) {
        inquiryPrescriptionTemplateService.updateInquiryPrescriptionTemplate(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除处方笺模板")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('signature:prescription-template:delete')")
    public CommonResult<Boolean> deleteInquiryPrescriptionTemplate(@RequestParam("id") Long id) {
        inquiryPrescriptionTemplateService.deleteInquiryPrescriptionTemplate(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得处方笺模板")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('signature:prescription-template:query')")
    public CommonResult<InquiryPrescriptionTemplateRespVO> getInquiryPrescriptionTemplate(@RequestParam("id") Long id) {
        return success(inquiryPrescriptionTemplateService.getInquiryPrescriptionTemplate(id));
    }

    @GetMapping("/page")
    @Operation(summary = "获得处方笺模板分页")
    @PreAuthorize("@ss.hasPermission('signature:prescription-template:query')")
    public CommonResult<PageResult<InquiryPrescriptionTemplateRespVO>> getInquiryPrescriptionTemplatePage(@Valid InquiryPrescriptionTemplatePageReqVO pageReqVO) {
        return success(inquiryPrescriptionTemplateService.getInquiryPrescriptionTemplatePage(pageReqVO));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出处方笺模板 Excel")
    @PreAuthorize("@ss.hasPermission('signature:prescription-template:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInquiryPrescriptionTemplateExcel(@Valid InquiryPrescriptionTemplatePageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InquiryPrescriptionTemplateRespVO> list = inquiryPrescriptionTemplateService.getInquiryPrescriptionTemplatePage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "处方笺模板.xls", "数据", InquiryPrescriptionTemplateRespVO.class, list);
    }

    @PutMapping("/generate")
    @Operation(summary = "生成处方笺模板PDF")
    @PreAuthorize("@ss.hasPermission('signature:prescription-template:update')")
    public CommonResult<InquiryPrescriptionTemplateRespVO> generateInquiryPrescriptionTemplate(@Valid @RequestBody InquiryPrescriptionTemplateGenerateReqVO reqVO) {
        return success(inquiryPrescriptionTemplateService.generateInquiryPrescriptionTemplate(reqVO));
    }

    @PutMapping("/preview")
    @Operation(summary = "预览处方笺模板PDF")
    @PreAuthorize("@ss.hasPermission('signature:prescription-template:update')")
    public CommonResult<String> previewInquiryPrescriptionTemplate(@RequestParam("id") Long id, HttpServletRequest request) {
        return success(inquiryPrescriptionTemplateService.previewInquiryPrescriptionTemplate(id, request.getParameterMap()));
    }

    @GetMapping("/get-fields")
    @Operation(summary = "获得处方笺模板默认字段下拉框")
    @PreAuthorize("@ss.hasPermission('signature:prescription-template:query')")
    public CommonResult<List<InquiryPrescriptionTemplateFieldRespVO>> getPrescriptionTemplateFields() {
        return success(inquiryPrescriptionTemplateService.getPrescriptionTemplateFields());
    }


}