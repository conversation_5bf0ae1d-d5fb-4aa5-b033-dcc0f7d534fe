package com.xyy.saas.inquiry.signature.server.mq.producer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionSignaturePlatformRequireEvent;
import org.springframework.stereotype.Component;

/**
 * 处方签章平台确认 Producer
 *
 * <AUTHOR>
 */
@Component
@EventBusProducer(
    topic = PrescriptionSignaturePlatformRequireEvent.TOPIC
)
public class PrescriptionSignaturePlatformRequireProducer extends EventBusRocketMQTemplate {


}
