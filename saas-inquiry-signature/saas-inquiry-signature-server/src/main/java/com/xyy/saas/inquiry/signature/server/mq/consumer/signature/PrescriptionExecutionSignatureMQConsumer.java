package com.xyy.saas.inquiry.signature.server.mq.consumer.signature;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureAuditDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureInitDto;
import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionExecutionSignatureEvent;
import com.xyy.saas.inquiry.signature.server.service.platform.InquirySignaturePlatformService;
import com.xyy.saas.inquiry.signature.server.service.prescription.strategy.SignaturePrescriptionStrategy;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Desc 处方执行签章MQ
 * <AUTHOR>
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_signature_server_mq_consumer_PrescriptionExecutionSignatureMQConsumer",
    topic = PrescriptionExecutionSignatureEvent.TOPIC)
public class PrescriptionExecutionSignatureMQConsumer {

    @Resource
    private InquirySignaturePlatformService inquirySignaturePlatformService;

    // 签章平台策略
    private final Map<Integer, SignaturePrescriptionStrategy> signaturePrescriptionStrategyMap = new HashMap<>();

    @Autowired
    public void initHandler(List<SignaturePrescriptionStrategy> strategies) {
        strategies.stream().filter(s -> s.getPlatform() != null).forEach(strategy -> signaturePrescriptionStrategyMap.put(strategy.getPlatform().getCode(), strategy));
    }

    @EventBusListener
    public void prescriptionExecutionSignatureMQConsumer(PrescriptionExecutionSignatureEvent prescriptionExecutionSignatureEvent) {
        // 获取签章平台配置，筛选执行策略
        PrescriptionSignatureInitDto signatureInitDto = prescriptionExecutionSignatureEvent.getMsg().getSignatureInitDto();
        // 签发
        if (signatureInitDto != null) {
            signaturePrescriptionStrategyMap.get(Optional.ofNullable(signatureInitDto.getSignaturePlatform())
                .orElse(inquirySignaturePlatformService.getMaster())).issuePrescription(signatureInitDto);
            return;
        }
        // 审方
        PrescriptionSignatureAuditDto psAuditDto = prescriptionExecutionSignatureEvent.getMsg().getPsAuditDto();
        signaturePrescriptionStrategyMap.get(SignaturePlatformEnum.fromCode(psAuditDto.getPlatform()).getCode()).auditPrescription(psAuditDto);
    }

}
