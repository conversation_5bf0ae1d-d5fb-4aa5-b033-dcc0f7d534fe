package com.xyy.saas.inquiry.signature.server.mq.consumer.signature;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.constant.PrescriptionConstant;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.exception.MqConsumerLaterException;
import com.xyy.saas.inquiry.signature.enums.SignatureSyncPlatformStatusEnum;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquirySignatureContractDO;
import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionSignatureEvent;
import com.xyy.saas.inquiry.signature.server.service.signature.InquirySignatureContractService;
import com.xyy.saas.inquiry.signature.server.service.signature.sync.SyncSignaturePlatformStrategy;
import jakarta.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Desc 处方签章回调MQ - 同步三方平台
 * <AUTHOR>
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_signature_server_mq_consumer_SyncSignaturePlatformMQConsumer",
    topic = PrescriptionSignatureEvent.TOPIC)
public class SyncSignaturePlatformMQConsumer {

    @Resource
    private InquirySignatureContractService inquirySignatureContractService;

    @Resource
    private ConfigApi configApi;

    // 签章平台策略
    private final Map<Integer, SyncSignaturePlatformStrategy> syncSignaturePlatformStrategyMap = new HashMap<>();

    @Autowired
    public void initHandler(List<SyncSignaturePlatformStrategy> strategies) {
        strategies.stream().filter(s -> s.getPlatform() != null).forEach(strategy -> syncSignaturePlatformStrategyMap.put(strategy.getPlatform().getCode(), strategy));
    }


    @EventBusListener
    public void syncSignaturePlatformMQConsumer(PrescriptionSignatureEvent prescriptionSignatureEvent) {

        if (prescriptionSignatureEvent.getMsg().getParticipantItem() == null
            || CommonStatusEnum.isDisable(prescriptionSignatureEvent.getMsg().getParticipantItem().getAccessPlatform())) {
            return;
        }

        // 判断是否同步三方 以及同步哪个三方
        String value = configApi.getConfigValueByKey(PrescriptionConstant.PRESCRIPTION_SIGNATURE_DEFAULT_SYNC_PLATFORM);
        if (StringUtils.isBlank(value) || StringUtils.equals(value, SignaturePlatformEnum.SELF.getCode().toString())) {
            return;
        }
        // 没有需要同步平台的参与方 忽略
        InquirySignatureContractDO signatureContract = inquirySignatureContractService.getSignatureContractByPref(prescriptionSignatureEvent.getMsg().getContractPref());
        if (signatureContract == null || CollUtil.isEmpty(signatureContract.getParticipants())
            || Objects.equals(signatureContract.getSyncPlatformStatus(), SignatureSyncPlatformStatusEnum.SYNCED.getCode())
            || signatureContract.getParticipants().stream().noneMatch(p -> CommonStatusEnum.isEnable(p.getAccessPlatform()))) {
            log.info("【处方同步三方平台】处方签章MQConsumer,contractPref:{},忽略同步平台 ", prescriptionSignatureEvent.getMsg().getContractPref());
            return;
        }

        log.info("【处方同步三方平台】处方签章MQConsumer,处方号:{},plat:{}", signatureContract.getBizId(), value);

        try {
            Optional.ofNullable(syncSignaturePlatformStrategyMap.get(SignaturePlatformEnum.fromCode(NumberUtil.parseInt(value)).getCode()))
                .ifPresent(s -> s.syncSignaturePlatform(signatureContract));
        } catch (Exception e) {
            log.error("【处方同步三方平台】处方签章MQConsumer 异常,处方号:{},plat:{},异常:{}", signatureContract.getBizId(), value, e.getMessage());
            throw new MqConsumerLaterException(e.getMessage());
        }

    }

}
