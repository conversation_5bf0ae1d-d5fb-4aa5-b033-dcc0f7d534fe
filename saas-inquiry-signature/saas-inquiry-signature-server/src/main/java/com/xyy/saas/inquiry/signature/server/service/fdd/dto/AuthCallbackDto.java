package com.xyy.saas.inquiry.signature.server.service.fdd.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.List;

/**
 * 法大大 授权相关回调类
 *
 * @Author:chenxiaoyi
 * @Date:2024/02/29 11:12
 */
@Data
public class AuthCallbackDto implements Serializable {

    /**
     * 应用id
     */
    private String appId;

    /**
     * 事件类型
     */
    private String eventId;

    /**
     * 事件触发时间。格式为Unix标准时间戳（毫秒）
     */
    private String eventTime;

    /*个人主体在应用上的openUserId*/
    private String openUserId;

    /*签名ID*/
    private Long sealId;

    /*设置的场景码*/
    private String businessId;

    /*免验证签授权的有效期。格式为Unix标准时间戳（毫秒），精确到天*/
    private String expiresTime;

    /*个人在业务系统的唯一标识*/
    private String clientUserId;

    /*免验证签授权失效时间。格式为Unix标准时间戳（毫秒）*/
    private String grantEndTime;

    // 本次授权操作结果：success: 成功；fail: 失败。
    private String authResult;

    // 本次授权失败原因：reject: 用户操作不允许授权。
    private String authFailedReason;

    // 个人用户实际授权范围，逗号分隔。
    private String authScope;


    /*企业在应用中的唯一标识*/
    private String clientCorpId;

    /*法大大平台为该企业在该应用appId范围内分配的唯一标识。*/
    private String openCorpId;

    /*本次授权时经办人帐号建立免登关系的clientUserId，业务系统可根据其中任意一个实现该经办人帐号的免登。。*/
    private List<String> clientUserIds;
}
