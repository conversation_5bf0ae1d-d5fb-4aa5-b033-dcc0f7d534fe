package com.xyy.saas.inquiry.signature.server.service.fdd.dto;

import com.xyy.saas.inquiry.enums.signature.ContractStatusEnum;
import java.io.Serializable;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

/**
 * 法大大签署任务回调类
 *
 * @Author:ch<PERSON><PERSON><PERSON>i
 * @Date:2024/02/29 11:08
 */
@Data
public class SignTaskCallbackDto implements Serializable {

    /**
     * 应用id
     */
    private String appId;

    /**
     * 法大大事件
     */
    private String eventId;

    /**
     * 事件触发时间。格式为Unix标准时间戳（毫秒）
     */
    private String eventTime;

    /**
     * 签署任务ID
     */
    private String signTaskId;

    /**
     * 事件发生后最新的签署任务状态  https://dev.fadada.com/api-help/ACJJHJQUCG/QBHKD80O7Y4QAHA3 task_created: 任务创建中 (签署任务创建中，未提交) finish_creation：已创建（任务完成创建并在审批中） fill_progress: 填写进行中 (签署任务正在进行协同填写流程阶段，必填控件尚未填完) fill_completed: 填写已完成
     * (签署任务文档中所有的必填控件均已填写，但文档尚未定稿) sign_progress: 签署进行中 (签署任务正在进行签署流程阶段) sign_completed: 签署已完成 (签署任务所有参与方均已签署完成) task_finished: 任务已结束 (签署任务已成功结束) task_terminated: 任务异常停止 (签署任务已经因为某种原因而停止运行，如因为某方拒填或拒签、撤销) expired：已逾期 abolishing：作废中
     * revoked：已作废
     */
    private String signTaskStatus;

    /**
     * 系统合同状态
     */
    private ContractStatusEnum contractStatus;
    /**
     * 参与方唯一标识
     */
    private String actorId;

    /**
     * 是否免验证签
     */
    private boolean verifyFreeSign = false;

    /**
     * 事件触发者姓名。如果是个人主体，显示个人姓名。如果是企业主体，显示企业成员姓名
     */
    private String userName;

    /**
     * 创建任务时指定的业务参考号，便于集成方在业务系统中标识对应。
     */
    private String transReferenceId;


    private String signRejectReason; // 拒签原因
    private String signFailedReason; // 签署失败原因
    private String terminationNote; // 撤销原因
    private String fillRejectReason; // 拒填原因


    public String getReason() {
        return StringUtils.defaultIfBlank(signRejectReason, StringUtils.defaultIfBlank(signFailedReason, StringUtils.defaultIfBlank(terminationNote, fillRejectReason)));
    }

}
