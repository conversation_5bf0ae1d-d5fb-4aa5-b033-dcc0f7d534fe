package com.xyy.saas.inquiry.signature.server.service.platform;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.signature.server.controller.admin.platform.vo.InquirySignatureCallbackLogPageReqVO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.platform.InquirySignatureCallbackLogDO;
import com.xyy.saas.inquiry.signature.server.dal.mysql.platform.InquirySignatureCallbackLogMapper;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 签章回调日志 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InquirySignatureCallbackLogServiceImpl implements InquirySignatureCallbackLogService {


    @Resource
    private InquirySignatureCallbackLogMapper inquirySignatureCallbackLogMapper;

    @Override
    public void createInquirySignatureCallbackLog(InquirySignatureCallbackLogDO inquirySignatureCallbackLogDO) {
        inquirySignatureCallbackLogMapper.insert(inquirySignatureCallbackLogDO);
    }

    @Override
    public InquirySignatureCallbackLogDO getInquirySignatureCallbackLog(Long id) {
        return inquirySignatureCallbackLogMapper.selectById(id);
    }

    @Override
    public PageResult<InquirySignatureCallbackLogDO> getInquirySignatureCallbackLogPage(InquirySignatureCallbackLogPageReqVO pageReqVO) {
        return inquirySignatureCallbackLogMapper.selectPage(pageReqVO);
    }

}