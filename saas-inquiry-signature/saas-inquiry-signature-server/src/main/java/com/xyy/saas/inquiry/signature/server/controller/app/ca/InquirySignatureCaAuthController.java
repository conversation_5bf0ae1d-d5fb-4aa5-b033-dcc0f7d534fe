package com.xyy.saas.inquiry.signature.server.controller.app.ca;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.idempotent.core.annotation.Idempotent;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.signature.api.signature.InquirySignatureImageApi;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.InquirySignatureCaAuthRespVO;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.InquirySignatureCaAuthSaveReqVO;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.UserCaAuthCallbackVo;
import com.xyy.saas.inquiry.signature.server.service.ca.InquirySignatureCaAuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import java.util.Optional;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "app - CA认证")
@RestController
@RequestMapping(value = {"/admin-api/kernel/signature/inquiry-signature-ca-auth", "/app-api/kernel/signature/inquiry-signature-ca-auth"})
@Validated
public class InquirySignatureCaAuthController {

    @Resource
    private InquirySignatureCaAuthService inquirySignatureCaAuthService;

    @Resource
    private InquirySignatureImageApi inquirySignatureImageApi;


    @GetMapping("/get")
    @Operation(summary = "获得CA认证状态信息")
    @Parameter(name = "userId", description = "用户id", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('signature:inquiry-signature-ca-auth:query')")
    public CommonResult<InquirySignatureCaAuthRespVO> getInquirySignatureCaAuth(@RequestParam(value = "userId", required = false) Long userId) {
        InquirySignatureCaAuthRespVO inquirySignatureCaAuth = inquirySignatureCaAuthService.getInquirySignatureCaAuth(Optional.ofNullable(userId).orElse(WebFrameworkUtils.getLoginUserId()), SignaturePlatformEnum.FDD);
        return success(inquirySignatureCaAuth);
    }

    @GetMapping("/get-remote-audit-signature-url")
    @Operation(summary = "获得药师远程审方签名图片")
    @Parameter(name = "userId", description = "用户id", required = true, example = "1024")
    @PreAuthorize("@ss.hasRole('pharmacist')")
    public CommonResult<String> getRemoteAuditSignatureUrl(@RequestParam(value = "userId", required = false) Long userId) {
        return success(inquirySignatureImageApi.getRemoteAuditSignatureUrl(Optional.ofNullable(userId).orElse(WebFrameworkUtils.getLoginUserId()), SignaturePlatformEnum.FDD));
    }

    @PostMapping("/ca-auth")
    @Operation(summary = "获取CA认证链接")
    @PreAuthorize("@ss.hasPermission('signature:inquiry-signature-ca-auth:create')")
    @Idempotent
    public CommonResult<String> createInquirySignatureCaAuth(@Valid @RequestBody InquirySignatureCaAuthSaveReqVO createReqVO) {
        return inquirySignatureCaAuthService.getInquirySignatureCaAuthUrl(createReqVO);
    }


    @RequestMapping(value = "/ca-auth-callback", produces = {"application/json"}, method = RequestMethod.POST)
    @Operation(summary = "更新CA认证")
    @PreAuthorize("@ss.hasPermission('signature:inquiry-signature-ca-auth:update')")
    public CommonResult<InquirySignatureCaAuthRespVO> updateInquirySignatureCaAuth(@RequestBody UserCaAuthCallbackVo caAuthCallbackVo) {
        return inquirySignatureCaAuthService.callBackInquirySignatureCaAuth(caAuthCallbackVo);
    }


    @PostMapping(value = "/create-seal")
    @Operation(summary = "创建手绘签名")
    @PreAuthorize("@ss.hasPermission('signature:inquiry-signature-ca-auth:update')")
    @Idempotent
    public CommonResult<String> createSeal(@RequestBody InquirySignatureCaAuthSaveReqVO createReqVO) {
        createReqVO.setUserId(Optional.ofNullable(createReqVO.getUserId()).orElse(WebFrameworkUtils.getLoginUserId()));
        return inquirySignatureCaAuthService.createSeal(createReqVO);
    }

    @PostMapping(value = "/drawn-seal")
    @Operation(summary = "创建降级手绘签名")
    @PreAuthorize("@ss.hasPermission('signature:inquiry-signature-ca-auth:update')")
    @Idempotent
    public CommonResult<String> drawnSeal(@RequestBody InquirySignatureCaAuthSaveReqVO createReqVO) {
        return inquirySignatureCaAuthService.drawnSeal(createReqVO);
    }

    @PostMapping(value = "/seal-free-sign-url")
    @Operation(summary = "获取设置签名免验证签名链接")
    @PreAuthorize("@ss.hasPermission('signature:inquiry-signature-ca-auth:update')")
    @Idempotent
    public CommonResult<String> getSealFreeSignUrl(@RequestBody InquirySignatureCaAuthSaveReqVO createReqVO) {
        return inquirySignatureCaAuthService.getSealFreeSignUrl(createReqVO);
    }


    @PostMapping(value = "/agreement-sign-url")
    @Operation(summary = "获取签章授权协议链接")
    @PreAuthorize("@ss.hasPermission('signature:inquiry-signature-ca-auth:update')")
    @Idempotent
    public CommonResult<String> getAgreementSignUrl(@RequestBody InquirySignatureCaAuthSaveReqVO createReqVO) {
        return inquirySignatureCaAuthService.getAgreementSignUrl(createReqVO);
    }


    /**
     * 执业医生兼职协议
     */


    @DeleteMapping("/delete")
    @Operation(summary = "删除CA认证")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('signature:inquiry-signature-ca-auth:delete')")
    public CommonResult<Boolean> deleteInquirySignatureCaAuth(@RequestParam("id") Long id) {
        inquirySignatureCaAuthService.deleteInquirySignatureCaAuth(id);
        return success(true);
    }


}