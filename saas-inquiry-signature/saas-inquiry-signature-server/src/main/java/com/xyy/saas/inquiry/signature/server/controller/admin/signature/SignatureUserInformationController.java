package com.xyy.saas.inquiry.signature.server.controller.admin.signature;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.idempotent.core.annotation.Idempotent;
import com.xyy.saas.inquiry.signature.server.controller.admin.signature.vo.InquiryUserElectronicSignatureSaveVO;
import com.xyy.saas.inquiry.signature.server.service.signature.InquiryUserSignatureInformationService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * /admin-api/kernel/fdd/notify/fddEventCallback
 *
 * @Author:chenxiaoyi
 * @Date:2024/11/18 13:37
 */
@Tag(name = "签章用户-签章信息")
@RestController
@RequestMapping("/signature/inquiry-user-signature-information")
@Slf4j
public class SignatureUserInformationController {

    @Resource
    private InquiryUserSignatureInformationService inquiryUserSignatureInformationService;

    /**
     * 创建用户电子签章图片信息-对接三方
     *
     * @param createReqVO
     * @return
     */
    @PostMapping("/create-electronic-signature")
    @Operation(summary = "创建用户电子签章图片")
    @Idempotent
    @PreAuthorize("@ss.hasPermission('signature:inquiry-user-signature-information:create')")
    public CommonResult<String> createUserElectronicSignature(@Valid @RequestBody InquiryUserElectronicSignatureSaveVO createReqVO) {
        return inquiryUserSignatureInformationService.createUserElectronicSignature(createReqVO);
    }

    /**
     * 删除用户电子签章图片信息-对接三方
     *
     * @param createReqVO
     * @return
     */
    @PostMapping("/del-electronic-signature")
    @Operation(summary = "删除用户电子签章图片")
    @PreAuthorize("@ss.hasPermission('signature:inquiry-user-signature-information:update')")
    public CommonResult<?> deleteUserElectronicSignature(@Valid @RequestBody InquiryUserElectronicSignatureSaveVO updateVO) {
        return inquiryUserSignatureInformationService.deleteUserElectronicSignature(updateVO);
    }


}
