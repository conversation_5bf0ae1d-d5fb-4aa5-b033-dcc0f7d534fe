package com.xyy.saas.inquiry.signature.server.mq.message;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import com.xyy.saas.inquiry.signature.server.service.fdd.dto.AuthCallbackDto;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 法大大CA认证事件
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class FddCaAuthEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "FDD_CA_AUTH";

    private AuthCallbackDto msg;


    @JsonCreator
    public FddCaAuthEvent(@JsonProperty("msg") AuthCallbackDto msg) {
        this.msg = msg;
    }


    @Override
    public String getTag() {
        return "";
    }

}
