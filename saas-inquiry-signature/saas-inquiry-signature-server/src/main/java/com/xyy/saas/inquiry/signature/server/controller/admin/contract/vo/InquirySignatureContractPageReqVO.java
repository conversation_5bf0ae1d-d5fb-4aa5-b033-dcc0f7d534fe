package com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

@Schema(description = "管理后台 - 签章合同分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InquirySignatureContractPageReqVO extends PageParam {

    @Schema(description = "合同编号,系统生成")
    private String pref;

    @Schema(description = "签章平台  1-法大大")
    private Integer signaturePlatform;

    @Schema(description = "合同业务类型", example = "2")
    private Integer contractType;

    @Schema(description = "业务方唯一标识", example = "2369")
    private String bizId;

    @Schema(description = "三方签署任务id signTaskId", example = "10496")
    private String thirdId;

    @Schema(description = "合同状态", example = "2")
    private Integer contractStatus;

    /**
     * 发起方uerId
     */
    private Long initiatorUserId;

    @Schema(description = "发起方姓名", example = "芋艿")
    private String initiatorName;

    @Schema(description = "发起方联系方式")
    private String initiatorMobile;

    @Schema(description = "参与方集合")
    private String participants;

    @Schema(description = "合同参数详情")
    private String paramDetail;

    @Schema(description = "合同图片", example = "https://www.iocoder.cn")
    private String imgUrl;

    @Schema(description = "合同PDF文件", example = "https://www.iocoder.cn")
    private String pdfUrl;

    @Schema(description = "创建时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    private LocalDateTime[] createTime;

}