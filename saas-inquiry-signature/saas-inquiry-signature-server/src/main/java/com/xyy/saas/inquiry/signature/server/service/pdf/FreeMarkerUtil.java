package com.xyy.saas.inquiry.signature.server.service.pdf;

import com.alibaba.fastjson.JSON;
import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Font;
import com.itextpdf.text.pdf.BaseFont;
import com.itextpdf.text.pdf.PdfWriter;
import com.itextpdf.tool.xml.XMLWorkerFontProvider;
import com.itextpdf.tool.xml.XMLWorkerHelper;
import com.xyy.saas.inquiry.enums.im.ImSourceTypeEnum;
import com.xyy.saas.inquiry.signature.api.immessage.dto.InquiryImMessageDto;
import freemarker.template.Configuration;
import freemarker.template.Template;
import lombok.extern.slf4j.Slf4j;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.io.StringWriter;
import java.io.Writer;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import freemarker.template.Version;
import org.apache.commons.collections4.CollectionUtils;

@Slf4j
public class FreeMarkerUtil {

    public static class AsianFontProvider extends XMLWorkerFontProvider {

        @Override
        public Font getFont(final String fontName, String encoding, float size, final int style) {
            try {
                BaseFont bfChinese = BaseFont.createFont("STSongStd-Light", "UniGB-UCS2-H", BaseFont.NOT_EMBEDDED);
                return new Font(bfChinese, size, style);
            } catch (Exception e) {
                log.error("FreeMarkerUtil.AsianFontProvider.getFont.error", e);
            }
            return super.getFont(fontName, encoding, size, style);
        }
    }

    /**
     * 生成imPdf
     *
     * @param inquiryImMessageDtoList
     * @param imPdf
     * @return
     */
    public static boolean generateImPdf(List<InquiryImMessageDto> inquiryImMessageDtoList, File imPdf) {

        if (CollectionUtils.isEmpty(inquiryImMessageDtoList)) {
            return false;
        }

        log.info("FreeMarkerUtil#generateImPdf.req.data:{}", JSON.toJSONString(inquiryImMessageDtoList));

        // 替换表情为[emoji]
        inquiryImMessageDtoList.forEach(item ->
            item.setMsgContent(ImSourceTypeEnum.MsgText.getCode().equals(item.getSourceType())
                ? replaceEmojis(item.getMsgContent()) : item.getMsgContent()));

        Writer out = new StringWriter();
        Configuration freemarkerCfg = new Configuration(new Version("2.3.28"));

        try {
            freemarkerCfg.setClassForTemplateLoading(FreeMarkerUtil.class, "/template");
            freemarkerCfg.setDefaultEncoding("UTF-8");

            // 获取模板,并设置编码方式
            Template template = freemarkerCfg.getTemplate("pdfTemplate.ftl");
            // 合并数据模型与模板
            Map<String, List<InquiryImMessageDto>> map = new HashMap<>();
            map.put("chatLists", inquiryImMessageDtoList);
            template.process(map, out); // 将合并后的数据和模板写入到流中，这里使用的字符流
            out.flush();

            // 创建imPdf
            try (FileOutputStream fos = new FileOutputStream(imPdf)) {
                createPdf(fos, out.toString());
            }

            return true;
        } catch (Exception e) {
            log.error("FreeMarkerUtil#generateImPdf#error.req.data:{}", JSON.toJSONString(inquiryImMessageDtoList), e);
        } finally {
            try {
                out.close();
            } catch (IOException ex) {
                log.error("FreeMarkerUtil#generateImPdf#error.req.data:{}", JSON.toJSONString(inquiryImMessageDtoList), ex);
            }
        }
        return false;
    }

    /**
     * 替换emoji表情为[emoji]
     *
     * @param input
     * @return
     */
    static String replaceEmojis(String input) {
        StringBuilder sb = new StringBuilder();
        int length = input.length();

        for (int i = 0; i < length; ) {
            int codePoint = input.codePointAt(i);
            int charCount = Character.charCount(codePoint);

            if (isEmoji(codePoint)) {
                sb.append("[emoji]");
            } else {
                // 追加原始字符（正确处理代理对）
                sb.append(input, i, i + charCount);
            }

            i += charCount; // 移动到下一个代码点
        }
        return sb.toString();
    }

    /**
     * 判断是否是emoji
     *
     * @param codePoint
     * @return
     */
    private static boolean isEmoji(int codePoint) {
        // 表情符号的 Unicode 范围定义（与之前一致）
        return (codePoint >= 0x1F600 && codePoint <= 0x1F64F) ||
            (codePoint >= 0x1F300 && codePoint <= 0x1F5FF) ||
            (codePoint >= 0x1F680 && codePoint <= 0x1F6FF) ||
            (codePoint >= 0x2600 && codePoint <= 0x26FF) ||
            (codePoint >= 0x2700 && codePoint <= 0x27BF) ||
            (codePoint >= 0x1F1E6 && codePoint <= 0x1F1FF) ||
            (codePoint >= 0x1F900 && codePoint <= 0x1F9FF) ||
            (codePoint >= 0x1FA70 && codePoint <= 0x1FAFF) ||
            (codePoint == 0x200D) ||  // 零宽连接符
            (codePoint == 0xFE0F);    // 变体选择器-16
    }

    /**
     * 填充pdf
     *
     * @param outputStream
     * @param content
     */
    public static void createPdf(OutputStream outputStream, String content) {
        Document document = new Document();
        try {
            PdfWriter writer = PdfWriter.getInstance(document, outputStream);
            document.open();
            XMLWorkerHelper.getInstance().parseXHtml(
                writer,
                document,
                new ByteArrayInputStream(content.getBytes(StandardCharsets.UTF_8)),
                null,
                StandardCharsets.UTF_8,
                new AsianFontProvider()
            );
        } catch (DocumentException | IOException e) {
            log.error("PDF生成失败", e);
        } finally {
            document.close();
            try {
                outputStream.close();
            } catch (IOException e) {
                log.error("PDF生成失败", e);
            }
        }
    }
}
