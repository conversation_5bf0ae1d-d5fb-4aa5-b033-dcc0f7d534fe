package com.xyy.saas.inquiry.signature.server.api.signature;

import com.xyy.saas.inquiry.enums.signature.SignatureBizTypeEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.signature.api.signature.InquiryUserSignatureInformationApi;
import com.xyy.saas.inquiry.signature.server.service.signature.InquiryUserSignatureInformationService;
import jakarta.annotation.Resource;
import org.apache.dubbo.config.annotation.DubboService;
import java.util.List;

/**
 * 用户签章信息Api
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/24 13:54
 */
@DubboService
public class InquiryUserSignatureInformationApiImpl implements InquiryUserSignatureInformationApi {

    @Resource
    private InquiryUserSignatureInformationService inquiryUserSignatureInformationService;

    @Override
    public void createMigrationUserSignatureInformation(String nickName, String signatureImgUrl, List<RoleCodeEnum> roleCodeEnums, Integer clockInStatus) {
        inquiryUserSignatureInformationService.createMigrationUserSignatureInformation(nickName, signatureImgUrl, roleCodeEnums, clockInStatus);
    }

    @Override
    public String getInquiryUserSignatureUrl(Long userId, SignaturePlatformEnum signaturePlatformEnum) {
        return inquiryUserSignatureInformationService.getInquiryUserSignature(userId, signaturePlatformEnum, SignatureBizTypeEnum.USER_HAND_DRAWN_SIGN);
    }

    @Override
    public String getInquiryUserSignatureUrl(Long userId, SignaturePlatformEnum signaturePlatformEnum, SignatureBizTypeEnum signatureBizTypeEnum) {
        return inquiryUserSignatureInformationService.getInquiryUserSignature(userId, signaturePlatformEnum, signatureBizTypeEnum);
    }
}
