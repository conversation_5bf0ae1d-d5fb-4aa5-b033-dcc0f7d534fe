package com.xyy.saas.inquiry.signature.server.dal.dataobject.prescriptiontemplate;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.enums.prescription.template.TemplateTypeEnum;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionTemplateField;
import lombok.*;

import java.util.List;

/**
 * 处方笺模板 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_inquiry_prescription_template", autoResultMap = true)
@KeySequence("saas_inquiry_prescription_template_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryPrescriptionTemplateDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 处方笺模板名称
     */
    private String name;
    /**
     * 处方笺模板类型 {@link TemplateTypeEnum}
     */
    private Integer type;
    /**
     * 处方笺模板描述
     */
    @TableField("`desc`")
    private String desc;
    /**
     * 是否禁用，默认true
     */
    private Boolean disable;
    /**
     * 文件 URL（底版）
     */
    private String url0;
    /**
     * 文件 URL
     */
    private String url;

    /**
     * 处方模板字段
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private List<PrescriptionTemplateField> templateFields;


}