package com.xyy.saas.inquiry.signature.server.service.pdf;

import cn.hutool.core.util.RandomUtil;
import org.springframework.stereotype.Service;

import java.util.*;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Service
public class MockDataPdfService {

    private static final String[] images = new String[]{
        "https://files.ybm100.com/INVT/Recipel/38187dad5a2f4ec0a7bc2ad3a205c274.jpg",
        "https://files.ybm100.com/INVT/Recipel/2c0f4add8923401880ec20b4e6e2ce50.jpg",
        "https://files.ybm100.com/INVT/Recipel/af6ec4f25d524ed0a9b4325311c86840.jpg"
    };

    public Map<String, String> dataMap(Integer type, Map<String, String[]> parameterMap) {
        return new HashMap<>() {{
            put("no", "CF24101830669239");
            put("date", "2024-10-18");
            put("name", "张X杰");
            put("sex", "男");
            put("age", "56岁");
            put("dept", "科室001");
            put("allergic", "无");
            put("diagnosis", "呼吸道感染，不可归类在他处者 CA45");
            put("drugs", getDetailText(type));

            // 医师药师签名图片
            put("doctorSign", images[RandomUtil.randomInt(images.length)]);
            put("pharmacistSign", images[RandomUtil.randomInt(images.length)]);

            put("instruction",
                "副数：共 10 副   每日 1 副   每日服用 2 次   加工方式：煎服   用法：口服");
            put("warning", "注：该处方为复诊处方，沿用原治疗方案");
            put("remarks", "本处方仅限xxxxx大药房使用(非本门店无效)");

            if (parameterMap != null) {
                parameterMap.forEach((k, v) -> put(k, v[0]));
            }
        }};
    }

    private String getDetailText(Integer type) {
        // 0-西药  1-中药
        if (Objects.equals(0, type)) {
            return String.join("\n", DrugList);
        }
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < MedicineList.size(); i++) {
            if (i % 3 == 0 && i != 0) {
                sb.append("\n");
            } else if (i != 0) {
                sb.append("\t");
            }
            sb.append(MedicineList.get(i));
        }
//        sb.append("\n副数：共 1 副   每日 1 副   每日服用 1 次")
//            .append("\n加工方式：没有别的意思，就是测试下最大值没有别的意思，就是测试下最大值没有别的意思，就是测试下最大值没有别的意")
//            .append("\n用法：爱康爱康哈啊按理说测都很短取完后诶哦u2931070我丶oad啥换卡换卡爱康好了了L 了L数量L了了");
        return sb.toString();
    }

    private static final List<String> DrugList = new ArrayList<>() {{
        add("辅酶 Q10 片（10mg*10s*3 板 糖衣）【3 盒】");
        add("用法用量：口服，一次 1 片，3 次/天1");
        add("天麻丸（6g*12 袋）【1 盒】");
        add("用法用量：口服，一次 1 袋，3 次/天1");
        add("------------------------------------以下空白--------------------------------");
    }};
    private static final List<String> MedicineList = new ArrayList<>() {{
        add("红参(古韩庄高丽参)(精美纸盒+塑料盒) 10g");
        add("柴胡 20g");
        add("炒酸枣仁 12g");
        add("抗新型冠状病毒肺炎感染的预防方剂预防方剂 12g");
        add("抗新型冠状病毒肺炎感染的预防方剂预防方剂   12g");

        add("柴胡 20g");
        add("炒酸枣仁 12g");
        add("醋郁金 12g");
        add("抗新型冠状病毒肺炎感染的预防方剂 12g");
        add("百部   30g");

        add("紫苑   30g");
        add("紫苑   30g");
        add("百部   30g");
        add("紫苑   30g");
        add("紫苑   30g");

        add("紫苑   30g");
        add("黄芪   50g");
        add("党参   50g");
        add("紫苑   30g");
        add("荆芥   27g");

        add("百部   30g");
        add("白前   30g");
        add("陈皮   18g");
        add("紫苑   30g");
        add("紫苑   30g");

        add("紫苑   30g");
        add("黄芪   50g");
        add("党参   50g");
        add("紫苑   30g");
        add("荆芥   27g");

        add("百部   30g");
        add("白前   30g");
        add("陈皮   18g");
    }};
}
