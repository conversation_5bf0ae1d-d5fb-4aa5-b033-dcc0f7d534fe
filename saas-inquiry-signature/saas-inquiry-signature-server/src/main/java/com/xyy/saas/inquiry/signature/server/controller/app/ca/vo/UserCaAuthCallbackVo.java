package com.xyy.saas.inquiry.signature.server.controller.app.ca.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @Description: 医生药师CA认证入参实体
 * <AUTHOR>
 * @Date 2021/11/19
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UserCaAuthCallbackVo implements Serializable {

    private static final long serialVersionUID = -2550132469925339152L;

    /**
     * 应用id
     */
    private String appId;

    ///////////////// redirectUrl重定向回调 ////////////////////
    // Unix标准时间戳，精确到毫秒。
    private String timestamp;

    /*请求参数的签名值。您可自行决定是否验证该签名，计算方法和样例参考下面示例。*/
    private String signature;

    // 个人用户在应用中的唯一标识，长度最大64个字符。
    private String clientUserId;

    // 法大大平台为该用户在该应用appId范围内的唯一标识。长度最大64个字符。
    private String openUserId;

    // 本次授权操作结果：success: 成功；fail: 失败。
    private String authResult;

    // 本次授权失败原因：reject: 用户操作不允许授权。
    private String authFailedReason;

    // 个人用户实际授权范围，逗号分隔。
    private String authScope;

    ///////////////// 授权事件回调 ////////////////////
    /**
     * 事件类型
     */
    private String eventId;
    /**
     * 事件触发时间。格式为Unix标准时间戳（毫秒）
     */
    private String eventTime;

    /*签名ID*/
    private Long sealId;
    /*设置的场景码*/
    private String businessId;

    /*免验证签授权的有效期。格式为Unix标准时间戳（毫秒），精确到天*/
    private String expiresTime;
    /*免验证签授权失效时间。格式为Unix标准时间戳（毫秒）*/
    private String grantEndTime;


    /*企业在应用中的唯一标识*/
    private String clientCorpId;
    /*法大大平台为该企业在该应用appId范围内分配的唯一标识。*/
    private String openCorpId;
    /*本次授权时经办人帐号建立免登关系的clientUserId，业务系统可根据其中任意一个实现该经办人帐号的免登。。*/
    private List<String> clientUserIds;


}
