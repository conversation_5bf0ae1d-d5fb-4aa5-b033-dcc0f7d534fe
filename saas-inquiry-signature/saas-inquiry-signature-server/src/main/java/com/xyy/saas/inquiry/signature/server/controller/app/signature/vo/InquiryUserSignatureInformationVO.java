package com.xyy.saas.inquiry.signature.server.controller.app.signature.vo;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 问诊用户(医生/药师/核对/调配)签章信息 DO
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class InquiryUserSignatureInformationVO extends BaseDO {

    /**
     * 主键
     */
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户ids
     */
    private List<Long> userIds;

    /**
     * 门店编号,药师医生无
     */
    private Long tenantId;

    /**
     * 签章平台 0-自签署 1-法大大
     */
    private Integer signaturePlatform;
    /**
     * 签章业务类型 1-用户签名 2-授权合同
     */
    private Integer signatureBizType;
    /**
     * 签署任务id(eg:合同号)
     */
    private String signatureTaskId;
    /**
     * 签章状态 1-签署中,2-签署完成
     */
    private Integer signatureStatus;
    /**
     * 签署url(eg:合同链接/签名图片)
     */
    private String signatureUrl;

    @Schema(description = "签名图片base64")
    private String signatureImgBase64;

}