package com.xyy.saas.inquiry.signature.server.mq.producer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionSignatureEvent;
import org.springframework.stereotype.Component;

/** 处方执行签章 Execution
 * @Desc 处方签章mq
 * <AUTHOR>
 */
@Component
@EventBusProducer(
    topic = PrescriptionSignatureEvent.TOPIC
)
public class PrescriptionSignatureProducer extends EventBusRocketMQTemplate {


}
