package com.xyy.saas.inquiry.signature.server.service.pdf;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.itextpdf.text.DocumentException;
import com.xyy.saas.inquiry.signature.dto.pdf.RectangleInfo;

import java.io.File;
import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public interface PdfService {

    /**
     * 生成PDF模板文件
     *
     * @param url        原PDF文件OSS链接
     * @param rectangles 字段坐标信息
     * @param destFile   新PDF临时文件
     * @return 返回是否成功
     */
    boolean generateTemplate(String url, List<RectangleInfo> rectangles, File destFile);


    /**
     * 填充数据生成PDF文件
     *
     * @param url      原PDF模板文件OSS链接
     * @param dataMap  填充数据
     * @param destFile 新PDF临时文件
     * @return 返回是否成功
     */
    boolean generate(String url, Map<String, String> dataMap, File destFile) throws IOException, DocumentException;

    /**
     * 填充数据生成PDF文件 并上传文件资源存储
     *
     * @param url     原PDF模板文件OSS链接
     * @param dataMap 填充数据
     * @return 文件url
     */
    CommonResult<String> generateAndUpload(String url, Map<String, String> dataMap);

    /**
     * 填充数据生成PDF文件 并上传文件资源存储
     *
     * @param bytes   原PDF模板文件byte[]
     * @param dataMap 填充数据
     * @return 文件url
     */
    CommonResult<String> generateAndUpload(byte[] bytes, Map<String, String> dataMap);

}
