package com.xyy.saas.inquiry.signature.server.dal.redis;

/**
 * @Author:chenxia<PERSON>i
 * @Date:2024/11/18 13:25
 */
public interface RedisKeyConstants {

    /**
     * 签章平台配置缓存key
     */
    String SIGNATURE_PLATFORM_CONFIG = "signature:platform_config";

    /**
     * 处方笺模板byte[]缓存 key: prescription_template_byte:{id} value:模板id
     */
    String SIGNATURE_PRESCRIPTION_TEMPLATE_BYTE = "signature:prescription_template_byte";

    /**
     * 处方同步三方平台合同锁 ,value:contractPref
     */
    String SIGNATURE_PRESCRIPTION_SYNC_PLATFORM_LOCK = "signature:prescription_sync_platform_lock:";

    /**
     * 处方回调锁 ,value:contractPref
     */
    String SIGNATURE_PRESCRIPTION_CALLBACK_LOCK = "signature:prescription_callback_lock:";

    /**
     * 处方审核签名图片 ,value:textUrl + signUrl
     */
    String SIGNATURE_AUDIT_REMOTE_PRESCRIPTION_SING_URL = "signature:audit_remote_prescription_sing_url";


}
