package com.xyy.saas.inquiry.signature.server.util;

import cn.hutool.core.util.URLUtil;
import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.PdfDictionary;
import com.itextpdf.text.pdf.PdfName;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.PdfString;
import com.xyy.saas.inquiry.signature.dto.signature.ElectSignInfoDto;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.security.Security;
import java.security.cert.CertificateFactory;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.Loader;
import org.apache.pdfbox.io.RandomAccessRead;
import org.apache.pdfbox.io.RandomAccessReadBuffer;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.pdmodel.interactive.digitalsignature.PDSignature;
import org.bouncycastle.asn1.ASN1Encodable;
import org.bouncycastle.asn1.ASN1InputStream;
import org.bouncycastle.asn1.ASN1Primitive;
import org.bouncycastle.asn1.ASN1Sequence;
import org.bouncycastle.asn1.ASN1TaggedObject;
import org.bouncycastle.asn1.DERBitString;
import org.bouncycastle.asn1.DEROctetString;
import org.bouncycastle.asn1.DLTaggedObject;
import org.bouncycastle.asn1.cms.Attribute;
import org.bouncycastle.asn1.cms.AttributeTable;
import org.bouncycastle.cms.CMSSignedData;
import org.bouncycastle.cms.SignerInformation;
import org.bouncycastle.cms.SignerInformationStore;

/**
 * 文件签名提取工具 - 适用于电子签名文件提取内容
 *
 * @Author:chenxiaoyi
 * @Date:2025/02/19 13:14
 */
@Slf4j
public class FileSignatureExtractorUtil {

    static {
        Security.addProvider(new org.bouncycastle.jce.provider.BouncyCastleProvider());
    }
    //
    // public static void main(String[] args) {
    //     extractSignatureItextPdf("F:\\test-1.pdf");
    // }


    public static List<ElectSignInfoDto> extractSignatureItextPdf(String pdfUrl) {
        List<ElectSignInfoDto> signInfoDtos = new ArrayList<>();
        if (StringUtils.isBlank(pdfUrl)) {
            return signInfoDtos;
        }
        try {
            PdfReader pdfReader = new PdfReader(pdfUrl);
            AcroFields fields = pdfReader.getAcroFields();
            ArrayList<String> names = fields.getSignatureNames();// 返回the field names that have signatures and are signed

            for (String name : names) {
                ASN1Sequence asn1Sequence = getAsn1Sequence(fields, name);
                // 解析SM2
                if (asn1Sequence != null && (asn1Sequence.size() == 4 || asn1Sequence.size() == 5)) {
                    ElectSignInfoDto signatureInfo = verifyFillSignWithSm2(name, fields);
                    if (signatureInfo != null) {
                        signInfoDtos.add(signatureInfo);
                    }
                }
            }
        } catch (Exception e) {
            log.error("提取extractSignatureItextPdf文件签名信息失败,url:{}", pdfUrl, e);
        }

        return signInfoDtos;

    }

    /**
     * 国密签署验签
     */
    public static ElectSignInfoDto verifyFillSignWithSm2(String name, AcroFields fields) {
        ElectSignInfoDto electSignInfoDto = new ElectSignInfoDto();
        try {
            PdfDictionary signDictionary = fields.getSignatureDictionary(name);
            PdfString contents = signDictionary.getAsString(PdfName.CONTENTS);
            byte[] originalBytes = contents.getOriginalBytes();
            ASN1Primitive pkcs;
            try (ASN1InputStream din = new ASN1InputStream(new ByteArrayInputStream(originalBytes));) {
                pkcs = din.readObject();
            } catch (IOException var34) {
                log.error("verifyFillSignWithSm2,解析签名数据失败");
                return null;
            }
            ASN1Sequence signedData = (ASN1Sequence) pkcs;
            // 签章者数字证书
            DEROctetString cert = (DEROctetString) signedData.getObjectAt(1);
            CertificateFactory factory = CertificateFactory.getInstance("X.509", "BC");
            ByteArrayInputStream streamCert = new ByteArrayInputStream(cert.getOctets());
            X509Certificate signCert = (X509Certificate) factory.generateCertificate(streamCert);
            electSignInfoDto.setCerFile(Base64.getEncoder().encodeToString(signCert.getEncoded()));
            log.info("verifyFillSignWithSm2 CertFile (Base64):{}", electSignInfoDto.getCerFile().length());
            // 签名值
            DERBitString signatureAsn = (DERBitString) signedData.getObjectAt(3);
            if (signatureAsn.getBytes() != null) {
                electSignInfoDto.setSignValue(Base64.getEncoder().encodeToString(signatureAsn.getBytes()));
                log.info("verifyFillSignWithSm2 SignValue (Base64):{}", electSignInfoDto.getSignValue().length());
            }
            // 签名时间戳
            if (signedData.size() > 4) {
                DERBitString timeStamp = null;
                if (signedData.getObjectAt(4) instanceof DLTaggedObject) {
                    timeStamp = DERBitString.getInstance((ASN1TaggedObject) signedData.getObjectAt(4), true);
                } else {
                    timeStamp = (DERBitString) signedData.getObjectAt(4);
                }
                electSignInfoDto.setSignTimestamp(Base64.getEncoder().encodeToString(timeStamp.getBytes()));
                log.info("verifyFillSignWithSm2 signTimestamp (Base64):{}", electSignInfoDto.getSignTimestamp().length());
            }
        } catch (Exception e) {
            log.error("verifyFillSignWithSm2", e);
            return null;
        }
        return electSignInfoDto;
    }


    /**
     * 获取签名的asn1结构
     *
     * @param fields
     * @param name
     * @return
     */
    private static ASN1Sequence getAsn1Sequence(AcroFields fields, String name) {
        try (InputStream inputStream = new ByteArrayInputStream(fields.getSignatureDictionary(name).getAsString(PdfName.CONTENTS).getOriginalBytes())) {
            // 获取签名域asn1结构数据
            ASN1InputStream asn1InputStream = new ASN1InputStream(inputStream);
            ASN1Primitive asn1Primitive = asn1InputStream.readObject();
            if (asn1Primitive instanceof ASN1Sequence) {
                ASN1Sequence asn1Sequence = (ASN1Sequence) asn1Primitive;
                return asn1Sequence;
            }
        } catch (Exception e) {
            log.error("获取签章的asn1结构失败：", e);
        }
        return null;
    }


    /**
     * 提取pdf文件中的电子签名信息
     *
     * @param pdfUrl pdf文件url
     * @return 电子签名信息
     */
    public static List<ElectSignInfoDto> extractSignaturePdfBox(String pdfUrl) {
        List<ElectSignInfoDto> signInfoDtos = new ArrayList<>();
        if (StringUtils.isBlank(pdfUrl)) {
            return signInfoDtos;
        }
        try (RandomAccessRead rar = new RandomAccessReadBuffer(URLUtil.getStream(URLUtil.url(pdfUrl)));
            PDDocument document = Loader.loadPDF(rar);) {
            List<PDSignature> signatures = document.getSignatureDictionaries();
            for (PDSignature pdSignature : signatures) {
                ElectSignInfoDto electSignInfoDto = new ElectSignInfoDto();
                // 获取签名内容字节流
                CMSSignedData cmsSignedData = new CMSSignedData(pdSignature.getContents());

                // 提取签名信息
                SignerInformation signer = cmsSignedData.getSignerInfos().getSigners().iterator().next();

                // 1. 提取 PKCS#1 格式签名值
                electSignInfoDto.setSignValue(java.util.Base64.getEncoder().encodeToString(signer.getSignature()));
                log.info("PKCS#1 Signature Value (Base64):{}", electSignInfoDto.getSignValue().length());

                // 2. 提取 SM2 国密时间戳
                AttributeTable unsignedAttributes = signer.getUnsignedAttributes();
                if (unsignedAttributes != null) {
                    Attribute timestampAttr = unsignedAttributes.get(
                        org.bouncycastle.asn1.pkcs.PKCSObjectIdentifiers.id_aa_signatureTimeStampToken);
                    if (timestampAttr != null) {
                        ASN1Encodable[] attrValues = timestampAttr.getAttributeValues();
                        for (ASN1Encodable attrValue : attrValues) {
                            // 解析时间戳令牌
                            CMSSignedData timeStampToken = new CMSSignedData(
                                attrValue.toASN1Primitive().getEncoded());
                            electSignInfoDto.setSignTimestamp(java.util.Base64.getEncoder().encodeToString(timeStampToken.getEncoded()));
                            log.info("SM2 Timestamp (Base64):{}", electSignInfoDto.getSignTimestamp().length());
                        }
                    }
                }
                signInfoDtos.add(electSignInfoDto);
            }
        } catch (Exception e) {
            log.error("提取pdf文件签名信息失败,url:{}", pdfUrl, e);
        }
        return signInfoDtos;
    }

    private static boolean isSM2Signature(CMSSignedData timeStampToken) {
        // 检查签名算法是否为SM2国密算法
        SignerInformationStore tsaSigners = timeStampToken.getSignerInfos();
        SignerInformation tsaSigner = tsaSigners.getSigners().iterator().next();
        String algorithmOID = tsaSigner.getEncryptionAlgOID();

        // SM2算法OID（示例值，请根据实际标准修改）
        final String SM2_OID = "1.2.840.113549.1.1.11";
        return SM2_OID.equals(algorithmOID);
    }

}
