package com.xyy.saas.inquiry.signature.server.api.immessage;

import cn.hutool.core.io.FileUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import com.xyy.saas.inquiry.signature.api.immessage.InquiryImPdfApi;
import com.xyy.saas.inquiry.signature.api.immessage.dto.InquiryImMessageDto;
import com.xyy.saas.inquiry.signature.server.service.pdf.FreeMarkerUtil;
import com.xyy.saas.inquiry.signature.server.service.pdf.PdfBoxUtils;
import com.xyy.saas.inquiry.util.FileApiUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.util.List;

/**
 * @Author: xucao
 * @Date: 2024/12/03 16:27
 * @Description: IM 消息相关接口实现
 */
@DubboService
@Slf4j
public class InquiryImPdfApiImpl implements InquiryImPdfApi {

    @Autowired
    protected FileApi fileApi;

    /**
     * 生成聊天pdf
     *
     * @param dtoList 聊天消息列表
     * @return pdf url
     */
    @Override
    public CommonResult<String> genarateImPdf(List<InquiryImMessageDto> dtoList) {
        File imPdf = FileUtil.createTempFile("im", ".pdf", true);
        try {
            if (PdfBoxUtils.writeMessagesToPdfWithHeader(dtoList, imPdf)) {
                return CommonResult.success(FileApiUtil.createFile(imPdf.getName(), FileUtil.readBytes(imPdf)));
            }
        } finally {
            FileUtil.del(imPdf);
        }
        return CommonResult.error("生成IM聊天记录失败");
    }

    @Override
    public CommonResult<String> generateImPdfByFreeMarker(List<InquiryImMessageDto> inquiryImMessageDtoList) {

        if (CollectionUtils.isEmpty(inquiryImMessageDtoList)) {
            return CommonResult.error("聊天记录为空");
        }

        File imPdf = FileUtil.createTempFile("im", ".pdf", true);

        try {
            if (FreeMarkerUtil.generateImPdf(inquiryImMessageDtoList, imPdf)) {
                return CommonResult.success(FileApiUtil.createFile(imPdf.getName(), FileUtil.readBytes(imPdf)));
            }
        } finally {
            FileUtil.del(imPdf);
        }
        return CommonResult.error("生成IM聊天记录失败");
    }
}
