package com.xyy.saas.inquiry.signature.server.convert.prescription;

import cn.hutool.core.util.NumberUtil;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.pojo.prescription.ParticipantItem;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionTemplateField;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionTemplateFieldDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionParamDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureAuditDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureInitDto;
import com.xyy.saas.inquiry.signature.mq.SignaturePassingMessage;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractAddParticipantVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractStatusVO;
import com.xyy.saas.inquiry.signature.server.convert.prescriptiontemplate.PrescriptionTemplateConvert;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquirySignatureContractDO;
import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionSignatureMessage;
import com.xyy.saas.inquiry.signature.server.service.fdd.dto.SignTaskCallbackDto;
import com.xyy.saas.inquiry.signature.server.util.PrescriptionParamUtil;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.List;
import java.util.Map;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author:chenxiaoyi
 * @Date:2024/10/12 17:17
 */
@Mapper
public interface InquiryPrescriptionSignatureConvert {

    InquiryPrescriptionSignatureConvert INSTANCE = Mappers.getMapper(InquiryPrescriptionSignatureConvert.class);

    /**
     * 转换处方笺模板参数
     *
     * @param psDto
     * @return
     */
    default void convertParamMap(PrescriptionSignatureInitDto psDto, Map<String, String> defaultValueMap, TenantDto tenantDto) {
        psDto.setParamMap(convertParamMap(psDto.getParam(), tenantDto));
        defaultValueMap.forEach((k, v) -> psDto.getParamMap().putIfAbsent(k, v));
    }

    /**
     * 转换合同数据到审核dto
     *
     * @param signatureContractDO
     * @param psAuditDto
     * @return
     */
    default void convertContractWithAudit(InquirySignatureContractDO signatureContractDO, Map<String, String> defaultValueMap, PrescriptionSignatureAuditDto psAuditDto, TenantDto tenantDto) {
        PrescriptionParamDto paramDto = JSON.parseObject(signatureContractDO.getParamDetail(), PrescriptionParamDto.class);
        psAuditDto.setParamMap(convertParamMap(paramDto, tenantDto));
        defaultValueMap.forEach((k, v) -> psAuditDto.getParamMap().putIfAbsent(k, v));
        psAuditDto.setContractPref(signatureContractDO.getPref());
        psAuditDto.setTemplateId(signatureContractDO.getTemplateIdLong());
        List<ParticipantItem> participants = signatureContractDO.getParticipants();
        psAuditDto.setParticipantItems(participants);
        psAuditDto.setParticipantItem(psAuditDto.getParticipantItems().getLast());
        psAuditDto.setPlatform(signatureContractDO.getSignaturePlatform());
    }

    default Map<String, String> convertParamMap(PrescriptionParamDto paramDto, TenantDto tenantDto) {
        paramDto.setTenantId(tenantDto.getId());
        Map<String, String> paramMap = JSON.parseObject(JSON.toJSONString(paramDto), new TypeReference<>() {
        });
        paramMap.put("drugs", PrescriptionParamUtil.getProductParamStr(paramDto.getInquiryProductDto(), MedicineTypeEnum.fromCode(paramDto.getMedicineType())));
        paramMap.put("remarks", PrescriptionParamUtil.getRemarkStr(tenantDto, paramDto));
        paramMap.put("instruction", PrescriptionParamUtil.getInstructionStr(paramDto.getInquiryProductDto(), MedicineTypeEnum.fromCode(paramDto.getMedicineType())));
        paramMap.put("warning", PrescriptionParamUtil.getWarningStr(paramDto.getInquiryProductDto(), MedicineTypeEnum.fromCode(paramDto.getMedicineType())));
        return paramMap;
    }

    /**
     * 转换处方签章消息Dto
     *
     * @param psDto           签章Dto
     * @param prescriptionUrl 处方图片
     * @return
     */

    default PrescriptionSignatureMessage convertPsDtoMessage(PrescriptionSignatureDto psDto, InquirySignatureContractStatusVO contractStatusVO) {
        return PrescriptionSignatureMessage.builder()
            .contractPref(psDto.getContractPref())
            .participantItem(psDto.getParticipantItems().getLast())
            .pdfUrl(contractStatusVO.getPdfUrl())
            .imgUrl(contractStatusVO.getImgUrl())
            .build();
    }

    /**
     * 转换合同发起方
     */
    @Mapping(target = "contractPref", source = "pref")
    @Mapping(target = "participantItem.userId", source = "initiatorUserId")
    @Mapping(target = "participantItem.name", source = "initiatorName")
    @Mapping(target = "participantItem.mobile", source = "initiatorMobile")
    PrescriptionSignatureMessage convertPsInitiator(InquirySignatureContractDO signatureContractDO);


    @Mapping(target = "bizId", source = "prescriptionPref")
    @Mapping(target = "signaturePlatform", source = "platform")
    @Mapping(target = "contractType", expression = "java(com.xyy.saas.inquiry.enums.signature.ContractTypeEnum.PRESCRIPTION.getCode())")
    @Mapping(target = "participantItem", expression = "java(psAuditDto.getParticipantItem().setBizId(psAuditDto.getAuditRecordId()))")
    InquirySignatureContractAddParticipantVO convertAddParticipantItem(PrescriptionSignatureAuditDto psAuditDto);

    @Mapping(target = "participantItems", expression = "java(java.util.Collections.singletonList(psDto.getParticipantItem()))")
    PrescriptionSignatureDto convertInit2Dto(PrescriptionSignatureInitDto psDto);

    PrescriptionSignatureDto convertAudit2Dto(PrescriptionSignatureAuditDto psAuditDto);


    default void fillParticipant(ParticipantItem participantItem, PrescriptionTemplateFieldDto nextTemplateField) {
        if (nextTemplateField != null) {
            participantItem.setActorField(nextTemplateField.getField())
                .setActorFieldName(nextTemplateField.getFieldName())
                .setSort(nextTemplateField.getSorted())
                .setSignImgUrl(nextTemplateField.getUserSignImgUrl())
                .setAccessPlatform(nextTemplateField.getAccessPlatform());
        }
    }

    default SignaturePassingMessage convertSignPassingMessage(InquirySignatureContractStatusVO contractStatusVO, Integer platformConfigId, PrescriptionSignatureMessage psMessageDto, Long auditRecordId, PrescriptionTemplateField nextField,
        long totalLevel) {
        return SignaturePassingMessage.builder()
            .bizId(contractStatusVO.getBizId())
            .participantItem(psMessageDto.getParticipantItem())
            .auditRecordId(auditRecordId)
            .platformConfigId(platformConfigId)
            .totalLevel((int) totalLevel)
            .nextField(PrescriptionTemplateConvert.INSTANCE.convertField(nextField))
            .imgUrl(contractStatusVO.getImgUrl())
            .pdfUrl(contractStatusVO.getPdfUrl())
            .callBackTime(psMessageDto.getCallBackTime())
            .build();
    }

    default PrescriptionSignatureMessage convertFddCallbackMessage(SignTaskCallbackDto signTaskCallbackDto, InquirySignatureContractDO signatureContract) {
        return PrescriptionSignatureMessage.builder()
            .contractPref(signatureContract.getPref())
            .participantItem(ParticipantItem.builder().name(signTaskCallbackDto.getUserName()).actorField(signTaskCallbackDto.getActorId()).build())
            .callBackTime(LocalDateTime.ofInstant(Instant.ofEpochMilli(NumberUtil.parseLong(signTaskCallbackDto.getEventTime())), ZoneId.systemDefault()))
            .build();
    }
}
