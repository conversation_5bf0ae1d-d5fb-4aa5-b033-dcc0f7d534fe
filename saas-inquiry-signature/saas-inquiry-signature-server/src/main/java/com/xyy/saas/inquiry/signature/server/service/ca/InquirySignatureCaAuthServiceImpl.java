package com.xyy.saas.inquiry.signature.server.service.ca;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.signature.enums.ErrorCodeConstants.INQUIRY_SIGNATURE_CA_AUTHED_EXISTS;
import static com.xyy.saas.inquiry.signature.enums.ErrorCodeConstants.INQUIRY_SIGNATURE_CA_AUTH_NOT_EXISTS;
import static com.xyy.saas.inquiry.signature.enums.ErrorCodeConstants.INQUIRY_SIGNATURE_CA_AUTH_USER_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.framework.web.core.util.WebFrameworkUtils;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.system.api.user.AdminUserApi;
import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.fasc.open.api.enums.common.AuthResultEnum;
import com.fasc.open.api.enums.signtask.SignTaskStatusEnum;
import com.fasc.open.api.enums.user.UserAuthScopeEnum;
import com.fasc.open.api.enums.user.UserIdentMethodEnum;
import com.fasc.open.api.v5_1.req.seal.CreatePersonalSealByImageReq;
import com.fasc.open.api.v5_1.req.seal.GetPersonalSealFreeSignUrlReq;
import com.fasc.open.api.v5_1.req.user.GetUserAuthUrlReq;
import com.fasc.open.api.v5_1.res.common.EUrlRes;
import com.fasc.open.api.v5_1.res.seal.CreatePersonalSealByImageRes;
import com.fasc.open.api.v5_1.res.seal.GetSealFreeSignUrlRes;
import com.fasc.open.api.v5_1.res.signtask.CreateSignTaskRes;
import com.fasc.open.api.v5_1.res.signtask.SignTaskDetailRes;
import com.xyy.saas.inquiry.constant.PrescriptionConstant;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantPackageCostApi;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantPackageCostDto;
import com.xyy.saas.inquiry.enums.doctor.DrawnSignEnum;
import com.xyy.saas.inquiry.enums.doctor.PharmacistTypeEnum;
import com.xyy.saas.inquiry.enums.file.FileTypeEnum;
import com.xyy.saas.inquiry.enums.signature.ActorEnum;
import com.xyy.saas.inquiry.enums.signature.ContractStatusEnum;
import com.xyy.saas.inquiry.enums.signature.ContractTypeEnum;
import com.xyy.saas.inquiry.enums.signature.FddCaConstantEnum;
import com.xyy.saas.inquiry.enums.signature.FddCaConstantEnum.AgreementStatus;
import com.xyy.saas.inquiry.enums.signature.FddCaConstantEnum.AuthorizeFreeSignStatus;
import com.xyy.saas.inquiry.enums.signature.FddCaConstantEnum.CertifyStatusEnum;
import com.xyy.saas.inquiry.enums.signature.FddCaConstantEnum.SignatureStatus;
import com.xyy.saas.inquiry.enums.signature.FddUserStatusEnum;
import com.xyy.saas.inquiry.enums.signature.SignatureAppConfigIdEnum;
import com.xyy.saas.inquiry.enums.signature.SignatureBizTypeEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.enums.system.RoleCodeEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantPackageEffectiveStatusEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.InquiryDoctorApi;
import com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalApi;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalSpecificPrescriptionCaDto;
import com.xyy.saas.inquiry.mq.doctor.DoctorAutoInquirySwitchProducer;
import com.xyy.saas.inquiry.pharmacist.api.pharmacist.InquiryPharmacistApi;
import com.xyy.saas.inquiry.pharmacist.api.pharmacist.dto.InquiryPharmacistDto;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.condition.ConditionParamDto;
import com.xyy.saas.inquiry.pojo.forward.ForwardPersonInfo;
import com.xyy.saas.inquiry.pojo.signature.PlatformConfigExtDto;
import com.xyy.saas.inquiry.pojo.signature.SignatureCAExtDto;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractSaveReqVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.signature.vo.InquiryUserElectronicSignatureSaveVO;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.InquirySignatureCaAuthRespVO;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.InquirySignatureCaAuthSaveReqVO;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.SignatureCAExtVO;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.UserCaAuthCallbackVo;
import com.xyy.saas.inquiry.signature.server.controller.app.signature.vo.InquiryUserSignatureInformationVO;
import com.xyy.saas.inquiry.signature.server.convert.ca.InquirySignatureCaAuthConvert;
import com.xyy.saas.inquiry.signature.server.convert.fdd.InquiryFddConvert;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.ca.InquirySignatureCaAuthDO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.person.InquirySignaturePersonDO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquirySignatureContractDO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquiryUserSignatureInformationDO;
import com.xyy.saas.inquiry.signature.server.dal.mysql.ca.InquirySignatureCaAuthMapper;
import com.xyy.saas.inquiry.signature.server.dal.redis.RedisKeyConstants;
import com.xyy.saas.inquiry.signature.server.service.fdd.FddSealService;
import com.xyy.saas.inquiry.signature.server.service.fdd.FddSignTaskBussService;
import com.xyy.saas.inquiry.signature.server.service.fdd.FddUserService;
import com.xyy.saas.inquiry.signature.server.service.fdd.bean.FddBaseReqDto;
import com.xyy.saas.inquiry.signature.server.service.fdd.dto.FddSignTaskCreateDto;
import com.xyy.saas.inquiry.signature.server.service.fdd.dto.SignTaskCallbackDto;
import com.xyy.saas.inquiry.signature.server.service.fdd.enums.FddCallbackEvent;
import com.xyy.saas.inquiry.signature.server.service.person.InquirySignaturePersonService;
import com.xyy.saas.inquiry.signature.server.service.platform.InquirySignaturePlatformService;
import com.xyy.saas.inquiry.signature.server.service.prescriptiontemplate.dto.TemplateSignCheckedDto;
import com.xyy.saas.inquiry.signature.server.service.signature.InquirySignatureContractService;
import com.xyy.saas.inquiry.signature.server.service.signature.InquiryUserSignatureInformationService;
import com.xyy.saas.inquiry.signature.server.util.ImageUtil;
import com.xyy.saas.inquiry.util.ConditionUtil;
import com.xyy.saas.inquiry.util.PrefUtil;
import jakarta.annotation.Resource;
import java.io.File;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.validation.annotation.Validated;

/**
 * CA认证 Service 实现类 saas_inquiry_signature_person 表的 userId 存system_users表的id,
 * <p>
 * 和法大大交互基本只传saas_inquiry_signature_person表的 openUserId,
 * <p>
 *
 * <AUTHOR>
 */
@Service
@Validated
@Slf4j
public class InquirySignatureCaAuthServiceImpl implements InquirySignatureCaAuthService {

    @Resource
    private InquirySignatureCaAuthMapper inquirySignatureCaAuthMapper;

    @Resource
    private InquiryUserSignatureInformationService inquiryUserSignatureInformationService;

    @Resource
    private InquirySignaturePersonService inquirySignaturePersonService;

    @Resource
    private InquirySignaturePlatformService inquirySignaturePlatformService;

    @Resource
    private InquirySignatureContractService inquirySignatureContractService;

    @Autowired
    private AdminUserApi adminUserApi;

    @Autowired
    private TenantApi tenantApi;

    @DubboReference
    private InquiryPharmacistApi inquiryPharmacistApi;

    @DubboReference
    private InquiryHospitalApi inquiryHospitalApi;

    @DubboReference
    private InquiryDoctorApi inquiryDoctorApi;

    @DubboReference
    private TenantPackageCostApi tenantPackageCostApi;

    @Resource
    private FddUserService fddUserService;

    @Resource
    private FddSealService fddSealService;

    @Resource
    private FddSignTaskBussService fddSignTaskBussService;

    @Resource
    private DoctorAutoInquirySwitchProducer doctorAutoInquirySwitchProducer;


    /**
     * 用户认证授权-授权范围
     */
    private static final List<String> USER_AUTH_SCOPES = Arrays.asList(
        UserAuthScopeEnum.IDENT_INFO.getCode(),
        UserAuthScopeEnum.SEAL_INFO.getCode(),
        UserAuthScopeEnum.SIGN_TASK_INIT.getCode(),
        UserAuthScopeEnum.SIGN_TASK_INFO.getCode(),
        UserAuthScopeEnum.SIGN_TASK_FILE.getCode());
    /**
     * 用户认证授权-认证方式
     */
    private static final List<String> USER_IDENT_METHODS = Arrays.asList(
        UserIdentMethodEnum.MOBILE.getCode(),
        UserIdentMethodEnum.BANK.getCode(),
        UserIdentMethodEnum.FACE.getCode()
//            UserIdentMethodEnum.OFFLINE.getCode()
    );
    private static final List<String> NON_EDITABLE_INFO = Arrays.asList("accountName", "userName", "userIdentType", "userIdentNo", "mobile");


    @Override
    public InquirySignatureCaAuthRespVO getInquirySignatureCaInfo(TemplateSignCheckedDto checkedDto, SignaturePlatformEnum signaturePlatformEnum) {
        if (checkedDto.getUserId() == null) {
            return InquirySignatureCaAuthRespVO.init();
        }
        AdminUserRespDTO user = adminUserApi.getUser(checkedDto.getUserId());
        InquirySignatureCaAuthRespVO caAuthRespVO = getInquirySignatureCaAuthRespVO(user, signaturePlatformEnum, false);
        // 判断CA完成
        assembleCaAuthCompletedInfo(caAuthRespVO, user);

        // CA完成 或者 签名不为空
        if (caAuthRespVO.isCaAuthCompleted() || StringUtils.isNotBlank(caAuthRespVO.getRealSignatureUrl())) {
            return caAuthRespVO;
        }
        // CA没完成兼容自绘图片-核对-发药  先取自绘
        String signatureUrl = inquiryUserSignatureInformationService.getInquiryUserSignature(checkedDto.getUserId()
            , SignaturePlatformEnum.SELF, SignatureBizTypeEnum.USER_HAND_DRAWN_SIGN);

        if (StringUtils.isBlank(signatureUrl)) {
            signatureUrl = inquiryUserSignatureInformationService.getInquiryUserSignature(checkedDto.getUserId()
                , SignaturePlatformEnum.FDD, SignatureBizTypeEnum.USER_HAND_DRAWN_SIGN);
        }
        caAuthRespVO.setRealSignatureUrl(signatureUrl);

        return caAuthRespVO;

    }

    @Override
    public InquirySignatureCaAuthRespVO getInquirySignatureCaAuth(Long userId, SignaturePlatformEnum signaturePlatformEnum) {
        AdminUserRespDTO user = adminUserApi.getUser(userId);
        InquirySignatureCaAuthRespVO vo = getInquirySignatureCaAuthRespVO(user, signaturePlatformEnum);
        // 处理东方中讯EZCA认证
        handleShowEzCa(vo, user);
        // 组装完成参数
        return assembleCaAuthCompletedInfo(vo, user);
    }

    private InquirySignatureCaAuthRespVO getInquirySignatureCaAuthRespVO(AdminUserRespDTO user, SignaturePlatformEnum signaturePlatformEnum, boolean... refresh) {
        InquirySignatureCaAuthDO caAuthDO = getByUserId(user.getId(), signaturePlatformEnum, refresh);

        InquirySignatureCaAuthRespVO vo = caAuthDO == null ? InquirySignatureCaAuthRespVO.init() : InquirySignatureCaAuthConvert.INSTANCE.convert(caAuthDO);
        vo.setName(user.getNickname()).setIdCard(user.getIdCard());

        // 获取签名
        if (Objects.equals(vo.getSignatureStatus(), SignatureStatus.FINISH.getCode())) {
            String signatureUrl = inquiryUserSignatureInformationService.getInquiryUserSignature(user.getId(), signaturePlatformEnum, SignatureBizTypeEnum.USER_HAND_DRAWN_SIGN);
            vo.setSignatureUrl(signatureUrl);
            vo.setRealSignatureUrl(vo.getSignatureUrl());
        }
        // 降级判断处理
        drawnSign(vo, user);
        return vo;
    }

    /**
     * 填充用户降级标识
     *
     * @param vo
     * @param user
     */
    private void drawnSign(InquirySignatureCaAuthRespVO vo, AdminUserRespDTO user) {
        if (CollUtil.contains(user.getRoleCodes(), RoleCodeEnum.PHARMACIST.getCode())) {
            // 判断药师是否降级
            InquiryPharmacistDto pharmacist = inquiryPharmacistApi.getPharmacistByUserId(user.getId());
            if (pharmacist == null) {
                vo.setOfflinePharmacist(true); // 线下药师
                return;
            }
            String signatureUrl = inquiryUserSignatureInformationService.getInquiryUserSignature(user.getId(), SignaturePlatformEnum.SELF, SignatureBizTypeEnum.USER_HAND_DRAWN_SIGN);
            vo.setDrawnSign(Objects.equals(pharmacist.getDrawnSign(), DrawnSignEnum.Y.getCode()));
            vo.setDrawnSignUrl(signatureUrl);
            vo.setDrawnSignCompleted(StringUtils.isNotBlank(signatureUrl));
            vo.setPlatformPharmacist(Objects.equals(PharmacistTypeEnum.PLATFORM.getCode(), pharmacist.getPharmacistType()));

            vo.setRealSignatureUrl(StringUtils.defaultIfBlank(signatureUrl, vo.getSignatureUrl()));
        }
    }

    /**
     * 组装认证完成信息
     *
     * @param caAuthStatusDto
     */
    public InquirySignatureCaAuthRespVO assembleCaAuthCompletedInfo(InquirySignatureCaAuthRespVO caAuthStatusDto, AdminUserRespDTO user) {
        // 实名认证 签名 授权协议 免验证签
        boolean caAuthCompleted = Objects.equals(caAuthStatusDto.getCertifyStatus(), FddCaConstantEnum.CertifyStatusEnum.FINISH.getCode())
            && Objects.equals(caAuthStatusDto.getSignatureStatus(), FddCaConstantEnum.SignatureStatus.FINISH.getCode())
            && Objects.equals(caAuthStatusDto.getAuthorizeAgreementStatus(), FddCaConstantEnum.AgreementStatus.FINISH.getCode())
            && Objects.equals(caAuthStatusDto.getAuthorizeFreeSignStatus(), FddCaConstantEnum.AuthorizeFreeSignStatus.AUTHORIZED.getCode());

        // 免验证签授权临期提醒 - 主应用下CA
        if (Objects.equals(caAuthStatusDto.getAuthorizeFreeSignStatus(), AuthorizeFreeSignStatus.AUTHORIZED.getCode()) && caAuthStatusDto.getAuthorizeFreeSignDdl() != null) {
            LocalDateTime freeSignDdl = caAuthStatusDto.getAuthorizeFreeSignDdl();
            long diffDays = LocalDateTime.now().until(freeSignDdl, ChronoUnit.DAYS);
            caAuthStatusDto.setAuthorizeFreeSignExpiring(diffDays <= 30);
        }
        // 处理非主应用下CA
        if (CollUtil.isNotEmpty(caAuthStatusDto.getExt())) {
            for (SignatureCAExtVO caExtVO : caAuthStatusDto.getExt()) {
                if (Objects.equals(caExtVO.getAuthorizeFreeSignStatus(), AuthorizeFreeSignStatus.AUTHORIZED.getCode()) && caExtVO.getAuthorizeFreeSignDdl() != null) {
                    LocalDateTime freeSignDdl = caExtVO.getAuthorizeFreeSignDdl();
                    long diffDays = LocalDateTime.now().until(freeSignDdl, ChronoUnit.DAYS);
                    caExtVO.setAuthorizeFreeSignExpiring(diffDays <= 30);
                }
            }
        }

        return caAuthStatusDto.setCaAuthCompleted(caAuthCompleted);
    }

    private void handleShowEzCa(InquirySignatureCaAuthRespVO caAuthStatusDto, AdminUserRespDTO user) {
        boolean showEzCa = false;

        // 医生 - 判断绑定医院得CA
        if (CollUtil.contains(user.getRoleCodes(), RoleCodeEnum.DOCTOR.getCode())) {
            List<InquiryHospitalRespDto> caHospitals = inquiryHospitalApi.getInquiryHospitalsCaByDoctorUser(user.getId(), SignatureAppConfigIdEnum.EZ);
            showEzCa = CollUtil.isNotEmpty(caHospitals);
        }

        // 非平台的 药师 - 判断药师所在门店套餐下医院得CA
        if (CollUtil.contains(user.getRoleCodes(), RoleCodeEnum.PHARMACIST.getCode()) && !caAuthStatusDto.isPlatformPharmacist()) {
            List<TenantPackageCostDto> packageCostDtos = tenantPackageCostApi.selectUserTenantPackages(user.getId(), List.of(TenantPackageEffectiveStatusEnum.EFFECT, TenantPackageEffectiveStatusEnum.UN_EFFECT));
            if (CollUtil.isNotEmpty(packageCostDtos)) {
                // 获取套餐下医院列表存在EZ CA 的
                List<String> hospitalPrefs = packageCostDtos.stream().filter(p -> CollUtil.isNotEmpty(p.getHospitalPrefs()))
                    .flatMap(tpd -> tpd.getHospitalPrefs().stream()).distinct().toList();
                List<InquiryHospitalRespDto> caHospitals = inquiryHospitalApi.getInquiryHospitalsCaByHospital(hospitalPrefs, SignatureAppConfigIdEnum.EZ);
                if (CollUtil.isNotEmpty(caHospitals)) {
                    // 匹配存在EZCA的医院他配餐门店
                    List<String> hospitalPref = caHospitals.stream().map(InquiryHospitalRespDto::getPref).toList();
                    List<TenantDto> tenantList = tenantApi.getTenantList(packageCostDtos.stream().filter(t -> CollUtil.containsAny(t.getHospitalPrefs(), hospitalPref)).map(TenantPackageCostDto::getTenantId).distinct().toList());
                    List<InquiryHospitalSpecificPrescriptionCaDto> list = caHospitals.stream().flatMap(c -> c.getSetting().getExtend().getSpecificPrescriptionCas().stream()).toList();
                    showEzCa = tenantList.stream().anyMatch(t -> ConditionUtil.isMatch(ConditionParamDto.builder().areaCodes(Stream.of(t.getProvinceCode(), t.getCityCode(), t.getAreaCode()).toList()).build(), list));
                }
            }
        }
        // or 平台药师
        if (showEzCa || caAuthStatusDto.isPlatformPharmacist()) {
            if (CollUtil.isEmpty(caAuthStatusDto.getExt())) {
                caAuthStatusDto.setExt(new ArrayList<SignatureCAExtVO>());
            }

            if (caAuthStatusDto.getExt().stream().noneMatch(c -> Objects.equals(c.getSignaturePlatformConfigId(), SignatureAppConfigIdEnum.EZ.getCode()))) {
                caAuthStatusDto.getExt().add(SignatureCAExtVO.initCa(SignatureAppConfigIdEnum.EZ));
            }

        } else {
            // 移除 EZCA
            caAuthStatusDto.setExt(Optional.ofNullable(caAuthStatusDto.getExt()).orElse(new ArrayList<>())
                .stream().filter(c -> !Objects.equals(c.getSignaturePlatformConfigId(), SignatureAppConfigIdEnum.EZ.getCode())).collect(Collectors.toList()));
        }
    }


    @Override
    public CommonResult<String> getInquirySignatureCaAuthUrl(InquirySignatureCaAuthSaveReqVO createReqVO) {

        createReqVO.setUserId(Optional.ofNullable(createReqVO.getUserId()).orElse(WebFrameworkUtils.getLoginUserId()));
        // 如果不是主应用 先校验并init
        checkAndInitOtherAppConfigCa(createReqVO);
        // 获取用户
        AdminUserRespDTO user = adminUserApi.getUserBaseInfo(createReqVO.getUserId());

        if (StringUtils.isBlank(user.getIdCard())) {
            return CommonResult.error("请先在员工管理中完善身份证信息(说明:如果门店为线下审方,可登录管理员账号,在我的-处方签名中设置签名)");
        }

        // 获取ca认证链接 clientUserId -> userId
        GetUserAuthUrlReq getUserAuthUrlReq = InquiryFddConvert.INSTANCE.convertFddAuth(user, createReqVO.getRedirectUrl(), USER_IDENT_METHODS);
        getUserAuthUrlReq.setAuthScopes(USER_AUTH_SCOPES);
        getUserAuthUrlReq.setNonEditableInfo(NON_EDITABLE_INFO);

        // 校验旧系统CA
        checkOldSysCa(createReqVO, user, getUserAuthUrlReq);

        // 存储法大大认证用户信息
        inquirySignaturePersonService.saveFddPerson(createReqVO.getSignaturePlatformConfigId(), getUserAuthUrlReq, null);
        // 调用法大大获取ca认证链接
        CommonResult<EUrlRes> caAuthResult = fddUserService.getUserAuthUrl(FddBaseReqDto.buildReq(createReqVO.getSignaturePlatformConfigId(), getUserAuthUrlReq));
        if (caAuthResult.isError()) {
            return CommonResult.error(caAuthResult.getMsg());
        }
        return CommonResult.success(caAuthResult.getData().getAuthUrl());
    }

    /**
     * 校验旧系统CA
     *
     * @param createReqVO
     * @param user
     * @param getUserAuthUrlReq
     */
    private void checkOldSysCa(InquirySignatureCaAuthSaveReqVO createReqVO, AdminUserRespDTO user, GetUserAuthUrlReq getUserAuthUrlReq) {
        if (SignatureAppConfigIdEnum.isDefault(createReqVO.getSignaturePlatformConfigId())) {
            // 查旧系统 如果存在认证 则修改认证状态和openUserId 然后 提示已认证
            ForwardPersonInfo personInfo = inquiryDoctorApi.queryUserCaInfo(user.getMobile());
            if (personInfo != null && StringUtils.isNotBlank(personInfo.getOpenUserId()) && StringUtils.equals(personInfo.getUserIdentNo(), user.getIdCard())) {
                log.info("CA认证用户已存在旧服务且已认证,直接同步,nickName:{},mobile:{},openUserId:{},idCard:{}", user.getNickname(), user.getMobile(), personInfo.getOpenUserId(), user.getIdCard());
                inquirySignaturePersonService.saveFddPerson(createReqVO.getSignaturePlatformConfigId(), getUserAuthUrlReq, personInfo.getOpenUserId());
                // 处理CA状态
                InquirySignatureCaAuthDO caAuthDO = Optional.ofNullable(getByUserId(user.getId(), SignaturePlatformEnum.FDD))
                    .orElse(InquirySignatureCaAuthDO.builder().userId(user.getId()).signaturePlatform(SignaturePlatformEnum.FDD.getCode()).build());
                caAuthDO.setCertifyStatus(CertifyStatusEnum.FINISH.getCode());
                inquirySignatureCaAuthMapper.insertOrUpdate(caAuthDO);
                throw exception(INQUIRY_SIGNATURE_CA_AUTHED_EXISTS);
            }
        }
    }

    private void checkAndInitOtherAppConfigCa(InquirySignatureCaAuthSaveReqVO createReqVO) {
        if (!SignatureAppConfigIdEnum.isDefault(createReqVO.getSignaturePlatformConfigId())) {
            InquirySignatureCaAuthDO authDO = getByUserId(createReqVO.getUserId(), SignaturePlatformEnum.FDD);
            if (authDO == null) {
                throw exception(INQUIRY_SIGNATURE_CA_AUTH_USER_NOT_EXISTS);
            }
        }
    }

    @Override
    public CommonResult<InquirySignatureCaAuthRespVO> callBackInquirySignatureCaAuth(UserCaAuthCallbackVo caAuthCallbackVo) {
        // 重定向回调 - 验签
        if (caAuthCallbackVo.getSignature() != null) {
            // String appSecret = inquirySignaturePlatformService.getSignaturePlatformConfig(SignaturePlatformEnum.FDD.getCode()).getAppSecret();
            // AuthCallback2RedirectDto authCallback2RedirectDto = InquiryFddConvert.INSTANCE.convertCallback(caAuthCallbackVo);
            // if (!FddSignUtil.isSignatureValid(appSecret, authCallback2RedirectDto)) {
            //     log.error("法大大redirectUrl回调验签失败: {}", JSON.toJSONString(caAuthCallbackVo));
            //     return CommonResult.error("法大大redirectUrl回调验签失败");
            // }
            // 重定向判断是否已存在认证用户，已存在说明多应用认证，忽略redirectUrl(这里拿不到appid)，走mq处理(mq可以拿到appid)
            long userId = NumberUtils.toLong(caAuthCallbackVo.getClientUserId());
            List<InquirySignaturePersonDO> signaturePersonDOS = inquirySignaturePersonService.queryPersonByUserId(userId, SignaturePlatformEnum.FDD);
            if (CollUtil.isNotEmpty(signaturePersonDOS)) {
                return CommonResult.success(null);
            }
        }
        // 更新用户信息 & 认证状态
        CommonResult<?> resultVO = updateAuthInfo(caAuthCallbackVo);
        if (resultVO.isError()) {
            return CommonResult.error(resultVO.getMsg());
        }
        // getInquirySignatureCaAuth(NumberUtils.toLong(caAuthCallbackVo.getClientUserId()), SignaturePlatformEnum.FDD)
        return CommonResult.success(null);
    }

    /**
     * h5回调或mq回调更新用户认证信息
     *
     * @param caAuthCallbackVo 回调vo
     * @return
     */
    private CommonResult<?> updateAuthInfo(UserCaAuthCallbackVo caAuthCallbackVo) {

        PlatformConfigExtDto platformConfig = inquirySignaturePlatformService.getSignaturePlatformConfig(SignaturePlatformEnum.FDD.getCode(), caAuthCallbackVo.getAppId());
        if (platformConfig == null) {
            return CommonResult.error("法大大用户认证回调失败,appId:" + caAuthCallbackVo.getAppId() + "不存在");
        }
        long userId = 0L;
        InquirySignaturePersonDO personDO;
        if (NumberUtil.isNumber(caAuthCallbackVo.getClientUserId())) {
            userId = NumberUtils.toLong(caAuthCallbackVo.getClientUserId());
            personDO = inquirySignaturePersonService.queryPersonByUserId(userId, SignaturePlatformEnum.FDD, platformConfig.getConfigId());
            if (personDO == null) {
                return CommonResult.error("用户不存在");
            }
        } else {
            // 兼容旧系统认证 根据openUserId查
            personDO = inquirySignaturePersonService.queryPersonByOpenId(caAuthCallbackVo.getOpenUserId(), SignaturePlatformEnum.FDD, platformConfig.getConfigId());
            if (personDO == null) {
                return CommonResult.error("用户不存在");
            }
            userId = personDO.getUserId();
        }
        InquirySignatureCaAuthDO caAuthDO = Optional.ofNullable(getByUserId(userId, SignaturePlatformEnum.FDD)).orElse(InquirySignatureCaAuthDO.builder().userId(userId).signaturePlatform(SignaturePlatformEnum.FDD.getCode()).build());

        // 添加其他应用认证信息
        if (!Objects.equals(SignatureAppConfigIdEnum.DEFAULT.getCode(), platformConfig.getConfigId())
            && caAuthDO.getExt().stream().noneMatch(c -> Objects.equals(c.getSignaturePlatformConfigId(), platformConfig.getConfigId()))) {
            SignatureCAExtDto caExtDto = new SignatureCAExtDto().setSignaturePlatformConfigId(platformConfig.getConfigId());
            caAuthDO.getExt().add(caExtDto);
        }

        AdminUserRespDTO user = adminUserApi.getUserBaseInfo(userId);
        InquirySignatureCaAuthConvert.INSTANCE.convertUser(personDO, user);
        // 授权链接重定向回调 || 授权事件回调
        if (caAuthCallbackVo.getAuthResult() != null && (caAuthCallbackVo.getEventId() == null || Objects.equals(caAuthCallbackVo.getEventId(), FddCallbackEvent.USER_AUTHORIZE.eventCode))) {
            // 实名认证状态
            boolean authSuccess = Objects.equals(caAuthCallbackVo.getAuthResult(), AuthResultEnum.SUCCESS.getCode());
            // 更新签章平台用户信息 + 认证状态
            personDO.setOpenUserId(caAuthCallbackVo.getOpenUserId());
            personDO.setUserStatus(authSuccess ? FddUserStatusEnum.AUTHORIZED.getCode() : FddUserStatusEnum.UNAUTHORIZED.getCode());
            inquirySignaturePersonService.updateInquirySignaturePerson(personDO);
            // 法大大实名认证状态
            if (Objects.equals(SignatureAppConfigIdEnum.DEFAULT.getCode(), platformConfig.getConfigId())) {
                caAuthDO.setCertifyStatus(authSuccess ? FddCaConstantEnum.CertifyStatusEnum.FINISH.getCode() : FddCaConstantEnum.CertifyStatusEnum.FAILED.getCode());
            } else {
                for (SignatureCAExtDto caExtDto : caAuthDO.getExt()) {
                    if (Objects.equals(caExtDto.getSignaturePlatformConfigId(), platformConfig.getConfigId())) {
                        caExtDto.setCertifyStatus(authSuccess ? FddCaConstantEnum.CertifyStatusEnum.FINISH.getCode() : FddCaConstantEnum.CertifyStatusEnum.FAILED.getCode());
                    }
                }
            }
        }
        // 免验证签授权状态 + 有效期
        long freeSignDdl = NumberUtil.parseLong(StringUtils.defaultIfBlank(caAuthCallbackVo.getExpiresTime(), caAuthCallbackVo.getGrantEndTime()));
        if (freeSignDdl > 0 && caAuthCallbackVo.getSealId() != null && StringUtils.equals(caAuthCallbackVo.getSealId().toString(), personDO.getSealId())) {
            if (Objects.equals(SignatureAppConfigIdEnum.DEFAULT.getCode(), platformConfig.getConfigId())) {
                caAuthDO.setAuthorizeFreeSignDdl(new Date(freeSignDdl).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                caAuthDO.setAuthorizeFreeSignStatus(freeSignDdl > System.currentTimeMillis() ? FddCaConstantEnum.AuthorizeFreeSignStatus.AUTHORIZED.getCode() : FddCaConstantEnum.AuthorizeFreeSignStatus.UNAUTHORIZED.getCode());
            } else {
                for (SignatureCAExtDto caExtDto : caAuthDO.getExt()) {
                    if (Objects.equals(caExtDto.getSignaturePlatformConfigId(), platformConfig.getConfigId())) {
                        caExtDto.setAuthorizeFreeSignDdl(new Date(freeSignDdl).toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime());
                        caExtDto.setAuthorizeFreeSignStatus(freeSignDdl > System.currentTimeMillis() ? FddCaConstantEnum.AuthorizeFreeSignStatus.AUTHORIZED.getCode() : FddCaConstantEnum.AuthorizeFreeSignStatus.UNAUTHORIZED.getCode());
                    }
                }
            }
        }
        inquirySignatureCaAuthMapper.insertOrUpdate(caAuthDO);
        return CommonResult.success(true);
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> createSeal(InquirySignatureCaAuthSaveReqVO createReqVO) {
        AdminUserRespDTO user = adminUserApi.getUserBaseInfo(createReqVO.getUserId());
        InquirySignatureCaAuthDO caAuthDO = getByUserId(user.getId(), SignaturePlatformEnum.FDD);
        InquirySignaturePersonDO signaturePerson = inquirySignaturePersonService.queryPersonByUserId(user.getId(), SignaturePlatformEnum.FDD, createReqVO.getSignaturePlatformConfigId());
        if (signaturePerson == null || caAuthDO == null || !Objects.equals(caAuthDO.getCertifyStatus(), FddCaConstantEnum.CertifyStatusEnum.FINISH.getCode())) {
            return CommonResult.error("请先完成实名认证");
        }
        // 创建法大大签名
        CreatePersonalSealByImageReq sealByImageReq = new CreatePersonalSealByImageReq();
        sealByImageReq.setOpenUserId(signaturePerson.getOpenUserId());
        sealByImageReq.setSealName(user.getNickname() + DateUtil.format(new Date(), "yyyyMMddHHmmss"));
        sealByImageReq.setSealWidth(40);
        sealByImageReq.setSealHeight(12);
        sealByImageReq.setSealColor("black");
        sealByImageReq.setSealImage(createReqVO.getImgBase64());
        sealByImageReq.setCreateSerialNo(user.getId().toString());
        CommonResult<CreatePersonalSealByImageRes> sealByImageRes = fddSealService.createSealByImage(FddBaseReqDto.buildReq(createReqVO.getSignaturePlatformConfigId(), sealByImageReq));
        if (sealByImageRes == null || sealByImageRes.isError()) {
            return CommonResult.error(sealByImageRes == null ? "" : sealByImageRes.getMsg());
        }
        // 更新状态
        signaturePerson.setSealId(sealByImageRes.getData().getSealId().toString());
        signaturePerson.setUserStatus(FddUserStatusEnum.SIGNED.getCode());
        inquirySignaturePersonService.updateInquirySignaturePerson(signaturePerson);
        // 创建签名，修改所有应用下得免签未完成
        if (CollUtil.isNotEmpty(caAuthDO.getExt())) {
            for (SignatureCAExtDto caExtDto : caAuthDO.getExt()) {
                caExtDto.setAuthorizeFreeSignStatus(AuthorizeFreeSignStatus.UNAUTHORIZED.getCode());
            }
        }
        inquirySignatureCaAuthMapper.updateById(InquirySignatureCaAuthDO.builder().id(caAuthDO.getId()).signatureStatus(FddCaConstantEnum.SignatureStatus.FINISH.getCode())
            .authorizeFreeSignStatus(FddCaConstantEnum.AuthorizeFreeSignStatus.UNAUTHORIZED.getCode()).ext(caAuthDO.getExt()).build());
        // 记录签名
        InquiryUserSignatureInformationVO informationVO = InquiryUserSignatureInformationVO.builder()
            .userId(user.getId())
            .signatureImgBase64(createReqVO.getImgBase64())
            .signaturePlatform(SignaturePlatformEnum.FDD.getCode())
            .signatureBizType(SignatureBizTypeEnum.USER_HAND_DRAWN_SIGN.getCode())
            .signatureStatus(ContractStatusEnum.COMPLETE.getCode())
            .build();
        InquiryUserSignatureInformationDO informationDO = inquiryUserSignatureInformationService.saveOrUpdateInquiryUserSign(informationVO);
        return CommonResult.success(informationDO.getSignatureUrl());
    }


    @Override
    public CommonResult<String> drawnSeal(InquirySignatureCaAuthSaveReqVO createReqVO) {
        AdminUserRespDTO user = adminUserApi.getUser(Optional.ofNullable(createReqVO.getUserId()).orElse(WebFrameworkUtils.getLoginUserId()));
        // 判断药师是否降级
        if (!CollUtil.contains(user.getRoleCodes(), RoleCodeEnum.PHARMACIST.getCode())) {
            return CommonResult.error("未打开手绘签名开关,不可设置");
        }
        InquiryPharmacistDto pharmacist = inquiryPharmacistApi.getRequiredApprovedPharmacistByUserId(user.getId());
        if (pharmacist == null || !Objects.equals(pharmacist.getDrawnSign(), DrawnSignEnum.Y.getCode())) {
            return CommonResult.error("未打开手绘签名开关,不可设置");
        }
        InquiryUserSignatureInformationVO informationVO = InquiryUserSignatureInformationVO.builder()
            .userId(user.getId())
            .tenantId(TenantContextHolder.getTenantId())
            .signatureImgBase64(createReqVO.getImgBase64())
            .signaturePlatform(SignaturePlatformEnum.SELF.getCode())
            .signatureBizType(SignatureBizTypeEnum.USER_HAND_DRAWN_SIGN.getCode())
            .signatureStatus(ContractStatusEnum.COMPLETE.getCode())
            .build();
        InquiryUserSignatureInformationDO informationDO = inquiryUserSignatureInformationService.saveOrUpdateInquiryUserSign(informationVO);
        return CommonResult.success(informationDO.getSignatureUrl());
    }

    @Override
    public CommonResult<String> getSealFreeSignUrl(InquirySignatureCaAuthSaveReqVO createReqVO) {
        String unSignBusinessId = inquirySignaturePlatformService.getSignaturePlatformConfig(SignaturePlatformEnum.FDD.getCode(), createReqVO.getSignaturePlatformConfigId()).getUnSignBusinessId();
        Long userId = Optional.ofNullable(createReqVO.getUserId()).orElse(WebFrameworkUtils.getLoginUserId());
        InquirySignatureCaAuthDO caAuthDO = getByUserId(userId, SignaturePlatformEnum.FDD);
        // 当前应用
        InquirySignaturePersonDO signaturePerson = inquirySignaturePersonService.queryPersonByUserId(userId, SignaturePlatformEnum.FDD, createReqVO.getSignaturePlatformConfigId());

        // 非主应用判断实名认证
        if (!SignatureAppConfigIdEnum.isDefault(createReqVO.getSignaturePlatformConfigId())) {
            if (caAuthDO.getExt().stream().anyMatch(c -> Objects.equals(c.getSignaturePlatformConfigId(), createReqVO.getSignaturePlatformConfigId()) && !Objects.equals(c.getCertifyStatus(), CertifyStatusEnum.FINISH.getCode()))) {
                return CommonResult.error("请先完成实名认证");
            }
            InquirySignaturePersonDO mainPersonDO = inquirySignaturePersonService.queryPersonByUserId(userId, SignaturePlatformEnum.FDD, SignatureAppConfigIdEnum.DEFAULT.getCode());
            signaturePerson.setSealId(mainPersonDO.getSealId()); // 设置同一sealId
        }
        if (signaturePerson == null || caAuthDO == null || !Objects.equals(caAuthDO.getCertifyStatus(), CertifyStatusEnum.FINISH.getCode())) {
            return CommonResult.error("请先完成实名认证");
        }
        if (StringUtils.isBlank(signaturePerson.getSealId()) || !Objects.equals(caAuthDO.getSignatureStatus(), SignatureStatus.FINISH.getCode())) {
            return CommonResult.error("请先完成电子签名");
        }
        GetPersonalSealFreeSignUrlReq freeSignUrlReq = InquiryFddConvert.INSTANCE.convertFddFreeSign(signaturePerson, createReqVO.getRedirectUrl());
        freeSignUrlReq.setBusinessId(unSignBusinessId);
        CommonResult<GetSealFreeSignUrlRes> sealFreeSignUrlRes = fddSealService.getPersonalSealFreeSignUrl(FddBaseReqDto.buildReq(createReqVO.getSignaturePlatformConfigId(), freeSignUrlReq));
        if (sealFreeSignUrlRes.isError()) {
            return CommonResult.error(sealFreeSignUrlRes.getMsg());
        }
        // 非主应用 设置sealId
        if (!SignatureAppConfigIdEnum.isDefault(createReqVO.getSignaturePlatformConfigId())) {
            inquirySignaturePersonService.updateInquirySignaturePerson(InquirySignaturePersonDO.builder().id(signaturePerson.getId()).sealId(signaturePerson.getSealId()).build());
        }
        return CommonResult.success(sealFreeSignUrlRes.getData().getFreeSignUrl());
    }

    @Override
    public CommonResult<String> getAgreementSignUrl(InquirySignatureCaAuthSaveReqVO createReqVO) {
        AdminUserRespDTO user = adminUserApi.getUserBaseInfo(Optional.ofNullable(createReqVO.getUserId()).orElse(WebFrameworkUtils.getLoginUserId()));
        InquirySignatureCaAuthDO caAuthDO = getByUserId(user.getId(), SignaturePlatformEnum.FDD);
        InquirySignaturePersonDO signaturePerson = inquirySignaturePersonService.queryPersonByUserId(user.getId(), SignaturePlatformEnum.FDD, createReqVO.getSignaturePlatformConfigId());
        if (signaturePerson == null || caAuthDO == null || !Objects.equals(caAuthDO.getCertifyStatus(), FddCaConstantEnum.CertifyStatusEnum.FINISH.getCode())) {
            return CommonResult.error("请先完成实名认证");
        }
        if (StringUtils.isBlank(signaturePerson.getSealId()) || !Objects.equals(caAuthDO.getSignatureStatus(), FddCaConstantEnum.SignatureStatus.FINISH.getCode())) {
            return CommonResult.error("请先完成电子签名");
        }
        if (!Objects.equals(caAuthDO.getAuthorizeFreeSignStatus(), FddCaConstantEnum.AuthorizeFreeSignStatus.AUTHORIZED.getCode())) {
            return CommonResult.error("请先完成免验证签");
        }
        // 签署完成 转换url存储,直接返回
        InquiryUserSignatureInformationVO informationVO = InquiryUserSignatureInformationVO.builder()
            .userId(user.getId())
            .signaturePlatform(SignaturePlatformEnum.FDD.getCode())
            .signatureBizType(SignatureBizTypeEnum.AUTHORIZATION_CONTRACT.getCode()).build();
        InquiryUserSignatureInformationVO existInfo = inquiryUserSignatureInformationService.queryOneByCondition(informationVO);
        log.info("获取用户ca授权合同,userId:{},existInfo:{}", user.getId(), existInfo != null);
        if (existInfo != null && Objects.equals(caAuthDO.getAuthorizeAgreementStatus(), FddCaConstantEnum.AgreementStatus.FINISH.getCode())) {
            if (StringUtils.isNotBlank(existInfo.getSignatureUrl())) {
                return CommonResult.success(existInfo.getSignatureUrl());
            }
            if (StringUtils.isNotBlank(existInfo.getSignatureTaskId())) {
                // 获取签署任务详情 如果签署完成 直接预览
                CommonResult<SignTaskDetailRes> detailRes = fddSignTaskBussService.getSignTaskDetail(createReqVO.getSignaturePlatformConfigId(), existInfo.getSignatureTaskId());
                if (detailRes.isSuccess()) {
                    if (StringUtils.equals(SignTaskStatusEnum.SIGN_COMPLETED.getCode(), detailRes.getData().getSignTaskStatus())
                        || StringUtils.equals(SignTaskStatusEnum.TASK_FINISHED.getCode(), detailRes.getData().getSignTaskStatus())) {
                        return fddSignTaskBussService.getSignTaskPreviewUrl(createReqVO.getSignaturePlatformConfigId(), existInfo.getSignatureTaskId(), createReqVO.getRedirectUrl());
                    }
                    // 没签署完成 获取签署链接
                    if (SignTaskStatusEnum.SIGN_PROGRESS.getCode().equals(detailRes.getData().getSignTaskStatus())) {
                        return fddSignTaskBussService.signTaskActorGetUrl(createReqVO.getSignaturePlatformConfigId(), existInfo.getSignatureTaskId(), ActorEnum.YI.actorId, createReqVO.getRedirectUrl());
                    }
                }
            }
        }
        // 创建签署任务
        PlatformConfigExtDto platformConfig = inquirySignaturePlatformService.getSignaturePlatformConfig(SignaturePlatformEnum.FDD.getCode(), createReqVO.getSignaturePlatformConfigId());
        FddSignTaskCreateDto fddSignTaskCreateDto = InquirySignatureCaAuthConvert.INSTANCE.convertCreateSignTaskDto(user, signaturePerson, platformConfig);
        fddSignTaskCreateDto.setBizId(PrefUtil.getCaSqPref(user.getId())).setContractType(ContractTypeEnum.AUTHORIZATION_CONTRACT).setActorId(ActorEnum.YI.actorId).setExpiresTime(LocalDateTime.now().plusDays(30))
            .setSignTemplateId(platformConfig.getSignAuthAgreementTempId());
        CommonResult<CreateSignTaskRes> withTemplateRes = fddSignTaskBussService.createWithTemplate(fddSignTaskCreateDto);
        if (withTemplateRes.isError()) {
            return CommonResult.error(withTemplateRes.getMsg());
        }
        // 记录签署任务合同
        informationVO.setSignatureTaskId(withTemplateRes.getData().getSignTaskId()).setSignatureStatus(ContractStatusEnum.SIGNING.getCode());
        inquiryUserSignatureInformationService.saveOrUpdateInquiryUserSign(informationVO);
        // 返回签署任务链接
        return fddSignTaskBussService.signTaskActorGetUrl(createReqVO.getSignaturePlatformConfigId(), withTemplateRes.getData().getSignTaskId(), ActorEnum.YI.actorId, createReqVO.getRedirectUrl());
    }


    @Override
    public void callbackAgreementSign(SignTaskCallbackDto signTaskCallbackDto) {
        InquirySignatureContractDO signatureContractDO = inquirySignatureContractService.querySignatureContractByThirdId(signTaskCallbackDto.getSignTaskId(), SignaturePlatformEnum.FDD);
        InquirySignatureCaAuthDO caAuthDO = getByUserId(NumberUtil.parseLong(PrefUtil.getCaSqPref(signTaskCallbackDto.getTransReferenceId())), SignaturePlatformEnum.FDD);
        if (caAuthDO == null || signatureContractDO == null) {
            return;
        }
        InquiryUserSignatureInformationVO informationVO = inquiryUserSignatureInformationService.queryOneByCondition(InquiryUserSignatureInformationVO.builder()
            .signaturePlatform(SignaturePlatformEnum.FDD.getCode()).userId(caAuthDO.getUserId()).signatureBizType(SignatureBizTypeEnum.AUTHORIZATION_CONTRACT.getCode()).build());

        if (Objects.equals(ContractStatusEnum.EXPIRED, signTaskCallbackDto.getContractStatus())
            || Objects.equals(ContractStatusEnum.TERMINATED, signTaskCallbackDto.getContractStatus())
            || Objects.equals(ContractStatusEnum.ABNORMAL, signTaskCallbackDto.getContractStatus())) {
            // 异常情况 合同删除掉
            if (informationVO == null || !Objects.equals(informationVO.getSignatureStatus(), ContractStatusEnum.COMPLETE.getCode())) {
                inquiryUserSignatureInformationService.deleteBySignTaskId(signTaskCallbackDto.getSignTaskId(), SignaturePlatformEnum.FDD);
            }
            return;
        }
        log.info("回调ca授权合同,callbackAgreementSign,informationVO:{}", informationVO);
        // 更新Ca状态
        if (!Objects.equals(signTaskCallbackDto.getContractStatus(), ContractStatusEnum.COMPLETE)) {
            return;
        }
        // 更新合同信息
        InquirySignatureContractSaveReqVO updateReqVO = InquirySignatureContractSaveReqVO.builder()
            .id(signatureContractDO.getId())
            .signaturePlatform(SignaturePlatformEnum.FDD.getCode())
            .contractStatus(signTaskCallbackDto.getContractStatus().getCode())
            .ext(StringUtils.isBlank(signTaskCallbackDto.getReason()) ? null : signatureContractDO.extGet().setRemark(signTaskCallbackDto.getReason())).build();
        inquirySignatureContractService.updateSignatureContract(updateReqVO);

        inquirySignatureCaAuthMapper.updateById(InquirySignatureCaAuthDO.builder().id(caAuthDO.getId()).authorizeAgreementStatus(FddCaConstantEnum.AgreementStatus.FINISH.getCode()).build());
        // 更新合同状态下载合同url
        String contractUrl = fddSignTaskBussService.downContractAndUpload(signatureContractDO.extGet().getPlatformConfigId(), signTaskCallbackDto.getSignTaskId(), FileTypeEnum.PDF);
        inquiryUserSignatureInformationService.updateUserSignatureInformation(informationVO.setSignatureStatus(ContractStatusEnum.COMPLETE.getCode()).setSignatureUrl(contractUrl));
    }


    @Override
    public Long createOrUpdateInquirySignatureCaAuth(InquirySignatureCaAuthDO caAuthDO) {
        InquirySignatureCaAuthDO authDO = inquirySignatureCaAuthMapper.selectOneByUserId(caAuthDO.getUserId(), caAuthDO.getSignaturePlatform());
        if (authDO == null) {
            inquirySignatureCaAuthMapper.insert(caAuthDO);
        } else {
            inquirySignatureCaAuthMapper.updateById(caAuthDO.setId(authDO.getId()));
        }
        return caAuthDO.getId();
    }

    @Override
    public void deleteInquirySignatureCaAuth(Long id) {
        // 校验存在
        validateInquirySignatureCaAuthExists(id);
        // 删除
        inquirySignatureCaAuthMapper.deleteById(id);
    }


    @Override
    public List<InquirySignatureCaAuthRespVO> queryByUserIds(List<Long> userIds, SignaturePlatformEnum signaturePlatform) {
        List<InquirySignatureCaAuthDO> signatureCaAuthDOS = inquirySignatureCaAuthMapper.selectList(new LambdaQueryWrapperX<InquirySignatureCaAuthDO>()
            .in(InquirySignatureCaAuthDO::getUserId, userIds).eq(InquirySignatureCaAuthDO::getSignaturePlatform, signaturePlatform.getCode()));
        return InquirySignatureCaAuthConvert.INSTANCE.convertVos(signatureCaAuthDOS);
    }

    @Override
    public boolean isCaAuthFreeSign(Long userId, SignaturePlatformEnum signaturePlatform) {
        if (userId == null) {
            return false;
        }
        InquirySignatureCaAuthDO caAuthDO = getByUserId(userId, signaturePlatform);
        InquirySignatureCaAuthRespVO vo = caAuthDO == null ? InquirySignatureCaAuthRespVO.init() : InquirySignatureCaAuthConvert.INSTANCE.convert(caAuthDO);
        assembleCaAuthCompletedInfo(vo, adminUserApi.getUser(userId));
        return vo.isCaAuthCompleted();
    }


    @Override
    public void resetCaAuth(Long userId) {
        List<InquirySignatureCaAuthDO> caAuthDOS = inquirySignatureCaAuthMapper.selectList(InquirySignatureCaAuthDO::getUserId, userId);
        if (CollUtil.isEmpty(caAuthDOS)) {
            return;
        }
        List<InquirySignatureCaAuthDO> list = caAuthDOS.stream().peek(c -> {
            c.setCertifyStatus(CertifyStatusEnum.NO_FINISH.getCode())
                .setSignatureStatus(SignatureStatus.NO_FINISH.getCode())
                .setAuthorizeFreeSignStatus(AuthorizeFreeSignStatus.UNAUTHORIZED.getCode())
                .setAuthorizeAgreementStatus(AgreementStatus.NO_FINISH.getCode())
                .setAuthorizeFreeSignDdl(null);

            for (SignatureCAExtDto caExtDto : c.getExt()) {
                caExtDto.setCertifyStatus(CertifyStatusEnum.NO_FINISH.getCode())
                    .setAuthorizeFreeSignStatus(AuthorizeFreeSignStatus.UNAUTHORIZED.getCode())
                    .setAuthorizeFreeSignDdl(null);
            }
        }).toList();
        // 重置CA
        inquirySignatureCaAuthMapper.updateById(list);
        // 重置电子签
        inquiryUserSignatureInformationService.deleteUserElectronicSignature(new InquiryUserElectronicSignatureSaveVO().setUserId(userId));
    }

    private void validateInquirySignatureCaAuthExists(Long id) {
        if (inquirySignatureCaAuthMapper.selectById(id) == null) {
            throw exception(INQUIRY_SIGNATURE_CA_AUTH_NOT_EXISTS);
        }
    }

    /**
     * 获取用户信息
     *
     * @param userId                用户id
     * @param signaturePlatformEnum 签章平台
     * @return
     */
    public InquirySignatureCaAuthDO getByUserId(Long userId, SignaturePlatformEnum signaturePlatformEnum, boolean... refresh) {
        InquirySignatureCaAuthDO caAuthDO = inquirySignatureCaAuthMapper.selectOne(InquirySignatureCaAuthDO::getUserId, userId, InquirySignatureCaAuthDO::getSignaturePlatform, signaturePlatformEnum.getCode());
        // 刷新免验证签授权状态
        if (refresh.length == 0 || refresh[0]) {
            refreshFreeSignStatus(caAuthDO);
        }
        return caAuthDO;
    }

    /**
     * 刷新免验证签授权状态 更新免验证签授权状态: 判断免验证签ddl是否过期 (法大大只有临期15/7/1天回调, 没有过期回调)
     *
     * @param caAuthDO
     */
    private void refreshFreeSignStatus(InquirySignatureCaAuthDO caAuthDO) {
        if (Objects.isNull(caAuthDO)) {
            return;
        }

        InquirySignatureCaAuthDO upd = InquirySignatureCaAuthDO.builder().id(caAuthDO.getId()).build();
        boolean update = false;
        if (Objects.equals(caAuthDO.getAuthorizeFreeSignStatus(), FddCaConstantEnum.AuthorizeFreeSignStatus.AUTHORIZED.getCode())
            && !Objects.isNull(caAuthDO.getAuthorizeFreeSignDdl())) {
            LocalDateTime freeSignDdl = caAuthDO.getAuthorizeFreeSignDdl();
            // 更新免验证签授权状态: 判断免验证签ddl是否过期 (法大大只有临期15/7/1天回调, 没有过期回调)
            if (freeSignDdl.isBefore(LocalDateTime.now())) {
                caAuthDO.setAuthorizeFreeSignStatus(FddCaConstantEnum.AuthorizeFreeSignStatus.UNAUTHORIZED.getCode());
                upd.setAuthorizeFreeSignStatus(FddCaConstantEnum.AuthorizeFreeSignStatus.UNAUTHORIZED.getCode());
                update = true;
                log.info("刷新免验证签授权状态为过期: {}", caAuthDO.getUserId());
            }
        }

        if (CollUtil.isNotEmpty(caAuthDO.getExt())) {
            for (SignatureCAExtDto caExtDto : caAuthDO.getExt()) {
                if (Objects.equals(caExtDto.getAuthorizeFreeSignStatus(), FddCaConstantEnum.AuthorizeFreeSignStatus.AUTHORIZED.getCode())
                    && !Objects.isNull(caExtDto.getAuthorizeFreeSignDdl())) {
                    LocalDateTime freeSignDdl = caExtDto.getAuthorizeFreeSignDdl();
                    if (freeSignDdl.isBefore(LocalDateTime.now())) {
                        caExtDto.setAuthorizeFreeSignStatus(FddCaConstantEnum.AuthorizeFreeSignStatus.UNAUTHORIZED.getCode());
                        upd.setExt(caAuthDO.getExt());
                        update = true;
                        log.info("刷新ext免验证签授权状态为过期: {}", caAuthDO.getUserId());
                    }
                }
            }
        }
        if (update) {
            int updCnt = inquirySignatureCaAuthMapper.updateById(upd);
        }
    }
}