package com.xyy.saas.inquiry.signature.server.mq.consumer.fdd;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.UserCaAuthCallbackVo;
import com.xyy.saas.inquiry.signature.server.convert.ca.InquirySignatureCaAuthConvert;
import com.xyy.saas.inquiry.signature.server.mq.message.FddCaAuthEvent;
import com.xyy.saas.inquiry.signature.server.service.ca.InquirySignatureCaAuthService;
import com.xyy.saas.inquiry.signature.server.service.fdd.enums.FddCallbackEvent;
import com.xyy.saas.inquiry.signature.server.service.fdd.handler.FddCallbackAuthorizeHandler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Desc CA认证回调
 * <AUTHOR> {@link FddCallbackAuthorizeHandler#handle(FddCallbackEvent, String)}
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_signature_server_mq_consumer_FddCaAuthMQConsumer",
    topic = FddCaAuthEvent.TOPIC)
public class FddCaAuthMQConsumer {

    @Resource
    private InquirySignatureCaAuthService inquirySignatureCaAuthService;

    @EventBusListener
    public void fddCaAuthConsumer(FddCaAuthEvent fddCaAuthEvent) {
        UserCaAuthCallbackVo userCaAuthCallbackVo = InquirySignatureCaAuthConvert.INSTANCE.convertVo(fddCaAuthEvent.getMsg());

        String eventId = userCaAuthCallbackVo.getEventId();
        FddCallbackEvent event = FddCallbackEvent.getCallbackEventEnum(eventId);
        if (event == null) {
            log.error("eventId: {} 事件不存在, 跳过处理!", eventId);
            return;
        }
        log.info("【FddCaAuth】eventId: {} 事件开始处理", eventId);
        switch (event) {
            case USER_AUTHORIZE:
            case PERSONAL_SEAL_AUTHORIZE_FREE_SIGN:
            case PERSONAL_SEAL_AUTHORIZE_FREE_SIGN_DUE_CANCEL:
                inquirySignatureCaAuthService.callBackInquirySignatureCaAuth(userCaAuthCallbackVo);
                break;
            default:
                log.info("eventId: {} 事件不处理", eventId);
                return;
        }
        log.info("eventId: {} 事件结束处理", eventId);

    }

}
