package com.xyy.saas.inquiry.signature.server.service.pdf;

import cn.hutool.core.util.URLUtil;
import com.lowagie.text.pdf.BaseFont;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.pdfbox.io.RandomAccessRead;
import org.apache.pdfbox.io.RandomAccessReadBuffer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Component
@AllArgsConstructor
public class PdfServiceFactory {

    private static final Logger log = LoggerFactory.getLogger(PdfServiceFactory.class);

    private final Map<String, PdfService> pdfServiceMap;
    private final PdfServiceProperties pdfServiceProperties;

    @Getter
    private static List<com.lowagie.text.pdf.BaseFont> openpdfFontList;
    @Getter
    private static List<com.itextpdf.text.pdf.BaseFont> itext5pdfFontList;
    @Getter
    private static List<org.apache.fontbox.ttf.TrueTypeFont> pdfboxFontList;

    @PostConstruct
    public void init() {
        assert pdfServiceMap != null;
        assert pdfServiceProperties != null;
        String type = pdfServiceProperties.getType();
        assert StringUtils.isNotBlank(type);

        // 中文字体问题
        List<File> fontFileList = this.loadFontFromClasspath();
        List<String> fontFilePathList = fontFileList.stream().map(File::getAbsolutePath).toList();
        log.info(">>>>>>>>>>>> 加载字体文件：\n{}", String.join("\n", fontFilePathList));

        openpdfFontList = createFonts(fontFileList, "OpenPDF", path -> {
            BaseFont bf = BaseFont.createFont(path, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            bf.setSubset(true);
            return bf;
        });
        itext5pdfFontList = createFonts(fontFileList, "ItextPDF", path -> {
            com.itextpdf.text.pdf.BaseFont bf = com.itextpdf.text.pdf.BaseFont.createFont(path, com.itextpdf.text.pdf.BaseFont.IDENTITY_H, com.itextpdf.text.pdf.BaseFont.EMBEDDED);
            bf.setSubset(true);
            return bf;
        });

        pdfboxFontList = createFonts(fontFileList, "Pdfbox", path -> {
            try (InputStream is = URLUtil.getStream(URLUtil.url(path))
                ; RandomAccessRead rar = new RandomAccessReadBuffer(is)) {
                return new org.apache.fontbox.ttf.TTFParser(true).parse(rar);
            }
        });
    }

    @PreDestroy
    public void destroy() {
        log.info(">>>>>>>>>>>> 关闭字体文件加载器");
        pdfboxFontList.forEach(i -> {
            try {
                if (i != null) {
                    i.close();
                }
            } catch (IOException e) {
                log.error(">>>>>>>>>>>> 关闭字体文件失败: {}", e.getMessage(), e);
            }
        });
    }

    private <T> List<T> createFonts(List<File> fontFileList, String type, ThrowableFunction<String, T> creator) {
        return fontFileList.stream().map(path -> {
            try {
                return creator.apply(path.getAbsolutePath());
            } catch (Exception e) {
                log.error(">>>>>>>>>>>> {}: 加载字体文件失败: {}", type, e.getMessage(), e);
                return null;
            }
        }).filter(Objects::nonNull).toList();
    }

    public PdfService getInstance() {
        assert pdfServiceProperties != null;
        String type = pdfServiceProperties.getType();
        assert StringUtils.isNotBlank(type);
        return this.getInstance(type);
    }

    public PdfService getInstance(String type) {
        assert pdfServiceMap.containsKey(type);
        return pdfServiceMap.get(type);
    }


    private List<File> loadFontFromClasspath() {
        String userDir = System.getProperty("user.dir");
        String fontsPath = pdfServiceProperties.getFontsPath();
        log.info(">>>>>>>>>>>> user.dir: {}， fontsPath: {}", userDir, fontsPath);
        // 遍历项目启动路径下：fonts目录下的字体文件 添加到字体解析器
        File fontsDir = new File(userDir + fontsPath);
        if (!fontsDir.exists()) {
            fontsDir.mkdirs();
        }
        if (!fontsDir.isDirectory()) {
            fontsDir.delete();
            fontsDir.mkdirs();
        }
        File[] files = fontsDir.listFiles(File::isFile);
        List<File> fontFileList = new ArrayList<>();
        List<String> fontFileNameList = new ArrayList<>();
        if (files != null) {
            for (File file : files) {
                fontFileList.add(file);
                fontFileNameList.add(file.getName());
            }
        }

        // 获取jar包中resources下的字体文件
        String fontsPathInJar = this.getClass().getClassLoader().getResource(fontsPath.replaceAll("^/+", "")).getPath();
        List<String> fontsHighPriority = pdfServiceProperties.getFontsHighPriority();
        log.info(">>>>>>>>>>>> fontsPathInJar: {}， fonts: {}", fontsPathInJar, fontsHighPriority);
        for (String f : fontsHighPriority) {
            try (InputStream is = this.getClass().getClassLoader().getResourceAsStream(fontsPath.replaceAll("^/+", "") + "/" + f);) {
                if (is == null) {
                    log.warn(">>>>>>>>>>>> jar中找不到字体文件：{}，跳过！", f);
                    continue;
                }
                if (fontFileNameList.contains(f)) {
                    log.info(">>>>>>>>>>>> 项目启动路径下已存在字体文件：{}，跳过！", f);
                    continue;
                }
                File copyFile = new File(fontsDir, f);
                FileUtils.copyInputStreamToFile(is, copyFile);
                log.info(">>>>>>>>>>>> copy字体文件：jar中 -> 项目启动路径下：{}", f);
                fontFileList.add(copyFile);
                fontFileNameList.add(copyFile.getName());
            } catch (IOException e) {
                log.error(">>>>>>>>>>>> copy字体文件失败: {}, {}", f, e.getMessage(), e);
            }
        }

        // 优先级排序：从高到低
        return fontFileList.stream().sorted(Comparator.comparingInt(f -> {
                int idx = fontsHighPriority.indexOf(f.getName());
                return idx == -1 ? Integer.MAX_VALUE : idx;
            }))
            .toList();
    }
}
