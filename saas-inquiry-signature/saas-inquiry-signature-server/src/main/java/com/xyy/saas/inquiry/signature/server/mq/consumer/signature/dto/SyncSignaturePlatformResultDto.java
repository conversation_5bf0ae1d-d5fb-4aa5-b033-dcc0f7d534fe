package com.xyy.saas.inquiry.signature.server.mq.consumer.signature.dto;

import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2025/02/20 10:45
 */
@Data
@Builder
@Accessors(chain = true)
public class SyncSignaturePlatformResultDto {


    /**
     * 三方签署任务id signTaskId
     */
    private String thirdId;

    /**
     * 同步的签章平台
     */
    private Integer syncPlatform;

    /**
     * 同步状态
     */
    private Integer syncPlatformStatus;

    /**
     * 同步说明
     */
    private String remark;

}
