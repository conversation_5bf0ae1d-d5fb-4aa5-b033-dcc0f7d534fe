package com.xyy.saas.inquiry.signature.server.dal.dataobject.ca;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.pojo.signature.SignatureCAExtDto;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;

/**
 * CA认证 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_inquiry_signature_ca_auth", autoResultMap = true)
@KeySequence("saas_inquiry_signature_ca_auth_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class InquirySignatureCaAuthDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 用户id
     */
    private Long userId;
    /**
     * 签章平台 0-自签署 1-法大大
     */
    private Integer signaturePlatform;
    /**
     * 实名认证状态 0: 待认证，1: 认证完成，2: 认证失败
     */
    private Integer certifyStatus;
    /**
     * 签名状态 0: 未签名，1: 已签名
     */
    private Integer signatureStatus;
    /**
     * 免签授权状态 0: 未授权，1: 已授权，
     */
    private Integer authorizeFreeSignStatus;
    /**
     * 免签授权截止时间
     */
    private LocalDateTime authorizeFreeSignDdl;
    /**
     * 授权协议签署状态 0: 未签署，1: 已签署
     */
    private Integer authorizeAgreementStatus;
    /**
     * 兼职协议状态 0: 未签署，1: 已签署
     */
    private Integer partTimeAgreementStatus;

    /**
     * CA认证扩展信息-其他应用下得CA认证信息
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private List<SignatureCAExtDto> ext = new ArrayList<>();

}