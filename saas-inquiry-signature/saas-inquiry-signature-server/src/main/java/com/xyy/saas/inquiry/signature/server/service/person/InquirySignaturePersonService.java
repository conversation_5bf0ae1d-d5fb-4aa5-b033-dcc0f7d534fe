package com.xyy.saas.inquiry.signature.server.service.person;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.fasc.open.api.v5_1.req.user.GetUserAuthUrlReq;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.signature.server.controller.admin.person.vo.InquirySignaturePersonPageReqVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.person.vo.InquirySignaturePersonSaveReqVO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.person.InquirySignaturePersonDO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 签章平台用户 Service 接口
 *
 * <AUTHOR>
 */
public interface InquirySignaturePersonService {

    /**
     * 存储法大大认证用户信息
     *
     * @param req
     */
    InquirySignaturePersonDO saveFddPerson(Integer signaturePlatformConfigId, GetUserAuthUrlReq req, String openUserId);


    /**
     * 保存或者修改签章平台用户
     *
     * @param createReqVO
     * @return
     */
    Long saveOrUpdateInquirySignaturePerson(InquirySignaturePersonSaveReqVO createReqVO);

    /**
     * 创建签章平台用户
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInquirySignaturePerson(@Valid InquirySignaturePersonSaveReqVO createReqVO);

    /**
     * 更新签章平台用户
     *
     * @param updateReqVO 更新信息
     */
    void updateInquirySignaturePerson(@Valid InquirySignaturePersonSaveReqVO updateReqVO);

    /**
     * 删除签章平台用户
     *
     * @param id 编号
     */
    void deleteInquirySignaturePerson(Long id);

    /**
     * 获得签章平台用户
     *
     * @param id 编号
     * @return 签章平台用户
     */
    InquirySignaturePersonDO getInquirySignaturePerson(Long id);

    /**
     * 获得签章平台用户分页
     *
     * @param pageReqVO 分页查询
     * @return 签章平台用户分页
     */
    PageResult<InquirySignaturePersonDO> getInquirySignaturePersonPage(InquirySignaturePersonPageReqVO pageReqVO);

    /**
     * 根据userId 查签章用户
     *
     * @param userId userid
     * @return 签章用户
     */
    InquirySignaturePersonDO queryPersonByUserId(Long userId, SignaturePlatformEnum signaturePlatformEnum, Integer signaturePlatformConfigId);

    InquirySignaturePersonDO queryPersonByOpenId(String openUserId, SignaturePlatformEnum signaturePlatformEnum, Integer signaturePlatformConfigId);


    List<InquirySignaturePersonDO> queryPersonByUserId(Long userId, SignaturePlatformEnum signaturePlatformEnum);

    /**
     * 根据userIds 查签章用户
     *
     * @param userIds userIds
     * @return 签章用户s
     */
    List<InquirySignaturePersonDO> queryPersonByUserIds(List<Long> userIds, SignaturePlatformEnum signaturePlatformEnum, Integer platformConfigId);

    /**
     * 更新签章平台用户
     *
     * @param personDO
     */
    void updateInquirySignaturePerson(InquirySignaturePersonDO personDO);

}