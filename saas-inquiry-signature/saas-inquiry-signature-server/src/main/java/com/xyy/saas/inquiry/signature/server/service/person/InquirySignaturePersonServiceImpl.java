package com.xyy.saas.inquiry.signature.server.service.person;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.signature.enums.ErrorCodeConstants.INQUIRY_SIGNATURE_PERSON_NOT_EXISTS;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.alibaba.fastjson.JSON;
import com.fasc.open.api.v5_1.req.user.GetUserAuthUrlReq;
import com.xyy.saas.inquiry.enums.signature.FddUserStatusEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.signature.server.controller.admin.person.vo.InquirySignaturePersonPageReqVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.person.vo.InquirySignaturePersonSaveReqVO;
import com.xyy.saas.inquiry.signature.server.convert.person.InquirySignaturePersonConvert;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.person.InquirySignaturePersonDO;
import com.xyy.saas.inquiry.signature.server.dal.mysql.person.InquirySignaturePersonMapper;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Service;

/**
 * 签章平台用户 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class InquirySignaturePersonServiceImpl implements InquirySignaturePersonService {

    @Resource
    private InquirySignaturePersonMapper inquirySignaturePersonMapper;

    @Override
    public InquirySignaturePersonDO saveFddPerson(Integer signaturePlatformConfigId, GetUserAuthUrlReq req, String openUserId) {
        // 查询账号是否存在
        InquirySignaturePersonDO signaturePerson = inquirySignaturePersonMapper.selectOneByCondition(InquirySignaturePersonPageReqVO
            .builder().userId(NumberUtils.toLong(req.getClientUserId())).signaturePlatform(SignaturePlatformEnum.FDD.getCode()).signaturePlatformConfigId(signaturePlatformConfigId).build());
        if (signaturePerson != null) {
            // 修复openUserId为空场景
            if (StringUtils.isNotBlank(openUserId) && !StringUtils.equals(signaturePerson.getOpenUserId(), openUserId)) {
                log.info("当前账号openUserId为空，修改账号: {}", signaturePerson.getAccountName());
                signaturePerson.setOpenUserId(openUserId);
                signaturePerson.setAccountName(req.getAccountName());
                signaturePerson.setMobile(req.getAccountName());
                signaturePerson.setUserName(req.getUserName());
                signaturePerson.setUserIdentNo(req.getUserIdentNo());
                inquirySignaturePersonMapper.updateById(signaturePerson);
            }

            return signaturePerson;
        }
        signaturePerson = InquirySignaturePersonConvert.INSTANCE.getUserAuthUrlReq2Po(req);
        signaturePerson.setUserStatus(StringUtils.isNotBlank(openUserId) ? FddUserStatusEnum.SIGNED.getCode() : FddUserStatusEnum.UNAUTHORIZED.getCode());
        signaturePerson.setOpenUserId(openUserId);
        signaturePerson.setSignaturePlatform(SignaturePlatformEnum.FDD.getCode());
        signaturePerson.setSignaturePlatformConfigId(signaturePlatformConfigId);
        int i = inquirySignaturePersonMapper.insert(signaturePerson);
        log.info("当前账号未注册，插入新账号: {}, {}", JSON.toJSONString(signaturePerson), i);
        return signaturePerson;
    }


    @Override
    public Long saveOrUpdateInquirySignaturePerson(InquirySignaturePersonSaveReqVO createReqVO) {
        InquirySignaturePersonDO inquirySignaturePerson = InquirySignaturePersonConvert.INSTANCE.convertDo(createReqVO);
        InquirySignaturePersonDO signaturePerson = inquirySignaturePersonMapper.selectOne(InquirySignaturePersonDO::getUserId, createReqVO.getUserId());
        if (signaturePerson == null) {
            inquirySignaturePersonMapper.insert(inquirySignaturePerson);
        } else {
            inquirySignaturePersonMapper.updateById(inquirySignaturePerson.setId(signaturePerson.getId()));
        }
        return inquirySignaturePerson.getId();
    }

    @Override
    public Long createInquirySignaturePerson(InquirySignaturePersonSaveReqVO createReqVO) {
        // 插入
        InquirySignaturePersonDO inquirySignaturePerson = BeanUtils.toBean(createReqVO, InquirySignaturePersonDO.class);
        inquirySignaturePersonMapper.insert(inquirySignaturePerson);
        // 返回
        return inquirySignaturePerson.getId();
    }

    @Override
    public void updateInquirySignaturePerson(InquirySignaturePersonSaveReqVO updateReqVO) {
        // 校验存在
        validateInquirySignaturePersonExists(updateReqVO.getId());
        // 更新
        InquirySignaturePersonDO updateObj = BeanUtils.toBean(updateReqVO, InquirySignaturePersonDO.class);
        inquirySignaturePersonMapper.updateById(updateObj);
    }

    @Override
    public void deleteInquirySignaturePerson(Long id) {
        // 校验存在
        validateInquirySignaturePersonExists(id);
        // 删除
        inquirySignaturePersonMapper.deleteById(id);
    }

    private void validateInquirySignaturePersonExists(Long id) {
        if (inquirySignaturePersonMapper.selectById(id) == null) {
            throw exception(INQUIRY_SIGNATURE_PERSON_NOT_EXISTS);
        }
    }

    @Override
    public InquirySignaturePersonDO getInquirySignaturePerson(Long id) {
        return inquirySignaturePersonMapper.selectById(id);
    }

    @Override
    public PageResult<InquirySignaturePersonDO> getInquirySignaturePersonPage(InquirySignaturePersonPageReqVO pageReqVO) {
        return inquirySignaturePersonMapper.selectPage(pageReqVO);
    }

    @Override
    public InquirySignaturePersonDO queryPersonByUserId(Long userId, SignaturePlatformEnum signaturePlatformEnum, Integer signaturePlatformConfigId) {
        return inquirySignaturePersonMapper.selectOneByCondition(InquirySignaturePersonPageReqVO.builder().userId(userId)
            .signaturePlatform(signaturePlatformEnum.getCode()).signaturePlatformConfigId(signaturePlatformConfigId).build());
    }

    @Override
    public InquirySignaturePersonDO queryPersonByOpenId(String openUserId, SignaturePlatformEnum signaturePlatformEnum, Integer signaturePlatformConfigId) {
        return inquirySignaturePersonMapper.selectOneByCondition(InquirySignaturePersonPageReqVO.builder().openUserId(openUserId)
            .signaturePlatform(signaturePlatformEnum.getCode()).signaturePlatformConfigId(signaturePlatformConfigId).build());
    }

    @Override
    public List<InquirySignaturePersonDO> queryPersonByUserId(Long userId, SignaturePlatformEnum signaturePlatformEnum) {
        return inquirySignaturePersonMapper.selectList(
            new LambdaQueryWrapperX<InquirySignaturePersonDO>().eq(InquirySignaturePersonDO::getUserId, userId).eq(InquirySignaturePersonDO::getSignaturePlatform, signaturePlatformEnum.getCode()));

    }

    @Override
    public List<InquirySignaturePersonDO> queryPersonByUserIds(List<Long> userIds, SignaturePlatformEnum signaturePlatformEnum, Integer platformConfigId) {
        return inquirySignaturePersonMapper.selectList(
            new LambdaQueryWrapperX<InquirySignaturePersonDO>().in(InquirySignaturePersonDO::getUserId, userIds)
                .eq(InquirySignaturePersonDO::getSignaturePlatform, signaturePlatformEnum.getCode())
                .eqIfPresent(InquirySignaturePersonDO::getSignaturePlatformConfigId, platformConfigId));
    }

    @Override
    public void updateInquirySignaturePerson(InquirySignaturePersonDO personDO) {
        if (personDO == null || personDO.getId() == null) {
            return;
        }
        inquirySignaturePersonMapper.updateById(personDO);
    }
}