package com.xyy.saas.inquiry.signature.server.dal.mysql.platform;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.signature.server.controller.admin.platform.vo.InquirySignatureCallbackLogPageReqVO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.platform.InquirySignatureCallbackLogDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 签章回调日志 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquirySignatureCallbackLogMapper extends BaseMapperX<InquirySignatureCallbackLogDO> {

    default PageResult<InquirySignatureCallbackLogDO> selectPage(InquirySignatureCallbackLogPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InquirySignatureCallbackLogDO>()
            .eqIfPresent(InquirySignatureCallbackLogDO::getSignaturePlatform, reqVO.getSignaturePlatform())
            .eqIfPresent(InquirySignatureCallbackLogDO::getType, reqVO.getType())
            .eqIfPresent(InquirySignatureCallbackLogDO::getBizId, reqVO.getBizId())
            .eqIfPresent(InquirySignatureCallbackLogDO::getBizContent, reqVO.getBizContent())
            .betweenIfPresent(InquirySignatureCallbackLogDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(InquirySignatureCallbackLogDO::getId));
    }

}