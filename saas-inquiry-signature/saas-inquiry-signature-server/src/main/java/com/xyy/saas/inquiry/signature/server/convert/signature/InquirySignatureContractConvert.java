package com.xyy.saas.inquiry.signature.server.convert.signature;

import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import com.alibaba.fastjson.JSON;
import com.xyy.saas.inquiry.enums.prescription.template.PrescriptionTemplateFieldEnum;
import com.xyy.saas.inquiry.enums.signature.ContractTypeEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.pojo.prescription.ParticipantItem;
import com.xyy.saas.inquiry.pojo.signature.SignatureContractExtDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureAuditDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureInitDto;
import com.xyy.saas.inquiry.signature.api.signature.dto.InquiryFlushContractReqDto;
import com.xyy.saas.inquiry.signature.api.signature.dto.InquiryFlushContractRespDto;
import com.xyy.saas.inquiry.signature.api.signature.dto.InquirySignatureContractDto;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractSaveReqVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractStatusVO;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.InquirySignatureCaAuthRespVO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquirySignatureContractDO;
import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionSignatureMessage;
import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionSignaturePlatformRequireMessage;
import com.xyy.saas.inquiry.signature.server.service.fdd.dto.FddSignTaskCreateDto;
import com.xyy.saas.inquiry.util.PrefUtil;
import java.util.Collections;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author:chenxiaoyi
 * @Date:2024/10/12 17:17
 */
@Mapper
public interface InquirySignatureContractConvert {

    InquirySignatureContractConvert INSTANCE = Mappers.getMapper(InquirySignatureContractConvert.class);

    default InquirySignatureContractSaveReqVO convertTaskCreateSaveVo(FddSignTaskCreateDto taskCreateDto, String signTaskId) {
        return InquirySignatureContractSaveReqVO.builder()
            .pref(PrefUtil.getContractPref())
            .signaturePlatform(SignaturePlatformEnum.FDD.getCode())
            .thirdId(signTaskId)
            .bizId(taskCreateDto.getBizId())
            .contractType(taskCreateDto.getContractType().getCode())
            .initiatorUserId(taskCreateDto.getUserId())
            .initiatorName(taskCreateDto.getNickname())
            .initiatorMobile(taskCreateDto.getMobile())
            .participants(Collections.singletonList(ParticipantItem.builder().userId(taskCreateDto.getUserId()).name(taskCreateDto.getNickname()).mobile(taskCreateDto.getMobile()).build()))
            .paramDetail(JSON.toJSONString(taskCreateDto.getParamMap()))
            .ext(SignatureContractExtDto.builder().platformConfigId(taskCreateDto.getPlatformConfig().getConfigId()).build())
            .build();

    }

    default InquirySignatureContractSaveReqVO convertVoByIssuePrescription(PrescriptionSignatureInitDto psDto) {
        String contractPref = PrefUtil.getContractPref();
        psDto.setContractPref(contractPref);
        return InquirySignatureContractSaveReqVO.builder()
            .pref(contractPref)
            .templateId(psDto.getTemplateId() == null ? null : psDto.getTemplateId().toString())
            .signaturePlatform(psDto.getSignaturePlatform())
            .bizId(psDto.getPrescriptionPref())
            .contractType(ContractTypeEnum.PRESCRIPTION.getCode())
            .initiatorUserId(psDto.getParticipantItem().getUserId())
            .initiatorName(psDto.getParticipantItem().getName())
            .initiatorMobile(psDto.getParticipantItem().getMobile())
            .participants(Collections.singletonList(psDto.getParticipantItem()))
            .paramDetail(JSON.toJSONString(psDto.getParam()))
            .ext(SignatureContractExtDto.builder().platformConfigId(psDto.getSignaturePlatformConfigId()).build())
            .build();
    }

    default InquirySignatureContractSaveReqVO convertVoByRemotePrescription(PrescriptionSignatureAuditDto psAuditDto) {
        String contractPref = PrefUtil.getContractPref();
        psAuditDto.setContractPref(contractPref);
        psAuditDto.setParticipantItems(Collections.singletonList(psAuditDto.getParticipantItem()));
        return InquirySignatureContractSaveReqVO.builder()
            .pref(contractPref)
            .bizId(psAuditDto.getPrescriptionPref())
            .imgUrl(psAuditDto.getPrescriptionImgUrl())
            .contractType(ContractTypeEnum.PRESCRIPTION.getCode())
            .initiatorUserId(psAuditDto.getParticipantItem().getUserId())
            .initiatorName(psAuditDto.getParticipantItem().getName())
            .initiatorMobile(psAuditDto.getParticipantItem().getMobile())
            .participants(psAuditDto.getParticipantItems())
            .ext(SignatureContractExtDto.builder()
                .inquiryBizType(psAuditDto.getInquiryBizType())
                .remotePrescriptionUrl(psAuditDto.getPrescriptionImgUrl())
                .coordinateX(psAuditDto.getCoordinate().getX())
                .coordinateY(psAuditDto.getCoordinate().getY()).build())
            .build();
    }

    InquirySignatureContractDO convertStatusVo(InquirySignatureContractStatusVO signatureContractStatusVO);

    InquirySignatureContractDO convertDo(InquirySignatureContractSaveReqVO createReqVO);

    @Mapping(target = "pref", source = "contractPref")
    InquirySignatureContractStatusVO convertPsMessage(PrescriptionSignatureMessage psMessageDto);


    PrescriptionSignaturePlatformRequireMessage convertRequireMessage(PrescriptionSignatureInitDto psDto);

    PrescriptionSignaturePlatformRequireMessage convertRequireMessage(PrescriptionSignatureAuditDto psAuditDto);

    default void convertFillRemoteParticipant(PrescriptionSignatureAuditDto psAuditDto, InquirySignatureCaAuthRespVO caAuthRespVO) {
        psAuditDto.getParticipantItem()
            .setSignImgUrl(StringUtils.defaultIfBlank(psAuditDto.getParticipantItem().getSignImgUrl(), caAuthRespVO.getRealSignatureUrl()))
            .setAccessPlatform(caAuthRespVO.isDrawnSign() ? CommonStatusEnum.DISABLE.getStatus() : CommonStatusEnum.ENABLE.getStatus())
            .setActorField(PrescriptionTemplateFieldEnum.PHARMACIST.getField())
            .setActorFieldName(PrescriptionTemplateFieldEnum.PHARMACIST.getFieldName())
            .setBizId(psAuditDto.getAuditRecordId());
    }

    InquirySignatureContractDto convert(InquirySignatureContractDO signatureContractDO);

    default InquirySignatureContractSaveReqVO convertFlushContract(InquirySignatureContractDO contract, InquiryFlushContractReqDto reqDto) {

        Optional.ofNullable(reqDto.getParamDto()).ifPresent(p -> {
            String paramDetail = JSON.toJSONString(reqDto.getParamDto());
            contract.setParamDetail(paramDetail);
        });
        Optional.ofNullable(reqDto.getParticipants()).ifPresent(p -> {
            contract.setParticipants(reqDto.getParticipants());
        });

        return InquirySignatureContractSaveReqVO.builder().id(contract.getId())
            .signaturePlatform(contract.getSignaturePlatform())
            .participants(contract.getParticipants())
            .paramDetail(contract.getParamDetail()).build();

    }

    InquiryFlushContractRespDto convertFlushRespDto(InquirySignatureContractSaveReqVO updateReqVO);
}
