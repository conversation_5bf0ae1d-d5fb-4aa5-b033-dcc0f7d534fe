package com.xyy.saas.inquiry.signature.server.service.fdd.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 法大大 授权相关回调类
 *
 * @Author:chenxia<PERSON>i
 * @Date:2024/02/29 11:12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EventCallbackHeaderDto implements Serializable {

    /**
     * 应用的AppId。
     */
    private String appId;

    /**
     * 签名算法类型：固定HMAC-SHA256。
     */
    private String signType;

    /**
     * 请求参数的签名值。
     */
    private String sign;

    /**
     * Unix标准时间戳，精确到毫秒。
     */
    private String timestamp;

    /**
     * 随机数。最长32个字符，10分钟内不能重复。
     */
    private String nonce;

    /**
     * 事件ID。
     */
    private String event;

    /**
     * 事件ID对应具体事件的请求参数，json字符串。
     */
    private String bizContent;
}
