package com.xyy.saas.inquiry.signature.server.service.fdd.enums;

import com.xyy.saas.inquiry.signature.server.service.fdd.bean.FddCallbackHandlerCode;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 回调事件枚举
 *
 * @Author:chenxiaoyi
 * @Date:2024/02/29 10:16
 */
public enum FddCallbackEvent {

    /**
     * 授权相关
     */
    USER_AUTHORIZE("user-authorize", FddCallbackHandlerCode.AUTHORIZE, "用户授权事件"),
    CORP_AUTHORIZE("corp-authorize", FddCallbackHandlerCode.AUTHORIZE, "企业授权事件"),

    /**
     * 签名相关
     */
    PERSONAL_SEAL_AUTHORIZE_FREE_SIGN("personal-seal-authorize-free-sign", FddCallbackHandlerCode.AUTHORIZE, "个人签名授权免验证签事件"),
    PERSONAL_SEAL_AUTHORIZE_FREE_SIGN_DUE_CANCEL("personal-seal-authorize-free-sign-due-cancel", FddCallbackHandlerCode.AUTHORIZE, "签名免验证签即将到期事件"),

    /**
     * 签署任务相关
     */
    SIGN_TASK_SIGNED("sign-task-signed", FddCallbackHandlerCode.SIGN_TASK, "签署任务参与方签署成功事件"),

    SIGN_TASK_SIGN_FAILED("sign-task-sign-failed", FddCallbackHandlerCode.SIGN_TASK, "签署任务签署失败事件"), // signTaskStatus  sign_progress：签署中

//    SIGN_TASK_FILL_REJECTED("sign-task-fill-rejected", FddCallbackHandlerCode.SIGN_TASK,"签署任务参与方拒填事件"), //signTaskStatus  task_terminated：任务已终止

    SIGN_TASK_SIGN_REJECTED("sign-task-sign-rejected", FddCallbackHandlerCode.SIGN_TASK, "签署任务参与方拒签事件"), // signTaskStatus  task_terminated：任务已终止

    SIGN_TASK_CANCELED("sign-task-canceled", FddCallbackHandlerCode.SIGN_TASK, "签署任务撤销事件"), // signTaskStatus task_terminated：任务已终止

//    SIGN_TASK_ABOLISH("sign-task-abolish", FddCallbackHandlerCode.SIGN_TASK,"签署任务作废事件"), //signTaskStatus  revoked：已作废

    SIGN_TASK_EXPIRE("sign-task-expire", FddCallbackHandlerCode.SIGN_TASK, "签署任务过期事件"),  // signTaskStatus  expired：已逾期

    SIGN_TASK_FINISHED("sign-task-finished", FddCallbackHandlerCode.SIGN_TASK, "签署任务完成事件"); // signTaskStatus  task_finished：任务已完成


    public final String eventCode;

    public final String handlerCode;

    public final String eventName;


    FddCallbackEvent(String eventCode, String handlerCode, String eventName) {
        this.eventCode = eventCode;
        this.handlerCode = handlerCode;
        this.eventName = eventName;
    }

    /**
     * 根据事件编码返回指定枚举
     *
     * @param eventCode 事件编码
     * @return 事件枚举
     */
    public static FddCallbackEvent getCallbackEventEnum(String eventCode) {
        for (FddCallbackEvent callbackEventEnum : FddCallbackEvent.values()) {
            if (Objects.equals(callbackEventEnum.eventCode, eventCode)) {
                return callbackEventEnum;
            }
        }
        return null;
    }

    /**
     * 获取所有事件状态
     *
     * @return
     */
    public static List<String> allEventCodes() {
        return Arrays.stream(values()).map(i -> i.eventCode).collect(Collectors.toList());
    }

    /**
     * 判断是否有匹配的事件
     *
     * @param eventCode
     * @return
     */
    public static boolean matchEvent(String eventCode) {
        if (StringUtils.isBlank(eventCode)) {
            return false;
        }
        return allEventCodes().contains(eventCode);
    }

    /**
     * 判断是否是签署的事件
     *
     * @param eventCode
     * @return
     */
    public static boolean isSignedEvent(String eventCode) {
        return StringUtils.equalsAnyIgnoreCase(eventCode, SIGN_TASK_SIGNED.eventCode, SIGN_TASK_SIGN_FAILED.eventCode, SIGN_TASK_FINISHED.eventCode);

    }

}
