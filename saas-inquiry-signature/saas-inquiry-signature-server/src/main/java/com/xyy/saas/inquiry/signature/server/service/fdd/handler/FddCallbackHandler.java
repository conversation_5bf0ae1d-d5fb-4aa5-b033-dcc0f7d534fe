package com.xyy.saas.inquiry.signature.server.service.fdd.handler;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.signature.server.service.fdd.enums.FddCallbackEvent;

/**
 * @Author:chen<PERSON><PERSON>i
 * @Date:2024/02/29 10:46
 */
public interface FddCallbackHandler {

    CommonResult<Object> handle(FddCallbackEvent eventEnum, String appId, String bizContent);

}
