package com.xyy.saas.inquiry.signature.server.service.pdf;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
@Data
@Component
@ConfigurationProperties(prefix = "inquiry.pdf-service")
public class PdfServiceProperties {

    /**
     * 模板类型，目前支持：
     * <p>1. openPdfService：{@link com.xyy.saas.inquiry.signature.server.service.pdf.OpenPdfServiceImpl}</p>
     * <p>2. itext5PdfService：{@link com.xyy.saas.inquiry.signature.server.service.pdf.Itext5PdfServiceImpl}</p>
     * <p>3. pdfboxService：{@link com.xyy.saas.inquiry.signature.server.service.pdf.PdfboxServiceImpl}</p>
     */
    private String type = "pdfboxService";

    /**
     * 字体路径, user.dir 目录下，要以/开头
     */
    private String fontsPath = "/fonts";
    private List<String> fontsHighPriority = Arrays.asList("msyh.ttf", "simple.ttf");
}
