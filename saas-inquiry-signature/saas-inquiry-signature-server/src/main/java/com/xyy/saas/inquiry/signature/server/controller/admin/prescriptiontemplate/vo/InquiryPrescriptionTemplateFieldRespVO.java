package com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo;

import com.xyy.saas.inquiry.enums.prescription.template.PrescriptionTemplateFieldEnum;
import com.xyy.saas.inquiry.enums.prescription.template.TemplateFieldTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;
import lombok.Data;

/**
 * {@link PrescriptionTemplateFieldEnum}
 */
@Schema(description = "管理后台 - 处方笺模板字段 Response VO")
@Data
@Builder
public class InquiryPrescriptionTemplateFieldRespVO {

    @Schema(description = "字段", requiredMode = Schema.RequiredMode.REQUIRED, example = "12132")
    private String field;

    @Schema(description = "名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "12132")
    private String fieldName;

    /**
     * {@link TemplateFieldTypeEnum}
     */
    @Schema(description = "类型", requiredMode = Schema.RequiredMode.REQUIRED, example = "12132")
    private Integer fieldType;

}