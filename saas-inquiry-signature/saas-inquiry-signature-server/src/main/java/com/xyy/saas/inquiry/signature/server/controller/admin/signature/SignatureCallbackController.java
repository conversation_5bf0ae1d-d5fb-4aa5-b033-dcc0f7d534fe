package com.xyy.saas.inquiry.signature.server.controller.admin.signature;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.pojo.signature.PlatformConfigExtDto;
import com.xyy.saas.inquiry.signature.server.service.fdd.FddCallbackService;
import com.xyy.saas.inquiry.signature.server.service.fdd.dto.EventCallbackHeaderDto;
import com.xyy.saas.inquiry.signature.server.service.platform.InquirySignaturePlatformService;
import com.xyy.saas.inquiry.signature.server.util.FddSignUtil;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestHeader;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * /admin-api/kernel/fdd/notify/fddEventCallback
 *
 * @Author:chenxiaoyi
 * @Date:2024/11/18 13:37
 */
@Tag(name = "签章平台回调")
@RestController
@RequestMapping("/fdd/notify")
@Slf4j
public class SignatureCallbackController {

    @Resource
    private InquirySignaturePlatformService inquirySignaturePlatformService;

    @Resource
    private FddCallbackService fddCallbackService;

    /**
     * 法大大 - 事件回调接口 https://dev.fadada.com/api-doc/SFEQBCY1GA/OUJ1WHN26Y8GFSSZ/5-1 所有的事件都会回调这个入口 根据event事件标识来区分 1.授权 2.免签 3.签署任务 ....
     */
    @ResponseBody
    @PostMapping(value = "/fddEventCallback")
    @ApiAccessLog(enable = false)
    public CommonResult fddEventCallback(@RequestHeader HttpHeaders headers,
        @RequestParam("bizContent") String bizContent) {
        // 获取请求头参数
        String appId = headers.getFirst("X-FASC-App-Id");
        String signType = headers.getFirst("X-FASC-Sign-Type");
        String sign = headers.getFirst("X-FASC-Sign");
        String timestamp = headers.getFirst("X-FASC-Timestamp");
        String event = headers.getFirst("X-FASC-Event");
        String nonce = headers.getFirst("X-FASC-Nonce");

        log.info("法大大 in.SignatureCallbackController.fddEventCallback 事件:{},appId:{},bizContent:{}", event, appId, bizContent);
        // 验签
        EventCallbackHeaderDto dto = EventCallbackHeaderDto
            .builder()
            .appId(appId).signType("HMAC-SHA256").sign(sign)
            .timestamp(timestamp).nonce(nonce).event(event).bizContent(bizContent)
            .build();
        PlatformConfigExtDto platformConfig = inquirySignaturePlatformService.getSignaturePlatformConfig(SignaturePlatformEnum.FDD.getCode(), appId);
        if (platformConfig == null) {
            return CommonResult.success("没有对应的appId应用");
        }

        if (!FddSignUtil.isSignatureValid(platformConfig.getAppSecret(), dto)) {
            log.error("法大大回调,签名失败,事件:{},入参:{}", event, bizContent);
            // 为了不重复接收该请求，建议这里返回success，返回success后这条消息法大大将中断重试回调机制
            return CommonResult.success("success");
        }

        // 处理回调api
        return fddCallbackService.eventCallback(event, appId, bizContent);
    }


}
