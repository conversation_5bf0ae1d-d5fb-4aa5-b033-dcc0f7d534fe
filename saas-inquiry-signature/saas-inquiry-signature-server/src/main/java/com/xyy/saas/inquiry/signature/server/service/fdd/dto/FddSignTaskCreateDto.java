package com.xyy.saas.inquiry.signature.server.service.fdd.dto;

import com.xyy.saas.inquiry.enums.signature.ContractTypeEnum;
import com.xyy.saas.inquiry.pojo.signature.PlatformConfigExtDto;
import java.time.LocalDateTime;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2024/11/20 14:27
 */
@Data
@Accessors(chain = true)
@Builder
public class FddSignTaskCreateDto {

    /**
     * 业务id - 处方号
     */
    private String bizId;
    /**
     * 用户名称
     */
    private String nickname;

    /**
     * 用户id
     */
    private Long userId;
    /**
     * 用户手机号
     */
    private String mobile;
    /**
     * 法大大平台为该用户在该应用appId范围内分配的唯一标识
     */
    private String openUserId;
    /**
     * 用户免签id
     */
    private String sealId;
    /**
     * 参与方id
     */
    private String actorId;
    /**
     * 文档id
     */
    private String docId;
    /**
     * 文件id
     */
    private String fileId;

    /**
     * 是否添加控件
     */
    private boolean addDoc;

    /**
     * 定位模式： pixel: 像素值，即坐标定位，计算方法参考文档页面坐标定位计算方法。 keyword: 关键字定位。
     */
    private String positionMode;
    /**
     * 心点定位横向坐标
     */
    private String positionX;

    /**
     * 心点定位纵向坐标
     */
    private String positionY;

    /**
     * 控件位置关键字
     */
    private String positionKeyword;
    /**
     * 控件位置关键字X轴偏移量
     */
    private String keywordOffsetX;
    /**
     * 控件位置关键字Y轴偏移量
     */
    private String keywordOffsetY;
    /**
     * 签章平台配置dto
     */
    private PlatformConfigExtDto platformConfig;

    /**
     * 合同模板id
     */
    private String signTemplateId;

    /**
     * 合同类型
     */
    private ContractTypeEnum contractType;

    /**
     * 签署任务过期时间
     */
    private LocalDateTime expiresTime;

    /**
     * 填充的任务控件参数
     */
    private Map<String, String> paramMap;

    /**
     * 基于文档创建签署任务的文档url
     */
    private String fileUrl;

    /**
     * 签署任务id
     */
    private String signTaskId;

    /**
     * 法大大应用 configId
     */
    private Integer configId;

    /**
     * 免登录 - 仅用于指定免签印章时候，默认 为空
     */
    private Boolean freeLogin;
}
