package com.xyy.saas.inquiry.signature.server.dal.dataobject.signature;

import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.enums.signature.ContractStatusEnum;
import com.xyy.saas.inquiry.pojo.prescription.ParticipantItem;
import com.xyy.saas.inquiry.pojo.signature.SignatureContractExtDto;
import java.util.List;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.ToString;
import lombok.experimental.Accessors;
import org.apache.commons.lang3.StringUtils;

/**
 * 签章合同 DO
 *
 * <AUTHOR>
 */
@TableName(value = "saas_inquiry_signature_contract", autoResultMap = true)
@KeySequence("saas_inquiry_signature_contract_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
public class InquirySignatureContractDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    /**
     * 合同编号,系统生成
     */
    private String pref;
    /**
     * 签章平台  0-无 1-法大大 {@link com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum}
     */
    private Integer signaturePlatform;

    /**
     * 合同状态
     */
    private Integer contractStatus;

    /**
     * 自绘合同标识  0-是,1-否 {@link cn.iocoder.yudao.framework.common.enums.CommonStatusEnum}
     */
    private Integer selfDrawn;

    /**
     * 合同业务类型 {@link com.xyy.saas.inquiry.enums.signature.ContractTypeEnum}
     */
    private Integer contractType;
    /**
     * 业务方唯一标识
     */
    private String bizId;
    /**
     * 三方签署任务id signTaskId
     */
    private String thirdId;

    /**
     * 模板id - 使用String兼容其他场景
     */
    private String templateId;

    /**
     * 发起方uerId
     */
    private Long initiatorUserId;
    /**
     * 发起方姓名
     */
    private String initiatorName;
    /**
     * 发起方联系方式
     */
    private String initiatorMobile;
    /**
     * 参与方集合
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private List<ParticipantItem> participants;
    /**
     * 合同参数详情 json
     */
    private String paramDetail;
    /**
     * 合同图片
     */
    private String imgUrl;
    /**
     * 合同PDF文件
     */
    private String pdfUrl;


    /**
     * 同步平台
     */
    private Integer syncPlatform;

    /**
     * 同步平台状态
     */
    private Integer syncPlatformStatus;

    /**
     * ext
     */
    @TableField(typeHandler = JsonTypeHandler.class)
    private SignatureContractExtDto ext;

    /**
     * 合同是否是等待签章状态
     *
     * @return true是
     */
    public boolean isInitStatus() {
        return getContractStatus() == null || Objects.equals(ContractStatusEnum.DRAFT.getCode(), getContractStatus());
    }

    /**
     * 合同是否完结状态
     *
     * @return
     */
    public boolean isEndStatus() {
        return Objects.equals(ContractStatusEnum.COMPLETE.getCode(), getContractStatus());
    }

    @JsonIgnore
    public Long getTemplateIdLong() {
        return StringUtils.isBlank(getTemplateId()) ? null : NumberUtil.parseLong(getTemplateId());
    }

    @JsonIgnore
    public SignatureContractExtDto extGet() {
        if (ext == null) {
            ext = new SignatureContractExtDto();
        }
        return ext;
    }
}