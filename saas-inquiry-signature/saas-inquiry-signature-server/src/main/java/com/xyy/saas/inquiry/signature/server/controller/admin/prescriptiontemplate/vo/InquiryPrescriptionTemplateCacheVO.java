package com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquiryPrescriptionTemplateCacheVO implements Serializable {

    private Long id;

    private String url;

    private byte[] content;

}