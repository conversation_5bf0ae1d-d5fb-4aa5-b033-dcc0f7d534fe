package com.xyy.saas.inquiry.signature.server.service.prescription.strategy;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception0;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import cn.iocoder.yudao.module.infra.api.file.FileApi;
import com.xyy.saas.inquiry.enums.file.FileTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.prescription.template.PrescriptionTemplateFieldEnum;
import com.xyy.saas.inquiry.enums.signature.ContractStatusEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureAuditDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureInitDto;
import com.xyy.saas.inquiry.signature.api.signature.InquirySignatureImageApi;
import com.xyy.saas.inquiry.signature.api.signature.dto.InquirySignatureImageDto;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractStatusVO;
import com.xyy.saas.inquiry.signature.server.convert.prescription.InquiryPrescriptionSignatureConvert;
import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionSignatureEvent;
import com.xyy.saas.inquiry.signature.server.mq.producer.PrescriptionSignaturePlatformRequireProducer;
import com.xyy.saas.inquiry.signature.server.mq.producer.PrescriptionSignatureProducer;
import com.xyy.saas.inquiry.signature.server.service.pdf.PdfServiceFactory;
import com.xyy.saas.inquiry.signature.server.service.prescriptiontemplate.InquiryPrescriptionTemplateService;
import com.xyy.saas.inquiry.signature.server.service.signature.InquirySignatureContractService;
import com.xyy.saas.inquiry.signature.server.util.FileUtils;
import com.xyy.saas.inquiry.util.FileApiUtil;
import jakarta.annotation.Resource;
import java.util.Objects;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 处方签章Strategy 默认自绘,下级策略集成至此,失败后走自绘流程
 *
 * @Author:chenxiaoyi
 * @Date:2024/11/27 15:47
 */
@Component
@Slf4j
public class SelfSignaturePrescriptionStrategy implements SignaturePrescriptionStrategy {

    @Resource
    protected InquirySignatureContractService inquirySignatureContractService;

    @Resource
    protected PdfServiceFactory pdfServiceFactory;

    @Resource
    protected PrescriptionSignatureProducer prescriptionSignatureProducer;

    @Resource
    protected InquiryPrescriptionTemplateService inquiryPrescriptionTemplateService;

    @Resource
    protected PrescriptionSignaturePlatformRequireProducer prescriptionSignaturePlatformRequireProducer;

    @Resource
    protected ConfigApi configApi;

    @Resource
    protected FileApi fileApi;


    public SignaturePlatformEnum getPlatform() {
        return SignaturePlatformEnum.SELF;
    }

    /**
     * 自绘签发处方笺
     *
     * @param psDto
     */
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<?> issuePrescription(PrescriptionSignatureInitDto psDto) {
        return generateHandlePrescription(InquiryPrescriptionSignatureConvert.INSTANCE.convertInit2Dto(psDto));
    }

    /**
     * 自绘审核处方
     *
     * @param psAuditDto
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<?> auditPrescription(PrescriptionSignatureAuditDto psAuditDto) {
        return generateHandlePrescription(InquiryPrescriptionSignatureConvert.INSTANCE.convertAudit2Dto(psAuditDto));
    }

    /**
     * 创建处理处方笺
     *
     * @param psDto
     * @return
     */
    protected CommonResult<String> generateHandlePrescription(PrescriptionSignatureDto psDto) {

        InquirySignatureContractStatusVO contractStatusVO = InquirySignatureContractStatusVO.builder()
            .pref(psDto.getContractPref())
            .selfDrawn(psDto.getSelfDrawn())
            .participants(psDto.getParticipantItems())
            .contractStatus(ContractStatusEnum.SIGNING.getCode()).build();

        if (Objects.equals(psDto.getInquiryBizType(), InquiryBizTypeEnum.REMOTE_INQUIRY.getCode())) {
            // 1 带方审方自绘
            drawnPrescriptionImgUrl(psDto, contractStatusVO);
        } else {
            // 1 生成处方pdf图片
            generatePrescriptionPdfUrl(psDto, contractStatusVO);
        }

        // 2. 修改合同状态为签署中
        psDto.getParticipantItems().getLast().setSignStatus(ContractStatusEnum.SIGNING.getCode());
        // 3. 修改合同状态为发起签章
        inquirySignatureContractService.updateSignatureContractStatus(contractStatusVO);

        // 4.发送处方签章事件 - (1.回调 2.同步第三方签章平台)
        prescriptionSignatureProducer.sendMessage(PrescriptionSignatureEvent.builder()
            .msg(InquiryPrescriptionSignatureConvert.INSTANCE.convertPsDtoMessage(psDto, contractStatusVO)).build());
        return CommonResult.success(null);
    }

    /**
     * 生成处方签章pdf
     *
     * @param psDto
     * @return
     */
    private String generatePrescriptionPdfUrl(PrescriptionSignatureDto psDto, InquirySignatureContractStatusVO contractStatusVO) {
        // 1. 填充paramMap、获取模板url
        psDto.getParticipantItems().forEach(p -> {
            psDto.getParamMap().put(p.getActorField(), p.getSignImgUrl());
            if (StringUtils.isNotBlank(p.getSignElectronicImgUrl())) {
                psDto.getParamMap().put(PrescriptionTemplateFieldEnum.convertElectronicCode(p.getActorField()), p.getSignElectronicImgUrl());
            }
        });
        // 2. 不是第一个参与方 &&  当前节点人为空或者没有签名,跳过绘制
        boolean skipDrawing = CollUtil.size(psDto.getParticipantItems()) > 1 &&
            (psDto.getParticipantItems().getLast().getUserId() == null || StringUtils.isBlank(psDto.getParticipantItems().getLast().getSignImgUrl()));
        CommonResult<String> generateAndUpload = skipDrawing ? CommonResult.success(null)
            : pdfServiceFactory.getInstance().generateAndUpload(inquiryPrescriptionTemplateService.getPrescriptionTemplate4Cache(psDto.getTemplateId()).getContent(), psDto.getParamMap());
        if (generateAndUpload.isError()) {
            throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), generateAndUpload.getMsg());
        }

        contractStatusVO.setPdfUrl(generateAndUpload.getData());
        return generateAndUpload.getData();
    }


    @Autowired
    private InquirySignatureImageApi inquirySignatureImageApi;

    // 签名图片目标宽度
    private static final int SIGN_WIDTH = 120;
    // 签名图片目标高度
    private static final int SIGN_HEIGHT = 50;

    private void drawnPrescriptionImgUrl(PrescriptionSignatureDto psDto, InquirySignatureContractStatusVO contractStatusVO) {
        // 获取用户签名图片
        // String mergeUrl = inquirySignatureImageApi.getRemoteAuditSignatureUrl(psDto.getParticipantItems().getLast().getUserId(), SignaturePlatformEnum.FDD);

        InquirySignatureImageDto imageDto = InquirySignatureImageDto.builder().sourceUrl(psDto.getPrescriptionImgUrl()).mergeUrl(psDto.getParticipantItems().getLast().getSignImgUrl())
            .x(psDto.getCoordinate().getX()).y(psDto.getCoordinate().getY()).alpha(1.0f).width(null).height(null).scaleRatio(3.5).build();

        String fileUrl = inquirySignatureImageApi.signatureImageMerge(imageDto);

        if (StringUtils.isBlank(fileUrl)) {
            throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), "签名图片生成失败 - 重试");
        }
        contractStatusVO.setImgUrl(fileUrl);

        Optional.ofNullable(FileUtils.downLoadFile(contractStatusVO.getImgUrl(), FileTypeEnum.PDF)).ifPresent(bytes -> contractStatusVO.setPdfUrl(FileApiUtil.createFile(bytes)));

    }

}
