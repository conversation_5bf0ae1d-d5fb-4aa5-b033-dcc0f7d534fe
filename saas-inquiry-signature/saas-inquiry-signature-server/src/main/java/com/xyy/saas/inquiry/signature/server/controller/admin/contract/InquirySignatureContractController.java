package com.xyy.saas.inquiry.signature.server.controller.admin.contract;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractPageReqVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractRespVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractSaveReqVO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquirySignatureContractDO;
import com.xyy.saas.inquiry.signature.server.service.signature.InquirySignatureContractService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;
import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 签章合同")
@RestController
@RequestMapping("/signature/signature-contract")
@Validated
public class InquirySignatureContractController {

    @Resource
    private InquirySignatureContractService signatureContractService;

    @PostMapping("/create")
    @Operation(summary = "创建签章合同")
    @PreAuthorize("@ss.hasPermission('signature:signature-contract:create')")
    public CommonResult<Long> createSignatureContract(@Valid @RequestBody InquirySignatureContractSaveReqVO createReqVO) {
        return success(signatureContractService.createSignatureContract(createReqVO));
    }

    @PutMapping("/update")
    @Operation(summary = "更新签章合同")
    @PreAuthorize("@ss.hasPermission('signature:signature-contract:update')")
    public CommonResult<Boolean> updateSignatureContract(@Valid @RequestBody InquirySignatureContractSaveReqVO updateReqVO) {
        signatureContractService.updateSignatureContract(updateReqVO);
        return success(true);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除签章合同")
    @Parameter(name = "id", description = "编号", required = true)
    @PreAuthorize("@ss.hasPermission('signature:signature-contract:delete')")
    public CommonResult<Boolean> deleteSignatureContract(@RequestParam("id") Long id) {
        signatureContractService.deleteSignatureContract(id);
        return success(true);
    }

    @GetMapping("/get")
    @Operation(summary = "获得签章合同")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('signature:signature-contract:query')")
    public CommonResult<InquirySignatureContractRespVO> getSignatureContract(@RequestParam("id") Long id) {
        InquirySignatureContractDO signatureContract = signatureContractService.getSignatureContract(id);
        return success(BeanUtils.toBean(signatureContract, InquirySignatureContractRespVO.class));
    }

    @GetMapping("/page")
    @Operation(summary = "获得签章合同分页")
    @PreAuthorize("@ss.hasPermission('signature:signature-contract:query')")
    public CommonResult<PageResult<InquirySignatureContractRespVO>> getSignatureContractPage(@Valid InquirySignatureContractPageReqVO pageReqVO) {
        PageResult<InquirySignatureContractDO> pageResult = signatureContractService.getSignatureContractPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InquirySignatureContractRespVO.class));
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出签章合同 Excel")
    @PreAuthorize("@ss.hasPermission('signature:signature-contract:export')")
    @ApiAccessLog(operateType = EXPORT)
    public void exportSignatureContractExcel(@Valid InquirySignatureContractPageReqVO pageReqVO,
        HttpServletResponse response) throws IOException {
        pageReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<InquirySignatureContractDO> list = signatureContractService.getSignatureContractPage(pageReqVO).getList();
        // 导出 Excel
        ExcelUtils.write(response, "签章合同.xls", "数据", InquirySignatureContractRespVO.class,
            BeanUtils.toBean(list, InquirySignatureContractRespVO.class));
    }

}