package com.xyy.saas.inquiry.signature.server.convert.ca;

import cn.iocoder.yudao.module.system.api.user.dto.AdminUserRespDTO;
import com.xyy.saas.inquiry.pojo.forward.ForwardCaInfo;
import com.xyy.saas.inquiry.pojo.signature.PlatformConfigExtDto;
import com.xyy.saas.inquiry.signature.api.ca.dto.InquirySignatureCaAuthRespDto;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.InquirySignatureCaAuthRespVO;
import com.xyy.saas.inquiry.signature.server.controller.app.ca.vo.UserCaAuthCallbackVo;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.ca.InquirySignatureCaAuthDO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.person.InquirySignaturePersonDO;
import com.xyy.saas.inquiry.signature.server.service.fdd.dto.AuthCallbackDto;
import com.xyy.saas.inquiry.signature.server.service.fdd.dto.FddSignTaskCreateDto;
import java.util.List;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.factory.Mappers;

/**
 * @Author:chenxiaoyi
 * @Date:2024/10/12 17:17
 */
@Mapper
public interface InquirySignatureCaAuthConvert {

    InquirySignatureCaAuthConvert INSTANCE = Mappers.getMapper(InquirySignatureCaAuthConvert.class);


    InquirySignatureCaAuthRespVO convert(InquirySignatureCaAuthDO caAuthDO);


    default void convertUser(InquirySignaturePersonDO personDO, AdminUserRespDTO user) {
        personDO.setUserName(user.getNickname());
        personDO.setMobile(user.getMobile());
        personDO.setAccountName(user.getMobile());
        personDO.setUserIdentNo(user.getIdCard());
    }


    default FddSignTaskCreateDto convertCreateSignTaskDto(AdminUserRespDTO user, InquirySignaturePersonDO signaturePerson, PlatformConfigExtDto platformConfig) {
        return FddSignTaskCreateDto.builder().userId(user.getId()).nickname(user.getNickname()).mobile(user.getMobile())
            .openUserId(signaturePerson.getOpenUserId()).platformConfig(platformConfig).freeLogin(true).build();
    }

    UserCaAuthCallbackVo convertVo(AuthCallbackDto msg);

    List<InquirySignatureCaAuthRespVO> convertVos(List<InquirySignatureCaAuthDO> signatureCaAuthDOS);

    @Mapping(target = "certifyStatus", source = "caInfo.fddCertifyStatus")
    @Mapping(target = "signatureStatus", source = "caInfo.fddSignatureStatus")
    @Mapping(target = "authorizeFreeSignStatus", source = "caInfo.fddAuthorizeFreeSignStatus")
    @Mapping(target = "authorizeAgreementStatus", source = "caInfo.fddAgreementStatus")
    @Mapping(target = "authorizeFreeSignDdl", source = "caInfo.fddAuthorizeFreeSignDdl")
    @Mapping(target = "signaturePlatform", expression = "java(com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum.FDD.getCode())")
    @Mapping(target = "userId", source = "userId")
    InquirySignatureCaAuthDO convertSync(ForwardCaInfo caInfo, Long userId);

    InquirySignatureCaAuthRespDto convertDto(InquirySignatureCaAuthRespVO signatureCaAuth);

    List<InquirySignatureCaAuthRespDto> convertDto(List<InquirySignatureCaAuthRespVO> caAuthRespVOS);
}
