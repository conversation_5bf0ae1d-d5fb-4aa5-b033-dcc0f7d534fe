package com.xyy.saas.inquiry.signature.server.dal.mysql.signature;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractPageReqVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractSaveReqVO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquirySignatureContractDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 签章合同 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquirySignatureContractMapper extends BaseMapperX<InquirySignatureContractDO> {

    default PageResult<InquirySignatureContractDO> selectPage(InquirySignatureContractPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InquirySignatureContractDO>()
            .eqIfPresent(InquirySignatureContractDO::getPref, reqVO.getPref())
            .eqIfPresent(InquirySignatureContractDO::getSignaturePlatform, reqVO.getSignaturePlatform())
            .eqIfPresent(InquirySignatureContractDO::getContractType, reqVO.getContractType())
            .eqIfPresent(InquirySignatureContractDO::getBizId, reqVO.getBizId())
            .eqIfPresent(InquirySignatureContractDO::getThirdId, reqVO.getThirdId())
            .eqIfPresent(InquirySignatureContractDO::getContractStatus, reqVO.getContractStatus())
            .likeIfPresent(InquirySignatureContractDO::getInitiatorName, reqVO.getInitiatorName())
            .eqIfPresent(InquirySignatureContractDO::getInitiatorMobile, reqVO.getInitiatorMobile())
            .eqIfPresent(InquirySignatureContractDO::getParticipants, reqVO.getParticipants())
            .eqIfPresent(InquirySignatureContractDO::getParamDetail, reqVO.getParamDetail())
            .eqIfPresent(InquirySignatureContractDO::getImgUrl, reqVO.getImgUrl())
            .eqIfPresent(InquirySignatureContractDO::getPdfUrl, reqVO.getPdfUrl())
            .betweenIfPresent(InquirySignatureContractDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(InquirySignatureContractDO::getId));
    }

    default InquirySignatureContractDO selectOne(InquirySignatureContractSaveReqVO reqVO) {
        return selectOne(new LambdaQueryWrapperX<InquirySignatureContractDO>()
            .eqIfPresent(InquirySignatureContractDO::getPref, reqVO.getPref())
            .eqIfPresent(InquirySignatureContractDO::getSignaturePlatform, reqVO.getSignaturePlatform())
            .eqIfPresent(InquirySignatureContractDO::getContractType, reqVO.getContractType())
            .eqIfPresent(InquirySignatureContractDO::getBizId, reqVO.getBizId())
            .eqIfPresent(InquirySignatureContractDO::getThirdId, reqVO.getThirdId())
            .eqIfPresent(InquirySignatureContractDO::getContractStatus, reqVO.getContractStatus())
            .likeIfPresent(InquirySignatureContractDO::getInitiatorName, reqVO.getInitiatorName())
            .eqIfPresent(InquirySignatureContractDO::getInitiatorMobile, reqVO.getInitiatorMobile())
            .orderByDesc(InquirySignatureContractDO::getId), false);

    }


}