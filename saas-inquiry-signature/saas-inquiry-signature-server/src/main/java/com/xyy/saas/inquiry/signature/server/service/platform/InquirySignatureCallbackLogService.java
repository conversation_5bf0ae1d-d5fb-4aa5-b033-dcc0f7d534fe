package com.xyy.saas.inquiry.signature.server.service.platform;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.signature.server.controller.admin.platform.vo.InquirySignatureCallbackLogPageReqVO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.platform.InquirySignatureCallbackLogDO;

/**
 * 签章回调日志 Service 接口
 *
 * <AUTHOR>
 */
public interface InquirySignatureCallbackLogService {


    void createInquirySignatureCallbackLog(InquirySignatureCallbackLogDO inquirySignatureCallbackLogDO);

    /**
     * 获得签章回调日志
     *
     * @param id 编号
     * @return 签章回调日志
     */
    InquirySignatureCallbackLogDO getInquirySignatureCallbackLog(Long id);

    /**
     * 获得签章回调日志分页
     *
     * @param pageReqVO 分页查询
     * @return 签章回调日志分页
     */
    PageResult<InquirySignatureCallbackLogDO> getInquirySignatureCallbackLogPage(InquirySignatureCallbackLogPageReqVO pageReqVO);

}