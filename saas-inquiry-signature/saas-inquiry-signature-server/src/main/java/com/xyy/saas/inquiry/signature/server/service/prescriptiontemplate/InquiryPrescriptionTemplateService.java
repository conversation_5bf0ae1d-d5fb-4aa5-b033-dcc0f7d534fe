package com.xyy.saas.inquiry.signature.server.service.prescriptiontemplate;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionTemplateField;
import com.xyy.saas.inquiry.pojo.prescription.PrescriptionTemplateFieldDto;
import com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo.InquiryPrescriptionTemplateCacheVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo.InquiryPrescriptionTemplateFieldRespVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo.InquiryPrescriptionTemplateGenerateReqVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo.InquiryPrescriptionTemplatePageReqVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo.InquiryPrescriptionTemplateRespVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo.InquiryPrescriptionTemplateSaveReqVO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.prescriptiontemplate.InquiryPrescriptionTemplateDO;
import com.xyy.saas.inquiry.signature.server.service.prescriptiontemplate.dto.TemplateSignCheckedDto;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * 处方笺模板 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryPrescriptionTemplateService {

    /**
     * 查询所有处方模板
     *
     * @return
     */
    List<InquiryPrescriptionTemplateDO> loadAllTemplate();

    /**
     * 获取处方模板 - 从缓存中
     *
     * @param templateId 处方模板id
     * @return pdf byte数组
     */
    InquiryPrescriptionTemplateCacheVO getPrescriptionTemplate4Cache(Long templateId);


    /**
     * 创建处方笺模板
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInquiryPrescriptionTemplate(@Valid InquiryPrescriptionTemplateSaveReqVO createReqVO);

    /**
     * 更新处方笺模板
     *
     * @param updateReqVO 更新信息
     */
    void updateInquiryPrescriptionTemplate(@Valid InquiryPrescriptionTemplateSaveReqVO updateReqVO);

    /**
     * 复制处方笺模板
     *
     * @param id 编号
     */
    void copyInquiryPrescriptionTemplate(Long id);

    /**
     * 删除处方笺模板
     *
     * @param id 编号
     */
    void deleteInquiryPrescriptionTemplate(Long id);

    /**
     * validate获取处方笺
     *
     * @param id
     * @return
     */
    InquiryPrescriptionTemplateDO validateInquiryPrescriptionTemplateExists(@NotNull Long id);

    /**
     * 获得处方笺模板
     *
     * @param id 编号
     * @return 处方笺模板
     */
    InquiryPrescriptionTemplateRespVO getInquiryPrescriptionTemplate(Long id);

    /**
     * 获得处方笺模板
     *
     * @param ids 编号
     * @return 处方笺模板
     */
    List<InquiryPrescriptionTemplateRespVO> listInquiryPrescriptionTemplate(List<Long> ids);

    /**
     * 获得处方笺模板分页
     *
     * @param pageReqVO 分页查询
     * @return 处方笺模板分页
     */
    PageResult<InquiryPrescriptionTemplateRespVO> getInquiryPrescriptionTemplatePage(
        InquiryPrescriptionTemplatePageReqVO pageReqVO);

    /**
     * 生成处方笺模板PDF
     *
     * @param reqVO 生成数据
     * @return 处方笺模板
     */
    InquiryPrescriptionTemplateRespVO generateInquiryPrescriptionTemplate(
        InquiryPrescriptionTemplateGenerateReqVO reqVO);

    /**
     * 预览处方笺PDF（模板填充数据）
     *
     * @param id           处方笺模板id
     * @param parameterMap 请求参数传递数据
     * @return
     */
    String previewInquiryPrescriptionTemplate(Long id, Map<String, String[]> parameterMap);

    /**
     * 获取处方笺模板下一级节点字段
     *
     * @param id    处方笺模板id
     * @param field 某一节点id
     * @return
     */
    PrescriptionTemplateField getSignNextTemplateField(Long id, String field);

    /**
     * 获取处方笺模板下一级节点字段 并校验用户签名url
     *
     * @param id     处方笺模板id
     * @param field  某一节点id
     * @param userId 用户id 可为空,为空不处理
     * @return
     */
    PrescriptionTemplateFieldDto getSignNextTemplateFieldValidUrl(TemplateSignCheckedDto checkedDto);

    /**
     * 获取处方笺模板默认值字段
     *
     * @param templateId 模板id
     * @return k v
     */
    Map<String, String> getPrescriptionTemplateDefaultValueFields(Long templateId);


    /**
     * 获取处方模板字段
     *
     * @return 字段列表
     */
    List<InquiryPrescriptionTemplateFieldRespVO> getPrescriptionTemplateFields();

    /**
     * 获取模板需要同步三方平台的字段
     *
     * @param id 模板id
     * @return 字段
     */
    List<PrescriptionTemplateField> getTemplateAccessPlatformFields(Long id);

    /**
     * 获取模板需要签章图片的字段
     *
     * @param id
     * @return
     */
    List<PrescriptionTemplateField> getTemplateSignPictureFields(Long id);
}