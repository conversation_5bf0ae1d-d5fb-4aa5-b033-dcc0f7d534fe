package com.xyy.saas.inquiry.signature.server.service.platform;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.pojo.signature.PlatformConfigExtDto;
import com.xyy.saas.inquiry.signature.server.controller.admin.platform.vo.InquirySignaturePlatformPageReqVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.platform.vo.InquirySignaturePlatformSaveReqVO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.platform.InquirySignaturePlatformDO;
import jakarta.validation.Valid;
import java.util.List;

/**
 * 签章平台配置 Service 接口
 *
 * <AUTHOR>
 */
public interface InquirySignaturePlatformService {

    /**
     * 创建签章平台配置
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInquirySignaturePlatform(@Valid InquirySignaturePlatformSaveReqVO createReqVO);

    /**
     * 更新签章平台配置
     *
     * @param updateReqVO 更新信息
     */
    void updateInquirySignaturePlatform(@Valid InquirySignaturePlatformSaveReqVO updateReqVO);

    /**
     * 删除签章平台配置
     *
     * @param id 编号
     */
    void deleteInquirySignaturePlatform(Long id);

    /**
     * 获得签章平台配置
     *
     * @param id 编号
     * @return 签章平台配置
     */
    InquirySignaturePlatformDO getInquirySignaturePlatform(Long id);

    /**
     * 获得签章平台配置分页
     *
     * @param pageReqVO 分页查询
     * @return 签章平台配置分页
     */
    PageResult<InquirySignaturePlatformDO> getInquirySignaturePlatformPage(InquirySignaturePlatformPageReqVO pageReqVO);


    /**
     * 获取某签章平台应用列表
     *
     * @param platform
     * @return
     */
    List<PlatformConfigExtDto> getSignaturePlatform(Integer platform);

    /**
     * 获取某签章平台某应用
     *
     * @param platform
     * @param configId 配置id
     * @return
     */
    PlatformConfigExtDto getSignaturePlatformConfig(Integer platform, Integer configId);

    /**
     * 获取某签章平台某应用
     *
     * @param platform
     * @param appId
     * @return
     */
    PlatformConfigExtDto getSignaturePlatformConfig(Integer platform, String appId);

    /**
     * 获取主签章平台
     *
     * @return 没有默认自营
     */
    Integer getMaster();

    /**
     * 获取默认的签章平台
     *
     * @return 签章平台
     */
    Integer getDefaultSignaturePlatform();

    /**
     * 修改平台为主平台
     *
     * @param platform 平台
     */
    void updatePlatformMaster(Integer platform);
}