package com.xyy.saas.inquiry.signature.server.mq.consumer.signature;

import cn.hutool.core.util.NumberUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import com.fasc.open.api.enums.signtask.SignStatusEnum;
import com.fasc.open.api.v5_1.res.signtask.ListSignTaskActorRes;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.enums.signature.ContractStatusEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.signature.server.constant.SignatureConstant;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquirySignatureContractDO;
import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionSignatureEvent;
import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionSignatureMessage;
import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionSignaturePlatformRequireEvent;
import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionSignaturePlatformRequireMessage;
import com.xyy.saas.inquiry.signature.server.mq.producer.PrescriptionSignaturePlatformRequireProducer;
import com.xyy.saas.inquiry.signature.server.mq.producer.PrescriptionSignatureProducer;
import com.xyy.saas.inquiry.signature.server.service.fdd.FddSignTaskBussService;
import com.xyy.saas.inquiry.signature.server.service.prescription.InquirySignaturePrescriptionService;
import com.xyy.saas.inquiry.signature.server.service.signature.InquirySignatureContractService;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @Desc 处方签章平台确认MQ-Consumer
 * <AUTHOR>
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_signature_server_mq_consumer_PrescriptionSignaturePlatformRequireMQConsumer",
    topic = PrescriptionSignaturePlatformRequireEvent.TOPIC)
public class PrescriptionSignaturePlatformRequireMQConsumer {

    @Resource
    private FddSignTaskBussService fddSignTaskBussService;

    @Resource
    private InquirySignatureContractService inquirySignatureContractService;

    @Resource
    private InquirySignaturePrescriptionService inquirySignaturePrescriptionService;

    @Resource
    private PrescriptionSignatureProducer prescriptionSignatureProducer;

    @Resource
    private PrescriptionSignaturePlatformRequireProducer prescriptionSignaturePlatformRequireProducer;

    @Resource
    protected ConfigApi configApi;

    /**
     * 当同步三方 多久后没有回调成功，则自己通知下游mq
     *
     * @return 默认 5+1次 * 10 = 60s ，最小0+1次 10s
     */
    private Integer requireMaxCount() {
        String requireMaxCount = configApi.getConfigValueByKey(SignatureConstant.PRESCRIPTION_SIGNATURE_REQUIRE_COUNT);
        return Math.max(NumberUtil.parseInt(requireMaxCount, 5), 0);
    }


    @EventBusListener
    public void prescriptionSignatureMQConsumer(PrescriptionSignaturePlatformRequireEvent platformRequireEvent) {
        PrescriptionSignaturePlatformRequireMessage msg = platformRequireEvent.getMsg();
        if (msg.getConsumerCount() >= requireMaxCount()) {
            log.info("【签章RequireMQ】达到消费次数,处方处理回调超时,合同号:{},actorField:{},consumerCount:{}", msg.getContractPref(), msg.getParticipantItem().getActorField(), msg.getConsumerCount());
            manualSendPrescriptionSignature(msg); // 达到最大次数我们还没收到回调，则手动发送回调
            return;
        }
        InquirySignatureContractDO signatureContractDO = inquirySignatureContractService.getSignatureContractByPref(msg.getContractPref());
        // 签章完成不处理
        if (signatureContractDO.isEndStatus() || signatureContractDO.getParticipants().stream()
            .filter(p -> StringUtils.equals(p.getActorField(), msg.getParticipantItem().getActorField())).anyMatch(p ->
                Objects.equals(p.getSignStatus(), ContractStatusEnum.COMPLETE.getCode()))) {
            log.info("【签章RequireMQ】签章已完成不处理,合同号:{},actorField:{},consumerCount:{}", msg.getContractPref(), msg.getParticipantItem().getActorField(), msg.getConsumerCount());
            return;
        }

        boolean require = false;

        if (Objects.equals(signatureContractDO.getSignaturePlatform(), SignaturePlatformEnum.FDD.getCode())) {
            require = fddRequire(signatureContractDO, msg);
        }

        // 再次发送Require延迟确认消息
        if (!require) {
            msg.setConsumerCount(msg.getConsumerCount() + 1);
            prescriptionSignaturePlatformRequireProducer.sendMessage(PrescriptionSignaturePlatformRequireEvent.builder().msg(msg).build(), LocalDateTime.now().plusSeconds(10));
        }
    }

    private boolean fddRequire(InquirySignatureContractDO signatureContractDO, PrescriptionSignaturePlatformRequireMessage msg) {
        CommonResult<List<ListSignTaskActorRes>> listSignTaskActorRes = fddSignTaskBussService.listSignTaskActor(signatureContractDO.extGet().getPlatformConfigId(), signatureContractDO.getThirdId());
        if (listSignTaskActorRes.isSuccess()) {
            ListSignTaskActorRes signTaskActorRes = listSignTaskActorRes.getData().stream().filter(a -> StringUtils.equals(a.getActorId(), msg.getParticipantItem().getActorField())).findAny().orElse(null);
            if (signTaskActorRes != null && StringUtils.equals(signTaskActorRes.getSignStatus(), SignStatusEnum.SIGNED.getCode())) {
                manualSendPrescriptionSignature(msg); // 如果法大大已经签署成功,我们还没收到回调，则手动发送回调
                log.info("【签章RequireMQ】法大大已经签署成功,手动发送回调MQ,合同号:{},actorField:{}", msg.getContractPref(), msg.getParticipantItem().getActorField());
                return true;
            }
        }
        return false;
    }

    private void manualSendPrescriptionSignature(PrescriptionSignaturePlatformRequireMessage msg) {
        PrescriptionSignatureMessage signatureMessage = PrescriptionSignatureMessage.builder()
            .contractPref(msg.getContractPref())
            .participantItem(msg.getParticipantItem())
            .pdfUrl(inquirySignaturePrescriptionService.drawnPrescriptionSelf(msg.getContractPref(), true))
            .callBackTime(LocalDateTime.now())
            .build();
        prescriptionSignatureProducer.sendMessage(PrescriptionSignatureEvent.builder().msg(signatureMessage).build());
    }

}
