package com.xyy.saas.inquiry.signature.server.service.signature.sync;

import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquirySignatureContractDO;

/**
 * @Author:ch<PERSON><PERSON><PERSON>i
 * @Date:2025/02/20 11:07
 */
public interface SyncSignaturePlatformStrategy {


    SignaturePlatformEnum getPlatform();

    /**
     * 同步签章平台
     *
     * @param signatureContract
     */
    void syncSignaturePlatform(InquirySignatureContractDO signatureContract);


}
