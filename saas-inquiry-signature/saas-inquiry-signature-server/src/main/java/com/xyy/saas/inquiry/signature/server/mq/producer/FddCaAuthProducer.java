package com.xyy.saas.inquiry.signature.server.mq.producer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.signature.server.mq.message.FddCaAuthEvent;
import org.springframework.stereotype.Component;

/**
 * @Desc 法大大CA认证mq
 * <AUTHOR>
 */
@Component
@EventBusProducer(
    topic = FddCaAuthEvent.TOPIC
)
public class FddCaAuthProducer extends EventBusRocketMQTemplate {


}
