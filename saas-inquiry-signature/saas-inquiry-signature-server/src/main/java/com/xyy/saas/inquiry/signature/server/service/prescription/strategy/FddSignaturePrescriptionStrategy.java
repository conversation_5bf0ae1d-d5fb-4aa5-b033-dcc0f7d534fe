package com.xyy.saas.inquiry.signature.server.service.prescription.strategy;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception0;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.exception.enums.GlobalErrorCodeConstants;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.fasc.open.api.v5_1.res.signtask.CreateSignTaskRes;
import com.fasc.open.api.v5_1.res.signtask.SignTaskDetailRes;
import com.xyy.saas.inquiry.enums.prescription.template.PrescriptionTemplateFieldEnum;
import com.xyy.saas.inquiry.enums.signature.ContractStatusEnum;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureAuditDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureInitDto;
import com.xyy.saas.inquiry.signature.server.constant.SignatureConstant;
import com.xyy.saas.inquiry.signature.server.controller.admin.contract.vo.InquirySignatureContractStatusVO;
import com.xyy.saas.inquiry.signature.server.convert.fdd.InquiryFddConvert;
import com.xyy.saas.inquiry.signature.server.convert.signature.InquirySignatureContractConvert;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.person.InquirySignaturePersonDO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquirySignatureContractDO;
import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionSignaturePlatformRequireEvent;
import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionSignaturePlatformRequireMessage;
import com.xyy.saas.inquiry.signature.server.service.fdd.FddSignTaskBussService;
import com.xyy.saas.inquiry.signature.server.service.fdd.dto.FddSignTaskCreateDto;
import com.xyy.saas.inquiry.signature.server.service.person.InquirySignaturePersonService;
import jakarta.annotation.Resource;
import java.time.LocalDateTime;
import java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * fdd处方签章Strategy
 *
 * @Author:chenxiaoyi
 * @Date:2024/11/27 15:47
 */
@Component
@Slf4j
public class FddSignaturePrescriptionStrategy extends SelfSignaturePrescriptionStrategy {

    @Resource
    private FddSignTaskBussService fddSignTaskBussService;


    @Resource
    private InquirySignaturePersonService inquirySignaturePersonService;

    @Override
    public SignaturePlatformEnum getPlatform() {
        return SignaturePlatformEnum.FDD;
    }

    /**
     * 是否可以降级自绘 - 当三方签章平台异常时
     *
     * @return 为空或者 = 1 都可自绘
     */
    private boolean canSelfDrawn() {
        final String selfDrawn = configApi.getConfigValueByKey(SignatureConstant.CAN_SELF_DRAWN_PRESCRIPTION);
        return StringUtils.isBlank(selfDrawn) || StringUtils.equals(selfDrawn, "1");
    }


    /**
     * 法大大开具处方
     *
     * @param psDto
     */
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<?> issuePrescription(PrescriptionSignatureInitDto psDto) {
        // 0.判断当前签章是否走三方平台
        if (!CommonStatusEnum.isEnable(psDto.getParticipantItem().getAccessPlatform())) {
            return super.issuePrescription(psDto);
        }

        // InquiryPrescriptionTemplateDO templateDO = inquiryPrescriptionTemplateService.validateInquiryPrescriptionTemplateExists(psDto.getTemplateId());
        // Map<String, PrescriptionTemplateField> templateFieldMap = templateDO.getTemplateFields().stream().collect(Collectors.toMap(PrescriptionTemplateField::getField, Function.identity(), (a, b) -> b));
        // if (!CommonStatusEnum.isEnable(templateFieldMap.get(psDto.getParticipantItem().getActorField()).getAccessPlatform())) {
        //     return super.issuePrescription(psDto);
        // }
        // 1. 获取发起方法大大信息
        InquirySignaturePersonDO fddPersonDO = inquirySignaturePersonService.queryPersonByUserId(psDto.getParticipantItem().getUserId(), SignaturePlatformEnum.FDD, psDto.getSignaturePlatformConfigId());
        if (fddPersonDO == null) {
            return super.issuePrescription(psDto);
        }

        // 1.1 填充paramMap
        if (StringUtils.isNotBlank(psDto.getParticipantItem().getSignElectronicImgUrl())) {
            psDto.getParamMap().put(PrescriptionTemplateFieldEnum.convertElectronicCode(psDto.getParticipantItem().getActorField()), psDto.getParticipantItem().getSignElectronicImgUrl());
        }

        // 2.绘制处方笺
        CommonResult<String> generateAndUploadRes = pdfServiceFactory.getInstance().generateAndUpload(inquiryPrescriptionTemplateService.getPrescriptionTemplate4Cache(psDto.getTemplateId()).getContent(), psDto.getParamMap());
        if (generateAndUploadRes.isError()) {
            throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), generateAndUploadRes.getMsg());
        }
        // 3.基于文档创建处方
        FddSignTaskCreateDto fddSignTaskCreateDto = InquiryFddConvert.INSTANCE.convertByIssuePs(psDto, fddPersonDO, generateAndUploadRes.getData());
        CommonResult<CreateSignTaskRes> signTaskRes = fddSignTaskBussService.createWithFile(fddSignTaskCreateDto);
        if (signTaskRes.isError()) {
            log.error("法大大-创建处方createWithFile失败,prescriptionPref:{},participantItem:{},signTaskResMsg:{}", psDto.getPrescriptionPref(), psDto.getParticipantItem(), signTaskRes.getMsg());
            if (canSelfDrawn()) {
                psDto.setSelfDrawn(CommonStatusEnum.ENABLE.getStatus());
                return super.issuePrescription(psDto);
            }
            throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), signTaskRes.getMsg());
        }
        //  设置当前参与方为签署中
        psDto.getParticipantItem().setSignStatus(ContractStatusEnum.SIGNING.getCode());
        // 4. 修改合同状态为发起签章
        inquirySignatureContractService.updateSignatureContractStatus(InquirySignatureContractStatusVO.builder()
            .pref(psDto.getContractPref())
            .thirdId(signTaskRes.getData().getSignTaskId())
            .pdfUrl(generateAndUploadRes.getData())
            .signaturePlatform(SignaturePlatformEnum.FDD.getCode())
            .participants(Collections.singletonList(psDto.getParticipantItem()))
            .contractStatus(ContractStatusEnum.SIGNING.getCode()).build());

        // 5.发送 签章确认mq
        PrescriptionSignaturePlatformRequireMessage platformRequireMessage = InquirySignatureContractConvert.INSTANCE.convertRequireMessage(psDto);
        prescriptionSignaturePlatformRequireProducer.sendMessage(PrescriptionSignaturePlatformRequireEvent.builder().msg(platformRequireMessage).build(), LocalDateTime.now().plusSeconds(10));
        return CommonResult.success(null);
    }

    /**
     * 法大大审核处方
     *
     * @param psAuditDto
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<?> auditPrescription(PrescriptionSignatureAuditDto psAuditDto) {
        // 0.判断合同自绘 或者当前签章是否走三方平台
        if (!CommonStatusEnum.isEnable(psAuditDto.getParticipantItem().getAccessPlatform())) {
            return super.auditPrescription(psAuditDto);
        }
        
        InquirySignatureContractDO signatureContract = inquirySignatureContractService.validateSignatureContractPrefExists(psAuditDto.getContractPref());
        if (CommonStatusEnum.isEnable(signatureContract.getSelfDrawn()) || StringUtils.isBlank(signatureContract.getThirdId())) { // 如果是自绘处方 或无三方签章信息
            return super.auditPrescription(psAuditDto);
        }
        // InquiryPrescriptionTemplateDO templateDO = inquiryPrescriptionTemplateService.validateInquiryPrescriptionTemplateExists(signatureContract.getTemplateIdLong());
        // Map<String, PrescriptionTemplateField> templateFieldMap = templateDO.getTemplateFields().stream().collect(Collectors.toMap(PrescriptionTemplateField::getField, Function.identity(), (a, b) -> b));
        // if (!CommonStatusEnum.isEnable(templateFieldMap.get(psAuditDto.getParticipantItems().getLast().getActorField()).getAccessPlatform())) {
        //     return super.auditPrescription(psAuditDto);
        // }
        // 1. 获取发起方法大大信息
        InquirySignaturePersonDO fddPersonDO = inquirySignaturePersonService.queryPersonByUserId(psAuditDto.getParticipantItem().getUserId(), SignaturePlatformEnum.FDD, signatureContract.extGet().getPlatformConfigId());
        if (fddPersonDO == null) {
            return super.auditPrescription(psAuditDto);
        }

        // 2.判断参与方是否已签署
        CommonResult<SignTaskDetailRes> signTaskDetail = fddSignTaskBussService.getSignTaskDetail(signatureContract.extGet().getPlatformConfigId(), signatureContract.getThirdId());
        if (signTaskDetail.isSuccess() && CollUtil.isNotEmpty(signTaskDetail.getData().getDocs())) {
            boolean matchSigned = signTaskDetail.getData().getActors().stream().anyMatch(a -> a.getActorInfo() != null
                && (StringUtils.equalsIgnoreCase(a.getActorInfo().getActorId(), psAuditDto.getParticipantItem().getActorField())));
            if (matchSigned) {
                return CommonResult.success(null); // 已签署等待回调
            }
        }
        // 3.添加控件、追加参与方
        FddSignTaskCreateDto taskCreateDto = InquiryFddConvert.INSTANCE.convertByAuditPs(signatureContract, psAuditDto.getParticipantItems().getLast(), fddPersonDO, signTaskDetail.getData());
        CommonResult<?> addFieldRes = fddSignTaskBussService.addField(taskCreateDto);
        CommonResult<?> addActorRes = fddSignTaskBussService.addActor(taskCreateDto);
        if (addActorRes.isError()) {
            log.error("法大大-审核处方走降级自绘,追加控件参与方失败,prescriptionPref:{},participantItem:{},addFieldResMsg:{},addActorResMsg:{}", psAuditDto.getPrescriptionPref(), psAuditDto.getParticipantItem(), addFieldRes.getMsg(),
                addActorRes.getMsg());
            if (canSelfDrawn()) {
                psAuditDto.setSelfDrawn(CommonStatusEnum.ENABLE.getStatus());
                return super.auditPrescription(psAuditDto);
            }
            throw exception0(GlobalErrorCodeConstants.FAIL.getCode(), addActorRes.getMsg());
        }

        // 4. 修改合同状态为发起签章
        psAuditDto.getParticipantItems().getLast().setSignStatus(ContractStatusEnum.SIGNING.getCode());
        inquirySignatureContractService.updateSignatureContractStatus(InquirySignatureContractStatusVO.builder()
            .pref(psAuditDto.getContractPref())
            .participants(psAuditDto.getParticipantItems())
            .contractStatus(ContractStatusEnum.SIGNING.getCode()).build());

        // 5.发送延迟 签章确认mq
        PrescriptionSignaturePlatformRequireMessage platformRequireMessage = InquirySignatureContractConvert.INSTANCE.convertRequireMessage(psAuditDto);
        prescriptionSignaturePlatformRequireProducer.sendMessage(PrescriptionSignaturePlatformRequireEvent.builder().msg(platformRequireMessage).build(), LocalDateTime.now().plusSeconds(10));
        return addActorRes;
    }
}
