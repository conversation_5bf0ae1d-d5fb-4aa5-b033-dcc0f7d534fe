package com.xyy.saas.inquiry.signature.server.mq.message;

import java.io.Serializable;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureAuditDto;
import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureInitDto;
import lombok.Builder;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 处方执行签章消息Dto
 *
 * <AUTHOR>
 */
@Builder
@Data
@Accessors(chain = true)
public class PrescriptionExecutionSignatureMessage implements Serializable {

    // 处方开具Dto
    private PrescriptionSignatureInitDto signatureInitDto;

    // 处方审核Dto
    private PrescriptionSignatureAuditDto psAuditDto;
}
