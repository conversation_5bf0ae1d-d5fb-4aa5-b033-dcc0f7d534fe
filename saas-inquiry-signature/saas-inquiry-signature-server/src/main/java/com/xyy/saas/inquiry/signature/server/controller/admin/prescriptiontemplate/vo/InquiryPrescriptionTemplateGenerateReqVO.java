package com.xyy.saas.inquiry.signature.server.controller.admin.prescriptiontemplate.vo;

import com.xyy.saas.inquiry.signature.dto.pdf.RectangleInfo;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

@Schema(description = "管理后台 - 生成处方笺模板PDF Request VO")
@Data
public class InquiryPrescriptionTemplateGenerateReqVO {

    @Schema(description = "主键", requiredMode = Schema.RequiredMode.REQUIRED, example = "12132")
    @NotNull(message = "模板id不能为空")
    private Long id;

    @Schema(description = "字段坐标信息", requiredMode = Schema.RequiredMode.REQUIRED, example = "25982")
    private List<RectangleInfo> rectangles;

}