package com.xyy.saas.inquiry.signature.server.dal.mysql.signature;

import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.signature.server.controller.app.signature.vo.InquiryUserSignatureInformationVO;
import com.xyy.saas.inquiry.signature.server.controller.app.signature.vo.InquiryUserSignatureManageVO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.signature.InquiryUserSignatureInformationDO;
import java.util.List;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 问诊用户(医生/药师/核对/调配)签章信息 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquiryUserSignatureInformationMapper extends BaseMapperX<InquiryUserSignatureInformationDO> {


    default InquiryUserSignatureInformationDO queryOneByCondition(InquiryUserSignatureInformationVO signatureInformationVO) {
        return selectOne(new LambdaQueryWrapperX<InquiryUserSignatureInformationDO>()
            .eq(InquiryUserSignatureInformationDO::getUserId, signatureInformationVO.getUserId())
            .inIfPresent(InquiryUserSignatureInformationDO::getUserId, signatureInformationVO.getUserIds())
            .eqIfPresent(InquiryUserSignatureInformationDO::getTenantId, signatureInformationVO.getTenantId())
            .eqIfPresent(InquiryUserSignatureInformationDO::getSignaturePlatform, signatureInformationVO.getSignaturePlatform())
            .eqIfPresent(InquiryUserSignatureInformationDO::getSignatureBizType, signatureInformationVO.getSignatureBizType())
            .eqIfPresent(InquiryUserSignatureInformationDO::getSignatureTaskId, signatureInformationVO.getSignatureTaskId())
            .eqIfPresent(InquiryUserSignatureInformationDO::getSignatureStatus, signatureInformationVO.getSignatureStatus()), false
        );
    }


    default List<InquiryUserSignatureInformationDO> queryByCondition(InquiryUserSignatureInformationVO signatureInformationVO) {
        return selectList(new LambdaQueryWrapperX<InquiryUserSignatureInformationDO>()
            .eq(InquiryUserSignatureInformationDO::getUserId, signatureInformationVO.getUserId())
            .inIfPresent(InquiryUserSignatureInformationDO::getUserId, signatureInformationVO.getUserIds())
            .eqIfPresent(InquiryUserSignatureInformationDO::getTenantId, signatureInformationVO.getTenantId())
            .eqIfPresent(InquiryUserSignatureInformationDO::getSignaturePlatform, signatureInformationVO.getSignaturePlatform())
            .eqIfPresent(InquiryUserSignatureInformationDO::getSignatureBizType, signatureInformationVO.getSignatureBizType())
            .eqIfPresent(InquiryUserSignatureInformationDO::getSignatureTaskId, signatureInformationVO.getSignatureTaskId())
            .eqIfPresent(InquiryUserSignatureInformationDO::getSignatureStatus, signatureInformationVO.getSignatureStatus()));
    }

//    default PageResult<InquiryUserSignatureInformationDO> selectPage(InquiryUserSignatureInformationDO reqVO) {
//        return selectPage(reqVO, new LambdaQueryWrapperX<InquiryUserSignatureInformationDO>()
//                .eq(InquiryUserSignatureInformationDO::getTenantId, Optional.ofNullable(reqVO.getTenantId()).orElse(TenantContextHolder.getRequiredTenantId()))
//                .eqIfPresent(InquiryUserSignatureInformationDO::getSignaturePlatform, reqVO.getSignaturePlatform())
//                .eqIfPresent(InquiryUserSignatureInformationDO::getSignatureBizType, reqVO.getSignatureBizType())
//                .eqIfPresent(InquiryUserSignatureInformationDO::getUserId, reqVO.getUserId())
//                .eqIfPresent(InquiryUserSignatureInformationDO::getSignatureTaskId, reqVO.getSignatureTaskId())
//                .eqIfPresent(InquiryUserSignatureInformationDO::getSignatureStatus, reqVO.getSignatureStatus())
//                .betweenIfPresent(InquiryUserSignatureInformationDO::getCreateTime, reqVO.getCreateTime())
//                .orderByDesc(InquiryUserSignatureInformationDO::getId));
//    }


    IPage<InquiryUserSignatureManageVO> pageInquiryUserSignature(Page<InquiryUserSignatureManageVO> objectPage, InquiryUserSignatureInformationDO informationDO);


    default void deleteBySignTaskId(String signTaskId, SignaturePlatformEnum signaturePlatformEnum) {
        delete(new LambdaQueryWrapperX<InquiryUserSignatureInformationDO>()
            .eq(InquiryUserSignatureInformationDO::getSignaturePlatform, signaturePlatformEnum.getCode())
            .eq(InquiryUserSignatureInformationDO::getSignatureTaskId, signTaskId));
    }

    void deleteByUserIdBizType(@Param("userId") Long userId, @Param("signaturePlatform") Integer signaturePlatform, @Param("signatureBizType") Integer signatureBizType);
}