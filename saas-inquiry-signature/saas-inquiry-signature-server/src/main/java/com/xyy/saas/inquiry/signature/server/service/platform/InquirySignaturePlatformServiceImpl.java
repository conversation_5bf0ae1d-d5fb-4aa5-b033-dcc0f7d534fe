package com.xyy.saas.inquiry.signature.server.service.platform;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.signature.enums.ErrorCodeConstants.INQUIRY_SIGNATURE_PLATFORM_NOT_EXISTS;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.pojo.signature.PlatformConfigExtDto;
import com.xyy.saas.inquiry.signature.server.controller.admin.platform.vo.InquirySignaturePlatformPageReqVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.platform.vo.InquirySignaturePlatformSaveReqVO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.platform.InquirySignaturePlatformDO;
import com.xyy.saas.inquiry.signature.server.dal.mysql.platform.InquirySignaturePlatformMapper;
import com.xyy.saas.inquiry.signature.server.dal.redis.RedisKeyConstants;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Objects;
import org.apache.commons.lang3.StringUtils;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.validation.annotation.Validated;

/**
 * 签章平台配置 Service 实现类
 *
 * <AUTHOR>
 */
@Service
@Validated
public class InquirySignaturePlatformServiceImpl implements InquirySignaturePlatformService {

    @Resource
    private InquirySignaturePlatformMapper inquirySignaturePlatformMapper;

    /**
     * 获得自身的代理对象，解决 AOP 生效问题
     *
     * @return 自己
     */
    private InquirySignaturePlatformServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }

    @Override
    @CacheEvict(value = RedisKeyConstants.SIGNATURE_PLATFORM_CONFIG, key = "#createReqVO.signaturePlatform",
        condition = "#createReqVO.signaturePlatform != null")
    public Long createInquirySignaturePlatform(InquirySignaturePlatformSaveReqVO createReqVO) {
        // 插入
        InquirySignaturePlatformDO inquirySignaturePlatform = BeanUtils.toBean(createReqVO, InquirySignaturePlatformDO.class);
        inquirySignaturePlatformMapper.insert(inquirySignaturePlatform.setMaster(Objects.equals(createReqVO.getSignaturePlatform(), getSelf().getMaster())));
        // 返回
        return inquirySignaturePlatform.getId();
    }

    @Override
    @CacheEvict(value = RedisKeyConstants.SIGNATURE_PLATFORM_CONFIG, key = "#updateReqVO.signaturePlatform",
        condition = "#updateReqVO.signaturePlatform != null")
    public void updateInquirySignaturePlatform(InquirySignaturePlatformSaveReqVO updateReqVO) {
        // 校验存在
        validateInquirySignaturePlatformExists(updateReqVO.getId());
        // 更新
        InquirySignaturePlatformDO updateObj = BeanUtils.toBean(updateReqVO, InquirySignaturePlatformDO.class);
        inquirySignaturePlatformMapper.updateById(updateObj);
    }

    @Override
    @CacheEvict(value = RedisKeyConstants.SIGNATURE_PLATFORM_CONFIG, allEntries = true) // 删除一个清空所有
    public void deleteInquirySignaturePlatform(Long id) {
        // 校验存在
        validateInquirySignaturePlatformExists(id);
        // 删除
        inquirySignaturePlatformMapper.deleteById(id);
    }


    private void validateInquirySignaturePlatformExists(Long id) {
        if (inquirySignaturePlatformMapper.selectById(id) == null) {
            throw exception(INQUIRY_SIGNATURE_PLATFORM_NOT_EXISTS);
        }
    }

    @Override
    public InquirySignaturePlatformDO getInquirySignaturePlatform(Long id) {
        return inquirySignaturePlatformMapper.selectById(id);
    }

    @Override
    public PageResult<InquirySignaturePlatformDO> getInquirySignaturePlatformPage(InquirySignaturePlatformPageReqVO pageReqVO) {
        return inquirySignaturePlatformMapper.selectPage(pageReqVO);
    }


    @Override
    @Cacheable(value = RedisKeyConstants.SIGNATURE_PLATFORM_CONFIG, key = "#platform", unless = "#result == null")
    public List<PlatformConfigExtDto> getSignaturePlatform(Integer platform) {
        List<InquirySignaturePlatformDO> platformDOS = inquirySignaturePlatformMapper.selectList(InquirySignaturePlatformDO::getSignaturePlatform, platform);
        if (CollUtil.isEmpty(platformDOS)) {
            return null;
        }
        return platformDOS.getFirst().getExt();
    }


    @Override
    public PlatformConfigExtDto getSignaturePlatformConfig(Integer platform, Integer configId) {
        List<PlatformConfigExtDto> configExtDtoList = getSelf().getSignaturePlatform(platform);
        if (CollUtil.isNotEmpty(configExtDtoList)) {
            // 兼容 appid为空获取第一个默认应用
            if (configId == null) {
                return configExtDtoList.getFirst();
            }
            return configExtDtoList.stream().filter(c -> Objects.equals(configId, c.getConfigId())).findFirst().orElse(null);
        }
        return null;
    }

    @Override
    public PlatformConfigExtDto getSignaturePlatformConfig(Integer platform, String appId) {
        List<PlatformConfigExtDto> configExtDtoList = getSelf().getSignaturePlatform(platform);
        if (CollUtil.isNotEmpty(configExtDtoList)) {
            // 兼容 appid为空获取第一个默认应用
            if (StringUtils.isBlank(appId)) {
                return configExtDtoList.getFirst();
            }
            return configExtDtoList.stream().filter(c -> StringUtils.equals(appId, c.getAppId())).findFirst().orElse(null);
        }
        return null;
    }

    @Override
    @Cacheable(value = RedisKeyConstants.SIGNATURE_PLATFORM_CONFIG, key = "'master'")
    public Integer getMaster() {
        InquirySignaturePlatformDO signaturePlatformDO = inquirySignaturePlatformMapper.selectOne(new LambdaQueryWrapperX<InquirySignaturePlatformDO>().eq(InquirySignaturePlatformDO::getMaster, true).last("limit 1"));
        return SignaturePlatformEnum.fromCode(signaturePlatformDO == null ? null : signaturePlatformDO.getSignaturePlatform()).getCode();
    }


    @Override
    public Integer getDefaultSignaturePlatform() {
        return SignaturePlatformEnum.FDD.getCode();
    }

    @Override
    @CacheEvict(value = RedisKeyConstants.SIGNATURE_PLATFORM_CONFIG, key = "'master'")
    public void updatePlatformMaster(Integer platform) {
        boolean exists = inquirySignaturePlatformMapper.exists(new LambdaQueryWrapperX<InquirySignaturePlatformDO>().eq(InquirySignaturePlatformDO::getSignaturePlatform, platform));
        if (!exists) {
            throw exception(INQUIRY_SIGNATURE_PLATFORM_NOT_EXISTS);
        }
        // 先更新所有未非主
        inquirySignaturePlatformMapper.updateBatch(new InquirySignaturePlatformDO().setMaster(false));
        // 更新
        inquirySignaturePlatformMapper.update(new LambdaUpdateWrapper<InquirySignaturePlatformDO>()
            .set(InquirySignaturePlatformDO::getMaster, true)
            .eq(InquirySignaturePlatformDO::getSignaturePlatform, platform));

    }
}