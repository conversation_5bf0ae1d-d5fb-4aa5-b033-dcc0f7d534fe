package com.xyy.saas.inquiry.signature.server.service.prescriptiontemplate.dto;

import java.io.Serializable;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2025/03/11 17:33
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
@Accessors(chain = true)
public class TemplateSignCheckedDto implements Serializable {

    /**
     * 模板id
     */
    private Long templateId;

    /**
     * 字段
     */
    private String field;

    /**
     * 签名用户id 可为空,为空不处理
     */
    private Long userId;

    /**
     * 签章平台  1-法大大
     */
    private Integer signaturePlatform;

    /**
     * 签章平台配置id
     */
    private Integer signaturePlatformConfigId;

    /**
     * 是否免签校验：自动开方、药师自动审方不校验
     */
    private boolean checkSignParam;

}
