package com.xyy.saas.inquiry.signature.server.mq.producer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.signature.mq.SignaturePassingEvent;
import org.springframework.stereotype.Component;

/**
 * @Desc 签章通过 - 消息传递mq
 * <AUTHOR>
 */
@Component
@EventBusProducer(
    topic = SignaturePassingEvent.TOPIC
)
public class SignaturePassingProducer extends EventBusRocketMQTemplate {


}
