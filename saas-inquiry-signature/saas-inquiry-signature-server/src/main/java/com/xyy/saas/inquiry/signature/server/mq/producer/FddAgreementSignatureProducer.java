package com.xyy.saas.inquiry.signature.server.mq.producer;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusProducer;
import com.xyy.saas.eventbus.rocketmq.core.original.EventBusRocketMQTemplate;
import com.xyy.saas.inquiry.signature.server.mq.message.FddAgreementSignatureEvent;
import org.springframework.stereotype.Component;

/**
 * @Desc 法大大授权合同mq
 * <AUTHOR>
 */
@Component
@EventBusProducer(
    topic = FddAgreementSignatureEvent.TOPIC
)
public class FddAgreementSignatureProducer extends EventBusRocketMQTemplate {


}
