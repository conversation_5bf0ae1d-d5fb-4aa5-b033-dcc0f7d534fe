package com.xyy.saas.inquiry.signature.server.mq.consumer.fdd;

import com.xyy.saas.eventbus.rocketmq.annotation.EventBusConsumer;
import com.xyy.saas.eventbus.rocketmq.annotation.EventBusListener;
import com.xyy.saas.inquiry.signature.server.mq.message.FddAgreementSignatureEvent;
import com.xyy.saas.inquiry.signature.server.service.ca.InquirySignatureCaAuthService;
import com.xyy.saas.inquiry.signature.server.service.fdd.enums.FddCallbackEvent;
import com.xyy.saas.inquiry.signature.server.service.fdd.handler.FddCallbackSignTaskHandler;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Desc 法大大签署任务回调
 * <AUTHOR> {@link FddCallbackSignTaskHandler#handle(FddCallbackEvent, String)}
 */
@Component
@Slf4j
@EventBusConsumer(groupId = "com_xyy_saas_inquiry_signature_server_mq_consumer_FddSignAgreementMQConsumer",
    topic = FddAgreementSignatureEvent.TOPIC)
public class FddAgreementSignatureMQConsumer {

    @Resource
    private InquirySignatureCaAuthService inquirySignatureCaAuthService;


    @EventBusListener
    public void fddSignAgreementEvent(FddAgreementSignatureEvent fddCaAuthEvent) {
        inquirySignatureCaAuthService.callbackAgreementSign(fddCaAuthEvent.getMsg());
    }


}
