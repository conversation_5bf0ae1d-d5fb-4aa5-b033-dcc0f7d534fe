package com.xyy.saas.inquiry.signature.server.service.fdd.dto;

import lombok.Data;

import java.io.Serializable;

/**
 * 授权链接 redirectUrl回调通知
 *
 * <AUTHOR>
 */
@Data
public class AuthCallback2RedirectDto implements Serializable {

    //Unix标准时间戳，精确到毫秒。
    private String timestamp;

    /*请求参数的签名值。您可自行决定是否验证该签名，计算方法和样例参考下面示例。*/
    private String signature;

    //个人用户在应用中的唯一标识，长度最大64个字符。
    private String clientUserId;

    //法大大平台为该用户在该应用appId范围内的唯一标识。长度最大64个字符。
    private String openUserId;

    //本次授权操作结果：success: 成功；fail: 失败。
    private String authResult;

    //本次授权失败原因：reject: 用户操作不允许授权。
    private String authFailedReason;

    // 个人用户实际授权范围，逗号分隔。
    private String authScope;
}
