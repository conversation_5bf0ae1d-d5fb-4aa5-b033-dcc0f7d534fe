package com.xyy.saas.inquiry.signature.server.dal.mysql.platform;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.mapper.BaseMapperX;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import com.xyy.saas.inquiry.signature.server.controller.admin.platform.vo.InquirySignaturePlatformPageReqVO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.platform.InquirySignaturePlatformDO;
import org.apache.ibatis.annotations.Mapper;

/**
 * 签章平台配置 Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface InquirySignaturePlatformMapper extends BaseMapperX<InquirySignaturePlatformDO> {

    default PageResult<InquirySignaturePlatformDO> selectPage(InquirySignaturePlatformPageReqVO reqVO) {
        return selectPage(reqVO, new LambdaQueryWrapperX<InquirySignaturePlatformDO>()
            .eqIfPresent(InquirySignaturePlatformDO::getSignaturePlatform, reqVO.getSignaturePlatform())
            .likeIfPresent(InquirySignaturePlatformDO::getParamName, reqVO.getParamName())
            .eqIfPresent(InquirySignaturePlatformDO::getParamValue, reqVO.getParamValue())
            .eqIfPresent(InquirySignaturePlatformDO::getDescription, reqVO.getDescription())
            .betweenIfPresent(InquirySignaturePlatformDO::getCreateTime, reqVO.getCreateTime())
            .orderByDesc(InquirySignaturePlatformDO::getId));
    }


}