package com.xyy.saas.inquiry.signature.server.service.pdf.pdfbox;

import org.apache.fontbox.util.BoundingBox;
import org.apache.pdfbox.pdfparser.PDFStreamParser;
import org.apache.pdfbox.pdfwriter.ContentStreamWriter;
import org.apache.pdfbox.pdmodel.common.PDRectangle;
import org.apache.pdfbox.cos.*;
import org.apache.pdfbox.pdmodel.*;
import org.apache.pdfbox.pdmodel.font.*;
import org.apache.pdfbox.pdmodel.graphics.color.*;
import org.apache.pdfbox.pdmodel.interactive.action.*;
import org.apache.pdfbox.pdmodel.interactive.annotation.*;
import org.apache.pdfbox.pdmodel.interactive.form.*;
import org.apache.pdfbox.contentstream.operator.*;
import org.apache.pdfbox.util.Matrix;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.geom.AffineTransform;
import java.awt.geom.GeneralPath;
import java.awt.geom.Point2D;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.text.AttributedCharacterIterator;
import java.text.AttributedString;
import java.text.BreakIterator;
import java.util.*;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
public class PdfboxGeneratorHelper {

    private static final Logger LOG = LoggerFactory.getLogger(PdfboxGeneratorHelper.class);

    private static final Operator BMC = Operator.getOperator("BMC");
    private static final Operator EMC = Operator.getOperator("EMC");


    /**
     * The highlight color
     * <p>
     * The color setting is used by Adobe to display the highlight box for selected entries in a list box.
     * <p>
     * Regardless of other settings in an existing appearance stream Adobe will always use this value.
     */
    private static final float[] HIGHLIGHT_COLOR = {153 / 255f, 193 / 255f, 215 / 255f};

    /**
     * The scaling factor for font units to PDF units
     */
    private static final int FONTSCALE = 1000;

    /**
     * The default font size used for multiline text
     */
    private static final float DEFAULT_FONT_SIZE = 12;

    /**
     * The minimum/maximum font sizes used for multiline text auto sizing
     */
    private static final float MINIMUM_FONT_SIZE = 4;
    private static final float MAXIMUM_FONT_SIZE = 300;

    /**
     * The default padding applied by Acrobat to the fields bbox.
     */
    private static final float DEFAULT_PADDING = 0.5f;

    private final PDDocument document;
    private final PDVariableText field;
    private final List<? extends PDFont> fontList;

    private PDDefaultAppearanceString defaultAppearance;
    private String value;


    /**
     * Constructs a COSAppearance from the given field.
     *
     * @param field the field which you wish to control the appearance of
     * @throws IOException
     */
    public PdfboxGeneratorHelper(PDDocument document, PDVariableText field, List<? extends PDFont> fontList) throws IOException {
        this.document = document;
        this.field = field;
        this.fontList = fontList == null ? new ArrayList<>() : fontList;
        validateAndEnsureAcroFormResources();

        try {
            COSBase base = getInheritableAttribute(field, COSName.DA);
            COSString da = base instanceof COSString ? (COSString) base : null;
            PDResources dr = field.getAcroForm().getDefaultResources();
            this.defaultAppearance = new PDDefaultAppearanceString(da, dr);
        } catch (IOException ex) {
            throw new IOException("Could not process default appearance string '" +
                field.getDefaultAppearance() + "' for field '" +
                field.getFullyQualifiedName() + "': " + ex.getMessage(), ex);
        }
    }

    /**
     * Returns the given attribute, inheriting from parent nodes if necessary.
     *
     * @param key the key to look up
     * @return COS value for the given key
     */
    public COSBase getInheritableAttribute(PDField field, COSName key) {
        COSDictionary dictionary = field.getCOSObject();
        if (dictionary.containsKey(key)) {
            return dictionary.getDictionaryObject(key);
        }
        PDField parent = field.getParent();
        if (parent != null) {
            return getInheritableAttribute(parent, key);
        }
        return field.getAcroForm().getCOSObject().getDictionaryObject(key);
    }


    /*
     * Adobe Reader/Acrobat are adding resources which are at the field/widget level
     * to the AcroForm level.
     */
    private void validateAndEnsureAcroFormResources() {
        // add font resources which might be available at the field
        // level but are not at the AcroForm level to the AcroForm
        // to match Adobe Reader/Acrobat behavior
        PDResources acroFormResources = field.getAcroForm().getDefaultResources();
        if (acroFormResources == null) {
            return;
        }

        for (PDAnnotationWidget widget : field.getWidgets()) {
            PDAppearanceStream stream = widget.getNormalAppearanceStream();
            if (stream == null) {
                continue;
            }
            PDResources widgetResources = stream.getResources();
            if (widgetResources == null) {
                continue;
            }
            COSDictionary widgetFontDict = widgetResources.getCOSObject()
                .getCOSDictionary(COSName.FONT);
            COSDictionary acroFormFontDict = acroFormResources.getCOSObject()
                .getCOSDictionary(COSName.FONT);
            for (COSName fontResourceName : widgetResources.getFontNames()) {
                try {
                    if (acroFormResources.getFont(fontResourceName) == null) {
                        LOG.debug("Adding font resource {} from widget to AcroForm", fontResourceName);
                        // use the COS-object to preserve a possible indirect object reference
                        acroFormFontDict.setItem(fontResourceName,
                            widgetFontDict.getItem(fontResourceName));
                    }
                } catch (IOException e) {
                    LOG.warn("Unable to match field level font with AcroForm font", e);
                }
            }
        }
    }


    /**
     * This is the public method for setting the appearance stream.
     *
     * @param apValue the String value which the appearance should represent
     * @throws IOException If there is an error creating the stream.
     */
    public void setAppearanceValue(String apValue) throws IOException {
        value = getFormattedValue(apValue);

        // Treat multiline field values in single lines as single lime values.
        // This is in line with how Adobe Reader behaves when entering text
        // interactively but NOT how it behaves when the field value has been
        // set programmatically and Reader is forced to generate the appearance
        // using PDAcroForm.setNeedAppearances
        // see PDFBOX-3911
        if (field instanceof PDTextField && !((PDTextField) field).isMultiline()) {
            value = value.replaceAll("\\u000D\\u000A|[\\u000A\\u000B\\u000C\\u000D\\u0085\\u2028\\u2029]", " ");
        }

        for (PDAnnotationWidget widget : field.getWidgets()) {
            if (widget.getCOSObject().containsKey("PMD")) {
                LOG.warn("widget of field {} is a PaperMetaData widget, no appearance stream created", field.getFullyQualifiedName());
                continue;
            }

            // some fields have the /Da at the widget level if the
            // widgets differ in layout.
            PDDefaultAppearanceString acroFormAppearance = defaultAppearance;

            if (widget.getCOSObject().getDictionaryObject(COSName.DA) != null) {
                defaultAppearance = getWidgetDefaultAppearanceString(widget);
            }

            PDRectangle rect = widget.getRectangle();
            if (rect == null) {
                widget.getCOSObject().removeItem(COSName.AP);
                LOG.warn("widget of field {} has no rectangle, no appearance stream created", field.getFullyQualifiedName());
                continue;
            }

            PDAppearanceStream appearanceStream = getPdAppearanceStream(widget);

            setAppearanceContent(widget, appearanceStream);

            // restore the field level appearance
            defaultAppearance = acroFormAppearance;
        }
    }

    private PDAppearanceStream getPdAppearanceStream(PDAnnotationWidget widget) throws IOException {
        PDAppearanceDictionary appearanceDict = widget.getAppearance();
        if (appearanceDict == null) {
            appearanceDict = new PDAppearanceDictionary();
            widget.setAppearance(appearanceDict);
        }

        PDAppearanceEntry appearance = appearanceDict.getNormalAppearance();
        // TODO support appearances other than "normal"

        PDAppearanceStream appearanceStream;
        if (isValidAppearanceStream(appearance)) {
            appearanceStream = appearance.getAppearanceStream();
        } else {
            appearanceStream = prepareNormalAppearanceStream(widget);
            appearanceDict.setNormalAppearance(appearanceStream);
            // TODO support appearances other than "normal"
        }
        PDAppearanceCharacteristicsDictionary appearanceCharacteristics =
            widget.getAppearanceCharacteristics();

        /*
         * Adobe Acrobat always recreates the complete appearance stream if there is an appearance characteristics
         * entry (the widget dictionaries MK entry). In addition if there is no content yet also create the appearance
         * stream from the entries.
         *
         */
        if (appearanceCharacteristics != null || appearanceStream.getContentStream().getLength() == 0) {
            initializeAppearanceContent(widget, appearanceCharacteristics, appearanceStream);
        }
        return appearanceStream;
    }

    private String getFormattedValue(String apValue) {
        // format the field value for the appearance if there is scripting support and the field
        // has a format event
        PDFormFieldAdditionalActions actions = field.getActions();
        if (actions == null) {
            return apValue;
        }
        PDAction actionF = actions.getF();
        if (actionF != null) {
            if (field.getAcroForm().getScriptingHandler() != null) {
                ScriptingHandler scriptingHandler = field.getAcroForm().getScriptingHandler();
                return scriptingHandler.format((PDActionJavaScript) actionF, apValue);
            }
            LOG.info("Field contains a formatting action but no ScriptingHandler has been supplied - formatted value might be incorrect");
        }
        return apValue;
    }

    private static boolean isValidAppearanceStream(PDAppearanceEntry appearance) {
        if (appearance == null) {
            return false;
        }
        if (!appearance.isStream()) {
            return false;
        }
        PDRectangle bbox = appearance.getAppearanceStream().getBBox();
        if (bbox == null) {
            return false;
        }
        return Math.abs(bbox.getWidth()) > 0 && Math.abs(bbox.getHeight()) > 0;
    }

    private PDAppearanceStream prepareNormalAppearanceStream(PDAnnotationWidget widget) {
        PDAppearanceStream appearanceStream = new PDAppearanceStream(document);

        // Calculate the entries for the bounding box and the transformation matrix
        // settings for the appearance stream
        int rotation = resolveRotation(widget);
        PDRectangle rect = widget.getRectangle();
        Matrix matrix = Matrix.getRotateInstance(Math.toRadians(rotation), 0, 0);
        Point2D.Float point2D = matrix.transformPoint(rect.getWidth(), rect.getHeight());

        PDRectangle bbox = new PDRectangle(Math.abs((float) point2D.getX()), Math.abs((float) point2D.getY()));
        appearanceStream.setBBox(bbox);

        AffineTransform at = calculateMatrix(bbox, rotation);
        if (!at.isIdentity()) {
            appearanceStream.setMatrix(at);
        }
        appearanceStream.setFormType(1);
        appearanceStream.setResources(new PDResources());
        return appearanceStream;
    }

    private PDDefaultAppearanceString getWidgetDefaultAppearanceString(PDAnnotationWidget widget) throws IOException {
        COSString da = (COSString) widget.getCOSObject().getDictionaryObject(COSName.DA);
        PDResources dr = field.getAcroForm().getDefaultResources();
        return new PDDefaultAppearanceString(da, dr);
    }

    private int resolveRotation(PDAnnotationWidget widget) {
        PDAppearanceCharacteristicsDictionary characteristicsDictionary = widget.getAppearanceCharacteristics();
        if (characteristicsDictionary != null) {
            // 0 is the default value if the R key doesn't exist
            return characteristicsDictionary.getRotation();
        }
        return 0;
    }

    /**
     * Initialize the content of the appearance stream.
     * <p>
     * Get settings like border style, border width and colors to be used to draw a rectangle and background color around the widget
     *
     * @param widget                    the field widget
     * @param appearanceCharacteristics the appearance characteristics dictionary from the widget or null
     * @param appearanceStream          the appearance stream to be used
     * @throws IOException in case we can't write to the appearance stream
     */
    private void initializeAppearanceContent(PDAnnotationWidget widget,
        PDAppearanceCharacteristicsDictionary appearanceCharacteristics,
        PDAppearanceStream appearanceStream) throws IOException {
        try (ByteArrayOutputStream output = new ByteArrayOutputStream();
            PDAppearanceContentStream contents = new PDAppearanceContentStream(appearanceStream, output)) {
            // TODO: support more entries like patterns, etc.
            if (appearanceCharacteristics != null) {
                PDColor backgroundColour = appearanceCharacteristics.getBackground();
                if (backgroundColour != null) {
                    contents.setNonStrokingColor(backgroundColour);
                    PDRectangle bbox = resolveBoundingBox(widget, appearanceStream);
                    contents.addRect(bbox.getLowerLeftX(), bbox.getLowerLeftY(), bbox.getWidth(), bbox.getHeight());
                    contents.fill();
                }

                float lineWidth = 0f;
                PDColor borderColour = appearanceCharacteristics.getBorderColour();
                if (borderColour != null) {
                    contents.setStrokingColor(borderColour);
                    lineWidth = 1f;
                }
                PDBorderStyleDictionary borderStyle = widget.getBorderStyle();
                if (borderStyle != null && borderStyle.getWidth() > 0) {
                    lineWidth = borderStyle.getWidth();
                }

                if (lineWidth > 0 && borderColour != null) {
                    if (Float.compare(lineWidth, 1) != 0) {
                        contents.setLineWidth(lineWidth);
                    }
                    PDRectangle bbox = resolveBoundingBox(widget, appearanceStream);
                    PDRectangle clipRect = applyPadding(bbox, Math.max(DEFAULT_PADDING, lineWidth / 2));
                    contents.addRect(clipRect.getLowerLeftX(), clipRect.getLowerLeftY(), clipRect.getWidth(), clipRect.getHeight());
                    contents.closeAndStroke();
                }

                // draw the dividers for a comb field
                if (borderColour != null && shallComb()) {
                    int maxLen = ((PDTextField) field).getMaxLen();
                    PDRectangle bbox = resolveBoundingBox(widget, appearanceStream);
                    PDRectangle clipRect = applyPadding(bbox, Math.max(DEFAULT_PADDING, lineWidth / 2));
                    float lowerLeft = clipRect.getLowerLeftX();
                    float height = clipRect.getHeight();

                    float combWidth = bbox.getWidth() / maxLen;

                    for (int i = 0; i < maxLen - 1; i++) {
                        contents.moveTo(combWidth + combWidth * i, height);
                        contents.lineTo(combWidth + combWidth * i, lowerLeft);
                    }
                    contents.closeAndStroke();
                }
            }

            writeToStream(output.toByteArray(), appearanceStream);

        }
    }

    /**
     * Constructs and sets new contents for given appearance stream.
     */
    private void setAppearanceContent(PDAnnotationWidget widget,
        PDAppearanceStream appearanceStream) throws IOException {
        // first copy any needed resources from the document’s DR dictionary into
        // the stream’s Resources dictionary
        defaultAppearance.copyNeededResourcesTo(appearanceStream);

        // then replace the existing contents of the appearance stream from /Tx BMC
        // to the matching EMC
        try (ByteArrayOutputStream output = new ByteArrayOutputStream()) {
            ContentStreamWriter writer = new ContentStreamWriter(output);

            List<Object> tokens = new PDFStreamParser(appearanceStream).parse();
            int bmcIndex = tokens.indexOf(BMC);
            if (bmcIndex == -1) {
                // append to existing stream
                writer.writeTokens(tokens);
                writer.writeTokens(COSName.TX, BMC);
            } else {
                // prepend content before BMC
                writer.writeTokens(tokens.subList(0, bmcIndex + 1));
            }

            // insert field contents
            insertGeneratedAppearance(widget, appearanceStream, output);

            // List<? extends PDFont> fonts = CollectionUtils.isEmpty(fontList) ? Collections.singletonList(defaultAppearance.getFont()) : fontList;
            // for (PDFont font : fonts) {
            //     try {
            //         insertGeneratedAppearance(widget, font, appearanceStream, output);
            //     } catch (IllegalArgumentException e) {
            //         LOG.error("Field: {} 不支持字体 {} : {}", field.getFullyQualifiedName(), font.getName(), e.getMessage(), e);
            //     }
            // }

            int emcIndex = tokens.indexOf(EMC);
            if (emcIndex == -1) {
                // append EMC
                writer.writeTokens(EMC);
            } else {
                // append contents after EMC
                writer.writeTokens(tokens.subList(emcIndex, tokens.size()));
            }
            writeToStream(output.toByteArray(), appearanceStream);
        }
    }

    /**
     * Generate and insert text content and clipping around it.
     */
    private void insertGeneratedAppearance(PDAnnotationWidget widget,
        PDAppearanceStream appearanceStream,
        OutputStream output) throws IOException {
        try (PDAppearanceContentStream contents = new PDAppearanceContentStream(appearanceStream, output)) {
            PDRectangle bbox = resolveBoundingBox(widget, appearanceStream);

            // Acrobat calculates the left and right padding dependent on the offset of the border edge
            // This calculation works for forms having been generated by Acrobat.
            // The minimum distance is always 1f even if there is no rectangle being drawn around.
            float borderWidth = 0;
            if (widget.getBorderStyle() != null) {
                borderWidth = widget.getBorderStyle().getWidth();
            }
            float padding = Math.max(1f, borderWidth);
            PDRectangle clipRect = applyPadding(bbox, padding);
            PDRectangle contentRect = applyPadding(clipRect, padding);

            contents.saveGraphicsState();

            // Acrobat always adds a clipping path
            contents.addRect(clipRect.getLowerLeftX(), clipRect.getLowerLeftY(),
                clipRect.getWidth(), clipRect.getHeight());
            contents.clip();

            // get the font
            PDFont font = defaultAppearance.getFont();
            if (font == null) {
                throw new IllegalArgumentException("font is null, check whether /DA entry is incomplete or incorrect");
            }
            if (font.getName().contains("+")) {
                LOG.warn("Font '{}' of field '{}' contains subsetted font '{}'", defaultAppearance.getFontName().getName(), field.getFullyQualifiedName(), font.getName());
                LOG.warn("This may bring trouble with PDField.setValue(), PDAcroForm.flatten() or PDAcroForm.refreshAppearances()");
                LOG.warn("You should replace this font with a non-subsetted font:");
                LOG.warn("PDFont font = PDType0Font.load(doc, new FileInputStream(fontfile), false);");
                LOG.warn("acroForm.getDefaultResources().put(COSName.getPDFName(\"{}\", font);", defaultAppearance.getFontName().getName());
            }
            // calculate the fontSize (because 0 = autosize)
            float fontSize = defaultAppearance.getFontSize();

            if (Float.compare(fontSize, 0) == 0) {
                fontSize = calculateFontSize(font, contentRect);
            }

            // for a listbox generate the highlight rectangle for the selected
            // options
            if (field instanceof PDListBox) {
                insertGeneratedListboxSelectionHighlight(contents, appearanceStream, font, fontSize);
            }

            // start the text output
            contents.beginText();

            // write font and color from the /DA string, with the calculated font size
            defaultAppearance.writeTo(contents, fontSize);

            // calculate the y-position of the baseline
            float y;

            // calculate font metrics at font size
            float fontScaleY = fontSize / FONTSCALE;
            float fontBoundingBoxAtSize = font.getBoundingBox().getHeight() * fontScaleY;

            float fontCapAtSize;
            float fontDescentAtSize;

            if (font.getFontDescriptor() != null) {
                fontCapAtSize = font.getFontDescriptor().getCapHeight() * fontScaleY;
                fontDescentAtSize = font.getFontDescriptor().getDescent() * fontScaleY;
            } else {
                float fontCapHeight = resolveCapHeight(font);
                float fontDescent = resolveDescent(font);
                LOG.debug("missing font descriptor - resolved Cap/Descent to {}/{}", fontCapHeight, fontDescent);
                fontCapAtSize = fontCapHeight * fontScaleY;
                fontDescentAtSize = fontDescent * fontScaleY;
            }

            if (field instanceof PDTextField && ((PDTextField) field).isMultiline()) {
                y = contentRect.getUpperRightY() - fontBoundingBoxAtSize;
            } else {
                // Adobe shows the text 'shifted up' in case the caps don't fit into the clipping area
                if (fontCapAtSize > clipRect.getHeight()) {
                    y = clipRect.getLowerLeftY() + -fontDescentAtSize;
                } else {
                    // calculate the position based on the content rectangle
                    y = clipRect.getLowerLeftY() + (clipRect.getHeight() - fontCapAtSize) / 2;

                    // check to ensure that ascents and descents fit
                    if (y - clipRect.getLowerLeftY() < -fontDescentAtSize) {

                        float fontDescentBased = -fontDescentAtSize + contentRect.getLowerLeftY();
                        float fontCapBased = contentRect.getHeight() - contentRect.getLowerLeftY() - fontCapAtSize;

                        y = Math.min(fontDescentBased, Math.max(y, fontCapBased));
                    }
                }
            }

            // show the text
            float x = contentRect.getLowerLeftX();

            try {
                // special handling for comb boxes as these are like table cells with individual
                // chars
                if (shallComb()) {
                    insertGeneratedCombAppearance(contents, appearanceStream, font, fontSize);
                } else if (field instanceof PDListBox) {
                    insertGeneratedListboxAppearance(contents, appearanceStream, contentRect, font, fontSize);
                } else {
                    PlainText textContent = new PlainText(value);
                    AppearanceStyle appearanceStyle = new AppearanceStyle();
                    appearanceStyle.setFont(font);
                    appearanceStyle.setFontSize(fontSize);

                    // Adobe Acrobat uses the font's bounding box for the leading between the lines
                    appearanceStyle.setLeading(font.getBoundingBox().getHeight() * fontScaleY);

                    PlainTextFormatter formatter = new PlainTextFormatter
                        // .Builder(contents)
                        .Builder(contents)
                        .style(appearanceStyle)
                        .text(textContent)
                        .width(contentRect.getWidth())
                        .wrapLines(isMultiLine())
                        .initialOffset(x, y)
                        .textAlign(getTextAlign(widget))
                        .build();
                    formatter.format();
                }
            } catch (IllegalArgumentException e) {
                LOG.error("Error generating appearance stream: {}", e.getMessage(), e);
            }

            contents.endText();

            contents.restoreGraphicsState();
        }
    }

    /**
     * Generate and insert text content and clipping around it.
     */
    public PlainTextFormatter plainTextFormatter(PDAnnotationWidget widget, String apValue, PDFont font, float fontSize) throws IOException {
        value = getFormattedValue(apValue);

        // Treat multiline field values in single lines as single lime values.
        // This is in line with how Adobe Reader behaves when entering text
        // interactively but NOT how it behaves when the field value has been
        // set programmatically and Reader is forced to generate the appearance
        // using PDAcroForm.setNeedAppearances
        // see PDFBOX-3911
        if (field instanceof PDTextField && !((PDTextField) field).isMultiline()) {
            value = value.replaceAll("\\u000D\\u000A|[\\u000A\\u000B\\u000C\\u000D\\u0085\\u2028\\u2029]", " ");
        }

        PDRectangle bbox = resolveBoundingBox(widget, new PDAppearanceStream(document));

        // Acrobat calculates the left and right padding dependent on the offset of the border edge
        // This calculation works for forms having been generated by Acrobat.
        // The minimum distance is always 1f even if there is no rectangle being drawn around.
        float borderWidth = 0;
        if (widget.getBorderStyle() != null) {
            borderWidth = widget.getBorderStyle().getWidth();
        }
        float padding = Math.max(1f, borderWidth);
        PDRectangle clipRect = applyPadding(bbox, padding);
        PDRectangle contentRect = applyPadding(clipRect, padding);

        // contents.saveGraphicsState();
        //
        // // Acrobat always adds a clipping path
        // contents.addRect(clipRect.getLowerLeftX(), clipRect.getLowerLeftY(),
        //                  clipRect.getWidth(), clipRect.getHeight());
        // contents.clip();

        // get the font
        if (font == null) {
            throw new IllegalArgumentException("font is null, check whether /DA entry is incomplete or incorrect");
        }
        // if (font.getName().contains("+"))
        // {
        //     LOG.warn("Font '{}' of field '{}' contains subsetted font '{}'", defaultAppearance.getFontName().getName(), field.getFullyQualifiedName(), font.getName());
        //     LOG.warn("This may bring trouble with PDField.setValue(), PDAcroForm.flatten() or PDAcroForm.refreshAppearances()");
        //     LOG.warn("You should replace this font with a non-subsetted font:");
        //     LOG.warn("PDFont font = PDType0Font.load(doc, new FileInputStream(fontfile), false);");
        //     LOG.warn("acroForm.getDefaultResources().put(COSName.getPDFName(\"{}\", font);", defaultAppearance.getFontName().getName());
        // }
        // // calculate the fontSize (because 0 = autosize)
        // float fontSize = defaultAppearance.getFontSize();

        if (Float.compare(fontSize, 0) <= 0) {
            fontSize = calculateFontSize(font, contentRect);
        }

        // // start the text output
        // contents.beginText();
        //
        // // write font and color from the /DA string, with the calculated font size
        // defaultAppearance.writeTo(contents, fontSize);

        // calculate the y-position of the baseline
        float y;

        // calculate font metrics at font size
        float fontScaleY = fontSize / FONTSCALE;
        float fontBoundingBoxAtSize = font.getBoundingBox().getHeight() * fontScaleY;

        float fontCapAtSize;
        float fontDescentAtSize;

        if (font.getFontDescriptor() != null) {
            fontCapAtSize = font.getFontDescriptor().getCapHeight() * fontScaleY;
            fontDescentAtSize = font.getFontDescriptor().getDescent() * fontScaleY;
        } else {
            float fontCapHeight = resolveCapHeight(font);
            float fontDescent = resolveDescent(font);
            LOG.debug("missing font descriptor - resolved Cap/Descent to {}/{}", fontCapHeight, fontDescent);
            fontCapAtSize = fontCapHeight * fontScaleY;
            fontDescentAtSize = fontDescent * fontScaleY;
        }

        if (field instanceof PDTextField && ((PDTextField) field).isMultiline()) {
            y = contentRect.getUpperRightY() - fontBoundingBoxAtSize;
        } else {
            // Adobe shows the text 'shifted up' in case the caps don't fit into the clipping area
            if (fontCapAtSize > clipRect.getHeight()) {
                y = clipRect.getLowerLeftY() + -fontDescentAtSize;
            } else {
                // calculate the position based on the content rectangle
                y = clipRect.getLowerLeftY() + (clipRect.getHeight() - fontCapAtSize) / 2;

                // check to ensure that ascents and descents fit
                if (y - clipRect.getLowerLeftY() < -fontDescentAtSize) {

                    float fontDescentBased = -fontDescentAtSize + contentRect.getLowerLeftY();
                    float fontCapBased = contentRect.getHeight() - contentRect.getLowerLeftY() - fontCapAtSize;

                    y = Math.min(fontDescentBased, Math.max(y, fontCapBased));
                }
            }
        }

        // show the text
        float x = contentRect.getLowerLeftX();

        try {
            PlainText textContent = new PlainText(value);
            AppearanceStyle appearanceStyle = new AppearanceStyle();
            appearanceStyle.setFont(font);
            appearanceStyle.setFontSize(fontSize);

            // Adobe Acrobat uses the font's bounding box for the leading between the lines
            appearanceStyle.setLeading(font.getBoundingBox().getHeight() * fontScaleY);

            PlainTextFormatter formatter = new PlainTextFormatter
                .Builder()
                .style(appearanceStyle)
                .text(textContent)
                .width(contentRect.getWidth())
                .clipRect(clipRect)
                .wrapLines(isMultiLine())
                .initialOffset(x, y)
                .textAlign(getTextAlign(widget))
                .build();
            formatter.format();

            return formatter;
        } catch (IllegalArgumentException e) {
            LOG.error("Error generating appearance stream: {}", e.getMessage(), e);
        }
        return null;

        // contents.endText();
        // contents.restoreGraphicsState();
    }

    /*
     * PDFBox handles a widget with a joined in field dictionary and without
     * an individual name as a widget only. As a result - as a widget can't have a
     * quadding /Q entry we need to do a low level access to the dictionary and
     * otherwise get the quadding from the field.
     */
    private int getTextAlign(PDAnnotationWidget widget) {
        // Use quadding value from joined field/widget if set, else use from field.
        return widget.getCOSObject().getInt(COSName.Q, field.getQ());
    }


    private AffineTransform calculateMatrix(PDRectangle bbox, int rotation) {
        if (rotation == 0) {
            return new AffineTransform();
        }
        float tx = 0, ty = 0;
        switch (rotation) {
            case 90:
                tx = bbox.getUpperRightY();
                break;
            case 180:
                tx = bbox.getUpperRightY();
                ty = bbox.getUpperRightX();
                break;
            case 270:
                ty = bbox.getUpperRightX();
                break;
            default:
                break;
        }
        Matrix matrix = Matrix.getRotateInstance(Math.toRadians(rotation), tx, ty);
        return matrix.createAffineTransform();
    }

    private boolean isMultiLine() {
        return field instanceof PDTextField && ((PDTextField) field).isMultiline();
    }

    /**
     * Determine if the appearance shall provide a comb output.
     *
     * <p>
     * May be set only if the MaxLen entry is present in the text field dictionary and if the Multiline, Password, and FileSelect flags are clear. If set, the field shall be automatically divided into as many equally spaced positions, or
     * combs, as the value of MaxLen, and the text is laid out into those combs.
     * </p>
     *
     * @return the comb state
     */
    private boolean shallComb() {
        return field instanceof PDTextField &&
            ((PDTextField) field).isComb() &&
            ((PDTextField) field).getMaxLen() != -1 &&
            !((PDTextField) field).isMultiline() &&
            !((PDTextField) field).isPassword() &&
            !((PDTextField) field).isFileSelect();
    }

    /**
     * Generate the appearance for comb fields.
     *
     * @param contents         the content stream to write to
     * @param appearanceStream the appearance stream used
     * @param font             the font to be used
     * @param fontSize         the font size to be used
     * @throws IOException
     */
    private void insertGeneratedCombAppearance(PDAppearanceContentStream contents, PDAppearanceStream appearanceStream,
        PDFont font, float fontSize) throws IOException {
        if (value == null || value.isEmpty()) {
            return;
        }
        int maxLen = ((PDTextField) field).getMaxLen();
        int quadding = field.getQ();
        int numChars = Math.min(value.length(), maxLen);

        float combWidth = appearanceStream.getBBox().getWidth() / maxLen;
        float ascentAtFontSize = font.getFontDescriptor().getAscent() / FONTSCALE * fontSize;

        float baselineOffset = appearanceStream.getBBox().getLowerLeftY() +
            (appearanceStream.getBBox().getHeight() - ascentAtFontSize) / 2;

        float prevCharWidth = 0f;

        // set initial offset based on width of first char.
        float firstCharWidth = font.getStringWidth(value.substring(0, 1)) / FONTSCALE * fontSize;
        float initialOffset = (combWidth - firstCharWidth) / 2;

        // add to initial offset if right aligned or centered
        if (quadding == 2) {
            initialOffset = initialOffset + (maxLen - numChars) * combWidth;
        } else if (quadding == 1) {
            initialOffset = initialOffset + Math.floorDiv(maxLen - numChars, 2) * combWidth;
        }

        float xOffset = initialOffset;

        for (int i = 0; i < numChars; i++) {
            String combString = value.substring(i, i + 1);
            float currCharWidth = font.getStringWidth(combString) / FONTSCALE * fontSize / 2;

            xOffset = xOffset + prevCharWidth / 2 - currCharWidth / 2;

            if (i == 0) {
                contents.newLineAtOffset(initialOffset, baselineOffset);
            } else {
                contents.newLineAtOffset(xOffset, baselineOffset);
            }
            contents.showText(combString);

            baselineOffset = 0;
            prevCharWidth = currCharWidth;
            xOffset = combWidth;
        }
    }

    private void insertGeneratedListboxSelectionHighlight(PDAppearanceContentStream contents, PDAppearanceStream appearanceStream,
        PDFont font, float fontSize) throws IOException {
        PDListBox listBox = (PDListBox) field;
        List<Integer> indexEntries = listBox.getSelectedOptionsIndex();
        List<String> values = listBox.getValue();
        List<String> options = listBox.getOptionsExportValues();

        if (!values.isEmpty() && !options.isEmpty() && indexEntries.isEmpty()) {
            // create indexEntries from options
            indexEntries = new ArrayList<>(values.size());
            for (String v : values) {
                indexEntries.add(options.indexOf(v));
            }
        }

        // The first entry which shall be presented might be adjusted by the optional TI key
        // If this entry is present, the first entry to be displayed is the keys value,
        // otherwise display starts with the first entry in Opt.
        int topIndex = listBox.getTopIndex();

        float highlightBoxHeight = font.getBoundingBox().getHeight() * fontSize / FONTSCALE;

        // the padding area
        PDRectangle paddingEdge = applyPadding(appearanceStream.getBBox(), 1);

        for (int selectedIndex : indexEntries) {
            contents.setNonStrokingColor(HIGHLIGHT_COLOR[0], HIGHLIGHT_COLOR[1], HIGHLIGHT_COLOR[2]);

            contents.addRect(paddingEdge.getLowerLeftX(),
                paddingEdge.getUpperRightY() - highlightBoxHeight * (selectedIndex - topIndex + 1) + 2,
                paddingEdge.getWidth(),
                highlightBoxHeight);
            contents.fill();
        }
        contents.setNonStrokingColor(0f);
    }


    private void insertGeneratedListboxAppearance(PDAppearanceContentStream contents, PDAppearanceStream appearanceStream,
        PDRectangle contentRect, PDFont font, float fontSize) throws IOException {
        contents.setNonStrokingColor(0f);

        int q = field.getQ();

        if (q == PDVariableText.QUADDING_CENTERED || q == PDVariableText.QUADDING_RIGHT) {
            float fieldWidth = appearanceStream.getBBox().getWidth();
            float stringWidth = (font.getStringWidth(value) / FONTSCALE) * fontSize;
            float adjustAmount = fieldWidth - stringWidth - 4;

            if (q == PDVariableText.QUADDING_CENTERED) {
                adjustAmount = adjustAmount / 2.0f;
            }

            contents.newLineAtOffset(adjustAmount, 0);
        } else if (q != PDVariableText.QUADDING_LEFT) {
            throw new IOException("Error: Unknown justification value:" + q);
        }

        List<String> options = ((PDListBox) field).getOptionsDisplayValues();
        int numOptions = options.size();

        float yTextPos = contentRect.getUpperRightY();

        int topIndex = ((PDListBox) field).getTopIndex();
        float ascent = font.getFontDescriptor().getAscent();
        float height = font.getBoundingBox().getHeight();

        for (int i = topIndex; i < numOptions; i++) {
            if (i == topIndex) {
                yTextPos = yTextPos - ascent / FONTSCALE * fontSize;
            } else {
                yTextPos = yTextPos - height / FONTSCALE * fontSize;
                contents.beginText();
            }

            contents.newLineAtOffset(contentRect.getLowerLeftX(), yTextPos);
            contents.showText(options.get(i));

            if (i != (numOptions - 1)) {
                contents.endText();
            }
        }
    }

    /**
     * Writes the stream to the actual stream in the COSStream.
     *
     * @throws IOException If there is an error writing to the stream
     */
    private void writeToStream(byte[] data, PDAppearanceStream appearanceStream) throws IOException {
        try (OutputStream out = appearanceStream.getCOSObject().createOutputStream()) {
            out.write(data);
        }
    }

    /**
     * My "not so great" method for calculating the fontsize. It does not work superb, but it handles ok.
     *
     * @return the calculated font-size
     * @throws IOException If there is an error getting the font information.
     */
    private float calculateFontSize(PDFont font, PDRectangle contentRect) throws IOException {
        float fontSize = defaultAppearance.getFontSize();

        // zero is special, it means the text is auto-sized
        if (Float.compare(fontSize, 0) == 0) {
            if (isMultiLine()) {
                PlainText textContent = new PlainText(value);
                if (textContent.getParagraphs() != null) {
                    float width = contentRect.getWidth() - contentRect.getLowerLeftX();
                    float fs = MINIMUM_FONT_SIZE;
                    while (fs <= DEFAULT_FONT_SIZE) {
                        // determine the number of lines needed for this font and contentRect
                        int numLines = 0;
                        for (PlainText.Paragraph paragraph : textContent.getParagraphs()) {
                            numLines += paragraph.getLines(font, fs, width).size();
                        }
                        // calculate the height required for this font size
                        float fontScaleY = fs / FONTSCALE;
                        float leading = font.getBoundingBox().getHeight() * fontScaleY;
                        float height = leading * numLines;

                        // if this font size didn't fit, use the prior size that did fit
                        if (height > contentRect.getHeight()) {
                            return Math.max(fs - 1, MINIMUM_FONT_SIZE);
                        }
                        fs++;
                    }
                    return Math.min(fs, DEFAULT_FONT_SIZE);
                }

                // Acrobat defaults to 12 for multiline text with size 0
                return DEFAULT_FONT_SIZE;
            } else {
                float yScalingFactor = FONTSCALE * font.getFontMatrix().getScaleY();
                float xScalingFactor = FONTSCALE * font.getFontMatrix().getScaleX();

                // fit width
                float width = font.getStringWidth(value) * font.getFontMatrix().getScaleX();
                float widthBasedFontSize = contentRect.getWidth() / width * xScalingFactor;

                // fit height
                float height = (font.getFontDescriptor().getCapHeight() +
                    -font.getFontDescriptor().getDescent()) * font.getFontMatrix().getScaleY();
                if (height <= 0) {
                    height = font.getBoundingBox().getHeight() * font.getFontMatrix().getScaleY();
                }

                float heightBasedFontSize = contentRect.getHeight() / height * yScalingFactor;
                if (Float.isInfinite(widthBasedFontSize)) {
                    // PDFBOX-5763: avoids -Infinity if empty value and tiny rectangle
                    return heightBasedFontSize;
                }

                return Math.min(heightBasedFontSize, widthBasedFontSize);
            }
        }
        return fontSize;
    }

    /*
     * Resolve the cap height.
     *
     * This is a very basic implementation using the height of "H" as reference.
     */
    private float resolveCapHeight(PDFont font) throws IOException {
        return resolveGlyphHeight(font, "H".codePointAt(0));
    }

    /*
     * Resolve the descent.
     *
     * This is a very basic implementation using the height of "y" - "a" as reference.
     */
    private float resolveDescent(PDFont font) throws IOException {
        return resolveGlyphHeight(font, "y".codePointAt(0)) - resolveGlyphHeight(font, "a".codePointAt(0));
    }

    // this calculates the real (except for type 3 fonts) individual glyph bounds
    private float resolveGlyphHeight(PDFont font, int code) throws IOException {
        GeneralPath path = null;
        if (font instanceof PDType3Font) {
            // It is difficult to calculate the real individual glyph bounds for type 3
            // fonts
            // because these are not vector fonts, the content stream could contain almost
            // anything
            // that is found in page content streams.
            PDType3Font t3Font = (PDType3Font) font;
            PDType3CharProc charProc = t3Font.getCharProc(code);
            if (charProc != null) {
                BoundingBox fontBBox = t3Font.getBoundingBox();
                PDRectangle glyphBBox = charProc.getGlyphBBox();
                if (glyphBBox != null) {
                    // PDFBOX-3850: glyph bbox could be larger than the font bbox
                    glyphBBox.setLowerLeftX(Math.max(fontBBox.getLowerLeftX(), glyphBBox.getLowerLeftX()));
                    glyphBBox.setLowerLeftY(Math.max(fontBBox.getLowerLeftY(), glyphBBox.getLowerLeftY()));
                    glyphBBox.setUpperRightX(Math.min(fontBBox.getUpperRightX(), glyphBBox.getUpperRightX()));
                    glyphBBox.setUpperRightY(Math.min(fontBBox.getUpperRightY(), glyphBBox.getUpperRightY()));
                    path = glyphBBox.toGeneralPath();
                }
            }
        } else if (font instanceof PDVectorFont) {
            PDVectorFont vectorFont = (PDVectorFont) font;
            path = vectorFont.getPath(code);
        } else if (font instanceof PDSimpleFont) {
            PDSimpleFont simpleFont = (PDSimpleFont) font;

            // these two lines do not always work, e.g. for the TT fonts in file 032431.pdf
            // which is why PDVectorFont is tried first.
            String name = simpleFont.getEncoding().getName(code);
            path = simpleFont.getPath(name);
        } else {
            // shouldn't happen, please open issue in JIRA
            LOG.warn("Unknown font class: {}", font.getClass());
        }
        if (path == null) {
            return -1;
        }
        return (float) path.getBounds2D().getHeight();
    }

    /**
     * Resolve the bounding box.
     *
     * @param fieldWidget      the annotation widget.
     * @param appearanceStream the annotations appearance stream.
     * @return the resolved boundingBox.
     */
    private PDRectangle resolveBoundingBox(PDAnnotationWidget fieldWidget,
        PDAppearanceStream appearanceStream) {
        PDRectangle boundingBox = appearanceStream.getBBox();
        if (boundingBox == null) {
            boundingBox = fieldWidget.getRectangle().createRetranslatedRectangle();
        }
        return boundingBox;
    }

    /**
     * Apply padding to a box.
     *
     * @param box box
     * @return the padded box.
     */
    private PDRectangle applyPadding(PDRectangle box, float padding) {
        return new PDRectangle(box.getLowerLeftX() + padding,
            box.getLowerLeftY() + padding,
            box.getWidth() - 2 * padding,
            box.getHeight() - 2 * padding);
    }
}


/**
 * A block of text.
 * <p>
 * A block of text can contain multiple paragraphs which will be treated individually within the block placement.
 * </p>
 */
class PlainText {

    private static final float FONTSCALE = 1000f;

    private final List<PlainText.Paragraph> paragraphs;

    /**
     * Construct the text block from a single value.
     * <p>
     * Constructs the text block from a single value splitting into individual {@link PlainText.Paragraph} when a new line character is encountered.
     *
     * @param textValue the text block string.
     */
    PlainText(String textValue) {
        if (textValue.isEmpty()) {
            paragraphs = new ArrayList<>(1);
            paragraphs.add(new PlainText.Paragraph(""));
        } else {
            String[] parts = textValue.replace('\t', ' ').split("\\R");
            paragraphs = new ArrayList<>(parts.length);
            for (String part : parts) {
                // Acrobat prints a space for an empty paragraph
                if (part.isEmpty()) {
                    part = " ";
                }
                paragraphs.add(new PlainText.Paragraph(part));
            }
        }
    }

    /**
     * Construct the text block from a list of values.
     * <p>
     * Constructs the text block from a list of values treating each entry as an individual {@link PlainText.Paragraph}.
     *
     * @param listValue the text block string.
     */
    PlainText(List<String> listValue) {
        paragraphs = new ArrayList<>(listValue.size());
        listValue.forEach(part -> paragraphs.add(new PlainText.Paragraph(part)));
    }

    /**
     * Get the list of paragraphs.
     *
     * @return the paragraphs.
     */
    List<PlainText.Paragraph> getParagraphs() {
        return paragraphs;
    }

    /**
     * Attribute keys and attribute values used for text handling.
     * <p>
     * This is similar to {@link java.awt.font.TextAttribute} but handled individually as to avoid a dependency on awt.
     */
    static class TextAttribute extends AttributedCharacterIterator.Attribute {

        /**
         * UID for serializing.
         */
        private static final long serialVersionUID = -3138885145941283005L;

        /**
         * Attribute width of the text.
         */
        public static final AttributedCharacterIterator.Attribute WIDTH = new PlainText.TextAttribute("width");

        protected TextAttribute(String name) {
            super(name);
        }


    }

    /**
     * A block of text to be formatted as a whole.
     * <p>
     * A block of text can contain multiple paragraphs which will be treated individually within the block placement.
     * </p>
     */
    static class Paragraph {

        private final String textContent;

        Paragraph(String text) {
            textContent = text;
        }

        /**
         * Get the paragraph text.
         *
         * @return the text.
         */
        String getText() {
            return textContent;
        }

        /**
         * Break the paragraph into individual lines.
         *
         * @param font     the font used for rendering the text.
         * @param fontSize the fontSize used for rendering the text.
         * @param width    the width of the box holding the content.
         * @return the individual lines.
         * @throws IOException
         */
        List<PlainText.Line> getLines(PDFont font, float fontSize, float width) throws IOException {
            BreakIterator iterator = BreakIterator.getLineInstance();
            iterator.setText(textContent);

            final float scale = fontSize / FONTSCALE;

            int start = iterator.first();
            int end = iterator.next();
            float lineWidth = 0;

            List<PlainText.Line> textLines = new ArrayList<>();
            PlainText.Line textLine = new PlainText.Line();

            while (end != BreakIterator.DONE) {
                String word = textContent.substring(start, end);
                float wordWidth = font.getStringWidth(word) * scale;

                boolean wordNeedsSplit = false;
                int splitOffset = end - start;

                lineWidth = lineWidth + wordWidth;

                // check if the last word would fit without the whitespace ending it
                if (lineWidth >= width && Character.isWhitespace(word.charAt(word.length() - 1))) {
                    float whitespaceWidth = font.getStringWidth(word.substring(word.length() - 1)) * scale;
                    lineWidth = lineWidth - whitespaceWidth;
                }

                if (lineWidth >= width && !textLine.getWords().isEmpty()) {
                    textLine.setWidth(textLine.calculateWidth(font, fontSize));
                    textLines.add(textLine);
                    textLine = new PlainText.Line();
                    lineWidth = font.getStringWidth(word) * scale;
                }

                if (wordWidth > width && textLine.getWords().isEmpty()) {
                    // single word does not fit into width
                    wordNeedsSplit = true;
                    while (true) {
                        splitOffset--;

                        String substring = word.substring(0, splitOffset);
                        float substringWidth = font.getStringWidth(substring) * scale;
                        if (substringWidth < width) {
                            word = substring;
                            wordWidth = font.getStringWidth(word) * scale;
                            lineWidth = wordWidth;
                            break;
                        }
                    }
                }

                AttributedString as = new AttributedString(word);
                as.addAttribute(PlainText.TextAttribute.WIDTH, wordWidth);
                PlainText.Word wordInstance = new PlainText.Word(word);
                wordInstance.setAttributes(as);
                textLine.addWord(wordInstance);

                if (wordNeedsSplit) {
                    start = start + splitOffset;
                } else {
                    start = end;
                    end = iterator.next();
                }
            }
            textLine.setWidth(textLine.calculateWidth(font, fontSize));
            textLines.add(textLine);
            return textLines;
        }
    }

    /**
     * An individual line of text.
     */
    static class Line {

        private final List<PlainText.Word> words = new ArrayList<>();
        private float lineWidth;

        float getWidth() {
            return lineWidth;
        }

        void setWidth(float width) {
            lineWidth = width;
        }

        float calculateWidth(PDFont font, float fontSize) throws IOException {
            final float scale = fontSize / FONTSCALE;
            float calculatedWidth = 0f;
            int indexOfWord = 0;
            for (PlainText.Word word : words) {
                calculatedWidth = calculatedWidth +
                    (Float) word.getAttributes().getIterator().getAttribute(PlainText.TextAttribute.WIDTH);
                String text = word.getText();
                if (indexOfWord == words.size() - 1 && Character.isWhitespace(text.charAt(text.length() - 1))) {
                    float whitespaceWidth = font.getStringWidth(text.substring(text.length() - 1)) * scale;
                    calculatedWidth = calculatedWidth - whitespaceWidth;
                }
                ++indexOfWord;
            }
            return calculatedWidth;
        }

        List<PlainText.Word> getWords() {
            return words;
        }

        float getInterWordSpacing(float width) {
            return (width - lineWidth) / (words.size() - 1);
        }

        void addWord(PlainText.Word word) {
            words.add(word);
        }
    }

    /**
     * An individual word.
     * <p>
     * A word is defined as a string which must be kept on the same line.
     */
    static class Word {

        private AttributedString attributedString;
        private final String textContent;

        Word(String text) {
            textContent = text;
        }

        String getText() {
            return textContent;
        }

        AttributedString getAttributes() {
            return attributedString;
        }

        void setAttributes(AttributedString as) {
            this.attributedString = as;
        }
    }
}

/**
 * Define styling attributes to be used for text formatting.
 */
class AppearanceStyle {

    private PDFont font;
    /**
     * The font size to be used for text formatting.
     * <p>
     * Defaulting to 12 to match Acrobats default.
     */
    private float fontSize = 12.0f;

    /**
     * The leading (distance between lines) to be used for text formatting.
     * <p>
     * Defaulting to 1.2*fontSize to match Acrobats default.
     */
    private float leading = 14.4f;

    /**
     * Get the font used for text formatting.
     *
     * @return the font used for text formatting.
     */
    PDFont getFont() {
        return font;
    }

    /**
     * Set the font to be used for text formatting.
     *
     * @param font the font to be used.
     */
    void setFont(PDFont font) {
        this.font = font;
    }

    /**
     * Get the fontSize used for text formatting.
     *
     * @return the fontSize used for text formatting.
     */
    float getFontSize() {
        return fontSize;
    }

    /**
     * Set the font size to be used for formatting.
     *
     * @param fontSize the font size.
     */
    void setFontSize(float fontSize) {
        this.fontSize = fontSize;
        leading = fontSize * 1.2f;
    }

    /**
     * Get the leading used for text formatting.
     *
     * @return the leading used for text formatting.
     */
    float getLeading() {
        return leading;
    }

    /**
     * Set the leading used for text formatting.
     *
     * @param leading the leading to be used.
     */
    void setLeading(float leading) {
        this.leading = leading;
    }
}

/**
 * Represents a default appearance string, as found in the /DA entry of free text annotations.
 *
 * <p>The default appearance string (DA) contains any graphics state or text state operators needed
 * to establish the graphics state parameters, such as text size and colour, for displaying the field’s variable text. Only operators that are allowed within text objects shall occur in this string.
 * <p>
 * Note: This class is not yet public, as its API is still unstable.
 */
class PDDefaultAppearanceString {

    /**
     * The default font size used by Acrobat.
     */
    private static final float DEFAULT_FONT_SIZE = 12;

    private final PDResources defaultResources;

    private COSName fontName;
    private PDFont font;
    private float fontSize = DEFAULT_FONT_SIZE;
    private PDColor fontColor;

    /**
     * Constructor for reading an existing DA string.
     *
     * @param defaultResources  DR entry
     * @param defaultAppearance DA entry
     * @throws IOException If the DA could not be parsed
     */
    PDDefaultAppearanceString(COSString defaultAppearance, PDResources defaultResources) throws IOException {
        if (defaultAppearance == null) {
            throw new IllegalArgumentException("/DA is a required entry. "
                + "Please set a default appearance first.");
        }

        if (defaultResources == null) {
            throw new IllegalArgumentException("/DR is a required entry");
        }

        this.defaultResources = defaultResources;
        processAppearanceStringOperators(defaultAppearance.getBytes());
    }

    /**
     * Processes the operators of the given content stream.
     *
     * @param content the content to parse.
     * @throws IOException if there is an error reading or parsing the content stream.
     */
    private void processAppearanceStringOperators(byte[] content) throws IOException {
        List<COSBase> arguments = new ArrayList<>();
        PDFStreamParser parser = new PDFStreamParser(content);
        Object token = parser.parseNextToken();
        while (token != null) {
            if (token instanceof Operator) {
                processOperator((Operator) token, arguments);
                arguments = new ArrayList<>();
            } else {
                arguments.add((COSBase) token);
            }
            token = parser.parseNextToken();
        }
    }

    /**
     * This is used to handle an operation.
     *
     * @param operator The operation to perform.
     * @param operands The list of arguments.
     * @throws IOException If there is an error processing the operation.
     */
    private void processOperator(Operator operator, List<COSBase> operands) throws IOException {
        switch (operator.getName()) {
            case OperatorName.SET_FONT_AND_SIZE:
                processSetFont(operands);
                break;
            case OperatorName.NON_STROKING_GRAY:
            case OperatorName.NON_STROKING_RGB:
            case OperatorName.NON_STROKING_CMYK:
                processSetFontColor(operands);
                break;
            default:
                break;
        }
    }

    /**
     * Process the set font and font size operator.
     *
     * @param operands the font name and size
     * @throws IOException in case there are missing operators or the font is not within the resources
     */
    private void processSetFont(List<COSBase> operands) throws IOException {
        if (operands.size() < 2) {
            throw new IOException("Missing operands for set font operator " + Arrays.toString(operands.toArray()));
        }

        COSBase base0 = operands.get(0);
        COSBase base1 = operands.get(1);
        if (!(base0 instanceof COSName)) {
            return;
        }
        if (!(base1 instanceof COSNumber)) {
            return;
        }
        COSName fontName = (COSName) base0;

        PDFont font = defaultResources.getFont(fontName);
        float fontSize = ((COSNumber) base1).floatValue();

        // todo: handle cases where font == null with special mapping logic (see PDFBOX-2661)
        if (font == null) {
            throw new IOException("Could not find font: /" + fontName.getName());
        }
        setFontName(fontName);
        setFont(font);
        setFontSize(fontSize);
    }

    /**
     * Process the font color operator.
     * <p>
     * This is assumed to be an RGB color.
     *
     * @param operands the color components
     * @throws IOException in case of the color components not matching
     */
    private void processSetFontColor(List<COSBase> operands) throws IOException {
        PDColorSpace colorSpace;

        switch (operands.size()) {
            case 1:
                colorSpace = PDDeviceGray.INSTANCE;
                break;
            case 3:
                colorSpace = PDDeviceRGB.INSTANCE;
                break;
            case 4:
                colorSpace = PDDeviceCMYK.INSTANCE;
                break;
            default:
                throw new IOException("Missing operands for set non stroking color operator " + Arrays.toString(operands.toArray()));
        }
        COSArray array = new COSArray();
        array.addAll(operands);
        setFontColor(new PDColor(array, colorSpace));
    }

    /**
     * Get the font name
     *
     * @return the font name to use for resource lookup
     */
    COSName getFontName() {
        return fontName;
    }

    /**
     * Set the font name.
     *
     * @param fontName the font name to use for resource lookup
     */
    void setFontName(COSName fontName) {
        this.fontName = fontName;
    }

    /**
     * Returns the font.
     */
    PDFont getFont() {
        return font;
    }

    /**
     * Set the font.
     *
     * @param font the font to use.
     */
    void setFont(PDFont font) {
        this.font = font;
    }

    /**
     * Returns the font size.
     */
    public float getFontSize() {
        return fontSize;
    }

    /**
     * Set the font size.
     *
     * @param fontSize the font size.
     */
    void setFontSize(float fontSize) {
        this.fontSize = fontSize;
    }

    /**
     * Returns the font color
     */
    PDColor getFontColor() {
        return fontColor;
    }

    /**
     * Set the font color.
     *
     * @param fontColor the fontColor to use.
     */
    void setFontColor(PDColor fontColor) {
        this.fontColor = fontColor;
    }

    /**
     * Write font name, font size and color from the /DA string to the given content stream.
     *
     * @param contents     The content stream.
     * @param zeroFontSize The calculated font size to use if the /DA string has a size 0 (autosize). Otherwise the size from the /DA string is used.
     */
    void writeTo(PDAppearanceContentStream contents, float zeroFontSize) throws IOException {
        float fontSize = getFontSize();
        if (Float.compare(fontSize, 0) == 0) {
            fontSize = zeroFontSize;
        }
        contents.setFont(getFont(), fontSize);

        if (getFontColor() != null) {
            contents.setNonStrokingColor(getFontColor());
        }
    }

    /**
     * Copies any needed resources from the document’s DR dictionary into the stream’s Resources dictionary. Resources with the same name shall be left intact.
     */
    void copyNeededResourcesTo(PDAppearanceStream appearanceStream) throws IOException {
        // make sure we have resources
        PDResources streamResources = appearanceStream.getResources();
        if (streamResources == null) {
            streamResources = new PDResources();
            appearanceStream.setResources(streamResources);
        }

        if (streamResources.getFont(fontName) == null) {
            streamResources.put(fontName, getFont());
        }

        // todo: other kinds of resource...
    }
}
