package com.xyy.saas.inquiry.signature.server.mq.message;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.xyy.saas.eventbus.rocketmq.core.EventBusAbstractMessage;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 处方执行签章Event
 *
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Builder
@Data
public class PrescriptionExecutionSignatureEvent extends EventBusAbstractMessage {

    public static final String TOPIC = "PRESCRIPTION_EXECUTION_SIGNATURE";

    private PrescriptionExecutionSignatureMessage msg;


    @JsonCreator
    public PrescriptionExecutionSignatureEvent(@JsonProperty("msg") PrescriptionExecutionSignatureMessage msg) {
        this.msg = msg;
    }


    @Override
    public String getTag() {
        return "";
    }

}
