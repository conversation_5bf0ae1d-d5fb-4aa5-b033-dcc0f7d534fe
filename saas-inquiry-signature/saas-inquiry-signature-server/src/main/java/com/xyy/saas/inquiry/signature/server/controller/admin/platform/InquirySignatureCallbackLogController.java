package com.xyy.saas.inquiry.signature.server.controller.admin.platform;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.signature.server.controller.admin.platform.vo.InquirySignatureCallbackLogPageReqVO;
import com.xyy.saas.inquiry.signature.server.controller.admin.platform.vo.InquirySignatureCallbackLogRespVO;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.platform.InquirySignatureCallbackLogDO;
import com.xyy.saas.inquiry.signature.server.service.platform.InquirySignatureCallbackLogService;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 签章回调日志")
@RestController
@RequestMapping("/signature/inquiry-signature-callback-log")
@Validated
public class InquirySignatureCallbackLogController {

    @Resource
    private InquirySignatureCallbackLogService inquirySignatureCallbackLogService;

    @GetMapping("/get")
    @Parameter(name = "id", description = "编号", required = true, example = "1024")
    @PreAuthorize("@ss.hasPermission('signature:inquiry-signature-callback-log:query')")
    public CommonResult<InquirySignatureCallbackLogRespVO> getInquirySignatureCallbackLog(@RequestParam("id") Long id) {
        InquirySignatureCallbackLogDO inquirySignatureCallbackLog = inquirySignatureCallbackLogService.getInquirySignatureCallbackLog(id);
        return success(BeanUtils.toBean(inquirySignatureCallbackLog, InquirySignatureCallbackLogRespVO.class));
    }

    @GetMapping("/page")
    @PreAuthorize("@ss.hasPermission('signature:inquiry-signature-callback-log:query')")
    public CommonResult<PageResult<InquirySignatureCallbackLogRespVO>> getInquirySignatureCallbackLogPage(@Valid InquirySignatureCallbackLogPageReqVO pageReqVO) {
        PageResult<InquirySignatureCallbackLogDO> pageResult = inquirySignatureCallbackLogService.getInquirySignatureCallbackLogPage(pageReqVO);
        return success(BeanUtils.toBean(pageResult, InquirySignatureCallbackLogRespVO.class));
    }

}