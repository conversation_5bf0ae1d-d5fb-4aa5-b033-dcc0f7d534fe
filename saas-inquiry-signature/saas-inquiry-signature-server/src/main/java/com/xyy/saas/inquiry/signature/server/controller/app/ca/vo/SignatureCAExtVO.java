package com.xyy.saas.inquiry.signature.server.controller.app.ca.vo;

import com.xyy.saas.inquiry.enums.signature.FddCaConstantEnum.AuthorizeFreeSignStatus;
import com.xyy.saas.inquiry.enums.signature.FddCaConstantEnum.CertifyStatusEnum;
import com.xyy.saas.inquiry.enums.signature.SignatureAppConfigIdEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import java.time.LocalDateTime;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * @Author:chenxiaoyi
 * @Date:2025/02/06 14:54
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Accessors(chain = true)
@Schema(description = "签章CA扩展信息extVO")
public class SignatureCAExtVO {

    /**
     * 签章平台配置id
     */
    private Integer signaturePlatformConfigId;
    /**
     * 签章平台配置Name
     */
    private String signaturePlatformConfigName;

    /**
     * 应用认证状态 - 展示
     */
    private Integer status;

    /**
     * 实名认证状态 0: 待认证，1: 认证完成，2: 认证失败
     */
    private Integer certifyStatus = CertifyStatusEnum.NO_FINISH.getCode();
    /**
     * 免签授权状态 0: 未授权，1: 已授权，
     */
    private Integer authorizeFreeSignStatus = AuthorizeFreeSignStatus.UNAUTHORIZED.getCode();
    /**
     * 免签授权截止时间
     */
    private LocalDateTime authorizeFreeSignDdl;

    /**
     * 免验证签署授权是否临期
     */
    private boolean authorizeFreeSignExpiring;

    /**
     * 任意未完成-则-未认证
     *
     * @return
     */
    public Integer getStatus() {
        if (!Objects.equals(certifyStatus, CertifyStatusEnum.FINISH.getCode()) || !Objects.equals(authorizeFreeSignStatus, AuthorizeFreeSignStatus.AUTHORIZED.getCode())) {
            return CertifyStatusEnum.NO_FINISH.getCode();
        }
        return CertifyStatusEnum.FINISH.getCode();
    }

    public String getSignaturePlatformConfigName() {
        return SignatureAppConfigIdEnum.caNameForCode(signaturePlatformConfigId);
    }


    public static SignatureCAExtVO initCa(SignatureAppConfigIdEnum authConfigId) {
        return SignatureCAExtVO.builder().signaturePlatformConfigId(authConfigId.getCode()).signaturePlatformConfigName(authConfigId.getCaName())
            .certifyStatus(CertifyStatusEnum.NO_FINISH.getCode()).authorizeFreeSignStatus(AuthorizeFreeSignStatus.UNAUTHORIZED.getCode()).build();
    }
}
