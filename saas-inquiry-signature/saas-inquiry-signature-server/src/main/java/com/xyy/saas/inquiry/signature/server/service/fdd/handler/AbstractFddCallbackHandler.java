package com.xyy.saas.inquiry.signature.server.service.fdd.handler;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.xyy.saas.inquiry.enums.signature.SignaturePlatformEnum;
import com.xyy.saas.inquiry.signature.server.dal.dataobject.platform.InquirySignatureCallbackLogDO;
import com.xyy.saas.inquiry.signature.server.service.fdd.enums.FddCallbackEvent;
import com.xyy.saas.inquiry.signature.server.service.platform.InquirySignatureCallbackLogService;
import jakarta.annotation.Resource;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import org.apache.commons.lang3.StringUtils;

/**
 * 法大大 回调处理器 - 记录日志
 *
 * @Author:chenxiaoyi
 * @Date:2024/02/29 10:48
 */
public abstract class AbstractFddCallbackHandler implements FddCallbackHandler {

    @Resource
    protected InquirySignatureCallbackLogService inquirySignatureCallbackLogService;

    /**
     * 默认处理是否记录log
     */
//    @Value(value = "${fdd.callback.default.log:false}")
    private boolean defaultLog = true;

    /**
     * 业务ID 字段
     */
    private static final List<String> BIZ_ID_KEYS = Arrays.asList("clientUserId", "signTaskId", "downloadId", "reportDownloadId", "templateId", "deptId", "memberId", "entityId", "sealId", "verifyId", "clientCorpId", "openId", "userIdentNo",
        "contractConsultId", "archivesId");

    public void insertLogAsync(FddCallbackEvent eventEnum, String bizId, String bizContent) {
        if (eventEnum == null) {
            return;
        }
        // 异步记录log
        CompletableFuture.runAsync(() -> {
            if (bizId == null && !defaultLog) {
                return;
            }
            String bizIdFinal = bizId != null ? bizId : getBizId(JSON.parseObject(bizContent, new TypeReference<>() {
            }));
            // 记录相关回调日志
            InquirySignatureCallbackLogDO callbackFddLog = InquirySignatureCallbackLogDO.builder()
                .signaturePlatform(SignaturePlatformEnum.FDD.getCode())
                .type(eventEnum.eventCode)
                .bizId(bizIdFinal)
                .bizContent(bizContent)
                .build();
            inquirySignatureCallbackLogService.createInquirySignatureCallbackLog(callbackFddLog);
        });
    }

    private String getBizId(Map<String, Object> bizContentMap) {
        if (bizContentMap == null || bizContentMap.isEmpty()) {
            return "";
        }
        for (String bizIdKey : BIZ_ID_KEYS) {
            Object o = bizContentMap.get(bizIdKey);
            if (o != null && StringUtils.isNotBlank(o.toString())) {
                return o.toString();
            }
        }
        return "";
    }

}
