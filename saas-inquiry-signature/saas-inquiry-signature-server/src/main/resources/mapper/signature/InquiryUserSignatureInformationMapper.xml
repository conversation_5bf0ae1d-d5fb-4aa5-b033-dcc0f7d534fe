<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.signature.server.dal.mysql.signature.InquiryUserSignatureInformationMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->


  <select id="pageInquiryUserSignature"
    resultType="com.xyy.saas.inquiry.signature.server.controller.app.signature.vo.InquiryUserSignatureManageVO">
    select a.id,a.signature_url,b.status,b.id as tenantUserRelationId,c.nickname,c.mobile,c.sex
    from saas_inquiry_user_signature_information a
    left join system_tenant_user_relation b on a.user_id = b.user_id
    left join system_users c on a.user_id = c.id
    <where>
      a.deleted = false and b.deleted = false and c.deleted = false
      <if test="informationDO.signaturePlatform != null ">
        AND signature_platform = #{informationDO.signaturePlatform}
      </if>
      <if test="informationDO.signatureBizType != null ">
        AND signature_biz_type = #{informationDO.signatureBizType}
      </if>

    </where>
  </select>

  <delete id="deleteByUserIdBizType">
    delete from saas_inquiry_user_signature_information where user_id = #{userId}
    <if test="signaturePlatform != null ">
      AND signature_platform = #{signaturePlatform}
    </if>
    <if test="signatureBizType != null ">
      AND signature_biz_type = #{signatureBizType}
    </if>
  </delete>

</mapper>