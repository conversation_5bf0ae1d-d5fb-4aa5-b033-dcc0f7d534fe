// package com.xyy.saas.inquiry.kernel.signature.fdd;
//
// import com.xyy.saas.inquiry.kernel.SaasInquiryKernelAllApplication;
// import com.xyy.saas.inquiry.signature.server.service.fdd.FddCallbackService;
// import jakarta.annotation.Resource;
// import org.junit.jupiter.api.Test;
// import org.springframework.boot.test.context.SpringBootTest;
// import org.springframework.test.context.ActiveProfiles;
//
// /**
//  * @Author:chenxiaoyi
//  * @Date:2024/12/13 10:19
//  */
// @SpringBootTest(classes = SaasInquiryKernelAllApplication.class)
// @ActiveProfiles("dev")
// public class FddCallbackTest {
//
//     @Resource
//     private FddCallbackService fddCallbackService;
//
//
//     /**
//      * 处方回调
//      */
//     @Test
//     public void eventCallback_success() {
//         fddCallbackService.eventCallback("sign-task-signed",
//             "{\"actorId\": \"doctorSign\", \"userName\": \"陈笑医\", \"eventTime\": \"1734055954000\", \"openUserId\": \"883c6cceebf746ceb41b429e1a83e829\", \"signTaskId\": \"1734055953753178357\", "
//                 + "\"signTaskStatus\": \"sign_completed\", \"verifyFret\": \"陈医生问诊处方\", \"transReferenceId\": \"CF015\"}");
//     }
//
//
// }
