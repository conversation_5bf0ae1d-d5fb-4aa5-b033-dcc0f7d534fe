package com.xyy.saas.inquiry.signature;

import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.datasource.config.YudaoDataSourceAutoConfiguration;
import cn.iocoder.yudao.framework.mybatis.config.YudaoMybatisAutoConfiguration;
import cn.iocoder.yudao.framework.test.config.SqlInitializationTestConfiguration;
import com.alibaba.druid.spring.boot3.autoconfigure.DruidDataSourceAutoConfigure;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration;
import com.baomidou.mybatisplus.autoconfigure.MybatisPlusAutoConfiguration;
import com.github.yulichang.autoconfigure.MybatisPlusJoinAutoConfiguration;
import com.xyy.saas.inquiry.test.MockerFactory;
import org.apache.dubbo.spring.boot.autoconfigure.DubboAutoConfiguration;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.jdbc.DataSourceAutoConfiguration;
import org.springframework.boot.autoconfigure.jdbc.DataSourceTransactionManagerAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.annotation.Rollback;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.transaction.annotation.Transactional;

/**
 * desc
 *
 * <AUTHOR>
 * @version 1.0
 * @since 1.0
 */
//@SpringBootTest(classes = InquiryUserServerApplication.class, webEnvironment = SpringBootTest.WebEnvironment.NONE)
@ActiveProfiles("dev")
@SpringBootTest(classes = BaseIntegrationTest.Application.class, webEnvironment = SpringBootTest.WebEnvironment.NONE, properties = {
    "spring.config.location=classpath:/application-dev.yml,classpath:/application.yml" // 同时加载两个配置文件
})
//@MapperScan(basePackages = {"cn.iocoder.yudao.module", "com.xyy.saas.inquiry"})
// 测试完成后事务统一回滚
@Rollback
@Transactional(rollbackFor = Throwable.class)
public abstract class BaseIntegrationTest {

    protected static final Logger log = LoggerFactory.getLogger(BaseIntegrationTest.class);

    @Import({
        // DB 配置类
        DynamicDataSourceAutoConfiguration.class, // MybatisPlus 动态数据源配置类
        YudaoDataSourceAutoConfiguration.class, // 自己的 DB 配置类
        DataSourceAutoConfiguration.class, // Spring DB 自动配置类
        DataSourceTransactionManagerAutoConfiguration.class, // Spring 事务自动配置类
        DruidDataSourceAutoConfigure.class, // Druid 自动配置类
        // MyBatis 配置类
        YudaoMybatisAutoConfiguration.class, // 自己的 MyBatis 配置类
        MybatisPlusAutoConfiguration.class, // MyBatis 的自动配置类
        MybatisPlusJoinAutoConfiguration.class, // MyBatis 的Join配置类

        // 其它配置类
        SpringUtil.class,

        // dubbo api 配置类
        DubboAutoConfiguration.class,
        // DrugstoreConfig.class
        // mock 配置类
        MockerFactory.class,


    })
    public static class Application {

    }


}
