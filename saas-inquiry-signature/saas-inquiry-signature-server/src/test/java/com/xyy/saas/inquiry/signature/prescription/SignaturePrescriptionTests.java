// package com.xyy.saas.inquiry.kernel.signature.prescription;
//
// import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
// import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
// import com.xyy.saas.inquiry.hospital.server.service.prescription.PrescriptionServiceImpl;
// import com.xyy.saas.inquiry.kernel.SaasInquiryKernelAllApplication;
// import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDetailDto;
// import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDto;
// import com.xyy.saas.inquiry.pojo.prescription.ParticipantItem;
// import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionParamDto;
// import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureAuditDto;
// import com.xyy.saas.inquiry.signature.api.prescription.dto.PrescriptionSignatureInitDto;
// import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionSignatureEvent;
// import com.xyy.saas.inquiry.signature.server.mq.message.PrescriptionSignatureMessage;
// import com.xyy.saas.inquiry.signature.server.service.pdf.MockDataPdfService;
// import com.xyy.saas.inquiry.signature.server.service.pdf.PdfService;
// import com.xyy.saas.inquiry.signature.server.service.prescription.InquirySignaturePrescriptionService;
// import com.xyy.saas.inquiry.util.TimeWatchUtil;
// import jakarta.annotation.Resource;
// import java.math.BigDecimal;
// import java.util.List;
// import java.util.stream.Stream;
// import org.junit.jupiter.api.BeforeEach;
// import org.junit.jupiter.api.Test;
// import org.slf4j.Logger;
// import org.slf4j.LoggerFactory;
// import org.springframework.beans.factory.annotation.Autowired;
// import org.springframework.boot.test.context.SpringBootTest;
// import org.springframework.mock.web.MockHttpServletRequest;
// import org.springframework.test.context.ActiveProfiles;
// import org.springframework.web.context.request.RequestAttributes;
// import org.springframework.web.context.request.RequestContextHolder;
// import org.springframework.web.context.request.ServletRequestAttributes;
//
// /**
//  * 单元测试扫描当前包路径,相关api测试写到这个层级下或者建立相关包路径 com.xyy.saas.inquiry.drugstore.server
//  */
// @SpringBootTest(classes = SaasInquiryKernelAllApplication.class)
// @ActiveProfiles("dev")
// class SignaturePrescriptionTests {
//
//     private static final Logger log = LoggerFactory.getLogger(SignaturePrescriptionTests.class);
//     @Autowired
//     private PrescriptionServiceImpl prescriptionServiceImpl;
//
//     @BeforeEach
//     public void before() {
//         RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
//         requestAttributes.setAttribute("login_user_id", 0L, RequestAttributes.SCOPE_REQUEST);
//         RequestContextHolder.setRequestAttributes(requestAttributes);
//         TenantContextHolder.setTenantId(1856621116684963842L); // cxy
//     }
//
//     @Resource
//     private InquirySignaturePrescriptionService inquirySignaturePrescriptionService;
//     @Resource
//     private PdfService pdfboxService;
//     @Resource
//     private MockDataPdfService mockDataPdfService;
//
//     // @Autowired
//     // private SyncSignaturePlatformMQConsumer syncSignaturePlatformMQConsumer;
//
//
//     public static List<InquiryProductDetailDto> getProductList() {
//         InquiryProductDetailDto detailDto1 = new InquiryProductDetailDto().setDirections("用药方法:口服").setSingleDose("单次剂量:3").setSingleUnit("单次剂量单位:片").setUseFrequency("使用频次:一日三次");
//         detailDto1.setCommonName("通用名").setQuantity(BigDecimal.TEN).setAttributeSpecification("规格:9mg*9片*2板").setUnitName("单位:盒");
//
//         InquiryProductDetailDto detailDto2 = new InquiryProductDetailDto().setDirections("用药方法:口服").setSingleDose("单次剂量:3").setSingleUnit("单次剂量单位:片").setUseFrequency("使用频次:一日三次");
//         detailDto2.setCommonName("通用名1").setQuantity(BigDecimal.TEN).setAttributeSpecification("规格:9mg*9片*2板").setUnitName("单位:盒");
//         return Stream.of(detailDto1, detailDto2).toList();
//     }
//
//     static String pref = "CF023";
//
//     /**
//      * 医生签发处方
//      */
//     @Test
//     public void issuePrescription_success() {
//         long l = System.currentTimeMillis();
//         PrescriptionSignatureInitDto initDto = PrescriptionSignatureInitDto.builder()
//             .prescriptionPref(pref)
//             .templateId(1864926509328166913L)
//             .participantItem(ParticipantItem.builder().userId(1867108208615505922L).name("陈医生").mobile("15926350017").build())
//             .param(PrescriptionParamDto.builder().no(pref).date("2024-12-11").name("测试患者")
//                 .diagnosis("这里是诊断|AC0.M1").medicineType(MedicineTypeEnum.ASIAN_MEDICINE.getCode())
//                 .inquiryProductDto(InquiryProductDto.builder().inquiryProductInfos(getProductList()).build()).build())
//             .build();
//         TimeWatchUtil.excute(() -> inquirySignaturePrescriptionService.issuePrescription(initDto), "issuePrescription");
//         System.out.println("11111111111111111111111111111111111" + (System.currentTimeMillis() - l));
//     }
//
//     /**
//      * 药师审核处方
//      */
//     @Test
//     public void auditPrescription_success() {
//         final PrescriptionSignatureAuditDto initDto = PrescriptionSignatureAuditDto.builder()
//             .prescriptionPref(pref)
//             .participantItem(ParticipantItem.builder().userId(1856634153722658818L).name("陈笑药").mobile("15926350002").build())
//             .build();
//         TimeWatchUtil.excute(() -> inquirySignaturePrescriptionService.auditPrescription(initDto), "auditPrescription");
//     }
//
//     /**
//      * 处方回调
//      */
//     @Test
//     public void signaturePrescriptionCallback_success() {
//         PrescriptionSignatureMessage initDto = PrescriptionSignatureMessage.builder()
//             .contractId(1867033381394173954L)
//             .participantItem(ParticipantItem.builder().userId(1867108208615505922L).name("陈医生").mobile("15926350017").actorField("doctorSign").build())
//             .pdfUrl("http://files.test.ybm100.com/INVT/Lzinq/20241212/pdf_6852861579457732907.pdf")
//             .build();
//         TimeWatchUtil.excute(() -> inquirySignaturePrescriptionService.signaturePrescriptionCallback(initDto), "signaturePrescriptionCallback");
//     }
//
//
//     /**
//      * 处方同步签章平台
//      */
//     @Test
//     public void syncSignaturePlatformMQConsumer_success() {
//         PrescriptionSignatureEvent build = PrescriptionSignatureEvent.builder().msg(PrescriptionSignatureMessage.builder()
//             .contractId(1868488154404245505L)
//             .participantItem(ParticipantItem.builder().userId(1867108208615505922L).name("陈医生").mobile("15926350017").build())
//             .build()).build();
//         // syncSignaturePlatformMQConsumer.syncSignaturePlatformMQConsumer(build);
//     }
//
//     /**
//      * 重新生成处方笺
//      */
//     @Test
//     public void drawnPrescriptionSelf_success() {
//
//         inquirySignaturePrescriptionService.drawnPrescriptionSelf(1873598598253088769L, true);
//     }
//
//
// }
