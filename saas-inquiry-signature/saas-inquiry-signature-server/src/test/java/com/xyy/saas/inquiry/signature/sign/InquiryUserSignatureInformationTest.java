// package com.xyy.saas.inquiry.kernel.signature.sign;
//
// import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
// import cn.iocoder.yudao.module.system.enums.common.SexEnum;
// import com.xyy.saas.inquiry.kernel.SaasInquiryKernelAllApplication;
// import com.xyy.saas.inquiry.signature.server.controller.app.signature.vo.InquiryUserSignatureManageVO;
// import com.xyy.saas.inquiry.signature.server.service.signature.InquiryUserSignatureInformationService;
// import jakarta.annotation.Resource;
// import org.junit.jupiter.api.BeforeEach;
// import org.junit.jupiter.api.Test;
// import org.springframework.boot.test.context.SpringBootTest;
// import org.springframework.mock.web.MockHttpServletRequest;
// import org.springframework.test.context.ActiveProfiles;
// import org.springframework.web.context.request.RequestAttributes;
// import org.springframework.web.context.request.RequestContextHolder;
// import org.springframework.web.context.request.ServletRequestAttributes;
//
// import java.util.Collections;
//
//
// @SpringBootTest(classes = SaasInquiryKernelAllApplication.class)
// @ActiveProfiles("test")
// public class InquiryUserSignatureInformationTest {
//
//     @Resource
//     private InquiryUserSignatureInformationService inquiryUserSignatureInformationService;
//
//
//     String imgBase64Str = "data:image/jpeg;base64,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";
//
//
//     @BeforeEach
//     public void before() {
//         RequestAttributes requestAttributes = new ServletRequestAttributes(new MockHttpServletRequest());
//         requestAttributes.setAttribute("login_user_id", 1848323075021201410L, RequestAttributes.SCOPE_REQUEST);
//         RequestContextHolder.setRequestAttributes(requestAttributes);
//         TenantContextHolder.setTenantId(1848323064560607233L);
//     }
//
//
//     /**
//      * 创建用户签名
//      */
//     @Test
//     public void createInquiryUserSignature_success() {
//         Long checkRoleId = 1852166482374033409L; // 核对
//         Long allocationRoleId = 1852166482386616322L; // 调配
//
//         InquiryUserSignatureManageVO signatureManageVO = new InquiryUserSignatureManageVO().setNickname("陈一").setMobile("15926350001").setSex(SexEnum.MALE.getSex())
//             .setRoleIds(Collections.singleton(allocationRoleId)).setSignatureImgBase64(imgBase64Str);
//
//         Long id = inquiryUserSignatureInformationService.createInquiryUserSignature(signatureManageVO);
//
//         System.out.println(id);
//
//     }
//
//
//     /**
//      * 创建用户签名
//      */
//     @Test
//     public void getInquiryUserSignature_success() {
//
//     }
//
//
// }