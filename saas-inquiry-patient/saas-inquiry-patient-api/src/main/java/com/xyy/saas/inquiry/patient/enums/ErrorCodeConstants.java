package com.xyy.saas.inquiry.patient.enums;

import cn.iocoder.yudao.framework.common.exception.ErrorCode;

/**
 * 使用 2_004_000_000 段
 */
public interface ErrorCodeConstants {


    ErrorCode INQUIRY_RECORD_NOT_EXISTS = new ErrorCode(2_004_000_000, "问诊记录不存在");

    ErrorCode INQUIRY_RECORD_ID_CARD_FAIL = new ErrorCode(2_004_000_001, "身份证格式有误");

    ErrorCode INQUIRY_NOT_MATCH_HOSPITAL = new ErrorCode(2_004_000_002, "未匹配到可接诊医院");

    ErrorCode INQUIRY_CANCEL_FAIL_STATUS_NOT_QUEUEING = new ErrorCode(2_004_000_003, "问诊非排队中状态，无法取消");

    ErrorCode INQUIRY_QUEUEING_NOT_EXISTS = new ErrorCode(2_004_000_004, "未查询到问诊排队信息");

    ErrorCode INQUIRY_NOT_MATCH_DOCTOR = new ErrorCode(2_004_000_005, "当前暂无医生接诊，请稍后尝试重新发起问诊");

    ErrorCode INQUIRY_PREF_EMPTY_ERROR = new ErrorCode(2_004_000_006, "问诊单号不能为空");

    ErrorCode INQUIRY_PATIENT_INFO_NOT_EXISTS = new ErrorCode(2_004_000_007, "患者信息不存在");

    ErrorCode INQUIRY_RECORD_AGE_RANGE_FAIL = new ErrorCode(2_004_000_008, "患者年龄不在互联网线上问诊范围内,建议下线就医");

    ErrorCode INQUIRY_RECORD_PREGNANCY_FAIL = new ErrorCode(2_004_000_009, "妊娠哺乳期患者不在互联网线上问诊范围内,建议下线就医");

    // 就诊登记************
    ErrorCode MEDICAL_REGISTRATION_NOT_EXISTS = new ErrorCode(2_004_000_010, "医疗就诊登记(挂号)不存在");

    ErrorCode INQUIRY_PATIENT_QUERY_STRATEGY_NOT_EXISTS = new ErrorCode(2_004_000_011, "患者查询策略获取失败");

    ErrorCode THIRD_PARTY_REPETITION_INQUIRY = new ErrorCode(2_004_000_012, "此三方信息已发起问诊，不允许重复问诊");

    ErrorCode THIRD_PARTY_PRE_INQUIRY_IS_DELETE = new ErrorCode(2_004_000_013, "此三方信息已被删除，请前往业务侧重新推送");

    ErrorCode THIRD_PARTY_PRE_INQUIRY_NOT_EXISTS = new ErrorCode(2_004_000_014, "根据预问诊单号查询预问诊信息失败");

    ErrorCode INQUIRY_NOT_MATCH_DEPT = new ErrorCode(2_004_000_015, "问诊未匹配到可接诊科室");

    // 当前预问诊单已审核。无需处理
    ErrorCode THIRD_PARTY_PRE_INQUIRY_AUDIT_HAS_AUDIT = new ErrorCode(2_004_000_016, "当前预问诊单已审核,无需处理");

    // 预问诊单保存失败，失败原因
    ErrorCode THIRD_PARTY_PRE_INQUIRY_SAVE_FAIL = new ErrorCode(2_004_000_017, "预问诊单保存失败,失败原因：{}");

    ErrorCode PRODUCT_INCLUDE_SPECIAL_SCOPE = new ErrorCode(2_004_000_018, "预购药品中，存在毒麻精放类复方制剂，请填写身份证信息");

    ErrorCode TENANT_TYPE_CANNOT_INQUIRY_FAIL = new ErrorCode(2_004_000_019, "当前机构为连锁总部，不支持发起问诊");

    // 远程审方
    ErrorCode INQUIRY_REMOTE_AUDIT_NO_QUALIFICATION = new ErrorCode(2_004_001_001, "当前门店无远程审方资质,请检查门店或总部配置");

    // 分布式锁相关
    ErrorCode INQUIRY_PATIENT_LOCK_ACQUIRE_FAIL = new ErrorCode(2_004_000_020, "当前患者问诊信息已提交,请稍后重试");

    // 分布式锁相关
    ErrorCode INQUIRY_PATIENT_SAVE_FAIL = new ErrorCode(2_004_000_021, "保存患者信息失败,请稍后重试");

}
