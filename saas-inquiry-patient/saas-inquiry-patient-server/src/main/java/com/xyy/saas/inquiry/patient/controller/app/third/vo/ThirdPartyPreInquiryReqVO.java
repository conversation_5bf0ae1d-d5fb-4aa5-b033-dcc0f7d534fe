package com.xyy.saas.inquiry.patient.controller.app.third.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.xyy.saas.inquiry.annotation.JsonTypeHandler;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.DrugstoreInquiryReqVO;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 三方预问诊对象
 *
 * <AUTHOR>
 * @Date 6/11/24 11:02 AM
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "app侧 - 三方预问诊处方信息")
public class ThirdPartyPreInquiryReqVO {

    @Schema(description = "用户姓名", example = "张三")
    @NotEmpty(message = "用户姓名不能为空")
    @Size(max = 20, message = "用户姓名不能超过20个字符")
    private String userName;

    @Schema(description = "手机号", example = "13000000000")
    @Size(max = 11, message = "手机号不能超过11个字符")
    private String telephone;

    @Schema(description = "身份证", example = "******************")
    @Size(max = 18, message = "身份证号不能超过18个字符")
    private String idCard;

    @Schema(description = "年龄", example = "25")
    private Integer age;

    @Schema(description = "性别 1 - 男 , 2 - 女", example = "1")
    private Integer sex;

    @Schema(description = "药品类型 0 - 中药 , 1 - 西药", example = "1")
    @NotNull(message = "药品类型不能为空")
    private Integer medicineType;

    @Schema(description = "租户ID-小程序问诊需要", example = "1")
    private Long tenantId;

    @Schema(description = "租户名称-小程序问诊需要", example = "XXX药店")
    private String tenantName;

    /**
     * 问诊方式 {@link InquiryWayTypeEnum}
     */
    @Schema(description = "问诊方式 1 - 图文 , 2 - 视频 ,  3-电话", example = "1")
    private Integer inquiryWayType;

    /**
     * 业务渠道来源 {@link com.xyy.saas.inquiry.enums.inquiry.BizChannelTypeEnum}
     */
    @Schema(description = "业务渠道来源", example = "1")
    private Integer bizChannelType;

    /**
     * 问诊请求参数
     */
    private DrugstoreInquiryReqVO ext;

    @Schema(description = "药品详情")
    @NotNull(message = "药品详情不能为空")
    @Valid
    private List<ThirdPartyPreInquiryDetailReqDto> drugList;

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Schema(description = "药品详情")
    public static class ThirdPartyPreInquiryDetailReqDto {

        @Schema(description = "药品名称", example = "阿莫西林分散片")
        @NotEmpty(message = "药品名称不能为空")
        @Size(max = 255, message = "药品名称不能超过255个字符")
        private String drugName;

        @Schema(description = "单位")
        @Size(max = 255, message = "单位名称不能超过255个字符")
        private String productUnit;

        @Schema(description = "规格")
        @Size(max = 255, message = "规格名称不能超过255个字符")
        private String attributeSpecification;

        @Schema(description = "数量")
        @NotNull(message = "数量不能为空")
        private BigDecimal quantity;

        @Schema(description = "69码")
        @Size(max = 255, message = "药品名称不能超过255个字符")
        private String barCode;

        @Schema(description = "厂商")
        @Size(max = 255, message = "厂商名称不能超过255个字符")
        private String manufacturer;

        @Schema(description = "批准文号")
        @Size(max = 255, message = "批准文号名称不能超过255个字符")
        private String medicinesQuasiName;
    }

}
