package com.xyy.saas.inquiry.patient.util;

import cn.iocoder.yudao.module.system.enums.common.SexEnum;
import org.apache.commons.lang3.StringUtils;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;

/**
 * @ClassName：BusinessUtil
 * @Author: xucao
 * @Date: 2024/10/25 16:49
 * @Description: 业务工具类
 */
public class BusinessUtil {

    /**
     * 根据身份证号码获取生日
     *
     * @param idCard
     * @return
     */
    public static LocalDateTime getBirthdayByIdCard(String idCard) {
        if (StringUtils.isBlank(idCard)) {
            return null;
        }
        // 检查身份证号码长度是否为18位
        if (idCard.length() != 18) {
            return null;
        }
        // 提取出生日期部分（第7位到第14位）
        String birthDateStr = idCard.substring(6, 14);
        // 解析出生日期字符串
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        LocalDate birthDate = LocalDate.parse(birthDateStr, formatter);
        // 转换为LocalDateTime
        return LocalDateTime.of(birthDate, java.time.LocalTime.MIDNIGHT);
    }

    /**
     * 根据身份证号实时计算当前年龄,如果当前时间和身份证生日相差剩余部分超过1天则年龄+1
     */
    public static String getAgeByIdCard(String idCard) {
        LocalDateTime birthday = getBirthdayByIdCard(idCard);
        if (birthday == null) {
            return null;
        }
        LocalDateTime now = LocalDateTime.now();
        int age = now.getYear() - birthday.getYear();
        if (now.getMonthValue() < birthday.getMonthValue()
            || (now.getMonthValue() == birthday.getMonthValue() && now.getDayOfMonth() < birthday.getDayOfMonth())) {
            age--;
        }
        return String.valueOf(age);
    }


    /**
     * 获取days天前的开始时间到今天结束的结束时间
     *
     * @param days
     * @return
     */
    public static LocalDateTime[] getBeforeDays(int days) {
        LocalDate now = LocalDate.now();
        LocalDateTime start = now.minusDays(days).atTime(LocalTime.MIN);
        ;
        LocalDateTime end = now.atTime(LocalTime.MAX);
        return new LocalDateTime[]{start, end};
    }

    /**
     * 根据身份证号码获取性别
     *
     * @param idCard 身份证号码
     * @return "男" 或 "女"，如果身份证无效则返回 null
     */
    public static Integer getSexByIdCard(String idCard) {
        if (StringUtils.isBlank(idCard) || idCard.length() != 18) {
            return null;
        }
        // 获取第17位
        char genderChar = idCard.charAt(16);
        if (genderChar < '0' || genderChar > '9') {
            return null;
        }
        int genderCode = Character.getNumericValue(genderChar);
        return (genderCode % 2) == 1 ? SexEnum.MALE.getSex() : SexEnum.FEMALE.getSex();
    }
}
