package com.xyy.saas.inquiry.patient.service.inquiry;

import java.util.*;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryComplainSaveReqVO;
import jakarta.validation.*;


/**
 * 问诊投诉记录 Service 接口
 *
 * <AUTHOR>
 */
public interface InquiryComplainService {

    /**
     * 创建问诊投诉记录
     *
     * @param createReqVO 创建信息
     * @return 编号
     */
    Long createInquiryComplain(@Valid InquiryComplainSaveReqVO createReqVO);
}