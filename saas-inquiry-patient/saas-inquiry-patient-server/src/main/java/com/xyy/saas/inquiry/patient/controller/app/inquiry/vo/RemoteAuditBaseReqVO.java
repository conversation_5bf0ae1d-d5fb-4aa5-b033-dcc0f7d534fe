package com.xyy.saas.inquiry.patient.controller.app.inquiry.vo;

import cn.iocoder.yudao.framework.common.validation.Mobile;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryDetailExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import lombok.Data;
import org.checkerframework.checker.units.qual.N;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author: xucao
 * @DateTime: 2025/4/22 11:06
 * @Description: 远程审方请求参数
 **/
@Schema(description = "远程问诊表单信息 Request VO")
@Data
public class RemoteAuditBaseReqVO {

    @Schema(description = "患者信息")
    @NotNull(message = "患者信息不能为空")
    @Valid
    private RemotePatientVO patient;

    @Schema(description = "三方处方单号")
    @NotBlank(message = "请输入处方号")
    @Size(max = 64, message = "处方号不能超过64个字符")
    private String thirdPrescriptionNo;

    @Schema(description = "开方医院")
    @Size(max = 64, message = "开方医院不能超过64个字符")
    private String hospitalName;

    @Schema(description = "开方时间")
    @NotNull(message = "开方时间不能为空")
    private LocalDateTime outPrescriptionTime;

    @Schema(description = "医生姓名")
    @Size(max = 32, message = "医生姓名不能超过32个字符")
    private String doctorName;

    @Schema(description = "购药类型", example = "0")
    @NotNull(message = "购药类型不能为空")
    private Integer medicineType;

    @Schema(description = "诊断信息")
    @Valid
    private List<DiagnosisItemVO> diagnosis;

    @Schema(description = "远程审方问诊处方图片", example = "https://saas.file.xxxx/222.jpg")
    @NotEmpty(message = "请上传处方图片")
    private String prescriptionImg;

    @Schema(description = "上传的线下就诊处方或病历文件路径", example = "https://saas.file.xxxx/222.jpg")
    private List<String> offlinePrescriptions;


    @Schema(description = "远程问诊患者信息 Request VO")
    @Data
    public static class RemotePatientVO implements Serializable{

        @Schema(description = "患者编码")
        private String patientPref;

        @Schema(description = "患者姓名", example = "张三")
        @NotBlank(message = "请输入用药人姓名")
        @Size(max = 20, message = "患者姓名不能超过20个字符")
        private String patientName;

        @Schema(description = "患者手机号", example = "13800138000")
        @Mobile(message = "用药人手机格式不正确")
        private String patientMobile;

        @Schema(description = "患者身份证号(非必填)", example = "110101199001011234")
        @Size(max = 32, message = "身份证号码超长，请检查")
        private String patientIdCard;

        @Schema(description = "患者年龄", example = "30")
        @Size(max = 4, message = "年龄输入有误,请检查")
        private String patientAge;

        @Schema(description = "患者性别", example = "1 男 2 女")
        @NotNull(message = "患者性别不能为空")
        private Integer patientSex;
    }
}
