package com.xyy.saas.inquiry.patient.controller.app.patient.vo;

import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.BaseInquiryProductVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.DiagnosisItemVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.PatientVO;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryDetailExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.ToString;
import java.io.Serializable;
import java.util.List;

@Schema(description = "商家APP和小程序 - 患者最后一次问诊记录")
@Data
@ToString(callSuper = true)
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class PatientInquiryRecordRespVO implements Serializable {

    @Schema(description = "患者信息")
    @NotNull(message = "患者信息不能为空")
    @Valid
    private PatientVO patient;

    @Schema(description = "病情描述(主诉)", example = "持续发热一周")
    @NotNull(message = "病情描述不能为空")
    private List<String> mainSuit;

    @Schema(description = "过敏史", example = "青霉素,头孢")
    private List<String> allergic;

    @Schema(description = "诊断信息")
    @NotNull(message = "诊断信息不能为空")
    @Valid
    private List<DiagnosisItemVO> diagnosis;

    @Schema(description = "肝肾功能是否异常", example = "1")
    private Integer liverKidneyValue;

    @Schema(description = "是否妊娠哺乳期", example = "1")
    private Integer gestationLactationValue;

    @Schema(description = "购药类型", example = "0")
    @NotNull(message = "购药类型不能为空")
    private Integer medicineType;

    @Schema(description = "预购药信息", requiredMode = Schema.RequiredMode.REQUIRED)
    @Valid
    private BaseInquiryProductVO inquiryProductInfo;

}
