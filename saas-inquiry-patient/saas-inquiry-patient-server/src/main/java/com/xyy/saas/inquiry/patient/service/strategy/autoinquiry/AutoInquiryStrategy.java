package com.xyy.saas.inquiry.patient.service.strategy.autoinquiry;

import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionGlobalConfigRespDto;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryEnum;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryJudgeNodeEnum;
import com.xyy.saas.inquiry.enums.inquiry.UnableAutoReasonEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.util.MathUtil;

/**
 * @Author: xucao
 * @Date: 2024/12/20 17:08
 * @Description: 自动开方判定策略
 */
public abstract class AutoInquiryStrategy {

    /**
     * 执行判定是否自动开方方法
     *
     * @param inquiryDto   问诊参数
     * @param globalConfig 全局配置
     */
    public abstract void executeJudge(InquiryRecordDto inquiryDto, InquiryOptionGlobalConfigRespDto globalConfig);

    public abstract AutoInquiryJudgeNodeEnum getAutoInquiryJudgeNodeEnum();

    /**
     * 根据比例设置是否回流
     *
     * @param inquiryDto           问诊信息
     * @param ratio                预设置的比例
     * @param unableAutoReasonEnum 不可自动开方原因
     */
    public void inquiryToRealByRatio(InquiryRecordDto inquiryDto, Integer ratio, UnableAutoReasonEnum unableAutoReasonEnum) {
        // 获取设置的回流比例
        if (ratio == null || ratio <= 0) {
            // 未设置回流比例，不回流
            return;
        }
        if (!MathUtil.generateResult(ratio)) {
            // 回流权重不满足，不回流
            return;
        }
        // 设置为不走自动开方
        inquiryDto.setAutoInquiry(AutoInquiryEnum.NO.getCode());
        inquiryDto.setUnableAutoReason(unableAutoReasonEnum.getCode());
    }
}
