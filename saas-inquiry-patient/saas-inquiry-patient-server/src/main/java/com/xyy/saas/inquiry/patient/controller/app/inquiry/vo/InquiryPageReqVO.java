package com.xyy.saas.inquiry.patient.controller.app.inquiry.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.ToString;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;

import static cn.iocoder.yudao.framework.common.util.date.DateUtils.FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND;

/**
 * @ClassName：InquiryPageReqVO
 * @Author: xucao
 * @Date: 2024/10/30 13:21
 * @Description: app端问诊记录查询请求参数
 */
@Schema(description = "app侧 - 问诊记录分页 Request VO")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
public class InquiryPageReqVO extends PageParam {

    @Schema(description = "患者GUID", example = "19102")
    private String patientGuid;

    @Schema(description = "问诊时间")
    @DateTimeFormat(pattern = FORMAT_YEAR_MONTH_DAY_HOUR_MINUTE_SECOND)
    @NotNull(message = "问诊时间不能为空")
    private LocalDateTime[] createTime;

}
