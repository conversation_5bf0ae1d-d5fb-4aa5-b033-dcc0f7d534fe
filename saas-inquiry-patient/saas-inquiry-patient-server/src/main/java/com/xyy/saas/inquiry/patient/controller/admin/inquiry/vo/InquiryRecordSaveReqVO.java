package com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo;

import com.xyy.saas.inquiry.pojo.inquiry.InquiryDetailExtDto;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;

@Schema(description = "管理后台 - 问诊记录新增/修改 Request VO")
@Data
public class InquiryRecordSaveReqVO {

    @Schema(description = "主键ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19216")
    private Long id;

    @Schema(description = "租户ID", requiredMode = Schema.RequiredMode.REQUIRED, example = "19216")
    @NotNull(message = "租户ID不能为空")
    private Long tenantId;

    @Schema(description = "问诊单号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String pref;

    @Schema(description = "患者姓名", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "患者姓名不能为空")
    private String patientName;

    @Schema(description = "患者年龄", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "患者年龄不能为空")
    private String patientAge;

    @Schema(description = "患者性别 1、男   2、女", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "患者性别 1、男   2、女不能为空")
    private Integer patientSex;

    @Schema(description = "患者手机号", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "患者手机号不能为空")
    private String patientMobile;

    @Schema(description = "患者身份证号", requiredMode = Schema.RequiredMode.REQUIRED, example = "123456789012345678")
    private String patientIdCard;

    @Schema(description = "互联网医院编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "14039")
    @NotEmpty(message = "互联网医院编码不能为空")
    private String hospitalPref;

    @Schema(description = "科室编码", requiredMode = Schema.RequiredMode.REQUIRED)
    private String deptPref;

    @Schema(description = "科室名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "张三")
    private String deptName;

    @Schema(description = "处方笺模版编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "22203")
    private Long preTempId;

    @Schema(description = "医生编码", requiredMode = Schema.RequiredMode.REQUIRED, example = "30134")
    @NotEmpty(message = "医生编码不能为空")
    private String doctorPref;

    @Schema(description = "接诊状态：0 排队中 1、患者取消问诊 2 问诊中 3问诊结束 4医生取消开方 5问诊超时取消", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "接诊状态不能为空")
    private Integer inquiryStatus;

    @Schema(description = "取消开方原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "不香")
    private String cancelReason;

    @Schema(description = "问诊方式  1、图文问诊  2、视频问诊  3、电话问诊", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "问诊方式不能为空")
    private Integer inquiryWayType;

    @Schema(description = "问诊业务类型 1、药店问诊  2、远程审方", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    @NotNull(message = "问诊业务类型不能为空")
    private Integer inquiryBizType;

    @Schema(description = "客户端渠类型 0、app  1、pc  2、小程序 ", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
//    @NotNull(message = "客户端渠道类型不能为空")
    private Integer clientChannelType;

    @Schema(description = "客户端系统类型 eg : ios  ,  android", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
//    @NotEmpty(message = "客户端系统类型不能为空")
    private String clientOsType;

    @Schema(description = "问诊渠道 0、荷叶 1、智慧脸  2、海典ERP", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "问诊渠道不能为空")
    private Integer bizChannelType;

    @Schema(description = "用药类型：0西药  、1中药", requiredMode = Schema.RequiredMode.REQUIRED, example = "1")
    @NotNull(message = "用药类型不能为空")
    private Integer medicineType;

    @Schema(description = "是否自动开方：0 否  、 1是", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer autoInquiry;

    @Schema(description = "不走自动开方的原因  1、预购药品无用法用量 2、无自动开方医生  3、门店未开通自动开方", example = "不好")
    private Integer unableAutoReason;

    @Schema(description = "IM平台类型  0、腾讯IM", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer imPlatform;

    @Schema(description = "订单编号", requiredMode = Schema.RequiredMode.REQUIRED)
    private String orderNo;

    @Schema(description = "问诊扩展字段")
    private InquiryDetailExtDto ext;

    @Schema(description = "医生接诊时间")
    private LocalDateTime startTime;

    @Schema(description = "问诊结束时间")
    private LocalDateTime endTime;

    @Schema(description = "肝肾功能异常  0、无  1、肝功能异常  2、肾功能异常  3、肝肾功能异常", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "肝肾功能不能为空")
    private Integer liverKidneyValue;

    @Schema(description = "妊娠哺乳期   0、否  1、妊娠期   2、哺乳期  3、妊娠哺乳期", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "妊娠哺乳期不能为空")
    private Integer gestationLactationValue;

    @Schema(description = "慢病病情需要 0 否  1是", requiredMode = Schema.RequiredMode.REQUIRED)
    private Integer slowDisease;

    @Schema(description = "主诉")
    private List<String> mainSuit;

    @Schema(description = "过敏史  eg：青霉素|头孢")
    @NotEmpty(message = "过敏史不能为空")
    private List<String> allergicItem;

    @Schema(description = "诊断编码")
    private List<String> diagnosisCode;

    @Schema(description = "诊断说明", example = "王五")
    private List<String> diagnosisName;

    @Schema(description = "个人史")
    private String patientHisDesc;

    @Schema(description = "现病史")
    private String currentIllnessDesc;

    @Schema(description = "线下就医处方或病历图片")
    private List<String> offlinePrescriptions;

    @Schema(description = "预购药明细")
    private InquiryProductDto preDrugDetail;

    @Schema(description = "备注说明")
    private String remarks;

}