package com.xyy.saas.inquiry.patient.controller.app.inquiry;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.idempotent.core.annotation.Idempotent;
import cn.iocoder.yudao.framework.ratelimiter.core.annotation.RateLimiter;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantPackageCostApi;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryRespVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.RemoteAuditInquiryReqVO;
import com.xyy.saas.inquiry.patient.service.inquiry.RemoteAuditInquiryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * @Author: xucao
 * @DateTime: 2025/4/22 10:13
 * @Description: 远程审方问诊
 * @Version: V1.0
 **/
@Tag(name = "APP+PC -远程审方问诊")
@RestController
@RequestMapping(value = {"/admin-api/kernel/patient/remote-audit", "/app-api/kernel/patient/remote-audit"})
@Validated
@Slf4j
public class RemoteAuditInquiryController {

    @Resource
    private TenantPackageCostApi tenantPackageCostApi;

    @Resource
    private RemoteAuditInquiryService remoteAuditInquiryService;

    @Idempotent(message = "问诊已发起，请勿重复提交")
    @RateLimiter(message = "问诊频率过高，请稍后再试")
    @Operation(summary = "远程审方(创建问诊单)")
    @PostMapping("/create-inquiry")
    public CommonResult<InquiryRespVO> createInquiry(@Valid @RequestBody RemoteAuditInquiryReqVO remoteAuditInquiryReqVO) {
        // 生成问诊记录单
        return remoteAuditInquiryService.createInquiry(remoteAuditInquiryReqVO);
    }
}
