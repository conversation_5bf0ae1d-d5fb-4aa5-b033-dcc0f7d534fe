package com.xyy.saas.inquiry.patient.controller.app.third;

import cn.iocoder.yudao.framework.apilog.core.annotation.ApiAccessLog;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageParam;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import cn.iocoder.yudao.framework.excel.core.util.ExcelUtils;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyBatchQueryPrescriptionReqVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyGetPrescriptionRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryAuditReqVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryAuditRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryCancelRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryInfoRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryReqVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquirySearchReqVO;
import com.xyy.saas.inquiry.patient.service.third.ThirdPartyAuditService;
import com.xyy.saas.inquiry.patient.service.third.ThirdPartyService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.servlet.http.HttpServletResponse;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import java.io.IOException;
import java.util.List;

import static cn.iocoder.yudao.framework.apilog.core.enums.OperateTypeEnum.EXPORT;

/**
 * 对接三方问诊
 */
@Tag(name = "对接三方问诊")
@RestController
@RequestMapping(value = {"/admin-api/kernel/patient/third-party/inquiry", "/app-api/kernel/patient/third-party/inquiry"})
@Validated
public class ThirdPartyController {

    @Resource
    private ThirdPartyService thirdPartyService;

    @Resource
    private ThirdPartyAuditService thirdPartyAuditService;

    @PostMapping("/pre-inquiry")
    @Operation(summary = "对接三方问诊 - 预问诊")
    @Parameter(name = "thirdPartyPreInquiryRequestDto", description = "三方预问诊订单信息", required = true)
    public CommonResult<ThirdPartyPreInquiryRespVO> thirdPartyPreInquiry(@Valid @RequestBody ThirdPartyPreInquiryReqVO thirdPartyPreInquiryReqVo) {
        return thirdPartyService.thirdPartyPreInquiry(thirdPartyPreInquiryReqVo);
    }

    @GetMapping("/get-prescription")
    @Operation(summary = "对接三方问诊 - 预问诊")
    @Parameter(name = "preInquiryId", description = "三方预问诊订单信息id", required = true)
    public CommonResult<ThirdPartyGetPrescriptionRespVO> getPrescription(@RequestParam("preInquiryPref") String preInquiryPref) {
        return thirdPartyService.getPrescription(preInquiryPref);
    }

    @GetMapping("/get-prescription-page-list")
    @Operation(summary = "三方批量查询门店处方单")
    @Parameter(name = "thirdPartyPreInquirySearchReqVO", description = "三方预问诊订单信息查询对象", required = true)
    public CommonResult<PageResult<ThirdPartyGetPrescriptionRespVO>> getInquiryPrescriptionPageList(ThirdPartyBatchQueryPrescriptionReqVO thirdPartyBatchQueryPrescriptionReqVO) {
        return thirdPartyService.getInquiryPrescriptionPageList(thirdPartyBatchQueryPrescriptionReqVO);
    }

    @GetMapping("/get-pre-inquiry-page-list")
    @Operation(summary = "查询三方预问诊分页集合")
    @Parameter(name = "thirdPartyPreInquirySearchReqVO", description = "三方预问诊订单信息查询对象", required = true)
    public CommonResult<PageResult<ThirdPartyPreInquiryInfoRespVO>> getPreInquiryPageList(@Valid ThirdPartyPreInquirySearchReqVO thirdPartyPreInquirySearchReqVO) {
        return thirdPartyService.getPreInquiryPageList(thirdPartyPreInquirySearchReqVO);
    }

    @GetMapping("/get-pre-inquiry-by-id")
    @Operation(summary = "查询三方预问诊详情")
    @Parameter(name = "thirdPartyPreInquiryId", description = "三方预问诊订单id", required = true)
    public CommonResult<ThirdPartyPreInquiryInfoRespVO> getPreInquiryById(@RequestParam("thirdPartyPreInquiryId") Long thirdPartyPreInquiryId) {
        return thirdPartyService.getPreInquiryById(thirdPartyPreInquiryId);
    }

    @DeleteMapping("/delete")
    @Operation(summary = "删除三方预问诊详情")
    @Deprecated
    @Parameter(name = "thirdPartyPreInquiryId", description = "三方预问诊订单id", required = true)
    public CommonResult<Boolean> deletePreInquiryById(@RequestParam("thirdPartyPreInquiryId") Long thirdPartyPreInquiryId) {
        return thirdPartyService.deletePreInquiryById(thirdPartyPreInquiryId);
    }

    @PutMapping("/pre-inquiry-cancel")
    @Operation(summary = "发起方取消预问诊")
    @Parameter(name = "pref", description = "预问诊编码", required = true)
    public CommonResult<ThirdPartyPreInquiryCancelRespVO> preInquiryCancel(@RequestParam("pref") String pref) {
        return CommonResult.success(thirdPartyAuditService.cancelPreInquiry(pref));
    }

    @PutMapping("/pre-inquiry-audit")
    @Operation(summary = "三方预问诊单审核")
    public CommonResult<ThirdPartyPreInquiryAuditRespVO> preInquiryAudit(@RequestBody @Valid ThirdPartyPreInquiryAuditReqVO thirdPartyPreInquiryAuditReqVO) {
        return thirdPartyAuditService.preInquiryAudit(thirdPartyPreInquiryAuditReqVO);
    }

    @GetMapping("/get-open-third-tag")
    @Operation(summary = "获取开通三方标识")
    public CommonResult<Boolean> getOpenThirdTag() {
        return thirdPartyService.getOpenThirdTag();
    }

    @GetMapping("/export-excel")
    @Operation(summary = "导出预问诊记录 Excel")
    @ApiAccessLog(operateType = EXPORT)
    public void exportInquiryRecordExcel(@Valid ThirdPartyPreInquirySearchReqVO thirdPartyPreInquirySearchReqVO,
        HttpServletResponse response) throws IOException {
        thirdPartyPreInquirySearchReqVO.setPageSize(PageParam.PAGE_SIZE_NONE);
        List<ThirdPartyPreInquiryInfoRespVO> list = thirdPartyService.getPreInquiryPageList(thirdPartyPreInquirySearchReqVO).getData().getList();
        // 导出 Excel
        ExcelUtils.write(response, "预问诊记录.xls", "数据", ThirdPartyPreInquiryInfoRespVO.class,
            BeanUtils.toBean(list, ThirdPartyPreInquiryInfoRespVO.class));
    }

}
