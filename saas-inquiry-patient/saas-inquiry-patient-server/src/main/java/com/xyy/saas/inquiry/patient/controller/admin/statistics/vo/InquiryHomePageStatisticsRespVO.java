package com.xyy.saas.inquiry.patient.controller.admin.statistics.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "管理后台 - 首页问诊统计入参 Response VO")
public class InquiryHomePageStatisticsRespVO {

    @Schema(description = "问诊记录总数", example = "1024")
    private Long inquiryRecordCount;

    @Schema(description = "问诊记录患者总数", example = "1024")
    private Long inquiryRecordPatientCount;

    @Schema(description = "问诊处方总数", example = "1024")
    private Long inquiryPrescriptionCount;

}
