package com.xyy.saas.inquiry.patient.service.dispatch;

import cn.hutool.core.util.ObjectUtil;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.drugstore.api.option.InquiryOptionConfigApi;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigQueryDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionGlobalConfigRespDto;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.service.strategy.autoinquiry.AutoInquiryStrategy;
import jakarta.annotation.Resource;
import java.util.Comparator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/12/20 11:07
 * @Description: 为问诊单指定问诊类型 真人开方 or 自动开方
 * @see inquiryAssignMedicalRegistration  previous
 * @see InquiryAssignReceptionArea  next
 */
@Component
@Slf4j
public class InquiryAssignType extends InquiryBaseDispatch {

    /**
     * 自动开方判定策略
     */
    private static final Map<String, AutoInquiryStrategy> autoInquiryStrategyMap = new LinkedHashMap<>();

    @Autowired
    public void initHandler(List<AutoInquiryStrategy> strategies) {
        // 排序
        strategies.sort(Comparator.comparingInt(o -> o.getAutoInquiryJudgeNodeEnum().getOrder()));
        strategies.forEach(strategy -> autoInquiryStrategyMap.put(strategy.getAutoInquiryJudgeNodeEnum().getCode(), strategy));
    }

    @Resource
    private InquiryOptionConfigApi inquiryOptionConfigApi;


    @Override
    @TraceNode(node = TraceNodeEnum.ASSIGN_TYPE ,prefLocation = "inquiryDto.pref")
    public void execute(InquiryRecordDto inquiryDto) {
        log.info("问诊单号：{},开始执行问诊单调度责任链【自动开方判定】", inquiryDto.getPref());
        // 初始化自动开方标识--默认自动开方
        inquiryDto.setAutoInquiry(AutoInquiryEnum.YES.getCode());
        // 获取全局配置
         InquiryOptionGlobalConfigRespDto globalConfig = inquiryOptionConfigApi.getInquiryOptionGlobalConfig(new InquiryOptionConfigQueryDto());
        // 执行自动开方判定策略
        autoInquiryStrategyMap.forEach((key, strategy) -> {
            // 如果已经判定为非自动开方，则不再执行后续策略
            if (ObjectUtil.equals(AutoInquiryEnum.NO.getCode(), inquiryDto.getAutoInquiry())) {
                return;
            }
            strategy.executeJudge(inquiryDto, globalConfig);
        });
        log.info("问诊单号:{},执行问诊单调度责任链【自动开方判定】结束,判定结果:{}", inquiryDto.getPref(), inquiryDto.isAutoInquiry());
    }
}
