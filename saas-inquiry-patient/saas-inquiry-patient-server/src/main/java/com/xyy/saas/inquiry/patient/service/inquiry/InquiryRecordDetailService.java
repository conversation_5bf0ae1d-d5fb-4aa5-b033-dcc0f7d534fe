package com.xyy.saas.inquiry.patient.service.inquiry;

import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryQueryDto;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDetailDO;
import java.util.List;

/**
 * @Author: xucao
 * @Date: 2024/12/30 13:11
 * @Description: 问诊单详情服务接口
 */
public interface InquiryRecordDetailService {

    /**
     * 查询问诊单详情列表
     *
     * @param inquiryQueryDto 查询条件
     * @return 问诊详情列表
     */
    List<InquiryRecordDetailDO> getInquiryRecordDetailList(InquiryQueryDto inquiryQueryDto);
}
