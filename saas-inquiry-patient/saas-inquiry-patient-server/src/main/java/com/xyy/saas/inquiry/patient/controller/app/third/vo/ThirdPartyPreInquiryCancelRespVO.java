package com.xyy.saas.inquiry.patient.controller.app.third.vo;

import com.xyy.saas.inquiry.enums.inquiry.PreInquiryCancelResultTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: xucao
 * @DateTime: 2025/5/9 13:53
 * @Description: 预问诊单取消返回结果
 **/
@Data
public class ThirdPartyPreInquiryCancelRespVO {

    @Schema(description = "取消结果 0-取消成功 1-审核通过，等待医生接诊（此时要切换等待状态为待接诊）  2-审核通过，医生已接诊（此时应该进入IM）")
    private Integer resultType;

    @Schema(description = "取消结果信息")
    private String resultMsg;

    /**
     * 问诊单号
     */
    @Schema(description = "问诊单号")
    private String inquiryPref;

    /**
     * 是否自动开方  0-否  1 是
     */
    @Schema(description = "是否自动开方 0-否  1 是")
    private Integer autoInquiry;

    /**
     * 预问诊单号
     */
    @Schema(description = "预问诊单号")
    private String preInquiryPref;

    public ThirdPartyPreInquiryCancelRespVO(PreInquiryCancelResultTypeEnum resultTypeEnum) {
        this.resultType = resultTypeEnum.getType();
        this.resultMsg = resultTypeEnum.getMsg();
    }
}
