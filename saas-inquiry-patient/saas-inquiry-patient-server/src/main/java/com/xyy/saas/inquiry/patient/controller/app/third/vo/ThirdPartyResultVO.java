package com.xyy.saas.inquiry.patient.controller.app.third.vo;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 三方对接结果对象
 *
 * <AUTHOR>
 * @Date 6/11/24 11:02 AM
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "app侧 - 三方统一返回对象")
public class ThirdPartyResultVO<T> extends CommonResult<T> {

    @Schema(description = "结果")
    private T result;

}
