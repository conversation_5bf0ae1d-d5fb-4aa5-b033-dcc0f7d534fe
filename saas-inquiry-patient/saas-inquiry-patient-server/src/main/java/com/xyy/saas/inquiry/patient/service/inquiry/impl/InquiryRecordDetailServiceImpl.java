package com.xyy.saas.inquiry.patient.service.inquiry.impl;

import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryQueryDto;
import com.xyy.saas.inquiry.patient.convert.inquiry.InquiryRecordDetailConvert;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDetailDO;
import com.xyy.saas.inquiry.patient.dal.mysql.inquiry.InquiryRecordDetailMapper;
import com.xyy.saas.inquiry.patient.service.inquiry.InquiryRecordDetailService;
import jakarta.annotation.Resource;
import java.util.List;
import org.springframework.stereotype.Service;

/**
 * @Author: xucao
 * @Date: 2024/12/30 13:12
 * @Description: 问诊单详情服务实现类
 * @Version: 1.0
 */
@Service
public class InquiryRecordDetailServiceImpl implements InquiryRecordDetailService {

    @Resource
    private InquiryRecordDetailMapper inquiryRecordDetailMapper;

    /**
     * 查询问诊单详情列表
     *
     * @param inquiryQueryDto 查询条件
     * @return 问诊详情列表
     */
    @Override
    public List<InquiryRecordDetailDO> getInquiryRecordDetailList(InquiryQueryDto inquiryQueryDto) {
        return inquiryRecordDetailMapper.selectListByCondition(InquiryRecordDetailConvert.INSTANCE.convertQueryDTO2VO(inquiryQueryDto));
    }
}
