package com.xyy.saas.inquiry.patient.service.statistics;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.alibaba.fastjson.JSONObject;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionApi;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.patient.controller.admin.statistics.vo.InquiryHomePageStatisticsRespVO;
import com.xyy.saas.inquiry.patient.controller.admin.statistics.vo.InquiryStatisticsReqVO;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDO;
import com.xyy.saas.inquiry.patient.dal.mysql.inquiry.InquiryRecordMapper;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class InquiryStatisticsServiceImpl implements InquiryStatisticsService {

    @Resource
    private InquiryRecordMapper inquiryRecordMapper;

    @DubboReference
    protected InquiryPrescriptionApi inquiryPrescriptionApi;

    /**
     * 首页统计
     *
     * @param inquiryStatisticsReqVO
     * @return
     */
    @Override
    public CommonResult<InquiryHomePageStatisticsRespVO> homePageStatistics(InquiryStatisticsReqVO inquiryStatisticsReqVO) {

        InquiryHomePageStatisticsRespVO inquiryHomePageStatisticsRespVO = InquiryHomePageStatisticsRespVO.builder()
            .inquiryRecordCount(0L)
            .inquiryRecordPatientCount(0L)
            .inquiryPrescriptionCount(0L)
            .build();

        inquiryStatisticsReqVO.setTenantId(TenantContextHolder.getTenantId());

        if (inquiryStatisticsReqVO.getCreateTime() == null
            || inquiryStatisticsReqVO.getCreateTime().length < 2
            || inquiryStatisticsReqVO.getCreateTime()[0] == null
            || inquiryStatisticsReqVO.getCreateTime()[1] == null
            || inquiryStatisticsReqVO.getTenantId() == null) {

            return CommonResult.success(inquiryHomePageStatisticsRespVO);
        }

        Long recordCount = inquiryRecordMapper.selectCount(
            new LambdaQueryWrapperX<InquiryRecordDO>().eq(InquiryRecordDO::getTenantId, inquiryStatisticsReqVO.getTenantId())
                .betweenIfPresent(InquiryRecordDO::getCreateTime, inquiryStatisticsReqVO.getCreateTime()).orderByDesc(InquiryRecordDO::getCreateTime)
                .eq(InquiryRecordDO::getDeleted, false));

        Long recordCountByDistinctPatient = inquiryRecordMapper.selectCountByDistinctPatient(inquiryStatisticsReqVO);

        Long prescriptionCount = inquiryPrescriptionApi.getPrescriptionCount(InquiryPrescriptionQueryDTO.builder().tenantId(inquiryStatisticsReqVO.getTenantId()).createTime(inquiryStatisticsReqVO.getCreateTime()).build());

        inquiryHomePageStatisticsRespVO.setInquiryRecordCount(recordCount);
        inquiryHomePageStatisticsRespVO.setInquiryRecordPatientCount(recordCountByDistinctPatient);
        inquiryHomePageStatisticsRespVO.setInquiryPrescriptionCount(prescriptionCount);

        return CommonResult.success(inquiryHomePageStatisticsRespVO);
    }
}
