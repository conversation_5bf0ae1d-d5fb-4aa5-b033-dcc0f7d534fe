package com.xyy.saas.inquiry.patient.service.dispatch;

import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.enums.inquiry.DispatchDeptTypeEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.hospital.api.dept.InquiryDeptApi;
import com.xyy.saas.inquiry.hospital.api.dept.dto.InquiryHospitalDepartmentRelationDto;
import com.xyy.saas.inquiry.hospital.api.dept.dto.InquiryHospitalDeptQueryReqDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pojo.HospitalDeptDto;
import com.xyy.saas.inquiry.pojo.HospitalDeptDto.Dept;
import jakarta.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

/**
 * @Author: xucao
 * @Date: 2024/12/20 11:10
 * @Description: 为问诊医院分配可接诊科室
 * @see InquiryAssignHospital previous
 * @see inquiryAssignMedicalRegistration next 挂号登记
 */
@Component
@Slf4j
public class InquiryAssignDept extends InquiryBaseDispatch {

    @Resource
    private InquiryDeptApi inquiryDeptApi;

    @Override
    @TraceNode(node = TraceNodeEnum.ASSIGN_DEPT ,prefLocation = "inquiryDto.pref")
    public void execute(InquiryRecordDto inquiryDto) {
        log.info("问诊单号：{},开始执行问诊单调度责任链【分配科室】", inquiryDto.getPref());
        // 查询当前医院所有科室
        List<InquiryHospitalDepartmentRelationDto> hospitalDepartmentRelationDtoList = inquiryDeptApi.getHospitalDeptList(InquiryHospitalDeptQueryReqDto.builder().hospitalPrefs(inquiryDto.getChoiceHospitalList()).build());
        // 诊断关联科室
        List<String> choiceDeptList = inquiryDto.getChoiceDeptList();
        // 根据诊断关联科室列表过滤出当前医院可选科室
        List<InquiryHospitalDepartmentRelationDto> currHospitalCanInquiryDeptList = hospitalDepartmentRelationDtoList.stream().filter(dept -> choiceDeptList.contains(dept.getDeptPref())).toList();
        // 将过滤后可问诊科室添加到当前医院可选科室列表
        if (CollectionUtils.isEmpty(currHospitalCanInquiryDeptList)) {
            return;
        }
        List<Dept> deptList = currHospitalCanInquiryDeptList.stream().map(dept -> Dept.builder().deptPref(dept.getDeptPref()).dispatchDeptType(DispatchDeptTypeEnum.DIAGNOSIS_DEPARTMENT.getCode()).deptName(dept.getDeptName()).build())
            .toList();
        // 将诊断关联科室添加到本次调度科室列表
        inquiryDto.getHospitalDeptDto().getInquiryDeptList().addAll(deptList);
    }
}