package com.xyy.saas.inquiry.patient.service.inquiry.handle;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.INQUIRY_CANCEL_FAIL_STATUS_NOT_QUEUEING;

import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.xyy.saas.inquiry.constant.RedisKeyConstants;
import com.xyy.saas.inquiry.mq.tenant.cost.TenantChangeCostDto;
import com.xyy.saas.inquiry.mq.tenant.cost.InquiryReBackCostEvent;
import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryStatusEnum;
import com.xyy.saas.inquiry.enums.tenant.CostRecordTypeEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.RemoteAuditPrescriptionApi;
import com.xyy.saas.inquiry.patient.convert.inquiry.InquiryRecordConvert;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDO;
import com.xyy.saas.inquiry.patient.dal.mysql.inquiry.InquiryRecordMapper;
import com.xyy.saas.inquiry.patient.dal.redis.tenant.TenantRedisDao;
import com.xyy.saas.inquiry.mq.tenant.cost.InquiryReBackProducer;
import com.xyy.saas.inquiry.patient.service.inquiry.InquiryImService;
import com.xyy.saas.inquiry.util.RedisUtils;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * @Author: xucao
 * @DateTime: 2025/4/22 17:48
 * @Description: 取消问诊处理类
 **/
@Component
public class InquiryCancelHandle {

    @Resource
    private InquiryRecordMapper inquiryRecordMapper;

    @Resource
    private TenantRedisDao tenantRedisDao;

    @Resource
    private InquiryReBackProducer inquiryReBackProducer;

    @Resource
    private InquiryImService inquiryImService;

    @DubboReference
    private TenantApi tenantApi;

    @Resource
    private RemoteAuditPrescriptionApi remoteAuditPrescriptionApi;

    private final Map<Integer, Function<InquiryRecordDO, Boolean>> cancelLogicMap = new HashMap<>();


    @PostConstruct
    public void init() {
        cancelLogicMap.put(InquiryBizTypeEnum.DRUGSTORE_INQUIRY.getCode(), this::defaultCancel);
        cancelLogicMap.put(InquiryBizTypeEnum.REMOTE_INQUIRY.getCode(), this::remoteCancel);
    }

    /**
     * 根据业务类型执行取消逻辑
     *
     * @param bizType   业务类型
     * @param inquiryDO 问诊单对象
     * @return
     */
    public Boolean executeCancelLogic(Integer bizType, InquiryRecordDO inquiryDO) {
        Function<InquiryRecordDO, Boolean> cancelLogic = cancelLogicMap.get(bizType);
        if (cancelLogic == null) {
            return Boolean.FALSE;
        }
        return cancelLogic.apply(inquiryDO);
    }

    /**
     * 远程问诊批量取消
     *
     * @param inquiryPrefList
     */
    @Transactional(rollbackFor = Exception.class)
    public void batchCancelRemoteInquiry(List<String> inquiryPrefList) {
        // 操作数据库批量删除问诊单
        batchDeleteInquiry(inquiryPrefList);
        // 批量取消远程审方
        remoteAuditPrescriptionApi.batchCancelRemotePrescription(inquiryPrefList);
        // 退回额度
        batchReBackCost(inquiryPrefList);
    }


    /**
     * 普通问诊取消问诊
     *
     * @param inquiryDO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    protected Boolean defaultCancel(InquiryRecordDO inquiryDO) {
        // 非排队中状态，无法取消
        if (InquiryStatusEnum.QUEUING.getStatusCode() != inquiryDO.getInquiryStatus()) {
            throw exception(INQUIRY_CANCEL_FAIL_STATUS_NOT_QUEUEING);
        }
        // 获取当前问诊单的派单医生记录
        List<String> sendList = new ArrayList<>(RedisUtils.lGetAll(RedisKeyConstants.getSendInquiryKey(inquiryDO.getPref())).stream().map(Object::toString).toList());
        // 更新问诊单状态为已取消
        inquiryDO.setInquiryStatus(InquiryStatusEnum.CANCELED.getStatusCode());
        // 操作数据库删除问诊单
        deleteInquiry(inquiryDO);
        // 更新redis
        tenantRedisDao.onDrugstoreCancel(InquiryRecordConvert.INSTANCE.convertDO2DTO(inquiryDO), tenantApi.getTenant(inquiryDO.getTenantId()));
        // 退回额度
        reBackCost(inquiryDO);
        // 发送IM推送问诊取消事件
        inquiryImService.batchNotifyDoctorForInquiryChange(sendList);
        return Boolean.TRUE;
    }

    /**
     * 远程审方取消问诊
     *
     * @param inquiryDO
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    protected Boolean remoteCancel(InquiryRecordDO inquiryDO) {
        // 操作数据库删除问诊单
        deleteInquiry(inquiryDO);
        // 取消远程审方
        remoteAuditPrescriptionApi.cancelRemotePrescription(inquiryDO.getPref());
        // 取消成功 退回额度
        reBackCost(inquiryDO);
        return Boolean.TRUE;
    }

    /**
     * 删除问诊单
     *
     * @param inquiryDO
     */
    private void deleteInquiry(InquiryRecordDO inquiryDO) {
        inquiryRecordMapper.updateById(inquiryDO);
        // 取消的问诊单同步删除
        // inquiryRecordMapper.deleteById(inquiryDO.getId());
    }

    /**
     * 批量删除问诊单
     *
     * @param inquiryPrefList
     */
    private void batchDeleteInquiry(List<String> inquiryPrefList) {
        // 根据问诊单批量删除问诊记录
        inquiryRecordMapper.delete(Wrappers.lambdaQuery(InquiryRecordDO.class).in(InquiryRecordDO::getPref, inquiryPrefList));
    }


    /**
     * 退回额度
     *
     * @param inquiryDO
     */
    private void reBackCost(InquiryRecordDO inquiryDO) {
        // 退回已扣减的额度
        inquiryReBackProducer.sendMessage(
            InquiryReBackCostEvent.builder().msg(TenantChangeCostDto.builder().bizId(inquiryDO.getPref()).recordType(CostRecordTypeEnum.INQUIRY.getCode()).reBackRecordType(CostRecordTypeEnum.INQUIRY_CANAL.getCode()).build()).build());
    }

    /**
     * 批量退回额度
     *
     * @param inquiryPrefList
     */
    private void batchReBackCost(List<String> inquiryPrefList) {
        // 批量退回已扣减的额度
        inquiryReBackProducer.sendMessage(InquiryReBackCostEvent.builder()
            .msg(TenantChangeCostDto.builder().bizIds(inquiryPrefList).recordType(CostRecordTypeEnum.INQUIRY.getCode()).reBackRecordType(CostRecordTypeEnum.INQUIRY_CANAL.getCode()).build())
            .build());
    }
}


