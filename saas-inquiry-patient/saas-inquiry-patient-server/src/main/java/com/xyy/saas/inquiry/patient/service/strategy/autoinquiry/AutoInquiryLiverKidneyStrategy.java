package com.xyy.saas.inquiry.patient.service.strategy.autoinquiry;

import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionGlobalConfigRespDto;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryEnum;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryJudgeNodeEnum;
import com.xyy.saas.inquiry.enums.inquiry.UnableAutoReasonEnum;
import com.xyy.saas.inquiry.enums.patient.LiverKidneyFuncEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

/**
 * @Author: xucao
 * @Date: 2024/12/20 18:21
 * @Description: 自动开方肝肾功能判定策略
 */
@Component
public class AutoInquiryLiverKidneyStrategy extends AutoInquiryStrategy{

    /**
     * 执行判定是否自动开方方法
     *
     * @param inquiryDto   问诊参数
     * @param globalConfig 全局配置
     */
    @Override
    @TraceNode(node = TraceNodeEnum.AUOTO_INQUIRY_LIVERKIDNEY ,prefLocation = "inquiryDto.pref")
    public void executeJudge(InquiryRecordDto inquiryDto, InquiryOptionGlobalConfigRespDto globalConfig) {
        // 肝肾异常流向真人开关是否开启
        if (!BooleanUtils.isTrue(globalConfig.getPresAutoInquiryToRealForLiverRenalDysfunction())) {
            // 未开启情况下直接返回
            return;
        }
        Integer liverKidneyValue = inquiryDto.getInquiryRecordDetailDto().getLiverKidneyValue();
        if (ObjectUtils.isEmpty(liverKidneyValue) || liverKidneyValue == LiverKidneyFuncEnum.NORMAL.getCode()) {
            // 无肝肾异常情况下直接返回
            return;
        }
        inquiryDto.setAutoInquiry(AutoInquiryEnum.NO.getCode());
        inquiryDto.setUnableAutoReason(UnableAutoReasonEnum.PATIENT_HAD_LIVER_KIDNEY_FUNCTION_ABNORMAL.getCode());
    }

    @Override
    public AutoInquiryJudgeNodeEnum getAutoInquiryJudgeNodeEnum() {
        return AutoInquiryJudgeNodeEnum.AUTO_INQUIRY_TO_REAL_FOR_LIVER_RENAL_DYSFUNCTION;
    }
}
