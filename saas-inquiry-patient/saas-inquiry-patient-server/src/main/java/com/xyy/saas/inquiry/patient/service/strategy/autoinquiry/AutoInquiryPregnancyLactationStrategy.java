package com.xyy.saas.inquiry.patient.service.strategy.autoinquiry;

import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionGlobalConfigRespDto;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryEnum;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryJudgeNodeEnum;
import com.xyy.saas.inquiry.enums.inquiry.UnableAutoReasonEnum;
import com.xyy.saas.inquiry.enums.patient.PregnancyLactationEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

/**
 * @Author: xucao
 * @Date: 2024/12/23 9:42
 * @Description: 自动开方妊娠、哺乳期判定
 */
@Component
public class AutoInquiryPregnancyLactationStrategy extends AutoInquiryStrategy{

    /**
     * 执行判定是否自动开方方法
     *
     * @param inquiryDto   问诊参数
     * @param globalConfig 全局配置
     */
    @Override
    @TraceNode(node = TraceNodeEnum.AUOTO_INQUIRY_PREGNANCYLACTATION ,prefLocation = "inquiryDto.pref")
    public void executeJudge(InquiryRecordDto inquiryDto, InquiryOptionGlobalConfigRespDto globalConfig) {
        // 妊娠哺乳期流向真人开关是否开启
        if (!BooleanUtils.isTrue(globalConfig.getPresAutoInquiryToRealForPregnancyLactation())) {
            // 未开启情况下直接返回
            return;
        }
        Integer gestationLactationValue = inquiryDto.getInquiryRecordDetailDto().getGestationLactationValue();
        if (ObjectUtils.isEmpty(gestationLactationValue) || gestationLactationValue == PregnancyLactationEnum.NORMAL.getCode()) {
            // 无妊娠哺乳期情况下直接返回
            return;
        }
        inquiryDto.setAutoInquiry(AutoInquiryEnum.NO.getCode());
        inquiryDto.setUnableAutoReason(UnableAutoReasonEnum.PATIENT_HAD_PREGNANCY_LACTATION.getCode());
    }

    @Override
    public AutoInquiryJudgeNodeEnum getAutoInquiryJudgeNodeEnum() {
        return AutoInquiryJudgeNodeEnum.AUTO_INQUIRY_TO_REAL_FOR_PREGNANCY_LACTATION;
    }
}
