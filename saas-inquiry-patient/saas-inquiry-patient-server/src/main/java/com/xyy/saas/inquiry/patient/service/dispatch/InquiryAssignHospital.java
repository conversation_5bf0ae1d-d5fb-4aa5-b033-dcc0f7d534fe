package com.xyy.saas.inquiry.patient.service.dispatch;

import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.enums.inquiry.DispatchDeptTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalApi;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalReqDto;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalSettingDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pojo.HospitalDeptDto;
import com.xyy.saas.inquiry.pojo.HospitalDeptDto.Dept;
import jakarta.annotation.Resource;
import java.util.List;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/12/20 11:09
 * @Description: 给问诊单分配医院
 * @see InquiryPreCheck 参数校验
 * @see InquiryAssignDept next 选择可接诊科室
 */
@Component
@Slf4j
public class InquiryAssignHospital extends InquiryBaseDispatch {

    @Resource
    private InquiryHospitalApi inquiryHospitalApi;

    @Override
    @TraceNode(node = TraceNodeEnum.ASSIGN_HOSPITAL ,prefLocation = "inquiryDto.pref")
    public void execute(InquiryRecordDto inquiryDto) {
        log.info("问诊单号：{},开始执行问诊单调度责任链【分配医院】", inquiryDto.getPref());
        // 查询所有医院信息
        List<HospitalDeptDto> hospitalDeptDtoList = inquiryHospitalApi.getInquiryHospitals(InquiryHospitalReqDto.builder().inquiryHospitalPrefs(inquiryDto.getChoiceHospitalList()).build()).stream().map(obj -> {
            return convertToHospitalDeptDto(obj, inquiryDto);
        }).toList();
        // 目前业务侧只会配置一个医院，后续可在这里加多医院的选取规则
        HospitalDeptDto hospitalDeptDto = hospitalDeptDtoList.getFirst();
        inquiryDto.setHospitalDeptDto(hospitalDeptDto);
        inquiryDto.setHospitalPref(hospitalDeptDto.getHospitalPref());
        inquiryDto.setHospitalName(hospitalDeptDto.getHospitalName());
        log.info("问诊单号：{},选定的医院编码：{}", inquiryDto.getPref(), hospitalDeptDto.getHospitalPref());
    }

    /**
     * 转换为医院科室信息 ,并根据当前问诊的购药类型 选择默认科室
     *
     * @param hospitalRespDto 医院信息
     * @param inquiryDto      问诊单信息
     * @return 医院科室信息
     */
    private HospitalDeptDto convertToHospitalDeptDto(InquiryHospitalRespDto hospitalRespDto, InquiryRecordDto inquiryDto) {
        InquiryHospitalSettingDto setting = hospitalRespDto.getSetting();
        List<Dept> defaultDept = inquiryDto.getMedicineType() == MedicineTypeEnum.ASIAN_MEDICINE.getCode() ? setting.getDefaultInquiryWesternMedicineDept() : setting.getDefaultInquiryChineseMedicineDept();
        //指定科室调度类型为默认科室
        defaultDept.forEach(dept -> {dept.setDispatchDeptType(DispatchDeptTypeEnum.DEFAULT_DEPARTMENT.getCode());});
        // 默认科室
        return HospitalDeptDto.builder().hospitalPref(hospitalRespDto.getPref()).hospitalName(hospitalRespDto.getName()).inquiryDeptList(defaultDept).build();
    }
}
