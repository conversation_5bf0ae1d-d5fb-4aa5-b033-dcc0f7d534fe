package com.xyy.saas.inquiry.patient.service.inquiry;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordDetailRespVO;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordRespVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryRecordAndPrescriptionVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryReceptionInfoRespVO;

/**
 * @Desc 问诊记录查询
 * <AUTHOR>
 */
public interface InquiryRecordQueryService {

    /**
     * 查询问诊记录 根据问诊单号
     *
     * @param inquiryPref 问诊单号
     * @return 问诊记录
     */
    InquiryRecordDetailRespVO queryInquiryRecordVOByPref(String inquiryPref);

    /**
     * 根据 问诊单号 查询问诊处方
     *
     * @param inquiryPref 问诊单号
     * @return 问诊处方信息
     */
    InquiryRecordAndPrescriptionVO getInquiryPrescriptionByPref(String inquiryPref);

    /**
     * 根据问诊单号查询问诊接诊信息
     * @param inquiryPref 问诊单号
     * @return 接诊信息
     */
    InquiryReceptionInfoRespVO getReceptionInfo(String inquiryPref);

    /**
     * 获得问诊记录分页
     *
     * @param pageReqVO 分页查询
     * @return 问诊记录分页
     */
    PageResult<InquiryRecordRespVO> getInquiryRecordPage(InquiryRecordPageReqVO pageReqVO);
}
