package com.xyy.saas.inquiry.patient.convert.third;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils;
import cn.iocoder.yudao.framework.web.core.util.LoginUserContextUtils;
import com.alibaba.fastjson2.JSON;
import com.xyy.saas.inquiry.enums.inquiry.BizChannelTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.PreInquiryCancelResultTypeEnum;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.BaseInquiryProductVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.BaseInquiryReqVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.DrugstoreInquiryReqVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryCancelRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryInfoRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryReqVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryReqVO.ThirdPartyPreInquiryDetailReqDto;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.third.ThirdPartyPreInquiryDO;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDetailDto;
import com.xyy.saas.inquiry.util.PrefUtil;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;

/**
 * 三方预问诊转换对象
 */
@Mapper
public interface ThirdPartyPreInquiryConvert {

    ThirdPartyPreInquiryConvert INSTANCE = Mappers.getMapper(ThirdPartyPreInquiryConvert.class);

    PageResult<ThirdPartyPreInquiryInfoRespVO> convertPage(PageResult<ThirdPartyPreInquiryDO> thirdPartyPreInquiryDOPageResult);

    ThirdPartyPreInquiryInfoRespVO convertDo2InfoRespVO(ThirdPartyPreInquiryDO thirdPartyPreInquiryDO);

    default ThirdPartyPreInquiryDO convertVO2DO(ThirdPartyPreInquiryReqVO thirdPartyPreInquiryReqVo, TenantDto tenantDto) {
        return ThirdPartyPreInquiryDO.builder().pref(PrefUtil.getPreInquiryPref()).tenantId(tenantDto.getId()).tenantName(tenantDto.getName()).transmissionOrganId(thirdPartyPreInquiryReqVo.getBizChannelType()).userName(thirdPartyPreInquiryReqVo.getUserName())
            .mobile(thirdPartyPreInquiryReqVo.getTelephone()).inquiryWayType(thirdPartyPreInquiryReqVo.getInquiryWayType()).idCard(thirdPartyPreInquiryReqVo.getIdCard()).age(thirdPartyPreInquiryReqVo.getAge()).sex(thirdPartyPreInquiryReqVo.getSex())
            .medicineType(thirdPartyPreInquiryReqVo.getMedicineType()).ext(thirdPartyPreInquiryReqVo.getExt()).build();
    }

    default ThirdPartyPreInquiryReqVO convertInquiryVO2PreInquiryVO(DrugstoreInquiryReqVO drugstoreInquiryReqVO) {
        drugstoreInquiryReqVO.setCreator(Objects.requireNonNull(SecurityFrameworkUtils.getLoginUserId()).toString());
        BaseInquiryReqVO inquiryReqVO = drugstoreInquiryReqVO.getBaseInquiryReqVO();
        return ThirdPartyPreInquiryReqVO.builder().userName(inquiryReqVO.getPatient().getPatientName()).bizChannelType(BizChannelTypeEnum.MINI_PROGRAM.getCode()).age(Integer.valueOf(inquiryReqVO.getPatient().getPatientAge()))
            .sex(inquiryReqVO.getPatient().getPatientSex()).idCard(inquiryReqVO.getPatient().getPatientIdCard()).telephone(inquiryReqVO.getPatient().getPatientMobile()).medicineType(inquiryReqVO.getMedicineType())
            .inquiryWayType(drugstoreInquiryReqVO.getInquiryWayType()).drugList(convertDrugList(inquiryReqVO.getInquiryProductInfo())).ext(drugstoreInquiryReqVO).build();
    }

    default List<ThirdPartyPreInquiryDetailReqDto> convertDrugList(BaseInquiryProductVO inquiryProductInfo) {
        List<ThirdPartyPreInquiryDetailReqDto> result = new ArrayList<>();
        List<InquiryProductDetailDto> inquiryProductInfos = inquiryProductInfo.getInquiryProductInfos();
        if (CollectionUtils.isEmpty(inquiryProductInfos)) {
            return result;
        }
        inquiryProductInfos.forEach(item -> {
            result.add(
                ThirdPartyPreInquiryDetailReqDto.builder().drugName(item.getCommonName()).productUnit(item.getSingleUnit()).attributeSpecification(item.getAttributeSpecification()).quantity(item.getQuantity()).barCode(item.getBarCode())
                    .manufacturer(item.getManufacturer()).medicinesQuasiName(item.getApprovalNumber()).build());
        });
        return result;
    }

    default ThirdPartyPreInquiryCancelRespVO convert(ThirdPartyPreInquiryDO preInquiryDO , InquiryRecordDO inquiryDo){
        ThirdPartyPreInquiryCancelRespVO respVO = new ThirdPartyPreInquiryCancelRespVO(
            ObjectUtil.equal(inquiryDo.getInquiryStatus(), InquiryStatusEnum.QUEUING.getStatusCode()) ? PreInquiryCancelResultTypeEnum.AUDIT_SUCCESS_WAIT_DOCTOR_CONNECT : PreInquiryCancelResultTypeEnum.AUDIT_SUCCESS_DOCTOR_CONNECTED
        );
        respVO.setInquiryPref(inquiryDo.getPref());
        respVO.setAutoInquiry(inquiryDo.getAutoInquiry());
        respVO.setPreInquiryPref(preInquiryDO.getPref());
        return respVO;
    }


}
