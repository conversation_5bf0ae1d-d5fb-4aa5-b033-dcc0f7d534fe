package com.xyy.saas.inquiry.patient.convert.inquiry;

import com.xyy.saas.inquiry.enums.inquiry.InquiryBizTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.PrescriptionTransferStatusEnum;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordDetailRespVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryRecordAndPrescriptionVO;
import com.xyy.saas.inquiry.patient.enums.IconType;
import com.xyy.saas.inquiry.pojo.StatusEnumDto;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: cxy
 * @Description: 问诊记录 + 处方 转换器
 */
@Mapper
public interface InquiryRecordPrescriptionConvert {

    InquiryRecordPrescriptionConvert INSTANCE = Mappers.getMapper(InquiryRecordPrescriptionConvert.class);


    default InquiryRecordAndPrescriptionVO convertRecordPrescription(InquiryRecordDetailRespVO inquiryRecordVO, InquiryPrescriptionRespDTO prescription, Boolean isHidePrescription) {
        InquiryPrescriptionRespDTO prescriptionRespDTO = Optional.ofNullable(prescription).orElse(new InquiryPrescriptionRespDTO());

        List<StatusEnumDto> prescriptionStatusEnums = PrescriptionTransferStatusEnum.convertInquiryPrescriptionStatusList(InquiryStatusEnum.fromStatusCode(inquiryRecordVO.getInquiryStatus()),
            PrescriptionStatusEnum.fromStatusCode(prescriptionRespDTO.getStatus()), InquiryBizTypeEnum.fromCode(inquiryRecordVO.getInquiryBizType()));

        PrescriptionTransferStatusEnum prescriptionStatusEnum = PrescriptionTransferStatusEnum.convertInquiryPrescriptionStatus(InquiryStatusEnum.fromStatusCode(inquiryRecordVO.getInquiryStatus()),
            PrescriptionStatusEnum.fromStatusCode(prescriptionRespDTO.getStatus()));

        StatusEnumDto convertStatusEnum = PrescriptionTransferStatusEnum.convertStatusEnum(prescriptionStatusEnum);
        /**
         * 处理前端展示图标样式
         * 2.1 如果 字段列表中的 status 小于 当前节点的status,  展示 勾选 ， 大于当前节点展示 灰色的...
         * 2.2 如果当前节点是 处方关闭3，开方超时4，审核驳回7，展示 x  ，其他的话当前节点就是 高亮的...
         */
        for (StatusEnumDto anEnum : prescriptionStatusEnums) {
            if (anEnum.getStatus() < convertStatusEnum.getStatus()) {
                anEnum.setType(IconType.T0.getType());
            }
            if (anEnum.getStatus() > convertStatusEnum.getStatus()) {
                anEnum.setType(IconType.T3.getType());
            }
            if (Objects.equals(anEnum.getStatus(), convertStatusEnum.getStatus())) {
                if (PrescriptionTransferStatusEnum.isCloseType(convertStatusEnum.getStatus())) {
                    anEnum.setType(IconType.T2.getType());
                } else {
                    anEnum.setType(Objects.equals(PrescriptionTransferStatusEnum.REVIEWED.getStatus(), convertStatusEnum.getStatus()) ? IconType.T0.getType() : IconType.T1.getType());
                }
            }
        }
        // 是否可以取消 ， 远程问诊 + 待审核 状态 才可需求
        boolean canCancel = Objects.equals(prescriptionRespDTO.getInquiryBizType(), InquiryBizTypeEnum.REMOTE_INQUIRY.getCode())
            && Objects.equals(prescriptionRespDTO.getStatus(), PrescriptionStatusEnum.WAIT_APPROVAL.getStatusCode());

        inquiryRecordVO.setOutPrescriptionTime(prescriptionRespDTO.getOutPrescriptionTime());
        inquiryRecordVO.setPrescriptionPref(prescriptionRespDTO.getPref());

        return InquiryRecordAndPrescriptionVO.builder()
            .inquiryRecordDetail(inquiryRecordVO)
            .InquiryPrescriptionStatusList(prescriptionStatusEnums)
            .currentInquiryPrescriptionStatus(convertStatusEnum)
            .canCancel(canCancel)
            .isHidePrescription(convertStatusEnum.getStatus() < PrescriptionTransferStatusEnum.OPENED.getStatus() ? Boolean.FALSE : isHidePrescription)
            .reason(StringUtils.defaultIfBlank(prescriptionRespDTO.getInvalidReason(), inquiryRecordVO.getCancelReason()))
            .prescriptionImgUrl(prescriptionRespDTO.getPrescriptionImgUrl())
            .prescriptionPdfUrl(prescriptionRespDTO.getPrescriptionPdfUrl())
            .build();
    }
}
