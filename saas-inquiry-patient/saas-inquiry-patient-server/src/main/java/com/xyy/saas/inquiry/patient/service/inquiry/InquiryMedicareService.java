package com.xyy.saas.inquiry.patient.service.inquiry;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.patient.api.medical.dto.MedicarePersonInsuranceRecordDTO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.BaseInquiryPatientVO;

/**
 * @Author: xucao
 * @DateTime: 2025/7/17 11:52
 * @Description: 问诊医保相关服务
 **/
public interface InquiryMedicareService {

    /**
     * 查询参保人信息
     *
     * @param baseInquiryPatientVO 患者信息
     * @return
     */
    CommonResult<Long> queryPersonInfo(BaseInquiryPatientVO baseInquiryPatientVO);

    /**
     * 查询参保人医保信息
     *
     * @param id
     * @return
     */
    MedicarePersonInsuranceRecordDTO getMedicarePersonInsuranceRecord(Long id);
}
