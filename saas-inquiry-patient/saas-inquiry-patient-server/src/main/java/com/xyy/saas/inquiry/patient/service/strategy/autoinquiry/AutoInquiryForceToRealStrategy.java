package com.xyy.saas.inquiry.patient.service.strategy.autoinquiry;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.drugstore.api.option.InquiryOptionConfigApi;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionGlobalConfigRespDto;
import com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryEnum;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryJudgeNodeEnum;
import com.xyy.saas.inquiry.enums.inquiry.UnableAutoReasonEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2025/2/19 14:28
 * @Description: 自动开方强制走真人问诊配置策略
 */
@Component
public class AutoInquiryForceToRealStrategy extends AutoInquiryStrategy {

    @Resource
    private InquiryOptionConfigApi inquiryOptionConfigApi;

    /**
     * 执行判定是否自动开方方法
     *
     * @param inquiryDto   问诊参数
     * @param globalConfig 全局配置
     */
    @Override
    @TraceNode(node = TraceNodeEnum.AUOTO_INQUIRY_FORCETOREAL ,prefLocation = "inquiryDto.pref")
    public void executeJudge(InquiryRecordDto inquiryDto, InquiryOptionGlobalConfigRespDto globalConfig) {
        InquiryOptionConfigRespDto optionConfigRespDto = inquiryOptionConfigApi.getInquiryOptionConfig(inquiryDto.getTenantDto(), InquiryOptionTypeEnum.PRES_ALL_REAL_PEOPLE_INQUIRY);

        // 未开启强制走真人问诊则直接返回
        if (ObjectUtil.equal(optionConfigRespDto.getPresAllRealPeopleInquiry(), Boolean.TRUE)) {
            // 如果医院是空 则说明配置查询到得是门店，或者区域没有配置医院
            // 如果医院不是空 则说明配置查询到得是区域，校验可选医院在不在配置里
            if (CollUtil.isEmpty(optionConfigRespDto.getPresAllRealPeopleInquiryHospitalPrefs())
                || CollUtil.containsAny(optionConfigRespDto.getPresAllRealPeopleInquiryHospitalPrefs(), inquiryDto.getChoiceHospitalList())) {
                inquiryDto.setAutoInquiry(AutoInquiryEnum.NO.getCode());
                inquiryDto.setUnableAutoReason(UnableAutoReasonEnum.AREA_OR_STORE_FORCE_TO_REAL.getCode());
            }
        }
    }

    @Override
    public AutoInquiryJudgeNodeEnum getAutoInquiryJudgeNodeEnum() {
        return AutoInquiryJudgeNodeEnum.AUTO_INQUIRY_FORCE_TO_REAL_FOR_CONFIGURATION;
    }
}
