package com.xyy.saas.inquiry.patient.controller.admin.statistics;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.patient.controller.admin.statistics.vo.InquiryHomePageStatisticsRespVO;
import com.xyy.saas.inquiry.patient.controller.admin.statistics.vo.InquiryStatisticsReqVO;
import com.xyy.saas.inquiry.patient.service.statistics.InquiryStatisticsService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Tag(name = "管理后台 - 统计记录")
@RestController
@RequestMapping("/patient/inquiry-statistics")
@Validated
public class InquiryStatisticsController {

    @Resource
    private InquiryStatisticsService inquiryStatisticsService;

    @GetMapping("/home-page-statistics")
    @Operation(summary = "首页统计")
    CommonResult<InquiryHomePageStatisticsRespVO> homePageStatistics( InquiryStatisticsReqVO inquiryStatisticsReqVO) {

        return inquiryStatisticsService.homePageStatistics(inquiryStatisticsReqVO);
    }
}
