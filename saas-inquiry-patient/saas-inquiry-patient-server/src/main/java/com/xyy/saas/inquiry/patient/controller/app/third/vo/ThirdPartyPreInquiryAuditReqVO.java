package com.xyy.saas.inquiry.patient.controller.app.third.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.util.List;

/**
 * @Author: xucao
 * @DateTime: 2025/4/28 17:50
 * @Description: 预问诊审核入参
 **/
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ThirdPartyPreInquiryAuditReqVO {

    @Schema(description = "预问诊单号")
    private String pref;

    @Schema(description = "审核状态 1 - 通过 , 2 - 驳回")
    @NotNull(message = "审核状态不能为空")
    private Integer auditStatus;

    @Schema(description = "预问诊单号集合-批量审核时使用")
    private List<String> prefList;
}
