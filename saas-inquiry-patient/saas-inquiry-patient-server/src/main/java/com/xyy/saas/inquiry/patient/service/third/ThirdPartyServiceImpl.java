package com.xyy.saas.inquiry.patient.service.third;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import cn.iocoder.yudao.module.system.api.tenant.TenantThirdAppApi;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantThirdAppReqDto;
import cn.iocoder.yudao.module.system.api.tenant.dto.TenantThirdAppRespDto;
import cn.iocoder.yudao.module.system.enums.common.SexEnum;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.xyy.saas.inquiry.enums.doctor.AuditorTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.BizChannelTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.ThirdPartPrescriptionStatusEnum;
import com.xyy.saas.inquiry.enums.prescription.PrescriptionAuditStatusEnum;
import com.xyy.saas.inquiry.hospital.api.hospital.InquiryHospitalApi;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalReqDto;
import com.xyy.saas.inquiry.hospital.api.hospital.dto.InquiryHospitalRespDto;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionApi;
import com.xyy.saas.inquiry.hospital.api.prescription.InquiryPrescriptionDetailApi;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionDetailQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionDetailRespDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionQueryDTO;
import com.xyy.saas.inquiry.hospital.api.prescription.dto.InquiryPrescriptionRespDTO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyBatchQueryPrescriptionReqVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyGetPrescriptionRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyGetPrescriptionRespVO.ThirdPartyMedicineInfoRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyGetPrescriptionRespVO.ThirdPartyMedicineUsageRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryDetailRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryInfoRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryReqVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryReqVO.ThirdPartyPreInquiryDetailReqDto;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquirySearchReqVO;
import com.xyy.saas.inquiry.patient.convert.third.ThirdPartyPreInquiryConvert;
import com.xyy.saas.inquiry.patient.convert.third.ThirdPartyPreInquiryDetailConvert;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.third.ThirdPartyDrugMatchFailRecordDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.third.ThirdPartyPreInquiryDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.third.ThirdPartyPreInquiryDetailDO;
import com.xyy.saas.inquiry.patient.dal.mysql.inquiry.InquiryRecordMapper;
import com.xyy.saas.inquiry.patient.dal.mysql.third.ThirdPartyDrugMatchFailRecordMapper;
import com.xyy.saas.inquiry.patient.dal.mysql.third.ThirdPartyPreInquiryDetailMapper;
import com.xyy.saas.inquiry.patient.dal.mysql.third.ThirdPartyPreInquiryMapper;
import com.xyy.saas.inquiry.patient.enums.ThirdPartyErrorCodeConstants;
import com.xyy.saas.inquiry.patient.service.patient.InquiryPatientInfoService;
import com.xyy.saas.inquiry.patient.util.BusinessUtil;
import com.xyy.saas.inquiry.pharmacist.api.audit.InquiryPrescriptionAuditApi;
import com.xyy.saas.inquiry.pharmacist.api.audit.dto.InquiryPrescriptionAuditDto;
import com.xyy.saas.inquiry.pharmacist.api.audit.dto.InquiryPrescriptionAuditPageReqDto;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDetailDto;
import com.xyy.saas.inquiry.product.api.search.ProductSearchApi;
import com.xyy.saas.transmitter.api.organ.TransmissionOrganApi;
import com.xyy.saas.transmitter.api.organ.dto.TransmissionOrganDTO;
import jakarta.annotation.Resource;
import java.math.BigDecimal;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.cloud.context.config.annotation.RefreshScope;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;

/**
 * 对接三方服务
 */
@Slf4j
@Service
@RefreshScope
public class ThirdPartyServiceImpl implements ThirdPartyService {

    @Resource
    private ThirdPartyPreInquiryMapper thirdPartyPreInquiryMapper;

    @Resource
    private ThirdPartyPreInquiryDetailMapper thirdPartyPreInquiryDetailMapper;

    @Resource
    private InquiryRecordMapper inquiryRecordMapper;

    @DubboReference
    private InquiryPrescriptionApi inquiryPrescriptionApi;

    @DubboReference
    private InquiryHospitalApi inquiryHospitalApi;

    @DubboReference
    private InquiryPrescriptionDetailApi inquiryPrescriptionDetailApi;

    @DubboReference
    private ProductSearchApi productSearchApi;

    @Resource
    private ThirdPartyDrugMatchFailRecordMapper thirdPartyDrugMatchFailRecordMapper;

    @Resource
    private TenantThirdAppApi tenantThirdAppApi;

    @DubboReference
    private TenantApi tenantApi;

    @Resource
    private TransmissionOrganApi transmissionOrganApi;

    @Resource
    private InquiryPatientInfoService inquiryPatientInfoService;

    @Value("${third.party.page.query.max.count:100}")
    private Integer thirdPartyPageQueryMaxCount;

    @DubboReference
    private InquiryPrescriptionAuditApi inquiryPrescriptionAuditApi;

    public ThirdPartyServiceImpl getSelf() {
        return SpringUtil.getBean(getClass());
    }

    @Override
    public CommonResult<ThirdPartyPreInquiryRespVO> thirdPartyPreInquiry(ThirdPartyPreInquiryReqVO thirdPartyPreInquiryReqVo) {

        // 校验三方app , 校验token携带的ak和sk是否正确 , 是否被禁用
        TenantThirdAppRespDto tenantThirdAppRespDto = this.checkAndGetThirdApp();
        thirdPartyPreInquiryReqVo.setBizChannelType(tenantThirdAppRespDto.getTransmissionOrganId());

        return getSelf().preInquiry(thirdPartyPreInquiryReqVo);
    }

    /**
     * 三方预问诊
     *
     * @param thirdPartyPreInquiryReqVo
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<ThirdPartyPreInquiryRespVO> preInquiry(ThirdPartyPreInquiryReqVO thirdPartyPreInquiryReqVo) {

        // 校验预问诊请求参数
        this.checkPreInquiryParam(thirdPartyPreInquiryReqVo);
        // 获取租户信息
        TenantDto tenantDto = tenantApi.getTenant();
        // 转换DO模型
        ThirdPartyPreInquiryDO thirdPartyPreInquiryDO = ThirdPartyPreInquiryConvert.INSTANCE.convertVO2DO(thirdPartyPreInquiryReqVo, tenantDto);
        // 插入预问诊记录
        thirdPartyPreInquiryMapper.insert(thirdPartyPreInquiryDO);
        // 转换预问诊明细
        List<ThirdPartyPreInquiryDetailDO> thirdPartyPreInquiryDetailDOList = ThirdPartyPreInquiryDetailConvert.INSTANCE.convertDetailDTOs2DOs(thirdPartyPreInquiryDO, thirdPartyPreInquiryReqVo.getDrugList(), tenantDto);
        // 插入预问诊明细
        thirdPartyPreInquiryDetailMapper.insertBatch(thirdPartyPreInquiryDetailDOList);

        return CommonResult.success(ThirdPartyPreInquiryRespVO.builder().preInquiryId(thirdPartyPreInquiryDO.getId()).preInquiryPref(thirdPartyPreInquiryDO.getPref()).build());
    }

    /**
     * 校验三方app , 校验token携带的ak和sk是否正确 , 是否被禁用
     *
     * @return
     */
    private TenantThirdAppRespDto checkAndGetThirdApp() {

        Long thirdAppId = TenantContextHolder.getThirdAppId();
        log.info("[checkAndGetThirdApp] 获取租户三方应用信息, thirdAppId: {}", thirdAppId);

        if (thirdAppId == null || thirdAppId <= 0) {
            throw exception(ThirdPartyErrorCodeConstants.THIRD_PARTY_THIRD_APP_NOT_EXIST);
        }

        TenantThirdAppReqDto tenantThirdAppReqDto = new TenantThirdAppReqDto();
        tenantThirdAppReqDto.setIdList(Lists.newArrayList(thirdAppId));
        List<TenantThirdAppRespDto> tenantThirdAppRespDtoList = tenantThirdAppApi.getListByCondition(tenantThirdAppReqDto);

        if (CollectionUtils.isEmpty(tenantThirdAppRespDtoList)) {
            throw exception(ThirdPartyErrorCodeConstants.THIRD_PARTY_THIRD_APP_NOT_EXIST);
        }

        TenantThirdAppRespDto tenantThirdAppRespDto = tenantThirdAppRespDtoList.getFirst();

        if (!CommonStatusEnum.ENABLE.getStatus().equals(tenantThirdAppRespDto.getStatus())) {
            throw exception(ThirdPartyErrorCodeConstants.THIRD_PARTY_THIRD_APP_IS_DISABLE);
        }

        TransmissionOrganDTO transmissionOrgan = transmissionOrganApi.getTransmissionOrgan(tenantThirdAppRespDto.getTransmissionOrganId());

        if (transmissionOrgan != null && transmissionOrgan.getDisable() != null && transmissionOrgan.getDisable()) {
            throw exception(ThirdPartyErrorCodeConstants.TRANSMISSION_ORGAN_IS_DISABLE);
        }

        return tenantThirdAppRespDto;
    }

    /**
     * 校验预问诊请求参数
     *
     * @param thirdPartyPreInquiryReqVo
     * @return
     */
    private void checkPreInquiryParam(ThirdPartyPreInquiryReqVO thirdPartyPreInquiryReqVo) {

        if (ObjectUtil.equals(BizChannelTypeEnum.MINI_PROGRAM.getCode(), thirdPartyPreInquiryReqVo.getBizChannelType())) {
            return;
        }

        // 从身份证计算年龄和性别
        Integer age = thirdPartyPreInquiryReqVo.getAge();
        Integer sex = thirdPartyPreInquiryReqVo.getSex();
        if (StringUtils.isNotBlank(thirdPartyPreInquiryReqVo.getIdCard())) {
            String ageStr = BusinessUtil.getAgeByIdCard(thirdPartyPreInquiryReqVo.getIdCard());
            age = StringUtils.isNotBlank(ageStr) ? Integer.valueOf(ageStr) : null;
            sex = BusinessUtil.getSexByIdCard(thirdPartyPreInquiryReqVo.getIdCard());
        }

        if (age != null && (age <= 0 || age > 150)) {
            throw exception(ThirdPartyErrorCodeConstants.PRE_INQUIRY_ORDER_AGE_ERROR);
        }

        if (sex != null && SexEnum.UNKNOWN.equals(SexEnum.forValue(thirdPartyPreInquiryReqVo.getSex()))) {
            throw exception(ThirdPartyErrorCodeConstants.PRE_INQUIRY_ORDER_SEX_ERROR);
        }

        if (CollectionUtils.isEmpty(thirdPartyPreInquiryReqVo.getDrugList())) {
            throw exception(ThirdPartyErrorCodeConstants.PRE_INQUIRY_ORDER_MEDICINE_NOT_NULL);
        }

        if (!Arrays.stream(MedicineTypeEnum.ARRAYS).boxed().toList().contains(thirdPartyPreInquiryReqVo.getMedicineType())) {
            throw exception(ThirdPartyErrorCodeConstants.PRE_INQUIRY_ORDER_MEDICINE_TYPE_NOT_NULL);
        }

        for (ThirdPartyPreInquiryDetailReqDto thirdPartyPreInquiryDetailReqDto : thirdPartyPreInquiryReqVo.getDrugList()) {
            // 数量不能为空并且范围在1-999999之间
            if (thirdPartyPreInquiryDetailReqDto.getQuantity() == null
                || thirdPartyPreInquiryDetailReqDto.getQuantity().compareTo(BigDecimal.ZERO) <= 0
                || thirdPartyPreInquiryDetailReqDto.getQuantity().compareTo(new BigDecimal(999999)) > 0) {
                throw exception(ThirdPartyErrorCodeConstants.PRE_INQUIRY_ORDER_QUANTITY_ERROR);
            }
        }

        if (Objects.equals(MedicineTypeEnum.ASIAN_MEDICINE.getCode(), thirdPartyPreInquiryReqVo.getMedicineType())) {

            if (thirdPartyPreInquiryReqVo.getDrugList().size() > 5) {
                throw exception(ThirdPartyErrorCodeConstants.PRE_INQUIRY_ORDER_MEDICINE_TYPE_NOT_EXCEED_FIVE);
            }

        } else if (Objects.equals(MedicineTypeEnum.CHINESE_MEDICINE.getCode(), thirdPartyPreInquiryReqVo.getMedicineType())) {

            if (thirdPartyPreInquiryReqVo.getDrugList().size() > 33) {
                throw exception(ThirdPartyErrorCodeConstants.PRE_INQUIRY_ORDER_MEDICINE_TYPE_NOT_EXCEED_THIRTY_THREE);
            }

            // 全部的药品名称
            Set<String> allDrugNameSet = new HashSet<>();
            // 重复的药品名称
            Set<String> repeatDrugNameSet = new HashSet<>();

            for (ThirdPartyPreInquiryDetailReqDto thirdPartyPreInquiryDetailReqDto : thirdPartyPreInquiryReqVo.getDrugList()) {
                // 如果添加失败，说明该药品名称已经存在，即为重复药品
                if (!allDrugNameSet.add(thirdPartyPreInquiryDetailReqDto.getDrugName())) {
                    repeatDrugNameSet.add(thirdPartyPreInquiryDetailReqDto.getDrugName());
                }
            }

            if (CollectionUtils.isNotEmpty(repeatDrugNameSet)) {
                throw exception(ThirdPartyErrorCodeConstants.PRE_INQUIRY_ORDER_MEDICINE_NAME_NOT_EXCEED_THIRTY_THREE, String.join(",", repeatDrugNameSet));
            }
        }

    }

    /**
     * 三方获取处方详情
     *
     * @param preInquiryPref
     * @return
     */
    @Override
    public CommonResult<ThirdPartyGetPrescriptionRespVO> getPrescription(String preInquiryPref) {

        // 校验三方app , 校验token携带的ak和sk是否正确 , 是否被禁用
        this.checkAndGetThirdApp();

        // 查询三方预问诊详情
        ThirdPartyPreInquiryDO thirdPartyPreInquiryDO = thirdPartyPreInquiryMapper.selectByPref(preInquiryPref);

        if (thirdPartyPreInquiryDO == null) {
            throw exception(ThirdPartyErrorCodeConstants.PRE_INQUIRY_ORDER_IS_DELETE);
        }

        if (StringUtils.isBlank(thirdPartyPreInquiryDO.getInquiryPref())) {
            return CommonResult.success(ThirdPartyGetPrescriptionRespVO.builder().status(ThirdPartPrescriptionStatusEnum.PHYSICIAN_PREPARING_PRESCRIPTION.getCode()).build());
        }

        // 查询问诊记录
        InquiryRecordDO inquiryDO = inquiryRecordMapper.selectOne(InquiryRecordDO::getPref, thirdPartyPreInquiryDO.getInquiryPref());

        if (inquiryDO == null) {
            return CommonResult.success(ThirdPartyGetPrescriptionRespVO.builder().status(ThirdPartPrescriptionStatusEnum.PHYSICIAN_PRESCRIPTION_FAILED.getCode()).failMsg("荷叶问诊医生开方异常,请重新问诊").build());
        }

        // 查询处方
        InquiryPrescriptionRespDTO prescription = inquiryPrescriptionApi.getInquiryPrescription(InquiryPrescriptionQueryDTO.builder().inquiryPref(thirdPartyPreInquiryDO.getInquiryPref()).build());

        if (prescription == null || StringUtils.isBlank(prescription.getPref())) {

            return CommonResult.success(ThirdPartyGetPrescriptionRespVO.builder()
                .status(ThirdPartPrescriptionStatusEnum.transitionInquiryStatusEnum(inquiryDO.getInquiryStatus()).getCode())
                .failMsg(StringUtils.isNotBlank(inquiryDO.getCancelReason()) ? inquiryDO.getCancelReason() : InquiryStatusEnum.fromStatusCode(inquiryDO.getInquiryStatus()).getDesc()).build());
        }

        ThirdPartyGetPrescriptionRespVO thirdPartyGetPrescriptionRespVO = ThirdPartyGetPrescriptionRespVO.builder().prescriptionUrl(prescription.getPrescriptionImgUrl())
            .status(ThirdPartPrescriptionStatusEnum.transitionPrescriptionStatusEnum(prescription.getStatus()).getCode()).failMsg(prescription.getInvalidReason()).physicianName(prescription.getDoctorName())
            .pharmacistName(prescription.getPharmacistName()).diagnosis(StringUtils.join(prescription.getDiagnosisName(), ",")).patientName(prescription.getPatientName()).patientSex(prescription.getPatientSex())
            .patientAge(prescription.getPatientAge()).patientMobile(prescription.getPatientMobile()).outPrescriptionTime(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(prescription.getOutPrescriptionTime()))
            .prescriptionPref(prescription.getPref()).build();

        // 填充患者信息
        InquiryPatientInfoRespVO patientInfoByPref = inquiryPatientInfoService.getPatientInfoByPref(prescription.getPatientPref());
        if (patientInfoByPref != null) {
            thirdPartyGetPrescriptionRespVO.setPatientIdCard(patientInfoByPref.getIdCard());
        }

        // 填充医院信息
        if (StringUtils.isNotBlank(prescription.getHospitalPref())) {
            InquiryHospitalRespDto inquiryHospitalRespDto = inquiryHospitalApi.getInquiryHospitalByPref(prescription.getHospitalPref());
            if (inquiryHospitalRespDto != null) {
                thirdPartyGetPrescriptionRespVO.setHospital(inquiryHospitalRespDto.getName());
            }
        }

        ThirdPartyMedicineUsageRespVO thirdPartyMedicineUsageRespVO = ThirdPartyMedicineUsageRespVO.builder().medicineType(prescription.getMedicineType()).build();
        // 填充中药信息
        if (prescription.getExt() != null) {
            thirdPartyMedicineUsageRespVO.setTcmTotalDosage(Convert.toBigDecimal(prescription.getExt().getTcmTotalDosage()));
            thirdPartyMedicineUsageRespVO.setTcmDailyDosage(Convert.toBigDecimal(prescription.getExt().getTcmDailyDosage()));
            thirdPartyMedicineUsageRespVO.setTcmDailyFrequency(prescription.getExt().getTcmUsage());
            thirdPartyMedicineUsageRespVO.setTcmUsage(prescription.getExt().getTcmDirections());
            thirdPartyMedicineUsageRespVO.setTcmProcessingMethod(prescription.getExt().getTcmProcessingMethod());
        }

        // 查询处方药品详情
        List<InquiryPrescriptionDetailRespDTO> prescriptionDetailList = inquiryPrescriptionDetailApi.getPrescriptionDetail(prescription.getPref());

        if (CollectionUtils.isNotEmpty(prescriptionDetailList)) {
            List<ThirdPartyMedicineInfoRespVO> thirdPartyMedicineInfoRespVOList = prescriptionDetailList.stream().map(
                item -> ThirdPartyMedicineInfoRespVO.builder().drugName(item.getCommonName()).attributeSpecification(item.getAttributeSpecification()).manufacturer(item.getManufacturer()).packageUnit(item.getPackageUnit())
                    .productUnit(item.getSingleUnit()).quantity(item.getQuantity()).useFrequency(item.getUseFrequency()).directions(item.getDirections()).singleDose(item.getSingleDose()).build()).toList();
            thirdPartyMedicineUsageRespVO.setDrugList(thirdPartyMedicineInfoRespVOList);
        }

        thirdPartyGetPrescriptionRespVO.setMedicineUsage(thirdPartyMedicineUsageRespVO);

        return CommonResult.success(thirdPartyGetPrescriptionRespVO);
    }

    /**
     * 获取三方问诊列表
     *
     * @param thirdPartyPreInquirySearchReqVO
     * @return
     */
    @Override
    public CommonResult<PageResult<ThirdPartyPreInquiryInfoRespVO>> getPreInquiryPageList(ThirdPartyPreInquirySearchReqVO thirdPartyPreInquirySearchReqVO) {

        thirdPartyPreInquirySearchReqVO.setTenantId(thirdPartyPreInquirySearchReqVO.getTenantId() == null ? TenantContextHolder.getTenantId() : thirdPartyPreInquirySearchReqVO.getTenantId());

        log.info("ThirdPartyService#getPreInquiryPageList:{}", JSONObject.toJSONString(thirdPartyPreInquirySearchReqVO));

        // 查询预订单列表
        PageResult<ThirdPartyPreInquiryDO> preInquiryPage = thirdPartyPreInquiryMapper.selectPage(thirdPartyPreInquirySearchReqVO);

        if (CollectionUtils.isEmpty(preInquiryPage.getList())) {
            return CommonResult.success(new PageResult<>());
        }

        List<Long> thirdPartyPreInquiryIdList = preInquiryPage.getList().stream().map(ThirdPartyPreInquiryDO::getId).toList();

        // 查询预订单详情列表
        List<ThirdPartyPreInquiryDetailDO> thirdPartyPreInquiryDetailDOList = thirdPartyPreInquiryDetailMapper.selectList(ThirdPartyPreInquiryDetailDO::getThirdPartyPreInquiryId, thirdPartyPreInquiryIdList);
        if (CollectionUtils.isEmpty(thirdPartyPreInquiryDetailDOList)) {
            return CommonResult.success(new PageResult<>());
        }
        Map<Long, List<ThirdPartyPreInquiryDetailDO>> preInquiryMap = thirdPartyPreInquiryDetailDOList.stream().collect(Collectors.groupingBy(ThirdPartyPreInquiryDetailDO::getThirdPartyPreInquiryId));

        // 三方服务商名称map
        Map<Integer, String> transmissionOrganMap = new HashMap<>();
        List<Integer> transmissionOrganIdList = preInquiryPage.getList().stream().map(ThirdPartyPreInquiryDO::getTransmissionOrganId).distinct().toList();
        if (CollectionUtils.isNotEmpty(transmissionOrganIdList)) {
            List<TransmissionOrganDTO> transmissionOrgans = transmissionOrganApi.getTransmissionOrgans(transmissionOrganIdList);
            if (CollectionUtils.isNotEmpty(transmissionOrgans)) {
                transmissionOrganMap = transmissionOrgans.stream().collect(Collectors.toMap(TransmissionOrganDTO::getId, TransmissionOrganDTO::getName, (v1, v2) -> v2));
            }
        }

        PageResult<ThirdPartyPreInquiryInfoRespVO> preInquiryRespPage = ThirdPartyPreInquiryConvert.INSTANCE.convertPage(preInquiryPage);

        for (ThirdPartyPreInquiryInfoRespVO thirdPartyPreInquiryDO : preInquiryRespPage.getList()) {

            if (transmissionOrganMap.containsKey(thirdPartyPreInquiryDO.getTransmissionOrganId())) {
                thirdPartyPreInquiryDO.setTransmissionOrganName(transmissionOrganMap.get(thirdPartyPreInquiryDO.getTransmissionOrganId()));
            }
            if (preInquiryMap.containsKey(thirdPartyPreInquiryDO.getId())) {
                thirdPartyPreInquiryDO.setDrugList(ThirdPartyPreInquiryDetailConvert.INSTANCE.convertDOList2VOList(preInquiryMap.get(thirdPartyPreInquiryDO.getId())));
            }

        }

        return CommonResult.success(preInquiryRespPage);
    }

    /**
     * 获取三方问诊详情
     *
     * @param thirdPartyPreInquiryId
     * @return
     */
    @Override
    public CommonResult<ThirdPartyPreInquiryInfoRespVO> getPreInquiryById(Long thirdPartyPreInquiryId) {

        ThirdPartyPreInquiryDO thirdPartyPreInquiryDO = thirdPartyPreInquiryMapper.selectOne(ThirdPartyPreInquiryDO::getId, thirdPartyPreInquiryId);

        if (thirdPartyPreInquiryDO == null) {
            throw exception(ThirdPartyErrorCodeConstants.PRE_INQUIRY_ORDER_IS_DELETE);
        }

        if (StringUtils.isNotBlank(thirdPartyPreInquiryDO.getInquiryPref())) {
            throw exception(ThirdPartyErrorCodeConstants.PRE_INQUIRY_ORDER_IS_INQUIRY);
        }

        ThirdPartyPreInquiryInfoRespVO thirdPartyPreInquiryInfoRespVO = ThirdPartyPreInquiryConvert.INSTANCE.convertDo2InfoRespVO(thirdPartyPreInquiryDO);

        if (StringUtils.isNotBlank(thirdPartyPreInquiryInfoRespVO.getIdCard())) {
            String ageStr = BusinessUtil.getAgeByIdCard(thirdPartyPreInquiryInfoRespVO.getIdCard());
            thirdPartyPreInquiryInfoRespVO.setAge(StringUtils.isNotBlank(ageStr) ? Integer.valueOf(ageStr) : null);
            thirdPartyPreInquiryInfoRespVO.setSex(BusinessUtil.getSexByIdCard(thirdPartyPreInquiryInfoRespVO.getIdCard()));
        }

        // 当为小程序渠道时,直接返回,小程序药品详情直接从ext里面取
        if (ObjectUtil.equal(thirdPartyPreInquiryInfoRespVO.getTransmissionOrganId(), BizChannelTypeEnum.MINI_PROGRAM.getCode())) {
            return CommonResult.success(thirdPartyPreInquiryInfoRespVO);
        }

        List<ThirdPartyPreInquiryDetailDO> thirdPartyPreInquiryDetailDOList = thirdPartyPreInquiryDetailMapper.selectList(ThirdPartyPreInquiryDetailDO::getThirdPartyPreInquiryId, thirdPartyPreInquiryId);

        if (CollectionUtils.isEmpty(thirdPartyPreInquiryDetailDOList)) {
            return CommonResult.success(thirdPartyPreInquiryInfoRespVO);
        }

        if (Objects.equals(MedicineTypeEnum.ASIAN_MEDICINE.getCode(), thirdPartyPreInquiryDO.getMedicineType())) {

            // 匹配西药
            List<ThirdPartyPreInquiryDetailRespVO> thirdPartyPreInquiryDetailRespVOList = this.matchWesternMedicine(thirdPartyPreInquiryDetailDOList);
            thirdPartyPreInquiryInfoRespVO.setDrugList(thirdPartyPreInquiryDetailRespVOList);

            // 插入药品匹配失败记录
            List<ThirdPartyDrugMatchFailRecordDO> matchFailList = thirdPartyPreInquiryDetailRespVOList.stream().filter(item -> StringUtils.isNotBlank(item.getMatchFailMsg())).map(
                item -> ThirdPartyDrugMatchFailRecordDO.builder().bizId(thirdPartyPreInquiryId.toString()).tenantId(TenantContextHolder.getTenantId()).transmissionOrganId(thirdPartyPreInquiryDO.getTransmissionOrganId()).commonName(item.getCommonName())
                    .attributeSpecification(item.getAttributeSpecification()).barCode(item.getBarCode()).approvalNumber(item.getApprovalNumber()).matchFailMsg(item.getMatchFailMsg()).build()).toList();

            if (CollectionUtils.isNotEmpty(matchFailList)) {
                thirdPartyDrugMatchFailRecordMapper.physicalDeleteByBizIdAndTransmissionOrganId(thirdPartyPreInquiryId.toString(), thirdPartyPreInquiryDO.getTransmissionOrganId(), TenantContextHolder.getTenantId());
                thirdPartyDrugMatchFailRecordMapper.insertBatch(matchFailList);
            }

        } else if (Objects.equals(MedicineTypeEnum.CHINESE_MEDICINE.getCode(), thirdPartyPreInquiryDO.getMedicineType())) {

            // 匹配中药
            List<ThirdPartyPreInquiryDetailRespVO> thirdPartyPreInquiryDetailRespVOList = this.matchChineseMedicine(thirdPartyPreInquiryDetailDOList);
            thirdPartyPreInquiryInfoRespVO.setDrugList(thirdPartyPreInquiryDetailRespVOList);

        }

        return CommonResult.success(thirdPartyPreInquiryInfoRespVO);
    }

    /**
     * 匹配中药
     *
     * @param thirdPartyPreInquiryDetailDOList
     * @return
     */
    private List<ThirdPartyPreInquiryDetailRespVO> matchChineseMedicine(List<ThirdPartyPreInquiryDetailDO> thirdPartyPreInquiryDetailDOList) {

        if (CollectionUtils.isEmpty(thirdPartyPreInquiryDetailDOList)) {
            return Lists.newArrayList();
        }

        List<String> commonNameList = thirdPartyPreInquiryDetailDOList.stream().filter(item -> StringUtils.isNotBlank(item.getCommonName())).map(item -> item.getCommonName().replace("中药饮片", "")).filter(StringUtils::isNotBlank)
            .distinct().toList();

        // 匹配中药
        CommonResult<List<InquiryProductDetailDto>> commonResult = productSearchApi.getProductStandardByProductNamesAndType(commonNameList, MedicineTypeEnum.CHINESE_MEDICINE.getCode());

        Map<String, List<InquiryProductDetailDto>> commonNameMap = new HashMap<>();
        if (commonResult.isSuccess() && CollectionUtils.isNotEmpty(commonResult.getData())) {
            commonNameMap = commonResult.getData().stream().collect(Collectors.groupingBy(InquiryProductDetailDto::getCommonName));
        }

        List<ThirdPartyPreInquiryDetailRespVO> thirdPartyPreInquiryDetailRespVOList = new ArrayList<>();

        for (ThirdPartyPreInquiryDetailDO item : thirdPartyPreInquiryDetailDOList) {
            ThirdPartyPreInquiryDetailRespVO thirdPartyPreInquiryDetailRespVO = ThirdPartyPreInquiryDetailRespVO.builder().commonName(item.getCommonName()).quantity(item.getQuantity()).build();

            thirdPartyPreInquiryDetailRespVOList.add(thirdPartyPreInquiryDetailRespVO);

            if (MapUtils.isEmpty(commonNameMap) || !commonNameMap.containsKey(item.getCommonName()) || commonNameMap.get(item.getCommonName()).size() > 1) {
                continue;
            }

            InquiryProductDetailDto inquiryProductDetailDto = commonNameMap.get(item.getCommonName()).getFirst();

            // 匹配规格并构建结果集
            this.buildThirdPartyPreInquiryDetailRespVO(thirdPartyPreInquiryDetailRespVO, inquiryProductDetailDto);

        }

        return thirdPartyPreInquiryDetailRespVOList;
    }

    /**
     * 匹配西药
     *
     * @param thirdPartyPreInquiryDetailDOList
     * @return
     */
    private List<ThirdPartyPreInquiryDetailRespVO> matchWesternMedicine(List<ThirdPartyPreInquiryDetailDO> thirdPartyPreInquiryDetailDOList) {

        // 69码集合
        List<String> barcodeList = new ArrayList<>();
        // 批准文号集合
        List<String> approvalNoList = new ArrayList<>();

        for (ThirdPartyPreInquiryDetailDO thirdPartyPreInquiryDetailDO : thirdPartyPreInquiryDetailDOList) {
            if (StringUtils.isNotBlank(thirdPartyPreInquiryDetailDO.getBarCode())) {
                barcodeList.add(thirdPartyPreInquiryDetailDO.getBarCode());
                continue;
            }
            if (StringUtils.isNotBlank(thirdPartyPreInquiryDetailDO.getApprovalNumber()) && !Lists.newArrayList("*", "-").contains(thirdPartyPreInquiryDetailDO.getApprovalNumber()) && StringUtils.isNotBlank(
                thirdPartyPreInquiryDetailDO.getAttributeSpecification())) {
                approvalNoList.add(thirdPartyPreInquiryDetailDO.getApprovalNumber());
            }
        }

        // 69码map
        Map<String, List<InquiryProductDetailDto>> barcodeMap = new HashMap<>();
        // 批准文号map
        Map<String, List<InquiryProductDetailDto>> approvalNoMap = new HashMap<>();

        if (CollectionUtils.isNotEmpty(barcodeList)) {
            log.info("getProductStandardListByBarcodeList#barcodeList:{}", barcodeList);
            CommonResult<List<InquiryProductDetailDto>> productStandardListByBarcodeList = productSearchApi.getProductStandardListByBarcodeList(barcodeList, MedicineTypeEnum.ASIAN_MEDICINE.getCode());
            log.info("getProductStandardListByBarcodeList#productStandardListByBarcodeList:{}", productStandardListByBarcodeList);
            if (productStandardListByBarcodeList.isSuccess() && CollectionUtils.isNotEmpty(productStandardListByBarcodeList.getData())) {
                barcodeMap = productStandardListByBarcodeList.getData().stream().filter(item -> StringUtils.isNotBlank(item.getBarCode())).collect(Collectors.groupingBy(InquiryProductDetailDto::getBarCode));
            }
        }

        if (CollectionUtils.isNotEmpty(approvalNoList)) {
            log.info("getProductStandardListByApprovalNoList#approvalNoList:{}", approvalNoList);
            CommonResult<List<InquiryProductDetailDto>> productStandardListByApprovalNoList = productSearchApi.getProductStandardListByApprovalNoList(approvalNoList, MedicineTypeEnum.ASIAN_MEDICINE.getCode());
            log.info("getProductStandardListByApprovalNoList#productStandardListByApprovalNoList:{}", productStandardListByApprovalNoList);
            if (productStandardListByApprovalNoList.isSuccess() && CollectionUtils.isNotEmpty(productStandardListByApprovalNoList.getData())) {
                approvalNoMap = productStandardListByApprovalNoList.getData().stream().filter(item -> StringUtils.isNotBlank(item.getApprovalNumber())).collect(Collectors.groupingBy(InquiryProductDetailDto::getApprovalNumber));
            }
        }

        List<ThirdPartyPreInquiryDetailRespVO> thirdPartyPreInquiryDetailRespVOList = new ArrayList<>();

        for (ThirdPartyPreInquiryDetailDO item : thirdPartyPreInquiryDetailDOList) {

            ThirdPartyPreInquiryDetailRespVO thirdPartyPreInquiryDetailRespVO = ThirdPartyPreInquiryDetailConvert.INSTANCE.convertDO2VO(item);

            thirdPartyPreInquiryDetailRespVOList.add(thirdPartyPreInquiryDetailRespVO);

            if (StringUtils.isNotBlank(item.getBarCode())) {

                List<InquiryProductDetailDto> inquiryProductDetailDtoByBarcodeList = barcodeMap.get(item.getBarCode());

                if (CollectionUtils.isEmpty(inquiryProductDetailDtoByBarcodeList)) {
                    thirdPartyPreInquiryDetailRespVO.setMatchFailMsg("药品69码未匹配到");
                    continue;
                }

                if (inquiryProductDetailDtoByBarcodeList.size() == 1) {
                    this.buildThirdPartyPreInquiryDetailRespVO(thirdPartyPreInquiryDetailRespVO, inquiryProductDetailDtoByBarcodeList.getFirst());
                    continue;
                }

                // 当根据69码找到了多条数据，则根据规格继续匹配
                // 当三方数据没有规格时，则提示用户未匹配到药品
                if (StringUtils.isBlank(item.getAttributeSpecification())) {
                    thirdPartyPreInquiryDetailRespVO.setMatchFailMsg("69码对应多个规格未匹配");
                    continue;
                }

                // 匹配规格并构建结果集
                this.matchingSpecificationAndBuildResult(thirdPartyPreInquiryDetailRespVO, item, inquiryProductDetailDtoByBarcodeList, "69码对应多个规格未匹配");

            } else {

                if (StringUtils.isBlank(item.getApprovalNumber())) {
                    thirdPartyPreInquiryDetailRespVO.setMatchFailMsg("药品无批准文号");
                    continue;
                }

                if (StringUtils.isBlank(item.getAttributeSpecification())) {
                    thirdPartyPreInquiryDetailRespVO.setMatchFailMsg("药品有批准文号但规格未匹配到");
                    continue;
                }

                List<InquiryProductDetailDto> inquiryProductDetailDtoByApprovalNumberList = approvalNoMap.get(item.getApprovalNumber());

                if (CollectionUtils.isEmpty(inquiryProductDetailDtoByApprovalNumberList)) {
                    thirdPartyPreInquiryDetailRespVO.setMatchFailMsg("药品批准文号未匹配到");
                    continue;
                }

                // 匹配规格并构建结果集
                this.matchingSpecificationAndBuildResult(thirdPartyPreInquiryDetailRespVO, item, inquiryProductDetailDtoByApprovalNumberList, "药品有批准文号但规格未匹配到");

            }

        }

        return thirdPartyPreInquiryDetailRespVOList;
    }

    /**
     * 匹配规格并构建结果集
     *
     * @param thirdPartyPreInquiryDetailRespVO
     * @param thirdPartyPreInquiryDetailDO
     * @param inquiryProductDetailDtoList
     * @param matchFailMsg
     */
    private void matchingSpecificationAndBuildResult(ThirdPartyPreInquiryDetailRespVO thirdPartyPreInquiryDetailRespVO, ThirdPartyPreInquiryDetailDO thirdPartyPreInquiryDetailDO, List<InquiryProductDetailDto> inquiryProductDetailDtoList,
        String matchFailMsg) {

        // 是否匹配的上规格
        boolean isMatch = false;

        for (InquiryProductDetailDto inquiryProductDetailDto : inquiryProductDetailDtoList) {

            if (StringUtils.isBlank(inquiryProductDetailDto.getAttributeSpecification())) {
                continue;
            }

            // 对比规格
            isMatch = inquiryProductDetailDto.getAttributeSpecification().equals(thirdPartyPreInquiryDetailDO.getAttributeSpecification());

            if (isMatch) {
                // 填充药品数据
                this.buildThirdPartyPreInquiryDetailRespVO(thirdPartyPreInquiryDetailRespVO, inquiryProductDetailDto);
                break;
            }

        }

        // 当全匹配未匹配上时，则根据规则匹配
        if (!isMatch) {

            // 三方药品规格
            String matchAttributeSpecification = Arrays.stream(thirdPartyPreInquiryDetailDO.getAttributeSpecification().split("\\*")).map(x -> x.replaceAll("[^0-9.]", "")).collect(Collectors.joining("*"));

            for (InquiryProductDetailDto inquiryProductDetailDto : inquiryProductDetailDtoList) {

                if (StringUtils.isBlank(inquiryProductDetailDto.getAttributeSpecification())) {
                    continue;
                }

                // 中台药品规格
                String oneselfAttributeSpecification = Arrays.stream(inquiryProductDetailDto.getAttributeSpecification().split("\\*")).map(x -> x.replaceAll("[^0-9.]", "")).collect(Collectors.joining("*"));

                // 对比规格
                isMatch = matchAttributeSpecification.equals(oneselfAttributeSpecification);

                // 当匹配上规格则直接退出当前循环
                if (isMatch) {
                    // 填充药品数据
                    this.buildThirdPartyPreInquiryDetailRespVO(thirdPartyPreInquiryDetailRespVO, inquiryProductDetailDto);
                    break;
                }

            }
        }

        // 未匹配上规格
        if (!isMatch) {
            thirdPartyPreInquiryDetailRespVO.setMatchFailMsg(matchFailMsg);
        }
    }

    /**
     * 填充药品数据
     *
     * @param thirdPartyPreInquiryDetailRespVO
     * @param inquiryProductDetailDto
     */
    private void buildThirdPartyPreInquiryDetailRespVO(ThirdPartyPreInquiryDetailRespVO thirdPartyPreInquiryDetailRespVO, InquiryProductDetailDto inquiryProductDetailDto) {
        BeanUtil.copyProperties(inquiryProductDetailDto, thirdPartyPreInquiryDetailRespVO, "quantity");
    }

    /**
     * 根据预问诊id删除预问诊记录
     *
     * @param thirdPartyPreInquiryId
     * @return
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<Boolean> deletePreInquiryById(Long thirdPartyPreInquiryId) {

        ThirdPartyPreInquiryDO thirdPartyPreInquiryDO = thirdPartyPreInquiryMapper.selectOne(
            new LambdaQueryWrapperX<ThirdPartyPreInquiryDO>().eqIfPresent(ThirdPartyPreInquiryDO::getTenantId, TenantContextHolder.getTenantId()).eqIfPresent(ThirdPartyPreInquiryDO::getId, thirdPartyPreInquiryId)
                .eq(ThirdPartyPreInquiryDO::getDeleted, false));

        if (thirdPartyPreInquiryDO == null) {
            throw exception(ThirdPartyErrorCodeConstants.PRE_INQUIRY_ORDER_IS_DELETE_NOT_NEED_DELETE);
        }

        if (StringUtils.isNotBlank(thirdPartyPreInquiryDO.getInquiryPref())) {
            throw exception(ThirdPartyErrorCodeConstants.PRE_INQUIRY_ORDER_IS_INQUIRY_NOT_NEED_DELETE);

        }

        ThirdPartyPreInquiryInfoRespVO thirdPartyPreInquiryInfoRespVO = ThirdPartyPreInquiryConvert.INSTANCE.convertDo2InfoRespVO(thirdPartyPreInquiryDO);

        List<ThirdPartyPreInquiryDetailDO> thirdPartyPreInquiryDetailDOList = thirdPartyPreInquiryDetailMapper.selectList(ThirdPartyPreInquiryDetailDO::getThirdPartyPreInquiryId, thirdPartyPreInquiryId);

        if (CollectionUtils.isEmpty(thirdPartyPreInquiryDetailDOList)) {
            return CommonResult.success(true);
        }

        thirdPartyPreInquiryMapper.deleteById(thirdPartyPreInquiryId);
        thirdPartyPreInquiryDetailMapper.delete(ThirdPartyPreInquiryDetailDO::getThirdPartyPreInquiryId, thirdPartyPreInquiryId);
        return null;
    }

    @Override
    public CommonResult<Boolean> getOpenThirdTag() {

        return CommonResult.success(CollUtil.isNotEmpty(tenantThirdAppApi.getByTenantId(TenantContextHolder.getRequiredTenantId())));
    }

    @Override
    public CommonResult<PageResult<ThirdPartyGetPrescriptionRespVO>> getInquiryPrescriptionPageList(ThirdPartyBatchQueryPrescriptionReqVO thirdPartyBatchQueryPrescriptionReqVO) {

        // 校验三方app , 校验token携带的ak和sk是否正确 , 是否被禁用
        TenantThirdAppRespDto tenantThirdAppRespDto = this.checkAndGetThirdApp();

        // 检查并构建入参
        InquiryPrescriptionQueryDTO inquiryPrescriptionQueryDTO = this.checkAndBuildQueryParam(thirdPartyBatchQueryPrescriptionReqVO, tenantThirdAppRespDto);

        PageResult<InquiryPrescriptionRespDTO> inquiryPrescriptionRespDTOPageResult = inquiryPrescriptionApi.getInquiryPrescriptionPage(inquiryPrescriptionQueryDTO);

        if (inquiryPrescriptionRespDTOPageResult == null || CollUtil.isEmpty(inquiryPrescriptionRespDTOPageResult.getList())) {
            return CommonResult.success(PageResult.empty());
        }

        Set<String> hospitalPrefSet = new HashSet<>();
        Set<String> patientPrefSet = new HashSet<>();
        Set<String> prescriptionPrefSet = new HashSet<>();

        for (InquiryPrescriptionRespDTO item : inquiryPrescriptionRespDTOPageResult.getList()) {
            if (StringUtils.isNotBlank(item.getHospitalPref())) {
                hospitalPrefSet.add(item.getHospitalPref());
            }
            if (StringUtils.isNotBlank(item.getPatientPref())) {
                patientPrefSet.add(item.getPatientPref());
            }
            if (StringUtils.isNotBlank(item.getPref())) {
                prescriptionPrefSet.add(item.getPref());
            }
        }

        Map<String, InquiryHospitalRespDto> inquiryHospitalRespDtoMap = new HashMap<>();
        Map<String, InquiryPatientInfoRespVO> inquiryPatientInfoRespVOMap = new HashMap<>();
        Map<String, List<InquiryPrescriptionDetailRespDTO>> inquiryPrescriptionDetailRespDTOListMap = new HashMap<>();
        Map<String, List<InquiryPrescriptionAuditDto>> inquiryPrescriptionAuditPrefMap = new HashMap<>();

        if (CollUtil.isNotEmpty(hospitalPrefSet)) {
            inquiryHospitalRespDtoMap = inquiryHospitalApi.getInquiryHospitals(InquiryHospitalReqDto.builder().inquiryHospitalPrefs(Lists.newArrayList(hospitalPrefSet)).build()).stream()
                .collect(Collectors.toMap(InquiryHospitalRespDto::getPref, Function.identity()));
        }
        if (CollUtil.isNotEmpty(patientPrefSet)) {
            inquiryPatientInfoRespVOMap = inquiryPatientInfoService.getPatientInfoListByCondition(InquiryPatientInfoPageReqVO.builder().patientPrefList(Lists.newArrayList(patientPrefSet)).build()).stream()
                .collect(Collectors.toMap(InquiryPatientInfoRespVO::getPref, Function.identity()));
        }
        if (CollUtil.isNotEmpty(prescriptionPrefSet)) {

            List<String> prescriptionPrefList = Lists.newArrayList(prescriptionPrefSet);

            List<InquiryPrescriptionDetailRespDTO> inquiryPrescriptionDetailListByCondition = inquiryPrescriptionDetailApi.getInquiryPrescriptionDetailListByCondition(InquiryPrescriptionDetailQueryDTO.builder().prescriptionPrefList(prescriptionPrefList).build());
            inquiryPrescriptionDetailRespDTOListMap = Optional.ofNullable(inquiryPrescriptionDetailListByCondition).orElse(Collections.emptyList()).stream().collect(Collectors.groupingBy(InquiryPrescriptionDetailRespDTO::getPrescriptionPref));

            InquiryPrescriptionAuditPageReqDto inquiryPrescriptionAuditPageReqDto = InquiryPrescriptionAuditPageReqDto.builder()
                .prefList(prescriptionPrefList)
                .auditorTypeList(Arrays.asList(AuditorTypeEnum.CHECK.getCode(), AuditorTypeEnum.ALLOCATION.getCode()))
                .auditStatus(PrescriptionAuditStatusEnum.APPROVED.getCode()).build();
            List<InquiryPrescriptionAuditDto> inquiryPrescriptionAuditDtoList = inquiryPrescriptionAuditApi.selectListByCondition(inquiryPrescriptionAuditPageReqDto);
            inquiryPrescriptionAuditPrefMap = Optional.ofNullable(inquiryPrescriptionAuditDtoList).orElse(Collections.emptyList()).stream().collect(Collectors.groupingBy(InquiryPrescriptionAuditDto::getPref));
        }

        List<ThirdPartyGetPrescriptionRespVO> resultList = new ArrayList<>();

        for (InquiryPrescriptionRespDTO prescription : inquiryPrescriptionRespDTOPageResult.getList()) {

            ThirdPartyGetPrescriptionRespVO thirdPartyGetPrescriptionRespVO = ThirdPartyGetPrescriptionRespVO.builder().prescriptionUrl(prescription.getPrescriptionImgUrl())
                .status(ThirdPartPrescriptionStatusEnum.transitionPrescriptionStatusEnum(prescription.getStatus()).getCode()).failMsg(prescription.getInvalidReason()).physicianName(prescription.getDoctorName())
                .pharmacistName(prescription.getPharmacistName()).diagnosis(StringUtils.join(prescription.getDiagnosisName(), ",")).patientName(prescription.getPatientName()).patientSex(prescription.getPatientSex())
                .patientAge(prescription.getPatientAge()).patientMobile(prescription.getPatientMobile()).outPrescriptionTime(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss").format(prescription.getOutPrescriptionTime()))
                .prescriptionPref(prescription.getPref()).build();

            thirdPartyGetPrescriptionRespVO.setHospital(inquiryHospitalRespDtoMap.containsKey(prescription.getHospitalPref())
                ? inquiryHospitalRespDtoMap.get(prescription.getHospitalPref()).getName() : "");
            thirdPartyGetPrescriptionRespVO.setPatientIdCard(inquiryPatientInfoRespVOMap.containsKey(prescription.getPatientPref())
                ? inquiryPatientInfoRespVOMap.get(prescription.getPatientPref()).getIdCard() : "");

            if (inquiryPrescriptionAuditPrefMap.containsKey(prescription.getPref())) {
                List<InquiryPrescriptionAuditDto> inquiryPrescriptionAuditDtoList = inquiryPrescriptionAuditPrefMap.get(prescription.getPref());
                thirdPartyGetPrescriptionRespVO.setCheckPersonName(inquiryPrescriptionAuditDtoList.stream()
                    .filter(item -> item.getAuditorType() != null && AuditorTypeEnum.CHECK.getCode() == item.getAuditorType()).findFirst().map(InquiryPrescriptionAuditDto::getAuditorName).orElse(""));
                thirdPartyGetPrescriptionRespVO.setAllocationPersonName(inquiryPrescriptionAuditDtoList.stream()
                    .filter(item -> item.getAuditorType() != null && AuditorTypeEnum.ALLOCATION.getCode() == item.getAuditorType()).findFirst().map(InquiryPrescriptionAuditDto::getAuditorName).orElse(""));
            }

            ThirdPartyMedicineUsageRespVO thirdPartyMedicineUsageRespVO = ThirdPartyMedicineUsageRespVO.builder().medicineType(prescription.getMedicineType()).build();
            // 填充中药信息
            if (prescription.getExt() != null) {
                thirdPartyMedicineUsageRespVO.setTcmTotalDosage(Convert.toBigDecimal(prescription.getExt().getTcmTotalDosage()));
                thirdPartyMedicineUsageRespVO.setTcmDailyDosage(Convert.toBigDecimal(prescription.getExt().getTcmDailyDosage()));
                thirdPartyMedicineUsageRespVO.setTcmDailyFrequency(prescription.getExt().getTcmUsage());
                thirdPartyMedicineUsageRespVO.setTcmUsage(prescription.getExt().getTcmDirections());
                thirdPartyMedicineUsageRespVO.setTcmProcessingMethod(prescription.getExt().getTcmProcessingMethod());
            }
            if (inquiryPrescriptionDetailRespDTOListMap.containsKey(prescription.getPref())) {
                List<InquiryPrescriptionDetailRespDTO> prescriptionDetailList = inquiryPrescriptionDetailRespDTOListMap.get(prescription.getPref());
                List<ThirdPartyMedicineInfoRespVO> thirdPartyMedicineInfoRespVOList = prescriptionDetailList.stream().map(
                    item -> ThirdPartyMedicineInfoRespVO.builder().drugName(item.getCommonName()).attributeSpecification(item.getAttributeSpecification()).manufacturer(item.getManufacturer()).packageUnit(item.getPackageUnit())
                        .productUnit(item.getSingleUnit()).quantity(item.getQuantity()).useFrequency(item.getUseFrequency()).directions(item.getDirections()).singleDose(item.getSingleDose()).build()).toList();
                thirdPartyMedicineUsageRespVO.setDrugList(thirdPartyMedicineInfoRespVOList);
            }
            thirdPartyGetPrescriptionRespVO.setMedicineUsage(thirdPartyMedicineUsageRespVO);

            resultList.add(thirdPartyGetPrescriptionRespVO);
        }

        return CommonResult.success(new PageResult<>(resultList, inquiryPrescriptionRespDTOPageResult.getTotal()));
    }

    /**
     * 检查并构建入参
     *
     * @param thirdPartyBatchQueryPrescriptionReqVO
     * @return
     */
    private InquiryPrescriptionQueryDTO checkAndBuildQueryParam(ThirdPartyBatchQueryPrescriptionReqVO thirdPartyBatchQueryPrescriptionReqVO, TenantThirdAppRespDto tenantThirdAppRespDto) {

        if (thirdPartyBatchQueryPrescriptionReqVO.getPageSize() > thirdPartyPageQueryMaxCount) {
            throw exception(ThirdPartyErrorCodeConstants.THIRD_PARTY_GET_PRESCRIPTION_PAGE_LIST_EXCEED_MAX_SIZE, thirdPartyPageQueryMaxCount);
        }

        // 判断开具处方开始时间和结束时间不能只填一个
        if (StringUtils.isNotBlank(thirdPartyBatchQueryPrescriptionReqVO.getOutPrescriptionBeginTime()) && StringUtils.isBlank(thirdPartyBatchQueryPrescriptionReqVO.getOutPrescriptionEndTime()) ||
            StringUtils.isBlank(thirdPartyBatchQueryPrescriptionReqVO.getOutPrescriptionBeginTime()) && StringUtils.isNotBlank(thirdPartyBatchQueryPrescriptionReqVO.getOutPrescriptionEndTime())) {
            throw exception(ThirdPartyErrorCodeConstants.THIRD_PARTY_OUT_PRESCRIPTION_TIME_PARAM_ERROR);
        }

        InquiryPrescriptionQueryDTO inquiryPrescriptionQueryDTO = new InquiryPrescriptionQueryDTO();
        inquiryPrescriptionQueryDTO.setPageNum(thirdPartyBatchQueryPrescriptionReqVO.getPageNo());
        inquiryPrescriptionQueryDTO.setPageSize(thirdPartyBatchQueryPrescriptionReqVO.getPageSize());
        inquiryPrescriptionQueryDTO.setTenantId(tenantThirdAppRespDto.getTenantId());
        inquiryPrescriptionQueryDTO.setQueryScene(0);

        // 判断开具处方开始时间和结束时间不能超过90天
        if (StringUtils.isNotBlank(thirdPartyBatchQueryPrescriptionReqVO.getOutPrescriptionBeginTime()) && StringUtils.isNotBlank(thirdPartyBatchQueryPrescriptionReqVO.getOutPrescriptionEndTime())) {
            LocalDateTime beginTime = LocalDateTime.parse(thirdPartyBatchQueryPrescriptionReqVO.getOutPrescriptionBeginTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            LocalDateTime endTime = LocalDateTime.parse(thirdPartyBatchQueryPrescriptionReqVO.getOutPrescriptionEndTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            if (Duration.between(beginTime, endTime).toDays() > 90) {
                throw exception(ThirdPartyErrorCodeConstants.THIRD_PARTY_OUT_PRESCRIPTION_TIME_EXCEED_NINETY_DAY);
            }
            inquiryPrescriptionQueryDTO.setOutPrescriptionTime(new LocalDateTime[]{beginTime, endTime});
        } else {
            // 当时间为空时 , 填充结束时间为当前时间 , 开始时间为当前时间90天前
            LocalDateTime endTime = LocalDateTime.now();
            LocalDateTime beginTime = endTime.minusDays(90);
            inquiryPrescriptionQueryDTO.setOutPrescriptionTime(new LocalDateTime[]{beginTime, endTime});
        }

        return inquiryPrescriptionQueryDTO;
    }
}
