package com.xyy.saas.inquiry.patient.service.patient;

import static cn.iocoder.yudao.framework.common.exception.util.ServiceExceptionUtil.exception;
import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUser;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.INQUIRY_PATIENT_INFO_NOT_EXISTS;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.INQUIRY_PATIENT_LOCK_ACQUIRE_FAIL;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.INQUIRY_PATIENT_QUERY_STRATEGY_NOT_EXISTS;
import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.INQUIRY_PATIENT_SAVE_FAIL;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.enums.CommonStatusEnum;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.collection.CollectionUtils;
import cn.iocoder.yudao.framework.mybatis.core.query.LambdaQueryWrapperX;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.xyy.saas.inquiry.constant.TenantConstant;
import com.xyy.saas.inquiry.drugstore.api.tenant.TenantParamConfigApi;
import com.xyy.saas.inquiry.drugstore.api.tenant.dto.TenantParamConfigDTO;
import com.xyy.saas.inquiry.enums.inquiry.ClientChannelTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.enums.patient.PatientQuerySenceEnum;
import com.xyy.saas.inquiry.enums.tenant.TenantParamConfigTypeEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoQueryReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoRespVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.DiagnosisItemVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.PatientVO;
import com.xyy.saas.inquiry.patient.controller.app.patient.vo.PatientInquiryRecordRespVO;
import com.xyy.saas.inquiry.patient.controller.app.patient.vo.PatientMainSuitVO;
import com.xyy.saas.inquiry.patient.convert.inquiry.InquiryRecordDetailConvert;
import com.xyy.saas.inquiry.patient.convert.patient.PatientInfoConvert;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDetailDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.patient.InquiryPatientInfoDO;
import com.xyy.saas.inquiry.patient.dal.mysql.inquiry.InquiryRecordDetailMapper;
import com.xyy.saas.inquiry.patient.dal.mysql.inquiry.InquiryRecordMapper;
import com.xyy.saas.inquiry.patient.dal.mysql.patient.InquiryPatientInfoMapper;
import com.xyy.saas.inquiry.patient.service.patient.strategy.PatientQueryStrategy;
import com.xyy.saas.inquiry.patient.util.BusinessUtil;
import com.xyy.saas.inquiry.pojo.migration.MigrationPatientDto;
import com.xyy.saas.inquiry.util.RedisUtils;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.UUID;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

/**
 * @Author: xucao
 * @Date: 2024/10/25 13:36
 * @Description: 患者服务实现类，完成针对患者的业务服务
 */
@Service
public class InquiryPatientInfoServiceImpl implements InquiryPatientInfoService {

    @Resource
    private InquiryPatientInfoMapper inquiryPatientInfoMapper;

    @Resource
    private InquiryRecordDetailMapper inquiryRecordDetailMapper;

    @Resource
    private InquiryRecordMapper inquiryRecordMapper;

    @DubboReference
    private TenantParamConfigApi tenantParamConfigApi;

    /**
     * 患者查询策略
     */
    private static final Map<Integer, PatientQueryStrategy> patientQueryParamhandleMap = Maps.newHashMap();

    @Resource
    public void initHandler(List<PatientQueryStrategy> strategies) {
        strategies.forEach(strategy -> patientQueryParamhandleMap.put(strategy.getQueryScene().getCode(), strategy));
    }

    /**
     * 新增或更新问诊患者信息
     *
     * @param patientInfoDO 患者信息
     * @return 保存结果
     */
    @Override
    public InquiryPatientInfoDO saveOrUpdatePatientInfo(InquiryPatientInfoDO patientInfoDO, InquiryRecordDto inquiryDto) {
        // 构建分布式锁的key
        String lockKey = String.format("patient:lock:%s:%s", patientInfoDO.getName(), patientInfoDO.getMobile());
        
        String requestId = UUID.randomUUID().toString();
        long lockExpireTime = 10000; // 10秒锁过期时间
        long maxWaitTime = 5000; // 最大等待时间5秒
        
        // 尝试获取分布式锁
        boolean lockAcquired = RedisUtils.tryLock(lockKey, requestId, lockExpireTime, maxWaitTime);
        if (!lockAcquired) {
            throw exception(INQUIRY_PATIENT_LOCK_ACQUIRE_FAIL);
        }
        
        try {
            // 注册事务同步器，确保锁在事务提交后释放
            boolean lockReleasedBySync = false;
            if (TransactionSynchronizationManager.isSynchronizationActive()) {
                lockReleasedBySync = true;
                TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization() {
                    @Override
                    public void afterCommit() {
                        // 事务提交后释放分布式锁
                        RedisUtils.releaseLock(lockKey, requestId);
                    }
                    
                    @Override
                    public void afterCompletion(int status) {
                        // 无论事务提交还是回滚，都确保锁被释放
                        RedisUtils.releaseLock(lockKey, requestId);
                    }
                });
            }
            
            InquiryPatientInfoDO existPatientInfoDO = null;
            // 根据问诊场景  患者手机号查询当前患者是否存在
            if (isMiniProgramPatient(inquiryDto.getClientChannelType())) {
                // 小程序端发起
                String creator = patientInfoDO.getCreator();
                // 查当前微信用户下有没有这个患者信息
                existPatientInfoDO = inquiryPatientInfoMapper.selectByNameAndMobile(InquiryPatientInfoPageReqVO.builder().creator(creator).name(patientInfoDO.getName()).mobile(patientInfoDO.getMobile()).build());
            } else {
                // 商家端发起，查当前门店是否存在此患者信息
                existPatientInfoDO = inquiryPatientInfoMapper.selectByNameAndMobile(InquiryPatientInfoPageReqVO.builder().name(patientInfoDO.getName()).mobile(patientInfoDO.getMobile()).tenantId(patientInfoDO.getTenantId()).build());
            }
            
            InquiryPatientInfoDO result = saveOrUpdate(patientInfoDO, existPatientInfoDO, inquiryDto.getClientChannelType());
            
            // 如果没有事务同步器，在方法结束前释放锁
            if (!lockReleasedBySync) {
                RedisUtils.releaseLock(lockKey, requestId);
            }
            return result;
        } catch (Exception e) {
            // 发生异常时立即释放锁
            RedisUtils.releaseLock(lockKey, requestId);
            throw exception(INQUIRY_PATIENT_SAVE_FAIL);
        }
    }

    /**
     * 新增或更新患者信息
     *
     * @param patientInfoDO      当前患者信息
     * @param existPatientInfoDO 根据当前患者关键数据查的存量患者信息
     * @return
     */
    private InquiryPatientInfoDO saveOrUpdate(InquiryPatientInfoDO patientInfoDO, InquiryPatientInfoDO existPatientInfoDO, Integer clientChannelType) {
        // 是否小程序问诊，小程序问诊单独处理患者信息
        if (isMiniProgramPatient(clientChannelType)) {
            return handleMiniProgramPatientUpdate(patientInfoDO, existPatientInfoDO);
        }
        if (existPatientInfoDO == null) {
            // 新增患者信息
            inquiryPatientInfoMapper.insert(patientInfoDO);
            return patientInfoDO;
        }
        // 更新患者信息
        updatePatient(patientInfoDO, existPatientInfoDO);
        return existPatientInfoDO;
    }


    /**
     * 更新患者信息
     *
     * @param patientInfoDO
     * @param existPatientInfoDO
     */
    private void updatePatient(InquiryPatientInfoDO patientInfoDO, InquiryPatientInfoDO existPatientInfoDO) {
        BeanUtil.copyProperties(patientInfoDO, existPatientInfoDO, "pref", "id");
        inquiryPatientInfoMapper.updateById(existPatientInfoDO);
    }

    /**
     * 判断当前患者信息是否是小程序端发起的(此代码为AI生成)
     *
     * @param clientChannelType
     * @return
     */
    private boolean isMiniProgramPatient(Integer clientChannelType) {
        return ObjectUtil.equals(ClientChannelTypeEnum.MINI_PROGRAM.getCode(), clientChannelType);
    }

    /**
     * 小程序端发起的更新患者信息(此代码为ai生成)
     *
     * @param patientInfoDO
     * @param existPatientInfoDO
     * @return
     */
    private InquiryPatientInfoDO handleMiniProgramPatientUpdate(InquiryPatientInfoDO patientInfoDO, InquiryPatientInfoDO existPatientInfoDO) {
        // 判断当前微信用户下是否存在此患者
        if (existPatientInfoDO == null) {
            return editPatient(patientInfoDO, null, Boolean.FALSE);
        }
        return onPatientExist(patientInfoDO, existPatientInfoDO);
    }

    /**
     * 当前微信用户下存在此患者
     *
     * @param patientInfoDO
     * @param existPatientInfoDO
     * @return
     */
    private InquiryPatientInfoDO onPatientExist(InquiryPatientInfoDO patientInfoDO, InquiryPatientInfoDO existPatientInfoDO) {
        // 判断患者是否在当前门店下
        if (ObjectUtil.equals(existPatientInfoDO.getTenantId(), patientInfoDO.getTenantId())) {
            // 在当前门店下直接更新患者信息
            updatePatient(patientInfoDO, existPatientInfoDO);
            return existPatientInfoDO;
        }
        return editPatient(patientInfoDO, existPatientInfoDO, Boolean.TRUE);
    }

    /**
     * 修改更新患者
     *
     * @param patientInfoDO
     * @return
     */
    private InquiryPatientInfoDO editPatient(InquiryPatientInfoDO patientInfoDO, InquiryPatientInfoDO existPatientInfoDO, boolean needUpdateExistUser) {
        // 是否需要更新已存在患者信息
        if (needUpdateExistUser) {
            // 将上次患者信息更新为默认用户
            existPatientInfoDO.setCreator(TenantConstant.DEFAULT_USER);
            inquiryPatientInfoMapper.updateById(existPatientInfoDO);
        }
        // 查询当前门店有无此患者
        InquiryPatientInfoDO patient = inquiryPatientInfoMapper.selectByNameAndMobile(InquiryPatientInfoPageReqVO.builder().name(patientInfoDO.getName()).mobile(patientInfoDO.getMobile()).tenantId(patientInfoDO.getTenantId()).build());
        if (patient != null) {
            // 有则更新
            updatePatient(patientInfoDO, patient);
            return patient;
        }
        inquiryPatientInfoMapper.insert(patientInfoDO);
        return patientInfoDO;
    }

    @Override
    public InquiryPatientInfoRespVO getPatientInfoByPref(String patientPref) {
        InquiryPatientInfoDO patientInfoDO = inquiryPatientInfoMapper.selectOne(InquiryPatientInfoDO::getPref, patientPref);
        if (patientInfoDO == null) {
            throw exception(INQUIRY_PATIENT_INFO_NOT_EXISTS);
        }
        // 每次获取患者详情,实时根据身份证号计算年龄
        Optional.ofNullable(BusinessUtil.getAgeByIdCard(patientInfoDO.getIdCard())).ifPresent(patientInfoDO::setAge);
        return PatientInfoConvert.INSTANCE.convertVO(patientInfoDO);
    }

    /**
     * 获得患者信息分页
     *
     * @param pageReqVO 分页查询
     * @return 患者信息分页
     */
    @Override
    public PageResult<InquiryPatientInfoRespVO> getInquiryPatientInfoPage(InquiryPatientInfoQueryReqVO pageReqVO) {
        if (ObjectUtil.isEmpty(pageReqVO.getQueryScene())) {
            pageReqVO.setQueryScene(PatientQuerySenceEnum.TENANT_QUERY.getCode());
        }
        PatientQueryStrategy patientQueryStrategy = patientQueryParamhandleMap.get(pageReqVO.getQueryScene());
        if (ObjectUtil.isEmpty(patientQueryStrategy)) {
            throw exception(INQUIRY_PATIENT_QUERY_STRATEGY_NOT_EXISTS);
        }
        return patientQueryStrategy.query(pageReqVO);
    }

    @Override
    public PageResult<PatientMainSuitVO> getInquiryPatientMainSuitPage(InquiryPatientInfoPageReqVO pageReqVO) {
        // 查询问诊详情
        PageResult<InquiryRecordDetailDO> pageResult = inquiryRecordDetailMapper.selectPage(InquiryRecordDetailConvert.INSTANCE.convertPatientRecord(pageReqVO));
        if (CollUtil.isEmpty(pageResult.getList())) {
            return new PageResult<>();
        }
        // 组装问诊记录
        Map<String, InquiryRecordDO> inquiryRecordDOMap = inquiryRecordMapper.selectListByCondition(
                InquiryRecordPageReqVO.builder().tenantId(pageReqVO.getTenantId()).prefs(CollectionUtils.convertList(pageResult.getList(), InquiryRecordDetailDO::getInquiryPref)).build()).stream()
            .collect(Collectors.toMap(InquiryRecordDO::getPref, Function.identity(), (a, b) -> b));
        List<PatientMainSuitVO> mainSuitVOS = pageResult.getList().stream()
            .map(i -> InquiryRecordDetailConvert.INSTANCE.convertPatientMainSuit(i).setInquiryWay(Optional.ofNullable(inquiryRecordDOMap.get(i.getInquiryPref())).map(InquiryRecordDO::getInquiryWayType).orElse(null))).toList();
        return new PageResult<>(mainSuitVOS, pageResult.getTotal());
    }

    /**
     * 远程审核问诊患者信息新增或保存
     *
     * @param patientInfoDO
     * @return
     */
    @Override
    public InquiryPatientInfoDO saveOrUpdateRemoteAuditInquiryPatent(InquiryPatientInfoDO patientInfoDO) {
        // 根据患者编码查询当前患者
        InquiryPatientInfoDO existPatientInfoDO = inquiryPatientInfoMapper.selectOne(InquiryPatientInfoDO::getPref, patientInfoDO.getPref());
        return saveOrUpdate(patientInfoDO, existPatientInfoDO, ClientChannelTypeEnum.APP.getCode());
    }

    @Override
    public List<InquiryPatientInfoRespVO> getPatientInfoListByCondition(InquiryPatientInfoPageReqVO inquiryPatientInfoPageReqVO) {

        List<InquiryPatientInfoDO> inquiryPatientInfoDOList = inquiryPatientInfoMapper.selectListByCondition(inquiryPatientInfoPageReqVO);

        if (CollUtil.isEmpty(inquiryPatientInfoDOList)) {
            return Lists.newArrayList();
        }

        inquiryPatientInfoDOList.forEach(item -> {
            Optional.ofNullable(BusinessUtil.getAgeByIdCard(item.getIdCard())).ifPresent(item::setAge);
        });

        return PatientInfoConvert.INSTANCE.convertList(inquiryPatientInfoDOList);
    }

    @Override
    public PatientInquiryRecordRespVO getLastInquiryRecord(String patientPref) {

        if (StringUtils.isBlank(patientPref)) {
            return new PatientInquiryRecordRespVO();
        }

        // 组装问诊记录
        InquiryRecordDO inquiryRecordDO = inquiryRecordMapper.selectPatientLastInquiryRecord(TenantContextHolder.getTenantId(), patientPref);
        if (inquiryRecordDO == null) {
            return new PatientInquiryRecordRespVO();
        }
        InquiryRecordDetailDO inquiryRecordDetailDO = inquiryRecordDetailMapper.selectByRecordPref(inquiryRecordDO.getPref());
        if (inquiryRecordDetailDO == null) {
            return new PatientInquiryRecordRespVO();
        }

        Integer medicineType = inquiryRecordDO.getMedicineType();
        if (medicineType == null || (MedicineTypeEnum.ASIAN_MEDICINE.getCode() != medicineType && MedicineTypeEnum.CHINESE_MEDICINE.getCode() != medicineType)) {
            return new PatientInquiryRecordRespVO();
        }

        // 获取中西药带出药品开关
        TenantParamConfigDTO tenantParamConfigDto = tenantParamConfigApi.queryTenantParamConfig(TenantContextHolder.getTenantId(),
            MedicineTypeEnum.ASIAN_MEDICINE.getCode() == medicineType ? TenantParamConfigTypeEnum.INQUIRY_WESTERN_MEDICINE_BRING : TenantParamConfigTypeEnum.INQUIRY_CHINESE_MEDICINE_BRING);

        PatientInquiryRecordRespVO patientInquiryRecordRespVO = new PatientInquiryRecordRespVO();

        PatientVO patientVO = new PatientVO();
        patientVO.setPatientPref(inquiryRecordDetailDO.getPatientPref());
        patientVO.setPatientName(inquiryRecordDetailDO.getPatientName());
        patientVO.setPatientMobile(inquiryRecordDetailDO.getPatientMobile());
        patientVO.setPatientIdCard(inquiryRecordDetailDO.getPatientIdCard());
        patientVO.setPatientAge(inquiryRecordDetailDO.getPatientAge());
        patientVO.setPatientSex(inquiryRecordDetailDO.getPatientSex());
        patientInquiryRecordRespVO.setPatient(patientVO);
        patientInquiryRecordRespVO.setMedicineType(inquiryRecordDO.getMedicineType());
        patientInquiryRecordRespVO.setAllergic(inquiryRecordDetailDO.getAllergic());
        patientInquiryRecordRespVO.setLiverKidneyValue(inquiryRecordDetailDO.getLiverKidneyValue());
        patientInquiryRecordRespVO.setGestationLactationValue(inquiryRecordDetailDO.getGestationLactationValue());

        // 当开关开启时 , 带出对应的病情描述,诊断,药品信息
        if (tenantParamConfigDto != null && StringUtils.isNotBlank(tenantParamConfigDto.getParamValue())
            && CommonStatusEnum.ENABLE.getStatus().equals(Convert.toInt(tenantParamConfigDto.getParamValue()))) {

            patientInquiryRecordRespVO.setInquiryProductInfo(InquiryRecordDetailConvert.INSTANCE.convertDto2VO(inquiryRecordDetailDO.getPreDrugDetail()));
            patientInquiryRecordRespVO.setDiagnosis(this.getDiagnosisItemVOList(inquiryRecordDetailDO.getDiagnosisCode(), inquiryRecordDetailDO.getDiagnosisName()));
            patientInquiryRecordRespVO.setMainSuit(inquiryRecordDetailDO.getMainSuit());
        }

        return patientInquiryRecordRespVO;
    }

    /**
     * 获取诊断信息
     *
     * @param diagnosisCode
     * @param diagnosisName
     * @return
     */
    private List<DiagnosisItemVO> getDiagnosisItemVOList(List<String> diagnosisCode, List<String> diagnosisName) {

        if (CollUtil.isEmpty(diagnosisCode) || CollUtil.isEmpty(diagnosisName) || diagnosisCode.size() != diagnosisName.size()) {
            return Lists.newArrayList();
        }

        List<DiagnosisItemVO> diagnosisItemVOList = new ArrayList<>();

        for (int i = 0; i < diagnosisCode.size(); i++) {
            DiagnosisItemVO diagnosisItemVO = new DiagnosisItemVO();
            diagnosisItemVO.setDiagnosisCode(diagnosisCode.get(i));
            diagnosisItemVO.setDiagnosisName(diagnosisName.get(i));
            diagnosisItemVOList.add(diagnosisItemVO);
        }

        return diagnosisItemVOList;
    }


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void migratePatientInfos(List<MigrationPatientDto> list) {
        if (CollUtil.isEmpty(list)) {
            return;
        }

        List<InquiryPatientInfoDO> doList = PatientInfoConvert.INSTANCE.convertMigrationList(list);

        List<String> tels = list.stream().map(MigrationPatientDto::getTelephone).filter(StringUtils::isNotBlank).toList();

        List<String> names = list.stream().map(MigrationPatientDto::getUserName).filter(StringUtils::isNotBlank).toList();

        if (CollUtil.isEmpty(tels) && CollUtil.isEmpty(names)) {
            // inquiryPatientInfoMapper.insertBatch(doList);
            return;
        }

        Map<String, List<InquiryPatientInfoDO>> map = inquiryPatientInfoMapper.selectList(new LambdaQueryWrapperX<InquiryPatientInfoDO>()
            .eq(InquiryPatientInfoDO::getTenantId, TenantContextHolder.getTenantId())
            .inIfPresent(InquiryPatientInfoDO::getMobile, tels)
            .inIfPresent(InquiryPatientInfoDO::getName, names)).stream().collect(Collectors.groupingBy(a -> a.getName().concat(a.getMobile())));

        List<InquiryPatientInfoDO> infoDOS = doList.stream().filter(a -> !map.containsKey(a.getName().concat(a.getMobile()))).toList();
        if (CollUtil.isNotEmpty(infoDOS)) {
            inquiryPatientInfoMapper.insertBatch(infoDOS);
        }
    }
}
