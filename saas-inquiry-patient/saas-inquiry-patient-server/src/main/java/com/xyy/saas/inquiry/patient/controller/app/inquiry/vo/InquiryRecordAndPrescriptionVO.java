package com.xyy.saas.inquiry.patient.controller.app.inquiry.vo;

import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordDetailRespVO;
import com.xyy.saas.inquiry.pojo.StatusEnumDto;
import io.swagger.v3.oas.annotations.media.Schema;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;

/**
 * 问诊单+详情Dto
 *
 * @Author: cxy
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Builder
@Accessors(chain = true)
public class InquiryRecordAndPrescriptionVO {


    @Schema(description = "问诊+详情", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private InquiryRecordDetailRespVO inquiryRecordDetail;

    //  =========== 转换前端展示字段 =======

    @Schema(description = "问诊处方状态列表", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private List<StatusEnumDto> InquiryPrescriptionStatusList;

    @Schema(description = "当前问诊处方状态", requiredMode = Schema.RequiredMode.REQUIRED, example = "2")
    private StatusEnumDto currentInquiryPrescriptionStatus;

    @Schema(description = "异常原因", requiredMode = Schema.RequiredMode.REQUIRED, example = "不好")
    private String reason;

    @Schema(description = "是否可以取消", requiredMode = Schema.RequiredMode.REQUIRED, example = "不好")
    private boolean canCancel;

    @Schema(description = "处方是否需要隐藏", requiredMode = Schema.RequiredMode.REQUIRED, example = "不好")
    private boolean isHidePrescription;

    // ============ 处方相关字段 ================

    @Schema(description = "处方笺图片url", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    private String prescriptionImgUrl;

    @Schema(description = "处方笺PDFurl", requiredMode = Schema.RequiredMode.REQUIRED, example = "https://www.iocoder.cn")
    private String prescriptionPdfUrl;

}
