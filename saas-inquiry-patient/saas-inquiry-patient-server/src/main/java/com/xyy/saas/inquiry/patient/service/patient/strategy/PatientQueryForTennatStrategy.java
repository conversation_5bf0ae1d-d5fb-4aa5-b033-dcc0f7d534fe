package com.xyy.saas.inquiry.patient.service.patient.strategy;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.enums.patient.PatientQuerySenceEnum;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoQueryReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoRespVO;
import org.springframework.stereotype.Component;

/**
 * @ClassName：PatientQueryStrategy
 * @Author: xucao
 * @Description: 患者查询策略--根据租户id查询
 */
@Component
public class PatientQueryForTennatStrategy extends PatientQueryStrategy {

    @Override
    public PageResult<InquiryPatientInfoRespVO> query(InquiryPatientInfoQueryReqVO pageReqVO) {

        pageReqVO.setTenantIds(handleQueryTenantIds(pageReqVO.getTenantId()));
        pageReqVO.setTenantId(null);
        //
        // if (ObjectUtil.isEmpty(pageReqVO.getTenantId())) {
        // }
        return super.baseQuery(pageReqVO);
    }

    @Override
    public PatientQuerySenceEnum getQueryScene() {
        return PatientQuerySenceEnum.TENANT_QUERY;
    }
}
