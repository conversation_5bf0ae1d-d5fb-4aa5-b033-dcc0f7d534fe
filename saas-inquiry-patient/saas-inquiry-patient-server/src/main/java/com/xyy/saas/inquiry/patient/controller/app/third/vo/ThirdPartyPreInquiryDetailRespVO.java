package com.xyy.saas.inquiry.patient.controller.app.third.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @Author: xucao
 * @DateTime: 2025/4/28 13:24
 * @Description: 三方预问诊单详情
 **/
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "药品信息")
public class ThirdPartyPreInquiryDetailRespVO implements Serializable {
    @Schema(description = "预问诊订单详情id")
    private Long thirdPartyPrePrescriptionDetailId;

    @Schema(description = "商品编码")
    private String pref;

    @Schema(description = "商品名称")
    private String productName;

    @Schema(description = "通用名")
    private String commonName;

    @Schema(description = "规格")
    private String attributeSpecification;

    @Schema(description = "包装单位")
    private String packageUnit;

    @Schema(description = "包装单位")
    private String unitName;

    @Schema(description = "使用频率")
    private String useFrequency;

    @Schema(description = "使用频率值")
    private String useFrequencyValue;

    @Schema(description = "单次剂量")
    private String singleDose;

    @Schema(description = "单次剂量值")
    private String singleDoseValue;

    @Schema(description = "单次剂量单位")
    private String singleUnit;

    @Schema(description = "单次剂量单位值")
    private String singleUnitValue;

    @Schema(description = "用药方法")
    private String directions;

    @Schema(description = "生产厂家")
    private String manufacturer;

    @Schema(description = "商品标准库id")
    private String standardId;

    @Schema(description = "国药准字号")
    private String approvalNumber;

    @Schema(description = "数量")
    private BigDecimal quantity;

    @Schema(description = "69码")
    private String barCode;

    @Schema(description = "剂型")
    private String dosageForm;

    @Schema(description = "匹配失败原因")
    private String matchFailMsg;

}
