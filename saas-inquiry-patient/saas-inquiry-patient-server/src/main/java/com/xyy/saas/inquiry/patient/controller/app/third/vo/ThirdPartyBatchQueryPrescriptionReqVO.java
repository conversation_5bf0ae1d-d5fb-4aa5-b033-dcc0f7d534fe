package com.xyy.saas.inquiry.patient.controller.app.third.vo;

import cn.iocoder.yudao.framework.common.pojo.PageParam;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;

@EqualsAndHashCode(callSuper = true)
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "app侧 - 三方批量查询门店处方单")
public class ThirdPartyBatchQueryPrescriptionReqVO extends PageParam {

    @Schema(description = "开具处方开始时间")
    private String outPrescriptionBeginTime;

    @Schema(description = "开具处方结束时间")
    private String outPrescriptionEndTime;

}
