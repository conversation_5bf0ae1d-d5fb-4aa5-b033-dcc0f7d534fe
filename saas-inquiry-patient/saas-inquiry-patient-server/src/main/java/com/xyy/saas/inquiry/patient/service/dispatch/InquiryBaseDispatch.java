package com.xyy.saas.inquiry.patient.service.dispatch;

import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import lombok.Data;

/**
 * @Author: xucao
 * @Date: 2024/12/20 10:47
 * @Description: 问诊调度派单基类
 */
@Data
public abstract class InquiryBaseDispatch {

    public abstract void execute(InquiryRecordDto inquiryDto);

    protected InquiryBaseDispatch nextNode;

    public final void doExecute(InquiryRecordDto inquiryDto) {
        // 执行当前节点的处理逻辑
        execute(inquiryDto);
        // 获取下一个处理节点
        InquiryBaseDispatch nextHandler = getNextHandler();
        // 如果存在下一个处理节点，则调用其 execute 方法
        if (nextHandler != null) {
            nextHandler.doExecute(inquiryDto);
        }
    }

    public InquiryBaseDispatch getNextHandler() {
        return this.nextNode;
    }

}
