package com.xyy.saas.inquiry.patient.convert.inquiry;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.xyy.saas.inquiry.enums.doctor.DoctorReviewStateEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryWayTypeEnum;
import com.xyy.saas.inquiry.hospital.api.doctor.dto.InquiryDoctorDto;
import com.xyy.saas.inquiry.hospital.api.doctor.review.dto.DoctorReviewDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryQueryDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordDetailPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoPageReqVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.BaseInquiryProductVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.BaseInquiryReqVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.DiagnosisItemVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryReceptionInfoRespVO;
import com.xyy.saas.inquiry.patient.controller.app.patient.vo.PatientMainSuitVO;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryDetailExtDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDetailDO;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.Optional;

/**
 * @Author: cxy
 * @Description: 问诊记录详情转换器
 */
@Mapper
public interface InquiryRecordDetailConvert {

    InquiryRecordDetailConvert INSTANCE = Mappers.getMapper(InquiryRecordDetailConvert.class);

    InquiryRecordDetailPageReqVO convertPatientRecord(InquiryPatientInfoPageReqVO pageReqVO);

    PatientMainSuitVO convertPatientMainSuit(InquiryRecordDetailDO recordDetailDO);

    default InquiryRecordDetailDto convertInquiryVO2DTO(BaseInquiryReqVO baseInquiryReqVO, String inquiryPref) {
        InquiryRecordDetailDto detailDto = convertVO2DTO(baseInquiryReqVO);
        BeanUtil.copyProperties(baseInquiryReqVO.getPatient(), detailDto);
        detailDto.setDiagnosisCode(baseInquiryReqVO.getDiagnosis().stream().map(DiagnosisItemVO::getDiagnosisCode).toList());
        detailDto.setDiagnosisName(baseInquiryReqVO.getDiagnosis().stream().map(DiagnosisItemVO::getDiagnosisName).toList());
        detailDto.setPreDrugDetail(convertProductVO2DTO(baseInquiryReqVO.getInquiryProductInfo()));
        detailDto.setInquiryPref(inquiryPref);
        return detailDto;
    }

    InquiryRecordDetailDto convertVO2DTO(BaseInquiryReqVO baseInquiryReqVO);

    InquiryRecordDetailDO convertDTO2DO(InquiryRecordDetailDto inquiryRecordDetailDto);

    InquiryProductDto convertProductVO2DTO(BaseInquiryProductVO baseInquiryProductVO);

    default InquiryRecordDetailPageReqVO convertQueryDTO2VO(InquiryQueryDto queryDto) {
        InquiryRecordDetailPageReqVO reqVO = new InquiryRecordDetailPageReqVO();
        reqVO.setInquiryPrefList(queryDto.getPrefs());
        return reqVO;
    }

    InquiryRecordDetailDto convertDO2DTO(InquiryRecordDetailDO recordDetailDO);

    default InquiryReceptionInfoRespVO convertDO2VO(String doctorIm, String patientIm, InquiryDoctorDto doctorDto, InquiryRecordDto recordDto, InquiryRecordDetailDO inquiryDetailDo, DoctorReviewDto reviewDto) {
        return InquiryReceptionInfoRespVO.builder().patientImAccount(patientIm).doctorImAccount(doctorIm).doctorHeadImageUrl(Optional.ofNullable(doctorDto).map(InquiryDoctorDto::getPhoto).orElse(""))
            .inquiryStatus(recordDto.getInquiryStatus()).inquiryWayType(recordDto.getInquiryWayType())
            .inquiryCallTime(recordDto.getStartTime() != null && recordDto.getEndTime() != null ? (int) ChronoUnit.SECONDS.between(recordDto.getStartTime(), recordDto.getEndTime()) : null)
            .questionAnswerList(InquiryWayTypeEnum.TEXT.getCode().equals(recordDto.getInquiryWayType()) ? Optional.ofNullable(inquiryDetailDo.extGet()).orElse(new InquiryDetailExtDO()).getQuestionAnswerList() : List.of())
            .reviewsState(ObjectUtil.isEmpty(reviewDto) ? DoctorReviewStateEnum.UN_REVIEW.getState() : DoctorReviewStateEnum.REVIEWED.getState())
            .reviewsContent(ObjectUtil.isEmpty(reviewDto) ? "" : reviewDto.getReviewsContent())
            .satisfactionScore(ObjectUtil.isEmpty(reviewDto) ? null : reviewDto.getSatisfactionScore())
            .satisfactionItem(ObjectUtil.isEmpty(reviewDto) ? null : reviewDto.getSatisfactionItem()).build();
    }

    BaseInquiryProductVO convertDto2VO(InquiryProductDto inquiryProductDto);
}
