package com.xyy.saas.inquiry.patient.dal.dataobject.patient;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 患者信息 DO
 *
 * <AUTHOR>
 */
@TableName("saas_inquiry_patient_info")
@KeySequence("saas_inquiry_patient_info_seq") // 用于 Oracle、PostgreSQL、Kingbase、DB2、H2 数据库的主键自增。如果是 MySQL 等数据库，可不写。
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InquiryPatientInfoDO extends BaseDO {

    /**
     * 主键ID
     */
    @TableId
    private Long id;


    /**
     * 租户id
     */
    private Long tenantId;
    /**
     * 患者编码
     */
    private String pref;
    /**
     * 患者姓名
     */
    private String name;
    /**
     * 患者性别：1 男 2 女
     */
    private Integer sex;
    /**
     * 患者年龄
     */
    private String age;
    /**
     * 出生日期
     */
    private LocalDateTime birthday;
    /**
     * 患者手机号
     */
    private String mobile;
    /**
     * 患者身份证号码
     */
    private String idCard;
    /**
     * 患者来源：0、荷叶问诊   1、智慧脸  2、海典ERP
     */
    private Integer source;
    /**
     * 三方系统患者id
     */
    private String thirdUserId;

}