package com.xyy.saas.inquiry.patient.convert.inquiry;

import com.xyy.saas.inquiry.hospital.api.medicare.dto.MedicarePersonInfoRespDTO;
import com.xyy.saas.inquiry.hospital.api.medicare.dto.MedicarePersonInfoRespDTO.BaseInfo;
import com.xyy.saas.inquiry.hospital.api.medicare.dto.MedicarePersonInfoRespDTO.InsuranceInfo;
import com.xyy.saas.inquiry.hospital.api.medicare.dto.MedicareSignDTO;
import com.xyy.saas.inquiry.patient.api.medical.dto.MedicarePersonInsuranceRecordDTO;
import com.xyy.saas.inquiry.patient.dal.dataobject.medicare.MedicarePersonInsuranceRecordDO;
import com.xyy.saas.inquiry.pojo.transmitter.his.RegistrationTransmitterDTO;
import com.xyy.saas.transmitter.api.transmission.dto.TransmissionReqDTO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

@Mapper
public interface InquiryMedicareConvert {

    InquiryMedicareConvert INSTANCE = Mappers.getMapper(InquiryMedicareConvert.class);

    /**
     * 构建人员信息查询传输数据
     */
    default RegistrationTransmitterDTO buildPersonInfoQueryTransmitterData(String idCard, String hospitalPref) {
        return new RegistrationTransmitterDTO().setIdCardNo(idCard).setHospitalPref(hospitalPref);
    }

    /**
     * 构建参保信息记录
     */
    default MedicarePersonInsuranceRecordDO buildPersonInsuranceRecord(Long tenantId, MedicarePersonInfoRespDTO personInfo) {
        BaseInfo baseInfo = personInfo.getBaseinfo();
        InsuranceInfo insuranceInfo = personInfo.getInsuinfo().getFirst();
        return MedicarePersonInsuranceRecordDO.builder()
            .age(baseInfo.getAge())
            .brdy(baseInfo.getBirthday())
            .certno(baseInfo.getCertno())
            .gend(baseInfo.getGender())
            .insutype(insuranceInfo.getInsuranceType())
            .insuplcAdmdvs(insuranceInfo.getInsuredAreaNo())
            .psnName(baseInfo.getPsnName())
            .psnNo(baseInfo.getPsnNo())
            .tenantId(tenantId)
            .psnType(insuranceInfo.getPsnType())
            .psnInsuDate(insuranceInfo.getBeginDate())
            .psnInsuStas(insuranceInfo.getInsuranceStatus())
            .empName(insuranceInfo.getEmpName())
            .build();
    }


    MedicarePersonInsuranceRecordDTO convert(MedicarePersonInsuranceRecordDO medicarePersonInsuranceRecordDO);
}