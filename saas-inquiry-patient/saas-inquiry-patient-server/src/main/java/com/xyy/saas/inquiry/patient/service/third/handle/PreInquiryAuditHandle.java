package com.xyy.saas.inquiry.patient.service.third.handle;

import static com.xyy.saas.inquiry.patient.enums.ErrorCodeConstants.THIRD_PARTY_PRE_INQUIRY_AUDIT_HAS_AUDIT;

import cn.hutool.core.util.ObjectUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.enums.doctor.AuditStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.BizChannelTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.InquiryStatusEnum;
import com.xyy.saas.inquiry.enums.inquiry.PreInquiryCancelResultTypeEnum;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.DrugstoreInquiryReqVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryAuditReqVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryAuditRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryCancelRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquirySearchReqVO;
import com.xyy.saas.inquiry.patient.convert.third.ThirdPartyPreInquiryConvert;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.third.ThirdPartyPreInquiryDO;
import com.xyy.saas.inquiry.patient.dal.mysql.third.ThirdPartyPreInquiryDetailMapper;
import com.xyy.saas.inquiry.patient.dal.mysql.third.ThirdPartyPreInquiryMapper;
import com.xyy.saas.inquiry.patient.service.inquiry.InquiryImService;
import com.xyy.saas.inquiry.patient.service.inquiry.InquiryService;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

/**
 * @Author: xucao
 * @DateTime: 2025/4/28 19:00
 * @Description: 预问诊单审核处理器
 **/
@Component
@Slf4j
public class PreInquiryAuditHandle {

    @Resource
    private ThirdPartyPreInquiryMapper thirdPartyPreInquiryMapper;

    @Resource
    private ThirdPartyPreInquiryDetailMapper thirdPartyPreInquiryDetailMapper;

    @Resource
    private InquiryImService inquiryImService;

    @Resource
    private InquiryService inquiryService;

    private final Map<Integer, Function<ThirdPartyPreInquiryDO, CommonResult<?>>> auditFunMap = new HashMap<>();

    private PreInquiryAuditHandle getSelf() {
        return SpringUtil.getBean(this.getClass());
    }

    @PostConstruct
    public void init() {
        auditFunMap.put(AuditStatusEnum.APPROVED.getCode(), this::passSingle);
        auditFunMap.put(AuditStatusEnum.REJECTED.getCode(), this::rejectSingle);
    }

    /**
     * 预问诊审核入口方法
     *
     * @param auditReqVO 审核入参
     * @return
     */
    public ThirdPartyPreInquiryAuditRespVO audit(ThirdPartyPreInquiryAuditReqVO auditReqVO) {
        Function<ThirdPartyPreInquiryDO, CommonResult<?>> function = auditFunMap.get(auditReqVO.getAuditStatus());
        if (function == null) {
            return null;
        }
        List<String> failList = new ArrayList<>();
        List<String> prefList = CollectionUtils.isEmpty(auditReqVO.getPrefList()) ? List.of(auditReqVO.getPref()) : auditReqVO.getPrefList();
        Map<String, ThirdPartyPreInquiryDO> preMap = thirdPartyPreInquiryMapper.selectList(ThirdPartyPreInquirySearchReqVO.builder().prefs(prefList).build()).stream()
            .collect(Collectors.toMap(ThirdPartyPreInquiryDO::getPref, Function.identity(), (k1, k2) -> k2));

        String errMsg = "第%s单 【%s】审核失败,失败原因:%s";

        for (int i = 0; i < prefList.size(); i++) {
            String pref = prefList.get(i);
            // 处方审核
            try {
                ThirdPartyPreInquiryDO preInquiryDO = preMap.get(pref);
                if (preInquiryDO == null || !Objects.equals(preInquiryDO.getAuditStatus(), AuditStatusEnum.PENDING.getCode())) {
                    failList.add(String.format(errMsg, (i + 1), pref, THIRD_PARTY_PRE_INQUIRY_AUDIT_HAS_AUDIT.getMsg()));
                    continue;
                }
                CommonResult<?> respVO = function.apply(preInquiryDO);
                if (respVO.isError()) {
                    failList.add(String.format(errMsg, (i + 1), pref, respVO.getMsg()));
                }
            } catch (Exception e) {
                log.error("预问诊单审核失败，预问诊单号：{}", pref, e);
                failList.add(String.format(errMsg, (i + 1), pref, e.getMessage()));
            }
        }
        return ThirdPartyPreInquiryAuditRespVO.builder().successNum(prefList.size() - failList.size()).failNum(failList.size()).failList(failList).build();
    }


    /**
     * 审核单个预问诊单
     *
     * @param preInquiryDO 预问诊记录
     * @return
     */
    public CommonResult<InquiryRespVO> passSingle(ThirdPartyPreInquiryDO preInquiryDO) {
        // 查询预问诊记录
        DrugstoreInquiryReqVO baseInquiryReqVO = preInquiryDO.getExt();
        if (ObjectUtil.isEmpty(baseInquiryReqVO) || ObjectUtil.isEmpty(baseInquiryReqVO.getBaseInquiryReqVO())) {
            return CommonResult.error("预问诊参数有误，无法发起问诊");
        }
        baseInquiryReqVO.getBaseInquiryReqVO().setThirdPartyPreInquiryId(preInquiryDO.getId());
        // 调用去问诊服务
        CommonResult<InquiryRespVO> result = inquiryService.createInquiryRecord(baseInquiryReqVO);
        if (result.isSuccess()) {
            // 修改问诊状态
            thirdPartyPreInquiryMapper.updateById(ThirdPartyPreInquiryDO.builder().id(preInquiryDO.getId()).auditStatus(AuditStatusEnum.APPROVED.getCode()).build());
            //  发送预问诊审核消息
            inquiryImService.sendPreInquiryAuditMessage(preInquiryDO, result.getData(), AuditStatusEnum.APPROVED);
        }
        return result;
    }

    /**
     * 预问诊审核驳回
     *
     * @param preInquiryDO 审核入参
     * @return
     */
    private CommonResult<?> rejectSingle(ThirdPartyPreInquiryDO preInquiryDO) {
        if (ObjectUtil.notEqual(preInquiryDO.getTransmissionOrganId(), BizChannelTypeEnum.MINI_PROGRAM.getCode()) && ObjectUtil.isNotEmpty(preInquiryDO.getInquiryPref())) {
            return CommonResult.error("此信息已发起问诊，不允许删除");
        }
        // 删除预问诊记录
        getSelf().delPreInquiry(preInquiryDO);
        // 发送预问诊审核消息
        inquiryImService.sendPreInquiryAuditMessage(preInquiryDO, new InquiryRespVO(), AuditStatusEnum.REJECTED);
        return CommonResult.success(Boolean.TRUE);
    }

    /**
     * 根据预问诊单号查询预问诊记录
     *
     * @param pref 预问诊单号
     * @return
     */
    public ThirdPartyPreInquiryDO selectByPref(String pref) {
        return thirdPartyPreInquiryMapper.selectByPref(pref);
    }


    /**
     * 物理删除预问诊相关记录
     *
     * @param preInquiryDO
     */
    @Transactional(rollbackFor = Exception.class)
    public void delPreInquiry(ThirdPartyPreInquiryDO preInquiryDO) {
        thirdPartyPreInquiryMapper.delectByPref(preInquiryDO.getPref());
        thirdPartyPreInquiryDetailMapper.deleteByPreInquiryId(preInquiryDO.getId());
    }

    /**
     * 问诊结束处理预问诊信息
     *
     * @param inquiryPref
     */
    public void inquiryEndHandle(String inquiryPref) {
        ThirdPartyPreInquiryDO preInquiryDO = thirdPartyPreInquiryMapper.selectOne(ThirdPartyPreInquiryDO::getInquiryPref, inquiryPref);
        if (ObjectUtil.isEmpty(preInquiryDO)) {
            return;
        }
        // 非小程序问诊直接返回
        if (ObjectUtil.notEqual(preInquiryDO.getTransmissionOrganId(), BizChannelTypeEnum.MINI_PROGRAM.getCode())) {
            return;
        }
        // 删除预问诊记录
        getSelf().delPreInquiry(preInquiryDO);
    }

    /**
     * 取消预问诊但是该预问诊已审核完成发起问诊
     *
     * @param preInquiryDO
     * @return
     */
    public ThirdPartyPreInquiryCancelRespVO preInquiryCancelButApproved(ThirdPartyPreInquiryDO preInquiryDO) {
        ThirdPartyPreInquiryCancelRespVO result = new ThirdPartyPreInquiryCancelRespVO(PreInquiryCancelResultTypeEnum.CANCEL_SUCCESS);
        // 查询问诊单信息
        InquiryRecordDO recordDO = inquiryService.getInquiryByPref(preInquiryDO.getInquiryPref());
        if (ObjectUtil.isEmpty(recordDO)) {
            return result;
        }
        if (ObjectUtil.notEqual(recordDO.getInquiryStatus(), InquiryStatusEnum.QUEUING.getStatusCode()) && ObjectUtil.notEqual(recordDO.getInquiryStatus(), InquiryStatusEnum.INQUIRING.getStatusCode())) {
            return result;
        }
        return ThirdPartyPreInquiryConvert.INSTANCE.convert(preInquiryDO, recordDO);
    }
}
