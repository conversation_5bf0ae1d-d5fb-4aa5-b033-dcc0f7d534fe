package com.xyy.saas.inquiry.patient.service.strategy.autoinquiry;

import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionGlobalConfigRespDto;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryJudgeNodeEnum;
import com.xyy.saas.inquiry.enums.inquiry.UnableAutoReasonEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pojo.condition.ConditionParamDto;
import com.xyy.saas.inquiry.util.ConditionUtil;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/12/23 9:49
 * @Description: 自动开方多诊断判定策略
 */
@Component
public class AutoInquiryMultiDiagnoseStrategy extends AutoInquiryStrategy {

    /**
     * 执行判定是否自动开方方法
     *
     * @param inquiryDto   问诊参数
     * @param globalConfig 全局配置
     */
    @Override
    @TraceNode(node = TraceNodeEnum.AUOTO_INQUIRY_MULTIDIAGNOSE ,prefLocation = "inquiryDto.pref")
    public void executeJudge(InquiryRecordDto inquiryDto, InquiryOptionGlobalConfigRespDto globalConfig) {
        if (!BooleanUtils.isTrue(globalConfig.getPresAutoInquiryToRealForMultiDiagnose())) {
            // 未开启情况下直接返回
            return;
        }
        // 构造条件参数
        ConditionParamDto param = getConditionParamDto(inquiryDto);
        // 是否满足条件
        if (!ConditionUtil.isMatch(param, globalConfig.getPresAutoInquiryToRealForMultiDiagnoseConditions())) {
            // 不满足设定走真人的条件，直接返回
            return;
        }
        // 满足设定走真人的条件，按权重回流真人
        inquiryToRealByRatio(inquiryDto, globalConfig.getPresAutoInquiryToRealForMultiDiagnoseRatio(), UnableAutoReasonEnum.MANY_DIAGNOSES_OR_DIAGNOSIS_TRANS_DEPT);
    }


    @Override
    public AutoInquiryJudgeNodeEnum getAutoInquiryJudgeNodeEnum() {
        return AutoInquiryJudgeNodeEnum.AUTO_INQUIRY_TO_REAL_FOR_MULTI_DIAGNOSE;
    }

    /**
     * 获取条件参数
     *
     * @param inquiryDto 问诊信息
     * @return 条件参数
     */
    private ConditionParamDto getConditionParamDto(InquiryRecordDto inquiryDto) {
        return ConditionParamDto.builder().diagnosisCount(inquiryDto.getInquiryRecordDetailDto().getDiagnosisCode().size()).diagnosisTransDeptCount(inquiryDto.getChoiceDeptList().size()).build();
    }
}
