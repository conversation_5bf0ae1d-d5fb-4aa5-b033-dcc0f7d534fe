package com.xyy.saas.inquiry.patient.controller.app.patient.vo;

import cn.iocoder.yudao.framework.common.validation.Mobile;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * 病情主诉
 *
 * @Author:chenxiaoyi
 * @Date:2024/12/16 19:07
 */
@Data
@Accessors(chain = true)
public class PatientMainSuitVO {

    @Schema(description = "患者 Pref", example = "123456")
    private String patientPref;

    @Schema(description = "患者姓名", example = "张三")
    @NotEmpty(message = "请输入用药人姓名")
    private String patientName;

    @Schema(description = "患者手机号", example = "13800138000")
    @Mobile(message = "用药人手机格式不正确")
    @NotEmpty(message = "请输入用药人手机号")
    private String patientPhone;

    @Schema(description = "患者身份证号(非必填)", example = "110101199001011234")
    private String patientIdCard;

    @Schema(description = "患者年龄", example = "30")
    private Integer patientAge;

    @Schema(description = "患者性别", example = "male")
    private String patientSex;

    @Schema(description = "问诊方式", example = "male")
    private Integer inquiryWay;

    @Schema(description = "历史病情", example = "male")
    private List<String> mainSuit;

    @Schema(description = "创建时间", example = "male")
    private LocalDateTime createTime;


}
