package com.xyy.saas.inquiry.patient.controller.app.inquiry.vo;

import cn.hutool.core.collection.CollUtil;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.AssertTrue;
import jakarta.validation.constraints.NotNull;
import java.util.Objects;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Desc 问诊单请求
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/8/28 下午4:53
 */
@Schema(description = "用户 App - 问诊单 Request VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class DrugstoreInquiryReqVO {

    @Schema(description = "创建人，小程序问诊需记录创建人", example = "1")
    private String creator;

    @Schema(description = "客户端渠类型-0、app  1、pc  2、小程序", example = "0")
    @NotNull(message = "问诊客户端类型不能为空")
    private Integer clientChannelType;

    @Schema(description = "接诊门店租户ID,小程序问诊时必传", example = "1")
    private Long tenantId;

    @Schema(description = "客户端系统类型", example = "android")
    private String clientOsType;

    @Schema(description = "问诊类型- 1图文 2视频", example = "1")
    @NotNull(message = "请选择问诊类型")
    private Integer inquiryWayType;

    @Schema(description = "业务渠道类型-0、荷叶  1、智慧脸  2、海典", example = "0")
    private Integer bizChannelType;

    @Schema(description = "问诊表单信息", example = "{\"patient\":{\"patientName\":\"张美丽\",\"patientMobile\":\"13235528029\",\"patientIdCard\":\"******************\",\"patientAge\":\"26\",\"patientSex\":2},\"mainSuit\":[\"头痛\",\"咽痛\"],\"allergic\":[\"头孢\",\"青霉素\"],\"diagnosis\":[{\"diagnosisCode\":\"001\",\"diagnosisName\":\"感冒\"},{\"diagnosisCode\":\"002\",\"diagnosisName\":\"上呼吸道感染\"}],\"slowDisease\":0,\"liverKidneyValue\":3,\"gestationLactationValue\":3,\"followUp\":0,\"medicineType\":0,\"offlinePrescriptions\":[],\"inquiryProductInfo\":{\"inquiryProductInfos\":[{\"commonName\":\"阿莫西林胶囊\",\"attributeSpecification\":\"0.125g*100s\",\"quantity\":1,\"isClick\":true,\"directions\":\"口服\",\"singleDose\":\"4\",\"singleUnit\":\"粒\",\"useFrequency\":\"3次/天\",\"pref\":\"847778\",\"productName\":\"阿莫西林胶囊\",\"type\":1,\"prescriptionYn\":\"1\",\"specifications\":\"0.125g*100s\",\"manufacturer\":\"中山市力恩普制药有限公司\",\"unitName\":\"盒\",\"useFrequencyValue\":\"THREETIMESONEDAY\",\"singleDoseValue\":\"4\",\"singleUnitValue\":\"粒\",\"pharmacyPref\":\"国药准字H44023590\"}]}}")
    @NotNull(message = "问诊表单信息不能为空")
    @Valid
    private BaseInquiryReqVO baseInquiryReqVO;

    // 当存在问诊消息问答时间时，校验问诊消息问答时间是否为空
    @AssertTrue(message = "问答消息时间不可为空,请尝试重新发起问诊")
    @JsonIgnore
    public boolean isQuestionAnswerTimeValid() {
        if (baseInquiryReqVO == null || baseInquiryReqVO.getExt() == null || CollUtil.isEmpty(baseInquiryReqVO.getExt().getQuestionAnswerList())) {
            return true;
        }

        return baseInquiryReqVO.getExt().getQuestionAnswerList().stream().filter(Objects::nonNull) // 过滤掉空元素
            .allMatch(questionAnswer -> questionAnswer.getAnswerTime() != null && questionAnswer.getQuestionTime() != null);
    }
}
