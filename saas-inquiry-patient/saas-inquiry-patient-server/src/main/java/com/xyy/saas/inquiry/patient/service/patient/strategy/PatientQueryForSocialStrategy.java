package com.xyy.saas.inquiry.patient.service.patient.strategy;

import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.xyy.saas.inquiry.enums.patient.PatientQuerySenceEnum;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoQueryReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoRespVO;
import com.xyy.saas.inquiry.patient.convert.patient.PatientInfoConvert;
import com.xyy.saas.inquiry.patient.dal.dataobject.patient.InquiryPatientInfoDO;
import org.springframework.stereotype.Component;

import java.util.Objects;

import static cn.iocoder.yudao.framework.security.core.util.SecurityFrameworkUtils.getLoginUserId;

/**
 * @Author: xucao
 * @DateTime: 2025/5/6 20:37
 * @Description: 社交用户（小程序）患者查询策略
 **/
@Component
public class PatientQueryForSocialStrategy extends PatientQueryStrategy{

    /**
     * 数据查询
     *
     * @param pageReqVO 请求参数
     * @return 返回参数
     */
    @Override
    public PageResult<InquiryPatientInfoRespVO> query(InquiryPatientInfoQueryReqVO pageReqVO) {
        pageReqVO.setCreator(Objects.requireNonNull(getLoginUserId()).toString());
        return super.baseQuery(pageReqVO);
    }

    /**
     * 获取查询场景
     *
     * @return
     */
    @Override
    public PatientQuerySenceEnum getQueryScene() {
        return PatientQuerySenceEnum.SOCIAL_USER_QUERY;
    }
}
