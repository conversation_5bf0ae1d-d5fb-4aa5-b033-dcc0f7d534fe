package com.xyy.saas.inquiry.patient.controller.app.inquiry.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.io.Serializable;

/**
 * @Author: xucao
 * @Date: 2024/12/27 13:51
 * @Description: 问诊排队信息
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquiryQueueingRespVO implements Serializable {

    @Schema(description = "当前排队位置", example = "5")
    private int index;

    @Schema(description = "前方等待人数", example = "4")
    private int waitNum;

    @Schema(description = "等待时间", example = "2")
    private int waitTime;

    @Schema(description = "问诊状态  0-排队中   1-已取消  2-问诊中  3-问诊结束   4-医生取消开方   5-问诊超时系统取消", example = "0")
    private Integer inquiryStatus;

    @Schema(description = "医生连接状态  0-未连接   1-已连接", example = "0")
    private int doctorConnectStatus;

    @Schema(description = "医生IM账号", example = "1545452645555455254-app")
    private String doctorImAccount;
}
