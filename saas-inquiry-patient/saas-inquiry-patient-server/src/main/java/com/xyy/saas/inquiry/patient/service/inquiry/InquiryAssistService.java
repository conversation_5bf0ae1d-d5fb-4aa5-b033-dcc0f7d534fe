package com.xyy.saas.inquiry.patient.service.inquiry;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.BaseInquiryPatientVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.CheckPatientInfoRespVO;

/**
 * @Author: xucao
 * @DateTime: 2025/7/17 11:01
 * @Description: 问诊流程中辅助服务
 **/
public interface InquiryAssistService {

    /**
     * 购药人信息填写完成后校验
     * @param baseInquiryPatientVO
     * @return
     */
    CommonResult<CheckPatientInfoRespVO> checkPatientInfo(BaseInquiryPatientVO baseInquiryPatientVO);
}
