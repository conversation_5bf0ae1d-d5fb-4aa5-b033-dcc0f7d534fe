package com.xyy.saas.inquiry.patient.controller.app.inquiry.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Author: xucao
 * @Date: 2025/01/20 16:49
 * @Description: 问诊返回结果
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class InquiryRespVO {

    @Schema(description = "当前问诊单号", example = "100084")
    private String inquiryPref;

    @Schema(description = "是否自动开方 0-否  1 是", example = "1")
    private int autoInquiry;

    @Schema(description = "预问诊单id-本次问诊为预问诊时返回", example = "100084")
    private Long preInquiryId;

    @Schema(description = "预问诊编码-本次问诊为预问诊时返回", example = "100084")
    private String preInquiryPref;

    @Schema(description = "本次问诊是否需审核-本次问诊为预问诊时返回true", example = "true")
    @Builder.Default
    private Boolean requireAudit = false;

    @Schema(description = "处方审核是否视频")
    private boolean prescriptionAuditVideo;
}
