package com.xyy.saas.inquiry.patient.controller.app.inquiry.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * @Author: AI Assistant
 * @DateTime: 2025/1/16
 * @Description: 远程审方发起通话请求VO
 **/
@Schema(description = "远程审方发起通话请求参数")
@Data
public class RemoteAuditCallRequestVO {

    @Schema(description = "处方号", example = "P202501160001")
    @NotBlank(message = "处方号不能为空")
    private String prescriptionPref;
}