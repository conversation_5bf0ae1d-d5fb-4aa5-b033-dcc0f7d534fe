package com.xyy.saas.inquiry.patient.convert.inquiry;

import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryComplainSaveReqVO;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryComplainDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: xucao
 * @DateTime: 2025/4/9 16:45
 * @Description: 问诊投诉转换类
 **/
@Mapper
public interface InquiryComplainConvert {
    InquiryComplainConvert INSTANCE = Mappers.getMapper(InquiryComplainConvert.class);

    default InquiryComplainDO convertVO2DO(InquiryComplainSaveReqVO createReqVO, Long complainUserId, Long beComplainUserId) {
        InquiryComplainDO result =  convert(createReqVO);
        result.setComplainUser(complainUserId);
        result.setBeComplainUser(beComplainUserId);
        return  result;
    }

    InquiryComplainDO convert(InquiryComplainSaveReqVO createReqVO);

}
