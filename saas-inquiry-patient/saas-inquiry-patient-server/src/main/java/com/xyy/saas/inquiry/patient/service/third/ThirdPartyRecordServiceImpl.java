package com.xyy.saas.inquiry.patient.service.third;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.api.tenant.TenantThirdAppApi;
import com.xyy.saas.inquiry.patient.controller.admin.third.vo.ThirdPartyDrugMatchFailRecordPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.third.vo.ThirdPartyDrugMatchFailRecordRespVO;
import com.xyy.saas.inquiry.patient.convert.third.ThirdPartyDrugMatchFailRecordConvert;
import com.xyy.saas.inquiry.patient.dal.dataobject.third.ThirdPartyDrugMatchFailRecordDO;
import com.xyy.saas.inquiry.patient.dal.mysql.third.ThirdPartyDrugMatchFailRecordMapper;
import com.xyy.saas.transmitter.api.organ.TransmissionOrganApi;
import com.xyy.saas.transmitter.api.organ.dto.TransmissionOrganDTO;
import jakarta.annotation.Resource;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class ThirdPartyRecordServiceImpl implements ThirdPartyRecordService{

    @Resource
    private ThirdPartyDrugMatchFailRecordMapper thirdPartyDrugMatchFailRecordMapper;

    @Resource
    private TenantThirdAppApi tenantThirdAppApi;

    @Resource
    private TransmissionOrganApi transmissionOrganApi;

    @Override
    public CommonResult<PageResult<ThirdPartyDrugMatchFailRecordRespVO>> pageQueryDrugMatchFailRecord(ThirdPartyDrugMatchFailRecordPageReqVO thirdPartyDrugMatchFailRecordPageReqVO) {

        PageResult<ThirdPartyDrugMatchFailRecordDO> doPageResult = thirdPartyDrugMatchFailRecordMapper.selectPage(thirdPartyDrugMatchFailRecordPageReqVO);

        if (CollectionUtils.isEmpty(doPageResult.getList())) {
            return CommonResult.success(new PageResult<>());
        }

        PageResult<ThirdPartyDrugMatchFailRecordRespVO> voPageResult = ThirdPartyDrugMatchFailRecordConvert.INSTANCE.convertPage(doPageResult);

        // 查询三方服务商名称
        List<Integer> transmissionOrganIdList = voPageResult.getList().stream().map(ThirdPartyDrugMatchFailRecordRespVO::getTransmissionOrganId).distinct().toList();

        // 填充三方服务商名称
        if (CollectionUtils.isNotEmpty(transmissionOrganIdList)) {

            Map<Integer, TransmissionOrganDTO> transmissionOrganDTOMap = transmissionOrganApi.getTransmissionOrgans(transmissionOrganIdList)
                .stream()
                .collect(Collectors.toMap(TransmissionOrganDTO::getId, Function.identity(), (x, y) -> y));

            if (MapUtils.isNotEmpty(transmissionOrganDTOMap)) {

                for (ThirdPartyDrugMatchFailRecordRespVO item : voPageResult.getList()) {

                    if (transmissionOrganDTOMap.containsKey(item.getTransmissionOrganId())) {
                        item.setTransmissionOrganName(transmissionOrganDTOMap.get(item.getTransmissionOrganId()).getName());
                    }
                }
            }
        }

        return CommonResult.success(voPageResult);
    }
}
