package com.xyy.saas.inquiry.patient.service.inquiry;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryRespVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.RemoteAuditInquiryReqVO;

/**
 * @Author: xucao
 * @DateTime: 2025/4/22 11:34
 * @Description: 远程审方问诊服务相关接口
 **/
public interface RemoteAuditInquiryService {

    /**
     * 远程问诊(创建问诊单)
     * @param remoteAuditInquiryReqVO
     * @return
     */
    CommonResult<InquiryRespVO> createInquiry(RemoteAuditInquiryReqVO remoteAuditInquiryReqVO);
}
