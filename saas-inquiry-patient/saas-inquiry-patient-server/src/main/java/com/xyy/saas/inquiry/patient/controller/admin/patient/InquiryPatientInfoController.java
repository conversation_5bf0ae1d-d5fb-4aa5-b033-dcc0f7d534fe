/*
package com.xyy.saas.inquiry.patient.controller.admin.patient;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoRespVO;
import com.xyy.saas.inquiry.patient.service.patient.InquiryPatientInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;


@Tag(name = "管理后台 - 患者信息")
@RestController
@RequestMapping("/patient/patient-info")
@Validated
public class InquiryPatientInfoController {

    @Resource
    private InquiryPatientInfoService inquiryPatientInfoService;


    @GetMapping("/page")
    @Operation(summary = "获得患者信息分页")
    @PreAuthorize("@ss.hasPermission('saas:inquiry-patient-info:query')")
    public CommonResult<PageResult<InquiryPatientInfoRespVO>> getInquiryPatientInfoPage(@Valid InquiryPatientInfoPageReqVO pageReqVO) {
        return success(inquiryPatientInfoService.getInquiryPatientInfoPage(pageReqVO));
    }
}*/
