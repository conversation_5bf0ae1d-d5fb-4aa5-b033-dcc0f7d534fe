package com.xyy.saas.inquiry.patient.controller.app.third.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 三方预问诊返参对象
 *
 * <AUTHOR>
 * @Date 6/11/24 11:02 AM
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "app侧 - 三方预问诊返回对象")
public class ThirdPartyPreInquiryRespVO {

    @Schema(description = "预问诊id")
    private Long preInquiryId;

    @Schema(description = "预问诊编码")
    private String preInquiryPref;

}
