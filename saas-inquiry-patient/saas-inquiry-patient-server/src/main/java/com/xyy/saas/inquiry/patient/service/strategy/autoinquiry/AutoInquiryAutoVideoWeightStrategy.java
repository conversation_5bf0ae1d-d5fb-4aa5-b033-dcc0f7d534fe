package com.xyy.saas.inquiry.patient.service.strategy.autoinquiry;

import cn.hutool.core.util.ObjectUtil;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.drugstore.api.option.InquiryOptionConfigApi;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionConfigRespDto;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionGlobalConfigRespDto;
import com.xyy.saas.inquiry.drugstore.enums.InquiryOptionTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryEnum;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryJudgeNodeEnum;
import com.xyy.saas.inquiry.enums.inquiry.UnableAutoReasonEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.util.MathUtil;
import jakarta.annotation.Resource;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;

/**
 * @Author: xucao
 * @Date: 2024/12/23 9:53
 * @Description: 视频问诊自动开方权重判定
 */
@Component
public class AutoInquiryAutoVideoWeightStrategy extends AutoInquiryStrategy {

    @Resource
    private InquiryOptionConfigApi inquiryOptionConfigApi;

    /**
     * 执行判定是否自动开方方法
     *
     * @param inquiryDto   问诊参数
     * @param globalConfig 全局配置
     */
    @Override
    @TraceNode(node = TraceNodeEnum.AUOTO_INQUIRY_AUTOVIDEOWEIGHT ,prefLocation = "inquiryDto.pref")
    public void executeJudge(InquiryRecordDto inquiryDto, InquiryOptionGlobalConfigRespDto globalConfig) {
        // 非视频问诊直接return
        if (!inquiryDto.isVideoInquiry()) {
            return;
        }
        // 查询录频问诊配置--顺序为  门店  ->  区域
        InquiryOptionConfigRespDto config = inquiryOptionConfigApi.getInquiryOptionConfig(inquiryDto.getTenantDto(), InquiryOptionTypeEnum.PROC_VIDEO_INQUIRY);
        // 未开通  记录原因   直接返回
        if (ObjectUtil.notEqual(config.getProcVideoInquiry(), Boolean.TRUE)) {
            inquiryDto.setAutoInquiry(AutoInquiryEnum.NO.getCode());
            inquiryDto.setUnableAutoReason(UnableAutoReasonEnum.STORE_OR_AREA_NOT_ENABLED_VIDEO_AUTO_INQUIRY.getCode());
            return;
        }
        // 判断权重，本次是否走自动开方，权重满足，则走自动
        // 如果不走自动开方则记录不走自动开方原因
        if (!ObjectUtils.isEmpty(config.getProcVideoInquiryRatio()) && MathUtil.generateResult(config.getProcVideoInquiryRatio())) {
            // 满足比例，默认走自动开方，直接return
            return;
        }
        inquiryDto.setAutoInquiry(AutoInquiryEnum.NO.getCode());
        inquiryDto.setUnableAutoReason(UnableAutoReasonEnum.VIDEO_AUTO_INQUIRY_WEIGHT_NOT_MATCH.getCode());
    }

    @Override
    public AutoInquiryJudgeNodeEnum getAutoInquiryJudgeNodeEnum() {
        return AutoInquiryJudgeNodeEnum.AUTO_INQUIRY_TO_REAL_FOR_AUTO_VIDEO_WEIGHT;
    }
}
