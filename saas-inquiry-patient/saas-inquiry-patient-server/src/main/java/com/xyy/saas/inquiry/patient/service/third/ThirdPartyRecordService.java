package com.xyy.saas.inquiry.patient.service.third;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.patient.controller.admin.third.vo.ThirdPartyDrugMatchFailRecordPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.third.vo.ThirdPartyDrugMatchFailRecordRespVO;

/**
 * 对接三方服务服务
 */
public interface ThirdPartyRecordService {

    /**
     * 分页查询药品匹配失败记录
     * @param thirdPartyDrugMatchFailRecordPageReqVO
     * @return
     */
    CommonResult<PageResult<ThirdPartyDrugMatchFailRecordRespVO>> pageQueryDrugMatchFailRecord(ThirdPartyDrugMatchFailRecordPageReqVO thirdPartyDrugMatchFailRecordPageReqVO);
}
