package com.xyy.saas.inquiry.patient.controller.app.inquiry.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @Author: AI Assistant
 * @DateTime: 2025/1/16
 * @Description: 用户通话响应VO
 **/
@Schema(description = "用户通话响应参数")
@Data
public class RemoteAuditCallResponseVO {

    @Schema(description = "处方号", example = "P202501160001")
    @NotBlank(message = "处方号不能为空")
    private String prescriptionPref;

    @Schema(description = "通话结果：1-接听，0-挂断", example = "1")
    @NotNull(message = "通话结果不能为空")
    private Integer callResult;
}