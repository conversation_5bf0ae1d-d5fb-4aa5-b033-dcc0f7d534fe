package com.xyy.saas.inquiry.patient.service.patient.strategy;

import cn.hutool.core.collection.CollUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import cn.iocoder.yudao.module.system.api.tenant.TenantApi;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.xyy.saas.inquiry.enums.patient.PatientQuerySenceEnum;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoQueryReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoRespVO;
import com.xyy.saas.inquiry.patient.convert.patient.PatientInfoConvert;
import com.xyy.saas.inquiry.patient.dal.dataobject.patient.InquiryPatientInfoDO;
import com.xyy.saas.inquiry.patient.dal.mysql.patient.InquiryPatientInfoMapper;
import com.xyy.saas.inquiry.pojo.patient.PatientSimpleDTO;
import jakarta.annotation.Resource;
import java.util.Collections;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @ClassName：PatientQueryStrategy
 * @Author: xucao
 * @Description: 患者查询场景
 */
@Component
public abstract class PatientQueryStrategy {

    @Resource
    private InquiryPatientInfoMapper inquiryPatientInfoMapper;

    @Autowired
    private TenantApi tenantApi;

    /**
     * 数据查询
     *
     * @param pageReqVO 请求参数
     * @return 返回参数
     */
    public abstract PageResult<InquiryPatientInfoRespVO> query(InquiryPatientInfoQueryReqVO pageReqVO);


    /**
     * 获取查询场景
     *
     * @return
     */
    public abstract PatientQuerySenceEnum getQueryScene();

    /**
     * 数据转换
     *
     * @param pageResult
     * @return
     */
    public PageResult<InquiryPatientInfoRespVO> handleConvert(IPage<PatientSimpleDTO> pageResult) {
        if (pageResult == null || pageResult.getRecords() == null || pageResult.getRecords().isEmpty()) {
            return PageResult.empty();
        }
        PageResult<InquiryPatientInfoRespVO> result = new PageResult<>();
        List<String> patientPrefs = pageResult.getRecords().stream().map(PatientSimpleDTO::getPref).distinct().toList();
        if (patientPrefs.isEmpty()) {
            return result;
        }
        PageResult<InquiryPatientInfoDO> patientPage = inquiryPatientInfoMapper.selectPage(InquiryPatientInfoPageReqVO.builder().patientPrefList(patientPrefs).build());
        return new PageResult<>(PatientInfoConvert.INSTANCE.convertList(patientPage.getList()), pageResult.getTotal());
    }

    /**
     * 通用基础查询方法
     *
     * @param pageReqVO
     * @return
     */
    public PageResult<InquiryPatientInfoRespVO> baseQuery(InquiryPatientInfoQueryReqVO pageReqVO) {
        InquiryPatientInfoPageReqVO req = PatientInfoConvert.INSTANCE.convertQueryVO(pageReqVO);
        PageResult<InquiryPatientInfoDO> pageResult = inquiryPatientInfoMapper.selectPage(req);
        return PatientInfoConvert.INSTANCE.convertPage(pageResult);
    }

    /**
     * 如果前端传入了 tenantId 就用前端传入的 tenantId， 如果前端没传入，获取当前登录门店下所有连锁门店的，如果为空,用当前登录用户所属的租户id
     *
     * @param tenantId
     * @return
     */
    List<Long> handleQueryTenantIds(Long tenantId) {
        if (tenantId != null) {
            return Collections.singletonList(tenantId);
        }
        List<Long> tenantIds = tenantApi.getTenantIdsByHeadId();
        return CollUtil.isEmpty(tenantIds) ? Collections.singletonList(TenantContextHolder.getRequiredTenantId()) : tenantIds;
    }
}
