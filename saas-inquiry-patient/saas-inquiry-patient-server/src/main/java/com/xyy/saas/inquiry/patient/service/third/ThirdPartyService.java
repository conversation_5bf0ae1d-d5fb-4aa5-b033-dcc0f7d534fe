package com.xyy.saas.inquiry.patient.service.third;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyBatchQueryPrescriptionReqVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyGetPrescriptionRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryAuditReqVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryAuditRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryInfoRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryReqVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquirySearchReqVO;
import jakarta.validation.Valid;

/**
 * 对接三方服务
 */
public interface ThirdPartyService {

    /**
     * 三方预问诊
     *
     * @param thirdPartyPreInquiryReqVo
     * @return
     */
    CommonResult<ThirdPartyPreInquiryRespVO> thirdPartyPreInquiry(ThirdPartyPreInquiryReqVO thirdPartyPreInquiryReqVo);

    /**
     * 三方预问诊
     *
     * @param thirdPartyPreInquiryReqVo
     * @return
     */
    CommonResult<ThirdPartyPreInquiryRespVO> preInquiry(ThirdPartyPreInquiryReqVO thirdPartyPreInquiryReqVo);

    /**
     * 三方获取处方详情
     *
     * @param preInquiryPref
     * @return
     */
    CommonResult<ThirdPartyGetPrescriptionRespVO> getPrescription(String preInquiryPref);

    /**
     * 获取三方问诊列表
     *
     * @param thirdPartyPreInquirySearchReqVO
     * @return
     */
    CommonResult<PageResult<ThirdPartyPreInquiryInfoRespVO>> getPreInquiryPageList(ThirdPartyPreInquirySearchReqVO thirdPartyPreInquirySearchReqVO);

    /**
     * 获取三方问诊详情
     *
     * @param thirdPartyPreInquiryId
     * @return
     */
    CommonResult<ThirdPartyPreInquiryInfoRespVO> getPreInquiryById(Long thirdPartyPreInquiryId);

    /**
     * 根据预问诊id删除预问诊记录
     *
     * @param thirdPartyPreInquiryId
     * @return
     */
    CommonResult<Boolean> deletePreInquiryById(Long thirdPartyPreInquiryId);

    /**
     * 获取开通三方标识
     *
     * @return
     */
    CommonResult<Boolean> getOpenThirdTag();

    /**
     * 三方批量查询门店处方单
     *
     * @param thirdPartyBatchQueryPrescriptionReqVO
     * @return
     */
    CommonResult<PageResult<ThirdPartyGetPrescriptionRespVO>> getInquiryPrescriptionPageList(ThirdPartyBatchQueryPrescriptionReqVO thirdPartyBatchQueryPrescriptionReqVO);
}
