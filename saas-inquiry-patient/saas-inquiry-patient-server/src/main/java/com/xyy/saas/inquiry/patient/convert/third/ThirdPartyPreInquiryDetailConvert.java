package com.xyy.saas.inquiry.patient.convert.third;

import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryDetailRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryReqVO.ThirdPartyPreInquiryDetailReqDto;
import com.xyy.saas.inquiry.patient.dal.dataobject.third.ThirdPartyPreInquiryDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.third.ThirdPartyPreInquiryDetailDO;
import com.xyy.saas.inquiry.pojo.TenantDto;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;
import java.util.List;

/**
 * 三方预问诊药品详情转换对象
 */
@Mapper
public interface ThirdPartyPreInquiryDetailConvert {

    ThirdPartyPreInquiryDetailConvert INSTANCE = Mappers.getMapper(ThirdPartyPreInquiryDetailConvert.class);

    List<ThirdPartyPreInquiryDetailRespVO> convertDOList2VOList(List<ThirdPartyPreInquiryDetailDO> thirdPartyPreInquiryDetailDOList);

    ThirdPartyPreInquiryDetailRespVO convertDO2VO(ThirdPartyPreInquiryDetailDO thirdPartyPreInquiryDetailDOList);

    default List<ThirdPartyPreInquiryDetailDO> convertDetailDTOs2DOs(ThirdPartyPreInquiryDO thirdPartyPreInquiryDO, List<ThirdPartyPreInquiryDetailReqDto> detailReqDto, TenantDto tenantDto) {
        return detailReqDto.stream().map(item -> ThirdPartyPreInquiryDetailDO.builder().tenantId(tenantDto.getId()).thirdPartyPreInquiryId(thirdPartyPreInquiryDO.getId()).commonName(item.getDrugName()).packageUnit(item.getProductUnit())
            .attributeSpecification(item.getAttributeSpecification()).quantity(item.getQuantity()).barCode(item.getBarCode()).manufacturer(item.getManufacturer()).approvalNumber(item.getMedicinesQuasiName()).build()).toList();
    }

}
