package com.xyy.saas.inquiry.patient.convert.inquiry;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.common.util.object.BeanUtils;
import com.xyy.saas.inquiry.enums.inquiry.InquiryStatusEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryQueryDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDetailDto;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordDetailRespVO;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordRespVO;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordSaveReqVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.BaseInquiryReqVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.DrugstoreInquiryReqVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryQueueingRespVO;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.inquiry.InquiryRecordDetailDO;
import com.xyy.saas.inquiry.patient.dal.dataobject.patient.InquiryPatientInfoDO;
import com.xyy.saas.inquiry.pojo.TenantDto;
import com.xyy.saas.inquiry.util.PrefUtil;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.factory.Mappers;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

/**
 * @Desc 患者问诊记录转换
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/9/2 下午1:56
 */
@Mapper
public interface InquiryRecordConvert {

    InquiryRecordConvert INSTANCE = Mappers.getMapper(InquiryRecordConvert.class);

    default InquiryRecordDto convertPreCheck(DrugstoreInquiryReqVO drugstoreInquiryReqVO) {
        return getInquiryRecordDto(drugstoreInquiryReqVO, null);
    }

    default InquiryRecordDto convert(DrugstoreInquiryReqVO drugstoreInquiryReqVO) {
        return getInquiryRecordDto(drugstoreInquiryReqVO, PrefUtil.getInquiryPref());
    }

    @NotNull
    private static InquiryRecordDto getInquiryRecordDto(DrugstoreInquiryReqVO drugstoreInquiryReqVO, String pref) {
        InquiryRecordDto inquiryRecordDto = BeanUtil.copyProperties(drugstoreInquiryReqVO, InquiryRecordDto.class);
        BaseInquiryReqVO baseInquiryReqVO = drugstoreInquiryReqVO.getBaseInquiryReqVO();
        BeanUtils.copyProperties(baseInquiryReqVO.getPatient(), inquiryRecordDto);
        inquiryRecordDto.setMedicineType(baseInquiryReqVO.getMedicineType());
        inquiryRecordDto.setThirdPartyPreInquiryId(baseInquiryReqVO.getThirdPartyPreInquiryId());
        inquiryRecordDto.setPref(pref);
        inquiryRecordDto.setInquiryRecordDetailDto(InquiryRecordDetailConvert.INSTANCE.convertInquiryVO2DTO(baseInquiryReqVO, inquiryRecordDto.getPref()));
        return inquiryRecordDto;
    }

    default InquiryRecordDO initConvertVO2DO(InquiryRecordSaveReqVO recordSaveReqVO, InquiryPatientInfoDO patientInfoDO) {
        InquiryRecordDO inquiryRecordDO = initInquiryRecordVO2DO(recordSaveReqVO);
        inquiryRecordDO.setPatientPref(patientInfoDO.getPref());
        inquiryRecordDO.setPref(PrefUtil.getInquiryPref());
        return inquiryRecordDO;
    }

    InquiryRecordDO initInquiryRecordVO2DO(InquiryRecordSaveReqVO recordSaveReqVO);

    default InquiryRecordDetailDO convertVO2DetailDO(InquiryRecordSaveReqVO createReqVO, InquiryRecordDO inquiryRecord) {
        InquiryRecordDetailDO detailDO = convertVO2DetailDO(createReqVO);
        detailDO.setInquiryPref(inquiryRecord.getPref());
        detailDO.setPatientPref(inquiryRecord.getPatientPref());
        return detailDO;
    }

    InquiryRecordDetailDO convertVO2DetailDO(InquiryRecordSaveReqVO createReqVO);


    default InquiryRecordDetailRespVO convertDO2VOWithRemainingTime(InquiryRecordDO recordDO, int outTime, TenantDto tenantDto) {
        InquiryRecordDetailRespVO respVO = convertDO2VO(recordDO);
        respVO.setTenantName(tenantDto.getName());
        if (ObjectUtils.isEmpty(respVO.getStartTime()) || ObjectUtil.notEqual(respVO.getInquiryStatus(), InquiryStatusEnum.INQUIRING.getStatusCode())) {
            return respVO;
        }
        respVO.setIsAutoGrabInquiry(recordDO.getAutoGrabStatus());
        // 医生接诊时间不为空  且 当前问诊单状态为接诊中 ， 则根据配置的开方超时时间计算剩余时间返回给前端
        respVO.setRemainingSecond(Long.valueOf(Duration.between(LocalDateTime.now(), respVO.getStartTime().plusMinutes(outTime)).getSeconds()).intValue());
        return respVO;
    }

    InquiryRecordDetailRespVO convertDO2VO(InquiryRecordDO recordDO);

    @Mapping(target = "id", ignore = true)
    InquiryRecordDetailRespVO convertDetailDO2VO(InquiryRecordDetailDO detailDO, @MappingTarget InquiryRecordDetailRespVO respVO);

    // default PageResult<InquiryRecordRespVO> convertPage(PageResult<InquiryRecordDO> inquiryPageResult, Integer dateType) {
    //     PageResult<InquiryRecordRespVO> pageResult = convertPage(inquiryPageResult);
    //     if (CollUtil.isNotEmpty(pageResult.getList())) {
    //         pageResult.getList().forEach(respVO -> {
    //             respVO.setDateType(dateType);
    //         });
    //     }
    //     return pageResult;
    // }


    PageResult<InquiryRecordRespVO> convertPage(PageResult<InquiryRecordDO> inquiryPageResult);

    InquiryRecordDto convertDO2DTO(InquiryRecordDO recordDO);

    default InquiryRecordDO convertDTO2DOAndSetPatientPref(InquiryRecordDto dto, InquiryPatientInfoDO patientInfoDO) {
        dto.setPatientPref(patientInfoDO.getPref());
        dto.getInquiryRecordDetailDto().setPatientPref(patientInfoDO.getPref());
        dto.setCreator(dto.getCreator());
        InquiryRecordDO recordDO = convertDTO2DO(dto);
        recordDO.setCreator(dto.getCreator());
        return recordDO;
    }

    InquiryRecordDO convertDTO2DO(InquiryRecordDto dto);

    InquiryRecordDetailDto convertDetailDO2DTO(InquiryRecordDetailDO recordDetailDO);

    default InquiryRecordDetailDto convertDetailDO2DTOWithBaseDO(InquiryRecordDetailDO recordDetailDO){
        InquiryRecordDetailDto detailDto = convertDetailDO2DTO(recordDetailDO);
        detailDto.setCreator(recordDetailDO.getCreator());
        detailDto.setCreateTime(recordDetailDO.getCreateTime());
        detailDto.setUpdateTime(recordDetailDO.getUpdateTime());
        detailDto.setUpdater(recordDetailDO.getUpdater());
        return detailDto;
    }

    default void setInquiryPatientInfoAndTenant(InquiryRecordDto inquiryDto, TenantDto tenantDto) {
        inquiryDto.setTenantId(tenantDto.getId());
        inquiryDto.setTenantName(tenantDto.getName());
        inquiryDto.setTenantDto(tenantDto);
        inquiryDto.getInquiryRecordDetailDto().setTenantId(tenantDto.getId());
    }

    InquiryRecordPageReqVO convertQueryDTO2QueryVO(InquiryQueryDto queryDto);

    default List<InquiryRecordDto> convertRespList(List<InquiryRecordDO> inquiryRecordList, List<InquiryRecordDetailDO> inquiryRecordDetailList) {
        List<InquiryRecordDto> recordDtos = convertDOS2DTOS(inquiryRecordList);
        if (CollectionUtils.isEmpty(inquiryRecordDetailList)) {
            return recordDtos;
        }
        Map<String, InquiryRecordDetailDO> inquiryRecordDetailDOMap = inquiryRecordDetailList.stream().collect(Collectors.toMap(InquiryRecordDetailDO::getInquiryPref, inquiryRecordDetailDO -> inquiryRecordDetailDO, (a, b) -> b));
        recordDtos.forEach(recordDto -> {
            Optional.ofNullable(inquiryRecordDetailDOMap.get(recordDto.getPref())).ifPresent(detailDO -> recordDto.setInquiryRecordDetailDto(InquiryRecordDetailConvert.INSTANCE.convertDO2DTO(detailDO)));
        });
        return recordDtos;
    }

    List<InquiryRecordDto> convertDOS2DTOS(List<InquiryRecordDO> recordDOs);

    default InquiryQueueingRespVO convertDO2QueueingVO(String doctorIm, InquiryRecordDO inquiryDO) {
        return InquiryQueueingRespVO.builder()
            .inquiryStatus(inquiryDO.getInquiryStatus())
            .doctorConnectStatus(StringUtils.isBlank(doctorIm) ? 0 : 1)
            .doctorImAccount(StringUtils.isBlank(doctorIm) ? "" : doctorIm)
            .index(1)
            .waitNum(0)
            .waitTime(1).build();
    }

    List<InquiryRecordDO> convertDTOList2DOList(List<InquiryRecordDto> dtoList);

    default List<InquiryRecordDetailDto> convertDetailDO2DTOs(List<InquiryRecordDetailDO> recordDetailByPrefs){
        if (recordDetailByPrefs == null) {
            return null;
        }
        List<InquiryRecordDetailDto> list = new ArrayList<InquiryRecordDetailDto>();
        for (InquiryRecordDetailDO inquiryRecordDetailDO : recordDetailByPrefs) {
            list.add(convertDetailDO2DTO(inquiryRecordDetailDO));
        }
        return list;
    }
}
