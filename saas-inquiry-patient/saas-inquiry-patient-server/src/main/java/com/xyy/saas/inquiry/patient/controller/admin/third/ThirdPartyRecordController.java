package com.xyy.saas.inquiry.patient.controller.admin.third;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.patient.controller.admin.third.vo.ThirdPartyDrugMatchFailRecordPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.third.vo.ThirdPartyDrugMatchFailRecordRespVO;
import com.xyy.saas.inquiry.patient.service.third.ThirdPartyRecordService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 对接三方问诊记录
 */
@Tag(name = "对接三方问诊记录")
@RestController
@RequestMapping(value = {"/patient/third-party/inquiry-record"})
@Validated
public class ThirdPartyRecordController {

    @Resource
    private ThirdPartyRecordService thirdPartyRecordService;

    @GetMapping("/page-query-drug-match-fail-record")
    @Operation(summary = "对接三方问诊 - 分页查询药品匹配失败记录")
    @Parameter(name = "thirdPartyPreInquiryRequestDto", description = "三方预问诊订单信息", required = true)
    public CommonResult<PageResult<ThirdPartyDrugMatchFailRecordRespVO>> pageQueryDrugMatchFailRecord(@Valid ThirdPartyDrugMatchFailRecordPageReqVO thirdPartyDrugMatchFailRecordPageReqVO) {
        return thirdPartyRecordService.pageQueryDrugMatchFailRecord(thirdPartyDrugMatchFailRecordPageReqVO);
    }

}
