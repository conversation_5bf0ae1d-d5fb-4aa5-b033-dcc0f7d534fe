package com.xyy.saas.inquiry.patient.controller.app.inquiry.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import java.util.*;
import jakarta.validation.constraints.*;

@Schema(description = "管理后台 - 问诊投诉记录新增/修改 Request VO")
@Data
public class InquiryComplainSaveReqVO {

    @Schema(description = "问诊单pref", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "问诊单pref不能为空")
    private String inquiryPref;

    @Schema(description = "举报类型", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "举报类型不能为空")
    private List<String> complainItem;

    @Schema(description = "举报描述", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "举报描述不能为空")
    @Size(max = 200, message = "举报描述长度不能超过200个字符")
    private String complainContent;

    @Schema(description = "证明材料图片", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotNull(message = "证明材料图片不能为空")
    private List<String> complainImage;

}