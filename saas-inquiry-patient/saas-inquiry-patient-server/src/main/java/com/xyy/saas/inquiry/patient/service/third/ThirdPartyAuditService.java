package com.xyy.saas.inquiry.patient.service.third;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryAuditReqVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryAuditRespVO;
import com.xyy.saas.inquiry.patient.controller.app.third.vo.ThirdPartyPreInquiryCancelRespVO;
import jakarta.validation.Valid;

/**
 * @Author: xucao
 * @DateTime: 2025/4/29 11:28
 * @Description: 三方订单审核服务
 **/
public interface ThirdPartyAuditService {


    /**
     * 三方预问诊单审核
     * @param thirdPartyPreInquiryAuditReqVO
     * @return
     */
    CommonResult<ThirdPartyPreInquiryAuditRespVO> preInquiryAudit(@Valid ThirdPartyPreInquiryAuditReqVO thirdPartyPreInquiryAuditReqVO);

    ThirdPartyPreInquiryCancelRespVO cancelPreInquiry(String pref);
}
