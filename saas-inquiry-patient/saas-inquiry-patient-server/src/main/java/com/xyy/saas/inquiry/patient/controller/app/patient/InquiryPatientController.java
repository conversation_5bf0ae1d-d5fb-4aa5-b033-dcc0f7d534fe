package com.xyy.saas.inquiry.patient.controller.app.patient;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.framework.common.pojo.PageResult;
import cn.iocoder.yudao.framework.tenant.core.context.TenantContextHolder;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoPageReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoQueryReqVO;
import com.xyy.saas.inquiry.patient.controller.admin.patient.vo.InquiryPatientInfoRespVO;
import com.xyy.saas.inquiry.patient.controller.app.patient.vo.PatientInquiryRecordRespVO;
import com.xyy.saas.inquiry.patient.controller.app.patient.vo.PatientMainSuitVO;
import com.xyy.saas.inquiry.patient.service.patient.InquiryPatientInfoService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.Valid;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * @ClassName：InquiryPatientController
 * @Author: xucao
 * @Date: 2024/10/30 14:43
 * @Description: 问诊患者相关接口
 */
@Tag(name = "APP+PC - 患者查询+历史病情")
@RestController
@RequestMapping(value = {"/admin-api/kernel/patient/patient-info", "/app-api/kernel/patient/patient-info"})
@Validated
public class InquiryPatientController {

    @Resource
    private InquiryPatientInfoService inquiryPatientInfoService;


    @GetMapping("/get")
    @Operation(summary = "获得患者基础信息 - 实时计算年龄")
    @Parameter(name = "patientPref", description = "患者编号", required = true, example = "HZ001")
    public CommonResult<InquiryPatientInfoRespVO> getPatientInfoByPref(@RequestParam("patientPref") String patientPref) {
        return success(inquiryPatientInfoService.getPatientInfoByPref(patientPref));
    }


    @GetMapping("/page")
    @Operation(summary = "获得患者信息分页")
    public CommonResult<PageResult<InquiryPatientInfoRespVO>> getInquiryPatientInfoPage(@Valid InquiryPatientInfoQueryReqVO pageReqVO) {
        return success(inquiryPatientInfoService.getInquiryPatientInfoPage(pageReqVO));
    }


    @GetMapping("/page-mainSuit")
    @Operation(summary = "获得患者历史病情")
    public CommonResult<PageResult<PatientMainSuitVO>> getInquiryPatientMainSuitPage(@Valid InquiryPatientInfoPageReqVO pageReqVO) {
        pageReqVO.setTenantId(TenantContextHolder.getTenantId());
        return success(inquiryPatientInfoService.getInquiryPatientMainSuitPage(pageReqVO));
    }

    @GetMapping("/get-last-inquiry-record")
    @Operation(summary = "获得患者最近一次问诊记录")
    public CommonResult<PatientInquiryRecordRespVO> getLastInquiryRecord(@RequestParam("patientPref") String patientPref) {
        return success(inquiryPatientInfoService.getLastInquiryRecord(patientPref));
    }

}
