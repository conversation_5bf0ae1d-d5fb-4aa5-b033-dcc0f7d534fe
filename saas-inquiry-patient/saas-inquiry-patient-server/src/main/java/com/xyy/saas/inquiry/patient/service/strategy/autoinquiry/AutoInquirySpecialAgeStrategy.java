package com.xyy.saas.inquiry.patient.service.strategy.autoinquiry;

import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionGlobalConfigRespDto;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryJudgeNodeEnum;
import com.xyy.saas.inquiry.enums.inquiry.UnableAutoReasonEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pojo.condition.ConditionParamDto;
import com.xyy.saas.inquiry.util.ConditionUtil;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/12/23 9:44
 * @Description: 自动开方特殊年龄区间判定策略
 */
@Component
public class AutoInquirySpecialAgeStrategy extends AutoInquiryStrategy {

    /**
     * 执行判定是否自动开方方法
     *
     * @param inquiryDto   问诊参数
     * @param globalConfig 全局配置
     */
    @Override
    @TraceNode(node = TraceNodeEnum.AUOTO_INQUIRY_SPECIALAGE ,prefLocation = "inquiryDto.pref")
    public void executeJudge(InquiryRecordDto inquiryDto, InquiryOptionGlobalConfigRespDto globalConfig) {
        if (!BooleanUtils.isTrue(globalConfig.getPresAutoInquiryToRealForSpecialAgeRange())) {
            // 未开启情况下直接返回
            return;
        }
        // 构造条件参数
        ConditionParamDto param = ConditionParamDto.builder().age(inquiryDto.getPatientAge()).build();
        if (!ConditionUtil.isMatch(param, globalConfig.getPresAutoInquiryToRealForSpecialAgeRangeConditions())) {
            // 不满足设定走真人的条件，直接返回
            return;
        }
        // 满足设定走真人的条件，按权重回流真人
        inquiryToRealByRatio(inquiryDto, globalConfig.getPresAutoInquiryToRealForSpecialAgeRangeRatio(), UnableAutoReasonEnum.SPECIFIC_AGE_RANGE_PATIENT);
    }

    @Override
    public AutoInquiryJudgeNodeEnum getAutoInquiryJudgeNodeEnum() {
        return AutoInquiryJudgeNodeEnum.AUTO_INQUIRY_TO_REAL_FOR_SPECIAL_AGE_RANGE;
    }
}
