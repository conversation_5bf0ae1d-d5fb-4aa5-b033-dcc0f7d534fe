package com.xyy.saas.inquiry.patient.service.strategy.autoinquiry;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import cn.iocoder.yudao.module.infra.api.config.ConfigApi;
import com.xyy.saas.inquiry.annotation.TraceNode;
import com.xyy.saas.inquiry.drugstore.api.option.dto.InquiryOptionGlobalConfigRespDto;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryEnum;
import com.xyy.saas.inquiry.enums.inquiry.AutoInquiryJudgeNodeEnum;
import com.xyy.saas.inquiry.enums.inquiry.MedicineTypeEnum;
import com.xyy.saas.inquiry.enums.inquiry.UnableAutoReasonEnum;
import com.xyy.saas.inquiry.enums.trace.TraceNodeEnum;
import com.xyy.saas.inquiry.patient.api.inquiry.dto.InquiryRecordDto;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDetailDto;
import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDto;
import com.xyy.saas.inquiry.product.api.search.ProductSearchApi;
import jakarta.annotation.Resource;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.apache.dubbo.config.annotation.DubboReference;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * @Author: xucao
 * @Date: 2024/12/23 9:47
 * @Description: 自动开方策略-用法用量不全 1、用法用量缺失 - 走真人 2、都填了但是临时商品  - 走真人
 */
@Component
public class AutoInquiryUsageDosageStrategy extends AutoInquiryStrategy {

    private static final Logger log = LoggerFactory.getLogger(AutoInquiryUsageDosageStrategy.class);

    @DubboReference
    private ProductSearchApi productSearchApi;

    @Resource
    private ConfigApi configApi;

    /**
     * 执行判定是否自动开方方法
     *
     * @param inquiryDto   问诊参数
     * @param globalConfig 全局配置
     */
    @Override
    @TraceNode(node = TraceNodeEnum.AUOTO_INQUIRY_PRODUCT_USAGEDOSAGE, prefLocation = "inquiryDto.pref")
    public void executeJudge(InquiryRecordDto inquiryDto, InquiryOptionGlobalConfigRespDto globalConfig) {
        boolean isUsageDosageMissing = false;
        // 获取商品信息
        InquiryProductDto inquiryProductDto = inquiryDto.getInquiryRecordDetailDto().getPreDrugDetail();
        if (ObjectUtil.equals(inquiryDto.getMedicineType(), MedicineTypeEnum.ASIAN_MEDICINE.getCode())) {
            isUsageDosageMissing = checkUsageDosageAsianMedicine(inquiryProductDto);
        } else {
            isUsageDosageMissing = checkUsageDosageChineseMedicine(inquiryProductDto);
        }
        if (isUsageDosageMissing) {
            // 设置为不走自动开方
            inquiryDto.setAutoInquiry(AutoInquiryEnum.NO.getCode());
            inquiryDto.setUnableAutoReason(UnableAutoReasonEnum.MISSING_DOSAGE_INSTRUCTIONS.getCode());
        }
        // 临时商品处理
        tempProductHandle(inquiryDto, inquiryProductDto.getInquiryProductInfos());
    }


    /**
     * 中药判断用法用量是否缺失
     *
     * @param inquiryProductDto 问诊预购药信息
     * @return
     */
    private boolean checkUsageDosageChineseMedicine(InquiryProductDto inquiryProductDto) {
        // 中药用法用量，每x日校验开关
        boolean checkTcmDaily = StringUtils.equalsIgnoreCase(configApi.getConfigValueByKey("auto.inquiry.check.tcm.daily"), "1");
        if (checkTcmDaily) {
            return StringUtils.isAnyBlank(inquiryProductDto.getTcmUsage(), inquiryProductDto.getTcmDaily(), inquiryProductDto.getTcmDailyDosage(), inquiryProductDto.getTcmTotalDosage(), inquiryProductDto.getTcmProcessingMethod(),
                inquiryProductDto.getTcmDirections());
        }
        return StringUtils.isAnyBlank(inquiryProductDto.getTcmUsage(), inquiryProductDto.getTcmDailyDosage(), inquiryProductDto.getTcmTotalDosage(), inquiryProductDto.getTcmProcessingMethod(),
            inquiryProductDto.getTcmDirections());
    }

    /**
     * 西药判断用法用量是否缺失
     *
     * @param inquiryProductDto 问诊预购药信息
     * @return
     */
    private boolean checkUsageDosageAsianMedicine(InquiryProductDto inquiryProductDto) {
        Boolean isUsageDosageMissing = false;
        // 获取药品明细
        List<InquiryProductDetailDto> productDetailDtos = inquiryProductDto.getInquiryProductInfos();
        for (InquiryProductDetailDto item : productDetailDtos) {
            // 兼容适量场景
            String singleDose = item.getSingleDose();
            if (StringUtils.equals(item.getSingleUnit(), "适量")) {
                singleDose = "1";
            }
            if (StringUtils.isAnyBlank(item.getDirections(), singleDose, item.getUseFrequency(), item.getUseFrequencyValue())) {
                isUsageDosageMissing = true;
                break;
            }
        }
        return isUsageDosageMissing;
    }

    /**
     * 临时商品处理 - 不填写预购药直接走真人
     *
     * @param productDetailDtos 商品列表
     */
    private void tempProductHandle(InquiryRecordDto inquiryDto, List<InquiryProductDetailDto> productDetailDtos) {

        if (CollUtil.isNotEmpty(productDetailDtos)) {
            CommonResult<List<InquiryProductDetailDto>> productDetailDtoList =
                productSearchApi.queryProductStandardListByStandardIds(productDetailDtos.stream().map(InquiryProductDetailDto::getStandardId).filter(StringUtils::isNotBlank).toList());
            if (productDetailDtoList.isError()) {
                log.error("queryProductStandardListByStandardIds error:{}", productDetailDtoList.getMsg());
                return;
            }
            if (productDetailDtoList.getData() != null && productDetailDtoList.getData().size() == productDetailDtos.size()) {
                return;
            }
        }

        inquiryDto.setAutoInquiry(AutoInquiryEnum.NO.getCode());
        inquiryDto.setUnableAutoReason(UnableAutoReasonEnum.TEMPORARY_PRODUCT.getCode());
    }

    @Override
    public AutoInquiryJudgeNodeEnum getAutoInquiryJudgeNodeEnum() {
        return AutoInquiryJudgeNodeEnum.AUTO_INQUIRY_TO_REAL_FOR_USAGE_DOSAGE_MISSING;
    }
}
