package com.xyy.saas.inquiry.patient.dal.dataobject.medicare;

import cn.iocoder.yudao.framework.mybatis.core.dataobject.BaseDO;
import com.baomidou.mybatisplus.annotation.KeySequence;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.*;

import java.time.LocalDateTime;

/**
 * 参保信息记录 DO
 *
 * <AUTHOR>
 */
@TableName("saas_medicare_person_insurance_record")
@KeySequence("saas_medicare_person_insurance_record_seq")
@Data
@EqualsAndHashCode(callSuper = true)
@ToString(callSuper = true)
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MedicarePersonInsuranceRecordDO extends BaseDO {

    /**
     * 主键
     */
    @TableId
    private Long id;
    
    /**
     * 租户ID
     */
    private Long tenantId;
    
    /**
     * 人员编号
     */
    private String psnNo;
    
    /**
     * 人员姓名
     */
    private String psnName;
    
    /**
     * 证件号码
     */
    private String certno;
    
    /**
     * 性别
     */
    private String gend;
    
    /**
     * 年龄
     */
    private String age;
    
    /**
     * 出生日期
     */
    private String brdy;
    
    /**
     * 险种类型
     */
    private String insutype;
    
    /**
     * 参保地行政区划
     */
    private String insuplcAdmdvs;

    /**
     * 参保单位名称
     */
    private String empName;

    /**
     * 参保人员类别
     */
    private String psnType;

    /**
     * 参保人员状态
     */
    private String psnInsuStas;

    /**
     * 参保开始时间
     */
    private String psnInsuDate;

} 