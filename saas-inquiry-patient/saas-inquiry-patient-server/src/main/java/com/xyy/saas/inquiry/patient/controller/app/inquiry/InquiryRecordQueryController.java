package com.xyy.saas.inquiry.patient.controller.app.inquiry;

import static cn.iocoder.yudao.framework.common.pojo.CommonResult.success;

import cn.iocoder.yudao.framework.common.pojo.CommonResult;
import com.xyy.saas.inquiry.patient.controller.admin.inquiry.vo.InquiryRecordDetailRespVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryReceptionInfoRespVO;
import com.xyy.saas.inquiry.patient.controller.app.inquiry.vo.InquiryRecordAndPrescriptionVO;
import com.xyy.saas.inquiry.patient.service.inquiry.InquiryRecordQueryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.annotation.Resource;
import jakarta.validation.constraints.NotEmpty;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;


@Tag(name = "APP+PC - 问诊记录查询")
@RestController
@RequestMapping(value = {"/admin-api/kernel/patient/inquiry-query", "/app-api/kernel/patient/inquiry-query"})
@Validated
public class InquiryRecordQueryController {

    @Resource
    private InquiryRecordQueryService inquiryRecordQueryService;

    @GetMapping("/get-record")
    @Operation(summary = "获得问诊记录详情(1.患者档案 2.用药申请 3.医生开方页)")
    @Parameter(name = "inquiryPref", description = "问诊单号", required = true, example = "1024")
    public CommonResult<InquiryRecordDetailRespVO> queryInquiryRecordByPref(@RequestParam("inquiryPref") @NotEmpty String inquiryPref) {
        return success(inquiryRecordQueryService.queryInquiryRecordVOByPref(inquiryPref));
    }

    @GetMapping("/get-prescription-by-pref")
    @Operation(summary = "商家-医生-药师 查看问诊处方信息(状态+用药申请单+处方笺)")
    @Parameter(name = "inquiryPref", description = "问诊单号", required = true, example = "1024")
    public CommonResult<InquiryRecordAndPrescriptionVO> getInquiryPrescriptionByPref(@RequestParam("inquiryPref") String inquiryPref) {
        return success(inquiryRecordQueryService.getInquiryPrescriptionByPref(inquiryPref));
    }

    @GetMapping("/reception-info")
    @Operation(summary = "问诊接诊信息查询")
    @Parameter(name = "inquiryPref", description = "问诊单号", required = true)
    public CommonResult<InquiryReceptionInfoRespVO> receptionInfo(@RequestParam("inquiryPref") String inquiryPref) {
        return CommonResult.success(inquiryRecordQueryService.getReceptionInfo(inquiryPref));
    }


}