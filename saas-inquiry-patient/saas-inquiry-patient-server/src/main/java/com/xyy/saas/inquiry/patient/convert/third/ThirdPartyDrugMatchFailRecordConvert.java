package com.xyy.saas.inquiry.patient.convert.third;

import cn.iocoder.yudao.framework.common.pojo.PageResult;
import com.xyy.saas.inquiry.patient.controller.admin.third.vo.ThirdPartyDrugMatchFailRecordRespVO;
import com.xyy.saas.inquiry.patient.dal.dataobject.third.ThirdPartyDrugMatchFailRecordDO;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * 三方药品匹配失败记录转换对象
 */
@Mapper
public interface ThirdPartyDrugMatchFailRecordConvert {

    ThirdPartyDrugMatchFailRecordConvert INSTANCE = Mappers.getMapper(ThirdPartyDrugMatchFailRecordConvert.class);

    PageResult<ThirdPartyDrugMatchFailRecordRespVO> convertPage(PageResult<ThirdPartyDrugMatchFailRecordDO> pageResult);
}
