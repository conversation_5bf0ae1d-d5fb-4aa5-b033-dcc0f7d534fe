package com.xyy.saas.inquiry.patient.controller.app.inquiry.vo;

import com.xyy.saas.inquiry.pojo.inquiry.InquiryDetailExtDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @Desc
 * <AUTHOR>  <EMAIL>
 * @Date Created in 2024/8/28 下午7:03
 */
@Schema(description = "用户问诊单基础 Request VO")
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseInquiryReqVO {

    @Schema(description = "患者信息")
    @NotNull(message = "患者信息不能为空")
    @Valid
    private PatientVO patient;

    @Schema(description = "病情描述(主诉)", example = "持续发热一周")
    @NotNull(message = "病情描述不能为空")
    private List<String> mainSuit;

    @Schema(description = "过敏史", example = "青霉素,头孢")
    private List<String> allergic;

    @Schema(description = "诊断信息")
    @NotNull(message = "诊断信息不能为空")
    @Valid
    private List<DiagnosisItemVO> diagnosis;

    @Schema(description = "是否为慢性疾病治疗需求", example = "0")
    private Integer slowDisease;

    @Schema(description = "肝肾功能是否异常", example = "1")
    private Integer liverKidneyValue;

    @Schema(description = "是否妊娠哺乳期", example = "1")
    private Integer gestationLactationValue;

    @Schema(description = "购药类型", example = "0")
    @NotNull(message = "购药类型不能为空")
    private Integer medicineType;

    @Schema(description = "上传的线下就诊处方或病历文件路径", example = "https://saas.file.xxxx/222.jpg")
    private List<String> offlinePrescriptions;

    @Schema(description = "预购药信息", requiredMode = Schema.RequiredMode.REQUIRED)
    @Valid
    private BaseInquiryProductVO inquiryProductInfo;

    @Schema(description = "处方类型")
    private Integer prescriptionType;

    @Schema(description = "问诊详情扩展信息ext")
    private InquiryDetailExtDto ext;

    @Schema(description = "三方渠道预问诊id", example = "1")
    private Long thirdPartyPreInquiryId;

}
