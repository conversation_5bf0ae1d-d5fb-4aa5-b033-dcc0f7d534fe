package com.xyy.saas.inquiry.patient.controller.app.third.vo;

import com.xyy.saas.inquiry.pojo.inquiry.DiagnosisItemDto;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import java.math.BigDecimal;
import java.util.List;

/**
 * 三方获取处方信息返参对象
 *
 * <AUTHOR>
 * @Date 6/11/24 11:02 AM
 */
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Data
@Schema(description = "app侧 - 三方获取处方信息返参对象")
public class ThirdPartyGetPrescriptionRespVO {

    @Schema(description = "处方imageUrl")
    private String prescriptionUrl;

    @Schema(description = "状态")
    private Integer status;

    @Schema(description = "失败原因")
    private String failMsg;

    @Schema(description = "医师姓名")
    private String physicianName;

    @Schema(description = "开方医院")
    private String hospital;

    @Schema(description = "诊断说明")
    private String diagnosis;

    @Schema(description = "患者姓名")
    private String patientName;

    @Schema(description = "患者性别：1 男 2 女")
    private Integer patientSex;

    @Schema(description = "患者年龄")
    private String patientAge;

    @Schema(description = "患者手机号")
    private String patientMobile;

    @Schema(description = "患者身份证号")
    private String patientIdCard;

    @Schema(description = "开具处方时间 yyyy-MM-dd HH:mm:ss")
    private String outPrescriptionTime;

    @Schema(description = "处方号")
    private String prescriptionPref;

    @Schema(description = "药师姓名")
    private String pharmacistName;

    @Schema(description = "核对人名称")
    private String checkPersonName;

    @Schema(description = "调配人名称")
    private String allocationPersonName;

    @Schema(description = "用药信息")
    private ThirdPartyMedicineUsageRespVO medicineUsage;

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Schema(description = "用药信息")
    public static class ThirdPartyMedicineUsageRespVO{

        @Schema(description = "药品类型")
        private Integer medicineType;

        @Schema(description = "药品信息")
        private List<ThirdPartyMedicineInfoRespVO> drugList;

        @Schema(description = "中药总副数")
        private BigDecimal tcmTotalDosage;

        @Schema(description = "中药每日副数")
        private BigDecimal tcmDailyDosage;

        @Schema(description = "中药每日频次")
        private String tcmDailyFrequency;

        @Schema(description = "中药用法")
        private String tcmUsage;

        @Schema(description = "中药加工方式")
        private String tcmProcessingMethod;
    }

    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Schema(description = "药品信息")
    public static class ThirdPartyMedicineInfoRespVO{

        @Schema(description = "药品名称（通用名）")
        private String drugName;

        @Schema(description = "规格（比如：0.5g*7s*2板）")
        private String attributeSpecification;

        @Schema(description = "生产厂家")
        private String manufacturer;

        @Schema(description = "包装单位（比如：盒）")
        private String packageUnit;

        @Schema(description = "单位（比如：粒）")
        private String productUnit;

        @Schema(description = "数量")
        private BigDecimal quantity;

        @Schema(description = "使用频率（西药，比如：3次/天）")
        private String useFrequency;

        @Schema(description = "用法（西药，比如：口服）")
        private String directions;

        @Schema(description = "用量")
        private String singleDose;

    }

}
