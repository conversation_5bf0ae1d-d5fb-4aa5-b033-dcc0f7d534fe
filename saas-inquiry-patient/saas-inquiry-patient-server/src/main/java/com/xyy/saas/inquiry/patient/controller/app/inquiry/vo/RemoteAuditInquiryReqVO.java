package com.xyy.saas.inquiry.patient.controller.app.inquiry.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.models.security.SecurityScheme.In;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * @Author: xucao
 * @DateTime: 2025/4/22 10:25
 * @Description: 远程审方问诊请求入参VO
 **/
@Schema(description = "远程审方问诊请求参数")
@Data
public class RemoteAuditInquiryReqVO {

    @Schema(description = "客户端渠类型-0、app  1、pc  2、小程序", example = "0")
    @NotNull(message = "问诊客户端类型不能为空")
    private Integer clientChannelType;

    @Schema(description = "客户端系统类型", example = "android")
    private String clientOsType;

    @Schema(description = "问诊业务类型 1-荷叶问诊 2-远程审方", example = "2")
    @NotNull(message = "问诊业务类型不能为空")
    private Integer inquiryBizType;

    private Integer bizChannelType;

    @Schema(description = "远程审方表单信息", example = "11")
    @NotNull(message = "远程审方表单信息不能为空")
    @Valid
    private RemoteAuditBaseReqVO remoteAuditBaseReqVO;
}
