package com.xyy.saas.inquiry.patient.controller.app.inquiry.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotEmpty;
import lombok.Data;

/**
 * @Author: xucao
 * @Date: 2024/12/19 16:25
 * @Description: 诊断信息
 */
@Data
public class DiagnosisItemVO {

    @Schema(description = "诊断编码", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotEmpty(message = "诊断编码不能为空")
    private String diagnosisCode;

    @Schema(description = "诊断名称", requiredMode = Schema.RequiredMode.REQUIRED, example = "芋艿")
    @NotEmpty(message = "诊断名称不能为空")
    private String diagnosisName;
}
