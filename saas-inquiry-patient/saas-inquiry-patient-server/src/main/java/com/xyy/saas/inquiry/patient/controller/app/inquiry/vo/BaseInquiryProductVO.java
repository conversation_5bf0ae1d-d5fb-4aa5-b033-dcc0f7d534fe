package com.xyy.saas.inquiry.patient.controller.app.inquiry.vo;

import com.xyy.saas.inquiry.pojo.inquiry.InquiryProductDetailDto;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import java.util.List;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * @Author: xucao
 * @Date: 2024/12/19 16:09
 * @Description: 问诊预购药入参
 */
@Schema(description = "用户问诊单预购药信息 Request VO")
@Data
@Valid
public class BaseInquiryProductVO {

    /**
     * 问诊药品
     */
    @Schema(description = "问诊药品集合")
    private List<InquiryProductDetailDto> inquiryProductInfos;

    // 中药 *******************
    /**
     * 总剂量  eg:10 副
     */
    @Schema(description = "中药总剂量  eg:10 副")
    private String tcmTotalDosage;

    /**
     * 每x日 eg: 每tcmDaily日tcmDailyDosage剂
     */
    @Schema(description = "每x日  eg:2")
    @Length(max = 1000, message = "每x日最大长度1000")
    private String tcmDaily;


    /**
     * 每日剂量 eg:每x日3 剂
     */
    @Schema(description = "中药每日剂量 eg:每日3 剂")
    private String tcmDailyDosage;

    /**
     * 每剂几次用药 eg:每剂2 次用药
     */
    @Schema(description = "中药每剂几次用药 eg:每剂2 次用药")
    private String tcmUsage;

    /**
     * 用法 eg: 温服
     */
    @Schema(description = "中药用法 eg: 温服")
    private String tcmDirections;
    /**
     * 加工方法 eg: 打粉冲服
     */
    @Schema(description = "中药加工方法 eg: 打粉冲服")
    private String tcmProcessingMethod;
}
