<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.xyy.saas.inquiry.patient.dal.mysql.inquiry.InquiryRecordMapper">

  <!--
      一般情况下，尽可能使用 Mapper 进行 CRUD 增删改查即可。
      无法满足的场景，例如说多表关联查询，才使用 XML 编写 SQL。
      代码生成器暂时只生成 Mapper XML 文件本身，更多推荐 MybatisX 快速开发插件来生成查询。
      文档可见：https://www.iocoder.cn/MyBatis/x-plugins/
   -->
  <update id="doctorGrabbingInquiry">
    update saas_inquiry_record
    set doctor_pref      = #{doctorPref}
      , doctor_name      = #{doctorN<PERSON>}
      , dept_pref        = #{deptPref}
      , dept_name        = #{deptName}
      , inquiry_status   = #{inquiryStatus}
      , auto_grab_status = #{autoGrabStatus}
      , start_time       = #{startTime}
    where id = #{id}
      and doctor_pref = ''
      and inquiry_status = #{originalInquiryStatus}
  </update>

  <select id="selectDoctorReceptionPatientPage" resultType="com.xyy.saas.inquiry.pojo.patient.PatientSimpleDTO">
    SELECT DISTINCT patient_pref as pref, patient_name as name
    FROM saas_inquiry_record
    WHERE doctor_pref = #{reqVo.doctorPref}
    AND create_time BETWEEN #{reqVo.startTime} AND #{reqVo.endTime}
    <if test="reqVo.name != null and reqVo.name != ''">
      AND patient_name LIKE CONCAT('%', #{reqVo.name}, '%')
    </if>
    <if test="reqVo.autoInquiry != null">
      AND auto_inquiry = #{reqVo.autoInquiry}
    </if>
    ORDER BY pref desc
  </select>

  <update id="updateInquiryEndTime">
    update saas_inquiry_record
    set end_time      = #{endTime},
        stream_status = #{streamStatus}
    where id = #{id}
      and end_time is null
  </update>

  <select id="selectCountByDistinctPatient" parameterType="com.xyy.saas.inquiry.patient.controller.admin.statistics.vo.InquiryStatisticsReqVO" resultType="java.lang.Long">
    select count(distinct patient_pref)
    from saas_inquiry_record
    where deleted = 0
      and tenant_id = #{tenantId}
      and create_time between #{createTime[0],javaType=java.time.LocalDateTime} and #{createTime[1],javaType=java.time.LocalDateTime}
  </select>

  <update id="batchUpdateInquiryImHistory">
    <foreach collection="inquiryRecordDOList" index="index" item="item" open="" close="" separator=";">
      update saas_inquiry_record set im_history = #{item.imHistory} where id=#{item.id}
    </foreach>
  </update>


</mapper>