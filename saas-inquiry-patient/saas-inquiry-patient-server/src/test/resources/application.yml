server:
  port: 48082

spring:
  profiles:
    active: test
  application:
    name: inquiry-kernel-all
  cloud:
    nacos:
      discovery:
        heart-beat:
          enabled: true
  # Redis 配置。Redisson 默认的配置足够使用，一般不需要进行调优
  datasource:
    dynamic:
      druid: # Druid 【连接池】相关的全局配置
        initial-size: 5 # 初始连接数
        min-idle: 10 # 最小连接池数量
        max-active: 20 # 最大连接池数量
        max-wait: 600000 # 配置获取连接等待超时的时间，单位：毫秒
        time-between-eviction-runs-millis: 60000 # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位：毫秒
        min-evictable-idle-time-millis: 300000 # 配置一个连接在池中最小生存的时间，单位：毫秒
        max-evictable-idle-time-millis: 900000 # 配置一个连接在池中最大生存的时间，单位：毫秒
        validation-query: SELECT 1 # 配置检测连接是否有效
        test-while-idle: true
        test-on-borrow: false
        test-on-return: false
      primary: master
dubbo:
  application:
    name: ${spring.application.name}
    qos-port: 22228
  registry:
    address: nacos://${spring.cloud.nacos.server-addr}
    #注册模式为实例级别
    register-mode: instance
    parameters:
      namespace: ${spring.cloud.nacos.discovery.namespace}
    group: dubbo
  config-center:
    address: nacos://${spring.cloud.nacos.server-addr}
    group: ${spring.cloud.nacos.config.group}
    namespace: ${spring.cloud.nacos.config.namespace}
  protocol:
    name: dubbo
    port: -1 # 自动选择可用端口
  consumer:
    timeout: 5000
    check: false
    filter: dubboTenantFilter
    #指定服务提供者的namespace （配置文档中有说明）
    provider-namespace: ${spring.cloud.nacos.discovery.namespace}
    group: ${dubbo.registry.group}
  provider:
    timeout: 10000
    filter: dubboTenantFilter
    group: ${dubbo.registry.group}
# MyBatis Plus 的配置项
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true # 虽然默认为 true ，但是还是显示去指定下。
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl # 开启SQL日志
#event:
#  bus:
#    aliMQAccessKey: ""
#    aliMQSecretKey: ""
#    aliYunNameServer: ************:9876

--- #################### 微信公众号相关配置 ####################


--- #################### 微信公众号相关配置 ####################
wx: # 参见 https://github.com/Wechat-Group/WxJava/blob/develop/spring-boot-starters/wx-java-mp-spring-boot-starter/README.md 文档
  mp:
    # 公众号配置(必填)
    app-id: x
    secret: x
    # 存储配置，解决 AccessToken 的跨节点的共享
    config-storage:
      type: RedisTemplate # 采用 RedisTemplate 操作 Redis，会自动从 Spring 中获取
      key-prefix: wx # Redis Key 的前缀
      http-client-type: HttpClient # 采用 HttpClient 请求微信公众号平台
  miniapp: # 小程序配置（必填），参见 https://github.com/Wechat-Group/WxJava/blob/develop/spring-boot-starters/wx-java-miniapp-spring-boot-starter/README.md 文档
    appid: wxd1425c818778593e #真
    secret: 32ab10e980c95f7eeb6d7163d35c2649 #真
    config-storage:
      type: RedisTemplate # 采用 RedisTemplate 操作 Redis，会自动从 Spring 中获取
      key-prefix: wa # Redis Key 的前缀
      http-client-type: HttpClient # 采用 HttpClient 请求微信公众号平台

yudao:
  info:
    version: 1.0.0
    base-package: cn.iocoder.yudao,com.xyy.saas
  web:
    admin-api:
      prefix: /admin-api/kernel
    app-api:
      prefix: /app-api/kernel
    admin-ui:
      url: http://dashboard.yudao.iocoder.cn # Admin 管理后台 UI 的地址
  xss:
    enable: false
  security:
    permit-all_urls:
      - /admin-api/mp/open/** # 微信公众号开放平台，微信回调接口，不需要登录
      - /admin-api/kernel/fdd/notify/** # fdd外部回调放行
  websocket:
    enable: false # websocket的开关
    path: /infra/ws # 路径
    sender-type: local # 消息发送的类型，可选值为 local、redis、rocketmq、kafka、rabbitmq
    sender-rocketmq:
      topic: ${spring.application.name}-websocket # 消息发送的 RocketMQ Topic
      consumer-group: ${spring.application.name}-websocket-consumer # 消息发送的 RocketMQ Consumer Group
    sender-rabbitmq:
      exchange: ${spring.application.name}-websocket-exchange # 消息发送的 RabbitMQ Exchange
      queue: ${spring.application.name}-websocket-queue # 消息发送的 RabbitMQ Queue
    sender-kafka:
      topic: ${spring.application.name}-websocket # 消息发送的 Kafka Topic
      consumer-group: ${spring.application.name}-websocket-consumer # 消息发送的 Kafka Consumer Group
  swagger:
    title: 荷叶问诊快速开发平台
    description: 提供管理后台、用户 App 的所有功能
    version: ${yudao.info.version}
    url: ${yudao.web.admin-ui.url}
    email: ybm100.com
    license: MIT
    license-url: https://gitee.com/zhijiantianya/ruoyi-vue-pro/blob/master/LICENSE
  codegen:
    base-package: ${yudao.info.base-package}
    db-schemas: ${spring.datasource.dynamic.datasource.master.name}
    front-type: 10 # 前端模版的类型，参见 CodegenFrontTypeEnum 枚举类
  tenant: # 多门店相关配置项
    enable: true
    ignore-urls:
      - /app-api/kernel/im/callback/onMessage
      - /admin-api/system/tenant/get-id-by-name # 基于名字获取门店，不许带门店编号
      - /admin-api/system/tenant/get-by-website # 基于域名获取门店，不许带门店编号
      - /admin-api/system/captcha/get # 获取图片验证码，和门店无关
      - /admin-api/system/captcha/check # 校验图片验证码，和门店无关
      - /admin-api/infra/file/*/get/** # 获取图片，和门店无关
      - /admin-api/system/sms/callback/* # 短信回调接口，无法带上门店编号
      - /admin-api/pay/notify/** # 支付回调通知，不携带门店编号
      - /jmreport/* # 积木报表，无法携带门店编号
      - /admin-api/mp/open/** # 微信公众号开放平台，微信回调接口，无法携带门店编号
      - /admin-api/kernel/fdd/notify/** # fdd外部回调放行
    ignore-tables:
      - system_tenant
      - system_users # 更改登录后选多门店逻辑 此表忽略门店id
      - system_tenant_package
      - saas_inquiry_diagnosis
      - saas_inquiry_main_suit
      - system_role_menu
      - system_dict_data
      - system_dict_type
      - system_error_code
      - system_menu
      - system_sms_channel
      - system_sms_template
      - system_sms_log
      - system_sensitive_word
      - system_oauth2_client
      - system_mail_account
      - system_mail_template
      - system_mail_log
      - system_notify_template
      - infra_codegen_column
      - infra_codegen_table
      - infra_config
      - infra_file_config
      - infra_file
      - infra_file_content
      - infra_job
      - infra_job_log
      - infra_job_log
      - infra_data_source_config
      - saas_inquiry_doctor
      - saas_inquiry_doctor_practice
      - saas_inquiry_doctor_status
      - saas_inquiry_filing
      - saas_doctor_billing
      - saas_doctor_work_record
      - saas_doctor_audited_record
      - saas_inquiry_hospital_doctor_relation
      - saas_inquiry_hospital
      - saas_inquiry_hospital_department
      - saas_inquiry_hospital_department_relation
      - saas_inquiry_hospital_dept_doctor_relation
      - saas_inquiry_diagnosis_department_relation
      - saas_inquiry_option_config
      - saas_inquiry_hospital_setting
      - saas_inquiry_pharmacist
      - saas_inquiry_profession_identification
      - saas_inquiry_profession_sign
      - saas_inquiry_patient_info
      - saas_inquiry_record
      - saas_inquiry_record_detail
      - saas_inquiry_prescription
      - saas_inquiry_prescription_detail
      - saas_inquiry_prescription_audit
      - saas_doctor_quick_reply_msg
      - jimu_dict
      - jimu_dict_item
      - jimu_report
      - jimu_report_data_source
      - jimu_report_db
      - jimu_report_db_field
      - jimu_report_db_param
      - jimu_report_link
      - jimu_report_map
      - jimu_report_share
      - rep_demo_dxtj
      - rep_demo_employee
      - rep_demo_gongsi
      - rep_demo_jianpiao
      - tmp_report_data_1
      - tmp_report_data_income
      - saas_inquiry_pharmacist
      - saas_inquiry_prescription_template
      - saas_inquiry_user_signature_information
      - saas_inquiry_signature_platform
      - saas_inquiry_signature_person
      - saas_inquiry_signature_ca_auth
      - saas_inquiry_signature_contract
      - saas_inquiry_signature_callback_log
      - saas_inquiry_im_user
      - saas_inquiry_im_message
    ignore-caches:
      - permission_menu_ids
      - oauth_client
      - notify_template
      - mail_account
      - mail_template
      - sms_template
    ignore-tenant-ids: # 忽略的门店id
      - -1
      - -2
  sms-code: # 短信验证码相关的配置项
    expire-times: 10m
    send-frequency: 1m
    send-maximum-quantity-per-day: 10
    begin-code: 9999 # 这里配置 9999 的原因是，测试方便。
    end-code: 9999 # 这里配置 9999 的原因是，测试方便。
  trade:
    order:
      app-id: 1 # 商户编号
      pay-expire-time: 2h # 支付的过期时间
      receive-expire-time: 14d # 收货的过期时间
      comment-expire-time: 7d # 评论的过期时间
    express:
      client: kd_niao
      kd-niao:
        api-key: cb022f1e-48f1-4c4a-a723-9001ac9676b8
        business-id: 1809751
      kd100:
        key: pLXUGAwK5305
        customer: E77DF18BE109F454A5CD319E44BF5177

# token配置
jose4j:
  token:
    # 令牌自定义标识
    header: Authorization
    # 令牌密钥
    keypair: |-
      {
          "p": "1wrA6xRiJc7NtK1jWWWwh2Wz0P4pN565FgDJo9c8GPSGly9gzzoIVNrGGxCbD3fYBFj1fJ6HYb8KkIdtUW3VRlWQ7R8yqr3QqCdJqTZzLTaWlzFaWUmdAglecirVoUqB5B9sORjoSK7zdrpUGso4NsrhAaRwpoZr8NeBo5bHUac",
          "kty": "RSA",
          "q": "0I7lbB2mEwep5b3tLj2LaSqUa2vU4Bo7oyYr-QfClGxb83sBJmKCagCos-tMDGAsNbImfs6Cu8oY-Su8oJlzr-tI5AiFiOtAWytVrUL4kTXrnbzrXVa3ENp54l-XIducQJEjeiJa3fY9hf1-oIOQDWiFyS2e9tusKdKExem3h9k",
          "d": "SjOQC3F8xYVyw1HAmR2e4YBHrCs3mIE_hWQUFY9qvCiP-7P3JRlR-soMLgXX2v9XmLYZ7IXT1TNnMmSUF1td3XSBhmtfMc6vwt0ixmwuzrA7Nxug5Mze7cLaXgWjnJce2MU9LBH6Tli1vXH6P3P1KwmfS3BP6KrHVxtvOkwlnrjjaZmmNCR9qWHM76gJdabZO2A6ghQ8oaCaGVxkGIMjNJoLqOJV3dPyT8Iv4A1zCovC1bavIFm2ApFp_CsQSGIl0hZyI2NauVO0fIDqngJ-K5FwjNc4xPkwvzvtaf-yoSmFRLR-KZXGRsf_gi6FfhOvt4WfqaLXE2SjJGKxpCMK4Q",
          "e": "AQAB",
          "kid": "uniq_key",
          "qi": "FX-97chEM7UsiD2tuTDqoZno8XtYFZEEu8GVBLcr86_kguFEs8QYAIFhpIswH17j6fTT4PqgLr_ogcTF3xOBc_gJH9GQobSM4xJyruIrMgTBOrjFbc9kv28WNmKtIXEdK6wUbikPzCmT042mCvKgH8mEaEqNtLR6peboB9VcQXc",
          "dp": "hMjrHz3S7S2p7-sw8b7NJo4yXtbSdLnZ6DBMUpNRVMeJZEmpKczX4C3HLmcumHqUNPsJew4Y4s6oo148ro93ppqnhwRAsRXMhkjcP1SUAlM24d1jwMan4N4QJ8czawkq88ZDeW5b4KXHSIT98hz0YUEdd3om5W0TsgAg2Pb91Pk",
          "alg": "PS512",
          "dq": "RoKZRBU-CZ8ErLyqjICYOoT_ytWZEe8sjHsiUL5z5ZRA_i9u8xhI-Seh4bpCasi1Ca5iX2rYPj8UdGF6E13uA-LrMNVEuEa46lnbTImg2g89yoWNmW_w0ozaulMRGIxT5mxrbyZgTDytDjjm78OrFy1oVdzLqrAYI8edxf-_Yfk",
          "n": "rzDFbXL4cmFbJPy8BO1HNWQTXxx-3PGOxW7ZtF7PimybMyXbc5mViSPK4PBIm5iChKxNRb0fCMEfzCrHWroDEcjW8x14xWrLGF_RstpT86t3Dqc5PTxU3zPZt0oSgKlbpyHT7Z7tPonSpRTSXIqHV1OYrh5RWiEXM_LaCZXwkch1I4l0JwIiIXETmUU2_h28FtpWvPJ6kZZDYTH6izXAd33jlGVg8fplLJiVWFyrcGrnsxD1rjt6VfLBb-wtROVOBHbEy55JlFtFeArFx-DV0Bcdi38cbUDjZdDPg4SnkBrYefrf1SGK6wus46FxeTaHjhvOJvytnfbjsEt9SGRHjw"
      }
    # 令牌有效期（默认30分钟）
    expireSeconds: 18000

management:
  endpoints:
    web:
      exposure:
        include: "*"
event:
  bus:
    consumer:
      group-id: com_xyy_saas_inquiry_hospital_server_mq_consumer_reception_RecetionAreaConsumer

# Lock4j 配置项
lock4j:
  acquire-timeout: 1000 # 获取分布式锁超时时间，默认为 1 秒
  expire: 30000 # 分布式锁的超时时间，默认为 30 秒
#  rocketmq: 与test环境共用，建议根据topic来区分   _test   |   _dev